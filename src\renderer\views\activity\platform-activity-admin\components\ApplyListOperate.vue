<template>
  <div>
    <div v-if="row.passedAt !== '0'" class="flex items-center gap-4">
      <span class="text-[#516082]">已同意</span>
      <t-tooltip placement="top-right">
        <iconpark-icon class="text-20 text-[#828DA5]" name="iconhelp" />

        <template #content>
          <div class="flex flex-col gap-2 text-12 leading-20">
            <div>操作时间：{{ dayjs(row.passedAt * 1000).format('YYYY-MM-DD HH:mm:ss') }}</div>
            <div>操作人：{{ row.operatorName }}</div>
          </div>
        </template>
      </t-tooltip>
    </div>

    <div v-else-if="row.invalidedAt !== '0'" class="flex items-center gap-4 text-[#516082]">
      <span class="text-[#516082]">已失效</span>
    </div>

    <div v-else-if="row.cancelledAt !== '0'" class="flex items-center gap-4 text-[#516082]">
      <span class="text-[#516082]">已取消</span>
      <t-tooltip placement="top-right">
        <iconpark-icon class="text-20 text-[#828DA5]" name="iconhelp" />

        <template #content>
          <div class="flex flex-col gap-2 text-12 leading-20">
            <div>取消原因：申请人取消申请</div>
            <div>操作时间：{{ dayjs(row.cancelledAt * 1000).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
        </template>
      </t-tooltip>
    </div>

    <div v-else-if="row.rejectedAt !== '0'" class="flex items-center gap-4 text-[#516082]">
      <span class="text-[#516082]">已拒绝</span>

      <t-tooltip placement="top-right">
        <iconpark-icon class="text-20 text-[#828DA5]" name="iconhelp" />

        <template #content>
          <div class="flex flex-col gap-2 text-12 leading-20">
            <div>拒绝原因：{{ row.rejectReason }}</div>
            <div>操作时间：{{ dayjs(row.rejectedAt * 1000).format('YYYY-MM-DD HH:mm:ss') }}</div>
            <div>操作人：{{ row.operatorName }}</div>
          </div>
        </template>
      </t-tooltip>
    </div>

    <div v-else class="flex items-center gap-8">
      <div class="cursor-pointer text-[#4D5EFF] hover:bg-[#EAECFF] rounded-4 p-4 inline-block" @click.stop="handleAgree">
        同意
      </div>
      <div class="cursor-pointer text-[#D54941] hover:bg-[#FDF5F6] rounded-4 p-4 inline-block" @click.stop="handleReject">
        拒绝
      </div>
    </div>

    <RejectDialog ref="rejectDialogRef" @change="change" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import dayjs from 'dayjs';
import to from 'await-to-js';
import { agreeDPActivity } from '@/api/activity/platform';
import RejectDialog from '@/views/activity/platform-activity-admin/components/RejectDialog.vue';

const props = defineProps({
  row: {
    type: Object,
  },
});

const emit = defineEmits(['change']);

const rejectDialogRef = ref(null);

const teamId = localStorage.getItem('honorteamid');

// 同意
const handleAgree = async () => {
  const [_, res] = await to(agreeDPActivity({
    activityId: props.row.id,
    teamId,
    notError: true,
  }));

  if (res.data.code === 0) {
    MessagePlugin.success('已同意');
    change();
    return;
  }

  const errorCode = res.data?.code;
  if (errorCode === 5048 || errorCode === 5049) {
    const messageMap = {
      5048: '该活动状态异常，申请已失效',
      5049: '该申请已被其他管理员处理，无法重复操作',
      300: '该申请已被其他管理员处理，无法重复操作',
    };

    const alertDia = DialogPlugin.alert({
      header: '提示',
      theme: 'info',
      body: messageMap[errorCode],
      closeBtn: null,
      confirmBtn: '知道了',
      onConfirm: () => {
        alertDia.destroy();
      },
      onClose: () => {
        alertDia.destroy();
      },
    });
    change();
  } else {
    MessagePlugin.error(res.data.message);
  }
};

// 拒绝
const handleReject = async () => {
  rejectDialogRef.value.open(props.row.id);
};

const change = () => {
  emit('change');
};

</script>

<style lang="less" scoped>

</style>
