<template>
  <div class="tabs" :class="{ 'b-border': bottomBorder, animation }" :style="{ height }">
    <div
      v-for="(item, index) in list"
      :key="item.value"
      ref="tabItemRefs"
      :class="['tab-item', size, { active: activeTab === index }]"
      @click="onTabClick(item, index)"
    >
      <slot :item="item">
        <t-badge v-if="item.count" :count="item.count" size="small">
          {{ item.label }}
        </t-badge>
        <template v-else>{{ item.label }}</template>
      </slot>
    </div>
    <div class="tab-indicator" :style="indicatorStyle" />
  </div>
</template>

<script setup lang="ts">
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { PropType, ref, watch, computed, onUnmounted, nextTick } from 'vue';

const props = defineProps({
  // tab 列表
  list: {
    type: Array as PropType<{ value: any, label: string, count?: number }[]>,
    default: () => [],
  },
  // 选中tab索引
  defaultValue: {
    type: Number,
    default: 0,
  },
  align: {
    type: String,
    default: 'center',
  },
  height: {
    type: String,
    default: '56px',
  },
  size: {
    type: String,
    default: 'md',
    validator: (value: string) => ['sm', 'md', 'lg'].includes(value),
  },
  // 是否有下边框
  bottomBorder: {
    type: Boolean,
    default: true,
  },
  // 是否开启指示器动画
  animation: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['change']);

const activeTab = ref(Math.min(props.defaultValue, props.list.length - 1));
const tabItemRefs = ref<HTMLElement[]>([]);
const indicatorLeft = ref(0);

// 更新指示器位置（根据当前激活的标签页的位置和宽度来计算指示器的新位置）
const updateIndicatorPosition = async () => {
  await nextTick();

  if (tabItemRefs.value && tabItemRefs.value[activeTab.value]) {
    const activeTabElement = tabItemRefs.value[activeTab.value];
    const tabWidth = activeTabElement.offsetWidth;
    indicatorLeft.value = activeTabElement.offsetLeft + (tabWidth / 2) - 8; // 8 是指示器宽度的一半
  }
};

const indicatorStyle = computed(() => ({
  transform: `translateX(${indicatorLeft.value}px)`,
}));

const onTabClick = (item, index: number) => {
  if (props.list.length === 1) return;

  activeTab.value = index;
  updateIndicatorPosition();
  emits('change', item, index);
};

watch(() => props.defaultValue, (val) => {
  activeTab.value = val;
  updateIndicatorPosition();
});

watch(() => props.list, () => {
  updateIndicatorPosition();
}, { deep: true });

onMountedOrActivated(() => {
  updateIndicatorPosition();
  window.addEventListener('resize', updateIndicatorPosition);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateIndicatorPosition);
});
</script>

<style lang="less" scoped>
.tabs {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: v-bind('align');
  height: 56px;
  padding: 0 16px;

  &.b-border {
    border-bottom: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
  }

  .tab-item {
    position: relative;
    display: flex;
    height: 100%;
    align-items: center;
    margin-right: 32px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px; /* 150% */
    text-align: center;
    cursor: pointer;
    color: var(--text-kyy-color-text-1, #1A2139);
    white-space: nowrap;
    flex-shrink: 0;

    &.lg,
    &.lg :deep(.t-badge) {
      font-size: 16px;
      line-height: 24px; /* 150% */
    }

    &.md {
      font-size: 16px;
      line-height: 24px;
      margin-right: 24px;
    }
    &.md :deep(.t-badge) {
      font-size: 16px;
      line-height: 24px;
    }

    &.active,
    &.active :deep(.t-badge) {
      color: var(--brand-kyy-color-brand-default, #4D5EFF);
      font-weight: 600;
    }
  }

  .tab-indicator {
    width: 16px;
    height: 3px;
    border-radius: 1.5px;
    background: var(--brand-kyy-color-brand-default, #4D5EFF);
    position: absolute;
    bottom: 0;
    left: 0;
  }

  &.animation .tab-indicator {
    transition: transform 0.3s ease;
  }
}
</style>
