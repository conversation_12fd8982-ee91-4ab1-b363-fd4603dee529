<template>
  <div class="cantainer">
    <div class="title">
      <span>{{ t("contacts.organizePerson") }}</span>
      <t-tooltip :content="t('contacts.addContanc')" :show-arrow="false" placement="bottom">
        <img
          style="cursor: pointer; width: 24px; height: 24px"
          src="@renderer/assets/svg/icon-peopleJoin-new.svg"
          alt=""
          @click="addBussniss"
        />
      </t-tooltip>
    </div>
    <div v-if="empty" class="recent-empty">
      <img src="@renderer/assets/emptydata.png" alt="" />
      <div class="tip">暂无数据</div>
    </div>
    <div v-else :class="['t-menu-container', isNotMac ? 'scrollbar' : '']">
      <t-anchor class="anchor-list">
        <t-anchor-item
          v-for="item in options"
          :key="item.initial"
          :href="`#${route.path}#${item.initial}`"
          :title="item.initial"
        />
      </t-anchor>
      <t-menu theme="light" defaultValue="dashboard" style="width: calc(100% - 40px)">
        <div v-for="item in options" :key="item.initial">
          <h1 class="anchor-title" :id="`${route.path}#${item.initial}`">{{ item.initial }}</h1>
          <t-menu-item
            v-for="person in item.data"
            :key="person.peer"
            :value="person.peer"
            @mouseover="showAct(person)"
            @mouseleave="hiddenAct"
            @click="showCard(person)"
          >
            <template #icon>
              <avatar
                class="avatar-icon"
                roundRadius
                :imageUrl="person.avatar"
                :userName="person.name"
                avatarSize="44px"
              />
            </template>
            <div class="user-info">
              <div class="user-name">
                <div>{{ person.name }}</div>
                <RelationTag :relation="person.relation"/>
              </div>
              <div v-if="person.peerInfo?.departments?.length" class="post">{{ `${person.peerInfo.departments[0].name}` }}/{{ person.peerInfo.departments[0].jobName }}</div>
              <MultiIdTag
                :myCard="{ cardId: person.main, teamName: person.mainInfo.teamName }"
                :anotherCard="{ cardId: person.peer, teamName: person.peerInfo.teamName }"
              ></MultiIdTag>
            </div>
            <div class="act-groups" v-if="person.main + person.peer === hoverValue">
              <div>
                <!-- <t-button class="mr-12" shape="circle" theme="primary" @click.stop="vioce(person)">
                  <template #icon>
                    <t-tooltip :content="t('zx.contacts.voiceCall')" :show-arrow="false" placement="bottom">
                      <img src="@renderer/assets/svg/voicefill_new.svg" alt="" />
                    </t-tooltip>
                  </template>
                </t-button>
                <t-button class="mr-12" shape="circle" theme="primary" @click.stop="video(person)">
                  <template #icon>
                    <t-tooltip :content="t('zx.contacts.videoCall')" :show-arrow="false" placement="bottom">
                      <img src="@renderer/assets/svg/videofill_new.svg" alt="" />
                    </t-tooltip>
                  </template>
                </t-button> -->
                <t-button shape="circle" theme="primary" @click.stop="msg(person)">
                  <template #icon>
                    <t-tooltip :content="t('zx.contacts.msgCall')" :show-arrow="false" placement="bottom">
                      <img src="@renderer/assets/svg/commentfill_new.svg" alt="" />
                    </t-tooltip>
                  </template>
                  <!--                {{ t('contacts.msg') }}-->
                </t-button>
              </div>
            </div>
          </t-menu-item>
        </div>
      </t-menu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { pairsList } from "@renderer/api/contacts/api/organize";
import avatar from "@renderer/components/kyy-avatar/index.vue";
import MultiIdTag from "@/components/contacts/MultiIdTag.vue";
import RelationTag from '@renderer/components/contacts/relationTag.vue'

import { getCards, getOpenid } from "@renderer/utils/auth";
import { pySegSort, videoMsg, voiceMsg } from "../utils";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { isNotMac } from "@renderer/views/zhixing/util";
import { useContactsStore } from "@/store/modules/contacts";
import { getNoteList } from "@renderer/api/contacts/api/common";
const contactsStore = useContactsStore();
import { openChat } from "@/utils/share";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();
const route = useRoute();
const options = ref([]);
const empty = ref(false);
const hoverValue = ref("");

const msg = (item) => {
  openChat({ main: item.main, peer: item.peer });
};
const video = (item) => {
  videoMsg(item.peer, item.main);
};
const vioce = (item) => {
  voiceMsg(item.peer, item.main);
};
const showAct = (item) => {
  hoverValue.value = item.main + item.peer;
};
const hiddenAct = () => {
  hoverValue.value = "";
};

const getPairsList = () => {
  const card_ids = getCards().map((v) => v.uuid);
  card_ids.push(getOpenid());
  pairsList().then((res) => {
    console.log(res, 'getPairsList');
    if (res.status === 200) {
      res.data.data.relations?.length ? polyPairsList(res.data.data.relations) : empty.value = true;
    }
  });
};
const replaceNote = (list) => {
  console.log(list, 'replaceNote');
  // 获取备注信息
  getNoteList({ cardIds: list.map((v) => v.peer) }).then((res) => {
    console.log(res, "getNoteList");
    if (res.data.code === 0) {
      res.data.data?.cardRemarks?.forEach((item) => {
        const itemFollow = list.find((v) => v.peer === item.cardId);
        if(itemFollow){
          itemFollow.CardName = itemFollow?.name;
          itemFollow.name = item.remarks ? item.remarks : itemFollow?.name;
        }
      });
      options.value = pySegSort(list);
    }
  });
};
const polyPairsList = async (list) => {
  const listData = [];
   list.forEach(val => {
    if (["CONSULT", "BUSINESS"].includes(val.pairType)) {
    const peerInfo = {
      ...val.friendCard,
      teamName: val.friendCard.teamName,
      teamId: val.friendCard.friendCard,
      cardId: val.friendCard.cardId,
      cardName: val.friendCard.cardName,
    }
    const mainInfo = {
      ...val.selfCard,
      teamName: val.selfCard.teamName,
      teamId: val.selfCard.friendCard,
      cardId: val.selfCard.cardId,
      cardName: val.selfCard.cardName,
    }
    const renderData = {
        name:val.comment || val.friendCard.cardName,
        avatar: val.friendCard.avatar,
        peerInfo,
        peerName: val.comment || val.friendCard.cardName,
        describe: val.describe,
        mainInfo,
        relation: val.pairType,
        team: val.team,
        peer: val.cardIdFriend,
        main: val.cardIdSelf,
        has_del: val.friendCard.removed == 0 || !!val.friendCard.removed,
      }
    listData.push(renderData)
  }});
  listData.length ?  replaceNote(listData)  : (empty.value = true);
};
const showCard = (item) => {
  ipcRenderer.invoke("identity-card", { cardId: item.peer, myId: item.main });
};
const addBussniss = () => {
  contactsStore.addContactsType = "business";
  contactsStore.addContactsVisible = true;
};

const updateContactListener = (event, arg) => {
  getPairsList();
};

onMounted(() => {
  getPairsList();
  ipcRenderer.on("update-contact-list", updateContactListener);
});

onUnmounted(() => {
  ipcRenderer.off("update-contact-list", updateContactListener);
});
</script>

<style lang="less" scoped>
.mr-12 {
  margin-right: 12px;
}
.cantainer {
  width: 100%;
  position: relative;
  .title {
    color: var(--text-kyy-color-text-1, #1a2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    margin: 20px 24px 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .recent-empty {
    width: 100%;
    position: absolute;
    top: 64px;
    left: 8px;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 200px;
      height: 200px;
    }
    .tip{
      color: var(--text-kyy_color_text_2, #516082);

    }
  }
  .t-menu-container {
    width: calc(100% - 48px);
    position: absolute;
    top: 64px;
    left: 24px;
    bottom: 0;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .anchor-list {
    position: fixed;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
  }
  .t-anchor {
    width: auto;
    background-color: transparent;

    :deep(.t-anchor__line) {
      display: none;
    }
    :deep(.t-anchor__item) {
      padding: 0 16px;
    }
    :deep(.t-anchor__item-link) {
      width: 21px;
      height: 21px;
      font-size: 14px;

      text-align: center;
      color: #2069e3;
    }
    :deep(.t-is-active > a) {
      background: #daecff;
      border-radius: 4px;
      color: #2069e3;
    }
  }
  :deep(.t-default-menu__inner .t-menu) {
    padding: 0;
  }
  :deep(.t-default-menu:not(.t-menu--dark) .t-menu__item.t-is-active:not(.t-is-opened)) {
    background: transparent !important;
    color: #13161b !important;
    &:hover {
      background: #f0f8ff !important;
      border-radius: 4px;
    }
  }
  :deep(.t-default-menu .t-menu__item) {
    position: relative;
    color: var(--text-kyy-color-text-1, #1a2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
    height: 68px !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_list_hover, #f3f6fa) !important;
    }
  }
  .anchor-title {
    color: var(--text-kyy-color-text-2, #516082);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    padding: 12px 0;
  }
  .avatar-icon {
    margin-right: 12px;
  }
  .user-info {
    .user-name {
      display: flex;
      align-items: flex-start;
    }
    .post {
      font-size: 12px;
      margin-top: 2px;
      color: #717376;
      line-height: 20px;
    }
    .company-mark {
      padding: 1px 5px;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_warning, #ffe5d1);
      color: var(--kyy_color_tag_text_warning, #fc7c14);
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin-left: 4px;
    }
  }
  .act-groups {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    .t-button {
      min-width: auto;
    }
    .t-button--shape-circle {
      border: none;
      background-color: transparent;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      img {
        width: 36px;
        height: 36px;
      }
    }
  }
}
</style>
