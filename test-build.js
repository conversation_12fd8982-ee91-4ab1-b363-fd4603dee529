const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始测试构建配置...');

try {
  // 运行构建
  console.log('📦 执行构建...');
  execSync('npm run build:electron', { stdio: 'inherit' });
  
  // 检查输出文件
  const distPath = path.join(__dirname, 'dist/electron/renderer');
  const assetsPath = path.join(distPath, 'assets');
  
  if (fs.existsSync(assetsPath)) {
    const files = fs.readdirSync(assetsPath);
    const jsFiles = files.filter(f => f.endsWith('.js'));
    const cssFiles = files.filter(f => f.endsWith('.css'));
    const assetFiles = files.filter(f => !f.endsWith('.js') && !f.endsWith('.css'));
    
    console.log('\n📊 构建结果统计:');
    console.log(`JS 文件: ${jsFiles.length} 个`);
    console.log(`CSS 文件: ${cssFiles.length} 个`);
    console.log(`其他资源文件: ${assetFiles.length} 个`);
    
    // 检查文件大小
    jsFiles.forEach(file => {
      const filePath = path.join(assetsPath, file);
      const stats = fs.statSync(filePath);
      const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
      console.log(`  ${file}: ${sizeInMB}MB`);
    });
    
    if (assetFiles.length === 0) {
      console.log('✅ 成功！所有静态资源已内联到JS文件中');
    } else {
      console.log('⚠️  仍有部分资源文件未内联:');
      assetFiles.forEach(file => {
        const filePath = path.join(assetsPath, file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
        console.log(`  ${file}: ${sizeInMB}MB`);
      });
    }
  }
  
} catch (error) {
  console.error('❌ 构建失败:', error.message);
}
