{"name": "lynker-desktop-web", "private": true, "version": "0.0.1", "type": "module", "scripts": {"clean": "rimraf ./node_modules/.vite", "dev": "npm run clean && vite --mode development", "pre": "vite --mode pre", "prod": "vite --mode prod", "qa": "vite --mode qa", "build:dev": "vue-tsc && vite build --mode development", "build": "NODE_OPTIONS=--max-old-space-size=8096 vite build --mode development", "build:test": "NODE_OPTIONS=--max-old-space-size=8096 vite build --mode test", "build:pre": "NODE_OPTIONS=--max-old-space-size=8096 vite build --mode pre", "build:prod": "NODE_OPTIONS=--max-old-space-size=8096 vite build --mode prod", "build:fast": "NODE_OPTIONS=--max-old-space-size=8096 vite build --mode test", "build:single": "NODE_OPTIONS=--max-old-space-size=8096 vite build --mode development", "preview": "vite preview", "lint": "eslint src --fix --ext .ts,.tsx,.vue,.js,.jsx --max-warnings 0", "prepare": "husky install", "build:vendor": "rimraf ./public/vendor && rollup -c"}, "dependencies": {"@lynker-desktop/web": "^0.0.63", "@rk/editor": "0.4.7", "@rk/unitPark": "0.1.2-beta.13", "@tanstack/vue-query": "4.36.1", "@videojs-player/vue": "^1.0.0", "@vueuse/components": "^13.1.0", "@vueuse/core": "10.2.1", "axios": "1.3.4", "bignumber.js": "^9.3.0", "cropperjs": "^1.6.1", "dayjs": "^1.11.11", "html-to-image": "^1.11.13", "html-to-text": "^9.0.5", "html2canvas": "^1.4.1", "less": "4.1.3", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.30.1", "pinia": "^2.1.7", "qs": "^6.11.2", "url-parse": "^1.5.10", "uuid": "^11.1.0", "video.js": "^8.3.0", "vue": "^3.4.21", "vue-clipboard3": "^2.0.0", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.3.2", "vue3-baidu-map-gl": "^2.6.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@lk/editor": "^0.1.25", "@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@types/ali-oss": "6.16.7", "@types/bignumber.js": "^5.0.4", "@types/md5": "^2.3.5", "@types/node": "^20.14.7", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "@unocss/core": "^0.55.7", "@unocss/eslint-config": "0.55.3", "@unocss/preset-rem-to-px": "0.51.8", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^4.0.0", "ali-oss": "6.17.1", "await-to-js": "^3.0.0", "crypto-js": "^4.2.0", "eslint": "^8.39.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.11.0", "husky": "^8.0.3", "js-md5": "^0.8.3", "lint-staged": "^15.1.0", "mp4box": "^0.5.2", "prettier": "^3.2.4", "qrcode.vue": "^3.4.1", "rimraf": "^6.0.1", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-visualizer": "^5.12.0", "tdesign-icons-vue-next": "^0.2.2", "tdesign-vue-next": "^1.10.1", "typescript": "^5.2.2", "unocss": "0.55.3", "vite": "^4.5.3", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-dynamic-prefetch": "^0.1.5", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-svg-icons": "^2.0.1", "vue-i18n": "^9.13.1", "vue-qr": "^4.0.9", "vue-tippy": "v6", "vue-tsc": "^2.0.6"}, "lint-staged": {"*.{vue,js,ts,tsx,jsx,json}": ["eslint --fix", "git add"]}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "@unocss/core": "^0.55.7"}}