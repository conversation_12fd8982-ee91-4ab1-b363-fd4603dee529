{"name": "lynker-h5", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "qa": "vite --mode qa", "pre": "vite --mode pre", "prod": "vite --mode prod", "build:dev": "vite build --mode test", "build:qa": "cross-env NODE_OPTIONS='--max-old-space-size=4096' vite build --mode qa", "build:pre": "vite build --mode pre", "build:prod": "vite build --mode prod", "build": "run-p build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "eslint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "eslint:fix": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "prettier": "prettier -l \"./**/*\"", "prettier:fix": "prettier --write -l \"./**/*\"", "update": "yarn add vue@latest && yarn add -D @babel/core@latest @babel/eslint-parser@latest @types/node@latest @typescript-eslint/eslint-plugin@latest @typescript-eslint/parser@latest @vitejs/plugin-vue@latest @vue/eslint-config-typescript@latest @vue/tsconfig@latest eslint@latest eslint-config-alloy@latest eslint-plugin-vue@latest npm-run-all@latest prettier@latest typescript@latest vite@latest vue-eslint-parser@latest vue-tsc@latest", "svgo": "svgo -r -f src/assets/svg -o src/assets/svg"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@rk/editor": "0.4.7", "@types/ali-oss": "^6.16.8", "@types/path-browserify": "^1.0.0", "@vueuse/core": "^10.3.0", "ali-oss": "^6.17.1", "await-to-js": "^3.0.0", "axios": "^1.4.0", "callapp-lib": "^3.5.3", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "fast-glob": "^3.3.1", "flipbook-vue": "^1.0.0-beta.4", "js-md5": "^0.8.3", "mitt": "^3.0.0", "moment": "^2.29.4", "npm": "^9.8.0", "patch-package": "^8.0.0", "path-browserify": "^1.0.1", "pinia": "^2.0.36", "pinia-plugin-persistedstate": "^3.1.0", "qrcode-parser": "^2.1.3", "qs": "^6.12.1", "swiper": "^11.0.7", "tdesign-vue-next": "^1.3.5", "uuid": "^9.0.0", "vant": "4.7.3", "vconsole": "^3.15.1", "vue": "3.3.2", "vue-cropper": "^0.6.5", "vue-i18n": "^9.2.2", "vue-router": "^4.2.0", "vue-tippy": "v6", "vue3-lazy": "^1.0.0-alpha.1"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/eslint-parser": "^7.21.8", "@tsconfig/node18": "^2.0.1", "@types/node": "^20.3.2", "@types/uuid": "^9.0.4", "@unocss/eslint-config": "^0.55.3", "@unocss/preset-rem-to-px": "^0.51.8", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "cross-env": "^7.0.3", "eslint": "^8.39.0", "eslint-config-alloy": "^5.0.0", "eslint-plugin-vue": "^9.14.1", "less": "^4.1.3", "lodash": "^4.17.21", "npm-run-all": "^4.1.5", "postcss": "^8.4.24", "postcss-px-to-viewport-8-plugin": "^1.2.2", "prettier": "^2.8.8", "rollup-plugin-visualizer": "^5.12.0", "svgo": "^3.0.2", "typescript": "~5.0.4", "unocss": "^0.51.12", "unplugin-icons": "^0.18.5", "unplugin-vue-components": "^0.26.0", "vite": "^4.3.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-static-copy": "^2.3.0", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^9.3.0", "vue-tsc": "^1.6.4"}}