<template>
  <div class="my-vcard-type-container">
    <div class="my-vcard-type-container-left">
      <div class="my-vcard-type">
        <div class="my-vcard-type-title">
          {{ $t('im.vcard.digital_card') }}
        </div>
        <div class="my-vcard-type-content">
          <div class="my-vcard-type-content-title">
            {{ $t('im.vcard.send_card_tip') }}
          </div>
          <MyVCardType className="my-vcard-type-content-vcard" :infoData="infoData" ref="MyVcardRef"></MyVCardType>
        </div>
        <div class="my-vcard-type-content-footer">
          <t-button class="my-vcard-type-content-footer-button" theme="default" @click="openCard">
            {{ $t('im.vcard.open_card') }}
          </t-button>
          <t-button :disabled="isSendVcard || msgStore.showNotFriends" class="my-vcard-type-content-footer-button" theme="primary" @click="sendCard">
            {{ !isSendVcard ? $t('im.vcard.send') : $t('im.vcard.send_success') }}
          </t-button>
        </div>
      </div>
    </div>
    <div class="use-vcard-type">
      <div class="use-vcard-type-avatar" @click="openMyCard">
        <ChatAvatarComponent :src="userInfo?.avatar" :alt="userInfo?.staffName" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch } from "vue";
  import { storeToRefs } from 'pinia';
  import { cardIdType } from "@/views/identitycard/data";
  import MyVCardType from "@/windows/vcard/components/MyVCardType.vue"
  import jssdk from '@renderer/_jssdk';

  import { mainCard } from "@renderer/api/vcard";
  import _ from 'lodash';
  import ChatAvatarComponent from '../../components/ChatAvatar.vue';

  import { useMessageStore } from "../../service/store";
  import { useChatExtendStore } from "../../service/extend";
  import { getProfilesInfo } from "@renderer/utils/auth";
  import { useImToolStore } from "../../tool/service/tool";
  import { ImToolChating, ImToolContainer, SceneTool } from "../../tool/service/type";
  const msgStore = useMessageStore();
  const userInfo = ref(getProfilesInfo());
  const toolStore = useImToolStore();
  const { chatingSession } = storeToRefs(msgStore);
  const infoData = ref(null);
  const MyVcardRef = ref(null);
  const isSendVcard = ref(useMessageStore().isMySendVcard);
  const isOuter = ref(false)

  watch(useMessageStore(), (newVal) => {
    isSendVcard.value = newVal.isMySendVcard;
  });

  const shareVcard = () => {
    // 发送数字名片
    mainCard({}).then(res => {
      infoData.value = res.data.data
      // ipcRenderer.invoke("set-popbv",
      //   {
      //     show: true, type: 'dialogVcard', data: {
      //       val: JSON.stringify(res.data.data)
      //     }
      //   }
      // );
    })
  };
  const sendCard = () => {
    console.log(msgStore.chatingSession.latestMessage, '触发这里了吗');
    const store = useChatExtendStore();
    // store.msgSendVcardDetailVisible = true
    // store.showSendVcard()
    // chatingSession
    useChatExtendStore().showChatDialog("send-vcard-chat-dialog", msgStore.chatingSession.latestMessage)
  }
  const openCard = () => {
    console.log(infoData.value, '打开数字名片');
    // toolStore.toggleTool(ImToolContainer.Chating, { chatingTool: ImToolChating.VCard})
    toolStore.toggleTool(ImToolContainer.Scene, { scene: SceneTool.digitalBusinessCard, sceneData: {tab: 1} });

  }
  const openMyCard = () => {
    const info = msgStore.allMembers.get(chatingSession.value.localSessionId)?.get(chatingSession.value.targetCardId);
    const myInfo = msgStore.allMembers.get(chatingSession.value.localSessionId)?.get(chatingSession.value.myCardId);
    jssdk.ipcRenderer.invoke("identity-card", { cardId: myInfo.cardId, myId: myInfo.cardId });
  }

  onMounted(() => {
    isOuter.value = cardIdType(msgStore?.chatingSession?.myCardId) === 'outer';
    userInfo.value = msgStore?.chatingSessionMembers?.get(msgStore?.chatingSession?.myCardId)
    shareVcard();
  });
</script>

<style lang="less" scoped>
  .my-vcard-type-container {
    display: flex;
    flex-direction: row;
    gap: 12px;
  }
  .my-vcard-type-container-left {
    display: flex;
    flex-grow: 2;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;
  }
  .my-vcard-type {
    width: 360px;
    background-color: #fff;
    border-radius: 8px;
    text-align: left;
  }
  .my-vcard-type-title {
    width: 360;
    height: 42;
    gap: 4px;
    padding-top: 9px;
    padding-right: 16px;
    padding-bottom: 9px;
    padding-left: 16px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    text-align: left;
    background-color: #EAECFF;
    color: #4D5EFF;
  }
  .my-vcard-type-content {
    padding: 16px;
    overflow-y: auto;
  }
  .my-vcard-type-content-title {
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
    margin-bottom: 16px;
    color: #1A2139;
  }
  .use-vcard-type-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #EAECFF;
  }
  .my-vcard-type-content-footer {
    border-top: 1px solid #E5E6EB;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: flex-end;
    gap: 16px;
    margin-right: 16px;
    margin-left: 16px;
    padding-top: 16px;
    padding-bottom: 16px;
  }
  .my-vcard-type-content-footer-button {
    width: 0;
    flex-grow: 2;
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
    text-align: center;
  }
</style>
<style lang="less">
  .my-vcard-type-content-vcard {
    overflow-y: auto;
   .head-card{
      width: 100%!important;
      margin-bottom: 0!important;
    }
    .my-card-text-box{
      width: 213px!important;
    }
  }
</style>
