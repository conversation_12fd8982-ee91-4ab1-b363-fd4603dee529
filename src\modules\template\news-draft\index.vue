<template>
  <!-- 内容区域 -->
  <div class="news-warp">
    <RTable
      ref="RTableRef"
      :filter="filter"
      :table="table"
      :is-sticky-header="true"
      :is-change-clean="false"
      @change="change"
    >
      <template #empty>
        <REmpty v-if="isNoData" name="no-result" tip="搜索无结果" />
        <template v-else>
          <REmpty name="no-data" :tip="props?.config.text?.emptyText || '暂无资讯内容'" />
        </template>
      </template>
    </RTable>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, watch } from 'vue';
import { RTable, REmpty, REllipsisTooltip } from '@rk/unitPark';
import { refDebounced } from '@vueuse/core';
import { useQuery } from '@tanstack/vue-query';
import { Information } from '@/types/api/culture/news';
import { formatDateTime } from '@utils/date';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import { highlightKeyword } from '@utils/index';

import { useModuleApi, useModuleRouter, useModuleStore } from '@modules/factory/context';
import { NewsDraftConfig } from './types';

const props = defineProps<NewsDraftConfig>();

const store = useModuleStore();

const api = useModuleApi();

const { navigateToDetailNews, navigateToEditNews } = useModuleRouter();

const RTableRef = ref(null);
const isNoData = ref(false);

const keyword = ref('');
const searchValueDebounced = refDebounced(keyword, 300);

const pageInfo = ref({
  total: 0,
  pageSize: 10,
  current: 1,
});

const me = computed(() => ({
  'me.openId': store.value?.query?.openId as string,
  'me.cardId': store.value?.query?.cardId as string,
  'me.teamId': store.value?.query?.teamId as string,
}));

const change = (info: any, key: any) => {
  if (key === 'filter') {
    keyword.value = info.filter.searchVal || '';
    // 不立即重置页码，等待防抖完成后再重置
    return;
  }
  if (key === 'table') {
    pageInfo.value.current = info.pageInfo.current;
    pageInfo.value.pageSize = info.pageInfo.pageSize;
    return;
  }
};

// 监听防抖后的搜索值变化，重置页码
watch(searchValueDebounced, (newValue, oldValue) => {
  // 只有当搜索词真正改变时才重置页码
  if (newValue !== oldValue) {
    pageInfo.value.current = 1;
  }
});

// 计算查询参数，集中管理所有参数
const queryParams = computed(() => ({
  keyword: searchValueDebounced.value,
  teamId: store.value?.query?.teamId as string,
  ...me.value,
  'page.size': pageInfo.value.pageSize,
  'page.number': pageInfo.value.current,
  status: 'StatusDraft',
  descFlag: true,
  sort: 'SortCreatedAt',
}));

// 使用useQuery获取数据
const { data, isLoading, refetch } = useQuery({
  queryKey: ['news-draft-list', queryParams],
  queryFn: () => api.getInformationList(queryParams.value, store.value?.query?.teamId as string),
});

// 响应式更新表格数据
watch(
  data,
  (newData: any) => {
    const responseData = newData?.data;
    if (responseData?.information?.length) {
      table.value.list = responseData.information;
      pageInfo.value.total = responseData.count?.draft || 0;
    } else {
      table.value.list = [];
      pageInfo.value.total = 0;
      isNoData.value = !!searchValueDebounced.value;
    }
  },
  { deep: true },
);

const handleDelete = async (template: Information) => {
  const dialog = DialogPlugin.confirm({
    header: '提示',
    theme: 'info',
    body: '确定删除吗？',
    closeBtn: false,
    onConfirm: async () => {
      dialog.destroy();
      const params = {
        id: Number(template.id),
        me: store.value?.query,
      };
      const [err] = await to(api.deleteInformation(params));
      if (err) {
        refetch();
        return;
      }
      MessagePlugin.success(`删除成功`);
      // 如果删除的是下一页的最后一条数据，则删除后，需要将当前页码减1
      if (pageInfo.value.current > 1 && table.value.list.length === 1) {
        pageInfo.value.current -= 1;
      }
      refetch();
    },
  });
};

const handleEdit = async (template: Information) => {
  if (!(await getInformationDetailInfo(template.id))) {
    refetch();
    return;
  }

  navigateToEditNews(template.id.toString());
};

const getInformationDetailInfo = async (id: number) => {
  const [err] = await to(api.getInformationDetail({ id, ...me.value }, store.value?.query?.teamId as string));
  if (err) {
    MessagePlugin.error(err.message || '获取资讯详情失败，请重试');
    return false;
  }
  return true;
};

const filter = {
  attrs: {
    size: 'small',
    labelWidth: '80px',
    placeholder: props?.config.text?.searchPlaceholder || '搜索资讯标题/作者/专栏',
  },
};

const table = ref({
  attrs: {
    'row-key': 'id',
    tableLayout: 'auto',
    hover: true,
    loading: isLoading,
    onRowClick: async ({ row }: { row: any }) => {
      if (!(await getInformationDetailInfo(row.id))) {
        refetch();
        return;
      }
      navigateToDetailNews(row.id.toString(), 'draft');
    },
  },
  list: [],
  pagination: isNoData.value ? false : pageInfo.value,
  columns: [
    {
      title: props?.config.text?.tableTitleName || '资讯名称',
      width: 448,
      colKey: 'title',
      cell: (h: any, { row }: { row: any }) => {
        // 方案1：使用变量存储 URL，避免 Vite 静态资源处理
        const defaultImageUrl =
          'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/h5/featured_service_default_cover';

        return (
          <div class="flex gap-12">
            <div class="news-image">
              <img src={row.cover || defaultImageUrl} class="w-96px h-72px rounded-4px object-cover" />
            </div>
            <div class="flex-col gap-4">
              <REllipsisTooltip content={row.title} line-number={2}>
                <div class="text-#1A2139" v-html={highlightKeyword(row.title, keyword.value)}></div>
              </REllipsisTooltip>
              {props.moduleId === 'culture' && (
                <template>
                  {row.author && (
                    <REllipsisTooltip
                      class="text-#828DA5"
                      text={`作者：${row.author || ''}`}
                      content={`作者：${row.author || ''}`}
                    />
                  )}
                  <div class="text-#828DA5">展示时间：{formatDateTime(Number(row.displayAt)) || ''}</div>
                </template>
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: props?.config.text?.tableColumnName || '专栏',
      width: 120,
      colKey: 'columnName',
    },
    {
      title: '创建人',
      width: 104,
      colKey: 'creatorName',
    },
    {
      title: '创建时间',
      width: 120,
      colKey: 'createdAt',
      cell: (h: any, { row }: { row: any }) => {
        return <div>{formatDateTime(Number(row.createdAt))}</div>;
      },
    },
    {
      title: '操作',
      width: 136,
      colKey: 'operate',
      cell: (h: any, { row }: { row: any }) => {
        return (
          <div class="flex gap-8">
            <div
              class="text-#4D5EFF w-max p-4px rounded-4px cursor-pointer hover:bg-#EAECFF"
              onClick={(e) => {
                e.stopPropagation();
                handleEdit(row);
              }}
            >
              编辑
            </div>
            <div
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(row);
              }}
              class="text-#4D5EFF w-max p-4px rounded-4px cursor-pointer hover:bg-#EAECFF"
            >
              删除
            </div>
          </div>
        );
      },
    },
  ],
});
</script>

<style scoped lang="less">
.news-warp {
  position: relative;
  height: 100%;
  padding: 0 16px;
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .RTable {
    flex: 1;
  }
}

.tabs-warp {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #eceff5;

  .tabs-warp-list {
    display: flex;
    height: 32px;
    min-width: 80px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #d5dbe4);
    background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
    cursor: pointer;
    color: var(--icon-kyy_color_icon_deep, #516082);
    text-align: center;
    font-size: 14px;
    font-weight: 600;

    &:hover {
      color: #707eff;
      border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_hover, #707eff);
      background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_hover, #dbdfff);
    }
  }

  .active-tab {
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4d5eff);
    color: var(--lingke-White-100, #fff);
    text-align: center;
    font-feature-settings:
      'clig' off,
      'liga' off;
    font-size: 14px;
    font-weight: 600;
    border: 1px solid transparent;
  }
}
</style>
