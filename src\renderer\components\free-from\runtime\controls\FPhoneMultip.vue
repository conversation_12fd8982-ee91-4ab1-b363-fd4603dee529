<template>
  <div class="F-input">
    <!--		<div class="F-input-top">-->
    <!--			<i class="require" v-if="attrs.required">*</i>-->
    <!--			<span class="F-input-title">{{ attrs.name }}</span>-->
    <!--		</div>-->
    <div>
      <t-form-item
        :rules="
          [
            {
              message: `至少保留一手机号`,
              validator: validator,
              required: props.attrs.required,
              trigger: 'blur',
            },
          ]
        "
        :name="props.attrs.id"
      >
        <template #label>
          <span style="flex: 1">{{ props.attrs.name }}</span>
        </template>
        <div class="email">
          <div
            v-for="(item, itemIndex) in props.attrs.value"
            :key="itemIndex"
            class="inputItem"
          >

            <t-input-adornment>
              <template #prepend>
                <t-select v-replace-svg
                  v-model="item.code"
                  class="inputItem-code"
                  @change="codeChange($event, itemIndex)"
                >
                  <t-option key="86" label="+86" value="86" />
                  <t-option key="852" label="+852" value="852" />
                  <t-option key="853" label="+853" value="853" />
                </t-select>
              </template>
              <t-input
                ref="phoneInputRef"
                v-model="item.phone"
                class="inputItem-value"
                :placeholder="props.attrs.placeholder"
                @enter="(e)=>e.preventDefault()"
                :maxlength="100"
                clearable
                style="width: 80%"
                @change="change"
              />

            </t-input-adornment>
            <span class="inputItem-operate">
              <img
                v-show="itemIndex === 0"
                src="@/assets/partner/icon_addpend.png"
                class="cursor icon_addpend"
                @click="onAddEmail"
              />
              <img
                v-show="itemIndex !== 0"
                class="cursor icon_addpend"
                src="@/assets/approval/icons/0.icon_icon-delete-fill.png"
                @click="onDeleteEmail(itemIndex)"
              />
            </span>

          </div>
        </div>

        <!-- <div v-else class="F-input-content">
          {{ props.attrs.value || "--" }}
        </div> -->
      </t-form-item>
    </div>
  </div>
</template>

<script setup lang="ts" name="F-phone">
import { onActivated, ref, defineEmits } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emits = defineEmits([
  "attrsFn",
  "showMOdifyRecordFn",
  "controls-event",
  "needValidate",
]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  isCheckDetail: {
    type: Boolean,
    default: false,
  },
  attrs: {
    type: Object,
    default: () => {},
  },
  form: {
    type: Object,
    default: () => {},
  },
});

const findDuplicatePhone = (phoneList) => {
  const phoneSet = new Set();
  const duplicateNumbers = [];
  for (const item of phoneList) {
    const { phone } = item;
    if (phoneSet.has(phone)) {
      duplicateNumbers.push(phone);
    }
    phoneSet.add(phone);
  }
  return duplicateNumbers;
};

const validator = () => {
  if (props.attrs.value.length === 0) {
    return {
      message: '至少保留一手机号',
      required: true,
      trigger: "blur",
    };
  }
  const emptyLength = props.attrs.value.filter(
    (item) => item.phone === ""
  ).length;
  const errorLength = props.attrs.value.filter(
    (item) => !checkPhoneNumber(item.code, item.phone)
  ).length;
  if (emptyLength !== 0) {
    return {
      message: '手机号不能为空',
      required: true,
      trigger: "blur",
    };
  }
  if (errorLength !== 0) {
    return {
      message: '手机号格式不正确，请检查输入',
      required: true,
      trigger: "blur",
    };
  }

  const duplicates = findDuplicatePhone(props.attrs.value);
  if (duplicates.length > 0) {
    console.log(duplicates);
    return {
      message: t("free_form.fphoneag"),
      required: true,
      trigger: "blur",
    };
  }

  return { result: true, message: "", type: "success" };
};

const checkPhoneNumber = (mobileZone, mobile, hideToast = false) => {
  let reg = null;
  let passed = true;
  switch (Number(mobileZone)) {
    case 86:
      reg = /^[1][3-9]\d{9}$/;
      if (!reg.test(mobile)) {
        passed = false;
      }
      break;
    case 852:
      reg = /^([5|6|7|8|9])\d{7}$/;
      if (!reg.test(mobile)) {
        passed = false;
      }
      break;
    case 853:
      reg = /^[2|3|4|5|6|7|8|9]\d{7}$/;
      if (!reg.test(mobile)) {
        passed = false;
      }
      break;
  }
  return passed;
};

onActivated(() => {});

const attrs = ref(props.attrs);
const phoneInputRef = ref(null);
const codeChange = (e, index) => {
  emits("controls-event", props.attrs);
  phoneInputRef.value[index].focus();
  phoneInputRef.value[index].blur();
};
const change = () => {
  emits("controls-event", props.attrs);
};

const onAddEmail = () => {
  // eslint-disable-next-line vue/no-mutating-props
  if (
    props.attrs.addLength &&
    props.attrs.value.length >= props.attrs.addLength
  ) {
    MessagePlugin.warning(
      t("free_form.Contadd7", { count: props.attrs.addLength })
    );
    return false;
  }
  props.attrs.value.push({
    code: "86",
    phone: "",
  });
};
const onDeleteEmail = (index) => {
  props.attrs.value.splice(index, 1);
};
</script>

<style lang="less" scoped>
.flex-align {
  display: flex;
  align-items: center;
}

.F-input {
  :deep(.t-input__extra) {
    position: initial !important;
  }

  .F-input-top {
    height: 30px;
    line-height: 30px;

    .require {
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #da2d19;
    }

    .F-input-title {
      font-size: 14px;

      font-weight: 400;
      text-align: left;
      color: #13161b;
      line-height: 22px;
      margin-left: 1px;
    }
  }

  .F-input-content {
    width: 816px;
    word-wrap: break-word;
  }
}
:deep(.t-input-adornment__prepend) {
  background:none !important;
}
:deep(.t-input-adornment__prepend) {
  width: 96px !important;

  .t-input:hover {
    border: 0 solid !important;
  }
  .t-input {
    border: 0 solid!important;
  }
}
:deep(.t-input-adornment) {
  transition: all 0.15s linear !important;
  border-radius: 4px!important;
  border: 1px solid var(--select-kyy_color_select_border_default, #D5DBE4);
  &:hover {
    transition: all 0.15s linear !important;
    border: 1px solid var(--input-kyy-color-input-border-hover, #707EFF) !important;
    border-radius: 4px!important;
  }
}
:deep(.t-input-adornment>:last-child) {
  .t-input {
    // border-left:0!important;
    border: 0 solid !important;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      width: 1px;
      height: 16px;
      margin: auto;
      background: #ECEFF5;
    }
  }
}
.email {
  display: flex;
  flex-direction: column;
  width: 100%;
  .inputItem {
    display: flex;
    align-items: center;
    flex: 1;
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }

    &-value {
      flex: 1;
    }
    &-operate {
      flex: initial;
      margin-left: 8px;
      display: flex;
    }
  }
}
.icon_addpend{
  width: 20px;
  height: 20px;
}
</style>
