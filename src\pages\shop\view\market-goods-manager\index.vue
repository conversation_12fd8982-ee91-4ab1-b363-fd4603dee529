<template>
  <div class="px-16 market">
    <div v-show="currentTab === TabsEnum.OwnPlatfom && (table?.pagination?.total > 0 || isSearchEmpty)" class="absole">
      <t-button id="guide-product-list" class="w-108" theme="primary" @click="addProduct">
        <!-- <iconpark-icon class="text-[#fff] text-20" name="iconadd" /> -->
        <!-- <span class="ml-4"></span> -->
        添加市场商品
      </t-button>
    </div>
    <!-- {{ currentTab }}
    {{ route?.query }} -->
    <!-- {{currentTab}} -->
    <RTable
      ref="RTableRef"
      :filter="currentTab === TabsEnum.OwnPlatfom ? filter : filterOther"
      :table="currentTab === TabsEnum.OwnPlatfom ? table : tableOther"
      :tabs="tabs"
      :class="{
        abtable: currentTab === TabsEnum.OtherPlatfom,
      }"
      @change="onTableChange"
    >
      <template #empty>
        <template v-if="!loading">
          <REmpty
            v-if="currentTab === TabsEnum.OwnPlatfom"
            :name="(currentTab === TabsEnum.OwnPlatfom ? isSearchEmpty : isSearchOtherEmpty) ? 'no-result' : 'no-goods'"
            :tip="isSearchEmpty ? '搜索无结果' : '还没有添加市场商品，快去添加吧 ~'"
          >
            <template #bottom>
              <t-button
                v-if="!isSearchEmpty"
                id="guide-product-list"
                class="w-108 mt-16px!"
                theme="primary"
                @click="addProduct"
              >
                添加市场商品
              </t-button>
            </template>
          </REmpty>
          <REmpty
            v-else
            :name="(currentTab === TabsEnum.OwnPlatfom ? isSearchEmpty : isSearchOtherEmpty) ? 'no-result' : 'no-data'"
            :tip="(currentTab === TabsEnum.OwnPlatfom ? isSearchEmpty : isSearchOtherEmpty) ? '搜索无结果' : '暂无数据'"
          />
        </template>
      </template>

      <template v-if="currentTab === TabsEnum.OwnPlatfom" #toolbarContent>
        <div class="expand-desc">
          展示说明
          <t-tooltip theme="light" placement="bottom-right">
            <template #content>
              <div class="expandDesc max-w-284px">
                <div>
                  1、添加自身店铺的商品，并在自身【数字平台-市场】上展示（不展示“已售罄、已下架、已删除”状态的商品）
                </div>
                <br />
                <div>2、添加的商品仅作展示，若需要调整商品信息，请在【数智工场-店铺】进行操作</div>
              </div>
            </template>
            <iconpark-icon name="iconhelp" class="icon" />
          </t-tooltip>
        </div>
      </template>
      <template v-else #toolbarContent>
        <div class="tags">
          <div class="radioButtonBox">
            <div
              :class="[filterParams.promotionViewState === '' ? 'activeRadioButton' : 'defaultRadioButton']"
              @click.stop="radioChange('')"
            >
              全部
            </div>
            <div
              :class="[
                filterParams.promotionViewState === PromotionViewState.PENDING_AUDIT
                  ? 'activeRadioButton'
                  : 'defaultRadioButton',
              ]"
              @click.stop="radioChange(PromotionViewState.PENDING_AUDIT)"
            >
              待审核
              <template v-if="audiCount">({{ audiCount }})</template>
            </div>
            <div
              :class="[
                filterParams.promotionViewState === PromotionViewState.SALE
                  ? 'activeRadioButton'
                  : 'defaultRadioButton',
              ]"
              @click.stop="radioChange(PromotionViewState.SALE)"
            >
              已售卖
            </div>
            <div
              :class="[
                filterParams.promotionViewState === PromotionViewState.PARTIAL_SOLD_OUT
                  ? 'activeRadioButton'
                  : 'defaultRadioButton',
              ]"
              @click.stop="radioChange(PromotionViewState.PARTIAL_SOLD_OUT)"
            >
              部分售罄
              <!-- <span style="padding-left: 4px" v-if="marketAdNum > 0">{{ marketAdNum }}</span> -->
            </div>

            <t-popup placement="bottom-right" overlay-class-name="lssPop">
              <span class="cur-lan">
                <div
                  :class="[
                    [
                      PromotionViewState.SOLD_OUT,
                      PromotionViewState.OFF_SHELF,
                      PromotionViewState.AUDIT_UNPASS,
                      PromotionViewState.DELETED,
                    ].includes(filterParams?.promotionViewState)
                      ? 'activeRadioButton'
                      : 'defaultRadioButton',
                  ]"
                >
                  更多
                  <iconpark-icon class="icon-down" name="iconarrowdown"></iconpark-icon>
                </div>
              </span>
              <template #content>
                <div>
                  <t-list>
                    <t-list-item
                      v-for="(item, itemIndex) in options"
                      :key="itemIndex"
                      class="list-item cursor"
                      :class="{
                        lactive: filterParams.promotionViewState === item.id,
                      }"
                      style="text-align: left"
                      @click="radioChange(item.id)"
                    >
                      {{ item.content }}
                    </t-list-item>
                  </t-list>
                </div>
              </template>
            </t-popup>
          </div>
        </div>
      </template>
    </RTable>

    <!-- <t-guide v-if="guideVisible" v-model="guideCurrent" :steps="guideSteps" /> -->
    <!-- <GuideLine v-if="guideVisible" :step="guideCurrent" /> -->
  </div>
  <AdSelectShop
    ref="adSelectShopRef"
    :change-type="'multiple'"
    :table-type="'platform'"
    @change-shop="onSelectBatchGoods"
  />
</template>

<script setup lang="tsx">
import { RTable } from '@rk/unitPark';
import { computed, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import {
  // productSaleState,
  // productState,
  PromotionViewStateLabel,
} from '@pages/shop/view/market-goods-manager/constant.ts';
import ProductTableOperate from '@pages/shop/view/market-goods-manager/components/ProductTableOperate.vue';
import ProductTableOtherOperate from '@pages/shop/view/market-goods-manager/components/ProductTableOtherOperate.vue';
import { REmpty } from '@rk/unitPark';
import { debounce } from 'lodash';
// import { checkCreateProductEquity } from '@pages/shop/view/product-list/utils.ts';
// import { useGuide } from '@pages/shop/components/guide/useGuide';
// import GuideLine from '../../components/guide/GuideLine.vue';
import {
  listOtherPromotionProducts,
  listSelfPromotionProducts,
  batchAddSelfPromotionProducts, // 批量添加自己的数字平台推广商品
  getPendingAuditCount,
} from '@pages/shop/api/productPromotion.ts';
import { to } from 'await-to-js';
// src\types\api\store\product.model.ts
import {
  ProductStateEnum,
  ProductTypeEnum,
  ProductTypeMap,
  // ProductViewState,
  PromotionViewState,
} from '@types/api/store/product.model';
import dayjs from 'dayjs';
// src\pages\shop\components\adSelectShop.vue
import AdSelectShop from '@pages/shop/components/adSelectShop.vue';
import { MessagePlugin } from 'tdesign-vue-next';
import sdk from '@lynker-desktop/web';
// const router = useRouter();
const route = useRoute();
// const { guideCurrent, guideVisible, guideSteps } = useGuide();
const adSelectShopRef = ref(null);
const table = ref({
  attrs: {
    // loading: true,
    rowKey: 'promotionId',
    onRowClick: (context) => onRowClick(context),
  },
  columns: [
    {
      title: '商品信息',
      width: 320,
      cell: (h, { row }) => (
        <div class="flex gap-12">
          <img class="w-64 h-64 object-cover rounded-8" src={row.imgUrls?.[0]} alt="" />
          <t-tooltip content={row.title}>
            <div class="ellipsis-3">{row.title}</div>
          </t-tooltip>
        </div>
      ),
    },
    {
      title: '商品类型',
      width: 136,
      cell: (h, { row }: any) => <div>{ProductTypeMap.get(row.typ)}</div>,
    },
    {
      title: '添加时间',
      width: 168,
      cell: (h, { row }: any) => <div>{dayjs(row.createdAt * 1000)?.format('YYYY-MM-DD HH:mm')}</div>,
    },
    // {
    //   title: '价格',
    //   width: 200,
    //   cell: (h, { row }) => (
    //     <div>
    //       ￥{row.minPrice} ~ {row.maxPrice}
    //     </div>
    //   ),
    // },
    // {
    //   colKey: 'name',
    //   title: '库存',
    //   width: 136,
    //   cell: (h, { row }) => (
    //     <div>
    //       {row.minStock}-{row.maxStock}个
    //     </div>
    //   ),
    // },
    // {
    //   colKey: 'name',
    //   title: '配送服务',
    //   width: 88,
    //   cell: (h, { row }) => (
    //     <div class="leading-22">
    //       {row.selfPickupEnabled && <div>自提</div>}
    //       {row.intraCityEnabled && <div>同城配送</div>}
    //       {row.logisticsEnabled && <div>全国送</div>}
    //       {row.crossBorderEnabled && <div>跨境配送</div>}
    //     </div>
    //   ),
    // },
    {
      colKey: 'name',
      title: () => (
        <div class="custom-header">
          <span>状态</span>
          <t-tooltip
            content={
              <div class="tool">状态为“已售卖、部分售罄、已售罄”才展示在数字平台，“已售罄、已下架、已删除”不展示</div>
            }
          >
            <iconpark-icon name="iconhelp" class="icon" />
          </t-tooltip>
        </div>
      ),
      width: 144,
      cell: (h, { row }) => (
        <div class="flex items-center gap-4">
          <>
            <div class="kuang">
              <div
                class="w-8 h-8 rounded-1/2"
                style={{ backgroundColor: PromotionViewStateLabel[row.promotionViewState]?.tagColor }}
              ></div>
            </div>
            <div>{PromotionViewStateLabel[row.promotionViewState]?.label}</div>
          </>
        </div>
      ),
    },
    {
      colKey: 'name',
      title: '操作',
      width: 144,
      cell: (h, { row }) => <ProductTableOperate row={row} onReload={loadData} />,
    },
  ],
  list: [],
  pagination: {
    pageSize: 10,
    current: 1,
    total: 0,
  },
});
const tableOther = ref({
  attrs: {
    // loading: true,
    rowKey: 'promotionId',
    onRowClick: (context) => onRowClick(context),
  },
  columns: [
    {
      title: '商品信息',
      width: 216,
      cell: (h, { row }) => (
        <div class="flex gap-12">
          <img class="w-64 h-64 object-cover rounded-8" src={row.imgUrls?.[0]} alt="" />
          <t-tooltip content={row.title}>
            <div class="ellipsis-3">{row.title}</div>
          </t-tooltip>
        </div>
      ),
    },
    {
      title: '商品类型',
      width: 80,
      cell: (h, { row }: any) => <div>{ProductTypeMap.get(row.typ)}</div>,
    },
    {
      colKey: 'store',
      title: '所属店铺',
      width: 192,
      cell: (h, { row }) => (
        <div>
          <div class="leading-22">{row?.store?.teamName}</div>
          <div class="leading-22">{row?.store?.title}</div>
        </div>
      ),
    },
    {
      title: '申请时间',
      width: 152,
      cell: (h, { row }: any) => <div>{dayjs(row.createdAt * 1000)?.format('YYYY-MM-DD HH:mm')}</div>,
    },
    // {
    //   title: '价格',
    //   width: 200,
    //   cell: (h, { row }) => (
    //     <div>
    //       ￥{row.minPrice} ~ {row.maxPrice}
    //     </div>
    //   ),
    // },
    // {
    //   colKey: 'name',
    //   title: '库存',
    //   width: 136,
    //   cell: (h, { row }) => (
    //     <div>
    //       {row.minStock}-{row.maxStock}个
    //     </div>
    //   ),
    // },
    // {
    //   colKey: 'name',
    //   title: '配送服务',
    //   width: 88,
    //   cell: (h, { row }) => (
    //     <div class="leading-22">
    //       {row.selfPickupEnabled && <div>自提</div>}
    //       {row.intraCityEnabled && <div>同城配送</div>}
    //       {row.logisticsEnabled && <div>全国送</div>}
    //       {row.crossBorderEnabled && <div>跨境配送</div>}
    //     </div>
    //   ),
    // },
    {
      colKey: 'name',
      title: () => (
        <div class="custom-header">
          <span>状态</span>
          <t-tooltip
            content={
              <div class="tool">状态为“已售卖、部分售罄、已售罄”才展示在数字平台，“已售罄、已下架、已删除”不展示</div>
            }
          >
            <iconpark-icon name="iconhelp" class="icon" />
          </t-tooltip>
        </div>
      ),
      width: 104,
      cell: (h, { row }: any) => (
        <div class="flex items-center gap-4">
          <>
            <div class="kuang">
              <div
                class="w-8 h-8 rounded-1/2"
                style={{ backgroundColor: PromotionViewStateLabel[row.promotionViewState]?.tagColor }}
              ></div>
            </div>
            <div>{PromotionViewStateLabel[row.promotionViewState]?.label}</div>
          </>
        </div>
      ),
    },
    {
      colKey: 'name',
      title: '操作',
      width: 144,
      cell: (h, { row }) => <ProductTableOtherOperate row={row} onReload={loadOtherData} />,
    },
  ],
  list: [],
  pagination: {
    pageSize: 10,
    current: 1,
    total: 12,
  },
});

const filter = {
  attrs: {
    size: 'small',
    labelWidth: '80px',
    placeholder: '搜索商品名称',
  },
  advanced: {
    submitText: '搜索',
    form: {
      list: [
        // {
        //   name: 'title',
        //   label: '申请组织 ',
        //   type: 'input',
        //   value: '',
        //   defaultValue: '',
        //   attrs: {
        //     clearable: true,
        //     placeholder: '请输入组织名称或ID',
        //     options: [],
        //   },
        // },
        {
          name: 'applyAtRange',
          label: '添加时间',
          type: 'dateRange',
          value: [],
          defaultValue: [],
          attrs: {
            clearable: true,
            placeholder: ['开始时间', '结束时间'],
            options: [],
          },
        },
        {
          name: 'typ',
          label: '商品类型',
          type: 'select',
          value: '',
          defaultValue: '',
          attrs: {
            clearable: true,
            placeholder: '请选择商品类型',
            // PRODUCT_TYPE_UNKNOWN、PRODUCT_TYPE_RETAIL、PRODUCT_TYPE_CHECK
            options: [
              {
                value: '',
                label: '全部',
              },
              {
                value: ProductTypeEnum.PRODUCT_TYPE_RETAIL,
                label: '零售型',
              },
              {
                value: ProductTypeEnum.PRODUCT_TYPE_CHECK,
                label: '核销型',
              },
            ],
          },
        },
        {
          name: 'status',
          label: '商品状态',
          type: 'select',
          value: '',
          defaultValue: '',
          attrs: {
            clearable: true,
            placeholder: '请选择商品状态',
            // 已售卖、部分售罄、已售罄、已下架、已删除
            // PRODUCT_STATE_UNKNOWN
            // PRODUCT_STATE_SALE
            // PRODUCT_STATE_OFF_SHELF
            // PRODUCT_STATE_DRAFT
            // PRODUCT_STATE_PENDING_AUDIT
            // PRODUCT_STATE_AUDIT_UNPASS
            // PRODUCT_STATE_DELETED
            options: [
              {
                value: '',
                label: '全部',
              },
              {
                value: ProductStateEnum.PRODUCT_VIEW_STATE_SALE,
                label: '已售卖',
              },
              // 部分售罄
              {
                value: ProductStateEnum.PRODUCT_VIEW_STATE_PARTIAL_SOLD_OUT,
                label: '部分售罄',
              },
              // 已售罄
              {
                value: ProductStateEnum.PRODUCT_VIEW_STATE_SOLD_OUT,
                label: '已售罄',
              },
              {
                value: ProductStateEnum.PRODUCT_VIEW_STATE_OFF_SHELF,
                label: '已下架',
              },
              // 已删除
              {
                value: ProductStateEnum.PRODUCT_VIEW_STATE_DELETED,
                label: '已删除',
              },
            ],
          },
        },
      ],
    },
  },
};

const filterOther = {
  attrs: {
    size: 'small',
    labelWidth: '80px',
    placeholder: '搜索商品名称',
  },
  advanced: {
    submitText: '搜索',
    form: {
      list: [
        {
          name: 'storeTitle',
          label: '所属店铺 ',
          type: 'input',
          value: '',
          defaultValue: '',
          attrs: {
            clearable: true,
            placeholder: '请输入店铺名称',
            options: [],
            maxlength: 50,
          },
        },
        {
          name: 'storeTeam',
          label: '关联组织 ',
          type: 'input',
          value: '',
          defaultValue: '',
          attrs: {
            clearable: true,
            placeholder: '请输入组织名称或组织ID',
            options: [],
            maxLength: 50,
          },
        },
        {
          name: 'applyAtRange',
          label: '申请时间',
          type: 'dateRange',
          value: [],
          defaultValue: [],
          attrs: {
            clearable: true,
            placeholder: ['开始时间', '结束时间'],
            options: [],
          },
        },
        {
          name: 'typ',
          label: '商品类型',
          type: 'select',
          value: '',
          defaultValue: '',
          attrs: {
            clearable: true,
            placeholder: '请选择商品类型',
            // PRODUCT_TYPE_UNKNOWN、PRODUCT_TYPE_RETAIL、PRODUCT_TYPE_CHECK
            options: [
              {
                value: '',
                label: '全部',
              },
              {
                value: ProductTypeEnum.PRODUCT_TYPE_RETAIL,
                label: '零售型',
              },
              {
                value: ProductTypeEnum.PRODUCT_TYPE_CHECK,
                label: '核销型',
              },
            ],
          },
        },
      ],
    },
  },
};
enum TabsEnum {
  OwnPlatfom = 1,
  OtherPlatfom = 2,
}
const tabs = ref({
  defaultInfo: {
    value: TabsEnum.OwnPlatfom,
  },
  attrs: {
    theme: 'normal',
  },
  list: [
    {
      value: TabsEnum.OwnPlatfom,
      label: '自身数字平台',
      name: 'ownPlatfom',
    },
    {
      value: TabsEnum.OtherPlatfom,
      label: '其它数字平台',
      name: 'otherPlatfom',
    },
  ],
  pagination: {
    number: 1,
    size: 10,
    total: 12,
  },
});

const options = [
  {
    content: '已售罄',
    id: PromotionViewState.SOLD_OUT,
  },
  {
    content: '已下架',
    id: PromotionViewState.OFF_SHELF,
  },
  {
    content: '已拒绝',
    id: PromotionViewState.AUDIT_UNPASS,
  },
  {
    content: '已删除',
    id: PromotionViewState.DELETED,
  },
];

const filterParams: any = reactive({
  title: null,
  'applyAtRange.startTime': null,
  'applyAtRange.endTime': null,
  typ: null, // 商品类型
  viewState: null, // 商品状态
  promotionViewState: '', // 推广状态视图

  storeTitle: null,
  storeTeam: null,
});

const isSearchEmpty = computed(() => {
  return (
    table.value.list.length < 1 &&
    (filterParams.title ||
      filterParams['applyAtRange.startTime'] ||
      filterParams['applyAtRange.endTime'] ||
      filterParams.typ ||
      filterParams.viewState)
  );
});
const isSearchOtherEmpty = computed(() => {
  return (
    tableOther.value.list.length < 1 &&
    (filterParams.title ||
      filterParams['applyAtRange.startTime'] ||
      filterParams['applyAtRange.endTime'] ||
      filterParams.typ ||
      filterParams.promotionViewState)
  );
});
// 用来定制样式的
// const isSearchvOther = computed(() => {
//   return (
//     filterParams['applyAtRange.startTime'] ||
//     filterParams['applyAtRange.endTime'] ||
//     filterParams.typ ||
//     filterParams.storeTitle ||
//     filterParams.storeTeam
//   );
// });

const onRowClick = ({ row }: any) => {
  console.log('点击行', row);
};
const currentTab = ref(tabs.value?.defaultInfo?.value);

// watch(currentTab, (newVal, oldVal) => {
//   console.log('currentTab', newVal, oldVal);
//   if (newVal === TabsEnum.OwnPlatfom) {
//     filterOther.value = filterOther.value;
//   } else {
//     filterOther.value = filterOther.value;
//   }
// });
const onTableChange = debounce(({ tabs, filter, pageInfo }, name) => {
  console.log(`表格${name}变化`, {
    tabs,
    filter,
    pageInfo,
  });
  tabs = typeof tabs === 'number' ? tabs : 1;
  console.log('currentTab.value', currentTab.value, tabs);
  if (currentTab.value !== tabs) {
    pageInfo.current = 1;
  }
  currentTab.value = tabs;
  filterParams.title = filter?.searchVal ?? null;
  filterParams['applyAtRange.startTime'] = filter?.applyAtRange?.[0] ?? null;
  filterParams['applyAtRange.endTime'] = filter?.applyAtRange?.[1] ?? null;
  filterParams.typ = filter?.typ ?? null;
  filterParams.viewState = filter?.status ?? null;
  filterParams.storeTitle = filter?.storeTitle ?? null;
  filterParams.storeTeam = filter?.storeTeam ?? null;

  // 动态计算表格高度
  const headerEl = document.querySelector('.header.sticky');
  const tableEl = document.querySelector('.RK-Table');

  if (headerEl && tableEl) {
    const headerHeight = headerEl.clientHeight;
    tableEl.style.height = `calc(100vh - ${headerHeight}px - 8px)`;
  }

  // 获取待审核梳理
  sdk.ipcRenderer.invoke('IM-refresh');

  if (currentTab.value === TabsEnum.OwnPlatfom) {
    table.value.pagination.current = name === 'table' ? pageInfo.current : 1;
    table.value.pagination.pageSize = pageInfo.pageSize;
    table.value.pagination.total = pageInfo.total;
    loadData();
  } else if (currentTab.value === TabsEnum.OtherPlatfom) {
    tableOther.value.pagination.current = name === 'table' ? pageInfo.current : 1;
    tableOther.value.pagination.pageSize = pageInfo.pageSize;
    tableOther.value.pagination.total = pageInfo.total;
    const toolbarWrap = document.querySelector('.header');
    const filterResult = document.querySelector('.filter-result');
    if (toolbarWrap && filterResult) {
      // filterResult.classList.add('filter-result-other');

      toolbarWrap.appendChild(filterResult);
    }
    loadOtherData();
  } else {
    table.value.pagination.current = name === 'table' ? pageInfo.current : 1;
    table.value.pagination.pageSize = pageInfo.pageSize;
    table.value.pagination.total = pageInfo.total;
    loadData();
  }
}, 200);
const loading = ref(false);
const loadData = async () => {
  const params = {
    ...filterParams,
    'applyAtRange.startTime': filterParams['applyAtRange.startTime']
      ? Math.floor(dayjs(filterParams['applyAtRange.startTime']).valueOf() / 1000)
      : null,
    'applyAtRange.endTime': filterParams['applyAtRange.endTime']
      ? Math.floor(dayjs(filterParams['applyAtRange.endTime']).endOf('day').valueOf() / 1000)
      : null,
    'page.size': table.value.pagination.pageSize,
    'page.number': table.value.pagination.current,
    teamId: route.query?.teamId,
    onlyAdded: true,
  };
  console.log('查询参数', params);
  loading.value = true;
  const [err, result] = await to(getlistSelfPromotionProductsAxios(params));
  loading.value = false;
  if (err) {
    console.log('查询商品列表失败', err);
    return;
  }

  console.log('查询商品列表成功', result);

  const list = result?.products || [];
  // for (let i = 0; i < 10; i++) {
  //   list.push({
  //     promotionId: i,
  //     title: '捣虽然此外蠕动只要畅销书俄罗斯传统头巾平坦抚' + i,
  //     spuId: '71',
  //     typ: 'PRODUCT_TYPE_RETAIL',
  //     imgUrls: [
  //       'https://loremflickr.com/400/400?lock=1047346935932116',
  //       'https://loremflickr.com/400/400?lock=4994037033928000',
  //       'https://loremflickr.com/400/400?lock=7996332590720683',
  //     ],
  //     channelCount: 25,
  //     promotionEnabledAt: 'anim exercitation esse nisi',
  //     promotionState: 'PRODUCT_STATE_OFF_SHELF',
  //     channelState: 'PRODUCT_STATE_DELETED',
  //     reason: 'consectetur Ut consequat',
  //     store: {
  //       id: 8,
  //       title: '索性也不不像比所以顾问难道',
  //       teamId: '66',
  //       teamName: '绍熙成',
  //     },
  //     publishedAt: 'velit in do sit',
  //     createdAt: '2025-06-27 23:26:41',
  //     updatedAt: '2024-11-22',
  //     viewState: 'PRODUCT_VIEW_STATE_UNKNOWN',
  //     promotionViewState: 'PROMOTION_VIEW_STATE_PARTIAL_SOLD_OUT',
  //     state: ((): string => {
  //       const states = [
  //         'PRODUCT_STATE_SALE',
  //         'PRODUCT_STATE_OFF_SHELF',
  //         'PRODUCT_STATE_DRAFT',
  //         'PRODUCT_STATE_PENDING_AUDIT',
  //         'PRODUCT_STATE_AUDIT_UNPASS',
  //         'PRODUCT_STATE_DELETED',
  //         'PRODUCT_STATE_PARTIAL_SOLD_OUT',
  //       ];
  //       return states[Math.floor(Math.random() * states.length)];
  //     })(),
  //     saleState: ((): string => {
  //       const states = ['SALE_STATE_UNKNOWN', 'SALE_STATE_SALE', 'SALE_STATE_PARTIAL_SOLD_OUT', 'SALE_STATE_SOLD_OUT'];
  //       return states[Math.floor(Math.random() * states.length)];
  //     })(),
  //   });
  // }
  table.value.list = list;
  table.value.pagination.total = result?.total || 0;
};

const loadOtherData = async () => {
  onGetAudiCount();
  loading.value = true;
  const params = {
    ...filterParams,
    'applyAtRange.startTime': filterParams['applyAtRange.startTime']
      ? Math.floor(dayjs(filterParams['applyAtRange.startTime']).valueOf() / 1000)
      : null,
    'applyAtRange.endTime': filterParams['applyAtRange.endTime']
      ? Math.floor(dayjs(filterParams['applyAtRange.endTime']).endOf('day').valueOf() / 1000)
      : null,
    'page.size': tableOther.value.pagination.pageSize,
    'page.number': tableOther.value.pagination.current,
    selfTeamId: route.query?.teamId,
  };
  console.log('查询参数', params);

  const [err, result] = await to(getlistOtherPromotionProductsAxios(params));
  loading.value = false;
  if (err) {
    console.log('查询商品列表失败', err);
    return;
  }

  console.log('查询商品列表成功', result);

  const list = result?.products || [];
  // for (let i = 0; i < 10; i++) {
  //   list.push({
  //     id: i,
  //     title: '捣虽然此外蠕动只要畅销书俄罗斯传统头巾平坦抚' + i,
  //     spuId: '71',
  //     typ: 'PRODUCT_TYPE_RETAIL',
  //     imgUrls: [
  //       'https://loremflickr.com/400/400?lock=1047346935932116',
  //       'https://loremflickr.com/400/400?lock=4994037033928000',
  //       'https://loremflickr.com/400/400?lock=7996332590720683',
  //     ],
  //     channelCount: 25,
  //     promotionEnabledAt: 'anim exercitation esse nisi',
  //     promotionState: 'PRODUCT_STATE_OFF_SHELF',
  //     channelState: 'PRODUCT_STATE_DELETED',
  //     reason: 'consectetur Ut consequat',
  //     store: {
  //       id: 8,
  //       title: '索性也不不像比所以顾问难道',
  //       teamId: '66',
  //       teamName: '绍熙成',
  //     },
  //     publishedAt: 'velit in do sit',
  //     createdAt: '2025-06-27 23:26:41',
  //     updatedAt: '2024-11-22',
  //     viewState: 'PRODUCT_VIEW_STATE_UNKNOWN',
  //     promotionViewState: 'PROMOTION_VIEW_STATE_PARTIAL_SOLD_OUT',
  //     state: ((): string => {
  //       const states = [
  //         'PRODUCT_STATE_SALE',
  //         'PRODUCT_STATE_OFF_SHELF',
  //         'PRODUCT_STATE_DRAFT',
  //         'PRODUCT_STATE_PENDING_AUDIT',
  //         'PRODUCT_STATE_AUDIT_UNPASS',
  //         'PRODUCT_STATE_DELETED',
  //         'PRODUCT_STATE_PARTIAL_SOLD_OUT',
  //       ];
  //       return states[Math.floor(Math.random() * states.length)];
  //     })(),
  //     saleState: ((): string => {
  //       const states = ['SALE_STATE_UNKNOWN', 'SALE_STATE_SALE', 'SALE_STATE_PARTIAL_SOLD_OUT', 'SALE_STATE_SOLD_OUT'];
  //       return states[Math.floor(Math.random() * states.length)];
  //     })(),
  //   });
  // }
  tableOther.value.list = list;
  tableOther.value.pagination.total = result?.total || 0;
};

const audiCount = ref(0);
const onGetAudiCount = async () => {
  const [err, res] = await to(getPendingAuditCount({ teamId: route?.query?.teamId }));
  console.log(res);
  if (err) {
    console.log(err?.message);
  }
  const { data } = res;
  audiCount.value = data?.count || 0;
  tabs.value.list[1].label = data?.count ? `其它数字平台(${audiCount.value})` : '其它数字平台';
};

const radioChange = (value: any) => {
  console.log('radioChange', value);
  filterParams.promotionViewState = value;
  tableOther.value.pagination.current = 1;
  loadOtherData();
  // pageChange({
  //   current: 1,
  //   pageSize: 10,
  // });
};

const getlistSelfPromotionProductsAxios = async (params: any) => {
  const [err, res] = await to(listSelfPromotionProducts(params, params?.teamId));
  if (err) {
    console.log('查询商品列表失败', err);
    throw err;
  }
  return res.data;
};

const getlistOtherPromotionProductsAxios = async (params: any) => {
  const [err, res] = await to(listOtherPromotionProducts(params));
  if (err) {
    console.log('查询商品列表失败', err);
    throw err;
  }
  return res.data;
};

// 新建商品
const addProduct = async () => {
  // const result = await checkCreateProductEquity();
  // if (result) {
  //   await router.push('/product-create');
  // }
  adSelectShopRef.value?.openWin(route?.query?.teamId);
};

const onSelectBatchGoods = async (data: any) => {
  console.log('onSelectBatchGoods', data);
  if (data?.length < 1) {
    return;
  }
  const params: any = {
    teamId: route.query?.teamId,
    // channelType: '',
    spuIds: data,
  };
  console.log('params:', params);
  const [err, res] = await to(batchAddSelfPromotionProducts(params));
  if (err) {
    console.log('批量添加商品失败', err);
    return;
  }
  console.log('批量添加商品成功', res);
  MessagePlugin.success('批量添加商品成功');
  loadData();
};
// watch(route.query?.unix, (newVal, oldVal) => {
//   console.log('newVal', newVal);
//   console.log('oldVal', oldVal);
//   if (newVal?.origin === 'OtherPlatfom') {
//     currentTab.value = TabsEnum.OtherPlatfom;
//     tabs.value.defaultInfo.value = TabsEnum.OtherPlatfom;
//   }
// });

onMounted(() => {
  tabs.value.defaultInfo.value = TabsEnum.OwnPlatfom;
  if (route?.query?.origin?.includes('OtherPlatfom')) {
    currentTab.value = TabsEnum.OtherPlatfom;
    tabs.value.defaultInfo.value = TabsEnum.OtherPlatfom;
  }
  // MessagePlugin.error('当前页面仅支持数字平台');
  loadData();
  loadOtherData();
  sdk.ipcRenderer.invoke('IM-refresh');

  window.addEventListener('focus', () => {
    loadOtherData();
  });
});
</script>

<style lang="less" scoped>
:deep(.t-table__empty-row) {
  height: 456px !important;
}
.expandDesc {
  padding: 12px 8px;
  color: var(--kyy_color_popcomfirm_content, #516082);

  /* kyy_fontSize_2/regular */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
:deep(.kuang) {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-item {
  width: 84px;
  padding: 0 8px;
}

.market {
  height: 100%;
  background-color: #fff;
}
:deep(.filter-result) {
  display: flex;
  // align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  .clear {
    color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    display: flex;
    align-items: center;
    gap: 4px;
  }
  .t-tag {
    background: var(--kyy_color_tag_bg_gray, #eceff5);
    color: var(--kyy_color_tag_text_black, #1a2139);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .filter-result-title {
    color: var(--text-kyy_color_text_2, #516082);
    /* kyy_fontSize_2/bold */
    // font-family: "PingFang SC";
    font-size: 14px;
    // font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
}
:deep(.abtable) {
  .toolbar-wrap {
    .toolbar-box {
      order: 2;
      display: flex;
      justify-content: flex-end;
    }
    .toolbarContent {
      order: 1;
    }
  }
}
:deep(.searchv) {
  .table-wrap {
    padding-top: 42px;
  }
}
.tags {
  .t-radio-group {
    background-color: transparent;
    border: none;
    .t-radio-button:before {
      content: none;
    }
    .t-radio-button {
      margin-right: 8px;
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
      background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
    }
    .t-is-checked {
      border: 1px solid var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);
      background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);
    }
  }

  .radioButtonBox {
    display: flex;
    gap: 8px;
    .defaultRadioButton {
      display: flex;
      height: 32px;
      cursor: pointer;
      min-width: 80px;
      min-height: 32px;
      max-height: 32px;
      padding: 0px 16px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
      background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
      color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
      text-align: center;

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
      &:hover {
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        border: 1px solid var(--brand-kyy_color_brand_hover, #707eff);
        background: var(--bg-kyy_color_bgBrand_foucs, #dbdfff);
        color: var(--brand-kyy_color_brand_hover, #707eff);
        text-align: center;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
    }
    .activeRadioButton {
      display: flex;
      height: 32px;
      min-width: 80px;
      cursor: pointer;
      min-height: 32px;
      max-height: 32px;
      padding: 0px 16px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);
      color: var(--lingke-white-100, #fff);
      text-align: center;
      font-feature-settings:
        'clig' off,
        'liga' off;

      /* Body meduim */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }
  }
}
.lactive {
  border-radius: var(--kyy_radius_dropdown_s, 4px);
  background: var(--kyy_color_dropdown_bg_active, #e1eaff);
  color: var(--kyy_color_dropdown_text_active, #4d5eff);

  font-size: 14px;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.absole {
  position: absolute;
  top: 0;
  right: 0;
  // left: 400;
  height: 48px;
  z-index: 54;
  display: flex;
  align-items: center;
  padding-right: 16px;
}

:deep(.RTable) {
  .header {
    z-index: 38 !important;
    position: unset !important;
  }
}
:deep(.custom-header) {
  display: flex;
  align-items: center;
  gap: 4px;
  .icon {
    font-size: 20px;
    color: #828da5;
  }
}
.expand-desc {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-kyy_color_text_1, #1a2139);

  font-size: 14px;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  .icon {
    font-size: 20px;
    color: #828da5;
  }
}
:deep(.RK-Table) {
  height: calc(100vh - 128px);
  overflow: auto;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
  .t-table {
    .t-table__body tr:not(.t-table__empty-row) {
      &:hover {
        background-color: #f3f6fa !important;
        cursor: pointer;
      }
    }

    td:last-child {
      padding: 8px !important;
    }
  }
}
</style>
