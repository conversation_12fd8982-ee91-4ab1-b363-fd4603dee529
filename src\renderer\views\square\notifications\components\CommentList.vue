<template>
  <div class="content-wrap">
    <template v-for="(item, index) in dataList" :key="index">
      <CategoryTitle :count="count" :index="index" />

      <div class="card-item" @click="outClick(item)">
        <KyyAvatar
          class="avatar"
          avatar-size="44px"
          shape="circle"
          :image-url="item.square.avatar"
          :user-name="item.square.name"
          @click.stop="goHomePage(item.square)"
        />

        <div class="main">
          <div class="name-wrap">
            <SquareCertIcon :square="item.square" class="mr-4" />
            <div class="name line-1" @click.stop="goHomePage(item.square)">
              {{ item.square.remark || item.square.name }}
            </div>
            <!--他人回复他人的评论，算作"评论"我的动态-->
            <div class="time">{{ item.createTime }} {{ item.targetText }}</div>
          </div>

          <div v-if="item.res.comment.deleted" class="content">{{ $t('square.post.commentDeleted') }}</div>
          <div v-else class="content">{{ item.res.comment.content }}</div>
          <div v-if="item.res.comment.replyComment" class="desc line-1">
            <div class="v-line" />
            <template v-if="item.res.comment.replyComment.deleted">{{ $t('square.post.commentDeleted') }}</template>
            <template v-else>
              <div class="name line-1">{{ item.res.comment.replyComment.squareName }}</div>：
              <div class="line-1">{{ item.res.comment.replyComment.content }}</div>
            </template>
          </div>

          <div class="footer">
            <div class="toolbar">
              <div v-if="!item.res.comment.deleted" class="item comment" @click.stop="itemClick('comment', item)">
                <iconpark-icon name="iconreply" class="mr-4" /> {{ $t('square.post.replyComment') }}
              </div>
              <div
                v-if="!item.res.comment.deleted"
                :class="['item like', { active: item.liked }]"
                @click.stop="itemClick('like', item)"
              >
                <!-- <iconpark-icon v-if="item.liked" name="iconlikefill" class="icon" />
                <iconpark-icon v-else name="iconlike" class="icon" /> -->
                <LikeAnimated :liked="item.liked" />
              </div>
            </div>
          </div>
        </div>

        <!--动态-->
        <template v-if="item.res.comment.resourceType === ResourceType.Post && !item.res.post.deleted">
          <div v-if="item.res.post.firstPicture || item.videoUrl" class="img-wrap">
            <t-image :src="getOSSImageResize(item.res.post.firstPicture) || item.videoUrl" fit="cover" class="img" />
            <t-icon
              v-if="item.videoUrl"
              name="play-circle"
              size="30"
              class="play-icon"
            />
          </div>

          <!--平台风采 -->
          <div v-else-if="item.res.post.postType === PostType.Fengcai" class="img-wrap fcbox">
            <t-image
              :src="item.res?.post.fengcai?.fields?.img"
              fit="cover"
              class="img"
              lazy
            />
            <div class="fc-tag">
              <iconpark-icon name="icon16elegance" class="icon text-20" />
            </div>
          </div>

          <!--党建 -->
          <div v-else-if="item.res.post.postType === PostType.PartyBuilding" class="img-wrap fcbox">
            <t-image
              :src="item.res?.post.partyBuilding?.fields?.img"
              fit="cover"
              class="img"
              lazy
            />
            <div class="fc-tag">
              <iconpark-icon name="icon-party" class="icon text-20" />
            </div>
          </div>

          <!--组织介绍 -->
          <div v-else-if="item.res.post.postType === PostType.TeamIntro" class="img-wrap fcbox">
            <t-image
              v-if="item.res?.post.teamIntro?.fields?.img"
              :src="item.res?.post.teamIntro?.fields?.img"
              fit="cover"
              class="img"
              lazy
            />
            <img
              v-else
              src="@/assets/business/info_def.png"
              fit="cover"
              class="img"
              lazy
            >
            <!-- <iconpark-icon v-else name="icon-logo" class="cl-logo" /> -->
            <div class="fc-tag">
              <iconpark-icon name="icon-about-us" class="icon text-20" />
            </div>
          </div>

          <!--荣誉 -->
          <div v-else-if="item.res.post.postType === PostType.TeamHonorRoll" class="img-wrap fcbox">
            <t-image
              :src="item.res?.post.teamHonorRoll?.fields?.img"
              fit="cover"
              class="img"
              lazy
            />
            <div class="fc-tag">
              <iconpark-icon name="icon-about-us" class="icon text-20" />
            </div>
          </div>

          <!-- 历程 -->
          <div v-else-if="item.res.post.postType === PostType.TeamHistory" class="img-wrap fcbox">
            <t-image
              v-if="item.res?.post.teamHistory?.fields?.img"
              :src="item.res?.post.teamHistory?.fields?.img"
              fit="cover"
              class="img"
              lazy
            />
            <iconpark-icon v-else name="icon-logo" class="cl-logo" />
            <div class="fc-tag">
              <iconpark-icon name="icon-about-us" class="icon text-20" />
            </div>
          </div>

          <div v-else class="text-wrap">
            <div class="inner">
              <template v-if="item.res.post.postType === PostType.AlbumNode">
                <template v-if="item.res.post.text">拾光相册:</template>
                <template v-else>[拾光相册]</template>
              </template>
              <iconpark-icon
                v-if="item.res.post.postType === PostType.Article"
                name="iconlink"
                class="text-16! color-text-3"
                style="vertical-align: -3px"
              />
              {{ item.res.post.text }}
              <template v-if="item.res.post.postType === PostType.Forward && !item.res.post.text">
                {{
                  item.res.post.forwardPostSquareName }}:
              </template>
            </div>
          </div>
        </template>

        <!--时光相册节点-->
        <template v-if="item.res.comment.resourceType === ResourceType.AlbumNode">
          <div v-if="item.res.albumNode?.images?.length && item.res.albumNode?.nodeType === 1" class="img-wrap album">
            <template v-for="(img, idx) in item.albumNodeImages" :key="idx">
              <t-image
                v-if="img.url"
                :src="getOSSImageResize(img.url)"
                fit="cover"
                class="img"
                :style="`z-index: ${idx + 1};right: ${idx * 6}px;top: ${idx * 6}px`"
              />
              <div v-else class="img empty-img" :style="`z-index: ${idx + 1};right: ${idx * 6}px;top: ${idx * 6}px`" />
            </template>
          </div>
          <div v-else-if="item.res.albumNode?.nodeType !== 1" class="img-wrap album">
            <t-image :src="getOSSImageResize(item.res.albumNode?.cover)" fit="cover" class="img" />
            <iconpark-icon name="zhiboanniu" class="live-icon" />
          </div>
        </template>

        <!--时光相册图片-->
        <template v-if="item.res.comment.resourceType === ResourceType.AlbumNodeImage">
          <div v-if="item.res.albumNodeImage.url" class="img-wrap album">
            <t-image :src="getOSSImageResize(item.res.albumNodeImage.url)" fit="cover" class="img" />
          </div>
        </template>
      </div>
    </template>
  </div>

  <PostDetail
    v-if="detailId && detailVisible"
    :id="detailId"
    v-model="detailVisible"
    :force-top-comment-id="forceTopCommentId"
    :default-toolbar="defaultToolbar"
    @promoting="openPromoting"
  />

  <PostPromotion
    :id="detailId"
    :key="promotionKey"
    v-model="promotionVisible"
    @upgrade="openAnnualFeeDialog()"
    @buy="openAnnualFeeDialog(false)"
    @refresh="emit('refresh')"
  />

  <AnnualFeeDialog
    v-if="annualFeeVisible"
    v-model="annualFeeVisible"
    :square-id="store.squareId"
    :team-id="store.teamId"
    :z-index="1600"
    :upgrade="upgrade"
    @success="promotionKey++"
  />

  <!-- <InfiniteLoading :class="{ 'empty-wrap': !dataList.length }" @infinite="loadMore">
    <template #complete>
      <div v-if="!dataList.length" class="is-empty">
        <Empty name="no-comment" />
      </div>
    </template>
  </InfiniteLoading> -->
  <InfiniteLoading @infinite="loadMore">
    <template v-if="dataList?.length" #complete>
      <div class="line-wrap">
        <div class="line-1" />
        <div class="text">{{ t('components.infiniteLoading.noMoreData') }}</div>
        <div class="line-2" />
      </div>
    </template>
    <template v-else #complete><span /></template>
  </InfiniteLoading>
  <Empty v-if="!dataList?.length" name="no-comment" class="notice-empty" />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import { useI18n } from 'vue-i18n';
import Empty from '@/components/common/Empty.vue';
import InfiniteLoading from '@/components/InfiniteLoading/index.vue';
import { getNewsCommentsList } from '@/api/square/news';
import { PostType, SNAPSHOT } from '@/views/square/constant';
import useInfiniteLoad from '@/hooks/infiniteLoad';
import { timeAgo } from '@/views/square/utils/time';
import PostDetail from '@/views/square/components/post/PostDetail.vue';
import { useSquareStore } from '@/views/square/store/square';
import { checkPostInvisible, likeToggle } from '@/api/square/post';
import useNavigate from '@/views/square/hooks/navigate';
import KyyAvatar from '@/components/kyy-avatar/index.vue';
import CategoryTitle from '@/views/square/notifications/components/CategoryTitle.vue';
import { ResourceType } from '@/api/square/models/comment';
import { CommentNews } from '@/api/square/models/news';
import { useGoAlbum } from '@/views/square/notifications/utils';
import { getOSSImageResize } from '@/views/square/utils/OSSHelpler';
import SquareCertIcon from '@/views/square/components/SquareCertIcon.vue';
import LikeAnimated from '@/components/common/like-animated/index.vue';
import { useCheckTrial } from '../../hooks/checkTrial';
import PostPromotion from '@/views/square/post-promotion/components/PostPromotionDrawer.vue';
import AnnualFeeDialog from '@/views/square/components/annual-fee/AnnualFeeDialog.vue';
import { usePromotion } from '@/views/square/components/post/hooks';

const store = useSquareStore();
const { goHomePage } = useNavigate();
const { t } = useI18n();
const count = store.newsStats.comments;

type CommentNewsExt = CommentNews & {
  createTime: string;
  videoUrl: string;
  targetText: string;
  albumNodeImages: { url: string }[];
};

// 创建包装函数来传递teamId
const getListWithTeamId = (params: any) => getNewsCommentsList(params, store.isPersonal ? '-1' : store.teamId || '');

const { dataList, loadMore } = useInfiniteLoad<CommentNewsExt>(getListWithTeamId, {
  dataHandler: ((v) => {
    const { post, comment } = v.res;
    const result = {
      ...v,
      albumNodeImages: [],
      createTime: timeAgo(comment.createdAt),
    };
    const images = v.res.albumNode?.images;
    const { video } = post || {};

    const { resourceType, replyComment } = comment;
    switch (resourceType) {
      case ResourceType.AlbumNode:
        // 最多3项，不满3项填充为3项
        result.albumNodeImages = images.length > 3 ? images.slice(0, 3) : (new Array(3 - images.length).fill({})).concat(images);
        result.targetText = '评论了你的拾光相册';
        break;
      case ResourceType.AlbumNodeImage:
        result.targetText = '评论了你的拾光相册';
        break;
      case ResourceType.Post:
        result.videoUrl = video ? `${video}${SNAPSHOT}` : '';
        result.targetText = replyComment?.squareId === store.squareId ? t('square.post.replyComment1') : t('square.post.commentPost');
        break;
      default:
        break;
    }

    return result;
  }),
});

// 打开对应的详情页
const detailVisible = ref(false);
const detailId = ref('');
const defaultToolbar = ref('like');
const forceTopCommentId = ref('');

const { goAlbum } = useGoAlbum();
const { checkTrial } = useCheckTrial();

const {
  promotionVisible,
  promotionKey,
  annualFeeVisible,
  upgrade,
  openAnnualFeeDialog,
  openPromoting,
} = usePromotion(detailId);

const outClick = (item) => {
  // if (!store.isPersonal) return;
  itemClick('', item);
};

const itemClick = async (type: string, item: CommentNewsExt) => {
  if (checkTrial()) return;

  // 他人的动态：点击“回复评论”“点赞”按钮，或整个评论卡片，toast提示：这条动态已设置不可见
  // 本人的动态：点击“回复评论”按钮，提示：这条动态已设置不可见，回复后对方不会收到通知
  const isSelfPost = store.isSelfSquare(item.square.squareId);
  const squareId = isSelfPost ? item.square.squareId : store.squareId;
  if (
    item.res.post?.deleted
    || item.res.albumNode?.deleted
    || item.res.albumNodeImage?.deleted
  ) {
    await MessagePlugin.error(t('square.post.deleted'));
    return;
  }

  const { resourceType } = item.res.comment;
  if (resourceType === ResourceType.Post) {
    // 他人的动态：点击“回复评论”“点赞”按钮，或整个评论卡片，toast提示：这条动态已设置不可见
    // 本人的动态：点击“回复评论”按钮，提示：这条动态已设置不可见，回复后对方不会收到通知
    const [err, res] = await to(
      checkPostInvisible(item.res.post.postId, { square_id: squareId }),
    );
    if (!err) {
      const { invisible, deleted } = res.data;
      if (deleted) {
        await MessagePlugin.error(t('square.post.deleted'));
        return;
      }
      if (invisible) {
        await MessagePlugin.error(isSelfPost ? t('square.post.tip11') : t('square.post.invisible'));
        return;
      }
    }

    if (type) {
      defaultToolbar.value = type;
      // if (type === 'comment' && item.post.invisible) {
      //   await MessagePlugin.warning('这条动态已设置不可见，回复后对方不会收到通知');
      //   return;
      // }

      if (type === 'like') {
        const isSelfPost = store.isSelfSquare(+item.square.squareId);
        if (!isSelfPost) {
          const [err, res] = await to(
            likeToggle({
              resource_id: item.res.comment.commentId,
              resource_type: ResourceType.Comment,
            }),
          );
          if (!err) {
            // dataList[index].liked = res.data.like;
            // eslint-disable-next-line no-param-reassign
            item.liked = res.data.like;
          }
          return;
        }
      }
    } else {
      defaultToolbar.value = 'comment';
    }

    if (defaultToolbar.value === 'comment') {
      if (item.res.post.allowComment === false) {
        await MessagePlugin.warning(t('square.errReason.postCommentClosed'));
        return;
      }
      forceTopCommentId.value = item.res.comment.commentId;
    }

    detailId.value = item.res.post.postId;
    detailVisible.value = true;
  }

  // 跳转拾光相册
  if ([ResourceType.AlbumNode, ResourceType.AlbumNodeImage].includes(resourceType)) {
    goAlbum(item);
  }
};
</script>

<style lang="less" scoped>
@import "list";

.album {
  position: relative;
  cursor: pointer;

  .img {
    position: absolute;

    &.empty-img {
      width: 72px;
      height: 72px;
      background: #EFEFEF;
    }
  }
}

.toolbar {
  .item.active {
    .icon {
      color: @kyy_red_5;
      border-radius: 4px;
    }
  }
}

.fcbox {
  position: relative;
  border-radius: 8px;
  color: var(--lingke-contain-fonts, #516082);
  background: var(--bg-kyy-color-bg-deep, #f5f8fe);

  .cl-logo {
    font-size: 47px;
    color: #e2e6f5;
  }
}

.fc-tag {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  border-radius: 0px 4px;
  background: rgba(21, 21, 21, 0.30);
  position: absolute;
  right: 0px;
  top: 0px;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  .icon {
    color: #fff;
  }
}

.live-div {
  position: relative;
}

.live-icon {
  position: absolute;
  top: 40%;
  left: 40%;
  z-index: 101;
  font-size: 20px;
}
</style>
