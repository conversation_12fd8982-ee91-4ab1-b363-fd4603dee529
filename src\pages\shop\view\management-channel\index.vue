<template>
  <div class="workbench-management-channel">
    <div class="header-box">
      <div style="display: flex; align-items: center; gap: 8px">
        <div
          v-if="routeData?.promotionViewState"
          class="tag"
          :style="{
            backgroundColor: statusMap.find((item) => item.value === routeData?.promotionViewState).backgroundColor,
            color: statusMap.find((item) => item.value === routeData?.promotionViewState).color,
          }"
        >
          {{ statusMap.find((item) => item.value === routeData?.promotionViewState).label }}
        </div>
        <div class="header-box-title">{{ routeData?.spuName }}</div>
        <div class="lin"></div>
        <div class="detail-text" @click="goGoodsDetail">详情</div>
      </div>
      <t-button
        v-if="
          routeData?.promotionViewState === 'PRODUCT_VIEW_STATE_SALE' ||
          routeData?.promotionViewState === 'PRODUCT_VIEW_STATE_PARTIAL_SOLD_OUT'
        "
        style="width: 88px; margin-left: 24px"
        theme="primary"
        @click="openMap"
      >
        添加渠道
      </t-button>
    </div>
    <div class="content-box">
      <div class="data-box-header">
        <div class="top">
          <div class="btn-all">
            <div
              v-for="(item, index) in statusMappings"
              :key="index"
              :class="{ 'active-tab-item': activeTab === index, 'search-item': true }"
              @click="changeActiveTab(index)"
            >
              {{ item.label }}
            </div>
          </div>
          <div class="approval-time">
            <div @keyup.enter="getDataRun(true)">
              <t-input
                v-model.trim="drawerForm.teamName"
                style="width: 304px"
                placeholder="搜索组织名称"
                clearable
                @clear="getDataRun(true)"
                @blur="getDataRun(true)"
              >
                <template #prefixIcon>
                  <iconpark-icon style="font-size: 20px; color: #828da5" name="iconsearch"></iconpark-icon>
                </template>
              </t-input>
            </div>
            <div @click="openWin">
              <div v-if="paramsSuper" class="af-icon" @click="openWin">
                <iconpark-icon style="font-size: 20px; color: #3e4cd1" name="iconscreen"></iconpark-icon>
              </div>
              <div v-else class="f-icon" @click="openWin">
                <iconpark-icon style="font-size: 20px; color: #828da5" name="iconscreen"></iconpark-icon>
              </div>
            </div>
          </div>
        </div>
        <div v-if="paramsSuper" class="filter-res">
          <div class="tit">{{ t('approval.approval_data.sures') }}</div>
          <div v-if="params.channelType" class="stat te">
            <span>渠道类型：{{ typeOption.find((item) => item.id === params.channelType)?.title }}</span>
            <span class="close2" @click="clearFilterCategories('channelType')">
              <img src="@assets/<EMAIL>" alt="" />
            </span>
          </div>

          <div v-if="params['startTime'] || params['endTime']" class="ov-time te">
            <span>申请时间： {{ params['startTime'] }} ~ {{ params['endTime'] }}</span>
            <span class="close2" @click="clearFilterCategories('duration')">
              <img src="@assets/<EMAIL>" alt="" />
            </span>
          </div>

          <div class="icon clearFiltersBox" @click="clearFilters">
            <span class="clearFilters">{{ t('approval.approval_data.clearFilters') }}</span>
          </div>
        </div>
      </div>
      <div class="data-box-con">
        <t-table :row-key="'id'" :data="listData" :columns="columns" :hover="false">
          <template #teamName="{ row }">
            <div style="display: flex; align-items: center; gap: 4px">
              <div
                v-if="row.isSelf"
                style="
                  font-size: 12px;
                  width: 20px;
                  height: 20px;
                  background-color: #e8f0fb;
                  border-radius: 4px;
                  color: #4093e0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                "
              >
                自
              </div>
              <div>
                {{ row.teamName }}
              </div>
            </div>
          </template>
          <template #channelType="{ row }">
            <div class="title-box" :style="{ color: titleType.find((e) => e.value === row.channelType).color }">
              <img :src="titleType.find((e) => e.value === row.channelType).img" />
              {{ titleType.find((e) => e.value === row.channelType).text }}
            </div>
          </template>
          <template #zt="{ row }">
            <div style="display: flex; align-items: center; gap: 10px">
              <div
                class="dian"
                :style="{ backgroundColor: tagArr.find((item) => item.value === row.promotionViewState).color }"
              ></div>
              <span>
                {{ tagArr.find((item) => item.value === row.promotionViewState).text }}
              </span>
            </div>
          </template>
          <template #sqsj="{ row }">
            <div>
              <span>
                {{ dayjs(row.applyAt * 1000).format('YYYY-MM-DD HH:mm') }}
              </span>
            </div>
          </template>

          <template #title-slot-name>
            <div style="height: 20px; display: flex; align-items: center; gap: 4px">
              分佣比例
              <div style="height: 20px">
                <t-tooltip content="不同的组织的分账比例不一样，且存在一定浮动，最多不超过5%">
                  <iconpark-icon style="font-size: 20px; color: #828da5" name="iconhelp"></iconpark-icon>
                </t-tooltip>
              </div>
            </div>
          </template>
          <template #fybl="{ row }">
            <div style="display: flex; align-items: center; gap: 4px">
              <div v-if="row.shareRate">{{ row.shareRate }}%</div>
              <div v-else>--</div>
            </div>
          </template>

          <template #actions="{ row }">
            <div class="btn-box">
              <div
                v-if="
                  row.promotionViewState === 'PROMOTION_VIEW_STATE_SALE' ||
                  row.promotionViewState === 'PROMOTION_VIEW_STATE_PARTIAL_SOLD_OUT' ||
                  row.promotionViewState === 'PROMOTION_VIEW_STATE_SOLD_OUT'
                "
                class="btn-tiem"
                @click="downFn(row)"
              >
                下架
              </div>
              <t-tooltip
                v-if="
                  row.promotionViewState === 'PROMOTION_VIEW_STATE_AUDIT_UNPASS' ||
                  row.promotionViewState === 'PROMOTION_VIEW_STATE_OFF_SHELF'
                "
                placement="right"
                trigger="click"
                theme="light"
              >
                <template #content>
                  <div class="dow-tip">原因：{{ row.reason }}</div>
                </template>
                <div class="btn-tiem">原因</div>
              </t-tooltip>
              <t-popconfirm
                v-if="row.promotionViewState === 'PROMOTION_VIEW_STATE_AUDIT_UNPASS'"
                theme="warning"
                placement="right"
                :popup-props="{ overlayClassName: 'channel-popconfirm-btn' }"
                confirm-btn="申请"
                :content="`是否将该商品重新向“${row.teamName}组织”发起审核申请？`"
                @confirm="reApplyPromotionProductFn(row)"
              >
                <div class="btn-tiem btn-tiem-error" style="color: #d54941">重新申请</div>
              </t-popconfirm>
              <t-popconfirm
                v-if="row.promotionViewState === 'PROMOTION_VIEW_STATE_OFF_SHELF'"
                theme="warning"
                placement="right"
                :popup-props="{ overlayClassName: 'channel-popconfirm-btn' }"
                confirm-btn="申请"
                :content="`是否将该商品重新向“${row.teamName}组织”发起上架申请？`"
                @confirm="reapplicationFn(row)"
              >
                <div class="btn-tiem btn-tiem-error" style="color: #d54941">申请上架</div>
              </t-popconfirm>
            </div>
          </template>

          <template #empty>
            <div>
              <REmpty
                :name="listData.length === 0 && drawerForm.teamName ? 'no-result' : 'no-data'"
                :tip="listData.length === 0 && drawerForm.teamName ? t('zx.schedule.nosearch') : t('clouddisk.nodata')"
              />

              <!-- <img
                style="width: 200px; height: 200px; display: block; margin: 10vh auto 8px"
                src="@assets/public/public-page/no-data-available.png"
              />
              <div style="font-size: 14px; color: #516082; text-align: center">
                {{ t('clouddisk.nodata') }}
              </div> -->
            </div>
          </template>
        </t-table>
        <div v-if="total && total > 10" class="pagination">
          <t-pagination
            show-jumper
            :total="total"
            show-previous-and-next-btn
            :current="params['page']"
            @change="pageChange"
          />
        </div>
      </div>
    </div>
    <t-drawer
      v-model:visible="filterVisible"
      :close-btn="true"
      size="472px"
      class="filter-drawer"
      :header="t('approval.approval_data.sur')"
      @close="closeWin"
    >
      <template #closeBtn>
        <iconpark-icon class="title-icon" name="iconerror-a961a3n0" />
      </template>
      <!-- :close-on-overlay-click="false" -->
      <div class="form-boxxx">
        <div class="fitem">
          <div class="title">渠道类型</div>
          <div class="ctl">
            <t-select v-model="drawerForm.channelType" clearable :placeholder="t('approval.operation.select')">
              <t-option v-for="item in typeOption" :key="item.id" :value="item.id" :label="item.title"></t-option>
              <template #suffixIcon>
                <!-- <img src="@/assets/svg/icon_arrow_down.svg"> -->

                <iconpark-icon name="iconarrowdown" style="color: #828da5"></iconpark-icon>
              </template>
            </t-select>
          </div>
        </div>
        <div class="fitem">
          <div class="title">申请时间</div>
          <div class="ctl">
            <t-date-range-picker
              v-model="drawerForm.duration"
              format="YYYY-MM-DD"
              value-type="YYYY-MM-DD"
              style="width: 100%"
              :placeholder="[t('approval.approval_data.start_time'), t('approval.approval_data.end_time')]"
              clearable
            >
              <template #suffixIcon></template>
            </t-date-range-picker>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="foot">
          <t-button theme="default" style="width: 80px; font-weight: 600" variant="outline" @click="rest">
            重置
          </t-button>
          <t-button
            :disabled="
              drawerForm?.duration.length === 0 &&
              drawerForm?.rating === '' &&
              drawerForm?.keyword === '' &&
              drawerForm?.['comment.productSnapshot'] === '' &&
              drawerForm?.userName === ''
            "
            style="width: 80px; font-weight: 600"
            @click="getDataRunDr"
          >
            {{ t('approval.approval_data.cm') }}
          </t-button>
        </div>
      </template>
    </t-drawer>
    <t-dialog
      v-model:visible="batchVisible"
      width="484px"
      top="46px"
      header="下架原因"
      class="batch-edit"
      @close="((textData = ''), (batchVisible = false))"
    >
      <div class="batch-box">
        <div class="text" :class="errorShow ? 'error-border' : ''">
          <t-textarea
            v-model="textData"
            placeholder="请输入下架原因"
            maxlength="50"
            :autosize="{ minRows: 4, maxRows: 4 }"
            @input="inputFn"
          />
          <p v-if="errorShow" class="reason-error">请输入</p>
        </div>
      </div>
      <template #footer>
        <div class="foot">
          <t-button
            theme="default"
            style="width: 88px; font-weight: 600"
            variant="outline"
            @click="((textData = ''), (batchVisible = false))"
          >
            取消
          </t-button>
          <t-button :disabled="!textData" style="width: 88px; font-weight: 600" @click="saveEdit">
            {{ t('approval.approval_data.cm') }}
          </t-button>
        </div>
      </template>
    </t-dialog>
    <addChannel ref="addChannelRef" @succ="refdataRun" />
    <RouteMap ref="RouteMapRef" :hasfooter="false"></RouteMap>
  </div>
</template>
<script setup lang="ts" name="workbenchManagementChannel">
import { ref, reactive, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import dayjs from 'dayjs';
import { getListPromotionChannels } from '@api/shop.ts';
import {
  applyPublishPromotionProduct,
  promotionUnpublishProduct,
  reApplyPromotionProduct,
} from '@pages/shop/api/productPromotion.ts';
import { MessagePlugin } from 'tdesign-vue-next';
import RouteMap from '@pages/shop/view/workbench/orders/components/deliveryRouteDetails/index.vue';
import { REmpty } from '@rk/unitPark';

import { PromotionViewState } from '@types/api/store/productPromotion.model.ts'; // 引入状态枚举
import addChannel from '@pages/shop/view/workbench/promotionChannels/components/addChannel.vue';
import sdk from '@lynker-desktop/web';
import { useRoute } from 'vue-router';
import { useShopStore } from '../../store';
const route = useRoute();
const closeWin = () => {
  console.log(params, 'paramsparamsparamsparamsparams');
  console.log(drawerForm, 'paramsparamsparamsparamsparamsdrawerFormdrawerForm');

  if (!params.channelType) {
    drawerForm.channelType = '';
  }
  if (!params['startTime']) {
    drawerForm.duration = [];
  }
};
const rest = () => {
  drawerForm.channelType = '';
  drawerForm.duration = [];
};
const addChannelRef = ref(null);
const refdataRun = () => {
  getChannelList();
};
const routeData = ref({});
const errorShow = ref(false);
const textData = ref('');

const inputFn = () => {
  if (textData.value) {
    errorShow.value = false;
  }
};

const statusMap = [
  {
    label: '已售卖',
    value: 'PRODUCT_VIEW_STATE_SALE',
    color: '#499D60',
    backgroundColor: '#E0F2E5',
  },
  {
    label: '已下架',
    value: 'PRODUCT_VIEW_STATE_OFF_SHELF',
    color: '#828DA5',
    backgroundColor: '#ECEFF5',
  },
  {
    label: '部分售罄',
    value: 'PRODUCT_VIEW_STATE_PARTIAL_SOLD_OUT',
    color: '#4D5EFF',
    backgroundColor: '#EAECFF',
  },
  {
    label: '已售罄',
    value: 'PRODUCT_VIEW_STATE_SOLD_OUT',
    color: '#D54941',
    backgroundColor: '#F7D5DB',
  },
  {
    label: '已删除',
    value: 'PRODUCT_VIEW_STATE_DELETED',
    color: '#D54941',
    backgroundColor: '#F7D5DB',
  },
];

const { t } = useI18n();
const total = ref(0);
const activeTab = ref(0);
const RouteMapRef = ref(null);
const batchVisible = ref(false);

const shopStore = useShopStore();
const goGoodsDetail = async () => {
  sdk.workBench_closeTab({
    path_uuid: 'product-admin-detail',
  });
  const teamId = shopStore?.store?.teamId;
  const token = localStorage.getItem('main_token');
  await sdk.workBench_openTabForWebview({
    // path_uuid: 'shop-detail' + routeData.value?.spuId, 商品详情也是只打开一个页签https://www.tapd.cn/tapd_fe/69781318/bug/detail/1169781318001059935
    path_uuid: `product-admin-detail`,
    icon: 'https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/04aae9e/shop.png?x-oss-process=image%2Fresize%2Cs_400%2Fquality%2Cq_90',
    title: routeData?.value.spuName,
    // url: location.origin + '/shop/index.html#/product-detail/' + routeData.value?.spuId,
    url:
      location.origin +
      '/shop/index.html#/product-admin-detail/' +
      routeData.value?.spuId +
      `?teamId=${teamId}&token=${token}`,
  });
};
const openMap = () => {
  addChannelRef.value.openDrwer(routeData.value?.spuId, false);
  // RouteMapRef.value.openWin({
  //   serialNumber: '1930591511866175488',
  //   orderId: 43995,
  // });
};
const titleType = [
  {
    text: '数字CBD',
    img: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/cbd.svg',
    color: '#4D5EFF',
    value: 'cbd',
  },
  {
    text: '数字商协',
    img: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/vip.svg',
    color: '#FC7C14',
    value: 'member',
  },
  {
    text: '数字社群',
    img: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/community.svg',
    color: '#D54941',
    value: 'association',
  },
  {
    text: '数字政企',
    img: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/government.svg',
    color: '#11BDB2',
    value: 'politics',
  },
  {
    text: '数字高校',
    img: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/community.png',
    color: '#21ACFA',
    value: 'uni',
  },
];
const listData = ref([]);
const typeOption = ref([
  { id: 2, title: '数字商协' },
  { id: 4, title: '数字政企' },
  { id: 6, title: '数字CBD' },
  { id: 8, title: '数字社群' },
  { id: 10, title: '数字高校' },
]);
const columns = [
  {
    colKey: 'channelType',
    title: '推广渠道',
    width: 176,
  },
  {
    colKey: 'teamName',
    title: '渠道信息',
  },
  {
    colKey: 'fybl',
    title: 'title-slot-name',
    width: 120,
  },
  {
    colKey: 'sqsj',
    width: 184,
    title: '申请时间',
  },
  {
    colKey: 'zt',
    title: '状态',
    width: 144,
  },
  {
    colKey: 'actions',
    title: '操作',
    width: 200,
  },
];
const params = reactive({
  page: 1,
  'page.size': 10,
  teamName: '',
  channelType: '',
  promotionViewState: '',
  applyAtRange: {
    startTime: '',
    endTime: '',
  },
  spuId: routeData.value?.spuId,
});
const filterVisible = ref(false);
const promotionIdData = ref(null);
const saveEdit = () => {
  if (!textData.value) {
    errorShow.value = true;
    return;
  }
  promotionUnpublishProduct({
    promotionId: promotionIdData.value,
    reason: textData.value,
  })
    .then((res) => {
      if (res.code === 0) {
        MessagePlugin.success('操作成功');
        textData.value = '';
        batchVisible.value = false;
        getChannelList();
      } else {
        MessagePlugin.error(res.message);
      }
    })
    .catch((err) => {
      MessagePlugin.error(err.message);
    });
};

const downFn = (row) => {
  batchVisible.value = true;
  promotionIdData.value = row.promotionId;
};

const reApplyPromotionProductFn = (row) => {
  reApplyPromotionProduct({
    promotionId: row.promotionId,
  })
    .then((res) => {
      console.log(res, '11qqq');

      if (res.code === 0) {
        MessagePlugin.success('操作成功');
        getChannelList();
      } else {
        MessagePlugin.error(res.message);
      }
    })
    .catch((err) => {
      MessagePlugin.error(err.message);
    });
};
const reapplicationFn = (row) => {
  applyPublishPromotionProduct({
    promotionId: row.promotionId,
  })
    .then((res) => {
      console.log(res, '11qqq');

      if (res.code === 0) {
        MessagePlugin.success('操作成功');
        getChannelList();
      } else {
        MessagePlugin.error(res.message);
      }
    })
    .catch((err) => {
      MessagePlugin.error(err.message);
    });
};

const reqParamsHandle = () => {
  params['startTime'] = drawerForm.duration?.[0];
  params['endTime'] = drawerForm.duration?.[1];
  params.teamName = drawerForm.teamName || '';
  params.channelType = drawerForm.channelType || '';
};
const changeActiveTab = (index) => {
  activeTab.value = index;
  // 设置状态值
  params.promotionViewState = statusMappings[index].value;
  console.log(statusMappings, 'paramsparamsparams');

  // 重置页码
  params.page = 1;
  // 调用列表接口
  getChannelList();
};

const clearFilters = () => {
  params.startTime = '';
  params.endTime = '';
  filterVisible.value = false;
  params.channelType = '';
  params.teamName = '';
  params.promotionViewState = '';
  params.spuId = '';
  params.applyAtRange = {
    startTime: '',
    endTime: '',
  };

  drawerForm.channelType = '';
  drawerForm.duration = [];
  drawerForm.teamName = '';
  drawerForm.startTime = '';
  drawerForm.endTime = '';
  getDataRun(true);
};

const clearFilterCategories = (val) => {
  drawerForm[val] = '';
  params[val] = '';
  getDataRun();
};

const paramsSuper = computed(() => params.channelType || params.startTime || params.endTime);
const getDataRun = (flag) => {
  if (flag) {
    params.page = 1;
    params['page.size'] = 10;
  }
  reqParamsHandle();
  getChannelList();
  // getData();
  // adwaitreviewcount(currentTeamId.value, {
  //   platform_type: 1,
  // }).then((res) => {
  //   marketAdNum.value = res.data.data.market_count;
  // });
};
const openWin = () => {
  filterVisible.value = true;
};
const getDataRunDr = () => {
  console.log(drawerForm, 'drawerFormdrawerFormdrawerForm');
  console.log(params, 'drawerFormdrawerFormdrawerFormparamsparamsparams');
  filterVisible.value = false;

  getDataRun(true);
};
const drawerForm = reactive({
  keyword: '',
  channelType: '',
  duration: [],
});

// 状态标签与PromotionViewState的映射关系
const statusMappings = [
  { label: '全部', value: PromotionViewState.UNKNOWN },
  { label: '已售卖', value: PromotionViewState.SALE },
  { label: '待审核', value: PromotionViewState.PENDING_AUDIT },
  { label: '部分售罄', value: PromotionViewState.PARTIAL_SOLD_OUT },
  { label: '已售罄', value: PromotionViewState.SOLD_OUT },
  { label: '已下架', value: PromotionViewState.OFF_SHELF },
  { label: '已拒绝', value: PromotionViewState.AUDIT_UNPASS },
  { label: '已删除', value: PromotionViewState.DELETED },
];
const tagArr = [
  {
    status: 0,
    value: PromotionViewState.UNKNOWN,
    text: '全部',
    color: '#499D60',
    backgroundColor: '#E0F2E5',
  },
  {
    status: 1,
    value: PromotionViewState.SALE,
    text: '已售卖',
    color: '#499D60',
    backgroundColor: '#E0F2E5',
  },
  {
    status: 8,
    text: '待审核',
    color: '#FC7C14 ',
    value: PromotionViewState.PENDING_AUDIT,
    backgroundColor: '#FFE5D1 ',
  },
  {
    status: 2,
    value: PromotionViewState.PARTIAL_SOLD_OUT,
    text: '部分售罄',
    color: '#4D5EFF',
    backgroundColor: '#EAECFF',
  },
  {
    status: 3,
    value: PromotionViewState.SOLD_OUT,
    text: '已售罄',
    color: '#D54941',
    backgroundColor: '#F7D5DB',
  },
  {
    status: 4,
    value: PromotionViewState.OFF_SHELF,
    text: '已下架',
    color: '#516082',
    backgroundColor: '#ECEFF5',
  },
  {
    status: 6,
    text: '已拒绝',
    color: '#D54941',
    value: PromotionViewState.AUDIT_UNPASS,
    backgroundColor: '#F7D5DB',
  },
  {
    status: 5,
    text: '已删除',
    value: PromotionViewState.DELETED,
    color: '#D54941',
    backgroundColor: '#F7D5DB',
  },
];

// 使用枚举作为状态值
// const statusOptions = [
//   { value: PromotionViewState.UNKNOWN, label: '全部' },
//   { value: PromotionViewState.SALE, label: '已售卖' },
//   { value: PromotionViewState.PENDING_AUDIT, label: '待审核' },
//   { value: PromotionViewState.PARTIAL_SOLD_OUT, label: '部分售罄' },
//   { value: PromotionViewState.SOLD_OUT, label: '已售罄' },
//   { value: PromotionViewState.OFF_SHELF, label: '已下架' },
//   { value: PromotionViewState.AUDIT_UNPASS, label: '已拒绝' },
//   { value: PromotionViewState.DELETED, label: '已删除' }
// ];

// 获取列表数据
const getChannelList = async () => {
  try {
    const apiParams = {
      'page.number': params.page,
      'page.size': params['page.size'],
      spuId: routeData.value?.spuId,
    };
    if (params.teamName) apiParams.teamName = params.teamName;
    if (params.channelType) apiParams.channelType = params.channelType;
    if (params.promotionViewState) apiParams.promotionViewState = params.promotionViewState;
    // 添加时间范围
    if (params.applyAtRange.startTime) {
      apiParams['applyAtRange.startTime'] = params.applyAtRange.startTime;
    }
    if (params.applyAtRange.endTime) {
      apiParams['applyAtRange.endTime'] = params.applyAtRange.endTime;
    }

    console.log(apiParams, 'apiParamsapiParamsapiParams');

    const res = await getListPromotionChannels(apiParams);
    if (res && res.data) {
      listData.value = res.data.channels || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取渠道列表失败', error);
  }
};
watch(
  () => route?.query,
  (newQuery) => {
    if (route.path.includes('managementChannel')) {
      routeData.value = newQuery;
      getChannelList();
    }
  },
  { immediate: true },
);
</script>
<style lang="less" scoped>
:global(.batch-edit .t-dialog--default) {
  padding-top: 24px;
  padding-left: 24px;
  padding-right: 24px;
}
:global(.error-border .t-textarea__inner) {
  border-color: #f5222d;
}

.reason-error {
  color: #f5222d;
  font-size: 12px;
  margin-top: 4px;
}

.dow-tip {
  color: #516082;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
  padding: 12px 8px;
}

:global(.batch-box .t-textarea__limit) {
  position: absolute;
  right: 3px;
  bottom: 3px;
}

.batch-box {
  position: relative;
}

:deep(.t-popconfirm__confirm) {
  margin-left: 8px;
}
.f-icon:hover {
  border-color: #707eff !important;
}
.icon-filter {
  color: #516082;
}
.icon-filter:hover {
  color: #707eff;
}
:global(.filter-drawer .t-drawer__body) {
  padding-top: 12px;
  padding-left: 24px;
  padding-right: 24px;
}
:global(.filter-drawer .t-icon-close) {
  color: #828da5;
}
:global(.t-range-input) {
  border-color: #d5dbe4 !important;
}
:global(.filter-drawer .t-drawer__close-btn) {
  right: 24px;
}

:global(.channel-popconfirm-btn .t-popconfirm__buttons) {
  display: flex;
  justify-content: end;
  gap: 8px;
}
:global(.channel-popconfirm-btn .t-popconfirm__confirm) {
  margin-left: 0;
}
.title-icon {
  color: #828da5;
  width: 24px;
  height: 24px;
  -webkit-app-region: no-drag;

  font-size: 24px;
  cursor: pointer;
}
.workbench-management-channel {
  background-image: url('@assets/shop/bg_small.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
  padding: 12px;
  display: flex;
  flex-direction: column;
  .dian {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .af-icon {
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    border: 1px solid #4d5eff;
    background: #eaecff;
    text-align: center;
    display: flex;
    align-items: center;
    margin-left: 8px;
    justify-content: center;
  }

  .active-tab-item {
    border-radius: 4px;
    background: #4d5eff !important;
    color: #fff !important;
    border-color: transparent !important;
  }

  .btn-all {
    display: flex;
    align-items: center;
    gap: 8px;

    .search-item {
      padding: 0 16px;
      height: 32px;
      line-height: 32px;
      border-radius: 4px;
      border: 1px solid #d5dbe4;
      background: #fff;
      color: #516082;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 32px;
      min-width: 80px;
      /* 157.143% */
    }
  }

  .content-box {
    padding: 16px;
    border-radius: 8px;
    margin-top: 12px;
    background: #fff;
    flex: 1;
  }

  .pagination {
    margin-top: 20px;
  }

  .data-box-header {
    padding-bottom: 16px;
    position: sticky;
    top: 0px;
    z-index: 111;
    background: #fff;

    /* padding-top: 16px; */
    .top {
      display: flex;
      justify-content: space-between;

      .approval-time {
        display: flex;
        align-items: center;

        .label {
          width: 76px;
          font-size: 14px;

          font-weight: 400;
          color: var(--kyy_color_tag_text_black, #1a2139);
        }

        .f-icon {
          display: flex;
          width: 32px;
          height: 32px;
          cursor: pointer;
          min-height: 32px;
          max-height: 32px;
          padding: 6px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          margin-left: 8px;
          border-radius: var(--radius-kyy-radius-button-s, 4px);
          border: 1px solid var(--color-button-border-kyy-color-button-border-dedault, #d5dbe4);
          background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
        }
      }
    }

    .tab-box {
      border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
      display: flex;
      /* height: 56px; */
      padding-top: 15px;
      align-items: center;
      justify-content: flex-start;

      .default-tab-item {
        margin-right: 44px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .t-button {
          padding: 0;
          color: var(--kyy_color_tabbar_item_text, #1a2139);
          text-align: center;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
          /* 157.143% */
          border: none !important;
        }

        .tab-item-border {
          width: 16px;
          height: 3px;
          border-radius: 1.5px;
          background: transparent;
        }
      }

      .active-tab-item {
        .t-button {
          font-weight: 600;
          color: var(--brand-kyy-color-brand-default, #4d5eff) !important;
        }

        .tab-item-border {
          width: 16px;
          height: 3px;
          border-radius: 1.5px;
          background: var(--brand-kyy-color-brand-default, #4d5eff);
        }
      }
    }
  }

  .title-box {
    width: fit-content;
    height: 24px;
    border-radius: 4px;
    background: #f5f8fe;
    gap: 2px;
    padding: 0 4px;
    display: flex;
    align-items: center;
  }

  .btn-tiem {
    width: fit-content;
    padding: 4px;
    border-radius: 4px;
    color: #4d5eff;
  }

  .btn-tiem:hover {
    background: #eaecff;
    cursor: pointer;
  }

  .btn-tiem-error:hover {
    background: #f7d5db !important;
  }

  .btn-box {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
  }

  .filter-res {
    display: flex;
    gap: 5px;
    margin-top: 16px;
    flex-wrap: wrap;

    .tit {
      color: var(--text-kyy-color-text-2, #516082);

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }

    .ov-time {
      display: flex;
      min-width: 290px;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0 8px;
      align-items: center;
      gap: 8px;
    }

    .close2 {
      margin-left: 8px;

      img {
        width: 10px;
        height: 10px;
      }
    }

    .clearFilters {
      color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .clearFiltersBox:hover {
      .clearFilters {
        color: var(--kyy_color_tag_text_black, #1a2139);
      }
    }

    .te {
      color: var(--kyy-color-tag-text-black, #1a2139);
      cursor: pointer;
      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      border-radius: 4px;
      background: var(--kyy-color-tag-bg-gray, #eceff5);
      margin-right: 8px;
    }

    .stat {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0 8px;
      align-items: center;
      gap: 8px;
    }

    .kword {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 2px 8px;
      align-items: center;
      gap: 8px;
      max-width: calc(100% - 100px);
    }

    .icon {
      display: flex;
      margin-left: 4px;
      cursor: pointer;

      img {
        width: 14px;
        height: 14px;
        margin-top: 4px;
        margin-right: 4px;
      }
    }
  }

  .header-box {
    padding: 12px;
    border-radius: 8px;
    background: #fff;
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: space-between;
    .lin {
      width: 1px;
      height: 16px;
      background: #eceff5;
    }

    .detail-text {
      color: #4d5eff;
      cursor: pointer;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }

    .tag {
      height: 20px;
      text-align: center;
      font-size: 12px;
      font-style: normal;
      width: fit-content;
      padding: 0 4px;
      font-weight: 400;
      line-height: 20px;
    }
  }

  .header-box-title {
    color: #1a2139;
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    max-width: calc(100vw - 278px);
    line-height: 22px;
    /* 157.143% */
    white-space: nowrap;
    overflow: hidden;
    word-break: break-all;
  }

  .form-boxxx {
    .fitem {
      margin-bottom: 24px;

      .title {
        color: var(--text-kyy-color-text-3, #828da5);

        /* kyy_fontSize_2/regular */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }

      .ctl {
        margin-top: 8px;
      }
    }
  }

  .foot {
    width: 100%;
    display: flex;
    justify-content: end;
  }
}
</style>
