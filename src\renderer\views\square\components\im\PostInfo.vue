<script setup lang="ts">
import { computed } from 'vue';
import { PostShare } from '@renderer/api/square/models/im';
import ImageLayout from '@/views/square/components/im/shared/ImageLayout.vue';
import { PostType, postTypeFieldMap } from '@/views/square/constant';
import VideoLayout from '@/views/square/components/im/shared/VideoLayout.vue';
import KyyAvatar from '@/components/kyy-avatar/index.vue';
import PostAlbumCard from '@/views/square/components/post/album/PostAlbumCard.vue';

// eslint-disable-next-line no-undef
const props = defineProps<{ data: PostShare }>();

const postType = computed(() => (props.data ? props.data.post.postType as PostType : PostType.Text));
const showType = [PostType.PartyBuilding, PostType.Fengcai, PostType.TeamHistory, PostType.TeamHonorRoll, PostType.TeamIntro];
const typeData = computed(() => (props.data ? props.data[postTypeFieldMap[props.data.post.postType]] : {}));
</script>

<template>
  <div v-if="data">
    <div v-if="data.post.text" class="text">{{ data.post.text }}</div>
    <ImageLayout v-if="postType === PostType.Picture" :data="data.post.picture" class="msgAlbum" />
    <VideoLayout v-if="postType === PostType.Video" :src="data.post.video" />

    <div v-if="postType === PostType.Article" class="article-wrap">
      <div class="article-cover">
        <t-image :src="data.article.article.img" fit="cover" />
        <!-- <div class="article-tag"><SvgIcon name="square-link" class="icon w-16 h-16 text-white" /></div> -->
        <div class="article-tag"><i class="i-svg:link text-16 text-white" /></div>
      </div>
      <div class="title">{{ data.article.article.title }}</div>
    </div>

    <PostAlbumCard
      v-if="postType === PostType.AlbumNode"
      readonly
      from-outer
      :album-node="data.albumNode"
      :square="data.square"
      class="album-wrap"
    />

    <div v-if="postType === PostType.Forward" class="forward-wrap">
      <KyyAvatar
        class="img"
        avatar-size="44px"
        :image-url="data.forwardPost.square.avatar"
        :user-name="data.forwardPost.square.name"
      />
      <div>
        <div class="text">{{ data.forwardPost.square.name }}</div>
        <div class="forward-text line-2">{{ data.forwardPost.post?.text || data.forwardPost.article?.title }}</div>
      </div>
    </div>

    <div v-if="showType.includes(postType as PostType)" class="forward-wrap about">
      <div class="img-wrap">
        <KyyAvatar
          class="img"
          avatar-size="44px"
          :image-url="typeData?.fields?.img"
        />
        <div class="article-tag">
          <img v-if="postType === (PostType.PartyBuilding as PostType)" class="w-16 h-16" src="@/assets/square/square_logo_party.svg">
          <img v-if="postType === (PostType.Fengcai as PostType)" class="w-12 h-12" src="@/assets/square/square_logo_fc.svg">
          <img v-if="[PostType.TeamIntro, PostType.TeamHonorRoll, PostType.TeamHistory].includes(postType as PostType)" class="w-10 h-10" src="@/assets/square/square_logo_about.svg">
        </div>
      </div>
      <div class="h-44 flex-col">
        <div v-if="postType === (PostType.TeamHistory as PostType) && typeData?.fields?.time" class="time">{{ typeData?.fields?.time }}</div>
        <div v-if="[PostType.TeamIntro, PostType.TeamHistory].includes(postType as PostType)" class="text1">{{ typeData?.fields?.content || '' }}</div>
        <div v-else class="text2">{{ typeData?.fields?.title || '' }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.text {
  font-size: 14px;
  margin-bottom: 16px;
  color: @kyy_font_1;
  line-height: 22px;
  .multi-ellipsis(2);
}
.text1 {
  font-size: 14px;
  color: @kyy_font_1;
  line-height: 22px;
  .multi-ellipsis(1);
}
.text2 {
  font-size: 14px;
  color: @kyy_font_1;
  line-height: 22px;
  .multi-ellipsis(2);
}

.article-wrap {
  height: 44px;
  display: flex;
  .article-cover {
    position: relative;
    width: 44px;
    height: 44px;
    margin-right: 8px;
    flex-shrink: 0;
    :deep(.t-image__wrapper) {
      width: 44px;
      height: 44px;
      border-radius: 8px;
    }
    .article-tag {
      position: absolute;
      top: 0;
      right: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(21, 21, 21, 0.30);
      border-radius: 0 8px 0 8px;
      z-index: 9;
    }
  }
}

.album-wrap {
  margin-bottom: 0 !important;
  height: 238px;
  &:hover {
    border-color: var(--divider-kyy_color_divider_light, #ECEFF5);
  }
  :deep(.icon-wrap) {
    padding: 3px 6px !important;
  }
}

.title {
  font-size: 17px;
  color: @kyy_font_1;
  line-height: 26px;
  height: 52px;
  .multi-ellipsis(2);
}
:deep(.t-avatar--round) {
  border-radius: 8px !important;
}
.forward-wrap {
  display: flex;
  align-items: center;
  gap: 8px;
  //width: 296px;
  //height: 84px;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  padding: 8px 12px;
  .time {
    color: var(--text-kyy_color_text_1, #1A2139);
    .multi-ellipsis(1);
    /* kyy_fontSize_2/bold */
    font-family: "PingFang SC";
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .img-wrap {
    display: flex;
    position: relative;
    .article-tag {
      position: absolute;
      top: 0;
      right: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(21, 21, 21, 0.30);
      border-radius: 0 8px 0 8px;
      z-index: 9;
    }
  }
  .img {
    width: 44px;
    height: 44px;
    border-radius: 8px;
    flex-shrink: 0;
    object-fit: cover;
  }
  .text {
    margin-bottom: 0;
  }
  .forward-text {
    overflow: hidden;
    color: var(--text-kyy_color_text_3, #828DA5);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.forward-wrap.about{
  padding: 0;
  background: none;
}
</style>
