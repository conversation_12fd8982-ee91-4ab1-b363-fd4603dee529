import { v4 as uuidv4 } from "uuid";
import { getOpenid, getImCardIds } from "@renderer/utils/auth";
import { getUserConfig, putUserConfig } from "@renderer/api/im/api"
import { safeParseJson, replaceUrlDomain } from "@renderer/utils/assist";
import {
  IRelationGroup,
  IRelationPrivate,
  MyAssistants
} from "@renderer/api/im/model/relation";
import to from "await-to-js";
import {getParsedTextMessage, MsgTextType} from "@/views/message/service/msgUtils";
import { logHandler } from "@renderer/log";
import { TIFChangePNG } from './upload'
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, fs, path } = LynkerSDK;

const msgContentTypeMap = {
  101: 'text',
  110: 'text',
  114: 114, // 引用消息
  2101: 2101, // 撤回消息
  111: 111 // 撤回消息
};
const msgStatusMap = { '-1': 10, 2: 30, 1: 10, 3: 20 }; // 10 发送中，20 发送失败，30发送成功
/**
 * 获取正确 main peer，防止会话错误
 * @param card1 身份卡
 * @param card2 身份卡
 * @returns
 */
export function getCardMainPeer(card1: string, card2: string) {
  const cards = getImCardIds();
  return cards.includes(card1) ? {main: card1, peer: card2} : {main: card2, peer: card1};
}


export function getMsgToStore(msg: MessageToSave,isSearch = false) {
  const { extra, ...content } = msg.content;

  if (!extra) {
    console.error("[消息 extra 解析错误]", msg);
  }
  const contentExtra = safeParseJson(extra, {});
  const contentInfo = content;
  if (msg.content.contentType === 114) {
    delete contentExtra.referenceMsg;

  }
  if (msg.conversationType === 1 && msg.sendID && !msg.sendID.includes(contentExtra.senderId)) {
    // ex发送者id和sendID对不上，交换发送者和接收者id
    const receId = contentExtra.senderId;
    contentExtra.senderId = contentExtra.receiverId;
    contentExtra.receiverId = receId;
    console.error('====>ex发送者id和sendID对不上', msg);
    logHandler({ name: 'im-会话错误', info: JSON.stringify(msg), desc: 'ex发送者id和sendID对不上，交换发送者和接收者id' });
  }
  // 通知消息字段兼容处理
  if (contentExtra && 'data' in contentExtra && 'contentType' in contentExtra) {
     // 通知消息数据出现了制表符需要移除掉
    //  let content = contentExtra.data?.replaceAll('\t','')
    contentExtra.data = safeParseJson(contentExtra.data, {});
  }
  if (!contentExtra.contentType) {
    contentExtra.contentType = contentExtra.type || msg.content.contentType;
  }
  if (contentExtra && 'content' in contentExtra) {
    contentExtra.data = safeParseJson(contentExtra.content, {});
  }
  delete msg?.ex;
  const obj: MessageToSave = {
    ...msg,
    id: msg.messageUId, // 兼容以前融云版本id
    tempId: uuidv4(),
    content: contentInfo,
    contentExtra,
    // payload: JSON.stringify(contentInfo),
    // payloadExtra: extraString,
    localSessionId: getMsgLocalSessionId(msg),
  };
  return obj;
}
/**
 * OpenIM消息构造为本地结构
 * @param msg
 */
export const msgToUseChat =(item, isSearch = false, origin?) => {

  // TODO 通知消息 item.notificationElem?.detail
  if (item.contentType === 1501) {
    const detail = item.notificationElem?.detail;
    if (!detail) {
      return false;
    }

  }
    const elemData = item.contentType === 111 ? item.revokedElem?.revokedMessage : item
    if(!elemData) return false;
    const customData = elemData.quoteElem?.ex || elemData.textElem?.ex || elemData.ex || elemData.content || elemData.customElem?.data;
    if (!customData) {
      return false;
    }
   const msgData = safeParseJson(customData);
     // 消息搜索不展示通知
    if (isSearch) {
      if (['server_video_call_finish'].includes(msgData.contentType)) {
        return false;
      }
    }
  // 无效的自定义消息过滤
  if ([110, 101, 114].includes(item.contentType) && (!msgData.extra || msgData.extra === 'null')) {
    return false;
  }
  if (item.contentType === 114) { // 回复消息
    const quoteMsg = item.quoteElem?.quoteMessage
    const ex = quoteMsg?.textElem?.ex || quoteMsg?.ex;
    if (!ex) return false;
    const quoteMsgData = safeParseJson(ex)
    msgData.contentType =  quoteMsg?.contentType
    msgData.referMsg = quoteMsgData
  }
   // 接收openIM消息转换之前融云消息格式
  item.sentStatus = item.status ? msgStatusMap[item.status]:30
  item.content = msgData
  item.conversationType = item.sessionType
  item.messageUId = item.clientMsgID
  item.senderUserId = item.sendID
  item.messageType =  msgContentTypeMap[item.contentType]
  item.targetId = item.sessionType === 1 ? item.recvID : item.groupID
  item.receivedTime = origin === 'newMsg' ? new Date() : null;
  item.sentTime = item.sendTime
  item.receipts = item.attachedInfoElem?.groupHasReadInfo ?  item.attachedInfoElem.groupHasReadInfo : item.receipts


  return item
}
/**
 * msg转换内部文件url域名
 * @param msg
 * @returns
 */
export const fileReplaceUrl = (msg) => {
  const fileType = msg.contentExtra?.contentType

  switch (fileType) {
    case "image":
      let { imgUrl, thumbnail, type } = msg.contentExtra.data;
      imgUrl = replaceUrlDomain(imgUrl, type)
      thumbnail = replaceUrlDomain(thumbnail, type)
      msg.contentExtra.data = { ...msg.contentExtra.data, imgUrl, thumbnail }
      return msg;
    case "voice":
      let { audioUrl } = msg.contentExtra.data;
      audioUrl = replaceUrlDomain(audioUrl)
      msg.contentExtra.data = { ...msg.contentExtra.data, audioUrl }
      return msg;
    case "video":
      let { videoUrl, videoImageUrl } = msg.contentExtra.data;
      videoUrl = replaceUrlDomain(videoUrl, 'MP4')
      videoImageUrl = replaceUrlDomain(videoImageUrl, 'PNG')
      msg.contentExtra.data = { ...msg.contentExtra.data, videoUrl, videoImageUrl }
      return msg;
    case "file":
      let { fileUrl } = msg.contentExtra.data;
      fileUrl = replaceUrlDomain(fileUrl)
      msg.contentExtra.data = { ...msg.contentExtra.data, fileUrl }
      return msg;
    case "PredefinedDayReminder":
      let { image } = msg.contentExtra.data;
      image = replaceUrlDomain(image)
      msg.contentExtra.data = { ...msg.contentExtra.data, image }
      return msg;
    case "APP_ACTIVITY":
      let { cover } = msg.contentExtra.data;
      cover = replaceUrlDomain(cover)
      msg.contentExtra.data.cover = cover
      return msg;
    default:
      return msg;
 }
}
/**
 * 由于身份卡，会话需要按照身份卡进行区分，所以需要计算本地会话id
 * @param msg 收到的消息
 * @returns
 */
export const getMsgLocalSessionId = (msg: MessageToSave) => {
  // 群聊、系统消息、聊天室等会话，直接使用 targetId
  if (msg.conversationType === 1) {
    try {
      const extra = msg?.content?.extra ? JSON.parse(msg?.content?.extra) : msg;
      let { senderId = "", receiverId = "" } = extra;
      if(!senderId || !receiverId ) {
        console.error("getMsgLocalSessionId 参数错误", msg);
        return null;
      }
      if (senderId?.includes('RKIM')) {
        senderId = senderId.split('RKIM')[1]
      }
      if (receiverId?.includes('RKIM')) {
        receiverId = receiverId.split('RKIM')[1]
      }
      return getSessionLocalIdByCards(senderId, receiverId);
    } catch (error) {}
  }
  if (msg.conversationType === 6) {
    const myOpenId = getOpenid()
    return msg.sendID === myOpenId ? msg.recvID : msg.sendID
  }
  return msg.targetId;
};

/**
 * 获取单聊会话本地id
 * @param relation 单聊关系
 * @returns string 单聊会话id
 */
export const getSessionIdByPrivateRelation = (relation: IRelationPrivate) =>
  getSessionLocalIdByCards(relation.main, relation.peer);

export const getSessionLocalIdByCards = (card1: string, card2: string) => {
  if (!card1 || !card2) {
    console.error(new Error("getSessionLocalIdByCards 参数错误").stack);
  }

  if (card1 === card2) {
    const openId = getOpenid();
    console.info(">>>自己给自己发消息", openId, "-->: ", card1, "-->: ", card2);
    return `:${openId}:${openId}`;
  }
  return card1.localeCompare(card2) <= 0
    ? `:${card1}:${card2}`
    : `:${card2}:${card1}`;
};
/**
 * 构造单聊会话 不使用attachment
 * @param relation
 * @returns
 */
export function getSessionByPrivateRelation2(relation: IRelationPrivate): ConversationToSave {
  const localSessionId = getSessionIdByPrivateRelation(relation);
  if(!relation.mainOpenImID || !relation.peerOpenImID){
      logHandler({ name: 'im-构造单聊会话', info: JSON.stringify(relation), desc: '' });
  }
  return {
    conversationID: relation.conversationId,
    conversationType: 1,
    targetId: relation.peer,
    myOpenImId: relation.mainOpenImID || relation.main,
    targetOpenImId: relation.peerOpenImID || relation.peer,
    localSessionId,
    unreadCount: 0,
    createTime: relation.created * 1000,
    updateTime: relation.updated ? relation.updated * 1000 : relation.created * 1000,
    myCardId: relation.main,
    targetCardId: relation.peer,
    latestMessage: null,
    isTop: relation.pin,
    isMute: relation.noDisturb,
    onlyName: null,
    msgId: undefined,
    relation: relation.origin,
    inSession: !relation.hasDel,
    isFollow: false,
    syncMsgAt: relation?.syncMsgAt,
    syncMsgDone: relation?.syncMsgDone,
    sceneRedDot:null
  };
};
/**
 * 构造群聊会话
 * @param groupInfo
 * @returns
 */
export function getSessionByGroupRelation(groupInfo: IRelationGroup): ConversationToSave {
  return {
    conversationID: groupInfo.conversation_id,
    conversationType: 3,
    targetId: groupInfo.group,
    localSessionId: groupInfo.group,
    myOpenImId:groupInfo.myCards?.[0].open_im_id,  //自己openImID
    targetOpenImId:groupInfo.group,
    unreadCount: 0,
    createTime: groupInfo.created * 1000,
    updateTime: groupInfo.updated ? groupInfo.updated * 1000 : groupInfo.created * 1000,
    myCardId: "",
    targetCardId: "",
    latestMessage: null,
    isTop: false,
    isMute: false,
    onlyName: null,
    msgId: undefined,
    inSession: true,
    removeSession: false,
    relation: `${groupInfo.type ?? 0}`,
    isFollow: false,
    syncMsgAt: groupInfo?.sync_msg_at,
    syncMsgDone: groupInfo?.sync_msg_done,
    sceneRedDot:''
  };
}
/**
 * 构造助手会话
 * @param MyAssistants
 * @returns
 */
export function getAssistantsSessions(assistants: MyAssistants): ConversationToSave {
  return {
    avatar: assistants.avatar,
    name: assistants.name,
    conversationID: assistants.conversationId,
    conversationType: 6,
    targetId: assistants.assistantId,
    localSessionId: assistants.assistantId,
    myOpenImId: getOpenid(),  //自己openImID
    targetOpenImId:assistants.assistantId,
    unreadCount: 0,
    createTime: assistants.createdAt * 1000,
    updateTime: assistants.updatedAt ? assistants.updatedAt * 1000 : assistants.createdAt * 1000,
    myCardId: "",
    targetCardId: "",
    latestMessage: null,
    isTop: assistants.stayOn,
    isMute: assistants.noDisturb,
    onlyName: null,
    msgId: undefined,
    inSession: true,
    removeSession: false,
    relation: assistants.assistantType,
    isFollow: false,
    sceneRedDot:''
  };
}

/**
 * 构造单聊会话
 * @param relation
 * @returns
 */
export function getSessionByPairs(relation: IRelationPrivate): ConversationToSave {
  const localSessionId = getSessionLocalIdByCards(relation.peer, relation.main);
  const peer = relation.card
  return {
    conversationID: relation.conversationId,
    conversationType: 1,
    targetId: peer?.cardId || relation.peer,
    myOpenImId: relation.mainOpenImID,
    targetOpenImId: relation.peerOpenImID ,
    localSessionId,
    unreadCount: 0,
    createTime: +relation.created * 1000,
    updateTime: +relation.updated ? +relation.updated * 1000 : +relation.created * 1000,
    myCardId: relation.main,
    targetCardId: relation.peer,
    latestMessage: null,
    isTop: relation?.pin,
    isMute: relation?.noDisturb,
    onlyName: null,
    msgId: undefined,
    relation: relation.origin,
    inSession: !relation.hasDel,
    isFollow: false,
    syncMsgAt: relation.syncMsgAt,
    syncMsgDone: relation.syncMsgDone,
    sceneRedDot:null,
    group_type: relation.groupType === "GT_FREQUENTLY" ? 1 : 0,
  };
};
/**
 * 构造群聊会话
 * @param relation
 * @returns
 */
export function getSessionByGroup(relation: IRelationGroup): ConversationToSave {
  const sessions = []
  relation.members && relation.members.forEach(member => {
    const myCardId = member?.memberid || member?.openid
    const session = {
      conversationID: relation.conversationId,
      conversationType: 3,
      targetId: relation.group,
      localSessionId: getSessionLocalIdByCards(myCardId, relation.group),
      myOpenImId: member?.openImId,  //自己openImID
      targetOpenImId:relation.group,
      unreadCount: 0,
      createTime: relation.created * 1000,
      updateTime: relation.updated ? relation.updated * 1000 : relation.created * 1000,
      myCardId,
      targetCardId: relation.group,
      latestMessage: null,
      isTop: member?.pin,
      isMute: member?.noDisturb,
      onlyName: null,
      msgId: undefined,
      inSession: +member.removed === 0,
      removeSession: false,
      relation: `${relation.type ?? 0}`,
      isFollow: false,
      syncMsgAt: relation?.syncMsgAt,
      syncMsgDone: relation?.syncMsgDone,
      sceneRedDot:'',
      group_type: member.groupType === "GT_FREQUENTLY" ? 1 : 0,
    };
    sessions.push(session)
  });
  return sessions
}
/**
 * 群信息
 * @param relation
 * @returns
 * */
export function getGroupData(group: GroupToSave): GroupToSave {
  const member = group.members?.[0]
  return {
    attachment: {
      avatar: (group as any)?.avatar,
      teamName: member?.card.internalTeamName,
      hasInviteMember: group.hasInviteMember,
      hasLookForHistory: group.hasLookForHistory,
      openCloudFile: group.openCloudFile,
      openPictureFile: group.openPictureFile,
    },
    created: group.created,
    group: group.group,
    owner: group.owner,
    name: group.name,
    path: Array.isArray(group.path) ? group.path.join(',') : group.path,
    total: group.total,
    type: group.type,
    updated: group.updated,
    host: group.host,
    creator: group.creator,
    removed: group.removed,
    scene: group.scene,
    team: group.team,
    creator_card: group.creatorCard,
    sync_disk: group.syncDisk ? 1 : 0,
    disk_folder: group.diskFolder ? 1 : 0,
  };
}


export function getpairSessionMember(
  id: string,
  attachment: ConversationMemberToSave,
  comment: string = '',
  describe: string = ''
): ConversationMemberToSave {
  return {
    sessionId: id,
    openId: attachment.openid,
    cardId: attachment.cardId,
    openImId: attachment.openImUserID || attachment.openId,
    staffName: '',
    nickname: attachment.cardName,
    avatar: attachment.avatar,
    roles: Array.isArray(attachment?.roles) ? attachment?.roles.join(',') : attachment?.roles,
    comment,
    describe,
    teamId: attachment.teamId || attachment.internalTeamId || '',
    teamName: attachment.teamName || attachment.internalTeamName || '',
    jobId: attachment.jobId || '',
    jobName: attachment.jobName || '',
    departmentId: attachment.departmentId || '',
    departmentName: attachment.departmentName || '',
    label_id: null,
    joined: 0,
  };
}
export const getSqliteInsertString = (originStr: string) => {
  if (!originStr) {
    return '';
  }
  return originStr.replace(/'/g, "''");
}

export const getSqliteInsertObject = (obj: object) => {
  return  obj ? JSON.stringify(obj)?.replace(/'/g, "''") : '';
}


export const getSecond = (ms: number) => Math.floor(ms / 1000);



export function makeNewCustomMessage(data: object, type: string, session: Pick<ConversationToSave, "myCardId" | "targetCardId" | "localSessionId" | "targetId" | "conversationType" | "targetOpenImId"|"myOpenImId"|"conversationID" >, user?: any, diff?:number) {
  console.log('makeNewCustomMessage===>', data);
  const tempId = uuidv4();
  const extra: Record<string, any> = {
    senderId: session.myCardId,
    receiverId: session.targetCardId || session.targetId,
    data: data,
    senderFaceUrl:user?.portrait,
    senderNickname:user?.name,
    clientId: tempId,
    source: process.platform,
    contentType: type,
  };

  const content: any = { content: ""}
  if (user) {
    content.user = user;
  }
  const msg: Partial<MessageToSave> = {
    tempId: tempId,
    // payload: JSON.stringify(content),
    // payloadExtra: JSON.stringify(extra),
    content,
    contentExtra: extra,
    localSessionId: session.localSessionId,
    messageType: "text",
    senderUserId: getOpenid(),
    targetId: user?.targetOpenImId || session.targetOpenImId,
    openImId:user?.myOpenImId || session.myOpenImId,
    conversationType: session.conversationType,
    conversationID: session.conversationID,
    sentTime: Date.now() + diff,
    createTime: diff ? Date.now() + diff : data.created * 1000,
    sentStatus: 10,
    messageUId: undefined,
    clientMsgID:undefined,
    receivedTime: undefined,
    replied: undefined,
    receipts: undefined,
    readTime: undefined,
    recallTime: undefined,
    deleteTime: undefined,
    source: process.platform,
    isRead:false, //已读未读
    receiptTime:0 //已读时间 0 未读
  };

  if (type !== 'clear_unread' && msg.senderUserId === msg.targetId) {
    const msgError = new Error(`[自己给自己发消息]`);
    const msgErrorStack = msgError.stack;
    const msgErrorSession = `[自己发给自己消息makeNewCustomMessage] main:[${session.myCardId}] peer:[${session.targetCardId}] target:[${msg.senderUserId}];msg:${JSON.stringify(msg)}`
    logHandler({ name: 'im-会话错误', info: msgErrorSession, desc: msgErrorStack});
  }
  return msg as MessageToSave;
}

export function makeNewReferMessage(msg: MessageToSave, referMsg: MessageToSave) {
  const referBody = {
    // 引用的消息的发送者
    referMsgUserId: referMsg.senderUserId,
    // 引用的消息唯一id
    referMsgUid: referMsg.messageUId,
    objName: "text",
    contentType: referMsg.messageType,
    referMsg: {
      content: '',
      ...referMsg.content,
      extra: JSON.stringify(referMsg.contentExtra)
    },
    content: "",
  } as any as IReferenceMessageBody;
  Object.assign(msg.content, referBody);
  const { extra, ...content } = msg.content;
  // msg.payload = JSON.stringify(content);
  msg.content.extra = extra;
  msg.messageType = 114;

  return msg;
}

/**
 * 将数组转换为map结构，方便查询
 * @param members 所有成员
 * @returns
 */
export const getMappedMembers = (members: ConversationMemberToSave[]) => {
  const membersMap = new Map<string, Map<string, ConversationMemberToSave>>();
  for (const item of members) {
    const sessionMembers = membersMap.get(item.sessionId);
    if (sessionMembers) {
      sessionMembers.set(item.cardId, item);
    } else {
      membersMap.set(
        item.sessionId,
        new Map([[item.cardId || item.openId, item]])
      );
    }
  }
  return membersMap;
};

export function getImageMessageUrl(url: string): Promise<{ width: number, height: number, size: number }> {
  return new Promise((resolvue) => {
    let image = new Image();

    image.src = url;
    image.crossOrigin = "*";
    image.onload = function (res) {
      console.log('发送截图', url, res);
      resolvue({ width: image.width, height: image.height, size: image.width * image.height});
    };
  });
}


export async function getImageMessageFromFile(file: File): Promise<{width: number, height: number, thumbnail: string }> {
  const thumbnailCanvas = document.createElement('canvas');
  const thumbnailCtx = thumbnailCanvas.getContext('2d');
  const Tiff = require("tiff.js");
  if (file.type === 'image/tiff') {
    const newUrl = await TIFChangePNG(file);
    return {thumbnail: newUrl.imgUrl, width: 160, height: 0}
  }
// 新增GIF格式处理
  if (file.type === 'image/gif') {
    return new Promise((resolve) => {
      const img = new Image();
      const url = URL.createObjectURL(file);
      img.onload = () => {
        // 生成首帧缩略图
        const maxWidth = 280;
        const scale = maxWidth / img.width;
        thumbnailCanvas.width = maxWidth;
        thumbnailCanvas.height = img.height * scale;
        thumbnailCtx.drawImage(img, 0, 0, maxWidth, img.height * scale);
        
        resolve({
          thumbnail: thumbnailCanvas.toDataURL('image/jpeg'),
          width: img.width,
          height: img.height
        });
        URL.revokeObjectURL(url);
      };
      img.src = url;
    });
  }
  return new Promise((resolvue) => {
    const reader = new FileReader();
    reader.onload = function (e) {
      const image = new Image();
      image.src = e.target?.result as string;
      image.onload = function () {
        const aspectRatio = image.width / image.height;
        const maxWidth = 280;
        const maxHeight = Math.floor(maxWidth / aspectRatio);
        thumbnailCanvas.width = maxWidth;
        thumbnailCanvas.height = maxHeight;
        thumbnailCtx.drawImage(image, 0, 0, maxWidth, maxHeight);
        const thumbnail = thumbnailCanvas.toDataURL('image/jpeg')
        resolvue({thumbnail, width: image.width, height: image.height});
      }
      image.onerror = function (e) {
        // TODO: 已知 heif 格式的图片不支持，转换或者提示用户
      }
    }
    reader.readAsDataURL(file);
  });
}

export const getFileType = (name: string) => {
  let type = name?.split('.');
  return type?.length > 1 ?  type.pop() : '';
}

export const saveMergedMessageTemp = async (data: any): Promise<{ filePath: string, name: string, size: number, type: string}> => {
  const tempDir = await ipcRenderer.invoke('get-temp-dir')

  return new Promise((resolve, reject) => {
    const name = `${uuidv4()}.json`;

    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const savePath = path.join(tempDir, name);
    const saveText = JSON.stringify(data);
    fs.writeFile(savePath, saveText, { encoding: 'UTF-8' }, (err: any) => {
      err ? reject(err) : resolve({
        name,
        type: 'application/json',
        size: saveText.length,
        filePath: savePath,
      });
    })
  })
}

// 会话是否相同
export function isConversationSame(conversation1: ConversationToSave, conversation2: ConversationToSave) {
  if (conversation1.conversationType === 1) {
    return conversation1.localSessionId === conversation2.localSessionId;

  } else if(conversation1.conversationType === 3) {
    return conversation1.targetId === conversation2.targetId;
  }
  return false;
}

// 消息是否属于会话
export function isConversationMessage(message: MessageToSave, converstaion?: ConversationToSave) {
  if (!converstaion) {
    return false;

  } else if (converstaion.conversationType === 1) {
    return converstaion.localSessionId === message.localSessionId;

  } else if(converstaion.conversationType === 3) {
    return converstaion.targetId === message.targetId;
  }
  return false;
}

const convertExtraData = (extra: MessageToSave['contentExtra']) => {
  if (extra.contentType === 'file') {
    const fileName = extra.data.fileName || '';
    return { id: new Date().getTime(), content: fileName, data: {...extra.data, name: fileName } };

  } else if (extra.contentType === 'video') {
    const fileName = extra.data.videoName || extra.data.videoUrl.split('/').pop() || '';
    return { id: new Date().getTime(), content: fileName, data: {...extra.data, name: fileName } };

  } else {
    return { id: new Date().getTime(), content: '', data: extra.data };
  }
}

// 极速模式保存常用
export async function speedModeSaveCommon(data: {type: string, openid: string, msg: any}) {
  let hasSame = false;
  const [err, res] = await to(getUserConfig(data.type));
  if (err) return false;
  let attachment = res.data.attachment;
  let tempAttachmentData = {};
  if (data.type === 'APP_ACCOUNT_WORDS') {
    // 系统默认表情是文本类型 过滤@(?和默认表情) 只留纯文本
    if (data.msg.contentExtra?.contentType === 'richText') {
      try {
        let text = data.msg.contentExtra.data.text;
        text = JSON.stringify(JSON.parse(text).filter(item => !item?.insert?.mention));
        if(text.length > 200) {
          return 'toLong';
        }
        tempAttachmentData = {
          id: new Date().getTime(),
          content: text,
          type: 'richText',
          data: {
            text: text,
            atInfo: null
          }
        }
      } catch (error) {

      }
    } else {
      const splits = getParsedTextMessage(data.msg);
      const textStr = splits?.filter(it => ![MsgTextType.At].includes(it.type))?.map(it => it.str)?.join('');
      console.log(textStr)
      if (!textStr) return false;
      if(textStr.length > 200) {
          return 'toLong';
      }
      tempAttachmentData = {
        id: new Date().getTime(),
        content: textStr,
        data: {
          text: textStr,
          atInfo: null
        }
      }
    }
  } else if (data.type === 'APP_ACCOUNT_EMO' || data.type === 'APP_ACCOUNT_PICTURE') {
    tempAttachmentData = {
      id: new Date().getTime(),
      content: data.msg.contentExtra?.data?.imgUrl,
      data: {
        name: data.msg.contentExtra?.data?.imgUrl,
        ...data.msg.contentExtra?.data
      }
    }
  } else if (data.type === 'APP_ACCOUNT_ADDRESS') {
    tempAttachmentData = {
      id: new Date().getTime(),
      content: data.msg.contentExtra?.data?.title,
      data: data.msg.contentExtra?.data
    }
  } else if (data.type === 'APP_ACCOUNT_FILE') {
    tempAttachmentData = convertExtraData(data.msg.contentExtra);
  }
  attachment?.data?.forEach(item => {
    if (item.type === 'defaultEmojiGroup' || item.type === 'defaultGroup') {
      if (item?.data?.some(ite => {
        if (data.type === 'APP_ACCOUNT_ADDRESS') {
          return JSON.stringify(ite.data) === JSON.stringify(tempAttachmentData.data)
        } else {
          return ite.content === tempAttachmentData.content
        }
      })) {
        hasSame = true;
        return
      } else {
        if (Array.isArray(item?.data)) {
          item.data?.unshift(tempAttachmentData)
        } else {
          item.data = [tempAttachmentData]
        }
      }
    }
  })
  if (hasSame) return data.type;
  const setRes = await putUserConfig({
    user_id: data.openid,
    typ: data.type,
    attachment
  })
  return setRes?.status === 200
}

// openIM发送状态转换为融云的数据状态
// 10 发送中，20 发送失败，30发送成功
export const SentStatusMap:Record<string | number, SentStatus> = {
  1: 10,
  2: 30,
  3: 20,
};
