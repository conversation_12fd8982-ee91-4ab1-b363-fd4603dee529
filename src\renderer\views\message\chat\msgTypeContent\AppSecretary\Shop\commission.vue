<template>
  <!-- 分佣金关系绑定申请 -->
  <AppCard>
    <AppCardHeader> {{ getTitle(sceneData.scene,sceneData.content.title) }}</AppCardHeader>
    <AppCardBody>
      <div class="content-box">
        {{ sceneData.content.title }}
      </div>
    </AppCardBody>
    <template v-if="[15201,15203].includes(sceneData.scene)">
      <div class="h-1 bg-[#ECEFF5] mb-16 ml-16 mr-16" />
      <AppCardFooter>
        <Button
          class="w-full"
          variant="outline"
          theme="danger"
          @click.stop.prevent="handleClick('reject')"
        >
          {{ i18nt("im.msg.refuse") }}zzh3
        </Button>
        <Button
          class="w-full agree"
          variant="outline"
          theme="primary"
          @click.stop.prevent="handleClick('agree')"
        >
          {{ i18nt("im.msg.agree") }}zzh3
        </Button>
      </AppCardFooter>
    </template>
  </AppCard>
</template>

<script setup lang="ts">
import { PropType, computed, nextTick, onMounted, ref, watch } from 'vue';
import { Button } from 'tdesign-vue-next';
import { digitalAgree, digitalContactStatus, digitalReject } from '@renderer/api/common';
import { MessageToSave } from 'customTypes/message';
import { i18nt } from '@/i18n';
import { getTitle } from './constant';
import { AppCard, AppCardHeader, AppCardBody, AppCardFooter } from '../../../MessageAppCard';
import { useMessageStore } from '@/views/message/service/store';

const msgStore = useMessageStore();

const props = defineProps({
  msg: { type: Object as PropType<MessageToSave>, default: null },
});

const sceneData = computed(() => props.msg?.contentExtra?.data);

const status = ref(sceneData.value?.extend?.status || null);

const handleClick = async (opt) => {
  const data = {
    uuid: sceneData.value?.extend?.uuid,
  };
  const fn = opt === 'agree' ? () => digitalAgree(data) : () => digitalReject(data);

  const { data: { code } } = await fn();
  if (code === 0) {
    getStatus();
  }
};
const getStatus = async () => {
  const data = {
    uuid_ids: [sceneData.value?.extend?.uuid],
  };
  const { data: { data: res, code } } = await digitalContactStatus(data);
  if (code === 0 && res?.status_list?.length) {
    status.value = res.status_list[0].status;
  }
};

watch(() => msgStore.needUpdateRefreshMessageUId, async (val) => {
  if (val && val === props.msg?.messageUId && sceneData.value?.extend?.uuid) {
    await getStatus();
    nextTick(() => {
      useMessageStore().refreshMessageByNeedUpdateMessageUId({ messageUId: '', scene: sceneData.value.scene });
    });
  }
});

onMounted(() => {
  getStatus();
});
</script>

<style scoped lang="less">
.agree{
  background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #EAECFF);
  color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4D5EFF);
}
</style>
