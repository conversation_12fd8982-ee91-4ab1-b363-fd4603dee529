import { defineStore } from 'pinia';
import to from 'await-to-js';
import { PostRequestData } from '@renderer/api/square/models/post';
import { nextTick } from 'vue';
import { getIndividualSquare, getSquareInfo, getSquaresList, getSquareStats } from '@/api/square/home';
import { SquareType } from '@/api/square/enums';
import { getUnreadStats } from '@/api/square/news';
import { getIpInfo, sharedReportSquareVisitorEvent } from '@/api/square/common';
import { Square, SquareData, SquareInList, teamAnnualFeeResponse } from '@/api/square/models/square';
import { unReadPost, updateSidebarCount } from '@/views/square/utils/ipcHelper';
import { getCurrencyByAreaCode } from '@/views/square/utils/currency';

type LastLoginInfo = Record<string, {
  squareId: string,
  type: SquareType,
}>

type SquareSelected = {
  notices: number,
  isAdmin: boolean,
  isSuperAdmin: boolean,
  square: Square
}

export const useSquareStore = defineStore('square', {
  state: () => ({
    // 保存上次登录的信息，以 openid 为键
    lastLoginInfo: {} as LastLoginInfo,
    // 需要跳转到上次登录账号
    needRedirectLastLogin: false,
    // 当前用户广场号信息
    squareInfo: null as SquareData | null,
    // 已选中的广场（广场额外信息）
    squareSelected: {} as SquareSelected,
    squareList: [],
    noticesTotalCount: 0,
    // 发送失败的动态（以 squareId 为键区分多账号登录）
    postStashList: {},
    // 消息通知数量
    newsStats: {
      comments: 0,
      likes: 0,
      follows: 0,
      total: 0,
      timelinePosts: 0,
    },
    // 好友圈新动态数
    unreadPostCount: 0,
    // 官网搭建的首页id
    indexPageId: '',
    // ip 地址信息
    ipInfo: '',
    // ip 地址请求时间戳，用于控制 ipInfo 过期时间
    ipTimestamp: '',
    // 年费到期时间
    annualFeeExpireDate: '',
    // 广场统计
    squareStats: {},
    // 动态发布内容缓存
    cachePublishContent: null,
    // 存在动态发布失败
    hasPostFailed: false,
    // 搜索关键字
    searchKeyword: '',
    cachePageList: ['friend-detail', 'setting'],

    // 当前激活的左侧菜单索引
    activeMenuIdx: 0,
    menuList: [],
    // 发布文章的状态管理
    publishFormData: [] as Array<PostRequestData & { id: number }>,
    publishIsDraft: false,
    ringkolHelpIsOver: {},
    showPostHideInNearbyDialog: false,
    // 是否手动关闭 tab 标识, removeTab 时触发，目前只在 publish-article > onBeforeRouteLeave 中使用
    isRemoveTag: false,

    // 当前年费套餐
    annualFeeInfo: {} as teamAnnualFeeResponse,
    // 当前年费套餐是否是体验套餐
    isTrial: false,
    // 年费套餐是否已过期
    isExpired: false,

    // 跳广场主页时显示的链接广场弹窗（广场id）
    showOrgConnect: '',
  }),

  getters: {
    squareId() {
      return this.squareInfo?.square?.squareId;
    },
    isPersonal() {
      if (!this.squareInfo) return true;
      const { squareType } = this.squareInfo?.square || {};
      return squareType === SquareType.Individual;
    },
    homePage() {
      return '/square/friend-circle';
      // if (this.isPersonal) return '/square/friend-circle';
      // return '/square/publish-records';
    },
    // 个人广场号信息
    individualSquareInfo(): SquareInList | null {
      if (!this.squareList?.length) return null;
      return this.squareList.find((v: SquareInList) => v.square.squareType === SquareType.Individual);
    },
    squareType() {
      return this.squareInfo?.square?.squareType;
    },
    // 组织广场是否到期
    orgSquareIsExpired() {
      if (this.isPersonal) return true;
      const expiredAt = this.squareInfo?.organizationProfile?.expiredAt;
      if (!expiredAt) return true;
      return new Date(expiredAt).getTime() < new Date().getTime();
    },
    getPostStashList() {
      return this.postStashList[this.squareId] || [];
    },
    teamId() {
      return this.squareInfo?.organizationProfile?.teamId;
    },
    currency() {
      return getCurrencyByAreaCode(this.squareInfo?.square?.regionCode);
    },
    activeMenu() {
      return this.menuList[this.activeMenuIdx];
    },
  },

  actions: {
    // 广场号列表（切换账号用）
    async getSquaresList(updateCount = true, onlyUpdateCount = false) {
      const [err, res] = await to(getSquaresList());
      if (err) return [];

      this.squareList = res.data.squares;
      this.noticesTotalCount = res.data.notices;
      if (!this.squareInfo) this.squareInfo = {};
      if (!onlyUpdateCount) {
        // 广场号下拉列表中名称更新后，更新当前登录的广场名称
        const selected: SquareInList = this.squareList.find((v: SquareInList) => v.square.squareId === this.squareInfo?.square?.squareId);
        if (selected) {
          this.squareInfo.square.name = selected.square.name;
        } else {
          // 切换账号登录，需重置之前的广场信息，以便拉取新的广场号信息
          this.squareInfo.square = { ...this.squareList[0]?.square };
        }
      }

      if (updateCount) {
        await updateSidebarCount(this.noticesTotalCount);

        // 更新当前广场红点
        const selected: SquareInList = this.squareList.find((v: SquareInList) => v.square.squareId === this.squareId);
        if (selected) {
          this.unreadPostCount = selected.unreadStats.timelinePosts;
        }
        // FIXME 当数值为 0 时，更新未读消息数会导致侧边栏消息数闪烁。临时解决
        // this.unreadPostCount && unReadPost(this.unreadPostCount);
      }
    },

    setSquareSelected(item) {
      this.squareSelected = item;
    },
    setPublishIsDraft(isDraft: boolean) {
      this.publishIsDraft = isDraft;
    },

    // 获取个人广场号（未开通时自动开通）
    async getIndividualInfo() {
      const data = { ip_region: '' };

      const [err, res] = await to(getIndividualSquare(data));
      if (err) return err;
      this.squareInfo = res.data.info;

      const { squareId, originId } = res.data.info.square;
      await this.setLastLoginInfo({
        squareId,
        type: SquareType.Individual,
        teamId: originId,
      });
    },

    // 记录最后一次登录的账号（用于下次登录时登录）
    setLastLoginInfo(data) {
      return new Promise((resolve, reject) => {
        const openid = localStorage.getItem('openid');
        if (!openid) {
          console.error('OpenID is missing in localStorage');
          reject(new Error('OpenID is required'));
          return;
        }

        // 更新 lastLoginInfo
        this.$patch((state) => {
          // eslint-disable-next-line no-param-reassign
          state.lastLoginInfo[openid] = data;
        });

        // HACK 上面的代码有时不能及时更新到 localStorage 中（偶现），导致组织广场未正确获取请求头，故手动设值
        nextTick(() => {
          localStorage.setItem('square', JSON.stringify(this.$state));
          setTimeout(() => {
            resolve(true);
          }, 16);
        });
      });
    },

    // 获取广场号信息 FIXME 切换为组组织广场号时请求了两次
    async getSquareInfo(id?) {
      if (!this.squareId && !id) return;
      const [err, res] = await to(getSquareInfo({ square_id: id || this.squareId }));
      if (err) return err;

      // for test
      // if (res.data.info?.organizationProfile) {
      //   res.data.info.organizationProfile.expiredAt = '2023-05-12 12:12';
      // }

      this.squareInfo = res.data.info;
    },

    isSelfSquare(squareId) {
      return this.squareInfo?.square?.squareId === squareId;
    },

    addPostStashList(data) {
      if (!this.postStashList[this.squareId]) this.postStashList[this.squareId] = [];
      this.postStashList[this.squareId].push(data);
    },

    clearPostStashList() {
      this.postStashList[this.squareId] = [];
    },

    logout() {
      this.tabs = [];
      // FIXME 多账号登录后会导致本地存储跟store里的广场信息不一致
      // this.squareInfo = null;
    },

    /**
     * 最新消息数量统计(当前广场号)
     * @param isForceInd {boolean} 忽略当前组织广场号，强行查询个人广场信息
     * @returns
     */
    async fetchNewsStats(isForceInd = false) {
      const [err, res] = await to(getUnreadStats({ force_ind_stats: isForceInd }));
      if (err) return;

      if (this.isPersonal) {
        this.newsStats = res.data.newsStats;
        this.unreadPostCount = res.data.unreadPostCount;

        if (this.unreadPostCount === 0) {
          unReadPost();
        }
      } else if (isForceInd) {
        // 组织下查询个人广场 unreadPostCount
        this.unreadPostCount = res.data.unreadPostCount;
      } else {
        this.newsStats = res.data.newsStats;
      }
    },

    // 获取统计数据
    async getSquareStats() {
      if (!this.squareId) return;
      const [err, res] = await to(getSquareStats({ square_id: this.squareId }));
      if (err) return;
      this.squareStats = res.data.stats;
    },

    // 获取 ip 地址信息newsStats
    async getIp(refresh = false) {
      // 缓存一天，未过期直接返回
      if (this.ipInfo && this.ipTimestamp && !refresh) {
        const expirationTime = 24 * 60 * 60 * 1000;
        if (new Date().getTime() - this.ipTimestamp < expirationTime) return this.ipInfo;
      }

      const [err, res] = await to(getIpInfo());
      if (err) return;

      this.ipTimestamp = new Date().getTime();
      this.ipInfo = res.data;
      return this.ipInfo;
    },

    // 保存文章的发布记录
    savePublishFormData(publishFormData: PostRequestData & { id: number }) {
      if (!Array.isArray(this.publishFormData)) {
        this.publishFormData = [];
      }
      const index = this.publishFormData.findIndex((item) => item.id === publishFormData.id);
      if (index < 0) {
        this.publishFormData.push(publishFormData);
        return;
      }
      this.publishFormData[index] = publishFormData;
    },
    // 记录访问广场主页的人数
    initVisiteNumber(squareId) {
      console.log('visit', squareId);
      sharedReportSquareVisitorEvent(squareId);
    },
  },
  persist: {
    key: 'square',
    storage: localStorage,
  },
});
