<template>
  <div class="h-full relative flex flex-col">
    <div :class="`activity-manage-content flex-1 p-12 ${isAllowEdit ? 'editable-content' : 'h-full'} scroll-content`">
      <div class="rounded-8 h-full bg-[#fff] p-16 overflow-auto">
        <slot name="scene" />

        <activity-lite-info v-if="editing" ref="liteInfoRef" :published="true" />

        <template v-else>
          <div class="text-[#1A2139] text-16 leading-24 font-600">{{ t('activity.activity.basic') }}</div>

          <div class="mt-16 flex flex-col gap-16">
            <div class="flex">
              <label class="w-112 text-right text-[#828DA5]">活动归属：</label>
              <div class="text-[#1A2139] max-w-564 break-all">{{ teamName }}</div>
            </div>
            <div class="flex">
              <label class="w-112 text-right text-[#828DA5]">活动主题：</label>
              <div class="text-[#1A2139] max-w-564 break-all">{{ activityDetailData.basic?.subject }}</div>
            </div>
            <div class="flex">
              <label class="w-112 text-right text-[#828DA5]">活动类型：</label>
              <div class="text-[#1A2139] max-w-564 break-all">
                {{ activityCategoryMap[activityDetailData.basic?.categoryId] }}
              </div>
            </div>
            <div class="flex">
              <label class="w-112 text-right text-[#828DA5]">主题图片：</label>
              <img
                v-if="activityDetailData.basic"
                class="w-128 h-96 rounded-8 object-cover"
                :src="activityDetailData.basic?.assetUrl"
                alt=""
              >
            </div>
            <div class="flex">
              <label class="w-112 text-right text-[#828DA5]">活动时间：</label>
              <div v-if="activityDetailData.basic" class="text-[#1A2139]">
                {{ moment.unix(activityDetailData.basic?.duration.startTime).format('YYYY-MM-DD HH:mm') }} ~
                {{ moment.unix(activityDetailData.basic?.duration.endTime).format('YYYY-MM-DD HH:mm') }}
              </div>
            </div>
            <div class="flex">
              <label class="w-112 text-right text-[#828DA5]">活动地点：</label>
              <div class="text-[#1A2139] max-w-564 break-all">{{ activityDetailData.basic?.location.title }}</div>
            </div>
            <div class="flex">
              <label class="w-112 text-right text-[#828DA5]">活动详情：</label>
              <div
                v-if="!_.isEmpty(activityDetailData.particulars?.content)"
                class="text-[#4D5EFF] cursor-pointer flex items-center"
                @click="activityContentDialogRef.open(activityDetailData.particulars?.content)"
              >
                <span>查看详情</span>
                <img src="@renderer/assets/activity/icon_arrow_right_blue.svg" alt="">
              </div>

              <div v-else class="text-[#1A2139]">未添加</div>
            </div>
          </div>

          <div class="mt-24 text-[#1A2139] text-16 leading-24 font-600">活动信息</div>

          <div class="mt-16 flex flex-col gap-16">
            <div class="flex">
              <label class="w-112 text-right text-[#828DA5]">参与人员范围：</label>
              <div class="text-[#1A2139]">
                {{ actorScopeText }}
              </div>
            </div>
            <div v-if="activityDetailData.basic?.actorScope === 'Internal'" class="flex">
              <label class="w-112 text-right text-[#828DA5]">参与人员：</label>
              <template v-if="activityDetailData.members?.actors?.length">
                <div class="text-[#1A2139]">{{ activityDetailData.members?.actors?.length }}人</div>
                <div
                  class="ml-8 text-[#4D5EFF] cursor-pointer flex items-center"
                  @click="activityActorsDialogRef.open(activityDetailData.members?.actors)"
                >
                  <span>人员详情</span>
                  <img src="@renderer/assets/activity/icon_arrow_right_blue.svg" alt="">
                </div>
              </template>

              <div v-else class="text-[#1A2139]">未添加</div>
            </div>

            <div v-if="isShowPlatforms" class="flex">
              <label class="w-112 text-right text-[#828DA5]">发布到数字平台：</label>
              <div class="text-[#1A2139] max-w-564 break-all">
                {{ activityDetailData.advanced?.platforms.length ? activityDetailData.advanced?.platforms.map((platform) => platform.name).join('、') : '未选择' }}
              </div>
            </div>

            <div v-if="activityDetailData.advanced?.registFeeEnable" class="flex">
              <label class="w-112 text-right text-[#828DA5]">报名费金额：</label>
              <div class="text-[#1A2139]">
                {{ activityDetailData.advanced.registFee.value.toFixed(2) }}元
              </div>
            </div>

            <div class="flex">
              <label class="w-112 text-right text-[#828DA5]">附件：</label>
              <ul v-if="activityDetailData.particulars?.files?.files?.length" class="gap-8 flex flex-wrap w-578">
                <li
                  v-for="file in activityDetailData.particulars?.files?.files"
                  :key="file.url"
                  class="upload-file-card"
                >
                  <img class="w-40 h-40" :src="getFileIcon(file.title ?? 'sss')" alt="">
                  <div class="ml-8 flex-1 truncate">
                    <div class="text-[#1A2139] truncate" :title="file.title">{{ file.title }}</div>
                    <div class="text-[##ACB3C0] text-12 leading-20">{{ getReadableFileSize(file.size) }}</div>
                  </div>
                  <div class="upload-file-card-actions">
                    <span class="text-[#4D5EFF] cursor-pointer" @click="preview(file)">{{
                      t('activity.activity.preview')
                    }}</span>
                  </div>
                </li>
              </ul>

              <div v-else class="text-[#1A2139]">未上传</div>
            </div>
            <div class="flex">
              <label class="w-112 text-right text-[#828DA5]">活动提醒：</label>
              <div class="text-[#1A2139]">{{ activityDetailData.advanced?.reminders.length ? reminderText : '未设置' }}</div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <div v-if="isAllowEdit" class="operate-box">
      <template v-if="editing">
        <t-button theme="default" @click="cancelEdit">取消</t-button>
        <t-button
          theme="primary"
          :loading="saveLoading"
          :disabled="saveLoading"
          @click="save"
        >
          保存
        </t-button>
      </template>
      <t-button v-else theme="primary" @click="edit">修改</t-button>
    </div>

    <activity-content-dialog ref="activityContentDialogRef" />
    <activity-actors-dialog ref="activityActorsDialogRef" />
  </div>
</template>

<script lang="ts">
// 显式声明组件的name（用于keep-alive）
export default {
  name: 'ManageActivityLiteInfo',
};
</script>

<script setup lang="ts">
import { inject, provide, ref, reactive, watchEffect, computed, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import _ from 'lodash';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import moment from 'moment/moment';
import LynkerSDK from '@renderer/_jssdk';
import { activityActorBatch, activityUpdate } from '@/api/activity';
import { cardIdType } from '@/views/identitycard/data';
import { useActivityStore } from '@/views/activity/store';
import ActivityLiteInfo from '@/views/activity/create/components/ActivityLiteInfo.vue';
import { activityCategoryMap, remindersOption } from '@/views/activity/utils';
import ActivityContentDialog from '@/views/activity/manage/components/ActivityContentDialog.vue';
import { getFileIcon } from '@/views/zhixing/note/util';
import { getReadableFileSize } from '@/views/message/service/msgUtils';
import { imgType } from '@/views/zhixing/constant';
import ActivityActorsDialog from '@/views/activity/manage/components/ActivityActorsDialog.vue';
import { usePlatforms } from '@/views/activity/hooks/usePlatforms';

const { ipcRenderer } = LynkerSDK;

const emit = defineEmits(['update']);

const { t } = useI18n();

const route = useRoute();

const activityStore = useActivityStore();

const liteInfoRef = ref(null);

const activityContentDialogRef = ref(null);
const activityActorsDialogRef = ref(null);

const activityDetailData = inject('activityDetailData');

const isAllowEdit = inject('isAllowEdit');

const componentEditingState = inject('componentEditingState');

// 修改模式下当前选中的活动归属组织
const selectedTeam = computed(
  () => activityStore.groupList.find((group) => group.teamId === activityFormData.basic?.teamId) || {},
);
// 修改模式下当前活动归属是否是个人身份
const isPersonal = computed(() => !selectedTeam.value.uuid || cardIdType(selectedTeam.value.uuid) === 'personal');

// 活动归属显示文案
const teamName = computed(() => {
  const team = activityStore.groupList.find((group) => group.teamId === activityFormData.basic?.teamId) || {};
  if (cardIdType(team.uuid) === 'personal') {
    return team.name;
  }
  return team.teamFullName;
});

// 参与人员范围显示文案
const actorScopeText = computed(() => {
  if (activityDetailData.value.basic?.actorScope === 'Publish') {
    return '公开报名';
  }
  return isPersonal.value ? '我的好友' : '组织内部';
});

// 活动提醒显示文案
const reminderText = computed(() => {
  const reminders = activityDetailData.value.advanced?.reminders || [];

  return reminders.map((reminder) => remindersOption.find((option) => option.value === reminder).label).join('、');
});

const saveLoading = ref(false);

// 活动当前表单数据（修改模式下使用）
const activityFormData = reactive({
  members: {
    // 活动参与人
    actors: [],
    // 活动嘉宾
    guests: [],
    // 工作人员
    staffs: [],
  },
});

// 注入活动表单数据
provide('activityFormData', activityFormData);
provide('selectedTeam', selectedTeam);
provide('isPersonal', isPersonal);

// 提交的表单数据
const submitFormData = computed(() => ({
  ...activityFormData,
  basic: {
    ...activityFormData.basic,
    teamId: isPersonal.value ? '' : activityFormData.basic.teamId,
  },
  advanced: {
    ...activityFormData.advanced,
    // 报名费，开启报名费则将金额转为字符串，否则整个对象传空
    registFee: activityFormData.advanced.registFeeEnable ? {
      ...activityFormData.advanced.registFee,
      value: activityFormData.advanced.registFee.value.toString(),
    } : null,
  },
}));

// 编辑中状态
const editing = ref(false);

const { personalPlatforms, getPersonalPlatforms } = usePlatforms();

// 是否展示数字平台选择
const isShowPlatforms = computed(() => activityDetailData.advanced?.platforms.length || (cardIdType(activityDetailData.value.basic.cardId) === 'personal' && personalPlatforms.value.length > 0));

const initActivityFormData = () => {
  Object.assign(
    activityFormData,
    _.cloneDeep({
      id: activityDetailData.value.id,
      basic: activityDetailData.value.basic,
      particulars: activityDetailData.value.particulars,
      members: activityDetailData.value.members,
      process: activityDetailData.value.process,
      advanced: activityDetailData.value.advanced,
      chatId: activityDetailData.value.chatId,
    }),
  );

  if (isPersonal.value) {
    // 个人身份时，查询一次个人已加入的数字平台，用于判断是否展示数字平台选择
    getPersonalPlatforms();
  }
};

const edit = () => {
  const confirmDia = DialogPlugin.confirm({
    header: t('activity.activity.tip'),
    theme: 'info',
    body: '活动已发布，确定修改吗？',
    confirmBtn: '确定',
    cancelBtn: '取消',
    onConfirm: async () => {
      editing.value = true;

      await nextTick();
      liteInfoRef.value.editorRenderContent();
      confirmDia.destroy();
    },
    onCancel: () => {
      confirmDia.destroy();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
    },
  });
};

const cancelEdit = () => {
  editing.value = false;
  // 重新给活动表单数据赋值
  initActivityFormData();
};

const save = async () => {
  const result = await liteInfoRef.value.validate();
  if (result !== true) return;

  const executeSave = async () => {
    saveLoading.value = true;

    // 非公开活动调用批量设置参与人接口
    if (activityFormData.basic.actorScope === 'Internal') {
      const [actorError] = await to(activityActorBatch({
        activityId: activityFormData.id,
        actors: activityFormData.members.actors,
      }));

      if (actorError) {
        saveLoading.value = false;
        return;
      }
    }

    const [error] = await to(activityUpdate(submitFormData.value));

    if (error) {
      saveLoading.value = false;
      return;
    }

    MessagePlugin.success('保存成功');
    emit('update');
    saveLoading.value = false;
    editing.value = false;

    // 保存后页签名字使用最新的活动名称
    const tabList = activityStore.tabList;
    tabList.find((tab) => tab.path === route.path).title = activityFormData.basic.subject;
    activityStore.setTabList(tabList);
  };

  await startTimeConfirm();
  await executeSave();
};

// 活动开始时间小于当前系统时间的确认
const startTimeConfirm = () => new Promise((resolve, reject) => {
  const currentTime = moment().unix();
  if (activityFormData.basic.duration.startTime > currentTime) {
    resolve();
    return;
  }
  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: '活动开始时间小于当前系统时间，确定发布？',
    theme: 'info',
    confirmBtn: '确定',
    cancelBtn: '选择时间',
    onConfirm: async () => {
      confirmDia.destroy();
      resolve();
    },
    onCancel: () => {
      // 跳转基本信息
      confirmDia.destroy();
      reject();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
      reject();
    },
  });
});

// 文件预览
const preview = (file) => {
  if (file.title.toLowerCase().endsWith('.mp4')) {
    ipcRenderer.invoke(
      'view-video',
      JSON.stringify({
        title: file.title,
        url: file.url,
        type: file.type,
        size: file.size,
      }),
    );
  }

  if (imgType.some((type) => file.title.toLowerCase().endsWith(type))) {
    // 图片预览
    ipcRenderer.invoke(
      'preview-file',
      JSON.stringify({
        url: file.url,
        type: 'png',
      }),
    );
  } else {
    ipcRenderer.invoke(
      'preview-file',
      JSON.stringify({
        title: file.title,
        url: file.url,
        type: file.metaData.type,
        size: file.size / 1024,
        officeId: file.metaData.file_id,
      }),
    );
  }
};

watchEffect(() => {
  initActivityFormData();
  componentEditingState.liteInfo = editing.value;
});
</script>

<style scoped lang="less">
.editable-content {
  height: calc(100% - 64px);
}

.upload-file-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #d5dbe4;
  width: 285px;

  .upload-file-card-actions {
    margin-left: 12px;
    display: none;
    gap: 8px;
  }

  &:hover {
    background: #f5f8fe;
    border-color: transparent;

    .upload-file-card-actions {
      display: flex;
    }
  }
}

.scroll-content {
  overflow: overlay;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.operate-box {
  background: #fff;
  padding: 16px 0;
  display: flex;
  justify-content: center;
  gap: 8px;
  box-shadow: 10px -3px 8px 0px rgba(0, 0, 0, 0.08);

  .t-button {
    width: 88px;

    &.publish-button {
      min-width: 88px;
      width: auto;
    }
  }
}
</style>
