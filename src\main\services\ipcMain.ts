import { ipcMain, dialog, BrowserWindow, app, screen, shell, BrowserView, Menu, session, webFrame, FileFilter, webContents } from 'electron';
import qs from 'qs';
// import { UpdateStatus } from 'electron_updater_node_core';
import path from 'path';
import fs from 'fs-extra';
import os from 'os';
import { getSDK, enable, getRandomUUID, getCustomSession } from '@lynker-desktop/electron-sdk/main';
import { showDevTool } from '@main/env';
import { dbDestory } from '@main/message/dbManager';
import { SquareWindow } from '@main/services/window/SquareWindow';
import EventEmitter from 'events';
import { GlobalStatus, ipcMainHandle } from '@main/utils';
import { IsUseSysTitle } from '../config/const';
import Server from '../server';
import { winURL, staticPaths, getRendererWindowEntry } from '../config/StaticPath';
import DownloadFile from './downloadFile';
import { previewWindowConfig, otherWindowConfig, leftWinMaxWidth, leftWinMainWidth, footHeoght } from '../config/windowsConfig';
import { usePrintHandle } from './printHandle';
import { setupMessageModule } from '../message';
import { setupZhixingModule } from '../zhixing';
import { restViewWin, initViewWin, viewImgWin, viewVideoWin, viewFileWin, closePreviwew } from './previewMethod';
import { beginCapture, endCapture } from './screenshoots';
import { windowManager, setUnread, isValidWindow } from './windowManager';
import { getFolderStructure } from './uploadFolder';
import { initStore, electronStore } from './store';
import {
  setSearch,
  setScreenShot,
  setShowHide,
  getShortCutStore,
  getIdentityCardFocus,
  getIdentityEditInfoFocus,
} from './shortcut';
import { setPopBv, setPopBvOther } from './windowBv';
// const fss = require("fs");

import {
  initWindow,
  createIframeWindow,
  createSettingWindow,
  createPreviewWindow,
  createDialogWindow,
  closeDialogWindow,
  createAloneWindow,
  closeAloneWindow,
  createRemindDialogWindow,
  closeRemindDialogWindow,
  removeRemindDialogWindow,
  refreshMainWindow,
  createMergedMessageWindow,
  createMonitorWindow
} from './windowMain';
import { getDownloadDirIpc, showInFolderIpc, openFileIpc } from './file';
import { mediaAccessHandle } from './requestMediaAccess';
import { applePayInAppPurchase } from './applePay';
import { handleBvRenderProcessGone } from './window/utils';
import { getSystemStats } from './systemStats';
import { aW } from '@fullcalendar/core/internal-common';

const Downloader = require('nodejs-file-downloader');
const loadingWinBW = {
  value: null,
};
const loadingBV = {
  value: null,
}
const ClouddiskWinBV = {
  value: null,
};
let newWinBv = null;
const StandAloneClouddiskWinBW = {
  value: null,
};
const ApprovalWinBV = {
  value: null,
};

const StandAloneApprovalWinBW = {
  value: null,
};
const DeviceWinBV = {
  value: null,
};
const StandAloneDeviceWinBW = {
  value: null,
};
const CustomerWinBV = {
  value: null,
};
const StandAloneCustomerWinBW = {
  value: null,
};
const SupplierWinBV = {
  value: null,
};
const StandAloneSupplierWinBW = {
  value: null,
};
const PartnerWinBV = {
  value: null,
};
const StandAlonePartnerWinBW = {
  value: null,
};
const ProjectWinBV = {
  value: null,
};
const StandAloneProjectWinBW = {
  value: null,
};
const StandAloneSquareWinBW = {
  value: null,
};
const CustomerServiceBV = {
  value: null,
};

const BusinessWinBV = {
  value: null,
};
const StandAloneBusinessWinBW = {
  value: null,
};

const ServiceWinBV = {
  value: null,
};
const StandAloneServiceWinBW = {
  value: null,
};

const StandAloneActivityWinBW = {
  value: null,
};
const ActivityWinBV = {
  value: null,
};
let myOrderWin = null;
let activeCodeWin = null;
const vcardWin = {
  value: null,
};
let myInvoice = null;
let myHelp = null;
let mergedMessageWin = null;
let nicheReadWin = null;
let nicheExamineWin = null;
let liveShowWin = null;

const SquareWinBV = {
  value: null,
};

const MemberWinBV = {
  value: null,
};
const StandAloneMemberWinBW = {
  value: null,
};

const PoliticsWinBV = {
  value: null,
};
const StandAlonePoliticsWinBW = {
  value: null,
};

const DigitalPlatformWinBV = {
  value: null,
};
const StandAloneDigitalPlatformWinBW = {
  value: null,
};

const ZhixingWinBV = {
  value: null,
};
const BenchWinBV = {
  value: null,
};
const bigMarketWinBV = {
  value: null,
};
const StandAloneZhixingWinBW = {
  value: null,
};
const StandAloneWorkBenchWinBW = {
  value: null,
};
const StandAlonebigMarketWinBW = {
  value: null,
};

const StandAloneCustomerServiceWinBW = {
  value: null,
};

const StandAloneH5BW = {
  value: null,
  bv: null,
};

let setSizeFlag = false;
const cardArg = {
  x: 0,
  y: 0,
};
let cardTimer: any = null;

const eventBus = new EventEmitter();

const saveFile = async (name, url) => {
  const Store = require('electron-store');
  const { join } = require('path');
  const savePath = new Store().get('im.save_path');
  const filePath = getFilePath(join(savePath, name)) ?? '未知文件';
  const pathInfo = path.parse(filePath);
  console.log('下载', filePath, pathInfo);
  const downloader = new Downloader({
    url,
    directory: pathInfo.dir,
    fileName: pathInfo.base,
    cloneFiles: false,
    maxAttempts: 9,
    onProgress(percentage: number, chunk: any, remainingSize: number) {
      console.log(percentage, remainingSize);
    },
    onError: (error) => {
      console.error('==================');
      console.error('==================');
      console.error('下载失败: ', url);
      console.error('pathInfo: ', pathInfo);
      console.error('error: ', error);
      console.error('==================');
      console.error('==================');
    },
  });
  try {
    await downloader.download();
    return filePath;
  } catch (error) {
    console.log(error);
  }
};

const saveAsFile = async (name, url, fileType?) => {
  const Store = require('electron-store');
  const useStore = new Store({ accessPropertiesByDotNotation: true });
  const { join } = require('path');
  const savePath = useStore.get('im.save_path');
  let filters = [{ name: '所有文件', extensions: ['*'] }];
  if (process.platform !== 'darwin') {
    const type = fileType || url.split('.').pop();
    filters = [{ name: type.toLocaleUpperCase(), extensions: [type] }];
  }
  const { filePath, ...data } = await dialog.showSaveDialog({
    title: '请选择保存路径', // 对话框标题
    defaultPath: getFilePath(join(savePath, name)),
    buttonLabel: '保存', // 按钮文本
    filters,
  });
  if (filePath) {
    const pathInfo = path.parse(filePath);
    console.log('下载', filePath, pathInfo);
    const downloader = new Downloader({
      url,
      directory: pathInfo.dir,
      fileName: pathInfo.base,
      cloneFiles: false,
      onProgress(percentage: number, chunk: any, remainingSize: number) {
        console.log(percentage, remainingSize);
      },
      onError: (error) => {
        console.error('==================');
        console.error('==================');
        console.error('==================');
        console.error('下载失败: ', url);
        console.error('pathInfo: ', pathInfo);
        console.error('error: ', error);
        console.error('==================');
        console.error('==================');
        console.error('==================');
      },
    });
    try {
      await downloader.download();
      return filePath;
    } catch (error) {
      console.log(error);
    }
  } else if (data.canceled) {
    // 取消文件下载
    return false;
  }
};

// name 包含下载路径
const getFilePath = (name, flag?) => {
  let saveName = name;
  let exist = fs.existsSync(name);
  if (flag) {
    if (exist) {
      const index = name.lastIndexOf('.');
      const tempName = `${name.substring(0, index)}${name.substring(index)}`;
      exist = fs.existsSync(tempName);
      saveName = tempName;
    }
  } else {
    let suffix = 1;
    if (exist) {
      const index = name.lastIndexOf('.');
      const tempName = `${name.substring(0, index)}(${suffix})${name.substring(index)}`;
      if (fs.existsSync(tempName)) {
        suffix++;
      } else {
        exist = false;
        saveName = tempName;
      }
    }
  }
  return saveName;
};

const onToggleLeftMenu = async (event, arg) => {
  // 校验应用权限 调试先屏蔽
  const Store = require('electron-store');
  const useStore = new Store();
  const routers = useStore.get('accountAuthRouters');
  // 点击左侧
  const windowsMap = {
    // 定义windowsMap，根据url返回相应的BrowserWindow对象
    '/loading': {
      bw: loadingWinBW,
      bv: loadingBV,
    },
    '/clouddiskIndex/clouddiskhome': {
      bw: StandAloneClouddiskWinBW,
      bv: ClouddiskWinBV,
      bvName: 'clouddisk',
    },
    '/approvalIndex/approve_home': {
      bw: StandAloneApprovalWinBW,
      bv: ApprovalWinBV,
      bvName: 'approval',
    },
    '/deviceIndex/device-list': {
      bw: StandAloneDeviceWinBW,
      bv: DeviceWinBV,
      bvName: 'device',
    },
    '/customerIndex/customer-list': {
      bw: StandAloneCustomerWinBW,
      bv: CustomerWinBV,
      bvName: 'customer',
    },
    '/supplierIndex/supplier-list': {
      bw: StandAloneSupplierWinBW,
      bv: SupplierWinBV,
      bvName: 'supplier',
    },
    '/partnerIndex/partner-list': {
      bw: StandAlonePartnerWinBW,
      bv: PartnerWinBV,
      bvName: 'partner',
    },
    '/businessIndex/businessReleaseList': {
      bw: StandAloneBusinessWinBW,
      bv: BusinessWinBV,
      bvName: 'business',
    },
    '/square/friend-circle': {
      bw: StandAloneSquareWinBW,
      bv: SquareWinBV,
      bvName: 'square',
    },
    '/engineerIndex/engineer_home': {
      bw: StandAloneProjectWinBW,
      bv: ProjectWinBV,
      bvName: 'project',
    },
    '/memberIndex/member_number': {
      bw: StandAloneMemberWinBW,
      bv: MemberWinBV,
      bvName: 'member',
    },
    '/politicsIndex/politics_number': {
      bw: StandAlonePoliticsWinBW,
      bv: PoliticsWinBV,
      bvName: 'politics',
    },
    '/digitalPlatformIndex/digital_platform_home': {
      bw: StandAloneDigitalPlatformWinBW,
      bv: DigitalPlatformWinBV,
      bvName: 'digitalPlatform',
    },
    '/serviceIndex/service_home': {
      bw: StandAloneServiceWinBW,
      bv: ServiceWinBV,
      bvName: 'service',
    },
    '/zhixing': {
      bw: StandAloneZhixingWinBW,
      bv: ZhixingWinBV,
      bvName: 'zhixing',
    },
    '/workBenchIndex/workBenchHome': {
      bw: StandAloneWorkBenchWinBW,
      bv: BenchWinBV,
      bvName: 'workBench',
    },
    '/bigMarketIndex/home': {
      bw: StandAlonebigMarketWinBW,
      bv: bigMarketWinBV,
      bvName: 'bigMarket',
    },

    '/customerServiceIndex/customerServiceList': {
      bw: StandAloneCustomerServiceWinBW,
      bv: CustomerServiceBV,
      bvName: 'customerService',
    },
    '/activity/activityList': {
      bw: StandAloneActivityWinBW,
      bv: ActivityWinBV,
      bvName: 'activity',
    },
  };
  for (const url in windowsMap) {
    if (url !== arg.url) {
      const { bv } = windowsMap[url];
      if (bv && bv.value) {
        bv.value.setAutoResize({ width: false, height: false });
      }
    }
  }
  if (!routers?.some((item) => item?.path === arg.url)) {
    // 工作台暂时注释
    setPopBv({
      show: true,
      type: 'accountAuthTip',
      data: {
        isAppDisabled: false,
        selected_path_uuid: arg.selected_path_uuid,
        click_path_uuid: arg.click_path_uuid,
      },
    });
    return;
  }
  if (arg.url.startsWith('/main/')) {
    // 晚一点
    setTimeout(() => windowManager.hideBvs(), 150);
    return true;
  }
  const { bw, bv, bvName } = windowsMap[arg.url]; // 获取相应的BrowserWindow对象

  return clickMenuItem(arg, bw, bv, bvName); // 调用clickMenuItem函数
};

const clickMenuItem = async (arg, winBw, winBv, bvName) => {
  // 判断云盘独立窗口是否存在,存在则展示
  console.log('打印clcik', arg, winBw, winBv);

  const win = windowManager.mainWindow; // 获取主窗口
  if (!win) return;

  if (winBw.value && !arg.cloudDiskJumpId && !arg.redirect && !arg.workBenchJumpTeamId && !arg?.toAdmin) {
    winBw.value.show();
    return false;
  }

  let url;
  const query = arg.query ? new URLSearchParams(arg.query).toString() : '';
  if (arg.url === '/clouddiskIndex/clouddiskhome' && arg.cloudDiskJumpId) {
    url = arg.cloudDiskFileId
      ? `${arg.url}?cloudDiskJumpId=${arg.cloudDiskJumpId}&cloudDiskFileId=${arg.cloudDiskFileId}&${query}`
      : `${arg.url}?cloudDiskJumpId=${arg.cloudDiskJumpId}&${query}`;
    if (arg.cloudDiskMB) url += '&cloudDiskMB=true';
  } else if (arg.url === '/clouddiskIndex/clouddiskhome' && arg.teamId) {
    url = `${arg.url}?teamId=${arg.teamId}&cloudDiskFileId=${arg.cloudDiskFileId}&${query}`;
    if (arg.cloudDiskMB) url += '&cloudDiskMB=true';
  } else if (arg.url === '/approvalIndex/approve_home' && arg.isIm) {
    url = `${arg.url}?dataid=${arg.dataid}&id=${arg.id}&departmentId=${arg.departmentId}&staffId=${arg.staffId}&isIm=${arg.isIm}&${query}`;
  } else {
    url = `${arg.url}?${query}`;
  }


  // 独立窗口存在则直接显示
  if (winBw.value) {
    url = `${winURL}#${url}`;
    // app.isPackaged ? winBv.value.webContents.loadFile(url) : winBv.value.webContents.loadURL(url);
    console.log(winBv.value.webContents, 'webContentswebContents');
    console.log(winBv.value, 'winBv.valuewinBv.valuewebContentswebContents');

    winBv.value.webContents.loadURL(url).then((res) => {
      console.log(res, 'ressss');

    }).catch((err) => {
      console.log(err, 'err');

    });
    winBw.value.show();
    return false;
  }

  // 设置尺寸
  const bX = setSizeFlag ? leftWinMaxWidth : leftWinMainWidth;
  const bWidth = win.getBounds().width - bX;
  const bHeight = win.getBounds().height - footHeoght;
  const bounds = {
    minWidth: 1232,
    x: bX,
    y: footHeoght,
    width: bWidth,
    height: bHeight,
  };

  const outerRedirectLockMap = new Map();

  const outerRedirectNotify = () => {
    if (arg.query?.from === 'outer') {
      // 防止多次触发
      const key = arg.query.redirect || 'default';
      if (outerRedirectLockMap.get(key)) return;
      outerRedirectLockMap.set(key, true);

      console.log('outer-redirect', arg);
      try {
        winBv.value.webContents.send('outer-redirect', arg.query);
      } catch (error) {
        console.error('outer-redirect error: ', error);
      }

      setTimeout(() => {
        outerRedirectLockMap.delete(key);
      }, 1500);
    }
  };
  if (winBv.value) {
    // 统一使用 setBv 来处理切换，不管是什么情况
    await windowManager.setBv(winBv.value, {
      bounds,
      showCrruentBv: true,
      url: `${winURL}#${url}`,
      jumpPath: arg?.jumpPath,
      reload: !!arg?.reload || !!arg?.query?.reload,
    }, undefined, bvName);

    // 特殊情况的额外处理
    if (arg.workBenchJumpTeamId) {
      try {
        winBv.value.webContents.send('workBenchJumpTeamId', arg.workBenchJumpTeamId);
      } catch (error) {
        console.error('workBenchJumpTeamId error: ', error);
      }
    } else if (arg.url === '/square/friend-circle') {
      outerRedirectNotify();
    }

    return true;
  }
  const options = {
    url: `${winURL}#${url}`,
    bounds,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      // 可开启iframe nodeIntegration
      // sandbox: false,
      // nodeIntegrationInSubFrames: true,
    },
    // reload: true,
    showDevtool: showDevTool,
  };

  // win.once('ready-to-show', () => {
    // 等待初始化完成再展示数据
    win.show(); // 显示新窗口
  // });

  // newWinBv = await windowManager.setBv(winBv.value, options);
  let newWinBv = await getSDK().windowManager.create({
    type: 'BV',
    name: bvName,
    url: options.url,
    browserWindow: options,
  });
  winBv.value = newWinBv;
  await windowManager.setBv(winBv.value, {
    bounds,
    showCrruentBv: true,
    url: `${winURL}#${url}`,
    jumpPath: arg?.jumpPath,
    reload: !!arg?.reload || !!arg?.query?.reload,
  }, undefined, bvName);
  // await attachViewWithRoute(win, { name: bvName, url: options.url, viewOptions: options},  (view) => {
  //   newWinBv = view;
  //   getSDK().windowManager.rename(view.webContents.id, bvName);
  //   winBv.value = view;
  // }, 2)
  console.log('newWinBv====', newWinBv);
  // 将新的BrowserView赋给对象属性

    // 判断大市场轮播显示
  if (bigMarketWinBV?.value) {
    const isHomeUrl = arg.url === '/bigMarketIndex/home';
    try {
      bigMarketWinBV.value.webContents.send('edit-ad-swiper', isHomeUrl);
    } catch (error) {
      console.error('bigMarketWinBV edit-ad-swiper error: ', error);
    }
  }
  if (DigitalPlatformWinBV?.value) {
    const isHomeUrl = arg.url === '/digitalPlatformIndex/digital_platform_home';
    try {
      DigitalPlatformWinBV.value.webContents.send('edit-ad-swiper', isHomeUrl);
    } catch (error) {
      console.error('DigitalPlatformWinBV edit-ad-swiper error: ', error);
    }
  }

  // 广场加载完成再执行跳转
  eventBus.once('square-loaded', () => {
    console.log('square-loaded');
    outerRedirectNotify();
  });

  winBv.value.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url);
    return { action: 'deny' };
  });

  if (showDevTool) {
    // newWinBv.webContents.openDevTools();
  }
  return true;
};
const publicIndependentWin = async (arg, standAloneBw, currentBv) => {
  console.log(currentBv, '打印currentBv');

  if (standAloneBw.value) {
    // 独立窗口存在则直接显示
    return standAloneBw.value.show();
  }
  const newStandAloneBw = await getSDK().windowManager.create({
    name: `newStandAloneBw-${getRandomUUID()}`,
    url: getRendererWindowEntry('windows/internal/index.html'),
    browserWindow: {
      // 独立窗口
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
      },
      x: 100,
      y: 150,
      width: 1296,
      minWidth: 1296,
      height: 720,
      titleBarStyle: 'hidden',
      backgroundColor: '#00000000',
      frame: false,
      show: false,
    },
  });
  // newStandAloneBw.loadURL(`${winURL}#/windows/StandAlon`).then(() => {
  // });
  // newStandAloneBw.loadURL().then(() => {
  //   newStandAloneBw.setBackgroundColor('#00000000');
  // });

  currentBv.value.setAutoResize({ width: true, height: true }); // 撑满
  newStandAloneBw.setBrowserView(currentBv.value); // 将BrowserView装进容器
  currentBv.value.setBounds({
    x: 0,
    y: footHeoght,
    width: 1296,
    minWidth: 1296,
    height: 720 - footHeoght,
  });
  // newStandAloneBw.once('ready-to-show', () => {
    // newStandAloneBw.webContents.openDevTools();
    newStandAloneBw.show();
  // });
  newStandAloneBw.on('closed', () => {
    // 清除引用
    currentBv.value = null;
    standAloneBw.value = null;
  });

  standAloneBw.value = newStandAloneBw; // 将新的BrowserWindow赋给对象属性
};

const getIpAddress = () => {
  const networkInterfaces = os.networkInterfaces();
  let ipAddress;

  // 遍历网络接口
  Object.values(networkInterfaces).forEach((interfaces) => {
    interfaces.forEach((iface) => {
      // 过滤出 IPv4 地址，并排除 loopback 地址和 Docker 接口
      if (iface.family === 'IPv4' && !iface.internal && !/(loopback|docker)/i.test(iface.name)) {
        ipAddress = iface.address;
      }
    });
  });

  return ipAddress;
};

/**
 * 个人信息创建窗口
 * @param preferences - webPreferences对象下的属性/非必填
 * @param browserWindow - BrowserWindow对象下的属性/非必填
 */
export const createPersonalInfoWindow = async (preferences?: any, browserWindow?: any) => {
  const win = await getSDK().windowManager.create({
    name: `personalInfoWindow-${getRandomUUID()}`,
    url: '',
    browserWindow: {
      titleBarStyle: IsUseSysTitle ? 'default' : 'hidden',
      ...otherWindowConfig(),
      // 添加圆角配置
      roundedCorners: true, // 启用窗口圆角
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        ...preferences,
      },
      width: 1296,
      minWidth: 1296,
      height: 720,
      ...browserWindow,
    },
  });
  return win;
};

/**
 * 客服和帮助创建窗口
 * @param args - 参数-随意
 * @param type - 是否初始化 init
 */
export const createHelpAndServiceWindow = async (args: any, type?: string) => {
  console.log('myHelp args', qs.stringify(args), type);
  if (myHelp && type !== 'init') {
    myHelp.reload();
    // myHelp.loadURL(`${winURL}#/myHelp?${qs.stringify(args)}`);
    myHelp.show();
  } else {
    myHelp = await createPersonalInfoWindow();
    myHelp.on('close', (e: any) => {
      e.preventDefault();
      myHelp.hide();
    });
    myHelp.loadURL(`${winURL}#/myHelp?${qs.stringify(args)}`);

    // 开发模式下自动开启devtools
    // !type && myHelp.show();
    /**
     * 关闭时判断窗口是否存在, 如果不存在就强制关闭窗口
     */
    // myHelp.on("closed", () => {
    // myHelp?.isDestroyed() === false && myHelp?.destroy();
    // myHelp = null;
    // });
    // 帮助中心控制台
    // if (showDevTool) {
    //   myHelp.webContents.openDevTools({
    //     mode: "undocked",
    //     activate: true,
    //   });
    // }
    // myHelp.webContents.openDevTools({
    //   mode: "undocked",
    //   activate: true,
    // });
  }

  setTimeout(() => {
    try {
      myHelp.webContents.send('argsHelp', args);
      console.log('webcontent lss');
    } catch (error) {
      console.error('myHelp argsHelp error: ', error);
    }
  }, 2000);
};

/**
 * 发票中心创建窗口
 */
export const createInvoiceCenterWindow = async (type?: string) => {
  if (myInvoice && type !== 'init') {
    myInvoice.reload();
    myInvoice.show();
  } else {
    myInvoice = await createPersonalInfoWindow();
    myInvoice.loadURL(`${winURL}#/myInvoice`);
    !type && myInvoice.show();
    // 监听关闭窗口, 如果触发关闭窗口就隐藏该窗口
    myInvoice.on('close', (e: any) => {
      e.preventDefault();
      myInvoice.hide();
    });
    // myInvoice.on("closed", () => {
    //   myInvoice?.isDestroyed() === false && myInvoice?.destroy();
    //   myInvoice = null;
    // 开发模式下自动开启devtools
    if (showDevTool) {
      // myInvoice.webContents.openDevTools({
      //   mode: "undocked",
      //   activate: true,
      // });
    }
  }
};

nicheExamineWin;
/**
 * 商机查看
 * @param vall
 * @param type
 */
export const createnicheExamineWindow = async(vall: any) => {
  const objs = JSON.parse(vall);
  if (nicheExamineWin) {
    console.log(objs, 'valk');
    nicheExamineWin.show();
    try {
      nicheExamineWin.webContents.send('update-niche-examine', objs);
    } catch (error) {
      console.error('nicheExamineWin update-niche-examine error: ', error);
    }
  } else {
    nicheExamineWin = await createPersonalInfoWindow(
      {
        // devTools: showDevTool,
        devTools: false,
        scrollBounce: process.platform === 'darwin',
      },
      {
        frame: IsUseSysTitle,
        useContentSize: true,
        autoHideMenuBar: true,
        transparent: false,
      },
    );
    console.log('nicheExamineWin', nicheExamineWin);
    nicheExamineWin.loadURL(`${winURL}#/nicheExamineWin?id=${objs.id}&teamId=${objs.teamId}&from=${objs.from}`);
    // 开发模式下自动开启devtools
    nicheExamineWin.show();
    nicheExamineWin.on('close', (e: any) => {
      e.preventDefault();
      nicheExamineWin.hide();
    });
    if (showDevTool) {
      // nicheExamineWin.webContents.openDevTools({
      //   mode: "undocked",
      //   activate: true,
      // });
    }
  }
};

/**
 * 直播查看
 * @param vall
 * @param type
 */
export const createLiveShowWindow = async (vall: any) => {
  const objs = JSON.parse(vall);
  if (liveShowWin) {
    console.log(objs, 'valk');
    liveShowWin.show();
    try {
      liveShowWin.webContents.send('update-niche-uuid', objs);
    } catch (error) {
      console.error('liveShowWin update-niche-uuid error: ', error);
    }
  } else {
    liveShowWin = await createPersonalInfoWindow(
      {
        // devTools: showDevTool,
        devTools: false,
        scrollBounce: process.platform === 'darwin',
      },
      {
        frame: IsUseSysTitle,
        useContentSize: true,
        autoHideMenuBar: true,
        transparent: false,
      },
    );
    liveShowWin.loadURL(objs);
    // 开发模式下自动开启devtools
    liveShowWin.show();
    liveShowWin.on('close', (e: any) => {
      e.preventDefault();
      liveShowWin.hide();
    });
    if (showDevTool) {
      // liveShowWin.webContents.openDevTools({
      //   mode: "undocked",
      //   activate: true,
      // });
    }
  }
};

/**
 * 商机查看
 * @param vall
 * @param type
 */
export const createNicheReadWindow = async (vall: any) => {
  const objs = JSON.parse(vall);
  if (nicheReadWin) {
    console.log(objs, 'valk');
    nicheReadWin.show();
    try {
      nicheReadWin.webContents.send('update-niche-uuid', objs);
    } catch (error) {
      console.error('nicheReadWin update-niche-uuid error: ', error);
    }
  } else {
    nicheReadWin = await createPersonalInfoWindow(
      {
        // devTools: showDevTool,
        devTools: false,
        scrollBounce: process.platform === 'darwin',
      },
      {
        frame: IsUseSysTitle,
        useContentSize: true,
        autoHideMenuBar: true,
        transparent: false,
      },
    );
    nicheReadWin.loadURL(`${winURL}#/nicheReadOnly?uuid=${objs.uuid}&teamId=${objs.teamId}&from=${objs.from}`);
    // 开发模式下自动开启devtools
    nicheReadWin.show();
    nicheReadWin.on('close', (e: any) => {
      e.preventDefault();
      nicheReadWin.hide();
    });
    if (showDevTool) {
      // nicheReadWin.webContents.openDevTools({
      //   mode: "undocked",
      //   activate: true,
      // });
    }
  }
};

/**
 * 我的订单创建窗口
 */
export const createMyOrderWindow = async (vall: any, type?: string) => {
  const objs = JSON.parse(vall);
  if (myOrderWin && type !== 'init') {
    console.log(objs, 'valk');
    myOrderWin.reload();
    myOrderWin.show();
  } else {
    myOrderWin = await createPersonalInfoWindow(
      {
        // devTools: showDevTool,
        devTools: false,
        scrollBounce: process.platform === 'darwin',
      },
      {
        frame: IsUseSysTitle,
        useContentSize: true,
        autoHideMenuBar: true,
        transparent: false,
      },
    );
    myOrderWin.loadURL(`${winURL}#/myOrder`);
    // 开发模式下自动开启devtools
    !type && myOrderWin.show();
    myOrderWin.on('close', (e: any) => {
      e.preventDefault();
      myOrderWin.hide();
    });
    // 我的订单控制台
    if (showDevTool) {
      // myOrderWin.webContents.openDevTools({
      //   mode: "undocked",
      //   activate: true,
      // });
    }
  }
};

export default {
  Mainfunc() {
    usePrintHandle();
    setupMessageModule();
    initViewWin();

    // 注册知行方法
    setupZhixingModule();

    const Store = require('electron-store');
    Store.initRenderer();
    getSDK().ipc.handleMain('window-close-all', async() => {
      try {
        for (const item in windowManager) {
          if (item && windowManager[item]?.webContents) {
            if (windowManager[item]?._name === 'mainWindow' || windowManager[item]?._name === 'loginWindow') {

            } else {
              console.log('window-close-all', item);
              windowManager[item] = null;
            }
          }
        }
        return true;
      } catch (error) {
        console.error('window-close-all error: ', error);
        return false;
      }
    });
    ipcMainHandle('getIpAddress', (event) => {
      const ipAddress = getIpAddress();
      return ipAddress;
    });
    // 客服与帮助
    ipcMainHandle('my-help', async (event, args, type?: string) => {
      createHelpAndServiceWindow(args, type);
    });
    ipcMain.on('update-nume-index', (event, value) => {
      const win = windowManager.mainWindow;
      console.log('走著路', win, value);
      if (win) {
        try {
          [0, 2].includes(value) && windowManager.hideBvs();
          win.webContents.send('update-nume-index', value);
        } catch (error) {
          console.error('update-nume-index error: ', error);
        }
      }
    });
    // ipcMain.on('code-verification', (event, value) => {
    //   console.log('code-verification');
    //   const win = windowManager.mainWindow;
    //   if (win) {
    //     win.webContents.send('code-verification-fn', value);
    //   } else {
    //     console.error('Main window not found');
    //   }
    // });
    ipcMain.on('send-card-end', (event, value) => {
      console.log('send-card-end');
      const win = windowManager.mainWindow;
      if (win) {
        win.webContents.send('send-card-end', value);
        if (vcardWin.value) {
          vcardWin.value.webContents.focus();
        }
      } else {
        console.error('Main window not found');
      }
    });
    //   ipcMain.on('send-card-end', () => {
    //     console.log('send-card-end'););

    //   const win = windowManager.mainWindow;
    //   win.webContents.send('send-card-end');

    // });
    // 打开文件所在位置url 下载地址+文件名
    ipcMainHandle('open-file-location', async (event, url) => {
      const filePath = path.join(url);
      const exist = fs.existsSync(filePath);
      if (exist) {
        shell.openPath(filePath);
        return true;
      }
      return false;
    });
    // 发票中心
    ipcMainHandle('my-invoice', async (e, type?: string) => {
      createInvoiceCenterWindow(type);
    });
    ipcMainHandle('IsUseSysTitle', async () => IsUseSysTitle);
    ipcMainHandle('toggle-devtools', (event, arg) => {
      const matchResult = arg.match(/打开控制台(.*)/);
      if (matchResult) {
        const bv = matchResult[1];
        const arr = {
          main: { value: windowManager.mainWindow },
          imWin: { value: windowManager.messageView },
          popView: { value: windowManager.popView },
          SquareWinBV,
          MemberWinBV,
          PoliticsWinBV,
          DigitalPlatformWinBV,
          ClouddiskWinBV,
          ZhixingWinBV,
          ApprovalWinBV,
          ProjectWinBV,
          ServiceWinBV,
          BusinessWinBV,
          BenchWinBV,
          bigMarketWinBV,
          ActivityWinBV,

          imgviewFile: viewImgWin,
          mp4viewFile: viewVideoWin,
          viewFile: viewFileWin,
          activeCodeWin,
          vcardWin,
          StandAloneCustomerServiceWinBW,
          StandAloneMemberWinBW,
          StandAlonePoliticsWinBW,
          StandAloneDigitalPlatformWinBW,
          StandAloneZhixingWinBW,
          StandAloneH5BW,
          StandAloneProjectWinBW,
          StandAloneServiceWinBW,
        };

        const humanLabel = {
          main: '主窗口',
          SquareWinBV: '广场',
          MemberWinBV: '会员',
          PoliticsWinBV: '城市',
          ClouddiskWinBV: '云盘',
          ZhixingWinBV: '知行',
          ApprovalWinBV: '审批',
          ProjectWinBV: '工程',
          ServiceWinBV: '服务',
          BusinessWinBV: '商机',
          BenchWinBV: '数智工场',
          bigMarketWinBV: '大市场',
          DigitalPlatformWinBV: '数字平台',
          ActivityWinBV: '活动',
          popView: '弹窗',
          viewFile: '文件预览',
          imgviewFile: '图片预览',
          mp4viewFile: '视频预览',
          imWin: 'imWin',
          activeCodeWin: '激活码',
        };

        const template = Object.entries(arr).map(([label, value]) => ({
          label: humanLabel[label] || label,
          click: () => {
            console.log('toggle-devtools', label, value);

            if (label === 'activeCodeWin') {
              value.webContents.toggleDevTools();

              return;
            }
            if (!value.value) {
              dialog.showErrorBox('提示', '该窗口不存在，请先切到该窗口对应的模块');
              return;
            }
            value.value.webContents.toggleDevTools();
          },
        }));

        const allWinManager = getSDK().windowManager.getAll();

        allWinManager.forEach((item) => {
          template.push({
            label: `新窗口管理_${item._name}`,
            click: () => {
              item.webContents.toggleDevTools();
            },
          });
        });

        const menu = Menu.buildFromTemplate(template);
        menu.popup({ window: BrowserWindow.fromWebContents(event.sender) });

        Object.entries(arr).forEach(([key, value]) => {
          if (bv === key && value?.value) {
            value.value.webContents.toggleDevTools();
          }
        });
      }
    });
    ipcMainHandle('windows-mini', (event) => {
      BrowserWindow.fromWebContents(event.sender)?.minimize();
    });
    ipcMainHandle('window-max', async (event) => {
      console.log(event.sender.id, 'idddddddddddddddd');
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win?.isMaximized()) {
        win?.restore();
        win?.unmaximize();
        return { status: false };
      }
      win?.maximize();
      return { status: true };
    });
    ipcMainHandle('view-img-window-hide', (event) => {
      const senderURL = event.sender.getURL();
      restViewWin(senderURL);
      setTimeout(() => {
        BrowserWindow.fromWebContents(event.sender).hide();
      }, 100);
      /**
       * 如有最后一次窗口id 刚关闭时显示打开它的窗口
       */
      if (GlobalStatus.latestViewImgWebContentId) {
        try {
          const webContent = webContents.fromId(GlobalStatus.latestViewImgWebContentId);
          const win = BrowserWindow.fromWebContents(webContent);
          win?.focus();
          win?.getBrowserViews()?.forEach((item) => {
            item.webContents.focus();
          });
        } catch (error) {
          windowManager.mainWindow?.focus();
          windowManager.mainWindow?.getBrowserViews()?.forEach((item) => {
            item.webContents.focus();
          });
        } finally {
          GlobalStatus.latestViewImgWebContentId = undefined;
        }
      } else {
        windowManager.mainWindow?.focus();
        windowManager.mainWindow?.getBrowserViews()?.forEach((item) => {
          item.webContents.focus();
        });
      }
    });
    ipcMainHandle('window-hide', (event) => {
      BrowserWindow.fromWebContents(event.sender).hide();
      windowManager.mainWindow.focus();
    });
    ipcMainHandle('window-close', (event) => {
      try {
        console.log('window-close-----------------------', event.sender);
        const win = BrowserWindow.fromWebContents(event.sender);
        win?.close?.();
      } catch (error) {
        console.error('window-close', error);
      }
    });

    ipcMainHandle('window-destory', (event) => {
      try {
        console.log('window-destory-----------------------', event.sender);
        const win = BrowserWindow.fromWebContents(event.sender);
        try {
          let view = win.getBrowserView();
          if (view) {
            if (!view.webContents?.isDestroyed()) {
              win.setBrowserView(null);  // 先移除
              view.webContents?.destroy();       // 释放资源
            }
          }
        } catch (error) {
          console.error('window-destory view error:', error);
        }
        win?.close?.();
        if (win) {
          win.destroy();
        }
      } catch (error) {
        console.error('window-destory', error);
      }
    });

    ipcMainHandle('menubar-icon-show', (event, arg) => {
      if (!windowManager.mainWindow?.isFocused()) return;
      setPopBv({ show: true, type: 'moreMenuIcon', isCollapse: arg.isCollapse });
    });
    ipcMainHandle('menubar-icon-hide', (event, arg) => {
      if (!windowManager.mainWindow?.isFocused()) return;
      setPopBv({ show: false, type: 'moreMenuIcon' });
    });

    ipcMainHandle('show-more-menu', (event, arg) => {
      setPopBv({
        show: true,
        type: 'moreMenu',
        data: arg.data,
        isCollapse: arg.isCollapse,
        leftMenuNum: arg.leftMenuNum,
      });
    });

    ipcMainHandle('check-update', (event, arg) => {
      const update_reminder = arg.data?.update_reminder;
      // todo 暂时屏蔽原来更新，直接全部弹框跳转url下载地址
      if (arg.isHandCheck || arg.data?.update_reminder !== 'normal') {
        const appStore = electronStore({ name: 'appStore' });
        const updatePack = appStore.get('updatePack');
        let _data = arg.data;
        // 如果是包更新触发的，直接拿本地数据就可以了
        if (!_data && arg.origin === 'updatePack' && updatePack.data) {
          _data = updatePack.data;
        }

        setPopBv({ show: true, type: 'appAuthUpdateTip', data: _data });

        // 强制更新 或者 非强制&主动提醒 才显示更新图标
        if (arg.data?.update_reminder !== 'normal') {
          console.log('appStore===', updatePack);
          // 暂不用(这里判断下，如果已经显示了更新图标就不去设置，以免造成状态混乱)
          // if (!updatePack || !updatePack?.show) {
          appStore.set('updatePack', {
            show: true,
            status: 'newPack',
            data: _data,
          });
          // }
        }
      }
    });
    ipcMainHandle('all-dialog', () => {
      // 暂时用下dialogLogout的尺寸
      setPopBv({ show: true, type: 'allDialog', extype: 'dialogLogout' });
      // windowManager?.popView.webContents.send("showtype", { type: 'allDialog' });
    });
    ipcMainHandle('all-dialog-org', (event, arg) => {
      // 暂时用下dialogLogout的尺寸
      if (arg?.show === false) {
        setPopBv({ show: false });
        return;
      }
      setPopBv({ show: true, type: 'allDialog', extype: 'dialog', data: { excuteFn: 4, type: arg?.type } });
      // windowManager?.popView.webContents.send("showtype", { type: 'allDialog' });
    });
    ipcMainHandle('popBvChangeData', (event, arg) => {
      // ipcRenderer.invoke('changeData', { key: 'checkTeams' });
      // 暂时用下dialogLogout的尺寸
      arg?.key && windowManager?.mainWindow.webContents.send(arg?.key, arg);
    });

    ipcMainHandle('app-close', () => {
      app.quit();
    });

    ipcMainHandle('get-static-path', () => staticPaths);
    ipcMainHandle('open-messagebox', async (event, arg) => {
      const res = await dialog.showMessageBox(BrowserWindow.fromWebContents(event.sender), {
        type: arg.type || 'info',
        title: arg.title || '',
        buttons: arg.buttons || [],
        message: arg.message || '',
        noLink: arg.noLink || true,
      });
      return res;
    });
    ipcMainHandle('open-errorbox', (event, arg) => {
      dialog.showErrorBox(arg.title, arg.message);
    });
    ipcMainHandle('start-server', async () => {
      try {
        const serveStatus = await Server.StatrServer();
        console.log(serveStatus);
        return serveStatus;
      } catch (error) {
        dialog.showErrorBox('错误', error);
      }
    });
    ipcMainHandle('stop-server', async () => {
      try {
        const serveStatus = await Server.StopServer();
        return serveStatus;
      } catch (error) {
        dialog.showErrorBox('错误', error);
      }
    });

    ipcMainHandle('start-download', (event, downloadUrl) => {
      new DownloadFile(BrowserWindow.fromWebContents(event.sender), downloadUrl).start();
    });

    ipcMainHandle('set-size', (event, arg) => {
      setSizeFlag = arg.flag;

      const { width, height } = windowManager.mainWindow.getBounds();
      const bX = arg.flag ? leftWinMaxWidth : leftWinMainWidth;
      const bounds = {
        x: bX,
        y: footHeoght,
        width: width - bX,
        height: height - footHeoght,
      };
      const bvs = windowManager.mainWindow.getBrowserViews();
      bvs.forEach((item) => {
        if (!item.webContents.getURL().includes('windows')) {
          item.setBounds(bounds);
          item.setAutoResize({ width: true, height: true });
        }
      });

      return !!arg.flag;
    });
    function readFileAsync(filePath) {
      return new Promise((resolve, reject) => {
        fs.readFile(filePath, (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });
    }
    // 递归遍历文件夹内的内容
    async function readFolder(folderPath) {
      let arr = [];
      try {
        const files = await fs.promises.readdir(folderPath);
        for (const file of files) {
          const filePathyuanben = path.join(folderPath, file);
          const filePath = path.join(folderPath).match(/(.*)\\[^\\]*$/)[1];
          const fileName = filePathyuanben.substring(filePathyuanben.lastIndexOf('\\') + 1);
          const parentName = folderPath.substring(folderPath.lastIndexOf('\\') + 1);
          // const parentName = parts[parts.length - 1];
          const stats = await fs.promises.stat(filePathyuanben);
          const objs = {
            file: null,
            fileType: null,
            filePath: filePathyuanben,
            folderPath,
            parentName,
            fileName,
          };

          if (stats.isFile()) {
            objs.file = await readFileAsync(filePathyuanben);

            objs.fileType = '文件';
          }

          if (stats.isDirectory() && stats.size === 0) {
            console.log(`空文件夹：${filePath}`);
            objs.fileType = '文件夹';
          }

          arr.push(objs);

          if (stats.isDirectory()) {
            const nestedArr = await readFolder(filePathyuanben);
            arr = arr.concat(nestedArr);
          }
        }
      } catch (error) {
        console.error(error);
        throw error;
      }

      return arr;
    }

    // class Folder {
    //   constructor(name, type, sourceFile) {
    //     this.name = name;
    //     this.type = type;
    //     this.sourceFile = sourceFile;
    //     this.children = [];
    //   }
    // }
    // function getFolderStructure(folderPath) {
    //   const folder = new Folder(path.basename(folderPath), "folder");

    //   const files = fs.readdirSync(folderPath);
    //   files.forEach((file) => {
    //     const filePath = path.join(folderPath, file);
    //     const stats = fs.statSync(filePath);

    //     if (stats.isDirectory()) {
    //       folder.children.push(getFolderStructure(filePath));
    //     } else {
    //       folder.children.push(new Folder(file, "file", filePath));
    //     }
    //   });

    //   return folder;
    // }
    ipcMainHandle('getUploadFile', async (event, filePath) => {
      try {
        const fileContent = fs.readFileSync(filePath, { encoding: null });
        return fileContent;
      } catch (error) {
        if (error.code === 'ENOENT') {
          return 'ENOENT';
        }
      }
    });
    ipcMainHandle('uploadFolder', async () => {
      try {
        const result = await dialog.showOpenDialog({ properties: ['openDirectory'] });
        if (!result.canceled) {
          const folderPath = result.filePaths[0];
          const folderStructure = getFolderStructure(folderPath);
          if (folderStructure?.totalSize === 0 && folderStructure?.children?.length === 0) {
            throw new Error('empty');
          }
          return JSON.stringify(folderStructure);
        }
        return null;
      } catch (err) {
        return err;
      }
    });
    ipcMainHandle('dow-file-tcs', async (event, arg) => {
      console.log(arg.arr, 'arrarrarr');

      // 批量下载文件
      // const folderDirectory = arg.arr[0].url.substring(
      //   arg.arr[0].url.lastIndexOf("/") + 1
      // );
      // const namepng = folderDirectory.substring(
      //   folderDirectory.lastIndexOf("/") + 1
      // );
      // const name = namepng.substring(0, namepng.lastIndexOf("."));
      const Store = require('electron-store');
      const useStore = new Store({ accessPropertiesByDotNotation: true });
      const { join } = require('path');
      const savePath = useStore.get('im.save_path');
      const { filePaths } = await dialog.showOpenDialog({
        title: '请选择保存路径',
        properties: ['openDirectory'],
        defaultPath: getFilePath(join(savePath), true),
      });
      await Promise.all(
        arg.arr.map(async (e) => {
          console.log(e, '这是是e');

          const fileNameWithExtension = e.url.substring(e.url.lastIndexOf('/') + 1);
          if (filePaths && filePaths.length > 0) {
            const filePath = path.join(
              filePaths[0],
              e.name,
              // fileNameWithExtension取消匹配
            );
            const pathInfo = path.parse(filePath);
            let downloader = null;
            console.log('下载', filePath, pathInfo);
            downloader = new Downloader({
              url: e.url,
              directory: pathInfo.dir,
              fileName: pathInfo.base,
              cloneFiles: false,
              onError: (error) => {
                console.error('==================');
                console.error('==================');
                console.error('==================');
                console.error('下载失败: ', e.url);
                console.error('pathInfo: ', pathInfo);
                console.error('error: ', error);
                console.error('==================');
                console.error('==================');
                console.error('==================');
              },
            });
            try {
              await downloader.download();
              event.sender.send('download-completed', fileNameWithExtension);
            } catch (error) {
              console.log(error);
            }
          }
        }),
      );

      return filePaths;
    });
    ipcMainHandle('download-file', async (event, arg) => {
      const useStore = new Store({ accessPropertiesByDotNotation: true });
      // 是否需要询问下载位置, 传的inquiry优先级高于偏好设置
      const inquiry = arg.inquiry ?? useStore.get('im.inquiry', true);
      console.error('inquiry', inquiry);
      const filePath = inquiry ? await saveAsFile(arg.title, arg.url, arg.fileType) : await saveFile(arg.title, arg.url);
      return filePath;
    });

    // 选择目录
    ipcMainHandle('select-dir', getDownloadDirIpc);
    // 打开文件
    ipcMainHandle('open-file', openFileIpc);
    // 打开文件所在文件夹
    ipcMainHandle('show-in-folder', showInFolderIpc);

    ipcMainHandle('click-standalone-window', async (event, arg) => {
      try {
        // 独立窗口
        console.log(StandAloneWorkBenchWinBW.value, 'StandAloneWorkBenchWinBW.valueStandAloneWorkBenchWinBW.value');

        // if (StandAloneWorkBenchWinBW.value) {
        //   // 工作台
        //   BenchWinBV.value.webContents.send("set-work-bench-tab-item", {
        //     activeIcon: "appuuidapproveactive",
        //     icon: "appuuidapprove",
        //     path: "/approvalIndex/approve_home",
        //     path_uuid: "approve",
        //     title: "审批",
        //     gopath: false,
        //   });

        //   StandAloneWorkBenchWinBW.value.addBrowserView(ApprovalWinBV.value);

        //   // const Store = require("electron-store");
        //   // const useStore = new Store({ accessPropertiesByDotNotation: true });

        //   // if (arg.groupId && arg.groupId === useStore.get('gztdqzzid')) {
        //   //   //当前独立窗口打开了并且窗口的id等于准备打开的窗口id,工作台窗口push当前窗口
        //   // } else {
        //   //   //打开一个独立窗口
        //   // }
        //   return;
        // }
        const windowsMap = {
          // 定义windowsMap，根据flag返回相应的BrowserWindow对象
          workBenchIndex: {
            bw: StandAloneWorkBenchWinBW,
            bv: BenchWinBV,
            bvName: 'workBench',
          },
          bigMarketIndex: {
            bw: StandAlonebigMarketWinBW,
            bv: bigMarketWinBV,
            bvName: 'bigMarket',
          },
          clouddiskIndex: {
            bw: StandAloneClouddiskWinBW,
            bv: ClouddiskWinBV,
            bvName: 'clouddisk',
          },
          approvalIndex: {
            bw: StandAloneApprovalWinBW,
            bv: ApprovalWinBV,
            bvName: 'approval',
          },
          deviceIndex: {
            bw: StandAloneDeviceWinBW,
            bv: DeviceWinBV,
            bvName: 'device',
          },
          CustomerIndex: {
            bw: StandAloneCustomerWinBW,
            bv: CustomerWinBV,
            bvName: 'customer',
          },
          SupplierIndex: {
            bw: StandAloneSupplierWinBW,
            bv: SupplierWinBV,
            bvName: 'supplier',
          },
          partnerIndex: {
            bw: StandAlonePartnerWinBW,
            bv: PartnerWinBV,
            bvName: 'partner',
          },
          businessIndex: {
            bw: StandAloneBusinessWinBW,
            bv: BusinessWinBV,
            bvName: 'business',
          },
          square: {
            bw: StandAloneSquareWinBW,
            bv: SquareWinBV,
            bvName: 'square',
          },
          engineerIndex: {
            bw: StandAloneProjectWinBW,
            bv: ProjectWinBV,
            bvName: 'project',
          },
          memberIndex: {
            bw: StandAloneMemberWinBW,
            bv: MemberWinBV,
            bvName: 'member',
          },
          politicsIndex: {
            bw: StandAlonePoliticsWinBW,
            bv: PoliticsWinBV,
            bvName: 'politics',
          },
          digitalPlatformIndex: {
            bw: StandAloneDigitalPlatformWinBW,
            bv: DigitalPlatformWinBV,
            bvName: 'digitalPlatform',
          },
          serviceIndex: {
            bw: StandAloneServiceWinBW,
            bv: ServiceWinBV,
            bvName: 'service',
          },

          zhixing: {
            bw: StandAloneZhixingWinBW,
            bv: ZhixingWinBV,
            bvName: 'zhixing',
          },
          customerServiceIndex: {
            bw: StandAloneCustomerServiceWinBW,
            bv: CustomerServiceBV,
            bvName: 'customerService',
          },
          activity: {
            bw: StandAloneActivityWinBW,
            bv: ActivityWinBV,
            bvName: 'activity',
          },
        };
        const { bw, bv } = windowsMap[arg.flag]; // 获取相应的BrowserWindow对象
        // 重置侧边栏数据
        const focusedWin = BrowserWindow.getFocusedWindow();
        focusedWin.webContents.send('new-window-left-rest');
        return await setTimeout(() => {
          publicIndependentWin(arg, bw, bv); // 调用publicIndependentWin函数
        }, 150);
      } catch (error) {
        console.error('click-standalone-window error: ', error);
      }
    });
    ipcMainHandle('click-menu-item', onToggleLeftMenu);

    ipcMainHandle('open-square-album', (evt, arg: { id: string; square_id: string; type: number }) => {
      try {
        windowManager.mainWindow.webContents.send('update-nume-index', 1);
        const jumpArg = {
          url: '/square/friend-circle',
          click_path_uuid: 'square',
          selected_path_uuid: 'message',
        };
        enum albumType {
          ONESEIF = 1,
          FRIEND = 2,
        }
        const albumUrl = arg.type === albumType.FRIEND
          ? `/#/square/friend-ablum/ablum-detail?id=${arg.id}&squareId=${arg.square_id}`
          : `/#/square/phone-album/album-detail?id=${arg.id}`;
        onToggleLeftMenu(evt, jumpArg).then(() => {
          const url = winURL + albumUrl;
          app.isPackaged ? SquareWinBV.value.webContents.loadFile(url) : SquareWinBV.value.webContents.loadURL(url);
        });
      } catch (error) {
        console.error('open-square-album error: ', error);
      }
    });

    ipcMainHandle('open-win', async (event, arg) => {
      const ChildWin = await getSDK().windowManager.create({
        name: `childWin-name-${arg.name || ''}-${getRandomUUID()}`,
        url: '',
        browserWindow: {
          // @ts-ignore
          minWidth: 680,
          minHeight: 480,
          // 移除不兼容的属性
          titleBarStyle: IsUseSysTitle ? 'default' : 'hidden',
          ...otherWindowConfig(),
        },
      });
      // 开发模式下自动开启devtools
      if (showDevTool) {
        // ChildWin.webContents.openDevTools({ mode: "undocked", activate: true });
      }
      ChildWin.loadURL(`${winURL}#${arg.url}`);
      // ChildWin.once('ready-to-show', () => {
        ChildWin.show();
        if (arg.IsPay) {
          // 检查支付时候自动关闭小窗口
          const testUrl = setInterval(() => {
            const Url = ChildWin.webContents.getURL();
            if (Url.includes(arg.PayUrl)) {
              ChildWin.close();
            }
          }, 1200);
          ChildWin.on('close', () => {
            clearInterval(testUrl);
          });
        }
      // });
      // 渲染进程显示时触发
      ChildWin.once('show', () => {
        try {
          ChildWin.webContents.send('send-data-test', arg.sendData);
        } catch (error) {
          console.error('ChildWin send-data-test error: ', error);
        }
      });
    });

    ipcMainHandle('init-window', () => {
      initWindow(false);
    });
    ipcMainHandle('login-suc', () => {
      console.log('login-suc');
      initWindow(true);
    });
    ipcMainHandle('login-suc-moreAccount', (event, arg) => {
      if (arg?.refreshMainWindow) {
        quitLogin(arg);
      }
      const windowManager = getSDK().windowManager;
      /**
   * 临时接入退出逻辑
   * 后期接入主窗口sdk 逻辑
   */

      windowManager.close('帮助中心');
      windowManager.close('我的订单');
      windowManager.close('发票中心');
    });
    ipcMainHandle('start-capture', () => {
      beginCapture();
    });
    ipcMainHandle('stop-capture', () => {
      endCapture();
    });
    ipcMainHandle('create-iframe', (event, arg) => {
      createIframeWindow(arg);
    });
    ipcMainHandle('create-h5-preview', (event, arg) => {
      createPreviewWindow(arg);
    });
    ipcMainHandle('create-dialog', (event, arg) => {
      createDialogWindow(arg.url, arg.opts);
    });
    ipcMainHandle('close-dialog', (event, arg) => {
      closeDialogWindow();
    });
    ipcMainHandle('create-alone-window', (event, arg) => {
      createAloneWindow(arg.url, arg.opts, arg.type);
    });
    ipcMainHandle('close-alone-window', (event, arg) => {
      closeAloneWindow();
    });
    ipcMainHandle('create-remind-dialog', (event, { url, data, opts }) => {
      createRemindDialogWindow(url, data, opts);
    });
    ipcMainHandle('close-remind-dialog', (event, arg) => {
      closeRemindDialogWindow();
    });
    ipcMainHandle('remove-remind-dialog', (event, { url, data, opts }) => {
      removeRemindDialogWindow(url, data, opts);
    });
    ipcMainHandle('set-remind-dialog-size', (event, arg) => {
      windowManager.setRemindDialogSize(windowManager[arg.window], arg.width, arg.height, arg.smooth ?? true);
    });
    ipcMainHandle('open-square-lite', (event, { url, opts }) => {
      new SquareWindow(url, opts).open();
    });
    ipcMainHandle('show-main', () => {
      const win = windowManager.mainWindow; // 获取主窗口
      win.show();
    });

    ipcMainHandle('open-vcard', async (event, arg) => {
      if (vcardWin.value && !vcardWin.value.isDestroyed()) {
        vcardWin.value.webContents.send('reloadData');
        if (arg.inMsg) {
          vcardWin.value.webContents.send('inMsgToList');
        }
        vcardWin.value.show();
      } else {
        vcardWin.value = await getSDK().windowManager.create({
          name: 'vcard',
          url: getRendererWindowEntry('windows/vcard/index.html'),
          browserWindow: {
            width: 376,
            height: 676,
            frame: false,
            resizable: false,
            show: false,
            webPreferences: {
              nodeIntegration: true,
             contextIsolation: false,
            }
          }
        })
        vcardWin.value.loadURL(getRendererWindowEntry('windows/vcard/index.html'));
        // 在页面加载完成后发送事件
        vcardWin.value.webContents.on('did-finish-load', () => {
          if (arg.inMsg) {
            vcardWin.value.webContents.send('inMsgToList');
          }
        });
        // vcardWin.value.on('ready-to-show', () => {
          if (vcardWin.value) {
            vcardWin.value.show();

          }
        // });

        vcardWin.value.on('closed', () => {
          vcardWin.value = null;
        });
      }
    });
    ipcMainHandle('open-activation-code', async () => {
      if (activeCodeWin) {
        activeCodeWin.show();
      } else {
        activeCodeWin = await getSDK().windowManager.create({
          name: 'activeCodeWin',
          url: getRendererWindowEntry('windows/activationCode/index.html'),
          browserWindow: {
            width: 520,
            height: 624,
            titleBarStyle: 'hidden',
            maximizable: false, // 禁用最大化
            frame: false,
            resizable: false,
            show: false,
            webPreferences: {
              nodeIntegration: true,
             contextIsolation: false,
            }
          }
        })

        // activeCodeWin.on('ready-to-show', () => {
          if (activeCodeWin) {
            activeCodeWin.show();
          }
        // });
        activeCodeWin.on('closed', () => {
          activeCodeWin = null;
        });
      }
      if (showDevTool) {
        activeCodeWin.webContents.openDevTools({
          mode: 'undocked',
          activate: true,
        });
      }
    });
    ipcMainHandle('doc.preview', async (event, arg: PreviewFileParams) => {
      const { x, y, width, height } = windowManager.mainWindow.getBounds();
      const preWin = await getSDK().windowManager.create({
        name: `doc-preview-${getRandomUUID()}`,
        url: '',
        browserWindow: {
          x: x + 40,
          y: y + 40,
          width,
          height,
          titleBarStyle: 'hidden',
          trafficLightPosition: { x: 20, y: 20 },
          webPreferences: {
            webSecurity: false, // 解决跨越问题
            nodeIntegration: true, // 允许渲染进程使用 node 模块
            contextIsolation: false,
          },
        },
      });
      const content = encodeURI(
        JSON.stringify({
          appId: process.env.userConfig?.VITE_WPS_KEY,
          ...arg,
        }),
      );
      preWin.loadURL(getRendererWindowEntry('windows/wps/index.html', content));

      if (showDevTool) {
        // preWin.webContents.openDevTools({
        //   mode: "undocked",
        //   activate: true,
        // });
      }
    });

    // 身份卡窗口尺寸
    const cardWinInfo = { width: 352, height: 560 };

    // 获取身份卡窗口始终位于视口的偏移位置
    const getCardPosition = (point: Electron.Point) => {
      // 鼠标所在显示器的设备像素比、工作区
      const { scaleFactor, bounds } = screen.getDisplayNearestPoint(point);

      // 窗口初始位置位于鼠标点击处右下角
      const x = point.x / scaleFactor;
      const y = point.y / scaleFactor;

      const { width, height } = cardWinInfo;
      return {
        // 确保窗口的 x 坐标在显示器内。若其右边界超出了，将被移动到左边缘
        x: Math.floor(x + width < bounds.width ? x : x - width),
        // 确保窗口的 y 坐标在显示器内。若其下边界超出了，将被移动到上边缘
        y: Math.floor(y + height < bounds.height ? y : y - height),
      };
    };

    // 显示身份卡

    let lastExpandWidth = 0;
    ipcMainHandle(
      'identity-card',
      async (
        event,
        arg: {
          cardId: string;
          myId: string;
          showMoreCard?: string;
          comment?: string;
          route_path?: string;
          applyId?: string;
          openMoreCard?: boolean;
        },
      ) => {
        // const query = Object
        // .keys(arg)
        // .map((key) => `${key}=${encodeURIComponent(arg[key])}`).join('&');
        // console.log(arg);
        if (cardTimer) return;

        lastExpandWidth = 0;

        const useStore = new Store({ accessPropertiesByDotNotation: true });
        // 传入一些其他值
        const storeObj = {
          comment: arg?.comment || '',
        };
        useStore.set('window.identity', storeObj);
        cardTimer = setTimeout(() => {
          clearTimeout(cardTimer);
          cardTimer = null;
        }, 1000);

        // 鼠标点击位置
        const point = screen.getCursorScreenPoint();
        const { x, y } = getCardPosition(point);

        /*
    const { x, y } = screen.getCursorScreenPoint();
    const display = screen.getPrimaryDisplay();
    // 设备像素比
    const scaleFactor = display.scaleFactor;
    cardArg.x = x;
    cardArg.y = y;
    const remainHeight = display.workAreaSize.height - y / scaleFactor;
    const remainRightWidth = display.workAreaSize.width - x / scaleFactor;
    // 判断右边距
    const posX = remainRightWidth < 340 ? x - 150 : x + 2;
    // 判断上边界和下边界
    const posY = y < 75 ? 0 : remainHeight < 75 ? display.workAreaSize.height - 75 : y - 75;
    */

        if (windowManager.identWin) {
          if (getIdentityCardFocus()) {
            windowManager.identWin.loadURL(
              `${winURL}#/identitycard/view/${encodeURIComponent(arg.cardId)}/${encodeURIComponent(
                arg.myId,
              )}?route_path=${arg.route_path || ''}&applyId=${arg.applyId || ''}`,
            );
            return;
          }
          try {
            windowManager.identWin.setPosition(x, y);
            // (cardArg.cardId !== arg.cardId || cardArg.myId !== arg.myId) && windowManager.identWin.loadURL(`${winURL}#/identitycard/load/${encodeURIComponent(arg.cardId)}/${encodeURIComponent(arg.myId)}`);
            // windowManager.identWin.loadURL(`${winURL}#/identitycard/view/${encodeURIComponent(arg.cardId)}/${encodeURIComponent(arg.myId)}`)
            windowManager.identWin.webContents.send('show-card', {
              cardId: encodeURIComponent(arg.cardId),
              myId: encodeURIComponent(arg.myId),
              showMoreCard: arg.showMoreCard || '',
              route_path: arg.route_path || '',
              applyId: arg.applyId || '',
              openMoreCard: arg.openMoreCard || '',
              comment: storeObj.comment || '',
            });
            windowManager.identWin.setOpacity(1);
            windowManager.setWindowSize(windowManager.identWin, 250, 250);
            windowManager.showIdentWin();
          } catch (error) {
            console.error('identity-card error: ', error);
          }
          return;
        }
        // const primaryDis = screen.getPrimaryDisplay();
        // console.log(primaryDis)
        windowManager.identWin = await getSDK().windowManager.create({
          name: `identWin-${getRandomUUID()}`,
          url: '',
          browserWindow: {
            x,
            y,
            width: 250,
            height: 250,
            frame: false,
            show: true,
            titleBarStyle: 'default',
            resizable: false,
            fullscreenable: false,
            maximizable: false,
            webPreferences: { nodeIntegration: true, contextIsolation: false },
            movable: true,
            transparent: true,
          },
        });
        windowManager.identWin.setMenu(null);
        // identWin.setIgnoreMouseEvents(true);

        // 开发模式下自动开启devtools
        // 为了复现bug获取错误信息，暂时开放工具
        if (showDevTool) {
          windowManager.identWin.webContents.openDevTools({ mode: "undocked" });
        }
        windowManager.identWin.loadURL(
          `${winURL}#/identitycard/view/${encodeURIComponent(arg.cardId)}/${encodeURIComponent(
            arg.myId,
            )}?showMoreCard=${arg.showMoreCard || ''}&route_path=${arg.route_path || ''}&applyId=${arg.applyId || ''}`,
          );
        // windowManager.identWin.setAlwaysOnTop(true, 'floating');
        windowManager.identWin.on('blur', (event) => {
          console.log('identWin-blur', lastExpandWidth);
          const win = BrowserWindow.getFocusedWindow();

          if (lastExpandWidth) {
            if (!win) {
              return;
            }
            windowManager.identWin.focus();
            try {
              windowManager.identWin.webContents.send('close-expand');
            } catch (error) {
              console.error('identity-card error: ', error);
            }
            return;
          }

          if (event.sender.webContents.getURL().includes('/identitycard/identityCardEdit')) {
            // windowManager.identWin.focus();
            const isAlwaysOnTop = windowManager.identWin.isAlwaysOnTop();
            const isFocus = getIdentityEditInfoFocus();
            console.log(isAlwaysOnTop, isFocus, getIdentityEditInfoFocus());
            if (!isFocus) {
              windowManager.identWin.setAlwaysOnTop(false);
              windowManager.mainWindow.focus();
            } else {
              // windowManager.identWin.focus();
              windowManager.identWin.setAlwaysOnTop(true);
            }
            return;
          }
          windowManager.identWin.setAlwaysOnTop(false);

          if (cardTimer) {
            windowManager.identWin.focus();
            return;
          }
          console.log('====>getIdentityCardFocus', getIdentityCardFocus());
          if (getIdentityCardFocus()) return;
          cardTimer = 1;
          windowManager.preHideIdentWin().then(() => {
            cardTimer = null;
          });
        });

        windowManager.identWin.on('close', (e) => {
          e.preventDefault();
          cardTimer = 1;
          windowManager.preHideIdentWin().then(() => {
            cardTimer = null;
          });
          try {
            windowManager.mainWindow.webContents.send('close-identWin');
          } catch (error) {
            console.error('identity-card error: ', error);
          }
        });

        // 窗口失去焦点时关闭
        // windowManager.identWin.on('blur', () => windowManager.hideIdentWin());
      },
    );

    ipcMainHandle('identity-expand', (event, arg: { show: boolean; expandWidth: number }) => {
      const win = windowManager.identWin;
      console.log(win, lastExpandWidth, arg);
      if (!win || win.isDestroyed()) return;

      const { width, height } = win.getBounds();
      const originW = width - lastExpandWidth;
      lastExpandWidth = arg.show ? arg.expandWidth : 0;
      const nextW = arg.show ? originW + arg.expandWidth : originW;
      console.log(width, originW, nextW);
      win.setContentSize(nextW, height, true);
    });

    ipcMainHandle('openDialogIdentity', (event, arg) => {
      // 设置窗口是否一直置顶
      if (arg.setNoAlwaysOnTop) {
        windowManager.identWin && windowManager.identWin.setAlwaysOnTop(false);
      }
      setPopBv({ show: true, type: 'dialogIdentity', data: arg });
    });

    ipcMainHandle('identWin-visible-users', (event, arg) => {
      windowManager.identWin.setAlwaysOnTop(true, 'floating');
      try {
        windowManager.identWin.webContents.send('identWin-visible-users', arg);
      } catch (error) {
        console.error('identWin-visible-users error: ', error);
      }
    });

    ipcMainHandle('focus-identWin', () => {
      windowManager.identWin.focus();
    });
    ipcMainHandle('blur-identWin', () => {
      windowManager.identWin.blur();
    });

    ipcMainHandle('hide-identWin', () => {
      cardTimer = 1;
      windowManager.preHideIdentWin().then(() => {
        cardTimer = null;
      });
    });

    ipcMainHandle('set-window-full-sise', (event, arg) => {
      const content = windowManager.mainWindow.getContentBounds();
      windowManager.setWindowSize(windowManager[arg.window], content.width, content.height, arg.smooth ?? true);
    });

    ipcMainHandle('set-identWin-pos', (_, arg) => {
      /*
  let posX;
  let posY;
  if (windowManager.mainWindow) {
    // 身份卡窗口初始打开位置控制在主窗口内
    const { x: mainX, y: mainY, width: mainWidth, height: mainHeight } = windowManager.mainWindow?.getBounds();
    posX = mainX + mainWidth - cardArg.x < 340 ? cardArg.x - 170 : cardArg.x;
    posY =
      mainY + mainHeight - cardArg.y < 500
        ? mainY + mainHeight - 520
        : cardArg.y - mainY < 100
        ? cardArg.y + 20
        : cardArg.y - 50;
  } else {
    const display = screen.getPrimaryDisplay();
    // 设备像素比
    const scaleFactor = display.scaleFactor;
    const remainHeight = display.workAreaSize.height - cardArg.y / scaleFactor;
    const remainRightWidth = display.workAreaSize.width - cardArg.x / scaleFactor;
    // 判断右边距
    posX = remainRightWidth < 340 ? cardArg.x - 340 : cardArg.x - 170;
    posY = cardArg.y < 250 ? cardArg.y : remainHeight < 250 ? display.workAreaSize.height - 500 : cardArg.y - 250;
  }
  windowManager.identWin.setPosition(posX, posY);
  */

      const { width, height } = arg;
      cardWinInfo.width = width;
      cardWinInfo.height = height;
      // 鼠标点击位置
      const point = screen.getCursorScreenPoint();
      const { x, y } = getCardPosition(point);
      windowManager.identWin.setPosition(x, y);
    });

    ipcMainHandle('update-contact', (evt, arg) => {
      try {
        windowManager.mainWindow.webContents.send('update-contact-list', arg);
      } catch (error) {
        console.error('update-contact error: ', error);
      }
    });

    ipcMainHandle('merged-message', (evt, arg: { fileUrl: string }) => {
      const sourceWindow = BrowserWindow.fromWebContents(evt.sender);
      createMergedMessageWindow(arg.fileUrl, sourceWindow);
    });

    ipcMainHandle('quit-login', (evt, arg: { refreshMainWindow?: boolean; openid?: string }) => {
      quitLogin(arg);
      const windowManager = getSDK().windowManager;
      /**
   * 临时接入退出逻辑
   * 后期接入主窗口sdk 逻辑
   */
      windowManager.close('帮助中心');
      windowManager.close('我的订单');
      windowManager.close('发票中心');
    });

    /**
 * 打开内部窗口
 */
    ipcMainHandle(
      'open-internal-window',
      async (
        event,
        args: {
          url: string;
          config: { lang: string; token: string; profile: string };
        },
      ) => {
        const { x, y } = windowManager.mainWindow.getBounds();
        const { width, height } = { width: 439, height: 720 };
        if (!StandAloneH5BW.value) {
          StandAloneH5BW.value = await getSDK().windowManager.create({
            name: `StandAloneH5BW-${getRandomUUID()}`,
            url: '',
            browserWindow: {
              ...previewWindowConfig(),
              x: x + 40,
              y: y + 40,
              width,
              height,
              backgroundColor: '#F5F8FE',
              // trafficLightPosition: { x: 20, y: 20 },
              // titleBarStyle: 'hidden',
              // webPreferences: {
              //   contextIsolation: false,
              //   nodeIntegration: true,
              //   webSecurity: true,
              //   // 在macos中启用橡皮动画
              //   scrollBounce: process.platform === 'darwin',
              // },
            },
          });
          StandAloneH5BW.value.resizable = false;
          StandAloneH5BW.value.loadURL(getRendererWindowEntry('windows/injoin/index.html'));
          StandAloneH5BW.value.on('closed', () => {
            destroyBv(StandAloneH5BW.bv);
            destoryBw(StandAloneH5BW.value);
            StandAloneH5BW.bv = null;
            StandAloneH5BW.value = null;
          });
        }

        StandAloneH5BW.value.show();
        if (StandAloneH5BW.bv) {
          StandAloneH5BW.value.setBrowserView(null);
          destroyBv(StandAloneH5BW.bv);
          StandAloneH5BW.bv = null;
        }
        // StandAloneH5BW.bv = await getSDK().windowManager.create({
        //   name: `stand-alone-h5`,
        //   type: 'BV',
        //   url: '',
        //   browserWindow: {
        //     webPreferences: {
        //       contextIsolation: false,
        //     },
        //   },
        // });
        StandAloneH5BW.bv = new BrowserView({
          webPreferences: {
            // session: getCustomSession(),
            contextIsolation: false,
            preload: getSDK()?.getPreload?.(),
          },
        });
        // StandAloneH5BW.bv = new BrowserView({
        //   webPreferences: {
        //     // session: getCustomSession(),
        //     contextIsolation: false,
        //     preload: getSDK()?.getPreload?.(),
        //   },
        // });
        handleBvRenderProcessGone(StandAloneH5BW.bv.webContents, 'StandAloneH5BW.bv');
        enable(StandAloneH5BW.bv.webContents);
        StandAloneH5BW.value.setBrowserView(StandAloneH5BW.bv);

        StandAloneH5BW.bv.setBackgroundColor('#F5F8FE');
        StandAloneH5BW.bv.setBounds({
          x: 32,
          y: 48,
          width: width - 64,
          height: height - 48,
        });
        StandAloneH5BW.bv.setAutoResize({ width: true, height: true });

        StandAloneH5BW.bv.webContents.executeJavaScript(`
          window.RingkolDesktopApp = {
            "token": '${args.config.token}',
            "appConfig": '{"Locale": "${args.config.lang}"}',
            "userInfo": '${args.config.profile}',
            "title": '申请加入'
          }
        `);

        let ua = StandAloneH5BW.bv.webContents.userAgent;
        ua = ua
          .split(' ')
          .filter((i) => !i.includes('Safari'))
          .join(' ');
        StandAloneH5BW.bv.webContents.loadURL(args.url, {
          userAgent: `${ua} RingkolDesktopApp`,
        });

        if (showDevTool) {
          // StandAloneH5BW.bv.webContents.openDevTools({
          //   mode: "undocked",
          //   activate: true,
          // });
        }
      },
    );
    ipcMainHandle('get-app-info', async (event, { appPath, customPath }) => {
      const { app } = require('electron');
      const { join } = require('path');
      console.log('get-app-info:', app.getVersion());
      return {
        version: app.getVersion(),
        platform: process.platform,
        getAppPath: appPath ? join(app.getPath(appPath), customPath) : '',
      };
    });

    ipcMainHandle('get-system-stats', async () => {
      try {
        return await getSystemStats();
      } catch (error) {
        console.error('获取系统统计信息失败:', error);
        return {
          systemCpuUsage: 0,
          systemMemoryUsage: 0,
          totalSystemMemory: 0,
          windows: []
        };
      }
    });

    ipcMainHandle('open-monitor-window', () => {
      createMonitorWindow();
    });

    ipcMainHandle('set-popbv', (event, arg: { show: boolean; type?: string; data?: any }) => {
      setPopBv(arg);
    });

    ipcMainHandle('set-popbv-other', (event, arg: { show: boolean; type?: string; data?: any }) => {
      setPopBvOther(arg);
    });

    ipcMainHandle('save-base64', async (event, imgUrl, fileName = '二维码') => {
      const useStore = new Store({ accessPropertiesByDotNotation: true });
      // 是否需要询问下载位置
      const inquiry = useStore.get('im.inquiry', true);
      const base64 = imgUrl.replace(/^data:image\/\w+;base64,/, '');
      const dataBuffer = Buffer.from(base64, 'base64');
      const path = `${useStore.get('im.save_path')}/${fileName}`;
      let result = '';
      const savePath = inquiry
        ? (result = await dialog.showSaveDialogSync({
          title: '保存图片',
          defaultPath: getFilePath(path),
          filters: [{ name: 'png', extensions: ['png'] }],
        }))
        : getFilePath(path);
      fs.outputFile(savePath, dataBuffer, (err) => {
        // err && console.log(err);
        !err && shell.showItemInFolder(savePath);
      });
      return result;
    });

    // 封装保存图片用的方法
    ipcMainHandle('save-base64-two', async (event, imgUrl) => {
      const useStore = new Store({ accessPropertiesByDotNotation: true });
      // 是否需要询问下载位置
      const inquiry = useStore.get('im.inquiry', true);
      const base64 = imgUrl.replace(/^data:image\/\w+;base64,/, '');
      const dataBuffer = Buffer.from(base64, 'base64');
      const path = `${useStore.get('im.save_path')}/${imgUrl}.jpg`;
      const savePath = inquiry
        ? dialog.showSaveDialogSync({
          title: '保存图片',
          defaultPath: getFilePath(path),
          filters: [{ name: 'Images', extensions: ['*'] }],
        })
        : getFilePath(path);
      fs.outputFile(savePath, dataBuffer, (err) => {
        // err && console.log(err);
        !err && shell.showItemInFolder(savePath);
      });
      return 'success';
    });
    // 封装保存图片用的方法-自定义名字
    ipcMainHandle('save-base64-three', async (event, imgUrl, fileName?: string, filters?: FileFilter[]) => {
      const useStore = new Store({ accessPropertiesByDotNotation: true });
      // 是否需要询问下载位置
      const inquiry = useStore.get('im.inquiry', true);
      const base64 = imgUrl.replace(/^data:image\/\w+;base64,/, '');
      const dataBuffer = Buffer.from(base64, 'base64');
      const path = `${useStore.get('im.save_path')}/${fileName ?? imgUrl}.png`;
      const savePath = inquiry
        ? dialog.showSaveDialogSync({
          title: '保存图片',
          defaultPath: getFilePath(path),
          filters: filters || [{ name: 'Images', extensions: ['*'] }],
        })
        : getFilePath(path);
      fs.outputFile(savePath, dataBuffer, (err) => {
        // err && console.log(err);
        !err && shell.showItemInFolder(savePath);
      });
      if (savePath === undefined || savePath === 'undefined') {
        return 'fail';
      }
      return 'success';
    });

    ipcMainHandle('save-blob', (event, data) => {
      const useStore = new Store({ accessPropertiesByDotNotation: true });
      // 是否需要询问下载位置
      const inquiry = useStore.get('im.inquiry', true);
      const dataBuffer = Buffer.from(data.blob);
      const path = `${useStore.get('im.save_path')}/${data.title}`;
      const savePath = inquiry
        ? dialog.showSaveDialogSync({
          title: '保存文件',
          defaultPath: getFilePath(path),
          filters: [{ name: data.title, extensions: ['*'] }],
        })
        : getFilePath(path);
      fs.outputFile(savePath, dataBuffer, (err) => {
        if (!err) {
          event.returnValue = savePath; // 返回savePath给渲染进程
        }
      });
      if (savePath === undefined || savePath === 'undefined') {
        return 'fail';
      }
      return 'success';
    });
    // 我的订单
    ipcMainHandle('my-order', async (e, vall, type?: string) => {
      createMyOrderWindow(vall, type);
    });
    ipcMainHandle('live-show', async (e, vall, type?: string) => {
      createLiveShowWindow(vall, type);
    });
    ipcMainHandle('niche-read', async (e, vall, type?: string) => {
      createNicheReadWindow(vall, type);
    });
    ipcMainHandle('niche-examine', async (e, vall, type?: string) => {
      createnicheExamineWindow(vall, type);
    });

    ipcMainHandle('open-setting', (event, arg) => {
      createSettingWindow();
    });

    ipcMainHandle('open-share', (event, arg) => {
      windowManager.hideIdentWin();
      // windowManager.mainWindow.webContents.send("open-share-dialog", arg);
      setPopBv({ show: true, type: 'dialogShare', data: arg });
    });

    ipcMainHandle('set-window-sise', (event, arg) => {
      windowManager.setWindowSize(windowManager[arg.window], arg.width, arg.height, arg.smooth ?? true);
    });

    ipcMainHandle('set-bv-size', (event, arg) => {
      windowManager.setBvSize(windowManager[arg.bv], arg.x, arg.y, arg.width, arg.height);
    });

    ipcMainHandle('set-shortcut', (event, arg) => {
      console.log(arg, 'aaaaaaa');
      switch (arg.type) {
        case 'search':
          setSearch(arg.val, arg.old);
          break;
        case 'screen':
          setScreenShot(arg.val, arg.old);
          break;
        case 'showhide':
          setShowHide(arg.val, arg.old);
          break;
      }
      try {
        windowManager.mainWindow.webContents.send('shortcut-change');
      } catch (error) {
        console.error('shortcut-change error: ', error);
      }
    });

    ipcMainHandle('get-shortcut', (event, arg) => {
      console.log('get-shortcut==:', arg);
      arg.forEach((v) => {
        try {
          windowManager.settingWindow.getBrowserViews().forEach((bv) => {
            try {
              bv.webContents.send('send-shortcut', {
                key: v,
                value: getShortCutStore(v),
              });
            } catch (error) {
              console.error('jump-zhixing error: ', error);
            }
          });
        } catch (error) {
          console.error('get-shortcut error: ', error);
        }
      });
    });

    ipcMainHandle('set-work-bench-tab-item', (event, arg) => {
      try {
        BenchWinBV.value.webContents.send('set-work-bench-tab-item', arg);
      } catch (error) {
        console.error('set-work-bench-tab-item error: ', error);
      }
    });
    ipcMainHandle('del-work-bench-tab-item', (event, arg) => {
      try {
        BenchWinBV.value.webContents.send('del-work-bench-tab-item', arg);
      } catch (error) {
        console.error('del-work-bench-tab-item error: ', error);
      }
    });
    ipcMainHandle('set-big-market-tab-item', (event, arg) => {
      try {
        bigMarketWinBV.value.webContents.send('set-big-market-tab-item', arg);
      } catch (error) {
        console.error('set-big-market-tab-item error: ', error);
      }
    });
    ipcMainHandle('get-init-savepath', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('send-init-savepath', app.getPath('downloads'));
      } catch (error) {
        console.error('get-init-savepath error: ', error);
      }
    });

    ipcMainHandle('change-save-path', (event, arg) => {
      const path = dialog.showOpenDialogSync(windowManager.settingWindow, {
        buttonLabel: '确定',
        properties: ['openDirectory', 'createDirectory'],
      });
      try {
        path && windowManager.settingWindow.webContents.send('set-save-path', path[0]);
      } catch (error) {
        console.error('change-save-path error: ', error);
      }
    });

    ipcMainHandle('ringingChange', (event, arg) => {
      if (arg.from === 'main') {
        windowManager.settingWindow && windowManager.settingWindow.webContents.send('ringingChange');
      } else {
        windowManager.mainWindow.webContents.send('ringingChange');
      }
    });

    ipcMainHandle('sidebar-count-change', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('sidebar-count-change', arg);
        windowManager.mainWindow.getBrowserViews().forEach((bv) => {
          try {
            bv.webContents.send('sidebar-count-change', arg);
          } catch (error) {
            console.error('sidebar-count-change error: ', error);
          }
        });
      } catch (error) {
        console.error('sidebar-count-change error: ', error);
      }

      if (arg?.title === '消息') {
        console.log('====>sidebar-count-change', arg);
        setUnread(arg);
      }
    });
    ipcMainHandle('relaunch-app', (event, arg) => {
      app.relaunch();
      app.exit(0);
    });
    ipcMainHandle('change-profiles-info', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('refresh-profiles-info', arg);
        windowManager.popView.webContents.send('refresh-profiles-info', arg);
      } catch (error) {
        console.error('change-profiles-info error: ', error);
      }
    });
    ipcMainHandle('change-add-contacts', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('refresh-add-contacts', arg);
        windowManager.popView.webContents.send('refresh-add-contacts', arg);
      } catch (error) {
        console.error('change-add-contacts error: ', error);
      }
    });
    ipcMainHandle('set-auth-routers', (event, arg) => {
      try {
        windowManager.mainWindow?.webContents?.send('refresh-auth-routers', arg);
      } catch (error) {
        console.error('set-auth-routers error: ', error);
      }
    });
    ipcMainHandle('add-video-remind', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('add-video-remind', arg);
      } catch (error) {
        console.error('add-video-remind error: ', error);
      }
    });
    ipcMainHandle('jump-zhixing', (event, arg) => {
      windowManager.mainWindow.getBrowserViews().forEach((bv) => {
        try {
          bv.webContents.send('jump-zhixing', arg);
        } catch (error) {
          console.error('jump-zhixing error: ', error);
        }
      });
    });
    ipcMainHandle('refresh-scence-activity', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('refresh-scence-activity', arg);
      } catch (error) {
        console.error('refresh-scence-activity error: ', error);
      }
    });
    // 活动详情更新
    ipcMainHandle('refresh-activity-detail', (event, arg) => {
      try {
        windowManager.dialog?.webContents.send('refresh-activity-detail');
        if (!ActivityWinBV.value) return;
        ActivityWinBV.value?.webContents.send('refresh-activity-detail');
      } catch (error) {
        console.error('refresh-activity-detail error: ', error);
      }
    });
    ipcMainHandle('refresh-activity-unread-stats', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('refresh-activity-unread-stats', arg);
        if (!ActivityWinBV.value) return;
        ActivityWinBV.value?.webContents.send('refresh-activity-unread-stats');
      } catch (error) {
        console.error('refresh-activity-unread-stats error: ', error);
      }
    });
    ipcMainHandle('go-path', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('go-path', arg);
      } catch (error) {
        console.error('go-path error: ', error);
      }
    });
    ipcMainHandle('close-popbv', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('close-popbv', arg);
      } catch (error) {
        console.error('close-popbv error: ', error);
      }
    });
    ipcMainHandle('show-popbv', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('show-popbv', arg);
      } catch (error) {
        console.error('show-popbv error: ', error);
      }
    });
    ipcMainHandle('menuicon-mouse-enter', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('menuicon-mouse-enter', arg);
      } catch (error) {
        console.error('menuicon-mouse-enter error: ', error);
      }
    });
    ipcMainHandle('menuicon-mouse-leave', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('menuicon-mouse-leave', arg);
      } catch (error) {
        console.error('menuicon-mouse-leave error: ', error);
      }
    });
    ipcMainHandle('menubar-toggle', (event, arg) => {
      try {
        windowManager.mainWindow.webContents.send('menubar-toggle', arg);
      } catch (error) {
        console.error('menubar-toggle error: ', error);
      }
    });
    // 全局message提示
    ipcMainHandle(
      'global-message-plugin',
      (
        event,
        arg: {
          content: string;
          theme: string;
          duration?: number;
          zIndex?: number;
          offset?: Array<number>;
          webContents?: string;
        },
      ) => {
        try {
          const winBvList = windowManager.mainWindow.getBrowserViews();
          if (arg.webContents) {
            return windowManager[arg.webContents].webContents.send('global-message-plugin', arg);
          }
          if (winBvList?.length) {
            winBvList.at(-1)?.webContents?.send('global-message-plugin', arg);
          } else {
            windowManager.mainWindow.webContents.send('global-message-plugin', arg);
          }
        } catch (error) {
          console.error('global-message-plugin error: ', error);
        }
      },
    );
    // 刷新数据通知
    ipcMainHandle('refresh-page-data', (event, arg?: { page: string }) => {
      try {
        windowManager.mainWindow.webContents.send('refresh-page-data', arg);
      } catch (error) {
        console.error('refresh-page-data error: ', error);
      }
    });

    // 新消息通知（评论/点赞/关注）, 未读消息统计
    ipcMainHandle('square-notify', (_, args) => {
      try {
        console.log('square-notify', args);
        if (!windowManager.mainWindow) return;
        windowManager.mainWindow.webContents.send('square-notify', args);
        SquareWinBV.value?.webContents.send('square-notify', args);
      } catch (error) {
        console.error('square-notify error: ', error);
      }
    });

    // 广场好友圈新动态红点
    ipcMainHandle('set-unread-post', (_, args) => {
      try {
        console.log('set-unread-post', args);
        if (!windowManager.mainWindow) return;
        windowManager.mainWindow.webContents.send('on-unread-post', args);
        SquareWinBV.value?.webContents.send('on-unread-post', args);
      } catch (error) {
        console.error('set-unread-post error: ', error);
      }
    });
    // 广场好友圈新动态红点
    ipcMainHandle('forum-unread-post', (_, args) => {
      try {
        console.log('forum-unread-post', args);
        console.log('forum-unread-post', DigitalPlatformWinBV.value);
        if (!windowManager.mainWindow) return;
        // windowManager.mainWindow.webContents.send("on-unread-post", args);
        DigitalPlatformWinBV.value?.webContents.send('forum-unread-post', args);
      } catch (error) {
        console.error('forum-unread-post error: ', error);
      }
    });
    // 广场好友圈新动态红点
    ipcMainHandle('IM-refresh', (_, args) => {
      try {
        if (!windowManager.mainWindow) return;
        windowManager.mainWindow.webContents.send('IM-refresh', args);
        BenchWinBV.value?.webContents.send('IM-refresh', args);
      } catch (error) {
        console.error('IM-refresh error: ', error);
      }
    });
    // 广场好友圈新动态红点
    ipcMainHandle('activity-refresh', (_, args) => {
      console.error('活动通知3', args);
      try {
        if (!windowManager.mainWindow) return;
        if (['activity-refresh-syncDynamic', 'activity-refresh-syncActors'].includes(args?.type)) {
          windowManager.mainWindow?.webContents.send('activity-refresh', args);
        }
        if (args?.type === 'activity-refresh-syncActors') {
          // 活动参与状态更新时，关闭对应的对应活动的小弹窗
          removeRemindDialogWindow(null, { remindDetail: { openid: args.data.activity_id }, type: 'activity-close' }, null);
        }

        ActivityWinBV.value?.webContents.send('activity-refresh', args);
        windowManager.dialog?.webContents.send('activity-refresh', args);
      } catch (error) {
        console.error('IM-refresh error: ', error);
      }
    });

    /**
   * 数字平台，刷新通知
   */
    ipcMainHandle('Digital-refresh', (_, args) => {
      try {
        if (!windowManager.mainWindow) return;
        windowManager.mainWindow.webContents.send('Digital-refresh', args);
        DigitalPlatformWinBV.value?.webContents.send('Digital-refresh', args);
      } catch (error) {
        console.error('Digital-refresh error: ', error);
      }
    });

    // 动态审核状态变更推送
    ipcMainHandle('square-post-status', (_, args) => {
      try {
        console.log('square-post-status', args);
        if (!windowManager.mainWindow) return;
        windowManager.mainWindow.webContents.send('square-post-status', args);
        SquareWinBV.value?.webContents.send('square-post-status', args);
      } catch (error) {
        console.error('square-post-status error: ', error);
      }
    });

    // 审批动态红点
    ipcMainHandle('set-unread-post-approval', () => {
      try {
        console.log('set-unread-post-approval');
        if (!windowManager.mainWindow) return;
        windowManager.mainWindow.webContents.send('set-unread-post-approval');
      } catch (error) {
        console.error('set-unread-post-approval error: ', error);
      }
    });

    // 知行红点
    ipcMainHandle('update-zhixing-count', () => {
      try {
        if (!windowManager.mainWindow) return;
        windowManager.mainWindow.webContents.send('set-zhixing-count');
      } catch (error) {
        console.error('update-zhixing-count error: ', error);
      }
    });

    ipcMainHandle('delect-ApprovalWinBV', () => {
      ApprovalWinBV.value = null;
    });


    ipcMainHandle('delect-squareWinBV', () => {
      SquareWinBV.value = null;
    });

    // 删除数字商协
    ipcMainHandle('delect-memberWinBV', () => {
      MemberWinBV.value = null;
    });

    // 删除数字城市
    ipcMainHandle('delect-politicsWinBV', () => {
      PoliticsWinBV.value = null;
    });

    // 审批通知刷新
    ipcMainHandle('update-approve', () => {
      try {
        console.log('ApprovalWinBV', ApprovalWinBV.value);
        if (BenchWinBV.value) {
          BenchWinBV.value.webContents.send('update-approve');
        }
        if (!ApprovalWinBV.value) return;
        ApprovalWinBV.value.webContents?.send('update-approve');
        // require("electron").ipcRenderer.invoke("set-unread-post-approval");
      } catch (error) {
        console.error('update-approve error: ', error);
      }
    });
    // 更新激活码组织
    ipcMainHandle('set-activate-code-org', (event, arg) => {
      event.sender.send('set-activate-code-org', arg);
    });

    // 商机通知刷新
    ipcMainHandle('update-niche-reddot', () => {
      if (!windowManager.mainWindow) return;
      windowManager.mainWindow.webContents.send('update-niche-reddot');
      if (!BenchWinBV.value) return;
      BenchWinBV.value.webContents.send('update-niche-reddot');
    });

    // 知行日程通知
    ipcMainHandle('send-refresh-schedule', () => {
      try {
        if (!ZhixingWinBV.value) return;
        ZhixingWinBV.value?.webContents.send('send-refresh-schedule');
      } catch (error) {
        console.error('send-refresh-schedule error: ', error);
      }
    });

    // 更新知行内部红点
    ipcMainHandle('update-zhixing-internal-count', () => {
      try {
        if (!ZhixingWinBV.value) return;
        ZhixingWinBV.value?.webContents.send('update-zhixing-internal-count');
      } catch (error) {
        console.error('update-zhixing-internal-count error: ', error);
      }
    });

    // 笔记更新消息
    ipcMainHandle('note-update-message', () => {
      try {
        if (windowManager.mainWindow) windowManager.mainWindow.webContents.send('note-update-message-count');
        if (!ZhixingWinBV.value) return;
        ZhixingWinBV.value?.webContents.send('note-update-message-count');
      } catch (error) {
        console.error('note-update-message error: ', error);
      }
    });

    // 笔记本标签变更消息
    ipcMainHandle('send-notebook-label-change-message', () => {
      try {
        if (!ZhixingWinBV.value) return;
        ZhixingWinBV.value?.webContents.send('notebook-label-change-message');
      } catch (error) {
        console.error('send-notebook-label-change-message error: ', error);
      }
    }); // 活动删除通知
    ipcMainHandle('delete-activity-item', () => {
      try {
        windowManager.dialog?.webContents.send('delete-activity-item');
        if (!ActivityWinBV.value) return;
        ActivityWinBV.value?.webContents.send('delete-activity-item');
      } catch (error) {
        console.error('delete-activity-item error: ', error);
      }
    });
    mediaAccessHandle();

    applePayInAppPurchase();
  },
};

const destroyBv = (bv: BrowserView) => {
  if (bv && bv?.webContents?.isDestroyed && bv.webContents.isDestroyed() === false) {
    // (bv.webContents as any)?.destroy && (bv.webContents as any)?.destroy();
    bv.webContents.close();
  }
};

const destoryBw = (bw: BrowserWindow) => {
  if (bw && bw?.isDestroyed && bw.isDestroyed() === false) {
    const bvs = bw.getBrowserViews();
    if (bvs.length) {
      bw.setBrowserView(null);
    }
    bw.destroy();
  }
};

const quitLogin = async (arg) => {
  try {
    const allWindows = await getSDK().windowManager.getAll();
    await getSDK().ipc.invokeMain('window-close-all');
    allWindows.forEach((window) => {
      if (window._name === 'mainWindow' || window._name === 'loginWindow') return;
      getSDK().windowManager.close(window.id);
    });
    const windowsMap = {
      // 定义windowsMap，根据flag返回相应的BrowserWindow对象
      vcardWin: {
        bw: vcardWin.value,
      },
      workBenchIndex: {
        bw: StandAloneWorkBenchWinBW.value,
        bv: BenchWinBV.value,
      },
      bigMarketIndex: {
        bw: StandAlonebigMarketWinBW.value,
        bv: bigMarketWinBV.value,
      },
      clouddiskIndex: {
        bw: StandAloneClouddiskWinBW.value,
        bv: ClouddiskWinBV.value,
      },
      approvalIndex: {
        bw: StandAloneApprovalWinBW.value,
        bv: ApprovalWinBV.value,
      },
      deviceIndex: {
        bw: StandAloneDeviceWinBW.value,
        bv: DeviceWinBV.value,
      },
      CustomerIndex: {
        bw: StandAloneCustomerWinBW.value,
        bv: CustomerWinBV.value,
      },
      SupplierIndex: {
        bw: StandAloneSupplierWinBW.value,
        bv: SupplierWinBV.value,
      },
      partnerIndex: {
        bw: StandAlonePartnerWinBW.value,
        bv: PartnerWinBV.value,
      },
      businessIndex: {
        bw: StandAloneBusinessWinBW.value,
        bv: BusinessWinBV.value,
      },
      square: {
        bw: StandAloneSquareWinBW.value,
        bv: SquareWinBV.value,
      },
      engineerIndex: {
        bw: StandAloneProjectWinBW.value,
        bv: ProjectWinBV.value,
      },
      memberIndex: {
        bw: StandAloneMemberWinBW.value,
        bv: MemberWinBV.value,
      },
      politicsIndex: {
        bw: StandAlonePoliticsWinBW.value,
        bv: PoliticsWinBV.value,
      },
      digitalPlatformIndex: {
        bw: StandAloneDigitalPlatformWinBW.value,
        bv: DigitalPlatformWinBV.value,
      },
      serviceIndex: {
        bw: StandAloneServiceWinBW.value,
        bv: ServiceWinBV.value,
      },

      zhixing: {
        bw: StandAloneZhixingWinBW.value,
        bv: ZhixingWinBV.value,
      },
      customerServiceIndex: {
        bw: StandAloneCustomerServiceWinBW.value,
        bv: CustomerServiceBV.value,
      },
      activity: {
        bw: StandAloneActivityWinBW.value,
        bv: ActivityWinBV.value,
      },
      StandAloneH5: {
        bw: StandAloneH5BW.value,
        bv: StandAloneH5BW.bv,
      },

      myOrderWin: {
        bw: myOrderWin,
      },
      myInvoice: {
        bw: myInvoice,
      },
      myHelp: {
        bw: myHelp,
      },
      // mergedMessageWin: {
      //   bw: mergedMessageWin,
      // },
      nicheReadWin: {
        bw: nicheReadWin
      },
      nicheExamineWin: {
        bw: nicheExamineWin
      },
      // newWinBv: {
      //   bv: newWinBv
      // },
      // popView: {
      //   bv: windowManager.popView
      // },
      // // messageView: {
      // //   bv: windowManager.messageView
      // // },
      // loadWindow: {
      //   bw: windowManager.loadWindow
      // },
      // iframeWindow: {
      //   bw: windowManager.iframeWindow
      // },
      // settingWindow: {
      //   bw: windowManager.settingWindow
      // },
      // previewWindow: {
      //   bw: windowManager.previewWindow
      // },
      // identWin: {
      //   bw: windowManager.identWin
      // },
      // // messageWindow: {
      // //   bw: windowManager.messageWindow
      // // },
      // dialog: {
      //   bw: windowManager.dialog
      // },
      // remindDialog: {
      //   bw: windowManager.remindDialog
      // },
    };
    setUnread({ count: 0, flashFrame: false });
    closePreviwew();
    console.log(SquareWinBV.value, 'SquareWinBV.valueSquareWinBV.value');
    SquareWinBV.value && SquareWinBV.value.webContents?.send('quit-login');
    for (const key in windowsMap) {
      destroyBv(windowsMap[key].bv);
      destoryBw(windowsMap[key].bw);
    }

    if (!windowManager.mainWindow) {
      initWindow();
      return;
    }
    const removeBv = () => {
      windowManager.mainWindow?.setBrowserView(null);
      windowManager.messageWindow?.setBrowserView(null);
    };
    removeBv();

    // if (StandAloneClouddiskWinBW.value) {
    //   StandAloneClouddiskWinBW.value.close();
    //   StandAloneClouddiskWinBW.value.on("closed", () => {
    //     StandAloneClouddiskWinBW.value = null;
    //   });
    // }
    StandAloneWorkBenchWinBW.value = null;
    BenchWinBV.value = null;
    StandAlonebigMarketWinBW.value = null;
    bigMarketWinBV.value = null;
    StandAloneClouddiskWinBW.value = null;
    ClouddiskWinBV.value = null;
    StandAloneApprovalWinBW.value = null;
    ApprovalWinBV.value = null;
    StandAloneDeviceWinBW.value = null;
    StandAloneCustomerWinBW.value = null;
    CustomerWinBV.value = null;
    StandAloneSupplierWinBW.value = null;
    SupplierWinBV.value = null;
    StandAlonePartnerWinBW.value = null;
    PartnerWinBV.value = null;
    StandAloneBusinessWinBW.value = null;
    BusinessWinBV.value = null;
    StandAloneSquareWinBW.value = null;
    SquareWinBV.value = null;
    StandAloneProjectWinBW.value = null;
    ProjectWinBV.value = null;
    StandAloneMemberWinBW.value = null;
    MemberWinBV.value = null;
    StandAlonePoliticsWinBW.value = null;
    PoliticsWinBV.value = null;
    StandAloneDigitalPlatformWinBW.value = null;
    DigitalPlatformWinBV.value = null;
    StandAloneServiceWinBW.value = null;
    ServiceWinBV.value = null;
    StandAloneZhixingWinBW.value = null;
    ZhixingWinBV.value = null;
    StandAloneCustomerServiceWinBW.value = null;
    CustomerServiceBV.value = null;
    StandAloneActivityWinBW.value = null;
    ActivityWinBV.value = null;
    StandAloneH5BW.value = null;
    StandAloneH5BW.bv = null;
    if (activeCodeWin) {
      activeCodeWin.close();
      activeCodeWin.on('closed', () => {
        activeCodeWin = null;
      });
    }
    activeCodeWin = null;

    vcardWin.value = null;
    myOrderWin = null;
    myInvoice = null;
    myHelp = null;


    nicheReadWin = null;
    nicheExamineWin = null;
    // newWinBv = null;

    // windowManager.popView = null;
    // windowManager.loadWindow = null;
    // windowManager.iframeWindow = null;
    // windowManager.settingWindow = null;
    // windowManager.previewWindow = null;
    // windowManager.identWin = null;
    // windowManager.dialog = null;
    // windowManager.remindDialog = null;

    // if (StandAloneApprovalWinBW.value) {
    //   StandAloneApprovalWinBW.value.close();
    //   StandAloneApprovalWinBW.value.on("closed", () => {
    //     StandAloneApprovalWinBW.value = null;
    //   });
    // }
    // StandAloneApprovalWinBW.value = null;
    // ApprovalWinBV.value = null;
    // if (myInvoice) {
    //   myInvoice.close();
    //   myInvoice.on("closed", () => {
    //     myInvoice = null;
    //   });
    // }
    // myInvoice = null;
    // if (myOrderWin) {
    //   myOrderWin.close();
    //   myOrderWin.on("closed", () => {
    //     myOrderWin = null;
    //   });
    // }
    // myOrderWin = null;
    // if (mergedMessageWin) {
    //   mergedMessageWin.close();
    //   mergedMessageWin.on("closed", () => {
    //     mergedMessageWin = null;
    //   });
    // }
    // mergedMessageWin = null;
    // if (StandAloneDeviceWinBW.value) {
    //   StandAloneDeviceWinBW.value.close();
    //   StandAloneDeviceWinBW.value.on("closed", () => {
    //     StandAloneDeviceWinBW.value = null;
    //   });
    // }
    // StandAloneDeviceWinBW.value = null;
    // DeviceWinBV.value = null;

    // if (StandAloneCustomerWinBW.value) {
    //   StandAloneCustomerWinBW.value.close();
    //   StandAloneCustomerWinBW.value.on("closed", () => {
    //     StandAloneCustomerWinBW.value = null;
    //   });
    // }
    // StandAloneCustomerWinBW.value = null;
    // CustomerWinBV.value = null;

    // if (StandAloneSupplierWinBW.value) {
    //   StandAloneSupplierWinBW.value.close();
    //   StandAloneSupplierWinBW.value.on("closed", () => {
    //     StandAloneSupplierWinBW.value = null;
    //   });
    // }
    // StandAloneSupplierWinBW.value = null;
    // SupplierWinBV.value = null;

    // if (StandAlonePartnerWinBW.value) {
    //   StandAlonePartnerWinBW.value.close();
    //   StandAlonePartnerWinBW.value.on("closed", () => {
    //     StandAlonePartnerWinBW.value = null;
    //   });
    // }
    // StandAlonePartnerWinBW.value = null;
    // PartnerWinBV.value = null;

    // if (StandAloneBusinessWinBW.value) {
    //   StandAloneBusinessWinBW.value.close();
    //   StandAloneBusinessWinBW.value.on("closed", () => {
    //     StandAloneBusinessWinBW.value = null;
    //   });
    // }
    // StandAloneBusinessWinBW.value = null;
    // BusinessWinBV.value = null;

    // // 服务
    // if (StandAloneServiceWinBW.value) {
    //   StandAloneServiceWinBW.value.close();
    //   StandAloneServiceWinBW.value.on("closed", () => {
    //     StandAloneServiceWinBW.value = null;
    //   });
    // }
    // StandAloneServiceWinBW.value = null;
    // ServiceWinBV.value = null;

    // if (StandAloneSquareWinBW.value) {
    //   StandAloneSquareWinBW.value.close();
    //   StandAloneSquareWinBW.value.on("closed", () => {
    //     StandAloneSquareWinBW.value = null;
    //   });
    // }
    // StandAloneSquareWinBW.value = null;
    // SquareWinBV.value = null;

    // // 工程
    // if (StandAloneProjectWinBW.value) {
    //   StandAloneProjectWinBW.value.close();
    //   StandAloneProjectWinBW.value.on("closed", () => {
    //     StandAloneProjectWinBW.value = null;
    //   });
    // }
    // StandAloneProjectWinBW.value = null;
    // ProjectWinBV.value = null;

    // // 会员
    // if (StandAloneMemberWinBW.value) {
    //   StandAloneMemberWinBW.value.close();
    //   StandAloneMemberWinBW.value.on("closed", () => {
    //     StandAloneMemberWinBW.value = null;
    //   });
    // }
    // StandAloneMemberWinBW.value = null;
    // MemberWinBV.value = null;

    // // 城市
    // // 会员
    // if (StandAlonePoliticsWinBW.value) {
    //   StandAlonePoliticsWinBW.value.close();
    //   StandAlonePoliticsWinBW.value.on("closed", () => {
    //     StandAlonePoliticsWinBW.value = null;
    //   });
    // }
    // StandAlonePoliticsWinBW.value = null;
    // PoliticsWinBV.value = null;

    // // 数字平台
    // if (StandAloneDigitalPlatformWinBW.value) {
    //   StandAloneDigitalPlatformWinBW.value.close();
    //   StandAloneDigitalPlatformWinBW.value.on("closed", () => {
    //     StandAloneDigitalPlatformWinBW.value = null;
    //   });
    // }
    // StandAloneDigitalPlatformWinBW.value = null;
    // DigitalPlatformWinBV.value = null;

    // // 知行
    // if (StandAloneZhixingWinBW.value) {
    //   StandAloneZhixingWinBW.value.close();
    //   StandAloneZhixingWinBW.value.on("closed", () => {
    //     StandAloneZhixingWinBW.value = null;
    //   });
    // }
    // StandAloneZhixingWinBW.value = null;
    // ZhixingWinBV.value = null;

    // if (StandAloneCustomerServiceWinBW.value) {
    //   StandAloneCustomerServiceWinBW.value.close();
    //   StandAloneCustomerServiceWinBW.value.on("closed", () => {
    //     StandAloneCustomerServiceWinBW.value = null;
    //   });
    // }
    // StandAloneCustomerServiceWinBW.value = null;
    // CustomerServiceBV.value = null;
    // // 活动
    // if (StandAloneActivityWinBW.value) {
    //   StandAloneActivityWinBW.value.close();
    //   StandAloneActivityWinBW.value.on("closed", () => {
    //     StandAloneActivityWinBW.value = null;
    //   });
    // }
    // StandAloneActivityWinBW.value = null;
    // ActivityWinBV.value = null;
    // console.log('15');
    // destroyBv(StandAloneH5BW.bv);
    // destoryBw(StandAloneH5BW.value);
    // StandAloneH5BW.bv = null;
    // StandAloneH5BW.value = null;
    // 关闭独立弹窗
    windowManager.closeDialog();
    windowManager.closeAloneWindow();
    windowManager.closeRemindDialog();

    // 关闭其他window
    windowManager.closeSettingWindow();
    windowManager.closeIdentWindow();
    windowManager.closeIframeWindow();
    windowManager.closePreviewWindow();
    windowManager.closeLoadWindow();
    mergedMessageWin?.hide();
    dbDestory();
    cardTimer = null;
    if (arg?.refreshMainWindow) {
      await windowManager.destoryMessageView();
      refreshMainWindow(arg);
    } else {
      // 关闭数据库
      // 退出im
      await windowManager.destoryMessageView();
      initWindow();
    }
  } catch (error) {
    console.error('quitLogin error: ', error);
    // 退出im
    await windowManager.destoryMessageView();
    initWindow();
  }
};

interface IArg {
  name: string;
  url: string;
}
/**
 * 创建独立窗口
 * 主要针对bv窗口创建的独立窗口
 */
ipcMainHandle('create-independent-window', async (event, arg: IArg) => {
  try {
    if (StandAloneWorkBenchWinBW.value) {
      BenchWinBV.value?.webcontents?.send('set', arg);
    }
    // 独立窗口

    const windowsMap = {
      // 定义windowsMap，根据flag返回相应的BrowserWindow对象
      clouddiskIndex: {
        bw: StandAloneClouddiskWinBW,
        bv: ClouddiskWinBV,
        bvName: 'clouddisk',
      },
      workBenchIndex: {
        bw: StandAloneWorkBenchWinBW,
        bv: BenchWinBV,
        bvName: 'workBench',
      },
      bigMarketIndex: {
        bw: StandAlonebigMarketWinBW,
        bv: bigMarketWinBV,
        bvName: 'bigMarket',
      },
      approvalIndex: {
        bw: StandAloneApprovalWinBW,
        bv: ApprovalWinBV,
        bvName: 'approval',
      },
      deviceIndex: {
        bw: StandAloneDeviceWinBW,
        bv: DeviceWinBV,
        bvName: 'device',
      },
      CustomerIndex: {
        bw: StandAloneCustomerWinBW,
        bv: CustomerWinBV,
        bvName: 'customer',
      },
      SupplierIndex: {
        bw: StandAloneSupplierWinBW,
        bv: SupplierWinBV,
        bvName: 'supplier',
      },
      partnerIndex: {
        bw: StandAlonePartnerWinBW,
        bv: PartnerWinBV,
        bvName: 'partner',
      },
      businessIndex: {
        bw: StandAloneBusinessWinBW,
        bv: BusinessWinBV,
        bvName: 'business',
      },
      square: {
        bw: StandAloneSquareWinBW,
        bv: SquareWinBV,
        bvName: 'square',
      },
      engineerIndex: {
        bw: StandAloneProjectWinBW,
        bv: ProjectWinBV,
        bvName: 'project',
      },
      memberIndex: {
        bw: StandAloneMemberWinBW,
        bv: MemberWinBV,
        bvName: 'member',
      },
      politicsIndex: {
        bw: StandAlonePoliticsWinBW,
        bv: PoliticsWinBV,
        bvName: 'politics',
      },
      digitalPlatformIndex: {
        bw: StandAloneDigitalPlatformWinBW,
        bv: DigitalPlatformWinBV,
        bvName: 'digitalPlatform',
      },

      serviceIndex: {
        bw: StandAloneServiceWinBW,
        bv: ServiceWinBV,
        bvName: 'service',
      },

      zhixing: {
        bw: StandAloneZhixingWinBW,
        bv: ZhixingWinBV,
        bvName: 'zhixing',
      },
      customerServiceIndex: {
        bw: StandAloneCustomerServiceWinBW,
        bv: CustomerServiceBV,
        bvName: 'customerService',
      },
      activity: {
        bw: StandAloneActivityWinBW,
        bv: ActivityWinBV,
        bvName: 'activity',
      },
    };
    const { bw, bv, bvName } = windowsMap[arg.name || 'zhixing']; // 获取相应的BrowserWindow对象
    const win = windowManager.mainWindow; // 获取主窗口
    // 设置尺寸
    const bX = setSizeFlag ? leftWinMaxWidth : leftWinMainWidth;
    const bWidth = win.getBounds().width - bX;
    const bHeight = win.getBounds().height - footHeoght;
    const bounds = {
      minWidth: 1232,
      x: bX,
      y: footHeoght,
      width: bWidth,
      height: bHeight,
    };
    const options = {
      url: `${winURL}#${arg.url}`,
      bounds,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: true,
      },
      reload: true,
      showDevtool: showDevTool,
    };
    // 如果有bw就聚焦过去F
    if (bw.value) {
      bw.value.focus();
      return;
    }
    newWinBv = await windowManager.setBv(bv.value, options, undefined, bvName);
    if (arg.name === 'zhixing') {
      ZhixingWinBV.value = newWinBv; // 将新的BrowserView赋给对象属性
    }
    setTimeout(() => {
      publicIndependentWin(arg, bw, bv); // 调用publicIndependentWin函数
    }, 500);
    if (showDevTool) {
      // newWinBv.webContents.openDevTools();
    }
  } catch (error) {
    console.error('create-independent-window error: ', error);
  }
});

// 广场模块已加载
ipcMainHandle('square-loaded', () => {
  eventBus.emit('square-loaded');
});

// imsdk出错刷新
ipcMainHandle('messageWindow-reload', () => {
  windowManager.refreshMessageWindow();
});

// electron-store
initStore();
