import {
  combMsgInfo,
  _transMsgData,
  _transPlatformDetail
} from "@renderer/views/identitycard/data";
import {
  innerCardsDetails,
  outerCardsDetails,
  personalCardsDetails,
  platformCardsDetails
} from "@renderer/api/contacts/api/follow";


import { getNoteList } from "@renderer/api/contacts/api/common";
import { getJwt } from "@renderer/api/account";

import {
  getServerLang,
  getRefreshCode,
  getAccesstoken,
  getProfilesInfoString,
  getOpenid,
  checkContactAuth,
} from "@renderer/utils/auth";
import { getBaseUrl } from "@renderer/utils/apiRequest";
import qs from "qs";
import { pinyin } from 'pinyin-pro';
import { MessagePlugin } from "tdesign-vue-next";
import { i18nt } from "@renderer/i18n";
import { getSessionLocalIdByCards } from "../message/service/utils";
import { openChat } from "@/utils/share";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer, shell, dialog } = LynkerSDK;

export const sendMsg = (params: {
  main: string;
  peer?: string;
  group?: string;
}) => {
  openChat({ main: params.main, peer: params.peer, group: params.group });
};

const meetingUserMapper = (member) =>
({
  type: "internal",
  name: member.staffName || member.nickname,
  avatar: member.avatar,
  openId: member.openId,
  cardId: member.cardId,
  teamId: member.teamId,
} as MeetingUser);

export const videoMsg = async (cardId, myId) => {
  const checkRes = await checkContactAuth({ from: myId, to: cardId });
  if (!checkRes) {
    MessagePlugin.warning({
      content: i18nt("im.public.pravicyTips"),
      style: { width: '280px' }
    });
    return;
  }

  const params = await combMsgInfo(cardId, myId);
  const myInfo = params.attachment.member.find(
    (item) => item.cardId === params.main
  );
  const peerInfo = params.attachment.member.find(
    (item) => item.cardId === params.peer
  );
  console.log(params, "params");
  const callData: AvMeetingStartData = {
    mediaType: "audioVideo",
    conversation: {
      type: 1,
      id: getSessionLocalIdByCards(params.peer, params.main),
      target: peerInfo.openId,
    },
    starter: meetingUserMapper(myInfo),
    invited: [meetingUserMapper(peerInfo)],
  };
  ipcRenderer.invoke("hide-identWin");
  ipcRenderer.invoke("im.bridge.avreal", { action: "call", data: callData });
};

export const voiceMsg = async (cardId, myId) => {
  const checkRes = await checkContactAuth({ from: myId, to: cardId });
  if (!checkRes) {
    MessagePlugin.warning({
      content: i18nt("im.public.pravicyTips"),
      style: { width: '280px' }
    });
    return;
  }

  const params = await combMsgInfo(cardId, myId);
  const myInfo = params.attachment.member.find(
    (item) => item.cardId === params.main
  );
  const peerInfo = params.attachment.member.find(
    (item) => item.cardId === params.peer
  );
  const callData: AvMeetingStartData = {
    mediaType: "audio",
    conversation: {
      type: 1,
      id: getSessionLocalIdByCards(params.peer, params.main),
      target: peerInfo.openId,
    },
    starter: meetingUserMapper(myInfo),
    invited: [meetingUserMapper(peerInfo)],
  };
  ipcRenderer.invoke("hide-identWin");
  ipcRenderer.invoke("im.bridge.avreal", { action: "call", data: callData });
};

const customSort = (arr) => {
  arr.sort((a, b) => {
    const pinyinA = getPinyin(a.name);
    const pinyinB = getPinyin(b.name);

    return compareStrings(pinyinA, pinyinB);
  });
  return arr;
};

const compareStrings = (a, b) => {
  // 获取较短字符串的长度
  const length = Math.min(a.length, b.length);

  // 逐字符比较
  for (let i = 0; i < length; i++) {
    const charA = a.charCodeAt(i);
    const charB = b.charCodeAt(i);

    if (charA !== charB) {
      return charA - charB;
    }
  }

  // 如果前面的字符都相等，则比较长度
  return a.length - b.length;
};

const getPinyin = (str) => {
  // 将中文字符串转换为拼音数组
  const pinyinArray = pinyin(str, { toneType: 'none', type: 'array' });

  // 将拼音数组拼接成字符串
  return pinyinArray.map((item) => item[0]).join("");
};

export const pySegSort = (arr) => {
  if (!String.prototype.localeCompare) return null;
  const letters = "#ABCDEFGHJKLMNOPQRSTWXYZIUV".split("");
  // let zh = "阿八嚓哒妸发旮哈讥咔垃痳拏噢妑七呥扨它穵夕丫帀".split("");
  const zh = "阿八嚓哒妸发旮哈讥咔垃麻拏噢妑七呥扨它穵夕丫帀".split("");
  const segs = []; // 存放数据
  let curr;
  const reChinese = /^[\u4e00-\u9fa5]/; // 中文正则
  const reEnglish = /^[a-zA-Z]/; // 英文正则

  letters.map((items, i) => {
    curr = {
      initial: "", // 字母
      data: [], // 数据
    };
    arr = arr.filter((v) => {
      // 特殊字符
      let retain = true;
      // 判断首个字是否是英文
      if (reEnglish.test(v.name[0])) {
        if (v.name[0].toUpperCase() === items) {
          curr.data.push(v);
          retain = false;
        }
      } else if (reChinese.test(v.name[0])) {
        // 判断首个字母是否是中文
        if (
          (!zh[i - 1] || zh[i - 1].localeCompare(v.name[0]) <= 0) &&
          v.name[0].localeCompare(zh[i]) <= -1
        ) {
          curr.data.push(v);
          retain = false;
        }
      } else {
        // 特殊字符
        curr.data.push(v);
        retain = false;
      }
      return retain;
    });

    if (curr.data.length) {
      curr.initial = letters[i];
      segs.push(curr);
      // curr.data.sort((a, b) => a.name.localeCompare(b.name));
      curr.data = customSort(curr.data);
    }
  });
  segs.sort((a, b) => a.initial.localeCompare(b.initial));
  segs[0].initial === "#" && segs.push(segs.shift());
  return segs;
};

export const filterCard = (list, key = "cardId") => {
  const outerCards = [];
  const innerCards = [];
  const platformCards = [];
  let personalCards = [];
  list = list.filter((v) => {
    if (~v[key].indexOf("#")) {
      outerCards.push(+v[key].slice(1));
    }
    return !~v[key].indexOf("#");
  });
  list = list.filter((v) => {
    if (~v[key].indexOf("$")) {
      innerCards.push(+v[key].slice(1));
    }
    return !~v[key].indexOf("$");
  });
  list = list.filter((v) => {
    if (/^PT/.test(v[key])) {
      platformCards.push(+v[key].slice(2));
    }
    return !/^PT/.test(v[key]);
  });
  personalCards = list.map((v) => v[key]);
  return { outerCards, innerCards, platformCards, personalCards };
};

export const handelCardIds = async (ids: Array<string>, from?) => {
  let outerDatas = [];
  let innerDatas = [];
  let platformDatas = [];
  let personalDatas = [];
  const { outerCards, innerCards, personalCards, platformCards } = filterids(ids);
  console.log(outerCards, innerCards, personalCards, platformCards, 'filteridsfilterids');

  const res = await getNoteList({ cardIds: ids });
  console.log(res, 'getNoteList');
  const noteList = res.data.data?.cardRemarks || [];
  if (outerCards && outerCards.length) {
    const { data } = await outerCardsDetails({ ids: outerCards });
    outerDatas = await transData(data.data, "outer", noteList);
  }
  if (innerCards && innerCards.length) {
    const { data } = await innerCardsDetails({ ids: innerCards });
    innerDatas = await transData(data.data, "inner", noteList);
  }
  if (personalCards && personalCards.length) {
    const { data } = await personalCardsDetails({ ids: personalCards });
    personalDatas = await transData(data.profiles, "personal", noteList);
  }
  if (platformCards && platformCards.length) {
    const { data } = await platformCardsDetails({ ids: platformCards });
    platformDatas = await transData(data.data, "platform", noteList);
  }
  console.log([...outerDatas, ...innerDatas, ...platformDatas, ...personalDatas], '过滤的数据');

  return [...outerDatas, ...innerDatas, ...platformDatas, ...personalDatas];
};

const filterids = (ids) => {
  const outerCards = [];
  const innerCards = [];
  const platformCards = [];
  let personalCards = [];
  ids = ids.filter((v) => {
    if (~v.indexOf("#")) {
      outerCards.push(+v.slice(1));
    }
    return !~v.indexOf("#");
  });
  ids = ids.filter((v) => {
    if (~v.indexOf("$")) {
      innerCards.push(+v.slice(1));
    }
    return !~v.indexOf("$");
  });
  ids = ids.filter((v) => {
    if (/^PT/.test(v)) {
      platformCards.push(+v.slice(2));
    }
    return !/^PT/.test(v);
  });
  personalCards = ids.filter((v) => !!v);
  return { outerCards, innerCards, platformCards, personalCards };
};

const transData = async (list, type, noteList) => {
  const data = await list.reduce(async (acc, cur) => {
    const emo = await acc;
    cur = type === 'platform' ? _transPlatformDetail(cur) : cur;
    const noteInfo = noteList.find((v) => {
      const id = cur?.cardId || cur?.openid;
      return id === v.cardId;
    });
    const transCradData = _transMsgData(
      type,
      cur,
      noteInfo || { remarks: "", describe: "" }
    );
    emo.push(transCradData);
    return emo;
  }, []);
  return data;
};
const detectDefaultBrowser = (): Promise<string | null> => new Promise((resolve) => {
  const exec = LynkerSDK.child_process.exec;
  if (process.platform === 'win32') {
    exec('reg query "HKCU\\Software\\Microsoft\\Windows\\Shell\\Associations\\UrlAssociations\\http\\UserChoice" /v ProgId', (err, stdout) => {
      if (err) {
        resolve(null);
        return;
      }

      const match = stdout.match(/ProgId\s+REG_SZ\s+(\w+)/);
      if (match && match[1]) {
        const progId = match[1];
        resolve(getBrowserPathFromProgId(progId));
      } else {
        resolve(null);
      }
    });
  } else if (process.platform === 'darwin') {
    exec('defaults read com.apple.LaunchServices/com.apple.launchservices.secure LSHandlers | grep -i web', (err, stdout) => {
      if (err) {
        resolve(null);
        return;
      }

      const match = stdout.match(/LSHandlerURLScheme = http; LSHandlerRoleAll = "(.*?)"/);
      if (match && match[1]) {
        const bundleId = match[1];
        resolve(getBrowserPathFromBundleId(bundleId));
      } else {
        resolve(null);
      }
    });
  } else {
    resolve(null);
  }
});

const getBrowserPathFromProgId = (progId: string): string | null => {
  switch (progId) {
    case 'ChromeHTML':
      return 'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe';
    case 'FirefoxURL-308046B0AF4A39CB':
      return 'C:\\Program Files (x86)\\Mozilla Firefox\\firefox.exe';
    case 'MSEdgeHTM':
      return 'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe';
    default:
      return null;
  }
};

const getBrowserPathFromBundleId = (bundleId: string): string | null => {
  switch (bundleId) {
    case 'com.google.Chrome':
      return '/Applications/Google Chrome.app';
    case 'org.mozilla.firefox':
      return '/Applications/Firefox.app';
    case 'com.microsoft.edgemac':
      return '/Applications/Microsoft Edge.app';
    default:
      return null;
  }
};
const openLinkWithBrowser = (link: string, browserPath: string) => {
  const fs = LynkerSDK.fs;
  if (fs.existsSync(browserPath)) {

    try {
      const childProcess = LynkerSDK.child_process;
      childProcess.execFile(browserPath, [link], (error: any) => {
        if (error) {
          console.log(`111${browserPath}:`, error);
        } else {
          console.log(`222${browserPath}`);
        }
      });
    } catch (error) {
      console.log(`333 ${browserPath}:`, error);
    }
  } else {
    console.log(browserPath);
  }
};
const openWithFallbackBrowsers = (link: string) => {
  const browsers = [
    { name: 'Edge', path: process.platform === 'win32' ? 'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe' : '/Applications/Microsoft Edge.app' },
    { name: 'Chrome', path: process.platform === 'win32' ? 'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe' : '/Applications/Google Chrome.app' },
    { name: 'Firefox', path: process.platform === 'win32' ? 'C:\\Program Files (x86)\\Mozilla Firefox\\firefox.exe' : '/Applications/Firefox.app' }
  ];
  const fs = LynkerSDK.fs;
  const childProcess = LynkerSDK.child_process;
  for (const browser of browsers) {
    if (fs.existsSync(browser.path)) {
      try {

        childProcess.execFile(browser.path, [link], (error: Error) => {
          if (error) {
            console.error('linklink', browser.name, error);
          }
        });
        return;
      } catch (error) {
        console.error('linklink11', browser.name, error);
      }
    } else {
      console.error('linklink22', browser.path);
    }
  }

  dialog.showMessageBox({
    type: 'info',
    title: '浏览器选择',
    message: '无法找到可用的浏览器，请手动打开链接。',
    buttons: ['确定']
  }).then(() => {
    console.log('用户确认');
  });
};
export const jumpWeb = async (
  path?: string,
  options: object = {},
  app = "org-web"
) => {
  const params = {
    ...getRefreshCode(),
    refer: "WEB",
  };
  if (!path) {
    path = "/#/";
  }
  const data = {
    token: getAccesstoken(),
    openid: getOpenid(),
  };
  if (data) {
    const query = {
      ...options,
      ...data,
    };
    console.log(query, 'queryqueryquery');

    const link = path
      ? `${getBaseUrl(app)}${path}?${qs.stringify(query)}`
      : `${getBaseUrl(app)}?${qs.stringify(query)}`;

    console.log(link);

    try {
      // 尝试使用默认浏览器打开链接
      console.log(link, 'linklinklink');

      await shell.openExternal(link);
    } catch (error) {
      console.log(error, '这里啊');

      console.error('1111111111Failed to open link with default browser:', error);

      // 检测默认浏览器
      detectDefaultBrowser().then((browserPath) => {
        console.log(browserPath, 'browserPathbrowserPathbrowserPath');

        if (browserPath) {
          // 使用检测到的默认浏览器打开链接
          openLinkWithBrowser(link, browserPath);
        } else {
          // 尝试使用备用浏览器打开链接
          openWithFallbackBrowsers(link);
        }
      });
    }
  }
};
export const jumpWebWithLink = async (link: string, teamId = "") => {
  const params = {
    ...getRefreshCode(),
    teamId,
    refer: "WEB",
  };
  const data = {
    token: getAccesstoken(),
    openid: getOpenid(),
    teamId
  };
  const url = new URL(link);
  const searchParams = new URLSearchParams(url.searchParams || {});
  for (const key of Object.keys(data)) {
    if (key === "refresh_token") {
      searchParams.set(
        key,
        new URLSearchParams(data[key] || {}).toString() || ""
      );
    } else {
      searchParams.set(key, data[key] || "");
    }
  }
  if (url.hash.length) {
    url.hash = `${url.hash}/?${searchParams.toString()}`;
  } else {
    url.hash = `/?${searchParams.toString()}`;
  }
  url.search = "";
  shell.openExternal(url.href);
};

export const jumpH5WithLink = async (link: string, teamId = "") => {
  const lang = getServerLang();
  const token = getAccesstoken();
  const profile = getProfilesInfoString();

  ipcRenderer.invoke("open-internal-window", {
    url: link,
    config: { lang, token, profile },
  });
};
