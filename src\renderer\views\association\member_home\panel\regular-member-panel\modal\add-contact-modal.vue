<template>
  <t-drawer v-model:visible="visible" :header="autoHeaderText" size="472px">
    <template #header>
      <div class="header">
        <div class="header-title">{{ autoHeaderText }}</div>
        <div class="header-close" @click.stop="onClose">
          <iconpark-icon name="iconerror" class="iconerror"></iconpark-icon>
        </div>
      </div>
    </template>
    <div class="toBody11">
      <!-- <t-alert theme="info">
					<template #message>
						<span>批量调整后原来的角色都会被替换为新的角色 </span>
					</template>
				</t-alert> -->
      <div class="form">

        <t-form
          ref="form"
          :data="formData"
          label-align="top"
          layout="vertical"
          label-width="120px"
          :rules="rules"
        >
          <t-form-item :label="'姓名'" name="name">
            <t-input
              v-model="formData.name"
              :maxlength="50"
              :placeholder="'请输入'"
              />
              <!-- :disabled="!!type" -->
          </t-form-item>
          <t-form-item :label="'手机号码'" name="telephone">
            <t-input-adornment style="width: 100%" :disabled="!!type">
              <template #prepend>
                <div>
                  <area-code v-model="formData.telCode" :disabled="!!type" />
                </div>
              </template>
              <t-input
                v-model="formData.telephone"
                placeholder="请输入手机号"
                :disabled="!!type"
              />
            </t-input-adornment>
          </t-form-item>
          <t-form-item label="邮箱" name="email">
            <!-- :disabled="!!type" -->
            <t-auto-complete
              v-model="formData.email"
              placeholder='请输入邮箱'
              :options="emailOptions"
              class="w-full"
              filterable
            />
          </t-form-item>
          <t-form-item :label="'所在单位岗位'" name="job">
            <t-input
              v-model="formData.job"
              :maxlength="50"
              :placeholder="'请输入'"
            />
          </t-form-item>
          <CImageUploadComp
              :attrs="attachImage"
              :multiple="false"
            />
        </t-form>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >取消</t-button>
        <t-button theme="primary" :loading="loading" @click="onSave">保存</t-button>
      </div>
    </template>
  </t-drawer>
  <!-- <t-dialog
    v-model:visible="visible"
    :header="autoHeaderText"
    class="createUpdate"
    :z-index="2500"
    attach="body"
    width="528px"
  >
    <template #body>
      <div class="toBody11">

        <div class="form">
          <t-form
            ref="form"
            :data="formData"
            label-align="right"
            layout="vertical"
            label-width="120px"
            :rules="rules"
          >
            <t-form-item :label="'姓名'" name="name">
              <t-input
                v-model="formData.name"
                :maxlength="50"
                :placeholder="'请输入'"
                :disabled="type"
              />
            </t-form-item>
            <t-form-item :label="'手机号码'" name="telephone">
              <t-input-adornment style="width: 100%" :disabled="type">
                <template #prepend>
                  <div>
                    <area-code v-model="formData.telCode" :disabled="type" />
                  </div>
                </template>
                <t-input
                  v-model="formData.telephone"
                  placeholder="请输入手机号"
                  :disabled="type"
                />
              </t-input-adornment>
            </t-form-item>
            <t-form-item label="邮箱" name="email">
              <t-auto-complete
                v-model="formData.email"
                :disabled="type"
                :placeholder="'请输入邮箱'"
                :options="emailOptions"
                class="w-full"
                filterable
              />
            </t-form-item>
            <t-form-item :label="'所在单位岗位'" name="job">
              <t-input
                v-model="formData.job"
                :maxlength="50"
                :placeholder="'请输入'"
              />
            </t-form-item>
          </t-form>
        </div>
      </div>
    </template>
    <template #closeBtn>

      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >取消</t-button>
        <t-button theme="primary" @click="onSave">确定</t-button>
      </div>
    </template>
  </t-dialog> -->
  <tip
    v-model:visible="tipVisible"
    :tip="checkPhoneTip"
    :btn-confirm="'确定变更'"
    :btn-cancel="$t('account.cancel')"
    @onconfirm="changeRegion"
  />
</template>

<script lang="ts" setup>
/**
 * @description 批量调整角色弹层
 * <AUTHOR>
 */
import { debounce } from "lodash";

import { ref, reactive, Ref, computed, toRaw } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import {
  createMemberContactAxios,
  createMemberOrderAxios,
  getRegularListAxios,
  patchContactorAxios,
  patchMemberOrderAxios
} from "@renderer/api/association/api/businessApi";
import { getResponseResult, priceRecovery } from "@renderer/utils/myUtils";
import areaCode from "@renderer/components/account/AreaCode.vue";
import tip from "@renderer/views/setting/dialog/tip.vue";
import CImageUploadComp from "@/components/free-from/runtime/components/AvatarImageUpload.vue";


import dayjs from "dayjs";
import { checkPhoneAndMatch } from "@renderer/components/account/util";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { getAssociationTeamID } from "@renderer/views/association/utils/auth";
import { platform as platformCon} from "@renderer/views/digital-platform/utils/constant";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const emailSuffix = ["@qq.com", "@163.com", "@gmail.com", "@126.com"];
const emailOptions = computed(() => {
  const emailPrefix = formData.email.split("@")[0];
  if (!emailPrefix) return [];

  return emailSuffix.map((suffix) => emailPrefix + suffix);
});

const attachImage = reactive({
  required: false,
  name: "照片",
  editable: true,
  max: 1,
  aspectRatio: 0.7142,

  value: []
});

const props = defineProps({
  memberId: {
    type: Number,
    default: 0
  },
  isMember: {
    type: Number,
    default: 1
  },
  platform: {
    type: String,
    default: '',
  },
});
const type = ref(0); // 0创建 1编辑

const form = ref(null);

const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})


const currentTeamId = computed(() => {
  if (platformCpt.value === platformCon.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } if (platformCpt.value === platformCon.digitalWorkbench) {
    return route.query?.teamId || 0;
  }
    return getAssociationTeamID();

});


const rules = {
  name: [
    {
      required: true,
      message: "请输入",
      type: "error",
      trigger: "blur"
    }
  ],
  job: [
    {
      required: false,
      message: "请输入",
      type: "error",
      trigger: "blur"
    }
  ],
  telephone: [
    {
      required: true,
      message: "请输入",
      type: "error",
      trigger: "blur"
    }
  ],
  email: [
    { email: { ignore_max_length: true }, message: t("identity.inputTipEmail") },
  ],
  // remark: [
  //   {
  //     required: false,
  //     message: "请输入",
  //     type: "error",
  //     trigger: "blur"
  //   }
  // ]
};
const visible = ref(false);

let formData = reactive({
  name: "", // 缴费金额，单位：元
  telephone: "",
  telCode: "86",
  job: "",
  email: "",
  photo: "",
  id: 0
});

const initForm = () => {
  formData = Object.assign(formData, {
    name: "",
    telephone: "",
    telCode: "86",
    job: "",
    email: "",
    photo: "",
  });
};

const autoHeaderText = computed(() =>
  (type.value ? "编辑代表人" : "添加代表人"));

// const props = defineProps({
//   levelOptions: {
//     // 会员职务列表
//     type: Array,
//     default: () => []
//   } // 0创建 1编辑
// });

const emits = defineEmits(["reload"]);
const loading= ref(false);
const onSave = debounce(() => {
  form.value
    .validate({ showErrorMessage: true })
    .then(async (validateResult) => {
      if (validateResult && Object.keys(validateResult).length) {
        console.log(formData);
      } else {
        if (!checkPhone()) return;
        let params: any = {};

        // params.money = priceRecovery(params.money);
        let res = null;
        try {
          loading.value = true;
          if (!type.value) {
            params = {
              ...toRaw(formData),
              association_id: props.memberId,
              is_member: props.isMember
            };
            if(attachImage.value.length > 0) {
              params.photo = attachImage.value[0].file_name;
            }
            delete params.id;
            res = await createMemberContactAxios(params, currentTeamId.value);
          } else {
            params = {
              name:formData.name,
              email:formData.email,
              job: formData.job

            };
            if(attachImage.value.length > 0) {
              params.photo = attachImage.value[0].file_name;
            }
            res = await patchContactorAxios(formData.id, params, currentTeamId.value);
          }
          res = getResponseResult(res);
          loading.value = false;

          if (!res) return;
          if (type.value) {
            MessagePlugin.success("操作成功");
          } else {
            MessagePlugin.success(
              "邀请发送成功，联系人登录同意后即可加入商协会"
            );
          }
          setTimeout(() => {
            onClose();
            emits("reload");
          }, 500);
        } catch (error) {
          console.log(error);
          const errMsg = error instanceof Error ? error.message : error;
          MessagePlugin.error(errMsg);
        }
        loading.value = false;

      }
    });
}, 500);

const tipVisible = ref(false);
const checkPhoneTip = ref("");
let checkRegion = 0;
const changeRegion = () => {
  tipVisible.value = false;
  formData.telCode = checkRegion.toString();
  // formData.value.code ? joinTeam() : getCode();
};

// 校验手机号的合法性
const checkPhone = () => {
  checkRegion = checkPhoneAndMatch(+formData.telCode, formData.telephone);
  if (!checkRegion) {
    MessagePlugin.error({
      content: "请填写正确的手机号",
      duration: 3000
    });
    return false;
  }
  if (checkRegion !== +formData.telCode) {
    (checkPhoneTip.value = `检测到你输入的手机号对应的国际区号为“+${checkRegion}”，是否为你自动变更区号`),
      (tipVisible.value = true);
    return false;
  }
  return true;
};




/**
 *
 * @param data 值不为空说明为编辑状态
 */
// const edit_data = ref(null);
const onOpen = (data?: any) => {
  if (data) {
    // edit_data.value = data;
    type.value = 1;

    initForm();
    formData.name = data.name;
    formData.email = data.email;
    formData.job = data.job;
    formData.telCode = data.telcode;
    formData.telephone = data.telephone;
    // formData.money = Number(data.money / 100);
    formData.id = data.id;
    formData.photo = data.photo;
    if(data.photo) {
      const origin_name = data.photo
          ? data.photo.substring(
            data.photo.lastIndexOf("/") + 1
            )
          : "";
      attachImage.value = [
        {
          file_name: data.photo,
          file_name_short: origin_name,
          original_name: origin_name
        }
      ];
    }

  } else {
    type.value = 0;
    // form.value.reset();
    initForm();
  }
  visible.value = true;
};

const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";
.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  &-title {
    color: var(--kyy_color_modal_title, #1a2139);

    /* kyy_fontSize_3/bold */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
  &-close {
    .iconerror {
      font-size: 24px;
      color: #516082;
    }
  }
}

:deep(.t-form__item) {
  margin-right: 0;
  margin-bottom: 20px !important;
}

:deep(.t-form__label) {
  white-space: wrap !important;
}
:deep(.t-input-adornment) {
  width: 100%;
}
:deep(.t-input--auto-width) {
  width: 90px;
}
.searchForm {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  // gap: 24px;
  &-item {
    min-width: 324px;
  }
}
.t-alert--info {
  padding: 8px 16px;
}
.form {
  margin-top: 14px;
}
:deep(.t-input-adornment__prepend) {
  background: #fff;
}
// :deep(.t-dialog__body) {
//   overflow: hidden !important;
//   padding-bottom: 0;
// }

.inputFlex {
  display: flex;
  flex-direction: column;
  width: 100%;
  .tips {
    font-size: 14px;

    font-weight: 400;
    color: #717376;
  }
}
.toBody {
  //   max-height: 70vh;
  //   overflow: auto;
  padding: 0 24px;
}

:deep(.t-form__item) {
  margin-bottom: 10px;
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}

// :deep(.t-dialog--default) {
//   padding: 0 !important;
// }
</style>
<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";

.createUpdate {
  .toBody {
    height: 60vh;
    overflow: auto;
  }
  .t-dialog__header {
    padding: 0 24px;
  }
  .t-dialog__footer {
    padding: 0 24px;
  }

  .t-dialog--default {
    padding-left: 0;
    padding-right: 0;
  }
}

.setWidth {
}
</style>
