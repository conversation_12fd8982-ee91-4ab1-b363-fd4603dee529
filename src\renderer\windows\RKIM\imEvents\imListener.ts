/*
 * @Author: ZHANGXIAO
 * @Date: 2024-05-23 11:44:36
 * @LastEditors: ZHANGXIAO
 * @LastEditTime: 2024-07-25 09:47:53
 * @FilePath: \lynker-desktop\src\renderer\windows\RKIM\imEvents\imListener.ts
 * @Description:openIm回调监听
 */
import { CbEvents } from "@rk/im-sdk";
import { logHandler } from "@renderer/log";
import {
  getOpenid
 } from '@renderer/utils/auth';
import {
  WSEvent,
  ReceiptInfo,
  MessageItem
} from "@rk/im-sdk/dist/types/entity";
import { msgToUseChat } from '@renderer/views/message/service/utils';
import { showDialog } from "@renderer/utils/DialogBV";
 import { getProfile } from "@/api/account";
import { IMSDK } from './imSetup';
import { imLogin, imDisconnect } from './invokeImEvents';
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, shell } = LynkerSDK;
export const setIMListener = () => {
  IMSDK.on(CbEvents.OnConnecting, () => {
    // 连接中
    console.log("connecting...", Date.now());
    const args = JSON.stringify({ eventName: 'IMCONNECT', eventArgs: ['connecting'] });
    ipcRenderer.invoke('im.bridge.events', args);
    logHandler({ name: 'im连接中', info: `connecting...;`, desc: `${new Date()}; imListener->connecting` });

  });
  IMSDK.on(CbEvents.OnConnectSuccess, () => {
    // 连接成功
    console.log("OnConnectSuccess...", Date.now());
    const args = JSON.stringify({ eventName: 'IMCONNECT', eventArgs: ['connected'] });
    ipcRenderer.invoke('im.bridge.events', args);
    logHandler({ name: '连接成功', info: `OnConnectSuccess...;`, desc: `${new Date()}; imListener->OnConnectSuccess` });

  });

  IMSDK.on(CbEvents.OnConnectFailed, async (err) => {
    // 连接失败
    console.error("OnConnectFailed...", Date.now(), err);
    const args = JSON.stringify({ eventName: 'IMCONNECT', eventArgs: ['disconnected'] });
    ipcRenderer.invoke('im.bridge.events', args);
    // // 检测登录状态，重新登录
    // imLogin();
    const netWork = navigator.onLine;
    logHandler({ name: 'im断开连接', info: `OnConnectFailed; netWork1:${netWork}; err:${JSON.stringify(err)}`, desc: `${new Date()}; imListener->OnConnectFailed` });

  });
  IMSDK.on(CbEvents.OnUserTokenExpired, (err) => {
    // token无效
    console.error("====>OnUserTokenExpired...", err);
    // 打回登陆页面
    // showDialog('dialogLogout', { tip: i18nt('im.public.auth_expired') });
    showDialog('dialogKickOut');
    logHandler({ name: 'IMToken失效on，dialogLogout', info: `OnUserTokenExpired; err:${JSON.stringify(err)}`, desc: `${new Date()}; imListener->OnUserTokenExpired` });
    const args = JSON.stringify({ eventName: 'IMCONNECT', eventArgs: ['tokenExpired'] });
    ipcRenderer.invoke('im.bridge.events', args);
  });
  // 消息发送状态回执
  IMSDK.on(CbEvents.OnMsgSend, onMsgSend);
  // message新消息列表
  IMSDK.on(CbEvents.OnRecvNewMessages, newMessageHandler);
  // message离线消息列表
  IMSDK.on(CbEvents.OnRecvOfflineNewMessages, recvOfflineNewMessages);
  // 收到已读回执
  IMSDK.on(CbEvents.OnRecvC2CReadReceipt, recvC2CReadReceipt);
  IMSDK.on(CbEvents.OnRecvGroupReadReceipt, recvGroupReadReceipt);
  // 消息撤回
  IMSDK.on(CbEvents.OnNewRecvMessageRevoked, revokedRecvMessage);
  // conversation
  IMSDK.on(CbEvents.OnConversationChanged, conversationChangeHandler);
  IMSDK.on(CbEvents.OnNewConversation, conversationChangeHandler);
  IMSDK.on(CbEvents.OnSyncServerFinish, onSyncServerFinish);
  // 互踢通知
  IMSDK.on(CbEvents.OnKickedOffline, onKickedOffline);
};

/**
 * 发送消息后sdk消息回执。到UI层更新消息发送状态
 * @param data MessageItem
 */
const onMsgSend = (data:MessageItem) => {
  console.log(`====>onMsgSend`, data);
  const args = JSON.stringify({ eventName: CbEvents.OnMsgSend, eventArgs: [{ msg: data.data }] });
  ipcRenderer.invoke('im.bridge.events', args);
  const logInfo = data.data;
  delete logInfo.ex;
  delete logInfo.attachedInfoElem;
  logHandler({ name: 'onMsgSend', info: `args:${JSON.stringify(logInfo)};`, desc: `sendID:${data?.data?.sendID}; ${new Date()}; 消息发送状态回执` });
};

/**
 * 收到消息或通知 分发需要处理的消息到UI层
 * @param msg
 */
const newMessageHandler = (msg:MessageItem) => {
  const { event, errCode, errMsg, data }= msg;
  console.log(`====>收到消息`, msg);
  const messages = data.filter((item) => {
    // 过滤掉不是发给自己的通知
    if (item.sessionType === 4 && !item.recvID.includes(getOpenid())) { return false; }
    const msgItem = msgToUseChat(item, false, 'newMsg');
    if (!msgItem) return;
    const extraObj = getMsgExtra(msgItem.content.extra) as MessageToSave['contentExtra'];
    if (extraObj && ['meeting', 'APP_MEETING_INVITE'].includes(extraObj.contentType)) {
      const message = item as MessageToSave;
      message.contentExtra = extraObj;
      // if (extraObj.contentType === 'meeting') {
      //   useMeetingManageStore().onSignalingMessage(message);
      // } else {
      //   useMeetingManageStore().onMeetingSyncMessage(message);
      // }
      if (extraObj.data?.meetingType === 'group') {
        return false;
      }
    }
    return true;
  });
  if (messages.length) {
    const args = JSON.stringify({ eventName: 'MESSAGES', eventArgs: [{ messages }] });
    ipcRenderer.invoke('im.bridge.events', args);
  }
  logHandler({ name: 'newMessageHandler', info: `args:${JSON.stringify(msg)}`, desc: `sendID:${data[0]?.sendID}; ${new Date()}; 收到消息` });

};

/**
 * 会话变更和收到新会话。只处理sg_和si_开头的会话。
 * @param data
 */
const conversationChangeHandler = ({ data }: WSEvent<[{conversationID:string}]>) => {
  console.log('=====>conversationChangeHandler', data);
  if (!data.length) return;
  const list = data.filter((item) => (item.conversationID.startsWith("sg_") || item.conversationID.startsWith("si_") || item.conversationID.startsWith("sa_")));
  if (!list.length) return;
  const args = JSON.stringify({ eventName: 'OnConversationChanged', eventArgs: [{ data: list }] });
  ipcRenderer.invoke('im.bridge.events', args);
  // logHandler({ name: 'OnConversationChanged', info: `args:${JSON.stringify(data)}`, desc: ` ${new Date()}; 收到会话变更` });
};

/**
 * 收到互踢通知。im断开连接，退出登录，弹出被踢出提示框。
 * @param data
 */
const onKickedOffline = (data) => {
  console.log('=====>onKickedOffline', data, new Date());
  // 收到踢出通知，先不校验业务token是否失效，直接踢出。
  imDisconnect();
  showDialog('dialogKickOut');
  logHandler({ name: '账号退出踢出imOnKickedOffline', info: `${JSON.stringify(data)}`, desc: `${new Date()}; onKickedOffline->dialogKickOut` });
  getProfile().then(res => {
    console.log('====>res', res);
    // imLogin({ action: '', data: { from: 'onKickedOffline' } });
  }).catch(res => {
    console.error('====>res', res);
    logHandler({ name: '账号退出踢出imOnKickedOffline-token失效', info: `${JSON.stringify(res)}`, desc: `${new Date()}; onKickedOffline->dialogKickOut` });
    // if (res.response?.status === 403 || res.response?.status === 401) {
    //   imDisconnect();
    //   showDialog('dialogKickOut');
    // }
  });
};

/**
 * sdk同步完成发出，用于初始登录拉取最新数据，只拉取第一次。
 * @param data 无用
 */
const onSyncServerFinish = ({ data }: WSEvent) => {
  console.log('=====>onSyncServerFinish', data);
  const args = JSON.stringify({ eventName: 'OnSyncServerFinish', eventArgs: [{ data: 'onSyncServerFinish-imListener' }] });
  ipcRenderer.invoke('im.bridge.events', args);
};

const recvOfflineNewMessages = ({ data }: WSEvent) => {
  console.log('=====>recvOfflineNewMessages', data);
};
/**
 * 收到单聊已读回执，单聊需要更新会话摘要和当前聊天界面消息已读状态。
 * @param data
 */
const recvC2CReadReceipt = ({ data }: WSEvent<ReceiptInfo[]>) => {
  console.log('=====>recvC2CReadReceipt', data);
  if (data.length) {
    const msgIDs = data.map((val) => val.msgIDList).flat(2);
    const args = JSON.stringify({ eventName: 'OnRecvC2CReadReceipt', eventArgs: [{ msgIDs, readTime: data[0].readTime, conversationID: data[0].conversationID }] });
    ipcRenderer.invoke('im.bridge.events', args);
  }
};

/**
 * 收到群聊已读回执，群聊更新当前聊天界面消息已读数量和列表。
 * @param data
 */
const recvGroupReadReceipt = ({ data }: WSEvent<ReceiptInfo[]>) => {
  console.log('=====>recvGroupReadReceipt', data);
  if (data.length) {
    const args = JSON.stringify({ eventName: 'OnRecvGroupReadReceipt', eventArgs: data });
    ipcRenderer.invoke('im.bridge.events', args);
  }
};

/**
 * 收到消息撤回通知，更新会话摘要和当前聊天界面消息撤回状态。
 * @param data
 */
const revokedRecvMessage = ({ data }: WSEvent) => {
  const args = JSON.stringify({ eventName: 'OnNewRecvMessageRevoked', eventArgs: [data] });
  ipcRenderer.invoke('im.bridge.events', args);
  logHandler({ name: '收到撤回消息', info: `${JSON.stringify(args)}`, desc: `${new Date()}; revokedRecvMessage` });
};

const getMsgExtra = (extra: string) => {
  const contentExtra = getMessageExtraObject(extra);
  if (contentExtra && 'contentType' in contentExtra) {
      return contentExtra;
  }
  // 通知消息字段兼容处理
  if (contentExtra && 'content' in contentExtra && 'type' in contentExtra) {
      contentExtra.data = getMessageExtraObject(contentExtra.content);
      contentExtra.contentType = contentExtra.type;
      delete contentExtra.content;
      delete contentExtra.type;
  }
  return contentExtra;
};
const getMessageExtraObject = (extra: string) => {
  try {
    const extraObj = JSON.parse(extra);
    return extraObj;
  } catch (err) {

  }
  return undefined;
};
