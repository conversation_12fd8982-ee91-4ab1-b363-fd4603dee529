<template>
    <div v-if="!isLoading && showBrithdayDetail">
      <div class="tool-header">
        <div class="header-left">
          <t-input
            class="input"
            v-model="queryText"
            :placeholder="t('im.birth.searchMember')"
            clearable
            >
              <template #prefix-icon>
                <i class="i-svg:im-history text-20 color-text-3" />
              </template>
          </t-input>
        </div>
        <div class="right-icon hover-click-class-1" @click="openSetting" v-if="isAdmin">
          <iconpark-icon name="iconsetUp" class="iconset"></iconpark-icon>
        </div>
      </div>
      <div class="tool-title">
        {{ `${t('im.birth.group', [members.length])}` }}
      </div>
      <div class="list">
        <template v-for="item in displayMembers">
          <div class="list-item" @click="showMemberBirthSetting(item)">
              <ChatAvatar :size="44" :src="item.attachments?.avatar"
                  :alt="item?.attachments?.nickname || item?.attachments?.staffName || ''" />
              <div class="border-bottom">
                  <div class="member-info">
                      <div class="member-name">
                          {{ item?.attachments?.nickname || item?.attachments?.staffName || '' }}
                      </div>
                      <div v-if="item.born_at" class="member-birth ">
                          {{ item.lunar ? `${getBirthLunarText(item.born_at)} ${getBirthText(item.born_at, true)}` : getBirthText(item.born_at) }}
                      </div>
                      <div v-else class="member-birth-notSet">
                          {{ t('im.birth.notSet') }}
                      </div>
                  </div>
                  <svg class="arrow" v-show="showArrow(item)">
                      <use href="#iconarrowright"></use>
                  </svg>
              </div>
          </div>
          <div class="line-bottom"></div>
        </template>
      </div>
    </div>

    <div v-else-if="!isLoading" class="brithday-entry">
      <img class="entry_image" src="@/assets/brithday_bg.svg" alt="">
      <div class="btn" @click="openSetting"></div>
    </div>

    <BrithdaySetting
      v-if="brithdaySettingVisible"
      v-model:visible="brithdaySettingVisible"
      :myCard="myCard"
      :members="members"
      :styleVal="styleVal"
      @confirmSetting="confirmSetting"
    />

    <!-- 生日设置 -->
    <t-dialog  attach="body" v-model:visible="settingVisible" :header="t('im.birth.setting1')"
      :confirm-btn="{ content: t('im.public.confirm'), disabled: !changedDate && !settingMember?.born_at }"
      @confirm="onConfirm"
      @close="onCancel">
      <template #body>
          <div v-if="settingMember">
              <div class="member-setting">
                  <ChatAvatar :size="44" :src="settingMember.attachments?.avatar"
                      :alt="settingMember?.attachments?.nickname || settingMember?.attachments?.staffName || ''" />
                  {{ settingMember?.attachments?.nickname || settingMember?.attachments?.staffName || '' }}
              </div>
              <DatePicker :value="settingMember.born_at*1000" :is-lunar="settingMember.lunar" @confirm="onDateConfirm" @change="onDateConfirm" />
          </div>
      </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { DialogPlugin, SwitchProps } from 'tdesign-vue-next'
import ChatAvatar from '../../components/ChatAvatar.vue'
import DatePicker from '../../components/DatePciker.vue'
import moment from 'moment';
import { getBirthText, getBirthLunarText } from '@/utils/lunar';
import BrithdaySetting from './BrithdaySetting.vue';
import type { IGroupMember } from '@renderer/api/im/model/relation';
import SvgIcon from "@/components/SvgIcon.vue";
import { storeToRefs } from "pinia";
import { useImToolStore } from "@renderer/views/message/tool/service/tool";
import { SceneTool } from "@renderer/views/message/tool/service/type";

import { useMessageStore } from '../../service/store';
import { useGroupTools } from '../../service/extend/chatTools'
import { msgEmit } from '../../service/msgEmit';
type BirthCardStyle = 'ONE' | 'TWO' | 'THREE';
const { t } = useI18n();
const msgStore = useMessageStore();
const { info, myCard, members, loadData, loadMembers, toggleBirthDaySetting, changeBirthCardStyle, changeBirthDay, changeBirthData } = useGroupTools();
const toolStore = useImToolStore();
const { sceneType } = storeToRefs(toolStore);


const switchVal = ref(false);

const styleVal = ref<BirthCardStyle>('ONE');


const isLoading = ref(true);
const brithdaySettingVisible = ref(false);
const showBrithdayDetail = ref(false);
const queryText = ref('');
const settingVisible = ref(false);
const settingMember = ref<IGroupMember>(null);

const showToggleTips = (isOpen: boolean) => {
  const birthAlert = DialogPlugin.confirm({
      theme: 'info',
      header: t('im.public.tips'),
      body: isOpen ? t('im.birth.warning1') : t('im.birth.warning2'),
      cancelBtn: null,
      confirmBtn: t('im.public.known'),
      onConfirm: () => {
          birthAlert.destroy();
      }
  })
}
const openSetting = () => {
  if (!isAdmin.value) {
    showToggleTips(true);
  } else {
    brithdaySettingVisible.value = true;
  }
};
const confirmSetting = (val) => {
  showBrithdayDetail.value = val?.switchVal;
  if (!val?.switchVal && !switchVal.value) return;
  changeBirthData(val);
};
const displayMembers = computed(() => {
    if (!queryText.value) {
        return members.value?.sort((a, b) => {
          if (a?.card?.card_id === msgStore.chatingSession.myCardId) {
            return -1;
          } else if (b?.card?.card_id === msgStore.chatingSession.myCardId) {
            return 1;
          }
          return (a.joined && b.joined) ? a.joined - b.joined : 0;
        });

    } else {
        return members.value.filter(item => {
            const name = item.attachments?.nickname || item.attachments?.staffName || '';
            return name.includes(queryText.value);
        } )?.sort((a, b) => {
          if (a?.card?.card_id === msgStore.chatingSession.myCardId) {
            return -1;
          } else if (b?.card?.card_id === msgStore.chatingSession.myCardId) {
            return 1;
          }
          return (a.joined && b.joined) ? a.joined - b.joined : 0;
        });
    }
})
const showArrow = (item) => {
  return item.card?.card_id === msgStore.chatingSession.myCardId || isAdmin.value
}
const showMemberBirthSetting = (member: IGroupMember) => {
    if (!showArrow(member)) return;
    settingVisible.value = true;
    settingMember.value = member;
}
const changedDate = ref<{timeValue: number, isLunarMode: boolean}>();
const onDateConfirm = (item: { timeValue: number, isLunarMode: boolean}) => {
    changedDate.value = item;
}

const onConfirm = () => {
    settingVisible.value = false;
    const birthTime = Math.floor(changedDate.value.timeValue/1000)
    onMemberBirthChange({ member: settingMember.value, birthTime, isLunar: changedDate.value.isLunarMode })
    changedDate.value = null;
    settingMember.value = null;
}

const onCancel = () => {
  settingVisible.value = false;
  settingMember.value = null;
  changedDate.value = null;
}

onMounted(() => {
    if (msgStore.chattingGroup) {
        loadBirthData();
    }
    // 监听群信息变化
    msgEmit.on('group-change', (data) => {
        console.log('group-change', data);
        const { groupId } = data as { groupId: string };
        if (groupId === msgStore.chatingSession.targetId) {
            loadBirthData();
            console.log('loadBirthData');
        }
    });
})

onBeforeUnmount(() => {
    msgEmit.off('group-change', loadBirthData);
})

const loadBirthData = () => {
    loadData(msgStore.chatingSession.targetId, msgStore.chatingSession.myCardId)
        .then(() => {
            switchVal.value = Boolean(info.value?.birthday_notify);
            styleVal.value = (info.value.birthday_style || 'ONE') as BirthCardStyle;
            showBrithdayDetail.value = Boolean(info.value?.birthday_notify);
        }).finally(() => {
          isLoading.value = false;
        })
    loadMembers(msgStore.chatingSession.targetId);
}


const isAdmin = computed(() => myCard.value?.roles?.some(r => r === 'ADMIN' || r === 'OWNER'));


const onMemberBirthChange = (params) => {
    changeBirthDay(params.member, params.birthTime, params.isLunar);
}


// watch(() => msgStore.chatingSession, (val) => {
//   if (val) {
//     brithdaySettingVisible.value = false;
//   }
//   if (val && msgStore.chattingGroup && sceneType.value === SceneTool.birthday) {
//     loadBirthData();
//   }
// });
</script>

<style lang="less" scoped>
@import "../../style/tools.less";

.header {
    gap: 12px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    font-size: @font-size4;
    color: @text-kyy-color-text-1;
    padding: 12px 0;
    .group-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.setting {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    min-height: 48px;
}

.info {
    flex: 1;
    font-size: @font-size5;
    color: @text-kyy-color-text-1;
    line-height: 22px;
}

.info-desc {
    color: @text-kyy-color-text-3;
    margin-top: 2px;
}

.member-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 12px 0 ;
    border-top: 1px solid @gray-gray3;
}

.members {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 17px;
    overflow: hidden;
    margin-bottom: 12px;

    &>div:nth-child(n+5) {
        display: none;
    }
}

.name {
    width: 44px;
    max-width: 44px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: @warning-kyy-color-warning-default;
    background-color: @tag-bg-kyy-color-tag-bg-warning;
    padding: 0 8px;
    font-size: @font-size6;
    border-radius: 10px;
    text-align: center;
    line-height: 20px;
    margin-top: 2px;
}

.birth-row {
    border-top:  1px solid #eceff5;
    border-bottom:  1px solid #eceff5;
}

.birth-cards {
    display: flex;
    flex-direction: row;
    gap: 16px;
    margin-top: 12px;

    &>div {
        padding: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 4px;
    }

    .active {
        border-radius: 8px;
        box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
    }
}


.brithday-entry {
  position: relative;
  width: 376px;
  height: 100%;
  overflow: hidden;
  flex-shrink: 0;
  img {
    width: 100%;
  }
  .btn {
    position: absolute;
    top: 146px;
    left: 31px;
    width: 138px;
    height: 42px;
    cursor: pointer;
  }
}

.tool-header{
  padding:12px 16px;
  :deep(.t-input.t-input--prefix > .t-input__prefix){
    z-index: auto;
  }
  .header-left {
    flex: 1;
  }
  .right-icon {
    height: 32px;
    width: 32px;
    border: 1px solid #d5dbe4;
    border-radius: 4px;
    margin-left: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    &:hover {
      border-color: #707eff;
    }
  }
}

.tool-title{
  padding: 12px 16px 8px 16px;
  color: var(--text-kyy_color_text_2, #516082);

  /* kyy_fontSize_2/regular */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}

.list {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
}

.border-bottom{
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.line-bottom{
  margin-left: 72px;
  width: 285px;
  height: 1px;
  background: var(--divider-kyy_color_divider_light, #ECEFF5);
}

.list-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    margin: 4px 8px;
    padding: 8px;

    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
    }
}

.member-info {
    flex: 1;
    overflow: hidden;
    line-height: 22px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 2px;
}

.member-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #000;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}

.member-birth {
    height: 20px;
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--kyy_color_tag_text_warning, #FC7C14);
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_warning, #FFE5D1);
    padding: 2px 4px;
    font-size: 12px;
    font-weight: 400;
    width: fit-content;
}

.member-birth-notSet{
    height: 20px;
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--kyy_color_tag_text_gray, #516082);
    background: var(--kyy_color_tag_bg_gray, #ECEFF5);
    padding: 2px 4px;
    font-size: 12px;
    font-weight: 400;
    border-radius: var(--kyy_radius_tag_s, 4px);
    width: fit-content;
}

.member-setting {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 12px;
  margin-bottom: 12px;
}

</style>


<style>
.birth-style-dialog .t-dialog--default {
    padding: 24px 0;
}

.birth-style-dialog .t-dialog__header,
.birth-style-dialog .t-dialog__footer {
    padding: 0 24px;
}

.birth-style-dialog .t-dialog__body {
    padding: 24px;
}

</style>
