<template>
  <t-select
    :value="modelValue"
    :auto-width="showCode"
    :disabled="disabled"
    :placeholder="props.placeholder || t('contacts.pleaseInput')"
    @change="onChange"
    @popup-visible-change="visibleChange"
    :popupProps="{
      overlayInnerStyle: hideCa ? { width: '280px' } : Object.assign({
      }, props.popupWidth ? { width: props.popupWidth } : {}),
      overlayInnerClassName: props.popupClassName,
    }"
    class="area-code-select"
  >
    <template #panelTopContent>
      <div v-if="!props.isMO" style="padding: 6px 6px 0 6px">
        <t-input v-model="search" :disabled="disabled" :placeholder="t('account.search')" @change="searchList">
          <template #prefix-icon>
            <!-- <t-icon style="height: 2rem;color: #D5DBE4;" name="search" /> -->
            <!-- <SvgIcon name="im-history" class="svg-size20" /> -->
            <i class="i-svg:im-history text-20 color-text-3" />
          </template>
        </t-input>
      </div>
    </template>
    <template #suffixIcon>
      <iconpark-icon name="iconarrowdown" :style="`font-size: 20px;color: ${disabled ? '#D5DBE4' : '#828DA5'}`"></iconpark-icon>
    </template>
    <template v-if="visibleChange">
      <template v-if="props.hideCa">
        <t-option
          v-for="item in options2[0].children"
          :key="item.code"
          :value="showCode ? item.code : item.area"
          :label="showCode ? `+${item.code}` : item.name"
        >
          <div style="display: flex">
            <div v-if="showCode" style="width: 80px">{{ `+${item.code}` }}</div>
            <div>{{ item.name }}</div>
          </div>
        </t-option>
      </template>

      <template v-else>
        <t-option-group v-for="group in options2" :key="group.group" :label="group.group">
          <t-option
            v-for="item in group.children"
            :key="item.code"
            :value="showCode ? item.code : item.area"
            :label="showCode ? `+${item.code}` : item.name"
          >
            <div style="display: flex">
              <div v-if="showCode" style="width: 80px">{{ `+${item.code}` }}</div>
              <div>{{ item.name }}</div>
            </div>
          </t-option>
        </t-option-group>
      </template>
    </template>
  </t-select>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from "vue";
import { useI18n } from "vue-i18n";
import { getAreaCodes } from "@renderer/api/account";
import { setArea, getArea } from "@renderer/utils/auth";
import SvgIcon from '@/components/SvgIcon.vue';
import _ from "lodash";

const { t } = useI18n();
const props = defineProps({
  modelValue: {
    type: [Number, String, null],
    required: true,
  },
  showCode: {
    type: Boolean,
    default: true,
  },
  placeholder: {
    type: String,
    default: "请输入",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  isMO: {
    type: Boolean,
    default: false,
  },
  hideCa: {
    type: Boolean,
    default: false,
  },
  popupWidth: {
    type: String,
    default: '280px',
  },
  popupClassName: {
    type: String,
    default: '',
  },
});
const search = ref("");
const options = ref([]);
const defaultOptions = ref([]);

const options2 = computed(() => {
  if (options.value?.length > 0) {
    if (props.isMO) {
      const items = options.value[0];
      return [{
        ...items,
        children: items.children.filter(i => `${i.code}` !== '886')
      }];
    }
    return options.value;
  }
  return []
});

onMounted(() => {
  const areaList = getArea();
  if (areaList) {
    options.value = areaList;
    defaultOptions.value = _.cloneDeepWith(options.value);
  }
  getAreaCodes({}).then((res) => {
    console.log(res, "getAreaCodes");
    if (res.status === 200) {
      const hotArea = res.data.data.areaCodes.filter((v) => v.mark);
      options.value = [
        {
          group: t("account.hot"),
          children: hotArea,
        },
        {
          group: t("account.otherType"),
          children: res.data.data.areaCodes,
        },
      ];
      defaultOptions.value = _.cloneDeepWith(options.value);
      setArea(defaultOptions.value);
    }
  });
});

const searchList = _.debounce(
  (v) => {
    const hotList = defaultOptions.value[0].children.filter((area) => ~area.name.indexOf(v) || ~area.code.indexOf(v));
    const otherList = defaultOptions.value[1].children.filter((area) => ~area.name.indexOf(v) || ~area.code.indexOf(v));
    options.value = [
      {
        group: t("account.hot"),
        children: hotList,
      },
      {
        group: t("account.otherType"),
        children: otherList,
      },
    ];
  },
  500,
  {
    leading: true,
    trailing: true,
  },
);
const visibleChange = (v) => {
  search.value = "";
  options.value = _.cloneDeepWith(defaultOptions.value);
};

const emits = defineEmits(["update:modelValue", "change"]);

const onChange = (val) => {
  // emits("update:modelValue", val);
  nextTick(() => {
    emits("update:modelValue", val);
    emits("change", val);
  });
};
</script>

<style lang="less" scoped>
.area-code-select {
  max-height: 100px;
}
</style>
