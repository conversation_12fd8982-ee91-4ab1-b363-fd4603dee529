<template>
  <!-- loading -->
  <!-- v-lkloading="{ show: loading, height: false, opacity: true }" -->
  <div  class="head-box">
    <iconpark-icon name="iconSpecial-graphics" class="icon-specials" />

    <div v-show="isShowTabsScrollIcon" class="tabs-arrow" @click="onScrollTabs(1)">
      <iconpark-icon name="iconarrowlift" style="font-size: 20px"></iconpark-icon>
    </div>
    <div ref="tabsListRef" class="tabs-list">
      <div class="tabs-list--box" :style="{ transform: `translate3d(${tabsScrollLeft}px, 0, 0)` }">
        <div
          v-for="(item, index) in store.tabs"
          :key="index"
          :class="['tabs-item', { 'bgc-fff': store.activeIndex === index }]"
          :title="item.title"
          @click.stop="switchTab(item, index)"
        >
          <div class="topTab">
            <!-- {{ item.parentPath }}
            {{ route.path }} -->
            <!-- {{ item.fullPath }} -->
            <!-- <div v-if="route.path === item.fullPath" class="left-head-color" /> -->
            <template v-if="item.icon && /^http/.test(item.icon)">
              <span class="tabIcon" style="background-color: transparent;">
                <img :src="item.icon" class="tabImg" />
              </span>
            </template>
            <template v-else>
              <span class="tabIcon">
                <!-- <img class="tabImg" :src="TAB_ICON" alt="" /> -->
                <!-- <iconpark-icon
                    name="servicedefault"
                    class="servicedefault"
                  ></iconpark-icon> -->



                  <img v-if="item.icon === 'rich'" :src="business" class="tabImg" />
                  <img v-else-if="item.icon === 'name'" :src="directory" class="tabImg" />
                  <img v-else-if="item.icon === 'active'" :src="activity" class="tabImg" />
                  <img v-else-if="item.icon === 'document'" :src="document" class="tabImg" />
                  <!-- <img v-else-if="item.icon === 'notice'" :src="notice" class="tabImg" /> -->
                  <iconpark-icon v-else-if="item.icon === 'notice'" class="tabImg" name="systemservice"></iconpark-icon>
                  <img v-else-if="item.icon === 'square'" :src="square" class="tabImg" />
                  <img v-else-if="item.icon === 'fengcai'" :src="fengcai" class="tabImg" />
                  <img v-else-if="item.icon === 'digital'" :src="digital" class="tabImg" />
                  <img v-else-if="item.icon === 'association'" :src="association" class="tabImg" />
                  <img v-else-if="item.icon === 'government'" :src="government" class="tabImg" />
                  <img v-else-if="item.icon === 'pb'" :src="pb" class="tabImg" />
                  <img v-else-if="item.icon === 'cbd'" :src="cbd" class="tabImg" />
                  <img v-else-if="item.icon === 'circle'" :src="circle" class="tabImg" />
                  <img v-else-if="item.icon === 'album'" :src="photoAlbum" class="tabImg" />
                  <iconpark-icon v-else-if="item.icon === 'forum'" class="tabImg" name="iconforum" />
                  <iconpark-icon v-else-if="item.icon === 'icon-video'" class="tabImg" name="icon-video" />
                  <img v-else-if="item.icon === 'policy'" :src="policy" class="tabImg" />
                  <img v-else-if="item.icon === 'uni'" :src="school" class="tabImg" />
                  <img v-else src="@renderer/assets/member/svg/member.svg" class="tabImg" />
              </span>
            </template>

            <!-- <iconpark-icon class="iconTo" name="iconthumbnail" /> -->

            <span class="tab-title line-1">{{ item.title }}</span>
          </div>
          <!-- <img

            style="width: 12px; height: 12px; margin-left: 8px"
            src="@renderer/assets/img/<EMAIL>"
            @click.stop="removeTab(item)"
          /> -->
          <iconpark-icon
            v-if="item?.query?.isClose || (route.path !== item.path && !item?.affix)"
            name="iconerror"
            style="font-size: 20px; margin-left: 8px"
            class="iconClose"
            @click.stop="removeTab(item)"
          ></iconpark-icon>
        </div>
      </div>
    </div>
    <div v-show="isShowTabsScrollIcon" class="tabs-arrow" @click="onScrollTabs(-1)">
      <iconpark-icon name="iconarrowright" style="font-size: 20px"></iconpark-icon>
    </div>
    <span>
      <t-popup  placement="bottom-right" overlayClassName="square-account-list-popup">

        <div v-if="store.activeAccount" class="account">
          <!-- <img
              v-show="store.activeAccount.teamLogo"
              class="org-img"
              :src="store.activeAccount.teamLogo"
              alt=""
            />
            <img
              v-show="!store.activeAccount.teamLogo"
              class="org-img"
              src="@/assets/building-fill.png"
              alt=""
            /> -->
          <!-- :count="0" -->
          <span class="avatar">
            <kyy-avatar
              class="rd-10"
              :avatar-size="'24px'"
              :image-url="store.activeAccount.teamLogo || ORG_DEFAULT_AVATAR"
              :user-name="store.activeAccount?.teamFullName"
              :shape="'circle'"
            />
            <span class="iconDot" v-show="digitalPlatformHasRed">
            </span>
          </span>

          <div class="text">
            {{ store.activeAccount?.teamFullName }}
          </div>
          <!-- <img
              class="arrow"
              src="@renderer/assets/img/icon_arrow_down.svg"
              alt=""
            /> -->
          <!-- v-if="open_enabled && open_enabled.length > 1" -->
          <iconpark-icon  name="iconarrowdown" class="arrow" />
        </div>
        <template #content>
          <div class="bolist">
            <div class="account-wrap">
              <template  v-if="open_enabled && open_enabled.length > 0">
                <div class="account-wrap-title mb-2px">
                  <span class="text">已开启平台</span>
                  <span v-if="open_enabled.length > 1" class="set cursor" @click="onSetting">排序</span>
                </div>
                <div
                  v-for="item in open_enabled"
                  :key="item.idTeam"
                  :class="['account-item', { active: item.teamId === store.activeAccount?.teamId }]"
                  @click="accountClick(item)"
                >
                  <!-- {{ item.teamId === store.activeAccount?.teamId }} -->
                  <!-- <img v-if="item?.teamLogo" :src="item.teamLogo" />
                    <img
                      v-else
                      class="org-img"
                      src="@/assets/building-fill.png"
                      alt=""
                    /> -->
                  <!-- dot -->

                  <span class="left">
                    <!-- <iconpark-icon
                      name="iconother"
                      :class="{
                        icon: true,
                        'op-0': item.teamId !== store.activeAccount?.teamId,
                      }"
                    /> -->
                    <img
                      :class="{
                        icon: true,
                      }"
                      v-if="item.teamId === store.activeAccount?.teamId"
                      src="@/assets/svg/clouddisk/icon_other.svg"
                    />
                    <div class="boxDot" v-else-if="item?.isRedDot && item.teamId !== store.activeAccount?.teamId">
                      <span class="iconDot outerDot" ></span>
                    </div>
                    <div v-else class="boxDot"></div>
                    <!-- <span class="iconDot" v-show="digitalPlatformHasRed"></span> -->
                    <!--
                    <t-badge :count="1" dot size="small" class="mr-5">

                    </t-badge> -->
                    <span class="avatar">
                      <kyy-avatar
                        class="rd-10"
                        :avatar-size="'24px'"
                        :image-url="item?.teamLogo || ORG_DEFAULT_AVATAR"
                        :user-name="item?.teamFullName"
                        :shape="'circle'"
                      />
                      <span class="iconDot" v-show="item?.isRedDot  && item.teamId === store.activeAccount?.teamId"></span>
                    </span>
                    <div class="name line-1 max-w-144px">{{ item.teamFullName }}</div>
                  </span>
                  <span class="right">
                    <span class="type government" v-show="type.Government === item?.platform_uuid">
                      <!-- <img :src="government" class="img"> -->
                      城市
                    </span>
                    <span class="type member"  v-show="type.Member === item?.platform_uuid">
                      <!-- <img :src="member" class="img"> -->
                      商协
                    </span>
                    <span class="type cbd" v-show="type.Cbd === item?.platform_uuid">
                      <!-- <img :src="cbd" class="img"> -->
                      CBD
                    </span>
                    <span class="type association" v-show="type.Association === item?.platform_uuid">
                      社群
                    </span>
                        <span class="type school" v-show="type.Uni === item?.platform_uuid">
                      高校
                    </span>
                  </span>
                  <!-- 商协会1.2暂时去掉 -->
                  <!-- <span class="right">
                    <t-badge
                      :count="store.getCountByTeam(item.teamId)"
                      size="small"
                      class="mr-5"
                    />
                  </span> -->
                </div>
              </template>
              <template  v-if="no_enabled && no_enabled.length > 0">
                <div class="account-wrap-title mb-2px">
                  <span class="text">未开启平台</span>
                  <!-- <span class="set">设置</span> -->
                </div>
                <div
                  v-for="item in no_enabled"
                  :key="item.idTeam"
                  :class="['account-item', 'no_enabled', { active: item.teamId === store.activeAccount?.teamId }]"
                  @click="accountClick(item)"
                >
                  <span class="left">

                    <img
                      :class="{
                        icon: true,
                        'op-0': item.teamId !== store.activeAccount?.teamId,
                      }"
                      src="@/assets/svg/clouddisk/icon_other.svg"
                    />

                    <kyy-avatar
                      class="rd-10"
                      :avatar-size="'24px'"
                      :image-url="item?.teamLogo || ORG_DEFAULT_AVATAR"
                      :user-name="item?.teamFullName"
                      :shape="'circle'"
                    />
                    <div class="name line-1">{{ item.teamFullName }}</div>

                  </span>
                  <span class="right">
                    <span class="btnOpen w-66px">去开启</span>
                  </span>

                </div>
              </template>
            </div>
            <div class="join mt-8px">
              <span class="btn" @click="onJoinDigital">
                <iconpark-icon name="iconpeopleadd" class="add"></iconpark-icon>
                加入数字平台
              </span>
            </div>
          </div>
        </template>
      </t-popup>
    </span>

    <span class="shuRush">
      <span class="line"></span>
      <iconpark-icon name="iconrefresh" class="btn-op" @click="refresh"></iconpark-icon>
      <iconpark-icon v-if="newWinFlag" name="iconwindow" class="btn-op" @click="openStandAloneWin"></iconpark-icon>
    </span>
  </div>
  <joinDrawer ref="joinDrawerRef"/>
  <setOrganizeSortDrawer ref="setOrganizeSortDrawerRef" @refresh="refreshListener"/>
</template>

<script setup lang="ts">
import cbd from "@/assets/member/svg/cbd.svg";
import business from "@renderer/assets/member/svg/business.svg";
import activity from "@renderer/assets/member/svg/activity.svg";
import school from "@renderer/assets/member/svg/school.svg";
import directory from "@renderer/assets/member/svg/directory.svg";
import square from "@renderer/assets/member/svg/square.svg";
import pb from "@/assets/pb/header_party.svg";
import digital from "@renderer/assets/member/svg/digital.svg";
import association from "@renderer/assets/member/svg/association.svg";
import government from "@renderer/assets/member/svg/government.svg";
// import business from "@renderer/assets/member/svg/business.svg";
import document from "@renderer/assets/member/svg/document.svg";
import notice from "@/assets/niche/ggao2.svg";
import fengcai from "@/assets/fengcai/elegance.svg";
import photoAlbum from '@renderer/assets/photo_album.svg';
import circle from '@renderer/assets/member/svg/circle.svg';
import policy from '@renderer/assets/bench/policy.svg'


import CountBadgeComp from "@renderer/components/member/components/CountBadgeComp.vue";

import TAB_ICON from "@renderer/assets/member/icon_apply_notes.svg";
import { useRoute, useRouter } from "vue-router";
import { ref, reactive, onMounted, toRaw, watch, nextTick, onUnmounted } from "vue";
import to from "await-to-js";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
// import { GetCommonOrganizesAxios } from "@renderer/api/engineer";
import { getResponseResult } from "@renderer/utils/myUtils";
// import { individualInfo } from "@/api/square/home";
// import { MemberTeamsReqAxios } from "@renderer/api/engineer";
import { getCommonTeamsAxios, getCommon212TeamsAxios, getPlatformRedDotApi } from "@renderer/api/digital-platform/api/businessApi";
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
import KyyAvatar from "@/components/kyy-avatar/index.vue";
import { setDigitalPlatformTeamID } from "@renderer/views/digital-platform/utils/auth";
import joinDrawer from "@renderer/views/digital-platform/components/join-drawer.vue";

import { DialogPlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { getNowDigitalType } from "../marketplace/utils";
import setOrganizeSortDrawer from '@renderer/views/digital-platform/modal/set-organize-sort-drawer.vue'

import member from "@renderer/assets/member/svg/member.svg";
import { workAreaRefreshKey } from "../utils/eventBusKey";
import { useEventBus } from "@vueuse/core";
import { getDigitalPlatformRedMenuTotal } from "@renderer/api/customerService";
import LynkerSDK from "@renderer/_jssdk";
import { destroyNode, getShowNode } from "@renderer/_jssdk/components/iframe/iframePool";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();

// 状态管理：控制移动tabs的偏移距离
const tabsScrollLeft = ref(0);
// 状态管理：获取tabs-list的dom使用
const tabsListRef = ref(null);
// 状态管理：是否现实移动的图标
const isShowTabsScrollIcon = ref(false);
const emit = defineEmits(["refresh", "loaded", "removetab"]);
const joinDrawerRef = ref(null);
const route = useRoute();
const router = useRouter();
const store = useDigitalPlatformStore();
// const tabList = store.tabs;
const loading = ref(false);
const newWinFlag = ref(true);
enum type  {
  Member = 'member',
  Government = 'government',
  Cbd = 'cbd',
  Association = 'association',
  Uni = 'uni',
}
// const accountList = reactive([
//   {
//     squareId: "1",
//     avatar: "http://dummyimage.com/100x100",
//     name: "hgj_个人广场号",
//     count: 0,
//     type: 1,
//   },
//   {
//     squareId: "2",
//     avatar: "https://fakeimg.pl/200x200/",
//     name: "深圳速米科技有限公司",
//     count: 6,
//     type: 0,
//   },
// ]);
const open_enabled = ref([]);
const no_enabled = ref([]);
const all_enabled = ref([]);

/**
 * 点击左右箭头图标移动tabs拦
 * @param normal 移动的方向
 */
const onScrollTabs = async (normal: -1 | 1, index = 0) => {
  await nextTick();
  const tabItemWidth = 180;
  tabsScrollLeft.value += tabItemWidth * normal * (index + 1);
  const scrollWidth = tabsListRef.value.scrollWidth;
  const offsetWidth = tabsListRef.value.offsetWidth;
  if (tabsScrollLeft.value < -scrollWidth + offsetWidth) {
    tabsScrollLeft.value = -scrollWidth + offsetWidth;
  } else if (tabsScrollLeft.value > 0) {
    tabsScrollLeft.value = 0;
  }
};

watch(
  () => store.tabs,
  async () => {
    await nextTick();
    const scrollWidth = tabsListRef.value.scrollWidth;
    const offsetWidth = tabsListRef.value.offsetWidth;
    isShowTabsScrollIcon.value = scrollWidth > offsetWidth;
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => store.activeIndex,
  async (newVal: number, oldVal: number) => {
    await nextTick();
    onScrollTabs(newVal - oldVal > 0 ? -1 : 1, newVal);
  },
  {
    immediate: true,
  },
);

watch(
  () => store.goTeamId,
  (val) => {
    if (val) {
      console.log('goTeamId', store.goTeamId)
      onInitData().then(()=> {
        emit("refresh");
      });
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

// onMounted(() => {
//   // TODO 获取所有广场号列表
//   // setTimeout(() => {
//   //   store.activeAccount = toRaw(accountList)[0];
//   // }, 1000);
// });

const getRedNums = async () => {
  const promises = open_enabled.value.map(async (element) => {
    const [err, res] = await to(getPlatformRedDotApi({
      team_id: element.teamId,
      with_num: 0
    }, store.activeAccount?.teamId));
    if(!err) {
      const {data} = res?.data;
      console.log(data)
      element.isRedDot = data?.is_red
    }

  });

  await Promise.all(promises);
};

const digitalPlatformHasRed = ref(false);
const getDigitalPlatformMessageInfo = async () => {
  digitalPlatformHasRed.value = false;
  const [err, res] = await to(getDigitalPlatformRedMenuTotal({}));
  if (err) {
    return;
  }
  digitalPlatformHasRed.value = res?.data?.data.is_red || false;
};



const onInitData = (inForum = false) => {

  return new Promise(async (resolve, reject) => {
    // store.activeAccount = null;
    if (!inForum) {
      loading.value = true;
    }
    let [err, res] = await to(getCommon212TeamsAxios({}));
    if (!inForum) {
      loading.value = false;
    }
    console.log(err, res);
    if (err) return reject();

    // let [err, res] = await to(MemberTeamsReqAxios());
    res = getResponseResult(res);
    // const resList = res?.data?.open_enabled ? res?.data?.open_enabled?.filter((v) => [1, 2, 4].includes(v.teamType)) : [];

    // const resList = res.data || [];
    // for (const item of resList) {
    //   item.name = item.teamFullName;
    //   item.icon = item.staffAvatar;
    //   item.id = item.teamId;
    // }
    open_enabled.value = res?.data?.open_enabled || [];
    no_enabled.value = res?.data?.no_enabled || [];
    all_enabled.value = open_enabled.value?.concat(no_enabled.value)
    // console.log(res.data?.team);
    console.log(res);
    console.log(store.activeAccount);
    console.log(store, "内容store");
    console.log(open_enabled.value);
    if (!err) {
      // store.activeAccount = res.data?.profile || {};

      // 加多一层，别的地方跳过来的
      console.log("flag:", route.query, all_enabled.value);
      if (route.query && ["digital_platform", "message"].includes(route.query?.from) && all_enabled.value.length > 0) {
        const { teamId } = route.query;
        const teamObj = all_enabled.value.find((v) => v.teamId === teamId);
        console.log("flag:", teamId, teamObj);
        store.setActiveAccount(teamObj || all_enabled.value[0]);
        // emit('refresh');
      } else if (store.activeAccount && all_enabled.value.length > 0) {
        if (store.goTeamId) {
          // 跳转到对应的新组织
          const tem = all_enabled.value.find((v) => v.teamId === store.goTeamId);
          // 切换tab
          store.removeAllTab();
          store.switchTab(0);
          if (store.tabs.length > 0) {
            router.push({
              path: store.tabs[0].fullPath,
              query: {
                ...(store.tabs[0].query || {}),
                __tabs_id__: store.tabs?.[0]?.path_uuid,
                __tabs_title__: store.tabs?.[0]?.label,
                __tabs_icon__: store.tabs?.[0]?.icon,
                __tabs_active_icon__: store.tabs?.[0]?.activeIcon,
              },
            });
          }

          store.setActiveAccount(tem);
          store.setGoTeam("");
        } else if (all_enabled.value.some((v) => v.teamId === store.activeAccount.teamId)) {
          console.log("已存在");
          store.setActiveAccount(all_enabled.value.find((v) => v.teamId === store.activeAccount.teamId)); // 为了更新里面的数据
          setDigitalPlatformTeamID(store.activeAccount.teamId);
        } else {
          store.setActiveAccount(all_enabled.value.length > 0 ? all_enabled.value[0] : null);
        }
      } else {
        store.setActiveAccount(all_enabled.value.length > 0 ? all_enabled.value[0] : null);
      }

      console.log("account::", store.activeAccount);

      console.log(res);
    }

    // const tab = store?.tabs?.find((v) => v.name === 'member_number');
    // console.log(tab);
    // if (tab) {
    //   tab.title = '数字商协';
    // }
    getRedNums();
    getDigitalPlatformMessageInfo()
    resolve('success');
  })

};






const bus = useEventBus(workAreaRefreshKey);


 onMounted(()=> {
  bus.on((e, postData) => {
    if (e.name === 'work-area-refresh') {
      console.log('work-area-refresh', '要刷新了')
      // refreshListener();
      refreshListener();
    }
  });
 })

 onUnmounted(() => {
  bus.off((e, postData) => {
    // if (e.name === 'work-area-refresh') {
    //   console.log('work-area-refresh', '要刷新了')
    // }
    console.log('onUnmounted');
  });
 })
 
const refreshListener = () => {
  onInitData().then(()=> {
    emit("refresh");
  });
}
const refreshTeams = () => {
  onInitData()
}
onMountedOrActivated(() => {
  onInitData().then(()=> {
    emit("refresh");
  });
  // setTimeout(() => {
  //   if (!loading.value) {
  //     console.log('我执行了刷新', store.activeAccount)
  //     emit("refresh");
  //   }
  // });
});
// store.isBrushTeam = true;
// watch(() => store.isBrushTeam, (val) => {
//   if (val) {
//     // onLoadingShow(4000);
//     onInitData();
//     store.isBrushTeam = false;
//   }
// });

// watch(() => store.isLoadingShow, (val) => {
//   if (val) {
//     onLoadingShow(500);
//     // onInitData();
//     store.setLoadingShow(false);
//   }
// });

// watch(() => route.fullPath, async () => {
//   setTimeout(() => {
//     if (route.query && route.query.from === 'message') {
//       const { teamId, redirect } = route.query;
//       if (teamId) {

//       }
//     }
//   });

// });
const setOrganizeSortDrawerRef = ref(null);
const onSetting = () => {
  setOrganizeSortDrawerRef.value?.onOpen()
}

const onJoinDigital = () => {
  joinDrawerRef.value?.onOpen();
}

const onLoadingShow = (timer?) => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, timer || 900);
};

const accountClick = async (item) => {
  return new Promise((resolve, reject) => {
    if (store.tabs.length > 1) {
      const confirmDia = DialogPlugin({
        header: "提示",
        theme: "info",
        body: "切换组织将会关闭已打开的标签页面，请确保已妥善保存。",
        closeBtn: null,
        confirmBtn: "确定",
        zIndex: 6000,
        className: "delmode",
        onConfirm: async () => {
          store.removeAllTab();
          store.switchTab(0);
          if (store.tabs.length > 0) {
            router.push({
              path: store.tabs[0].fullPath,
              query: {
                ...(store.tabs[0].query || {}),
                __tabs_id__: store.tabs?.[0]?.path_uuid,
                __tabs_title__: store.tabs?.[0]?.title,
                __tabs_icon__: store.tabs?.[0]?.icon,
                __tabs_active_icon__: store.tabs?.[0]?.activeIcon,
              },
            });
          }
          resolve(true);
          // 删除字段操作
          confirmDia.hide();
          accountClickAfter(item);
        },
        onClose: () => {
          confirmDia.hide();
          resolve(false);
        },
      });
    } else {
      resolve(true);
      store.switchTab(0);
      router.push({
        path: store.tabs[0].fullPath,
        query: {
          ...(store.tabs[0].query || {}),
          __tabs_id__: store.tabs?.[0]?.path_uuid,
          __tabs_title__: store.tabs?.[0]?.title,
          __tabs_icon__: store.tabs?.[0]?.icon,
          __tabs_active_icon__: store.tabs?.[0]?.activeIcon,
        },
      });
      accountClickAfter(item);
    }
  })
};
const accountClickAfter = (item) => {
  onLoadingShow(100);
  // store.activeAccount = item;
  store.setActiveAccount(item);

  emit("refresh", true);


  // const tab = store?.tabs?.find((v) => v.name === 'member_number');
  // console.log(tab);
  // console.log(store);
  // console.log(store.activeAccount);
  // console.log('accountClick');
  // if (tab) {
  //   tab.title = '数字商协';
  // }
};

const switchTab = (item, index) => {
  console.log("switchTab", item.path);
  // onLoadingShow();

  // if (item.fullPath === "/memberIndex/member_manage") return;
  // store.removeTab("/memberIndex/member_manage"); // 移除依赖页面
  // store.switchTab(index);
  // router.replace(item.fullPath);

  // const tab = store?.tabs?.find((v) => v.name === 'member_number');
  // console.log(tab, 'tab');
  // if (tab) {
  //   tab.title = tab.title? tab.title: '数字商协';
  // }

  store.switchTab(index);
  let query = "";
  if (item.query) {
    console.log("🚀 ~ switchTab ~ item.query:", item.query);
    query = `?${new URLSearchParams(item.query).toString()}`;
  }
  console.log(`${item.path}${query}`, "switchTabswitchTab");
  console.log(item, "switchTabswitchTabitemitemitem");
  router.replace({
    path: `${item.fullPath}${query}`,
    query: item.query,
  });
};

const handleBeforeClose = (title: string, content: string): Promise<boolean> => new Promise((resolve) => { const confirmDia = DialogPlugin.confirm({ theme: 'info', header: title, body: content, confirmBtn: '确认关闭', cancelBtn: { content: '取消', theme: 'default', variant: 'outline' }, onConfirm: async () => { confirmDia.hide(); resolve(true); }, onClose: () => { confirmDia.destroy(); resolve(false); }, }); });

const removeTab = async (item) => {
  // console.log(item.fullPath);
  // console.log(store.getActiveTab().fullPath);
  // onLoadingShow();
  // if (store.getActiveTab().fullPath === item.fullPath) {
  //   console.log('A')
  //   store.removeTab(item.);
  // } else {
  //   console.log('B')

  //   store.removeTab(item.fullPath, true);
  // }
  // console.log("275", store.getActiveTab());
  // router.replace(store.getActiveTab().fullPath);
  const path_uuid = item.path_uuid;
  if (path_uuid) {
    const index = store.tabs.findIndex((v) => v.path_uuid === path_uuid);
    // 关闭页面前确认
    const beforeClose = store.tabs[index]?.beforeCloseOptions;
    console.log('close-digital-platform-tab-item',  store.tabs[index]);
    if (beforeClose && beforeClose.title && beforeClose.content) {
      const confirm = await handleBeforeClose(beforeClose.title, beforeClose.content);
      if (!confirm) return;
    }
    if (item.path_uuid) {
      getShowNode()?.forEach((i) => {
        if (i.getAttribute('data-id') === item?.path_uuid) {
          destroyNode(i.getAttribute('data-id'));
        }
      });
    } else {
      getShowNode().forEach((node) => {
        destroyNode(node.getAttribute('id') || '');
      });
    }
    if (index > -1) {
      const item = store.tabs[index];
      store.activeIndex
      store.removeTab(item);
      setTimeout(() => {
        const next = Math.max(0, index - 1);
        let query = "";
        const nextItem = store.tabs[next];
        if (nextItem.query) {
          console.log("🚀 ~ switchTab ~ item.query:", nextItem.query);
          query = `?${new URLSearchParams(nextItem.query).toString()}`;
        }
        console.log(`${nextItem.path}${query}`, "switchTabswitchTab");
        console.log(nextItem, "switchTabswitchTabitemitemitem");
        router.replace({
          path: `${nextItem.fullPath}${query}`,
          query: nextItem.query,
        });
      }, 0);
    }
  }
  if (item.path === "member_manage") {
    const confirmDia = DialogPlugin({
      header: t("approval.desgin.header"),
      body: t("approval.desgin.body"),
      confirmBtn: t("approval.desgin.confirmBtn"),
      className: "dialog-classp24",
      cancelBtn: t("approval.desgin.cancelBtn"),
      onConfirm: ({ e }) => {
        // emits("design-goback", true);
        confirmDia.hide();
        actionRemoveTab(item);
      },
      onClose: ({ e }) => {
        confirmDia.hide();
      },
    });
  } else {
    actionRemoveTab(item);
  }
};

const emitRemovetab = (item) => {
  const type = getNowDigitalType();
  if (item.name === `digital_platform_${type}_rich`) {
    store.richPagekey += 1;
  }
  if (item.name === `digital_platform_${type}_supply`) {
    store.supplyPagekey += 1;
  }
  if (item.name === `digital_platform_${type}_desired`) {
    store.desiredPagekey += 1;
  }
};

const actionRemoveTab = (item) => {
  emitRemovetab(item);
  setTimeout(() => {
    item && store.removeTab(item);
    const currentRoute = store.getActiveTab();
    console.error("currentRoute", currentRoute);

    console.log(currentRoute);
    if (!currentRoute) return;
    router.replace({ path: currentRoute.fullPath, query: currentRoute.query });
  }, 100);
};

const refresh = () => {
  // 论坛内部自行处理刷新逻辑
  const inForum = route.path.startsWith('/digitalPlatformIndex/digital_platform_forum');
  if (!inForum) {
    onLoadingShow();
  }
  // onInitData(inForum);
  onInitData(inForum).then(()=> {
    emit("refresh");
  });
  // emit("refresh");

  // router.go(0);
};

const openStandAloneWin = () => {
  newWinFlag.value = false;
  // ipcRenderer.invoke('click-standalone-window', {
  //   url: 'squareLayout',
  //   flag: route.fullPath.split('/')[1],
  // });

  console.log(route, "routerouteroute");

  ipcRenderer.invoke("click-standalone-window", {
    url: "projectLayout",
    flag: route.fullPath.split("/")[1],
  });
};

defineExpose({
  refreshTeams,
  selectTeam: async (teamId: string) => {
    const item = all_enabled.value?.find((v) => v.teamId === teamId);
    console.log('selectTeam', all_enabled.value, store.activeAccount.teamId, item);

    if (item && item.teamId && item.teamId !== store.activeAccount.teamId) {
      return accountClick(item)
    }
    if (item && item.teamId && item.teamId === store.activeAccount.teamId) {
      return true;
    }
    return false;
  },
  getActiveTeamId: () => {
    return store.activeAccount.teamId;
  },
})

</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";

.join {
  border-top: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
  padding: 12px 12px 4px;

  .btn {
    border-radius: 16px;
    border: 1px solid var(--brand-kyy_color_brand_default, #4D5EFF);
    background: var(--bg-kyy_color_bg_light, #FFF);
    padding-left: 8px;
    padding-right: 12px;
    display: inline-flex;
    align-items: center;
    gap: 4px;

    color: var(--brand-kyy_color_brand_default, #4D5EFF);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */

    user-select: none;
    cursor: pointer;
    transition: all 0.15s linear ;
    &:hover {
      border-radius: 16px;
      border: 1px solid var(--brand-kyy_color_brand_hover, #707EFF);
      color: var(--brand-kyy_color_brand_hover, #707EFF);
      background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
      transition: all 0.15s linear ;
      .add {
        color: #707EFF;
        transition: all 0.15s linear ;
      }
    }

    .add {
      color: #4D5EFF;
      font-size: 20px;
      height: 32px;
      transition: all 0.15s linear ;
    }


  }
}


/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  // height: 2px;
  // background-color: #f5f5f5;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  // background-color: #e3e6eb;
  // background-color: #fff;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 7px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  // background-color: #D5DBE4;
  background-color: var(--divider-kyy_color_divider_deep, #D5DBE4);
}


:deep(.t-image__wrapper) {
  z-index: 0 !important;
}

.backgrd {
  background: #272b4f;
}
.bgc-fff {
  background: #fff !important;
  .iconClose {
    color: #3e4cd1;
  }
  &::after {
    display: none;
  }
}

.topTab {
  display: flex;
  align-items: center;
  gap: 8px;
  .tabIcon {
    // margin-left: 7px !important;
    border-radius: 4px !important;
    width: 20px;
    height: 20px;
    // background: var(--cyan-kyy-color-cyan-default, #11bdb2) !important;
    background: #fd9d4f;
    display: flex;
    align-items: center;
    justify-content: center;
    .tabImg {
      width: 20px !important;
      height: 20px !important;
      margin-right: 0 !important;
      font-size: 20px;
    }
    .servicedefault {
      font-size: 14px;
      color: #fff;
    }
  }
  .tab-title {
    overflow: hidden;
    color: var(--kyy_color_tabbar_item_text, #1a2139);
    text-overflow: ellipsis;
    max-width: 90px;
    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

.btn-op {
  width: 20px;
  height: 20px;
  font-size: 20px;
  margin-right: 16px;
  cursor: pointer;
}
.org-img {
  width: 24px;
  height: 24px;
  border-radius: 5px;
  margin-left: 4px;
}

.shuRush {
  display: flex;
  align-items: center;
  // border-left: 1px solid #D5DBE4;
  .line {
    width: 1px;
    height: 16px;
    background: var(--divider-kyy_color_divider_deep, #D5DBE4);
    margin: 0 8px;
  }
}


.avatar {
  position: relative;
  .iconDot {
    position: absolute;
    z-index: 3;
    border-radius: var(--kyy_radius_badge_full, 999px);
    border: 1px solid var(--kyy_color_badge_border, #FFF);
    background: var(--kyy_color_badge_bg, #FF4AA1);
    width: 8px;
    height: 8px;
    right: 0;
    top: 0;
  }
}
.account {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  max-width: 214px;



  .arrow {
    width: 20px;
    height: 20px;
    // margin-right: 16px;
  }
  .text {
    // max-width: 94px;
    font-size: 12px;
    font-weight: 400;
    margin: 0 4px;
    color: #13161b;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.head-box {
  height: 40px;
  display: flex;
  align-items: center;
  overflow: hidden;
  border-radius: 8px 0px 0px 0px;
  // background: var(--kyy_color_tabbar_bg, #ebf1fc);
  background-color: var(--kyy_color_tabbar_bg, #ebf1fc);
  .tabs-item {
  }

  .tabs-list {
    &:hover {
      //overflow-x: overlay !important;
    }
    display: flex;
    flex: 1;
    width: 0;
    overflow-x: hidden;
    background: var(--kyy_color_tabbar_bg, #ebf1fc);
    &--box {
      display: flex;
      transition: transform 0.5s;
    }
    .tabs-item {
      cursor: pointer;
      min-width: 140px;
      max-width: 168px;

      justify-content: space-between;
      height: 32px;
      margin: 4px;
      display: flex;
      align-items: center;
      // background: #f1f2f5;
      border-radius: 4px;
      // background: var(--kyy_color_tabbar_item_bg_active, #fff);
      background: var(--kyy_color_tabbar_bg, #ebf1fc);

      border-radius: 4px;
      padding: 4px 12px;
      position: relative;
      &::after {
        position: absolute;
        content: " ";
        height: 20px;
        min-width: 1px;
        max-width: 1px;
        background-color: #d5dbe4;
        right: 0;
      }

      &:hover {
        background: var(--kyy_color_tabbar_item_bg_hover, rgba(255, 255, 255, 0.5));
        .iconClose {
          color: #707eff;
        }
        &::after {
          display: none;
        }
      }

      // box-shadow: 0 1px 4px 0 rgba(19, 22, 27, 0.24);
      .left-head-color {
        width: 5px;
        border-radius: 4px 0 0 4px;
        height: 32px;
        background: #2069e3;
      }
      img {
        width: 15px;
        height: 15px;
        margin-right: 0 !important;
      }
      span {
        height: 20px;
        font-size: 12px;
        font-weight: 400;
        color: #13161b;
        line-height: 20px;
      }
    }
  }
}
</style>

<style lang="less" scoped>
.tabs-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 35px;
  border-radius: 5px 0 0 0;
  background: var(--kyy_color_tabbar_bg, #ebf1fc);
  &:hover {
    cursor: pointer;
    background: var(--kyy_color_tabbar_item_bg_hover, rgba(255, 255, 255, 0.5));
  }
}
.icon-specials {
  width: 8px;
  height: 8px;
  position: absolute;
  top: 0;
  left: 0;
  color: var(--bg-kyy-color-bg-software-foucs, #272b4f);
}
.bolist {
  width: 320px;
}
.square-account-list-popup {

  //width: 311px;
  // width: 336px !important;
  .account-wrap {
    max-height: 270px;
    overflow-y: overlay;

    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;
      padding: 0 12px;
      .text {
        color: var(--text-kyy_color_text_3, #828DA5);
        font-size: 14px;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
      .set {
        color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
  }

  .no_enabled {
    .name {
      max-width: 232px;
    }
    .right {
      display: none !important;
    }
    &:hover {
      .name {
        max-width: 154px;
      }
      .right {
        display: block !important;
      }
    }
  }
  .account-item {
    display: flex;
    align-items: center;
    height: 32px;
    border-radius: 4px;
    margin-bottom: 2px;
    padding: 0 8px 0 12px;
    justify-content: space-between;

    .left {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 12px;
      .icon {
        font-size: 20px;
      }
      .boxDot {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        .outerDot {
          border-radius: var(--kyy_radius_badge_full, 999px);
          border: 1px solid var(--kyy_color_badge_border, #FFF);
          background: var(--kyy_color_badge_bg, #FF4AA1);
          width: 8px;
          height: 8px;

        }
      }

    }


    .right {
      flex: none;
      display: flex;
      align-items: center;
      // padding-right: 8px;
      .btnOpen {
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
        background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
        color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);

        font-size: 14px;
        font-weight: 400;
        line-height: 22px; /* 157.143% */

        display: flex;
        justify-content: center;
        align-items: center;
      }
      .type {
        width: fit-content;
        // padding:  4px 0;
        padding: 0 4px;
        border-radius: 4px;
        // background: var(--bg-kyy_color_bg_deep, #F5F8FE);
        display: flex;
        height: 20px;
        align-items: center;
        line-height: 20px;
        gap: 2px;

        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        .img {
          width: 16px;
          height: 16px;
        }
      }
    }
    // gap: 12px;
    cursor: pointer;
    &.active {
      background: var(--lingke-select, #e1eaff);
    }

    &:hover {
      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
    }
    > .icon {
      margin-right: 8px;
      font-size: 20px;
    }
  }
  .avatar {
    width: 24px;
    height: 24px;
    border-radius: 5px;
    // margin-right: 8px;
  }
  .name {
    // max-width: 220px;
    // margin-right: 20px;
    color: var(--lingke-black-90, #1a2139);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
}
.government {
  color: var(--kyy_color_tag_text_cyan, #11BDB2);
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_cyan, #E6F9F8);
}
.member {
  color: var(--kyy_color_tag_text_warning, #FC7C14);

  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_warning, #FFE5D1);
}
.cbd {
  color: var(--kyy_color_tag_text_brand, #4D5EFF);
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_brand, #EAECFF);
}
.association {
  color: var(--kyy_color_tag_text_error, #D54941);
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_error, #F7D5DB);
}
.school{
    color: var(--kyy_color_tag_text_error, #21ACFA);
  border-radius: var(--kyy_radius_tag_s, 4px);
background: var(--kyy_color_tag_bg_kyyBlue, #E4F5FE);
}
.account {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 50px;
  .text {
    color: var(--text-kyy-color-text-2, #516082);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
  .arrow {
    font-size: 24px;
  }
}
:deep(.t-badge--dot) {
  right: 3px;
  margin-top: 2px;
}
:deep(.t-badge--circle) {
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  padding: 0 8px;
  // color: var(--kyy_color_tag_text_magenta, #ff4aa1);
  color: var(--kyy_color_tag_text_magenta, #ff4aa1);
  // background: var(--kyy_color_tag_bg_magenta, #ffe3f1);
  background: var(--kyy_color_tag_bg_magenta, #ffe3f1);

  font-family: PingFang SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}
</style>
