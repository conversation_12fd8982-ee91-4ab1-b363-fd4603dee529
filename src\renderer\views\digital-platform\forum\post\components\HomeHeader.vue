<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import to from 'await-to-js';
import { UserDetail } from '@renderer/api/forum/models/user';
import { getUserInfo } from '@renderer/api/forum/user';
import { uesCheckLineRows } from '@renderer/views/square/homepage/hooks';
import { TagColors } from '@renderer/views/digital-platform/utils/constant';
import { regionToArray } from '@renderer/components/common/region/utils';
import { Region } from '@renderer/api/square/models/common';
import Avatar from '@/components/kyy-avatar/index.vue';
import SettingDropdown from './SettingDropdown.vue';
import { useForumStore } from '../../store';
import { DEFAULT_COVER } from '../../constant';

const props = defineProps<{
  self?: boolean;
  openid?: string;
  cardId?: string;
  hasBack?: boolean;
}>();
const emit = defineEmits(['update-remark']);

const forumStore = useForumStore();
const loading = ref(false);
const userInfo = ref<UserDetail>();

// 自已主页
const isSelf = computed(() => forumStore.currCard.card === props.cardId || props.self);

// 匿名主页
const isAnon = computed(() => userInfo.value?.identity === '匿名');

// 当前身份是否匿名且为个人主页
const isAnonymous = computed(() => isSelf.value && forumStore.currCard.flag === 'anonymous');

// 性别图标
const genderIcon = computed(() => {
  const gender = userInfo.value?.sex;
  if (!gender) return '';
  return { 2: 'icongirl', 1: 'iconboy' }[gender] || '';
});

// 背景图
const cover = ref(DEFAULT_COVER);

// 简介是否折叠
const introFold = ref(true);
const introText = computed(() => (introFold.value ? userInfo.value.intro : userInfo.value.intro.replaceAll('\n', '<br />')));
const showMoreIntro = ref(false);
const showFields = ref([]);
const { root, calcEllipsised } = uesCheckLineRows();

// 是否显指定字段
const showField = (field: string) => {
  if (showFields.value.length === 0) return;

  const isFirstField = showFields.value[0] === field;
  const isFieldIncluded = showFields.value.includes(field);
  return isFirstField ? !introFold.value : isFieldIncluded && !introFold.value;
};

// 判断折叠/展开状态下的字段是否展示
const adjustFoldFields = (data: UserDetail): UserDetail => {
  const result = { ...data, intro: data.intro || '暂无简介' };

  if (result.intro) showFields.value.push('intro');
  if (data.birth_show && data.birth) showFields.value.push('birth');

  if (data.tag_show && data.tag_list?.length) {
    showFields.value.push('tag');

    // 标签颜色
    result.tag_list = data.tag_list.map((v) => {
      const colorConfig = TagColors.find((item) => item.intColor === Number(v.colour));
      return {
        ...v,
        style: {
          backgroundColor: colorConfig?.bgColor,
          color: colorConfig?.color,
        },
      };
    });
  }

  return result;
};

// 获取用户信息
const getInfo = async () => {
  loading.value = true;

  const [err, res] = await to(
    getUserInfo({
      card_id: props.cardId || forumStore.currCard.card,
      openid: props.openid || forumStore.currCard.openid,
    }),
  );

  loading.value = false;
  if (err) return;

  const data = adjustFoldFields(res.data.data);
  cover.value = data.bg_cover || DEFAULT_COVER;

  // 回显地址
  try {
    const { names } = regionToArray(data.address as Region);
    if (names?.length) {
      data.addressFormatted = names.join('·');
    }
  } catch (error) {
    console.log('error', error);
  }

  userInfo.value = data;
  // TODO
  // if (isAnonymous.value) {
  //   const [err, res] = await to(getAnonDetail(forumStore.platformType ?? 'uni'));

  //   if (err) return;

  //   userInfo.value.avatar = res.data.data.avatar;
  //   userInfo.value.name = res.data.data.name;
  // }

  await nextTick();
  const hasMultiIntro = calcEllipsised(1, introText.value);
  showMoreIntro.value = hasMultiIntro || showFields.value.length > 1;
};

// 更新匿名身份
const handleAnonConfirm = (data: { name: string; avatar: string }) => {
  if (!isAnonymous.value) return;

  userInfo.value.avatar = data.avatar;
  userInfo.value.name = data.name;
};

onMounted(() => {
  getInfo();
});

watch(
  () => forumStore.currCard.card,
  () => {
    getInfo();
  },
);
</script>

<template>
  <div v-if="userInfo" class="home-header">
    <div v-if="hasBack" class="back" @click="$router.push({ name: 'forumHome' })">
      <iconpark-icon name="iconarrowlift" class="icon text-24" /> 返回
    </div>

    <t-image
      :src="cover"
      class="cover"
      fit="cover"
      lazy
      alt=""
    />

    <div class="content">
      <div class="user-wrap">
        <Avatar
          avatar-size="88px"
          round-radius
          :image-url="userInfo.avatar"
          :user-name="userInfo.remark || userInfo.name"
          class="avatar"
        />

        <div class="user-info">
          <div class="name-wrap">
            <div class="name ellipsis-1">
              {{ userInfo.remark || userInfo.name }}
              <span v-if="userInfo.remark" class="real-name">({{ userInfo.name }})</span>
            </div>
            <div v-if="isSelf" class="tag blue">{{ forumStore.currCard._flagText }}</div>
            <iconpark-icon v-if="userInfo.sex_show && genderIcon" :name="genderIcon" :class="['icon', genderIcon]" />

            <iconpark-icon
              v-if="!isSelf"
              name="iconedit"
              class="icon edit"
              @click="emit('update-remark')"
            />
          </div>
          <div class="tags">
            <div v-if="userInfo.ip_belong_address" class="tag-item">IP: {{ userInfo.ip_belong_address }}</div>
            <div v-if="userInfo.address_show && userInfo.addressFormatted" class="tag-item">
              {{ userInfo.addressFormatted }}
            </div>
          </div>
        </div>

        <div v-if="isSelf">
          <SettingDropdown
            :card-id="forumStore.currCard.card"
            @change:cover="cover = $event"
            @change:anonymous="handleAnonConfirm"
          />
        </div>
      </div>
      <div v-if="showFields.length && !isAnonymous && !isAnon" class="more-info" :class="{ fold: introFold }">
        <iconpark-icon
          v-if="showMoreIntro"
          name="iconarrowdwon"
          :class="['btn-fold icon', { toggle: !introFold }]"
          @click="introFold = !introFold"
        />

        <div v-if="userInfo.intro" class="info-item intro">
          <iconpark-icon
            fill="#fff"
            color="#828DA5"
            name="iconnote"
            class="icon mr-12 flex-items-start"
          />
          <div
            ref="root"
            class="brief relative"
            :class="{ 'line-1': introFold }"
            v-html="introText"
          />
        </div>

        <div v-if="showField('birth')" class="info-item">
          <iconpark-icon name="icondate" class="icon mr-12 flex-items-start" />
          <div>{{ userInfo.birth }}</div>
        </div>

        <div v-if="showField('tag')" class="info-item">
          <iconpark-icon name="icontag" class="icon mr-12 flex-items-start" />
          <div class="tag-list">
            <div
              v-for="(item, index) in userInfo.tag_list"
              :key="index"
              class="tag"
              :style="item.style"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.home-header {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  background: var(--bg-kyy_color_bg_light, #fff);
  margin-bottom: 8px;
  position: relative;
}

.cover {
  width: 100%;
  height: 130px;
  border-radius: 8px;
}

.back {
  display: flex;
  width: 100%;
  padding: 16px;
  gap: 4px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 90.18%);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  border-radius: 8px;
  color: var(--text-kyy_color_text_white, #fff);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  cursor: pointer;
}

.tag {
  display: flex;
  height: 20px;
  padding: 2px 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  text-align: center;
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  flex-shrink: 0;

  &.blue {
    color: var(--kyy_color_tag_text_brand, #4d5eff);
    background: var(--kyy_color_tag_bg_brand, #eaecff);
  }

  &.green {
    color: var(--kyy_color_tag_text_success, #499d60);
    background: var(--kyy_color_tag_bg_success, #e0f2e5);
  }
}

.content {
  display: flex;
  padding: 8px 16px 16px 16px;
  flex-direction: column;
  gap: 16px;
  position: relative;

  .user-wrap {
    display: flex;
    gap: 12px;
    padding-left: 100px;
  }

  .avatar {
    width: 88px;
    height: 88px;
    position: absolute;
    top: -36px;
    left: 16px;
    z-index: 1;
    border-radius: var(--kyy_avatar_radius_full, 999px);
    border: 2px solid var(--kyy_avatar_border_default, #fff);
    box-sizing: content-box;
  }

  .user-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;

    .name-wrap {
      display: flex;
      align-items: center;
      gap: 8px;

      .name {
        color: var(--text-kyy_color_text_1, #1a2139);
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
      }

      .real-name {
        color: var(--text-kyy_color_text_3, #828da5);
        font-weight: 400;
      }

      .icon {
        font-size: 16px;
        padding: 2px;
        color: #4d5eff;
        flex-shrink: 0;
        margin-right: 2px;
        &.icongirl {
          color: #ff4aa1;
        }
        &.edit {
          color: #828da5;
          cursor: pointer;
        }
      }
    }

    .tags {
      display: flex;
      gap: 8px;
      .tag-item {
        display: flex;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: 1px 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: var(--kyy_radius_tag_full, 999px);
        background: var(--kyy_color_tag_bg_gray, #eceff5);
        color: var(--kyy_color_tag_text_gray, #516082);
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }

  .more-info {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .btn-fold {
      cursor: pointer;
      align-items: flex-start;
      position: absolute;
      right: 0;
      top: 0;
    }

    .info-item {
      display: flex;
      align-items: center;
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      padding-right: 28px;

      &.info-item {
        align-items: flex-start;
      }

      .brief {
        position: relative;
        flex: 1;
      }
    }

    .icon {
      font-size: 20px;
      transition: transform 0.3s;
      color: #828da5;

      &.toggle {
        transform: rotate(180deg);
      }
    }

    .tag-list {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}
</style>
