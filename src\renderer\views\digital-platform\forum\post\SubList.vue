<script setup lang="ts">
import { ref, computed, provide } from 'vue';
import { onBeforeRouteLeave, useRoute } from 'vue-router';
import PostList from '@/views/digital-platform/forum/components/post/PostList.vue';
import TopPostList from '@/views/digital-platform/forum/components/post/TopPostList.vue';
import SetRemark from '@/views/digital-platform/forum/components/SetRemark.vue';
import HomeHeader from './components/HomeHeader.vue';
import { useForumStore } from '../store';
import { PageTypeMap } from '../constant';

provide(PageTypeMap.Topic, PageTypeMap.Topic);

const route = useRoute();
const forumStore = useForumStore();

/** 1: 论坛，2: 我的主页，3: 特定所有者 */
type From = '1' | '2' | '3';
const { from, name = '2', target_owner_id, openid, pin, remark: remarkInit, cardId } = route.query as Record<string, string>;
const remark = ref(remarkInit)
const params = ref({
  from: from as From,
  // from 为 3 时特定所有者 ID
  target_owner_id: target_owner_id,
  // true 时只查询置顶帖子
  pin,
});

// const headerTitle = computed(() => {
//   const isSelf = forumStore.ownerId === target_owner_id;
//   return `${isSelf ? '我' : remark.value || name}的主页`;
// });
const pinTitle = computed(() => {
  if (from === '1') return '置顶帖子';
  if (from === '2') return '我的置顶';
  return name ? `${name}的置顶` : '置顶帖子';
});

const refreshKey = ref(0);
const pageContent = ref(null);
const scroll = ref(0);
onBeforeRouteLeave((to, from, next) => {
  scroll.value = pageContent.value?.scrollTop;
  next();
});

const postListRef = ref(null);
const scrollDisabled = computed(() => postListRef.value?.status === 'finished');
const onLoading = () => {
  postListRef.value?.loading();
};

// 设置备注名
const remarkVisible = ref(false);
const homeHeaderKey = ref(1);
const onSuccessRemark = (val: string) => {
  // 刷新备注名数据
  remark.value = val;
  homeHeaderKey.value++;
  forumStore.refreshRemark = {
    cardId,
    remark: val,
  }
}
</script>

<template>
  <!-- 他人主页、我的或他人的置顶列表 -->
  <div
    ref="pageContent"
    v-infinite-scroll="onLoading"
    class="page-content"
    :infinite-scroll-immediate-check="false"
    :infinite-scroll-distance="1800"
    :infinite-scroll-disabled="scrollDisabled"
    infinite-scroll-watch-disabled="scrollDisabled"
  >
    <!-- xx的主页 -->
    <!-- <div v-if="!pin && params.target_owner_id" class="header">
      <div class="back" @click="$router.back()">
        <iconpark-icon name="iconarrowlift" class="icon" /> {{ $t('square.return') }}
      </div>
      <div class="content">{{ headerTitle }}</div>
    </div> -->

    <!-- 置顶列表页头部 -->
    <div v-if="pin" class="header">
      <div class="back" @click="$router.back()">
        <iconpark-icon name="iconarrowlift" class="icon" /> {{ $t('square.return') }}
      </div>
      <div class="title">{{ pinTitle }}</div>
    </div>

    <template v-else>
      <HomeHeader
        :key="homeHeaderKey"
        :has-back="!!params.target_owner_id"
        :openid="openid"
        :card-id="cardId"
        @update-remark="remarkVisible = true"
      />

      <SetRemark
        v-if="remarkVisible"
        v-model="remarkVisible"
        :card-id="cardId"
        :remark="remark"
        @success="onSuccessRemark"
      />

      <!-- 折叠的置顶 -->
      <TopPostList
        :from="params.from"
        :owner-id="params.target_owner_id"
        class="mb-8"
      />
    </template>

    <div class="list-wrap">
      <PostList
        ref="postListRef"
        :key="refreshKey"
        :params="params"
        :can-head-click="from === '1'"
        :hide-setting="!!pin"
        :ownerId="target_owner_id || forumStore.ownerId"
        go-detail
        class="trends-list"
        from="friend-circle"
        @removed="refreshKey++"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.page-content {
  gap: var(--kyy_radius_dropdown_m, 8px);

  .header {
    display: flex;
    padding: 12px 16px;
    align-items: center;
    gap: 4px;
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
    background: var(--bg-kyy_color_bg_light, #FFF);
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 16px;
    font-weight: 400;
    line-height: 24px; /* 150% */
    margin-bottom: 8px;
    .back {
      display: flex;
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      cursor: pointer;
      .icon {
        font-size: 20px;
      }
    }
    .title {
      flex: 1;
      padding-right: 52px;
      text-align: center;
    }

    .content {
      flex: 1;
      padding-right: 52px;
      text-align: center;
      .avatar-wrap {
        display: inline-flex;
        padding: 4px var(--kyy_radius_dropdown_m, 8px) 4px 4px;
        justify-content: center;
        align-items: center;
        gap: var(--kyy_radius_dropdown_m, 8px);
        border-radius: 99px;
        background: var(--bg-kyy_color_bg_deepest, #ECEFF5);
        color: var(--text-kyy_color_text_1, #1A2139);
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px; /* 150% */
      }
    }
  }

  .list-wrap {
    flex: 1;
    overflow-y: auto;
  }
}
</style>
