<template>
  <!-- 内容区域 -->
  <div class="warp">
    <RTable
      ref="RTableRef"
      :filter="filter"
      :table="table"
      :is-no-data="isNoData"
      :is-change-clean="false"
      @change="change"
    >
      <template #toolbarContent>
        <t-button class="sort w80 font-600" variant="outline" @click="sortFn">排序</t-button>
        <t-button @click="() => editFn('add')">
          <div class="flex-y-center">
            <t-icon name="add" />
            <span class="ml-4 font-600">{{ config?.text?.addButton || '添加' }}</span>
          </div>
        </t-button>
      </template>
      <template #empty>
        <REmpty v-if="isNoData" name="no-result" tip="搜度无结果" />
        <template v-else>
          <REmpty name="no-data" :tip="config?.text?.emptyText || '暂无内容'" />
        </template>
      </template>
    </RTable>
    <SortDialog v-model:visible="showSortDialog" :list="list" :copywriter="config?.text" @confirm="getData" />
    <EditDialog v-model:visible="showEditDialog" :info="selectInfo" :copywriter="config?.text" @confirm="getData" />
  </div>
</template>

<script setup lang="tsx">
import { REmpty, RTable } from '@rk/unitPark';
import { useQuery } from '@tanstack/vue-query';
import { refDebounced } from '@vueuse/core';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { computed, ref, watch } from 'vue';
import EditDialog from './components/EditDialog.vue';
import SortDialog from './components/SortDialog.vue';
import { NewsColumnConfig } from './types';
import { useModuleApi, useModuleStore } from '@modules/factory/context';

const props = defineProps<NewsColumnConfig>();

const store = useModuleStore();

const api = useModuleApi();

const RTableRef = ref(null);
const isNoData = ref(false);
const showSortDialog = ref(false);
const showEditDialog = ref(false);
const selectInfo = ref({});

const keyword = ref('');
const searchValueDebounced = refDebounced(keyword, 500);
const pageInfo = ref({
  total: 0,
  pageSize: 10,
  pageNumber: 1,
});

const me = {
  teamId: store.value.teamId,
  openId: store.value.query?.openId as string,
  cardId: store.value.query?.cardId as string,
};

const list = computed(() => {
  return table.value?.list || [];
});

const total = ref(0);

const change = (info: any, key: any) => {
  if (key === 'table') {
    return;
  }
  if (key === 'filter') {
    keyword.value = info.filter.searchVal || '';
    return;
  }
};

const editFn = (name: string, row?: any) => {
  showEditDialog.value = true;
  selectInfo.value = {
    type: name || 'add',
    row: row,
  };
};

const sortFn = () => {
  showSortDialog.value = true;
};

const delFn = async (row: any) => {
  const dailog = DialogPlugin({
    header: '删除',
    body: props?.config?.text?.deleteMessage || '删除后，关联的资讯内容也会被删除',
    theme: 'info',
    onConfirm: async () => {
      let res;
      const params = {
        me: me,
        id: row.id,
      };
      try {
        res = await api.deleteColumn(params);
      } catch (e) {
        MessagePlugin.error(props?.config?.text?.deleteFailedMessage || '出错了!');
      }
      if (res) {
        getData();
        MessagePlugin.success(props?.config?.text?.deleteSuccessMessage || '删除成功');
      }
      dailog.hide();
    },
  });
};

// 计算查询参数，集中管理所有参数
const queryParams = computed(() => ({
  name: searchValueDebounced.value,
  teamId: store.value.teamId,
  'me.openId': store.value.query?.openId,
  'me.cardId': store.value.query?.cardId,
  'me.teamId': store.value.teamId,
  'page.size': pageInfo.value.pageSize,
  'page.number': pageInfo.value.pageNumber,
}));

// 使用useQuery获取数据
const { data, isLoading, refetch } = useQuery({
  queryKey: ['news-column', queryParams],
  queryFn: () => api?.getColumnList(queryParams.value, store.value.teamId),
  retry: false,
  refetchOnWindowFocus: false,
  refetchOnReconnect: false,
  refetchOnMount: true,
});

// 响应式更新表格数据
watch(
  data,
  (newData: any) => {
    const responseData = newData?.data;
    if (responseData?.columns?.length) {
      table.value.list = responseData.columns;
      total.value = responseData.total;
    } else {
      table.value.list = [];
      total.value = 0;
      isNoData.value = !!searchValueDebounced.value;
    }
  },
  { deep: true },
);

const filter = {
  attrs: {
    size: 'small',
    labelWidth: '80px',
    placeholder: props?.config?.text?.searchPlaceholder || '搜索专栏名称',
  },
};

const table = ref({
  attrs: {
    'row-key': 'id',
    tableLayout: 'auto',
    hover: true,
    loading: isLoading,
  },
  pagination: pageInfo.value,
  columns: [
    {
      title: props?.config?.text?.table?.name || '专栏名称',
      width: 364,
      colKey: 'name',
    },
    {
      title: props?.config?.text?.table?.informationCount || '关联资讯条数',
      width: 364,
      colKey: 'informationCount',
    },
    {
      title: '操作',
      width: 200,
      cell: (h: any, { row }: { row: any }) => {
        return (
          <div class="operate">
            <t-button variant="text" onClick={() => editFn('edit', row)}>
              编辑
            </t-button>
            {total.value > 1 && (
              <t-button variant="text" onClick={() => delFn(row)}>
                删除
              </t-button>
            )}
          </div>
        );
      },
    },
  ],
  list: [],
});

const getData = () => {
  refetch();
};
</script>

<style scoped lang="less">
.warp {
  position: relative;
  height: 100%;
  overflow-y: scroll;
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 0 8px 0 16px;
  margin-right: 2px;
  :deep(.RTable) {
    flex: 1;
    .toolbar-wrap {
      padding-bottom: 16px;
      border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
      .sort {
        font-weight: 600;
        margin-right: 8px;
      }
    }
    .operate {
      margin-left: -8px;
      .t-button {
        background-color: initial !important;
        color: #4d5eff !important;
        font-size: 14px;
        padding-left: 8px;
        padding-right: 8px;
        &:hover {
          background-color: #eaecff !important;
        }
      }
    }
  }
}
</style>
