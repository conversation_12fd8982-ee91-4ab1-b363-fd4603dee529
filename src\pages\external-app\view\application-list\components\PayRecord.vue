<template>
  <t-drawer
    class="payment-record-drawer"
    :header="isRefund ? '退款记录' : '支付记录'"
    :size="472"
    :visible="visible"
    :footer="renderFooter"
    :close-btn="true"
    @close="handleClose"
  >
    <RScrollArea class="px-12">
      <t-loading :loading="isLoading" show-overlay>
        <div class="flex flex-col gap-12">
          <div v-if="isRefund" class="bg-white p-12 rounded-8">
            <div class="flex items-center gap-8">
              <div class="w-3 h-16 rounded-8 bg-#4D5EFF"></div>
              <div class="font-600 text-16 text-#1A2139">退款信息</div>
            </div>
            <!-- 退款信息 -->
            <div class="flex flex-col gap-12 pt-16">
              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">退款方式</div>

                <div class="text-14 text-#1A2139">
                  <span>{{ data?.refund?.refund_method ?? '-' }}</span>
                </div>
              </div>
              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">开户行</div>

                <div class="text-14 text-#1A2139">
                  <span>{{ data?.refund?.opening_bank ?? '-' }}</span>
                </div>
              </div>
              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">银行账号</div>

                <div class="text-14 text-#1A2139">
                  <span>{{ data?.refund?.bank_account ?? '-' }}</span>
                </div>
              </div>
              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">退款时间</div>

                <div class="text-14 text-#1A2139">
                  <span>{{ data?.refund?.refund_time ?? '-' }}</span>
                </div>
              </div>
              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">退款金额</div>

                <div class="text-14 text-#D54941 font-600">
                  <span>
                    {{ data?.currency === 'CNY' ? '¥' : 'MOP'
                    }}{{ addCommasToNumber(Number(data?.refund?.refund_amount ?? 0).toFixed(2)) ?? '-' }}
                  </span>
                </div>
              </div>
              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">退款凭证</div>

                <div class="flex gap-8 flex-wrap">
                  <div
                    v-for="(item, index) in data?.refund?.refund_voucher"
                    :key="index"
                    @click="handlePreview({ images: data?.refund?.refund_voucher || [], index, url: item })"
                  >
                    <img :src="item" class="w-78 h-78 rounded-8 object-cover" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 应用信息 -->
          <div class="bg-white p-12 rounded-8">
            <div class="flex items-center gap-8">
              <div class="w-3 h-16 rounded-8 bg-#4D5EFF"></div>
              <div class="font-600 text-16 text-#1A2139">应用信息</div>
            </div>

            <div class="flex flex-col gap-12 pt-16">
              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">应用名称</div>

                <div class="text-14 text-#1A2139">
                  <span>{{ data?.name ?? '-' }}</span>
                </div>
              </div>

              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">保证金金额</div>

                <div class="text-14 text-#D54941 font-600">
                  <span>
                    {{ data?.currency === 'CNY' ? '¥' : 'MOP'
                    }}{{ addCommasToNumber(Number(data?.bond_amount ?? 0).toFixed(2)) ?? '-' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 支付信息 -->
          <div class="bg-white p-12 rounded-8">
            <div class="flex items-center gap-8">
              <div class="w-3 h-16 rounded-8 bg-#4D5EFF"></div>
              <div class="font-600 text-16 text-#1A2139">支付信息</div>
            </div>

            <div class="flex flex-col gap-12 pt-16">
              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">保证金编号</div>

                <div class="text-14 text-#1A2139">
                  <span>{{ data?.bond_sn ?? '-' }}</span>
                </div>
              </div>

              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">支付状态</div>

                <div class="text-14 text-#1A2139">
                  <span>已支付</span>
                </div>
              </div>

              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">付款时间</div>

                <div class="text-14 text-#1A2139">
                  <span>{{ data?.order?.payed_at ?? '-' }}</span>
                </div>
              </div>

              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">付款人</div>

                <div class="text-14 text-#1A2139">
                  <span>{{ data?.order?.pay_openid_name ?? '-' }}</span>
                </div>
              </div>

              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">付款方式</div>

                <div class="text-14 text-#1A2139">
                  <span>
                    {{ data?.order?.pay_method }}{{ data?.order?.pay_channel ? `（${data?.order?.pay_channel}）` : '' }}
                  </span>
                </div>
              </div>

              <div class="flex gap-16">
                <div class="w-70 text-14 text-#828DA5 flex-shrink-0">收款方</div>

                <div class="text-14 text-#1A2139">
                  <span>{{ data?.order?.receive_team_name ?? '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </t-loading>
    </RScrollArea>
  </t-drawer>
</template>

<script setup lang="tsx">
import { IOptions } from '@axios/types';
import { getExternalAppInfo } from '@pages/external-app/api';
import { useQuery } from '@tanstack/vue-query';
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { Utils } from '@utils/index';
import { addCommasToNumber } from '@utils/pay';
import sdk from '@lynker-desktop/web';
import { RScrollArea } from '@rk/unitPark';

const visible = ref(false);
const app_id = ref<number>();
const isRefund = ref<boolean>(false);

const router = useRouter();

const options = computed<IOptions>(() => ({
  env: (router.currentRoute.value.query.env as string) || Utils.config.env,
  teamId: (router.currentRoute.value.query.teamId as string) || '',
  openId: (router.currentRoute.value.query.openId as string) || '',
  token: (router.currentRoute.value.query.token as string) || Utils.config.token,
}));

const { data, isLoading } = useQuery({
  queryKey: ['payRecord', options, app_id],
  queryFn: async () => {
    if (!app_id.value) {
      return;
    }
    const response = await getExternalAppInfo(app_id.value, options.value);
    return response.data;
  },
  enabled: computed(() => !!app_id.value),
});

const handlePreview = ({ images, index, url }: { images: string[]; index: number; url: string }) => {
  console.log('images', images);
  console.log('index', index);
  console.log('url', url);
  if (sdk.isDesktop) {
    sdk.previewImage({
      images,
      index,
      url,
    });
  }
};

const handleClose = () => {
  visible.value = false;
  app_id.value = undefined;
};

const renderFooter = () => {
  return (
    <div class="flex justify-end items-center">
      <t-button variant="outline" theme="default" class="w-80" onClick={handleClose}>
        关闭
      </t-button>
    </div>
  );
};

defineExpose({
  open(id: number, refund?: boolean) {
    visible.value = true;
    app_id.value = id;
    isRefund.value = refund ?? false;
  },
  close: handleClose,
});
</script>

<style lang="less">
.payment-record-drawer {
  .t-drawer__body {
    background-color: #f5f8fe;
    padding: 12px 0;
  }

  .t-drawer__footer {
    border-radius: 0;
  }

  .rk-scroll-area__scrollbar--vertical {
    padding: 3px;
  }

  .rk-scroll-area__thumb--vertical {
    width: 6px;
    border-radius: 4px;
    background-color: rgba(26, 33, 57, 0.36);
  }
}
</style>
