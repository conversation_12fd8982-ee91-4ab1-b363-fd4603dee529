<template>
  <div class="cantainer">
    <div class="title">
      <div>{{ t("contacts.follow") }}</div>
      <div class="select-identify-wrap" style="display: flex; align-items: center">
        <t-dropdown maxColumnWidth="350" trigger="click">
          <div style="display: flex; align-items: center" v-if="curCardInfo" class="cur-selected">
            <avatar :userName="curCardInfo.nickname" :imageUrl="curCardInfo.avatar" avatarSize="24px" shape="circle" />
            <div class="user-name">{{ curCardInfo.nickname }}</div>
            <span
              :class="[
                'changeCardOrgStyle',
                cardIdType(curCardInfo?.cardId) === 'outer' ? 'imPop-create-group-out' : 'imPop-create-group-inner',
              ]"
              v-if="curCardInfo.teamName"
            >
              {{ `${curCardInfo.teamName}` }}{{ cardIdType(curCardInfo?.cardId) === 'platform' ? '(平台)':'' }}
            </span>
            <span v-else class="changeCardOrgStyle imPop-create-group-personal">
              {{ t("contacts.guide_profile") }}
            </span>
            <iconpark-icon name="iconarrowdown" class="arrow-down" />
          </div>
          <t-dropdown-menu>
            <t-dropdown-item v-for="(items, indexs) in allGroupCards" class="imPop-create-group-dialog-dropdownItem">
              <div class="item-title">
                <div calss="group-name">{{ items.group }}</div>
                <div class="expand" @click.stop="expandList(indexs, items.expand)" v-if="items.children.length > 2">
                  <template v-if="!items.expand">
                    <span>{{ $t("square.action.unfold") }}</span>
                    <iconpark-icon class="iconarrowdwon" name="iconarrowdwon"></iconpark-icon>
                  </template>
                  <template v-else>
                    <span>{{ $t("square.action.fold") }}</span>
                    <iconpark-icon class="iconarrowdwon" name="iconarrowup"></iconpark-icon>
                  </template>
                </div>
              </div>
              <div
                :class="['group-item', item.cardId === curCardInfo.cardId ? 'activeCard' : '']"
                v-for="(item, index) in items.children"
                :key="index"
                @click="changeCard(item)"
                v-show="index < 3 || items.expand"
              >
                <div style="display: flex; flex-direction: row; align-items: center">
                  <avatar
                    avatar-size="24px"
                    :image-url="item.avatar"
                    :user-name="item.nickname"
                    roundRadius
                    style="margin-right: 8px"
                  ></avatar>
                  {{ item.nickname }}
                  <div
                    v-if="item.teamId"
                    :class="[
                      'changeCardOrgStyle',
                      cardIdType(item?.cardId) === 'outer' ? 'imPop-create-group-out' : 'imPop-create-group-inner',
                    ]"
                    style="margin-left: 4px"
                  >
                    {{ item?.teamName }}
                  </div>
                  <div v-else class="changeCardOrgStyle imPop-create-group-personal" style="margin-left: 4px">
                    {{ t("contacts.guide_profile") }}
                  </div>
                </div>
                <img
                  style="width: 20px; height: 20px"
                  v-if="item.cardId === curCardInfo.cardId"
                  src="@renderer/assets/im/radio_checkbox.svg"
                  alt=""
                />
              </div>
              <div v-if="indexs !== allGroupCards.length - 1" class="group-line"></div>
            </t-dropdown-item>
          </t-dropdown-menu>
        </t-dropdown>
      </div>
      <!-- <selectIdentify @changeCard="changeCard" /> -->
    </div>
    <div v-if="!options.length || empty" class="recent-empty">
      <img src="@renderer/assets/emptydata.png" alt="" />
      <div class="tip">暂无数据</div>
    </div>
    <div v-else :class="['t-menu-container', isNotMac ? 'scrollbar' : '']">
      <t-menu theme="light" defaultValue="dashboard" style="width: 100%">
        <template
          v-for="item in options"
          :key="item.cardId">
          <t-menu-item
            :value="item.cardId"
            @mouseover="showAct(item)"
            @mouseleave="hiddenAct"
            @click="showCard(item)"
          >
            <template #icon>
              <avatar
                class="avatar-icon"
                roundRadius
                avatarSize="44px"
                :imageUrl="item.avatar"
                :userName="item.title || item.name"
              />
            </template>
            <div class="user-info">
              <div class="user-name">
                <div>{{ item.name }}</div>
                <RelationTag :relation="item.relationship" />
              </div>
              <div class="post" v-if="item.departmentName">{{ item.departmentName }}/{{ item.jobName }}</div>
              <div class="multi-id" v-if="!(cardIdType(item.cardId) === 'personal' && 'personal'=== cardIdType(curCardInfo.cardId))" :class="{'inner':['inner','platform'].includes(cardIdType(item.cardId))}">

                <template v-if="cardIdType(item.cardId) === 'outer' || cardIdType(curCardInfo.cardId) === 'outer'" >
                  <div>{{ curCardInfo?.teamName || '个人' }}</div>
                  <!-- <SvgIcon name="icon_standing" class="svg-icon" /> -->
                  <i class="i-svg:icon_standing svg-icon" />
                  <div>{{ item.teamName || '个人' }}</div>
                </template>
                <div v-else>{{ item.teamName || curCardInfo.teamName}} {{ cardIdType(item.cardId) === 'platform'?'(平台)':'' }}</div>
              </div>
              <!-- <MultiIdTag
                :myCard="{ cardId: curCardInfo.cardId, teamName: curCardInfo.teamName }"
                :anotherCard="item"
                from="follow"
              ></MultiIdTag> -->
            </div>
            <div class="act-groups" v-if="item.cardId === hoverValue">
              <div v-if="!item.relationship">
                <t-button variant="base" theme="primary" @click.stop="addContact(item)">
                  {{ t("contacts.addStatusAdd") }}
                </t-button>
              </div>
              <div v-else>
                <!-- <t-button class="mr-12" shape="circle" theme="primary" @click.stop="vioce(item)">
                  <template #icon>
                    <t-tooltip :content="t('zx.contacts.voiceCall')" :show-arrow="false" placement="bottom">
                      <img src="@renderer/assets/svg/voicefill_new.svg" alt="" />
                    </t-tooltip>
                  </template>
                </t-button> -->
                  <!--                {{ t('zx.contacts.voice') }}-->
                  <!--                {{ t('zx.contacts.video') }}-->
                  <!--                {{ t('contacts.msg') }}-->
                <!-- <t-button class="mr-12" shape="circle" theme="primary" @click.stop="video(item)">
                  <template #icon>
                    <t-tooltip :content="t('zx.contacts.videoCall')" :show-arrow="false" placement="bottom">
                      <img src="@renderer/assets/svg/videofill_new.svg" alt="" />
                    </t-tooltip>
                  </template>
                </t-button> -->
                <t-button shape="circle" theme="primary" @click.stop="msg(item)">
                  <template #icon>
                    <t-tooltip :content="t('zx.contacts.msgCall')" :show-arrow="false" placement="bottom">
                      <img src="@renderer/assets/svg/commentfill_new.svg" alt="" />
                    </t-tooltip>
                  </template>
                </t-button>
              </div>
            </div>
          </t-menu-item>
        </template>
      </t-menu>
    </div>

    <apply-dialog v-model:visible="applyVisible" :cardInfo="cardInfo" :myId="curCardInfo.cardId" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import applyDialog from "@renderer/views/identitycard/dialog/applyContact.vue";
import { cardData } from "@renderer/views/identitycard/data";
import avatar from "@renderer/components/kyy-avatar/index.vue";
import MultiIdTag from "@/components/contacts/MultiIdTag.vue";
import RelationTag from "@renderer/components/contacts/relationTag.vue";
import SvgIcon from '@/components/SvgIcon.vue';

import { getStaff } from "@renderer/utils/auth";
import {
  followList,
  innerCardsDetails,
  outerCardsDetails,
  personalCardsDetails,
  platformCardsDetails,
} from "@renderer/api/contacts/api/follow";
import { searchPairs, getNoteList } from "@renderer/api/contacts/api/common";
import { getAllGroupCardsByGroupType } from "@renderer/components/selectMember/group-data";

import { filterCard } from "../utils";
import { useI18n } from "vue-i18n";
import { isNotMac } from "@renderer/views/zhixing/util";
import { cardIdType } from "@renderer/views/identitycard/data";
import { openChat } from "@/utils/share";
import { videoMsg, voiceMsg } from "../utils";

import { useContactsStore } from "@renderer/store/modules/contacts";
const contactStore = useContactsStore();
import { getChangePlatformData } from "@renderer/utils/apiInit";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();
const applyVisible = ref(false);
const options = ref([]);
const hoverValue = ref("");
const empty = ref(false);
const curCardInfo = ref({
  avatar: "",
  openId: "",
  teamName: "",
  nickname: "",
  cardId: "",
  type: "",
}); //当前选中身份卡
const followListData = ref();
const listCardId = ref();
const cardInfo: any = ref({});


/**
 *  获取流程有点复杂。
 * 1.先获取所有关注的cardId list
 * 2.通过cardId list分类，区分个人身份卡id，外部内部身份卡。批量查询身份信息list
 * 3.获取备注信息赋值对应身份信息
 * 4.获取关系信息relationship "FRIEND"，PLATFORM_FRIEND，CO_WORKER
 *
 * 建立关系  选中内部和关注内部一组，个人外部一组
 *  */


//切换身份
const changeCard = (cardInfo) => {
  emptyRelation();
  curCardInfo.value = cardInfo;
  getFollowList();
};

const emptyRelation = () => {
  followListData.value = followListData.value?.map((v) => {
    // 同事关系才能建立聊天，不是同事关系relationship为空展示添加好友。
    if (v.relationship !== "CO_WORKER" && v.relationship !=="PLATFORM_FRIEND") {
      v.relationship = "";
    }
    return v;
  });
};
const getFollowList = () => {
  followList({ cardId: curCardInfo.value.cardId }).then(({ data }) => {
    // 获取所有关注的cardId
    console.log(data,curCardInfo.value.cardId, "followList cardIds");
    if (data.code === 0) {
      listCardId.value = data.data.list;
      const cards = listCardId.value.map((v) => v.cardId);
      contactStore.setFollowList(cards, true);
      data.data.list.length ? getCardsDetail(data.data.list) : (empty.value = true);
    }
  });
};
const getCardsDetail = async (list) => {
  // 区分个人身份卡id，外部内部身份卡
  const { personalCards, innerCards, outerCards, platformCards } = filterCard(list);
  let promiseAll = [];
  if(['inner','platform'].includes(cardIdType(curCardInfo.value.cardId))){
    innerCards.length && promiseAll.push(innerCardsDetails({ ids: innerCards }));
    platformCards.length && promiseAll.push(platformCardsDetails({ ids: platformCards }));
  }else{
    personalCards.length && promiseAll.push(personalCardsDetails({ ids: personalCards }));
    outerCards.length && promiseAll.push(outerCardsDetails({ ids: outerCards }));
  }
 // 获取身份卡信息
  Promise.all(promiseAll).then(async (res) => {
    console.log(res, "promise all cardsDetails");
    let dataList0 = [],
      dataList1 = [];
    // 判断是否获取个人身份卡信息
    if (['outer','personal'].includes(cardIdType(curCardInfo.value.cardId)) && personalCards.length && res[0].status === 200) {
      dataList0 = await res[0].data?.profiles.reduce(async (acc, v) => {
        const emo = await acc;
        // --- 先这样吧，懒得管了，莫名其妙的设计(好友不显示好友标签，好友+同事显示好友标签) ---
        // const { data } = await staffOpenid({openId:v.openid});
        // const isCoWorker = ~data.data.staff.findIndex(v => ~getStaff().findIndex(my => my.teamId === v.teamId));
        // 取消循环调用 跟app同步 取消好友标签展示 一直false
        const isCoWorker = false;
        const item = {
          avatar: v.avatar || "",
          team: "",
          cardId: v.openid,
          jobName: "",
          title: v.title,
          name: v.title,
          relationship: "",
          coWorker: isCoWorker,
        };
        emo.push(item);
        return emo;
      }, []);
    } else if (res[0]?.data.code === 0) {
      dataList0 = await handleDataList(res[0].data.data);
    }
    if (res[1]?.data.code === 0) {
      dataList1 = await handleDataList(res[1].data.data);
    }
    followListData.value = [...dataList0, ...dataList1];
    if(followListData.value.length > 0){
      empty.value = false
      replaceNote(list);
    }else{
      empty.value = true
    }
  });
};
const replaceNote = (list) => {
  // 获取备注信息
  getNoteList({ cardIds: list.map((v) => v.cardId) }).then((res) => {
    console.log(res, "getNoteList");
    if (res.data.code === 0) {
      res.data.data.forEach((item) => {
        const itemFollow = followListData.value.find((v) => v.cardId === item.cardId);
        if(itemFollow){
          itemFollow.CardName = itemFollow?.name;
          itemFollow.name = item.remarks ? item.remarks : itemFollow?.name;
        }
      });
      getPairs(list);
    }
  });
};
const handleDataList = async (dataList) => {
  // $开头的身份卡肯定是同事关系
  const showList = await dataList.reduce(async (acc, v) => {
    const emo = await acc;
    // --- 先这样吧，懒得管了，莫名其妙的设计(好友不显示好友标签，好友+同事显示好友标签) ---
    // const { data } = await staffOpenid({openId: v?.openId || v?.openid});
    // const isCoWorker = ~data.data.staff.findIndex(v => ~getStaff().findIndex(my => my.teamId === v.teamId));
    // 取消循环调用 跟app同步 取消好友标签展示 一直false
    const isCoWorker = false;
    // const rela = await searchPairs({mains: uuids.value, peers: [cardId]})
    // --------
    const item = v?.platform?.id
     ? {
        avatar: v?.platform?.avatar || "",
        team: v?.team?.fullName || '',
        teamName: v?.team?.fullName || '',
        cardId: v?.platform?.cardId || v?.platform?.card_id || ('PT' + v?.platform?.id) || '',
        departmentName: v.position?.[0]?.departmentName || "",
        jobName: (/^PT/.test(v?.platform?.cardId || v?.platform?.card_id) && v.position?.[0]?.jobName) || "",
        name: v?.platform?.name || '',
        teamId: v.team?.teamId || v?.platform?.team_id || '',
        relationship: /^PT/.test(v?.platform?.cardId || v?.platform?.card_id) ? "PLATFORM_FRIEND" : "",
        coWorker: isCoWorker,
      }
     : {
        avatar: v.avatar || "",
        team: v.team,
        teamName: v.team,
        cardId: v.cardId,
        departmentName: v.position[0]?.departmentName || "",
        jobName: (~v.cardId.indexOf("$") && v.position[0]?.jobName) || "",
        name: v.name,
        teamId: v.teamId,
        relationship: ~v.cardId.indexOf("$") ? "CO_WORKER" : "",
        coWorker: isCoWorker,
      };
    emo.push(item);
    return emo;
  }, []);
  return showList;
};

const getPairs = (list) => {
  if (!list?.length) return;
  // 获取关系信息
  const peers = list.reduce((acc, cur) => {
    if (!~cur.cardId.indexOf("$")) {
      acc.push(cur.cardId);
    }
    return acc;
  }, []);
  searchPairs({ mains: [curCardInfo.value.cardId], peers }).then((res) => {
    if (res.status === 200) {
      const rela = res.data.rela;
      rela?.FRIEND && findRelationship(rela, "FRIEND");
      rela?.BUSINESS && findRelationship(rela, "BUSINESS");
      rela?.CO_WORKER && findRelationship(rela, "CO_WORKER");
      rela?.PLATFORM_FRIEND && findRelationship(rela, "PLATFORM_FRIEND");
      listSort(list);
    }
  });
};
const listSort = (list) => {
  // 过滤选中身份卡同内部组织，或者个人外部关系一组
  let follows = []
  if((['outer','personal'].includes(cardIdType(curCardInfo.value.cardId)))){
    follows =  followListData.value
  }else{
    follows = []
    followListData.value.forEach(item => {
      if( item.teamId === curCardInfo.value.teamId){
        follows.push(item)
      }
    })
  }
  if(!follows.length){
    options.value = []
    return
  }
    // 按照给的关注身份卡列表数据重新排序
  options.value = list.reduce((acc, cur) => {
    const followCard = follows.find((v) => v.cardId === cur.cardId);
    followCard && acc.push(followCard);
    return acc;
  }, []);
};
const findRelationship = (rela, key) => {
  rela[key].pair_ids.forEach((ele) => {
    const findCard = followListData.value.find((v) => v.cardId === ele.peer);
    findCard.relationship = key;
  });
};
const msg = (item) => {
  openChat({ main: getMyId(item), peer: item.cardId });
};
const video = (item) => {
  videoMsg(item.cardId, getMyId(item));
};
const vioce = (item) => {
  voiceMsg(item.cardId, getMyId(item));
};
const showAct = (item) => {
  hoverValue.value = item.cardId;
};
const hiddenAct = () => {
  hoverValue.value = "";
};
const showCard = (item) => {
  ipcRenderer.invoke("identity-card", { cardId: item.cardId, myId: getMyId(item) });
};
const getMyId = (item) => {
  let myId = curCardInfo.value.cardId; //除内部用选中身份
  if (cardIdType(item.cardId) === "inner") {
    myId = getStaff(true).find((v) => v.teamId === item.teamId).uuid;
  }
  return myId;
};
const addContact = async (item) => {
  cardInfo.value = await cardData(item.cardId);
  applyVisible.value = true;
};

const followListener = (event,arg) => {
  console.log('====>followListener', 22);
  getFollowList();
};

// 获取所有身份卡
const allGroupCards = ref([]);
onMounted(async () => {
  let allCards = getAllGroupCardsByGroupType({});
  // 专属名称转换 team 名字
  allGroupCards.value = await getChangePlatformData(allCards);
  curCardInfo.value = allGroupCards.value[0]?.children[0];
  getFollowList();

  ipcRenderer.on("update-contact-list", followListener);
});
onUnmounted(() => {
  ipcRenderer.off("update-contact-list", followListener);
});
// 选人分类收起展开
const expandList = (index, expand) => {
  allGroupCards.value[index].expand = !expand;
};
</script>

<style lang="less" scoped>
.mr-12 {
  margin-right: 12px;
}
.arrow-down{
  color: var(--icon-kyy_color_icon_deep, #516082);
  font-size: 20px;
}
.cantainer {
  width: 100%;
  position: relative;
  .title {
    color: var(--text-kyy-color-text-1, #1a2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    margin: 20px 24px 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .user-name {
      font-size: 14px;

      color: #13161b;
      line-height: 22px;
    }
  }
  .recent-empty {
    width: 100%;
    position: absolute;
    top: 64px;
    left: 8px;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 200px;
      height: 200px;
    }
    .tip {
      color: var(--text-kyy_color_text_2, #516082);
    }
  }
  .t-menu-container {
    width: calc(100% - 48px);
    position: absolute;
    top: 64px;
    left: 24px;
    bottom: 0;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  :deep(.t-default-menu__inner .t-menu) {
    padding: 0;
  }
  :deep(.t-default-menu:not(.t-menu--dark) .t-menu__item.t-is-active:not(.t-is-opened)) {
    background: transparent !important;
    color: #13161b !important;
    &:hover {
      background: #f0f8ff !important;
      border-radius: 4px;
    }
  }
  :deep(.t-default-menu .t-menu__item) {
    position: relative;
    font-size: 14px;

    color: #13161b;
    line-height: 22px;
    height: 68px !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_list_hover, #f3f6fa) !important;
    }
  }
  .avatar-icon {
    margin-right: 12px;
  }
  .user-info {
    .user-name {
      display: flex;
      align-items: flex-start;
    }
    .post {
      font-size: 12px;
      margin-top: 2px;
      color: #717376;
      line-height: 16px;
    }
  }
  .act-groups {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    .t-button {
      min-width: auto;
    }
    .t-button--shape-circle {
      border: none;
      background-color: transparent;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      img {
        width: 36px;
        height: 36px;
      }
    }
  }
}
.item-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 30px;
  padding: 0 8px;
  color: var(--text-kyy_color_text_2, #516082);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  .expand {
    display: flex;
    align-items: center;
    color: var(--brand-kyy-color-brand-default, #4d5eff);
    cursor: pointer;
    .iconarrowdwon {
      font-size: 16px;
    }
  }
}
.cur-selected {
  display: flex;
  padding: 4px 4px 4px 8px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  &:hover {
    background: var(--bg-kyy_color_bg_list_foucs, #e1eaff);
  }
}
.multi-id {
  display: flex;
  align-items: center;
  color: var(--error_kyy_color_warning_default, #fc7c14);
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  margin-top: 2px;
  .svg-icon {
    margin: 0 2px;
    // height: 16px;
    // width: 16px;
    font-size: 16px;
  }
}
.inner{
  color: #49BBFB;

}
</style>
