<template>
  <div class="content-section">
    <div class="section-title">{{ sectionTitle }}活动</div>
    <div class="activity-card">
      <div class="activity-thumbnail">
        <img class="thumbnail-image" :src="thumbnailUrl" alt="活动缩略图">
      </div>
      <div class="activity-info">
        <div class="activity-title ellipsis-1">{{ title }}</div>
        <div class="activity-time ellipsis-1">{{ time }}</div>
        <div class="activity-location ellipsis-1">{{ location }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  sectionTitle: string // '申请' 或 '推广'
  thumbnailUrl: string
  title: string
  time: string
  location: string
}

defineProps<Props>();
</script>

<style scoped>
.content-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.section-title {
  width: 88px;
  height: 22px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.571;
  color: #828da5;
}

.activity-card {
  display: flex;
  align-items: center;
  padding: 8px;
  background: #f5f8fe;
  border-radius: 8px;
  gap: 12px;
}

.activity-thumbnail {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  gap: -20px;
}

.thumbnail-image {
  width: 72px;
  height: 72px;
  border-radius: 8px;
  object-fit: cover;
}

.activity-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.activity-title {
  width: 100%;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #1a2139;
}

.activity-time {
  width: 100%;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #828da5;
}

.activity-location {
  width: 100%;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #828da5;
}
</style>
