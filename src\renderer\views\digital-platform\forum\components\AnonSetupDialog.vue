<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :header="false"
    :footer="false"
    :close-btn="false"
    width="343px"
    :destroy-on-close="true"
    :close-on-overlay-click="false"
    placement="center"
    class="anon-setup-dialog"
    attach="body"
  >
    <!-- 跳过按钮 -->
    <div class="skip-button-container">
      <div class="skip-button" @click="handleSkip">
        <span>跳过</span>
      </div>
    </div>

    <!-- 弹窗主体 -->
    <div class="dialog-container">
      <!-- Vector装饰 -->
      <div class="vector-decoration"></div>

      <!-- 文案区域 -->
      <div class="content-text">
        <h3 class="title">设置匿名资料</h3>
        <div class="description">
          <p class="desc-main">在论坛畅所欲言，无须担心泄露隐私。</p>
          <div>
            <t-tooltip :content="tooltipContent" placement="bottom">
              <span class="desc-link" @click="handleHowToUse">如何使用"匿名"？</span>
            </t-tooltip>
          </div>
        </div>
      </div>

      <!-- 头像区域 -->
      <div class="avatar-section">
        <div class="avatar-container" @click="uploadAvatarRef?.open(avatarUrl)">
          <div class="default-avatar">
            <div v-if="!avatarUrl && !nickname" class="avatar-icon">
              <iconpark-icon size="72" name="iconpeople-i2n6mh9m"></iconpark-icon>
            </div>
            <div v-else class="avatar-icon">
              <KyyAvatar
                class="avatar"
                avatar-size="100px"
                shape="circle"
                :image-url="avatarUrl"
                :user-name="nickname"
              />
            </div>

            <div class="camera-button" @click="handleAvatarChange">
              <div class="camera-icon">
                <iconpark-icon size="18" name="Frame"></iconpark-icon>
              </div>
            </div>
          </div>
        </div>

        <UploadAvatar ref="uploadAvatarRef" @confirm="uploadConfirm" />
      </div>

      <!-- 昵称输入区域 -->
      <div class="nickname-section">
        <label class="nickname-label">昵称</label>
        <div class="nickname-input-container">
          <t-input
            type="text"
            class="nickname-input"
            placeholder="请输入匿名昵称"
            v-model="nickname"
            maxlength="15"
            clearable
            @blur="handleBlur"
          />
        </div>

        <div class="nickname-tip" :class="{ 'nickname-tip--error': nicknameError }">
          <p>请输入匿名昵称</p>
        </div>
      </div>

      <!-- 确定按钮 -->
      <div
        class="confirm-button"
        :class="{ 'confirm-button--disabled': isConfirmDisabled || isLoading }"
        @click="handleConfirm"
      >
        <span>确定</span>
      </div>

      <!-- 底部装饰线 -->
      <div class="bottom-decoration"></div>
    </div>
  </t-dialog>
</template>

<script setup lang="tsx">
import { ref, computed } from 'vue';
import UploadAvatar from '@/components/common/UploadAvatar.vue';
import KyyAvatar from '@/components/kyy-avatar/index.vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useForumStore } from '../store';
import { useMutation } from '@tanstack/vue-query';
import { anonSetup } from '@renderer/api/forum/user';
import to from 'await-to-js';
import { AxiosError, AxiosResponse } from 'axios';

interface Emits {
  (e: 'skip'): void;
  (e: 'confirm', data: { name: string; avatar: string }): void;
  (e: 'how-to-use'): void;
  (e: 'avatar-change'): void;
}

const emit = defineEmits<Emits>();

const forumStore = useForumStore();

const { mutateAsync, isLoading } = useMutation({
  mutationFn: (data: { name: string; avatar: string }) => anonSetup(forumStore.platformType, data),
});

const uploadAvatarRef = ref<InstanceType<typeof UploadAvatar>>(null);

const nickname = ref('');

const avatarUrl = ref('');

const nicknameError = ref(false);

// 使用计算属性来同步TDesign Dialog的visible状态
const dialogVisible = ref(false);

// 确定按钮是否禁用
const isConfirmDisabled = computed(() => {
  return !nickname.value.trim();
});

// 提示内容
const tooltipContent = () => {
  return (
    <div>
      <p>匿名使用场景：</p>
      <p>发帖或评论时，可切换匿名身份进行操作；</p>
      <p>其他成员查看时，将显示对应匿名主页。</p>
    </div>
  );
};

// 处理输入框失去焦点时的事件
const handleBlur = () => {
  nickname.value = nickname.value.trim();

  if (!nickname.value.trim()) {
    nicknameError.value = true;
  } else {
    nicknameError.value = false;
  }
};

// 处理弹窗关闭事件
const handleClose = () => {
  nickname.value = '';
  avatarUrl.value = '';
  nicknameError.value = false;

  dialogVisible.value = false;
};

const handleSkip = () => {
  emit('skip');

  handleClose();
};

const uploadConfirm = (url: string) => {
  avatarUrl.value = url;
};

const handleConfirm = async () => {
  if (isConfirmDisabled.value) {
    return;
  }

  if (!nickname.value.trim()) {
    MessagePlugin.error('请输入昵称');
    nicknameError.value = true;
    return;
  }

  if (forumStore.platformType !== 'uni') {
    MessagePlugin.error('当前平台咱不支持匿名身份');
    return;
  }

  const [err] = await to<AxiosResponse, AxiosError<{ message: string }>>(
    mutateAsync({
      name: nickname.value,
      avatar: avatarUrl.value || '',
    }),
  );

  if (err) {
    MessagePlugin.error(err.response?.data?.message || '设置匿名身份失败，请稍后再试');
    return;
  }

  MessagePlugin.success('设置匿名身份成功');

  try {
    await forumStore.getCardList();
    await forumStore.getOwnerId(forumStore.currCard?.card);
  } catch (error) {
    MessagePlugin.success('更新身份失败，请手动刷新');
  }
  emit('confirm', {
    name: nickname.value,
    avatar: avatarUrl.value,
  });

  handleClose();
};

const handleHowToUse = () => {
  emit('how-to-use');
};

const handleAvatarChange = () => {
  emit('avatar-change');
};

defineExpose({
  open(formData?: { avatarUrl: string; nickname: string }) {
    avatarUrl.value = formData?.avatarUrl ?? '';
    nickname.value = formData?.nickname ?? '';

    dialogVisible.value = true;
  },
  close: handleClose,
});
</script>

<style scoped lang="less">
/* TDesign Dialog 自定义样式 */
.skip-button-container {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 12px;
}

.skip-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 16px;
  background: rgba(255, 255, 255, 0.16);
  border-radius: 99px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.skip-button:hover {
  background: rgba(255, 255, 255, 0.24);
}

.skip-button span {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.57;
  color: #ffffff;
  text-align: center;
}

.dialog-container {
  position: relative;
  width: 343px;
  background: linear-gradient(180deg, #f9f3ff 0%, #d7c4fb 100%);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
}

.vector-decoration {
  position: absolute;
  top: 23px;
  left: 57.5px;
  width: 86px;
  height: 28px;
  background: url('@/assets/forum/vector.png') no-repeat;
  background-size: 100% 100%;
  pointer-events: none;
}

.content-text {
  position: relative;
  width: 100%;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: start;
  gap: 8px;
}

.title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 18px;
  line-height: 1.44;
  text-align: center;
  color: #1a2139;
  margin: 0;
}

.description {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.desc-main {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.57;

  color: #516082;
  margin: 0;
}

.desc-link {
  display: inline-block;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.57;

  color: #4d5eff;
  margin: 0;
  cursor: pointer;
}

.desc-link:hover {
  opacity: 0.8;
}

.avatar-section {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  align-self: stretch;
  gap: 4px;
  padding: 24px 0;
}

.avatar-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.default-avatar {
  width: 100px;
  height: 100px;
  background: #bae7fa;
  border-radius: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 50px;
}

.camera-button {
  position: absolute;
  right: -4px;
  bottom: 4px;
  width: 28.44px;
  height: 28.44px;
  background: #1a2139;
  border-radius: 14.22px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 99;
}

.camera-button:hover {
  background: #2a3149;
}

.camera-icon {
  width: 17.78px;
  height: 17.78px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nickname-section {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-self: stretch;
  gap: 4px;
  padding: 12px 0 18px;
}

.nickname-tip {
  color: #d54941;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  opacity: 0;
  transition: all 0.2s ease;
}

.nickname-label {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.67;
  color: #1a2139;
}

.nickname-input-container {
  display: flex;
  align-items: center;
  align-self: stretch;
  gap: 4px;
  padding: 3px 12px;
  background: linear-gradient(90deg, #edf8fe 0%, #faf7fe 100%);
  border-radius: 99px;
}

.nickname-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.57;
  color: #1a2139;
}

.nickname-input::placeholder {
  color: #acb3c0;
}

.confirm-button {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  padding: 12px 80px;
  background: #7030f6;
  border-radius: 99px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5) inset;
}

.confirm-button:hover:not(.confirm-button--disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(125, 91, 227, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.5) inset;
}

.confirm-button--disabled {
  cursor: not-allowed;
  opacity: 0.6;
  box-shadow: none;
}

.confirm-button--disabled:hover {
  transform: none;
  box-shadow: none;
}

.confirm-button span {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  text-align: center;
  color: #ffffff;
}

.confirm-button--disabled span {
  color: #ffffff;
  opacity: 0.8;
}

.nickname-tip--error {
  opacity: 1;
}

.bottom-decoration {
  position: absolute;
  left: 77px;
  bottom: -20px;
  width: 189px;
  height: 4px;
  background: #7e5ce3;
  border-radius: 2px;
  filter: blur(20px);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .anon-setup-dialog {
    padding: 16px;
  }

  .dialog-container {
    width: 100%;
    max-width: 343px;
  }

  .vector-decoration {
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>

<style lang="less">
.anon-setup-dialog {
  .t-dialog {
    background: transparent;
    box-shadow: none;
    padding: 0;
    border-radius: 0;
  }

  .t-dialog__ctx {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    gap: 12px;
    padding: 20px;
  }

  .t-dialog--default {
    padding: 0;
    background-color: transparent;
  }

  .t-input {
    background: transparent;
    border: none;
  }

  .t-input:hover {
    border: none !important;
  }
}
</style>
