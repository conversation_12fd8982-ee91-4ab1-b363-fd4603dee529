import { Utils } from './index.ts';
/**
 * 下单渠道
 */
export enum OrderChannel {
  /** iOS */
  iOS = 1,
  /** Mac（应用市场） */
  Mac = 2,
  /** Mac（非应用市场） */
  MacNonMarket = 3,
  /** Windows */
  Windows = 4,
  /** Android */
  Android = 5,
  /** Web */
  Web = 6,
}

// 获取下单渠道
export const getOrderChannel = (): OrderChannel | undefined => {
  if (Utils.isMas()) return OrderChannel.Mac;
  if (Utils.isMacOS()) return OrderChannel.MacNonMarket;
  if (Utils.isWindows()) return OrderChannel.Windows;
  return undefined; // Handle cases where no condition is met
};

export const addCommasToNumber = (str: string | number = '0') => {
  if (!str) {
    return '0.00';
  }
  str = str.toString();
  let [integerPart, decimalPart] = str.split('.');
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  if (decimalPart) {
    decimalPart = decimalPart.length === 1 ? `${decimalPart}0` : decimalPart.slice(0, 2);
  } else {
    decimalPart = '00';
  }

  return `${integerPart}.${decimalPart}`;
};
