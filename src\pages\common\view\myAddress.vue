<template>
  <div class="my-Invoice-box">
    <address-bar
      :is-title="t('order.invoiceManagement')"
      :member-cards="memberCards"
      :current-member-card="currentMemberCard"
      @on-set-card="onSetCard"
    />

    <!-- 头部搜索 -->
    <div class="setBody">
      <div class="head-search-box">
        <div class="form-items">
          <t-button class="min-80" @click="addAddress">
            <!-- <template #icon>
              <img src="@assets/svg/iconadd.svg" />
            </template> -->
            <!-- {{ t('order.addinvoiceHeader') }} -->
            新增收货地址
          </t-button>

          <!-- <t-button
            v-if="teamsauthList.length > 0"
            class="gogrouporder"
            theme="default"
            variant="outline"
            @click="openGroup"
          >
            {{ t('banch.qwzzfp') }}
          </t-button> -->
        </div>
      </div>
      <div class="bgF">
        <div class="alicontent">
          <div v-if="tableData?.length > 0" class="alicontent-boxc">
            <t-config-provider>
              <t-table
                row-key="id"
                :fixed-rows="undefined"
                :height="pagination.total > 10 ? 'calc(100vh - 232px)' : 'calc(100vh - 182px)'"
                hide-sort-tips
                :columns="area === 'CN' ? columns : columns"
                :data="tableData"
                style="position: relative; overflow: auto"
                :loading="isLoading"
              >
                <template #consignee="{ row }">
                  <div class="consignee line-2">
                    <span v-show="row?.address?.isDefault" class="consignee-default">默认</span>
                    {{ row?.address?.userName }}
                  </div>
                </template>
                <template #phone="{ row }">
                  <div>
                    +{{ row?.address?.phone?.countryCode || '--' }}
                    {{ row?.address?.phone?.number || '--' }}
                    <!-- {{ row.phone?.countryCode ? '+' + row.phone?.countryCode : '+86' }}
                    {{ row.phone?.number ? row.phone?.number : '1343545643655' }} -->
                  </div>
                </template>
                <template #region="{ row }">
                  <div>
                    {{ row?.address?.address?.location?.address || '--' }}
                    {{ row?.address?.address?.location?.name }}
                  </div>
                </template>
                <template #detailedAddress="{ row }">
                  <div>{{ row?.address?.address?.houseNumber }}</div>
                </template>

                <!-- <template #empty>
                  <div>
                    <img
                      style="width: 200px; height: 200px; display: block; margin: 49px auto 8px"
                      src="@assets/prompt-picture/notdata.svg"
                    />
                    <div style="font-size: 14px; color: #516082; text-align: center">
                      {{ t('clouddisk.nodata') }}
                    </div>
                  </div>
                </template> -->
                <template #operation="{ row }">
                  <div class="operateBtn">
                    <div
                      class="lib cursor"
                      style="color: var(--kyy_color_button_text_brand_font_default, #4d5eff)"
                      @click="editAddress(row)"
                    >
                      {{ t('identity.edit') }}
                    </div>
                    <div
                      class="lib cursor"
                      style="color: var(--kyy_color_error_default, #4d5eff)"
                      @click="delThis(row)"
                    >
                      {{ t('identity.delete') }}
                    </div>
                    <!-- {{ rowIndex }} -->
                  </div>
                </template>
              </t-table>
            </t-config-provider>
            <div v-if="pagination.total && pagination.total > 10" class="m20 mt-16px! mb-0px!">
              <t-pagination
                :total="pagination.total"
                :total-content="false"
                show-previous-and-next-btn
                :show-page-size="false"
                :current="pagination.current"
                @change="pagination.onChange"
              />
            </div>
          </div>
          <div v-else class="noData">
            <REmpty class="wh-full mt-0!" name="no-data" />
          </div>
        </div>
      </div>
    </div>
    <selectOrganizationGoWeb ref="selectOrganizationGoWebRef"></selectOrganizationGoWeb>
    <AddAddressDrawer ref="addressDrawerRef" :current-member-card="currentMemberCard" @reload="getList" />
  </div>
</template>

<script setup lang="tsx">
// src\pages\common\view\common\AddressBar.vue
// import AddressBar from './common/AddressBar.vue';
import AddressBar from '@pages/common/view/common/AddressBar.vue';
import selectOrganizationGoWeb from './components/selectOrganizationGoWeb.vue';
import { REmpty } from '@rk/unitPark';
import { ref, reactive, onMounted, onActivated } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
// src\pages\common\view\components\address\add-address-drawer.vue
import AddAddressDrawer from '@pages/common/view/components/address/add-address-drawer.vue';
// src\pages\common\view\components\address\cards-modal.vue
import { Button as TButton } from 'tdesign-vue-next';
import {
  getUserDeliveryAddressListAxios,
  deleteStoreUserDeliveryAddressAxios,
  authorityTeamsAxios,
  getUserDeliveryAddressAxios,
} from '@api/shop';
import { to } from 'await-to-js';
import sdk from '@lynker-desktop/web';
const addressDrawerRef = ref();
const area = ref('CN');
// const popupVisible = ref(false);
// const addpopupVisible = ref(false);

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log(pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    getList();
  },
});
// const params = ref({
//   pageSize: 10,
//   page: 1,
// });

const imageUrl = ref('');
const userName = ref('');
const getCurrentAccount = async () => {
  try {
    const accountData = JSON.parse(await sdk.getLocalStorage('currentAccount'));
    imageUrl.value = accountData?.avatar || '';
    userName.value = accountData?.name || '';
  } catch (e) {
    imageUrl.value = '';
    userName.value = '';
  }
};

const memberCards = ref([]);
const currentMemberCard = ref(null);
const onGetAuthorityTeamsAxios = async () => {
  const [err, res] = await to(authorityTeamsAxios('order'));
  if (err) {
    MessagePlugin.error(err?.message);
    return;
  }
  return [{ full_name: '个人', person: userName.value, team_id: undefined, type: '', logo: imageUrl.value }].concat(
    res?.data || [],
  );
};

const onSetCard = (card) => {
  currentMemberCard.value = card;
  pagination.current = 1;
  getList();
};

const editAddress = async (row) => {
  const [err, res] = await to(getUserDeliveryAddressAxios({ id: row?.address?.id }, currentMemberCard.value?.team_id));
  if (err) {
    MessagePlugin.error(err?.message);
    return;
  }
  const { data } = res;
  console.log(data);
  addressDrawerRef.value?.onOpen(data?.deliveryAddress);
};

onMounted(() => {
  // if (window.localStorage.getItem('profile')) {
  //   area.value = JSON.parse(window.localStorage.getItem('profile')).area;
  // }
  getCurrentAccount();
  if (window.localStorage.getItem('profile')) {
    area.value = JSON.parse(window.localStorage.getItem('profile')).area;
  }

  onGetAuthorityTeamsAxios().then((res) => {
    console.log('dddf', res);
    memberCards.value = res || [];
    if (res?.length > 0) {
      currentMemberCard.value = res[0];
      getList();
    }
  });
  // document.addEventListener('click', () => {
  //   ipcRenderer.invoke('set-popbv', { show: true, type: 'personal' });
  // }, {
  //   capture: true,
  // });
});
onActivated(() => {
  getList();
});
// const teamsauthList = ref([]);
const getList = () => {
  // teamsauth('invoice_header').then((res) => {
  //   teamsauthList.value = res.data;
  // });
  const paramsData = {
    'page.size': pagination.pageSize,
    'page.number': pagination.current,
  };
  getUserDeliveryAddressListAxios(
    {
      ...paramsData,
    },
    currentMemberCard.value?.team_id,
  ).then((res) => {
    console.log(res);
    pagination.total = res.data?.page?.total;
    tableData.value = res?.data?.deliveryAddress;
    // if ((params.value.pageSize * params.value.page) % total.value === 0 && params.value.page > 1) {
    //   params.value.page -= 1;
    //   getList();
    // }
  });
};
const { t } = useI18n();
// const total = ref(200);

// const pageChange = (e) => {
//   params.value.page = e.current;
//   getList();
// };
const isLoading = ref(false);
const delThis = (row: any) => {
  const myDialog = DialogPlugin({
    header: () => (
      <>
        <div class="dialogheader mb-16px" style="display: flex; gap: 8px;">
          <iconpark-icon class="icon" style="font-size: 24px;" name="iconerror-g4lj3da4"></iconpark-icon>
          <div class="text-16 font-bold text-kyy_color_text_primary">删除</div>
        </div>
      </>
    ),
    // theme: 'danger',
    class: 'delmode',
    body: () => (
      <>
        <div style="color:#516082;font-size: 14px;">确定删除当前收货地址？</div>
      </>
    ),
    closeBtn: null,
    className: 'dialogSetFooter_top_24 dialogWebPosition',
    confirmBtn: t('home.openPreloadWindowError.confirm'),
    onConfirm: () => {
      // currentMemberCard.value?.team_id
      deleteStoreUserDeliveryAddressAxios({ id: row?.address?.id }, currentMemberCard.value?.team_id)
        .then(() => {
          MessagePlugin.success(t('clouddisk.success'));
          myDialog.hide();
          getList();
        })
        .catch((err) => {
          MessagePlugin.error(err?.message);
        });
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const addAddress = () => {
  addressDrawerRef.value?.onOpen();
};

const selectOrganizationGoWebRef = ref(null);
const tableData = ref([]);
// const columnsMaca = ref([
//   { colKey: 'title', title: t('order.invoiceHeader'), ellipsis: true },
//   { colKey: 'mail', title: t('order.Emile'), ellipsis: true },
//   { colKey: 'operate', title: '操作', width: 100 },
// ]);

const columns = ref([
  { colKey: 'consignee', title: '收货人', ellipsis: true, width: 216 },
  { colKey: 'phone', title: '电话/手机', ellipsis: true, width: 160 },
  {
    colKey: 'region',
    title: `所在地区`,
    width: 248,
  },
  {
    colKey: 'detailedAddress',
    title: `详细地址`,
    width: 384,
  },
  { colKey: 'operation', title: '操作', width: 144 },
]);
</script>
<style lang="less" scoped>
// .dialogheader {
//   display: flex;
//   width: 100%;
// }
:deep(.colorsssssss) {
  .t-button__text {
    font-weight: bold !important;
    color: red;
  }
}
.noData {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.consignee {
  &-default {
    color: var(--kyy_color_tag_text_brand, #4d5eff);
    font-size: 12px;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_brand, #eaecff);
    padding: 0 4px;
    margin-right: 4px;
  }
}
.bgF {
  background-color: #fff;
  // margin: 0px 24px;
  border-radius: 12px;
  max-height: calc(100vh - 136px);
  height: 100%;
}
.alicontent {
  // overflow: auto;
  flex: 1;
  height: 100%;
  padding: 24px;
  border-radius: 12px;

  background-image: url('@assets/address/pic_two_ya.png');
  background-size: contain;
  background-repeat: no-repeat;
  height: 100%;

  &-boxc {
    // src\assets\address\pic_two_ya.png
  }
}

.invoice-type::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 24px;
  font-size: 10px;
  background-image: url('@assets/img/icon_subscript.svg');
  height: 24px;
  background-repeat: no-repeat;
  background-size: 100%;
}

.my-Invoice-box {
  height: 100vh;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
}

.diandiandian {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.outer {
  padding: 0 6px;
}

.form-box {
  padding: 0 18px;
  overflow-y: overlay;
  max-height: 420px;
  display: flex;
  flex-wrap: wrap;
  .form-flex {
    display: flex;
    align-items: center;
  }
  .value-item {
    height: 22px;
    font-size: 14px;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei-Regular;
    font-weight: 400;
    color: #1a2139;

    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .laber-item {
    width: 70px;
    font-size: 14px;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei-Regular;
    font-weight: 400;
    // padding-top: 24px;
    text-align: left;
    margin-bottom: 8px;
    color: #828da5;
  }
}
:deep(.t-form__item) {
  margin-bottom: 24px;
}
.setBody {
  //
  background-image: url('@assets/address/bg_positioning_pic_ya.png');
  background-size: contain;
  background-repeat: no-repeat;
  // height: calc(100vh - 44px);
  height: 100%;
  padding: 24px;
}
.head-search-box {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  padding-bottom: 16px;
  // src\assets\address\bg_positioning_pic_ya.png
}
.gogrouporder {
  margin-right: 24px;
}
.form-items {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* padding-right: 32px; */
  width: 100%;
  .labels {
    height: 22px;
    padding-right: 8px;
    font-size: 14px;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei-Regular;
    font-weight: 400;
    text-align: left;
    color: #13161b;
    line-height: 22px;
  }
}
.operateBtn {
  display: flex;
  // justify-content: flex-end;
  gap: 8px;
  .lib {
    border-radius: 4px;
    padding: 4px;
    &:hover {
      background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
    }
  }
}
.btn-box {
  display: flex;
  // justify-content: space-between;
  cursor: pointer;
  gap: 4px;
}

.flex-a {
  display: flex;
  align-items: center;
}
.invul {
  padding: 8px;
  max-height: 200px;
  overflow: auto;
  li {
    cursor: pointer;
    line-height: 32px;
    height: 32px;
    padding: 0 8px;
    width: 100%;
    border-radius: 4px !important;
  }
  li:hover {
    background-color: var(--select-kyy-color-select-multiple-option-item-bg-active, #e1eaff) !important;
    color: var(--select-kyy-color-select-multiple-option-item-text, #1a2139) !important;
  }
}
</style>
<style lang="less">
.dialogSetForm-noPadding-450px {
  .t-dialog__body {
    overflow: overlay;
    max-height: 450px;
  }
}
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}
</style>
