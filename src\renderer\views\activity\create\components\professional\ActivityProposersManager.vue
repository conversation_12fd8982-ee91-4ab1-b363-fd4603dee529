<template>
  <div :style="`--scroll-height: ${tableMaxHeight}px`">
    <RTable :table="table" :filter="filter" @change="onTableChange">
      <template #toolbarContent>
        <div v-if="isAllowEdit && hasEditPermission" class="toolbar-right">
          <t-button class="w-108" theme="default" @click="activityProposerFormDialogRef?.open()">
            <iconpark-icon class="text-20" name="iconadd" />
            <span class="ml-4 font-bold">添加人员</span>
          </t-button>
          <t-button class="!ml-8 w-108" theme="primary" @click="activityProposerUploadDialogRef?.open()">
            <iconpark-icon class="text-20 text-[#4D5EFF]" name="iconsave-cloud" />
            <span class="ml-4">导入人员</span>
          </t-button>
        </div>
      </template>
    </RTable>
  </div>

  <activity-proposer-form-dialog ref="activityProposerFormDialogRef" @refresh="loadData" />

  <activity-tags-setting-dialog ref="activityTagsSettingDialogRef" />

  <activity-proposer-detail-dialog ref="activityProposerDetailDialogRef" />

  <activity-proposer-upload-dialog ref="activityProposerUploadDialogRef" @success="init" />
</template>

<script setup lang="tsx">
import { computed, inject, markRaw, onBeforeUnmount, onMounted, reactive, ref, watchEffect } from 'vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import _ from 'lodash';
import ActivityProposerFormDialog from './ActivityProposerFormDialog.vue';
import ActivityTagsSettingDialog from './ActivityTagsSettingDialog.vue';
import ActivityProposerDetailDialog from './ActivityProposerDetailDialog.vue';
import ActivityTagSelector from './ActivityTagSelector.vue';
import ActivitySeatNumberInput from './ActivitySeatNumberInput.vue';
import ActivityPromoterSelector from '@/views/activity/create/components/professional/ActivityPromoterSelector.vue';
import ActivityTagFilter from '@/views/activity/create/components/professional/ActivityTagFilter.vue';
import ActivityProposerUploadDialog from '@/views/activity/create/components/professional/ActivityProposerUploadDialog.vue';
import { sortSeatNumber } from '@/views/activity/utils';
import { getOpenid } from '@/utils/auth';
import { listProposer, listTags, removeProposer, updateProposer } from '@/api/activity/proposer';
import { getActivityListActors } from '@/api/activity';

const activityProposerFormDialogRef = ref(null);
const activityTagsSettingDialogRef = ref(null);
const activityProposerDetailDialogRef = ref(null);
const activityProposerUploadDialogRef = ref(null);

const activityFormData = inject('activityFormData');
const selectedTeam = inject('selectedTeam');

const isAllowEdit = inject('isAllowEdit');
const isManage = inject('isManage');
const hasEditPermission = inject('hasEditPermission');

// 是否是新增数据
const isCreate = computed(() => !activityFormData.id);

// 计算表格高度需要减去的高度，如果在详情管理中，则不需要减去底部操作栏的64
const clientHeightDiff = inject('isInDetail') ? 195 : 259;

// 表格高度
const tableMaxHeight = ref(document.body.clientHeight - clientHeightDiff);

// 筛选参数
const filterParams = reactive({
  pTagIdens: [],
  name: '',
  companyName: '',
  positionName: '',
  phoneNum: '',
  seatNumber: '',
  promoterKey: '',
});

// 排序参数
const sort = ref(undefined);

const table = ref({
  attrs: {
    'row-key': 'proposerKey',
  },
  list: [],
  columns: [
    {
      title: '姓名',
      colKey: 'name',
      width: '12.7%',
      cell: (h, { row }) => <t-tooltip content={row.name} placement="top"><div class="inline-block truncate w-full">{row.name ?? '--'}</div></t-tooltip>,
    },
    {
      title: '公司（单位）/职位',
      colKey: 'positions',
      width: '18.6%',
      cell: (h, { row }) => <div class="flex flex-col items-start">
        <t-tooltip content={row.positions[0].companyName} placement="top"> <div class="truncate w-full">{row.positions[0].companyName ?? '--'}</div></t-tooltip>
        <t-tooltip content={row.positions[0].positionName} placement="top"><div class="truncate w-full">{row.positions[0].positionName ?? '--'}</div></t-tooltip>
      </div>,
    },
    {
      title: '标签',
      colKey: 'pTagIden',
      width: '15.2%',
      cell: (h, { row, rowIndex }) => ((isAllowEdit && hasEditPermission) ? <ActivityTagSelector modelValue={row.pTagIden} change={(val) => onProposerPTagIdenChange(val, rowIndex)} /> : row.pTagName ?? '--'),
    },
    {
      title: () => <div class="flex gap-4">
        <span>所属推广人</span>
        <t-tooltip content={'请前往协作人中添加所属推广人'}>
          <iconpark-icon name="iconhelp" class="text-20 text-[#828DA5]"></iconpark-icon>
        </t-tooltip>
      </div>,
      colKey: 'promoterKey',
      width: '17%',
      cell: (h, { row, rowIndex }) => ((isAllowEdit && hasEditPermission) ? <ActivityPromoterSelector modelValue={row.promoterKey} change={(val) => onProposerPromoterKeyChange(val, rowIndex)} /> : row.promoterName ?? '--'),
    },
    {
      title: '座位号',
      colKey: 'seatNumber',
      width: '20.3%',
      cell: (h, { row, rowIndex }) => <ActivitySeatNumberInput width={54} seatType={activityFormData.basic.seatType} repeatCheckList={[...actors.value, ...activityFormData.members.proposers.filter((proposer) => proposer.proposerKey !== row.proposerKey)]} placeholder="输入" modelValue={row.seatNumber} change={(val) => onProposerSeatNumberChange(val, rowIndex)} readonly={!isAllowEdit || !hasEditPermission} />,
      sorter: true,
    },
    {
      title: '操作',
      colKey: 'operate',
      cell: (h, { row }) => (
        <div class="flex gap-8">
        <div class="cursor-pointer text-[#4D5EFF] hover:bg-[#EAECFF] rounded-4 p-4 inline-block"
               onClick={() => activityProposerDetailDialogRef.value.open(row)}>详情
          </div>
          <div class="cursor-pointer text-[#4D5EFF] hover:bg-[#EAECFF] rounded-4 p-4 inline-block"
               onClick={() => activityProposerFormDialogRef.value.open(row)}>编辑
          </div>
          <div class="cursor-pointer text-[#D54941] hover:bg-[#FDF5F6] rounded-4 p-4 inline-block"
               onClick={() => handleRemoveProposer(row)}>删除
          </div>
        </div>
      ),
      isShow: () => isAllowEdit && hasEditPermission,
    },
  ],
});

const filter = computed(() => ({
  attrs: {
    size: 'small',
    labelWidth: '80px',
    placeholder: '搜索姓名',
  },
  advanced: {
    form: {
      list: [
        {
          label: '',
          name: 'pTagIdens',
          value: [],
          defaultValue: [],
          isShow: () => activityFormData.majorSuppliment.tags.filter((tag) => tag.enabled).length > 0,
          type: 'component',
          component: markRaw(ActivityTagFilter),
          attrs: {
            tagLabel: '标签',
            multiple: true,
            resultText: ({ pTagIdens }) => activityFormData.majorSuppliment.tags.filter((tag) => pTagIdens.includes(tag.tagIden)).map((tag) => tag.tagName).join('、'),
          },
        },
        {
          label: '公司（单位）',
          name: 'companyName',
          value: filterParams.companyName,
          defaultValue: '',
          type: 'input',
        },
        {
          label: '职位',
          name: 'positionName',
          value: filterParams.positionName,
          defaultValue: '',
          type: 'input',
        },
        {
          label: '手机号',
          name: 'phoneNum',
          value: filterParams.phoneNum,
          defaultValue: '',
          type: 'input',
        },
        {
          label: '座位号',
          name: 'seatNumber',
          value: filterParams.seatNumber,
          defaultValue: '',
          type: 'component',
          component: markRaw(ActivitySeatNumberInput),
          attrs: {
            seatType: activityFormData.basic.seatType,
            resultText: ({ seatNumber }) => {
              const [row, number] = seatNumber.split('-');
              if (activityFormData.basic.seatType === 0) {
                return [row && `${row}排`, number && `${number}座`].filter(Boolean).join('');
              } if (activityFormData.basic.seatType === 1) {
                return [row && `${row}桌`, number && `${number}号`].filter(Boolean).join('');
              }
            },
          },
        },
        {
          label: '所属推广人',
          name: 'promoterKey',
          value: filterParams.promoterKey,
          defaultValue: '',
          type: 'select',
          attrs: {
            placeholder: '请选择推广人',
            options: activityFormData.members.collaborators.filter((collaborator) => collaborator.promoterKey).map((collaborator) => ({
              label: collaborator.collaboratorName,
              value: collaborator.promoterKey,
            })),
          },
          isShow: () => activityFormData.members.collaborators.filter((collaborator) => collaborator.promoterKey).length,
        },
      ],
    },
  },
}));

const actors = ref([]);

const loadTags = async () => {
  const res = await listTags({
    activityId: activityFormData.id,
    'me.openId': getOpenid(),
    'me.teamId': selectedTeam.value.teamId,
    'me.cardId': selectedTeam.value.uuid,
  });

  activityFormData.majorSuppliment.tags = res.data.data.tags;
};

const loadData = async () => {
  const params = {
    activityId: activityFormData.id,
    keyword: filterParams.name ?? null,
    pTagIdens: filterParams.pTagIdens.length ? filterParams.pTagIdens : null,
    companyName: filterParams.companyName ?? null,
    positionName: filterParams.positionName ?? null,
    phoneNum: filterParams.phoneNum ?? null,
    seatNumber: filterParams.seatNumber || null,
    promoterKey: filterParams.promoterKey || null,
    'me.openId': getOpenid(),
    'me.cardId': selectedTeam.value.uuid,
    'me.teamId': selectedTeam.value.teamId,
  };

  if (sort.value?.sortBy === 'seatNumber') {
    params.orderBySeatNumber = sort.value.descending ? 'DESC' : 'ASC';
  } else {
    params.orderBySeatNumber = 'OrderByDefault';
  }

  const res = await listProposer(params);

  activityFormData.members.proposers = res.data.data.proposers.map((proposer) => ({
    ...proposer,
    pTagIden: proposer.pTagIden || null,
  }));
};

const openActivityTagsSettingDialog = () => {
  activityTagsSettingDialogRef.value?.open();
};

// 移除拟定人员
const handleRemoveProposer = (row) => {
  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: `确定删除 ${row.name}？`,
    theme: 'info',
    confirmBtn: '确定',
    cancelBtn: '取消',
    onConfirm: async () => {
      if (isCreate.value) {
        activityFormData.members.proposers = activityFormData.members.proposers.filter(
          (member) => member.proposerKey !== row.proposerKey,
        );
      } else {
        await removeProposer({
          activityId: activityFormData.id,
          me: {
            openId: getOpenid(),
            cardId: selectedTeam.value.uuid,
            teamId: selectedTeam.value.teamId,
          },
          proposer: {
            id: row.id,
          },
        });

        loadData();
      }

      MessagePlugin.success('删除成功');
      confirmDia.destroy();
    },
    onCancel: () => {
      confirmDia.destroy();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
    },
  });
};

const updateData = async (row) => {
  await updateProposer({
    activityId: activityFormData.id,
    proposer: row,
    me: {
      openId: getOpenid(),
      cardId: selectedTeam.value.uuid,
      teamId: selectedTeam.value.teamId,
    },
  });
};

// 拟定人员标签修改回调
const onProposerPTagIdenChange = (val, rowIndex) => {
  const row = activityFormData.members.proposers[rowIndex];
  row.pTagIden = val;

  if (!isCreate.value) {
    updateData(row);
  }
};

// 拟定人员所属推广人修改回调
const onProposerPromoterKeyChange = (val, rowIndex) => {
  const row = activityFormData.members.proposers[rowIndex];
  row.promoterKey = val;

  if (!isCreate.value) {
    updateData(row);
  }
};

const onProposerSeatNumberChange = (val, rowIndex) => {
  const row = activityFormData.members.proposers[rowIndex];
  row.seatNumber = val;

  if (!isCreate.value) {
    updateData(row);
  }
};

const onTableChange = ({ filter, sortInfo }) => {
  if (filter) {
    filterParams.pTagIdens = filter?.pTagIdens ? _.cloneDeep(filter?.pTagIdens) : [];
    filterParams.name = filter?.searchVal ?? '';
    filterParams.companyName = filter?.companyName ?? '';
    filterParams.positionName = filter?.positionName ?? '';
    filterParams.seatNumber = filter?.seatNumber ?? '';
    filterParams.phoneNum = filter?.phoneNum ?? '';
  }
  sort.value = sortInfo;

  if (!isCreate.value) {
    loadData();
  }
};

watchEffect(() => {
  let list = activityFormData.members.proposers;

  if (isCreate.value) {
    // 筛选条件过滤
    if (filterParams.pTagIdens.length > 0) {
      list = list.filter((item) => filterParams.pTagIdens.includes(item.pTagIden));
    }
    if (filterParams.name !== '') {
      list = list.filter((item) => item.name.includes(filterParams.name));
    }
    if (filterParams.companyName !== '') {
      list = list.filter((item) => item.positions[0].companyName.includes(filterParams.companyName));
    }
    if (filterParams.positionName !== '') {
      list = list.filter((item) => item.positions[0].positionName.includes(filterParams.positionName));
    }
    if (filterParams.seatNumber !== '') {
      list = list.filter((item) => item.seatNumber.includes(filterParams.seatNumber));
    }
    if (filterParams.phoneNum !== '') {
      list = list.filter((item) => item.phoneNum.includes(filterParams.phoneNum));
    }

    if (sort.value?.sortBy === 'seatNumber') {
      if (sort.value.descending) {
        list = sortSeatNumber(list, 'desc');
      } else {
        list = sortSeatNumber(list, 'asc');
      }
    } else {
      list = list.sort((a, b) => a.addTime - b.addTime);
    }
  }

  table.value.list = list;
});

// 重置表格最大高度
const resizeTableMaxHeight = () => {
  tableMaxHeight.value = document.body.clientHeight - clientHeightDiff;
};

const init = () => {
  loadTags();
  loadData();
};

const loadActors = async () => {
  const res = await getActivityListActors({
    activityId: activityFormData.id,
    replyStatus: '-1', // 固定查询全部报名状态
    approveStatus: -1, // 固定查询全部审核状态
  });
  actors.value = res.data.data.actors;
};

onMounted(() => {
  if (!isCreate.value) {
    // 当非新建时，加载表格数据和标签数据
    init();
  }

  // 如果在管理侧，则需要查询一次员工列表用于校验座位号重复
  if (isManage) {
    loadActors();
  }

  window.addEventListener('resize', resizeTableMaxHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeTableMaxHeight);
});

defineExpose({
  openActivityTagsSettingDialog,
});

</script>

<style lang="less" scoped>
:deep(.RTable){
  .header{
    padding-bottom: 16px;
  }

  .toolbar-wrap{
    display: flex;
    justify-content: space-between;
    padding-top: 0;
    padding-left: 24px;
    padding-right: 24px;

    .toolbar-box{
      flex:1;
      align-items: center;
      text-align: left;
    }
  }

  .table-wrap{
    padding: 0 24px;
    height: calc(var(--scroll-height) - 56px);
    overflow: auto;
  }
}

:deep(.t-table){
  td:last-child{
    padding: 8px !important;
  }
}

:deep(.t-form-item__pTagIdens){
  .t-form__label{
    height: 0 !important;
    margin: 0 !important;
    min-height: 0 !important;
  }
}
</style>
