<template>
  <div class="chat-message">
    <!-- 消息卡片 -->
    <div class="message-card">
      <!-- 消息头部 -->
      <AppCardHeader style="font-size: 16px" :theme="handleActivityData(sceneData).type">
        {{ handleActivityData(sceneData).title }}
      </AppCardHeader>

      <!-- 消息内容 -->
      <div class="message-content">
        <!-- 活动信息 -->
        <ActivityCard
          section-title="推广"
          :thumbnail-url="sceneData?.asset_url || ''"
          :title="sceneData?.subject || ''"
          :time="handleActivityData(sceneData).time"
          :location="sceneData?.address || ''"
        />

        <!-- 申请人信息 -->
        <SectionItem
          title="推广位置"
          :value="handleActivityData(sceneData).position || ''"
        />
        <!-- 申请人信息 -->
        <SectionItem
          v-if="sceneData.promotion_message_type === 3"
          title="取消理由"
          :value="sceneData?.cancel_reason || ''"
        />

        <!-- 分割线 -->
        <div class="divider" />
      </div>

      <!-- 底部按钮 -->
      <ActionButtons
        :buttons="actionButtons"
        @button-click="handleButtonClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref } from 'vue';
import { AppCardHeader } from '@renderer/views/message/chat/MessageAppCard';
import { getTitle } from '@renderer/views/message/chat/msgTypeContent/AppWorks/constant';
import ActivityCard from '../../components/activity-promote/components/ActivityCard.vue';
import SectionItem from '../../components/activity-promote/components/SectionItem.vue';
import ActionButtons from '../../components/activity-promote/components/ActionButtons.vue';
import { useSceneData } from '../../components/activity-promote/composables/useSceneData';
import LynkerSDK from "@renderer/_jssdk";

const { ipcRenderer } = LynkerSDK;

const props = defineProps({
  msg: { type: Object as PropType<any>, default: null },
});

const { sceneData, handleActivityData } = useSceneData(props);

const actionButtons = [{
  key: 'info',
  text: '查看活动详情',
  variant: 'outline' as const,
  theme: 'default' as const,
}];

const handleButtonClick = (key: string) => {
 ipcRenderer.invoke('create-dialog', { url: `layoutActivity/activityParticipantDetailLayout/${sceneData.value?.activity_id}?subject=${encodeURIComponent( sceneData.value?.subject )}
  &type=aloneWindow`, opts: { x: 50, y: 50, width: 1296, minWidth: 1296, height: 720, }, });
};
</script>

<style scoped>
.chat-message {
  width: 360px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-card {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.message-content {
  display: flex;
  flex-direction: column;
  padding: 16px 16px 0;
  gap: 12px;
}

.divider {
  height: 1px;
  background: #eceff5;
}
</style>
