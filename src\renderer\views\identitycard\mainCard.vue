<template>
  <div v-if="loading" class="load">
    <t-loading />
  </div>
  <!-- close 事件里面需要修改 expand slot 中组件的 v-if -->
  <CollapsiblePage v-if="!editDes && !loading" @close="showExpandType = null">
    <div class="main-card">
      <div id="main-content" ref="mainContent">
        <div class="top-bg-box">
          <!-- <div class="top-bg2"></div> -->
        </div>
        <!-- 展示多身份卡选择浮层时取消drag -->
        <div :class="['w-header', showMoreCard ? 'no-drag' : '']">
          <div class="btns">
            <Tricks :isDrag="false" size="small" :offset="{ x: '-5', y: '2' }" uuid="IM-身份卡" />

            <t-tooltip v-if="cardIdType(cardId) === 'personal' &&!isSelf" :content="'发送数字名片'">
              <div @click.stop="shareVcard" class="btn-item">
                <iconpark-icon class="item-icon" name="iconbusinesscard"></iconpark-icon>
              </div>
            </t-tooltip>
            <t-tooltip v-if="!removed" :content="t('identity.shareBusinessCards')">
              <div @click.stop="share" class="btn-item">
                <iconpark-icon class="item-icon" name="iconshare"></iconpark-icon>
              </div>
            </t-tooltip>
            <template v-if="!isSelf">
              <t-tooltip v-if="!checkFollow() && !removed" :content="t('identity.follow')">
                <div @click.stop="fl" class="btn-item">
                  <iconpark-icon class="item-icon" name="iconcollect"></iconpark-icon>
                </div>
              </t-tooltip>
              <t-tooltip v-if="checkFollow()" :content="t('identity.unfollow')">
                <div @click.stop="delFl" class="btn-item">
                  <iconpark-icon class="item-icon" name="iconstar-blf0nie5"></iconpark-icon>
                </div>
              </t-tooltip>
              <t-tooltip v-if=" !isFriend &&
                          !checkPair &&
                          rela !== 'CO_WORKER' &&
                          rela !== 'CONSULT' &&
                          rela !== 'PLATFORM_FRIEND' &&
                          cardIdType(cardId) !== 'inner' &&
                          cardIdType(myId) !== 'inner' &&
                          cardIdType(cardId) !== 'platform' &&
                          cardIdType(myId) !== 'platform' &&
                          applyStatus === 'none' &&
                          !removed &&
                          !isSameExternal" :content="t('identity.addContact')">
                <div @click.stop="applyVisible = true" class="btn-item">
                  <iconpark-icon class="item-icon" name="iconpeopleadd"></iconpark-icon>
                </div>
              </t-tooltip>
            </template>

            <!-- 二维码弹窗 -->
            <qrCodeImgCom v-if="showQr" :cardId="cardId" :removed="removed" :cardInfo='cardInfo' :noteInfo="remarkInfo"></qrCodeImgCom>

            <t-tooltip
              v-if="!isSelf && (cardIdType(cardId) === 'inner' || cardIdType(cardId) === 'platform') && cardInfo.is_card"
              :content="t('identity.seeIdentity')">
              <div @click.stop="gotoPersonalPage" class="btn-item">
                <iconpark-icon class="item-icon" name="iconidcard-bn4pmf4b"></iconpark-icon>
              </div>
            </t-tooltip>
            <!-- <t-tooltip v-if="isSelf" :content="t('identity.setting')">
              <div @click.stop="gotoEditPage" class="btn-item">
                <iconpark-icon class="item-icon" name="iconsetUp"></iconpark-icon>
              </div>
            </t-tooltip> -->
            <div class="btn-item more-btns" v-if="!isSelf">
              <iconpark-icon class="item-icon" name="iconmore"></iconpark-icon>
              <div class="more-btns-wrap">
                <div class="label-item" @click.stop="reportCard">
                  <iconpark-icon class="label-icon" name="report"></iconpark-icon>
                  <div>{{ t('square.action.report') }}</div>
                </div>
                <div v-if="['FRIEND', 'BUSINESS', 'CONSULT', 'TEMPORARY'].includes(rela)"
                  @click.stop="delVisible = true" class="label-item">
                  <iconpark-icon class="label-icon" name="iconpeopleminus"></iconpark-icon>
                  <div>{{ t('identity.deleteContact') }}</div>
                </div>
              </div>
            </div>

            <div v-if="isSelf && isShowMoreCard" class="more-card-box">
              <div style="width: 1px;height:16px; background: #ECEFF5"></div>
              <div class="more-card" @click="viewMore">
                <iconpark-icon class="item-icon" name="iconidcard-bn4pmf4b"></iconpark-icon>
                <div> {{ t("identity.moreIdentity") }}</div>
              </div>
            </div>
          </div>
          <div class="flex flex-row gap-12 items-end">
            <div class="box-avatar">
              <kyy-avatar avatar-size="44px" :image-url="cardInfo.avatar" :user-name="cardInfo.name" bordered
                roundRadius />
            </div>
            <div class="w-1">
              <div class="nicknameBox">
                <div class="nickname">{{ remarkInfo.remarks || cardInfo.name }}</div>

                <img v-if="showGenderValue(genderValue) && genderValue.value === 1"
                  src="@renderer/assets/identity/icon_man.png" alt="" />
                <img v-else-if="showGenderValue(genderValue) && genderValue.value === 2"
                  src="@renderer/assets/identity/icon_woman.png" alt="" />
              </div>
              <div style="height: 4px"></div>
              <div style="display: flex; align-items: center">
                <div class="identity-text">
                  {{ cardInfo?.official_type === 2 ? '访客' : cardIdTypeMap.get(cardIdType(cardId)) }}
                </div>
                <t-tooltip :content="t('identity.e_signed')" :show-arrow="false" placement="top">
                  <div v-if="cardIdType(cardId) === 'personal' && cardInfo.psn_auth"
                    class="identity-text no-drag">
                    {{ cardIdTypeMap.get('e_sign') }}
                  </div>
                </t-tooltip>
                <div class="removed-text">
                  {{ removed ? t("identity.accountCancelled") : "" }}
                </div>
              </div>

            </div>
            <div v-if="isSelf">
              <div class="edit-btn" @click="gotoEditPage">
                <div class="edit-btn-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                    <path d="M1 14H14" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M2.44556 8.48222V11.1111H5.08781L12.5567 3.639L9.91875 1L2.44556 8.48222Z" stroke="white" stroke-width="1.4" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="edit-btn-text">
                  编辑
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="top-fixed"
          v-if="cardIdType(cardId) !== 'inner' && cardIdType(cardId) !== 'platform' && (showRelatedName(relatedList) || showRelatedName(relatedBusinessList))"
          ref="topFix">
          <div class="fixed-top-info" v-show="isTopSticky">
            <div class="person-info">
              <div class="box-avatar">
                <kyy-avatar avatar-size="28px" :image-url="cardInfo.avatar" :user-name="cardInfo.name" roundRadius />
              </div>
              <div class="nickname overflowEllipsis">{{ remarkInfo.remarks || cardInfo.name }}</div>
              <div class="identity-text">
                {{ cardIdTypeMap.get(cardIdType(cardId)) }}
              </div>
            </div>
            <div class="right-item">
              <div class='btns'>
                <t-tooltip v-if="!removed" :content="t('identity.shareBusinessCards')">
                  <div @click.stop="share" class="btn-item">
                    <iconpark-icon class="item-icon" name="iconshare"></iconpark-icon>
                  </div>
                </t-tooltip>
                <template v-if="!isSelf">
                  <t-tooltip v-if="!checkFollow() && !removed" :content="t('identity.follow')">
                    <div @click.stop="fl" class="btn-item">
                      <iconpark-icon class="item-icon" name="iconcollect"></iconpark-icon>
                    </div>
                  </t-tooltip>
                  <t-tooltip v-if="checkFollow()" :content="t('identity.unfollow')">
                    <div @click.stop="delFl" class="btn-item">
                      <iconpark-icon class="item-icon" name="iconstar-blf0nie5"></iconpark-icon>
                    </div>
                  </t-tooltip>
                  <t-tooltip v-if=" !isFriend &&
                          !checkPair &&
                          rela !== 'CO_WORKER' &&
                          rela !== 'CONSULT' &&
                          rela !== 'PLATFORM_FRIEND' &&
                          cardIdType(cardId) !== 'inner' &&
                          cardIdType(myId) !== 'inner' &&
                          cardIdType(cardId) !== 'platform' &&
                          cardIdType(myId) !== 'platform' &&
                          applyStatus === 'none' &&
                          !removed &&
                          !isSameExternal" :content="t('identity.addContact')">
                    <div @click.stop="applyVisible = true" class="btn-item">
                      <iconpark-icon class="item-icon" name="iconpeopleadd"></iconpark-icon>
                    </div>
                  </t-tooltip>
                </template>


                <!-- 二维码弹窗 -->
                <qrCodeImgCom :cardId="cardId" :removed="removed" :cardInfo='cardInfo' :noteInfo="remarkInfo">
                </qrCodeImgCom>


                <t-tooltip
                  v-if="!isSelf && (cardIdType(cardId) === 'inner' || cardIdType(cardId) === 'platform') && cardInfo.is_card"
                  :content="t('identity.seeIdentity')">
                  <div @click.stop="gotoPersonalPage" class="btn-item">
                    <iconpark-icon class="item-icon" name="iconidcard-bn4pmf4b"></iconpark-icon>
                  </div>
                </t-tooltip>
                <t-tooltip v-if="isSelf" :content="t('identity.setting')">
                  <div @click.stop="gotoEditPage" class="btn-item">
                    <iconpark-icon class="item-icon" name="iconsetUp"></iconpark-icon>
                  </div>
                </t-tooltip>
                <div class="btn-item more-btns" v-else>
                  <iconpark-icon class="item-icon" name="iconmore"></iconpark-icon>
                  <div class="more-btns-wrap">
                    <div class="label-item" @click.stop="reportCard">
                      <iconpark-icon class="label-icon" name="report"></iconpark-icon>
                      <div>{{ t('square.action.report') }}</div>
                    </div>
                    <div v-if="rela === 'FRIEND' || rela === 'BUSINESS' || rela === 'CONSULT'"
                      @click.stop="delVisible = true" class="label-item">
                      <iconpark-icon class="label-icon" name="iconpeopleminus"></iconpark-icon>
                      <div>{{ t('identity.deleteContact') }}</div>
                    </div>
                  </div>
                </div>


                <div v-if="isSelf && isShowMoreCard" class="more-card-box">
                  <div style="width: 1px;height:16px; background: #D5DBE4"></div>
                  <div class="more-card" @click="viewMore">
                    <iconpark-icon class="item-icon" name="iconidcard-bn4pmf4b"></iconpark-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div :class="['personal_tabs',isTopSticky?'top-sticky':'']">
            <div :class="[
              'default-tab-item',
              tabValue === 'basicInfo' ? 'active-tab-item' : ''
            ]">
              <t-button theme="default" variant="text" @click.stop="jumpTo('basicInfo')">{{ t("identity.basicInfo")
                }}</t-button>
              <div class="tab-item-border"></div>
            </div>
            <div v-if="showRelatedName(relatedList)" :class="[
              'default-tab-item',
              tabValue === 'otherOrg' ? 'active-tab-item' : ''
            ]">
              <t-button theme="default" variant="text" @click.stop="jumpTo('otherOrg')">{{ t("identity.otherOrg")
                }}</t-button>
              <div class="tab-item-border"></div>
            </div>
            <div v-if="showRelatedName(relatedBusinessList)" :class="[
              'default-tab-item',
              tabValue === 'businessOrg' ? 'active-tab-item' : ''
            ]">
              <t-button theme="default" variant="text" @click.stop="jumpTo('businessOrg')">{{
                t("identity.relatedBusinessAssociation") }}</t-button>
              <div class="tab-item-border"></div>
            </div>
          </div>
        </div>
        <div
          :class="['content', isNotMac ? 'scrollbar' : '', cardIdType(cardId) === 'personal' ? (isSelf ? 'personalSelfHeightContent' : 'personalHeightContent') : (isSelf ? 'selfHeightContent' : 'heightContent')]">
          <div class="info-box">
            <div class="tab-header-title" id="basicInfo">{{ t("identity.basicInfo") }}</div>
            <div class="w-info">
              <!-- <SquareOrgEntry
              v-if="!isPersonal && isSquareOpened"
              :card="cardInfo"
              :self="isSelf"
              @clicks="squareRedirect(false)"
            /> -->
              <div v-if="cardInfo?.teamName" class="com-title flex" style="align-items: center">
                <img v-if="cardInfo.teamLogo" class="com-logo" :src="cardInfo.teamLogo" alt="" />
                <!--          {{ cardInfo.teamName || t("identity.personalInfo") }}-->
                {{ cardInfo.teamName }}
              </div>
              <div class="box-info">
                <div class="t-item flex">
                  <div class="t-title">{{ t("identity.name") }}</div>
                  <div v-if="!isEditName" class="t-text ellipsis"
                    @click.stop="cardIdType(cardId) === 'personal' && isSelf && startNameInput()">
                    <!-- 内部和外部身份卡是不允许编辑姓名的，个人身份卡的是可以的 -->
                    {{ cardInfo.name }}
                  </div>
                  <div v-else class="t-text ellipsis">
                    <t-input ref="cardInfoNameRef" v-model="cardInfoName" maxlength="20" show-limit-number
                      @blur="endNameInput" />
                  </div>
                </div>
                <div v-if="cardInfo.linkId" class="t-item flex">
                  <div class="t-title">{{ t("identity.lkID") }}</div>
                  <div class="t-text flex! ellipsis link-id">
                    {{ cardInfo.linkId }}
                    <t-tooltip class="inline-flex ml-5" :content="t('identity.copy')">
                      <div @click.stop="copy(cardInfo.linkId)" class="copy-item">
                        <iconpark-icon class="item-icon" name="iconcopy"></iconpark-icon>
                      </div>
                    </t-tooltip>
                  </div>
                </div>
                <div v-for="item in cardInfo.options" :key="item.type" class="flex">
                  <div
                    v-if="showOptions(item) && item.type !== 'gender' && item.type !== 'avatar' &&  item.type !== 'name'"
                    :class="['t-item', 'flex', (['phone'].includes(item.type) ? 't-item-hover' : '')]">
                    <div class="t-title">{{ keyName[item.type] ? keyName[item.type] : item.name }}</div>
                    <div v-if="item.type === 'phone'" class="t-text ellipsis">
                      <div v-for="phone in item.value" class="ellipsis phone-number flex">
                        {{ `+${phone.code} ${phone.phone}` }}
                        <t-tooltip :content="t('identity.copy')">
                          <div @click.stop="copy(`+${phone.code} ${phone.phone}`)" class="copy-item">
                            <iconpark-icon class="item-icon" name="iconcopy"></iconpark-icon>
                          </div>
                        </t-tooltip>
                      </div>
                    </div>
                    <div v-else-if="item.type === 'positions'" class="t-text ellipsis">
                      <template v-if="item.value?.length">
                        <div v-for="de in item.value" class="ellipsis line-1"
                          :class="{'can-click':permissionPosition.includes(de.departmentId.toString()) && cardIdType(cardId) !== 'outer'}"
                          @click.stop="jumpContacts(de)">
                          {{ `${de.departmentName || "- -"}/${de.jobName || "- -"}` }}
                        </div>
                      </template>
                      <template v-else>
                        <div class="ellipsis line-1">
                          {{ `${"- -"}` }}
                        </div>
                      </template>

                    </div>
                    <div v-else-if="item.type === 'gender'" class="t-text ellipsis">
                      {{
                      (item.value &&
                      (+item.value === 1
                      ? t("identity.man")
                      : t("identity.woman"))) ||
                      "- -"
                      }}
                    </div>
                    <div v-else class="t-text ellipsis2">
                      {{ item.value || "- -" }}
                    </div>
                  </div>
                </div>
                <t-divider v-if='groupComment || !isSelf || rela || isSameExternal' class="my-8"
                  style="margin-top: 8px;margin-bottom: 8px;" />
                <div v-if="groupComment" class="t-item flex" style="align-items: center;">
                  <div class="t-title">{{ t("identity.groupNickname") }}</div>
                  <div class="t-text" :style="{ color: '#13161b', flexGrow: 1 }">
                    {{ groupComment }}
                  </div>
                  <!-- <img class="t-arrow" src="@renderer/assets/svg/icon_arrowRight.svg" alt=""> -->
                </div>
                <div v-if="!isSelf" class="t-item flex flex-justify-between! items-center! pdxy48 editor-item" style="align-items: center;margin-top: 4px;"
                  >
                  <div class="t-title">{{ t("identity.note") }}</div>
                  <div v-if="(!remarkInfo.remarks && !remarkInfo.describe && !remarkInfo.phoneNumbers?.length && !remarkInfo.emails?.[0] && !remarkInfo.locations?.length && !remarkInfo.images?.length)" class="t-text ellipsis2 cursor-pointer" :style="{
                      color: remarkInfo.remarks ? '#13161b' : '#828DA5'
                    }">
                    {{ t("identity.editTagContent") }}
                  </div>
                  <img
                    @click.stop="toEdit('notes')"
                    class="t-arrow mt-[4px]"
                    src="@renderer/assets/identity/icon_edit_default.svg"
                    alt=""
                  />
                </div>

                <div v-if="!isSelf && remarkInfo.remarks" class="t-item flex pdxy48" style="align-items: center;margin-top: 4px;"
                  >
                  <div class="t-title">{{ t("identity.notes") }}</div>
                  <div class="t-text ellipsis2 cursor-pointer" :style="{
                      color: remarkInfo.remarks ? '#13161b' : '#828DA5'
                    }">
                    {{ remarkInfo.remarks || t("identity.editTagContent") }}
                  </div>
                  <!-- <img
                    class="t-arrow"
                    src="@renderer/assets/identity/icon_edit.png"
                    alt=""
                  /> -->
                </div>

                <div v-if="!isSelf && remarkInfo.describe" class="t-item flex pdxy48" style="align-items: center"
                  >
                  <div class="t-title">{{ t("identity.describe") }}</div>
                  <div class="t-text ellipsis2 cursor-pointer" :style="{
                      color: remarkInfo.describe ? '#13161b' : '#828DA5'
                    }">
                    {{ remarkInfo.describe || t("identity.editTagContent") }}
                  </div>
                  <!-- <img
                    class="t-arrow"
                    src="@renderer/assets/identity/icon_edit.png"
                    alt=""
                  /> -->
                </div>

                <template v-if="!isSelf && remarkInfo?.phoneNumbers?.length">
                  <div v-for="(item, index) in (remarkInfo?.phoneNumbers|| [])" :key="item?.number" class="t-item flex pdxy48 new-item"
                    >
                    <div class="t-title">{{ index === 0 ? t("identity.phoneNumber") : '' }}</div>
                    <div class="t-text ellipsis2 cursor-pointer" :style="{
                        color: item ? '#13161b' : '#828DA5'
                      }">
                      +{{item?.label}} {{item?.number}}
                    </div>
                    <t-tooltip :content="t('identity.copy')">
                      <div @click.stop="copy(`+${item.label} ${item.number}`)" class="copy-item">
                        <iconpark-icon class="item-icon" name="iconcopy"></iconpark-icon>
                      </div>
                    </t-tooltip>
                  </div>
                </template>

                <div v-if="!isSelf && remarkInfo?.emails?.[0]" class="t-item flex pdxy48 new-item" style="align-items: center"
                  >
                  <div class="t-title">{{ t("identity.emailAddress") }}</div>
                  <div class="t-text ellipsis2 cursor-pointer" :style="{
                      color: remarkInfo?.emails?.[0] ? '#13161b' : '#828DA5'
                    }">
                    {{ remarkInfo?.emails?.[0] }}
                  </div>
                  <t-tooltip :content="t('identity.copy')">
                    <div @click.stop="copy(`${remarkInfo?.emails?.[0]}`)" class="copy-item">
                      <iconpark-icon class="item-icon" name="iconcopy"></iconpark-icon>
                    </div>
                  </t-tooltip>
                  <!-- <img
                    class="t-arrow"
                    src="@renderer/assets/identity/icon_edit.png"
                    alt=""
                  /> -->
                </div>

                <template v-if="!isSelf && remarkInfo?.locations?.length">
                  <div v-for="(item, index) in remarkInfo?.locations" :key="item" class="t-item flex pdxy48 flex-items-start"
                    >
                    <div class="t-title">{{ index === 0 ? t("identity.locationInfo") : '' }}</div>
                    <t-tooltip :content="t(item?.name + ' ' + item?.description + ' ' + item?.doorNumber)">
                      <div class="t-text cursor-pointer h-[auto]!" :style="{
                          color: item ? '#13161b' : '#828DA5'
                        }">
                        <div class="ellipsis1 cursor-pointer">
                          {{ item?.name }}
                        </div>
                        <div class="ellipsis1 cursor-pointer text-[#828DA5]!">
                          {{ item?.description }}
                        </div>
                        <div class="ellipsis1 cursor-pointer text-[#828DA5]!">
                          {{ item?.doorNumber }}
                        </div>
                      </div>
                    </t-tooltip>
                  </div>
                </template>

                <template v-if="!isSelf && remarkInfo?.images?.length">
                  <div class="t-item flex pdxy48 flex-items-start"
                    >
                    <div class="t-title">{{ t("identity.imagesInfo") }}</div>
                    <div
                      class="t-text cursor-pointer flex! flex-grow-2 flex-row gap-[4px] h-[auto]!"
                      :style="{
                        color: remarkInfo.describe ? '#13161b' : '#828DA5'
                      }"
                    >
                      <template v-for="(item, index) in remarkInfo?.images" :key="item">
                        <t-image
                          :src="item"
                          fit="cover"
                          class="w-[40px] h-[40px] block bg-[#eee] rounded-[8px]"
                          :style="{ width: '40px', height: '40px', 'border-radius': '8px' }"
                          @click.stop="previewImage(remarkInfo?.images, index, item)"
                        />
                      </template>
                    </div>
                  </div>
                </template>

                <div class="t-item flex pdxy48" style="align-items: center" v-if="rela || isSameExternal">
                  <div class="t-title">{{ t('identity.tag') }}</div>
                  <template v-if="tagList && tagList.length">
                    <div class="t-text ellipsis2 cursor-pointer" @click.stop="viewTags">
                      <span v-for="(item,index) in tagList" :key="item.id" class="tag-item">
                        {{`${index !== 0?'、':''}${item.des}` }}
                      </span>
                    </div>
                  </template>
                  <span v-else class="t-text" style="color: #828DA5;cursor:pointer" @click.stop="viewTags">{{
                    t('identity.editTagContent') }}</span>

                </div>

                <template v-if="showSquareEntry && !removed">
                  <t-divider style="margin-top: 8px;margin-bottom: 8px;" />
                  <div class="color-text-3">
                    <!--广场号权限 -->
                    <div v-if="rela === 'FRIEND'" class="flex mb-4 pdxy48" style="padding: 3px 8px;">
                      <div class="w-92 flex-y-center flex-shrink-0">{{ t('identity.squareAuth') }}</div>
                      <div class="flex-y-center w-full cursor-pointer" @click.stop="toEdit('square')">
                        <div>
                          <p v-if="squareInfo.postPrivilege?.hideMyPost">{{ t('identity.noTAWatch') }}</p>
                          <p v-if="squareInfo.postPrivilege?.hidePost">{{ t('identity.noWatchTA') }}</p>
                        </div>
                        <div class="flex-1 text-right">
                          <t-icon name="chevron-right" size="18" class="color-text-3" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <SquarePersonalEntry :info="squareInfo" :org-info="orgSquareInfo" :is-friend="rela === 'FRIEND'"
                    :is-personal="isPersonal" :card-id="cardId" @click="squareRedirect" />
                </template>
              </div>
            </div>

            <div v-if="isFriend || checkPair" class="contacts">
              <div class="flex t-item">
                <span class="t-title">{{ t("identity.notesInfo") }}</span>
                <span class="t-text">{{ applyInfo?.remarks }}</span>
              </div>

              <div class="flex t-item">
                <span class="t-title">{{ t("identity.source") }}</span>
                <span class="t-text ellipsis">{{ t("identity.pass")
                  }}{{ sourceMap.get(+applyInfo?.source || 2) }}</span>
              </div>
            </div>


          </div>
          <div v-if="
              (cardIdType(cardId) === 'inner' || cardIdType(cardId) === 'platform') && innerList && innerList.length > 0
            " class="bussiness info-box">
            <div class="tab-header-title" v-if="cardIdType(cardId) === 'inner'">会员信息</div>
            <div v-for="(lItem, lIndex) in innerList" :key="lIndex" class="list-item pl-8px">
              <div class="item-title" v-if="cardIdType(cardId) === 'inner'">{{ lItem.name || "- -" }}</div>
              <div class="li" v-if="cardIdType(cardId) === 'platform'">
                <div class="label">关联会员</div>
                <div class="name">{{ lItem.name || "- -" }}</div>
              </div>
              <div class="li">
                <div class="label">会员级别</div>
                <div class="name">{{ lItem.level_name || "- -" }}</div>
              </div>
              <div v-show="lItem?.type === 1" class="li">
                <div class="label">岗位</div>
                <div class="name">{{ lItem.job || "- -" }}</div>
              </div>

            </div>
          </div>

          <div v-if="
              (cardIdType(cardId) === 'inner' || cardIdType(cardId) === 'platform') && innerGovermentList && innerGovermentList.length > 0
            " class="bussiness info-box">
            <div class="tab-header-title">组织信息</div>
            <div v-for="(lItem, lIndex) in innerGovermentList" :key="lIndex" class="list-item pl-8px">
              <div class="item-title">{{ lItem.team_name || "- -" }}</div>
              <!-- <div class="li">
                <div class="label">会员级别</div>
                <div class="name">{{ lItem.level_name || "- -" }}</div>
              </div> -->
              <div v-show="lItem?.type === 1" class="li">
                <div class="label">岗位</div>
                <div class="name">{{ lItem.job || "- -" }}</div>
              </div>

            </div>
          </div>
          <div v-if="
              (cardIdType(cardId) === 'inner' || cardIdType(cardId) === 'platform') && innerAssociationList && innerAssociationList.length > 0
            " class="bussiness info-box">
            <div class="tab-header-title">组织信息</div>
            <div v-for="(lItem, lIndex) in innerAssociationList" :key="lIndex" class="list-item pl-8px">
              <div class="item-title">{{ lItem.team_name || "- -" }}</div>
              <div v-show="lItem?.type === 1" class="li">
                <div class="label">岗位</div>
                <div class="name">{{ lItem.job || "- -" }}</div>
              </div>

            </div>
          </div>

          <div v-if="
              (cardIdType(cardId) === 'inner' || cardIdType(cardId) === 'platform') && innerTenantList && innerTenantList.length > 0
            " class="bussiness info-box">
            <div class="tab-header-title">租户信息</div>
            <div v-for="(lItem, lIndex) in innerTenantList" :key="lIndex" class="list-item pl-8px">
              <div class="li">
                <div class="label">关联租户</div>
                <div class="name">{{ lItem.team_name || "- -" }}</div>
              </div>
              <div v-show="lItem?.type === 1" class="li">
                <div class="label">岗位</div>
                <div class="name">{{ lItem.job || "- -" }}</div>
              </div>

            </div>
          </div>


          <div v-if="projectList.length" style="margin-bottom: 20px;" class="bussiness info-box">
            <div class="tab-header-title">工程信息</div>
            <div v-for="item in projectList" :key="item.id" class="list-item  pl-8px">
              <div class="item-title">{{ item.name || "- -" }}</div>

              <div class="li">
                <div class="label">{{ t("identity.departmentLabel") }}</div>
                <div class="name ellipsis">{{item?.positions[0]?.departmentName || "- -"}}</div>
              </div>
              <div class="li">
                <div class="label">{{ t("identity.position") }}</div>
                <div class="name ellipsis">{{item?.positions[0]?.jobName || "- -"}}</div>
              </div>
            </div>
          </div>
          <div
            v-if="cardIdType(cardId) !== 'inner' && cardIdType(cardId) !== 'platform' && showRelatedName(relatedList)"
            class="info-wrap">
            <div class="info-box">
              <div class="tab-header-title" id="otherOrg">{{ t("identity.otherOrg") }}</div>
              <div v-if="relatedList.length" class="related">
                <template v-for="item in relatedList" :key="item.organizeId">
                  <div class="related-item" @click.stop="viewOrg(item)" v-if="showRelatedItem(item)">
                    <OrganizationItem showType="org" :dataInfo="item"></OrganizationItem>
                  </div>
                </template>
              </div>
              <div v-else class="related emptyDataTips">
                {{ t('contacts.emptyDataTips') }}
              </div>
            </div>
          </div>
          <div
            v-if="cardIdType(cardId) !== 'inner' && cardIdType(cardId) !== 'platform' && showRelatedName(relatedBusinessList)"
            class="info-wrap">
            <div class="info-box">
              <div class="tab-header-title" id="businessOrg">{{ t("identity.relatedBusinessAssociation") }}</div>
              <div v-if="relatedBusinessList.length" class="related">
                <template v-for="item in relatedBusinessList" :key="item.id">
                  <div class="related-item" v-if="showRelatedItem(item)" @click.stop="viewOrgBusiness(item)">
                    <OrganizationItem showType="business" :dataInfo="{...item, name:item.team_name,post:item.job}">
                    </OrganizationItem>
                  </div>
                </template>
              </div>
              <div v-else class="related emptyDataTips">
                {{ t('contacts.emptyDataTips') }}
              </div>
            </div>
          </div>
        </div>

        <div v-if="!isSelf && !removed && !isSameInnerAndDepart" class="w-footer flex">
          <div v-if="!isFriend && applyStatus === 'needAgree'" class="flex flex-1">
            <t-button :class="['flex-1', contactAuth ? '' : 'contactAuthBox']" theme="primary"
              @click.stop="agreeApply( applyId,true)">
              {{ t("identity.agree") }}
            </t-button>
          </div>
          <div v-else-if="
              !isFriend &&
              !checkPair &&
              !['CO_WORKER','PLATFORM_FRIEND'].includes(rela)
          " class="flex flex-1">
            <t-button :class="['flex-1', contactAuth ? '' : 'contactAuthBox']" theme="primary"
              :disabled="applyStatus === 'isPending'" @click.stop="addContact">
              {{
              applyStatus === 'isPending'
              ? t("contacts.addStatusPassing")
              : t("identity.addContact")
              }}
            </t-button>
          </div>
          <div v-if="rela && (isFriend || checkPair || ['CO_WORKER','PLATFORM_FRIEND'].includes(rela))"
            class="flex flex-1" style="align-items: center">
            <!-- <t-button class="voiceBtn" shape="circle" theme="primary" variant="base" ghost @click.stop="voiceMsg"
              :disabled="!contactAuth">
              <template #icon>
                <div class="icon">
                  <img v-if="contactAuth" src="@renderer/assets/svg/voicefill_new_grey.svg" alt="" />
                  <img v-else src="@renderer/assets/svg/voicefill_new_grey_disabled.svg" alt="" />
                </div>
              </template>
            </t-button> -->

            <!-- <div style="width: 20px" /> -->

            <!-- <t-button class="voiceBtn" shape="circle" theme="primary" variant="base" ghost @click.stop="videoMsg"
              :disabled="!contactAuth">
              <template #icon>
                <div class="icon">
                  <img v-if="contactAuth" src="@renderer/assets/svg/videofill_new_grey.svg" alt="" />
                  <img v-else src="@renderer/assets/svg/videofill_new_grey_disabled.svg" alt="" />
                </div>
              </template>
            </t-button> -->

            <!-- <div style="width: 28px" /> -->

            <t-button :class="['flex-1', contactAuth ? '' : 'contactAuthBox']" theme="primary" variant="base" ghost
              style="color: #ffffff" @click.stop="message" :disabled="!contactAuth">
              <!--          <template #icon>-->
              <!--            <div class="icon">-->
              <!--              <img src="@renderer/assets/svg/commentfill.svg" alt="">-->
              <!--            </div>-->
              <!--          </template>-->
              {{ t("identity.message") }}
            </t-button>
          </div>
        </div>

        <!-- <t-drawer
        v-model:visible="drawerVisible"
        :show-overlay="true"
        :header="false"
        :footer="false"
        size="348px"
        :on-overlay-click="() => (drawerVisible = false)"
        placement="bottom"
        class="drawerVisible"
        @cancel="drawerVisible = false"
      >
        <template #body>
          <otherOrgPage :relatedOrg='relatedOrg'></otherOrgPage>
        </template>
      </t-drawer> -->

        <del-friend v-model:visible="delVisible" :relation="rela" :card-id="cardId" :my-id="myId" :card-openid="cardInfo.openid"
          :card-team-id="cardInfo.teamId" @onconfirm="delSuc" />
        <apply-dialog v-model:visible="applyVisible" :card-info="cardInfo" :my-id="myId" class="identity_apply-dialog"
          @onconfirm="agreeApply" @onApplyContact="onApplyContact" />
      </div>
    </div>
    <!-- 对 expand slot 的组件添加 v-if -->
    <template #expand>
      <!-- slot 内部为监听子元素个数，为了已打开切换时效果 -->
      <div v-if="showExpandType" style="display:none;"></div>
      <otherOrgPage :relatedOrg='relatedOrg' v-if='showExpandType === "org"'></otherOrgPage>
      <MemberCard :data='businessData' v-else-if='showExpandType === "business"' />
      <setNotes v-else-if='showExpandType === "notes"' :card-id="cardId" :my-id="myId" :remarks="remarkInfo.remarks"
        :describe="remarkInfo.describe" @onclose="closeEdit" />
      <tag v-else-if='showExpandType === "tags"' :my-id="myId" :relation-id="cardId" @confirm="confirmTags" />
      <PrivilegeSetting v-else-if='showExpandType === "square"' :info="squareInfo" @close="showExpandType=''"
        @confirm="getPrivilege" />
      <ApplyLanguage v-else-if='showExpandType === "applyLanguage"' :card-id="cardId" :my-id="myId"
        :info="applyLanguageInfo" @onclose="showExpandType = ''" />
    </template>
  </CollapsiblePage>
  <t-drawer :class="['all-card-drawer', showExpandType === 'applyLanguage' ? 'all-card-drawer-border-radius' : '']"
    v-model:visible="showMoreCard" @close="showMoreCard=false" destroyOnClose :header="false" :footer="false"
    placement="bottom" size="calc(100vh - 47px)">
    <template #body>
      <all-card :current="cardId" @close="(showExpandType = '',showMoreCard=false)" @openApply="setApplyLanguage" />
    </template>
  </t-drawer>
  <MyVCardType :infoData="infoData" ref="MyVcardRef"></MyVCardType>
</template>

<script lang="ts" setup>
  import tag from './components/tag_new.vue';
  import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
  import { watch, ref, computed, toRaw, nextTick, onMounted, onUnmounted } from "vue";
  import { MessagePlugin } from "tdesign-vue-next";
  import MyVCardType from "@/windows/vcard/components/MyVCardType.vue"

  // mainCard
  import { mainCard } from "@renderer/api/vcard/index";

  import { followList } from "@renderer/api/contacts/api/follow";
  import { useRoute, useRouter } from "vue-router";
  import {
    getImCardIds,
    getOpenid,
    setApplyCards,
    getApplyCards,
    getCards,
    checkContactAuth
  } from "@renderer/utils/auth";
  import { useI18n } from "vue-i18n";
  import MemberCard from "@renderer/views/identitycard/dialog/memberCard.vue";
  import { isNotMac } from "../zhixing/util";
  import CollapsiblePage from './components/CollapsiblePage.vue';
  import {
    follow,
    delFollow,
    getExternalCardNew,
    getInnerMemberCard,
    getInnerQovernmentCard,
    getInnerTenantCard,
    setProfilesCard,
    getInnerAssociationCard
  } from "../../api/identity/api/card";
import { acceptFriend } from '@renderer/api/im/api';

  import {
    cardData,
    combMsgInfo,
    cardRelation,
    getProjectList,
    getRelatedOrganize,
    cardIdType,
    relatedOrgInfo,
    getRelatedBusiness,
    tranStaffs,
    loadAcountIdentityCards,
    cardRemark
  } from "@renderer/views/identitycard/data";
  import AllCard from "@renderer/views/identitycard/allCard.vue";
  import { cardTags } from '@renderer/api/contacts/api/tag';
  import setNotes from "./setNotes.vue";
  import applyDialog from "./dialog/applyContact.vue";
  import delFriend from "./dialog/delFriend.vue";
  import qrCodeImgCom from './components/qrCodeImg.vue'
  import otherOrgPage from './components/otherOrgPage.vue'
  import { getSessionLocalIdByCards } from "../message/service/utils";
  import { openChat } from "@/utils/share";
  import SquarePersonalEntry from "@/views/identitycard/components/SquarePersonalEntry.vue";
  import { useSquareEntry } from "@/views/identitycard/hooks";
  import { canApplyFriend } from "@/api/contacts/api/friend";
  import { setIdentityCardFocus } from "@/views/setting/util";
  import { showDialog } from '@renderer/utils/DialogBV';
  import OrganizationItem from "./components/organizationItem.vue"
  import PrivilegeSetting from "./components/PrivilegeSetting.vue";
  import useClipboard from "vue-clipboard3";
  import to from "await-to-js";
  import { getPostPrivilege } from "@/api/square/privilege";
  import { getDepartmentPermission, getOrganizeList } from '@renderer/api/contacts/api/organize';
  import _ from 'lodash';
  import ApplyLanguage from "@/views/identitycard/components/setApplyLanguage.vue";

  import LynkerSDK from "@renderer/_jssdk";
import { app } from 'electron';
  const { ipcRenderer } = LynkerSDK;

  const Store = LynkerSDK.eStore;
  const { t } = useI18n();
  const { toClipboard } = useClipboard();

  const showExpandType = ref < '' | 'org' | 'business' | 'notes' | 'tags' | 'square' | 'applyLanguage' > ('');

  const route = useRoute();
  const router = useRouter();

  const sourceMap = new Map([
    [1, t("identity.withinGroup")],
    [2, t("identity.mobileSearch")],
    [3, t("identity.recommend")],
    [4, t("identity.consult")],
    [5, t("identity.scanCode")],
    [6, t("identity.shareCard")]
  ]);
  const cardIdTypeMap = new Map([
    ["outer", t("identity.outer")],
    ["inner", t("identity.inner")],
    ["personal", t("identity.personal")],
    ["platform", t("identity.platform")],
    ["e_sign", t("identity.e_sign")],
  ]);
  const keyName = { 'email': t("identity.emailAddress"), 'phone': t("identity.phoneNumber") }
  const cardId = ref("");
  const myId = ref("");
  const isSelf = computed(
    () =>
      cardId.value === myId.value ||
      getImCardIds()?.some((item) => item === cardId.value)
  );

  const showQr = computed(() => !isSelf.value && !['personal', 'outer', 'inner', 'platform'].includes(cardIdType(cardId.value)));

  const isSameExternal = computed(() => {
    if (
      cardIdType(cardId.value) === "outer" &&
      cardIdType(myId.value) === "outer"
    ) {
      return (
        getCards()?.find((item) => item.uuid === myId.value)?.internal_team_id ===
        cardInfo.value?.teamId
      );
    }
    return false;
  });
  const isSameInnerAndDepart = computed(() => {
    if (
      (cardIdType(cardId.value) === "inner" || cardIdType(cardId.value) === 'platform') &&
      (cardIdType(myId.value) === "inner" || cardIdType(myId.value) === 'platform')
    ) {
      return Boolean(cardInfo.value?.isDeleted);
    }
    return false;
  });
  const editDes = ref(false);
  const drawerVisible = ref(false);
  const isShowMoreCard = ref(true);

  const cardInfo: any = ref({});
  const remarkInfo: any = ref({});
  const rela = ref("");
  const checkPair = ref(false); //是否checkPair检测有返回关系
  const projectList = ref([]);
  const relatedList = ref([]);
  const relatedBusinessList = ref([]); // 关联商协会列表
  // const accountsDetail = ref({}); // 认证账号信息
  const tabValue = ref("basicInfo");

  const relatedOrg: any = ref({});
  const followListCardId = ref([]);
  const delVisible = ref(false);
  const applyVisible = ref(false);
  const applyInfo = ref();
  const loading = ref(true);
  const removed = ref(false);
  const groupComment = ref(""); // 群昵称
  const {
    isPersonal,
    showSquareEntry,
    squareInfo,
    orgSquareInfo,
    squareRedirect,
    squareConfirmDia
  } = useSquareEntry(cardInfo, isSelf);
  const isEditName = ref(false);
  const cardInfoName = ref("");
  const cardInfoNameRef = ref(null);
  //标签
  const tagList = ref([]);
  const tagVisible = ref(false);
  const contactAuth = ref(true);

  const viewTags = _.throttle(() => {
    showExpandType.value = 'tags';
  }, 500)

  const getTags = async () => {
    const { data: tag } = await cardTags(cardId.value, myId.value);
    console.log(tag, 'taglist')
    tagList.value = tag?.tags || [];
  }

  const confirmTags = async () => {
    // showExpandType.value = '';
    await getTags();
  }

  const startNameInput = () => {
    cardInfoName.value = cardInfo.value.name || "";
    isEditName.value = true;
    nextTick(() => {
      cardInfoNameRef.value.focus();
    });
  };

  const endNameInput = () => {
    const name = cardInfoName.value.trim();
    if (name && name !== cardInfo.value.name) {
      let data = {
        openid: cardInfo.value.openid,
        title: name,
        cellphone: cardInfo.value.options?.find((item) => item.type === "phone")
          ?.value[0].phone,
        avatar: cardInfo.value.avatar,
        attachments: {
          options: cardInfo.value.options
        }
      };
      setProfilesCard(data).then((res) => {
        if (res?.status === 200) {
          MessagePlugin.success(t("identity.editSuccess"));
          ipcRenderer.invoke("change-profiles-info", {
            avatar: cardInfo.value.avatar,
            title: name
          });
          initData();
        }
      });
    }
    isEditName.value = false;
  };

  const previewImage = (items, index, url) => {
    LynkerSDK.previewImage({
      images: items,
      index,
      url
    })
  }

  // 刷新添加状态,刷新弹框列表
  const onApplyContact = () => {
    checkAllApplyList();
    ipcRenderer.invoke("change-add-contacts");
  };

  // 已发起过添加申请 不再显示添加按钮 根据好友申请记录过滤
  // 是好友，非好友<未发起,我发起待通过，对方发起我待同意>，需要添加，不需要添加
  const isFriend = ref(false)
  const applyStatus = ref<'none' | 'isPending' | 'needAgree'>('none') 
  const applyId = ref(0)
  const checkAllApplyList = async () => {
    isFriend.value = false;
    applyStatus.value = 'none'
    if (isSelf.value) return;
    const applyres =  await canApplyFriend({main:myId.value,peer:cardId.value})
    const { data } = applyres.data
    if(data.isFriend) {
      // 是好友
      isFriend.value = true;
      return;
    }
    if(!data.apply) {
      applyStatus.value = data.hasApply ? 'isPending' : 'none'
      return;
    }
    if(data.apply.status === "FRIEND_APPLY_STATUS_PENDING") {
      applyId.value = data.apply.id
      if(data.apply.cardIdApply === myId.value){
       // 我发起待通过
       applyStatus.value = 'isPending'
      } else {
       // 待同意
       applyStatus.value = 'needAgree'
      }
    } else {
      // 添加好友
      applyStatus.value = 'none';
    }
  };
  const innerList = ref([]);
  const getInnerList = async () => {
    let cardID = 0;
    if (cardInfo.value && cardInfo.value?.cardId?.includes("$") || cardInfo.value?.cardId && /^PT/.test(cardInfo.value?.cardId)) {
      cardID = cardInfo.value?.cardId;
    }
    // getInnerMemberCard
    let res = await getInnerMemberCard(cardID);
    if (res?.status === 200) {
      // 先只判断单向添加记录，后期可能处理互加的逻辑处理

      innerList.value = res.data?.data;
    }
  };

  // 获取政府单位信息
  const innerGovermentList = ref([]);
  const getInnerGovermentList = async () => {
    let cardID = 0;
    if (cardInfo.value && cardInfo.value?.cardId?.includes("$") || cardInfo.value?.cardId && /^PT/.test(cardInfo.value?.cardId)) {
      cardID = cardInfo.value?.cardId;
    }
    // getInnerMemberCard
    let res = await getInnerQovernmentCard({ cardId: cardID });
    if (res?.status === 200) {
      // 先只判断单向添加记录，后期可能处理互加的逻辑处理

      innerGovermentList.value = res.data?.data;
    }
  };
  // 获取政府单位信息
  const innerAssociationList = ref([]);
  const getInnerAssociationList = async () => {
    let cardID = 0;
    if (cardInfo.value && cardInfo.value?.cardId?.includes("$") || cardInfo.value?.cardId && /^PT/.test(cardInfo.value?.cardId)) {
      cardID = cardInfo.value?.cardId;
    }
    // getInnerMemberCard
    let res = await getInnerAssociationCard({ cardId: cardID });
    if (res?.status === 200) {
      // 先只判断单向添加记录，后期可能处理互加的逻辑处理

      innerAssociationList.value = res.data?.data;
    }
  };

  // 获取租户信息
  const innerTenantList = ref([]);
  const getInnerTenantList = async () => {
    let cardID = 0;
    if (cardInfo.value && cardInfo.value?.cardId?.includes("$") || cardInfo.value?.cardId && /^PT/.test(cardInfo.value?.cardId)) {
      cardID = cardInfo.value?.cardId;
    }
    let res = await getInnerTenantCard({ cardId: cardID });
    if (res?.status === 200) {

      innerTenantList.value = res.data?.data;
    }
  };


  const genderValue = computed(() => {
    let val = cardInfo.value?.options?.find((item) => item.type === "gender");
    return {
      ...val,
      value: Number(val?.value),
    };
  });
  const show_visible_users = (item, key = 'visible_type') => {
    let res = true;
    if (isSelf.value) return res;
    if (!item?.visible_users?.staffs?.length) return res;
    let cardId = tranStaffs([{ cardId: myId.value }], 'id')?.[0]
    res = item?.visible_users?.staffs?.includes(cardId);
    if (item[key] === 3) {
      return res;
    } else if (item[key] === 4) {
      return !res;
    }
  }
  // 跳转到通讯录部门,没有部门权限或者外部身份不能跳转
  const jumpContacts = (item) => {
    if (!permissionPosition.value.includes(item.departmentId.toString()) || cardIdType(cardId.value) === 'outer') return false;
    const query = { from: 'mainCard', teamId: cardInfo.value.teamId, departmentId: item.departmentId };
    ipcRenderer.send("update-nume-index", { value: 'address_book', query });
    ipcRenderer.invoke("hide-identWin");
  }
  const showOptions = (item) => {
    // 控制显示（0：所有人可见，1：仅联系人可见，2：所有人不可见, 3：部分可见，4：不给谁看）
    if (cardIdType(cardId.value) === 'personal') {
      return (item.visible_type === undefined ||
        item.visible_type === 0 ||
        ([3, 4].includes(item.visible_type) && show_visible_users(item)) ||
        (item.visible_type === 1 && (isSelf.value || rela.value === 'FRIEND' || rela.value === 'BUSINESS' || rela.value === 'CO_WORKER' || rela.value === 'PLATFORM_FRIEND')))
    } else {
      return item.display
    }
  };
  const showGenderValue = (data) => {
    let item = data;
    if (cardIdType(cardId.value) === 'personal') {
      return (item.visible_type === undefined ||
        item.visible_type === 0 ||
        ([3, 4].includes(item.visible_type) && show_visible_users(item)) ||
        (item.visible_type === 1 && (isSelf.value || rela.value === 'FRIEND' || rela.value === 'BUSINESS' || rela.value === 'CO_WORKER' || rela.value === 'PLATFORM_FRIEND')))
    } else {
      return item.display
    }
  };
  const showRelatedItem = (item) => {
    // // 控制显示（0：所有人可见，1：仅联系人可见，2：所有人不可见）
    // item?.display === 0 ||
    // (item?.display === 1 &&
    //   (isSelf.value ||
    //     rela.value === "FRIEND" ||
    //     rela.value === "BUSINESS" ||
    //     rela.value === "CO_WORKER"));
    return item?.display === undefined ||
      item.display === 0 ||
      ([3, 4].includes(item.display) && show_visible_users(item, 'display')) ||
      (item?.display === 1 &&
        (isSelf.value ||
          rela.value === "FRIEND" ||
          rela.value === "BUSINESS" ||
          rela.value === "CO_WORKER" ||
          rela.value === 'PLATFORM_FRIEND'));
  }

  const showRelatedName = (list) => {
    // // 控制显示（0：所有人可见，1：仅联系人可见，2：所有人不可见）
    // list?.some(
    //   (item) =>
    //     item?.display === 0 ||
    //     (item?.display === 1 &&
    //       (isSelf.value ||
    //         rela.value === "FRIEND" ||
    //         rela.value === "BUSINESS" ||
    //         rela.value === "CO_WORKER"))
    // );
    return list?.some(
      (item) =>
        item?.display === undefined ||
        item.display === 0 ||
        ([3, 4].includes(item.display) && show_visible_users(item, 'display')) ||
        (item?.display === 1 &&
          (isSelf.value ||
            rela.value === "FRIEND" ||
            rela.value === "BUSINESS" ||
            rela.value === "CO_WORKER" ||
            rela.value === 'PLATFORM_FRIEND'))
    );
  }

  const getContactAuth = async () => {
    contactAuth.value = await checkContactAuth({ from: myId.value, to: cardId.value });
    if (!contactAuth.value) MessagePlugin.warning({
      content: t("identity.contactAuthTips"),
      style: { width: '280px' }
    });
    return contactAuth.value
  }

  const addContact = async () => {
    applyVisible.value = true;
  }

  const message = async () => {

    if (!await getContactAuth()) return;
    openChat({ main: myId.value, peer: cardId.value });
    ipcRenderer.invoke("hide-identWin");
  };

  const meetingUserMapper = (member) =>
  ({
    type: "internal",
    name: member.staffName || member.nickname,
    avatar: member.avatar,
    openId: member.openId,
    cardId: member.cardId,
    teamId: member.teamId
  } as MeetingUser);

  const videoMsg = async () => {
    if (!await getContactAuth()) return;
    const params = await combMsgInfo(cardId.value, myId.value);
    const myInfo = params.attachment.member.find(
      (item) => item.cardId === params.main
    );
    const peerInfo = params.attachment.member.find(
      (item) => item.cardId === params.peer
    );
    const callData: AvMeetingStartData = {
      mediaType: "audioVideo",
      conversation: {
        type: 1,
        id: getSessionLocalIdByCards(params.peer, params.main),
        target: peerInfo.openId
      },
      starter: meetingUserMapper(myInfo),
      invited: [meetingUserMapper(peerInfo)]
    };
    ipcRenderer.invoke("hide-identWin");
    ipcRenderer.invoke("im.bridge.avreal", { action: "call", data: callData });
  };

  const voiceMsg = async () => {
    if (!await getContactAuth()) return;
    const params = await combMsgInfo(cardId.value, myId.value);
    const myInfo = params.attachment.member.find(
      (item) => item.cardId === params.main
    );
    const peerInfo = params.attachment.member.find(
      (item) => item.cardId === params.peer
    );
    const callData: AvMeetingStartData = {
      mediaType: "audio",
      conversation: {
        type: 1,
        id: getSessionLocalIdByCards(params.peer, params.main),
        target: peerInfo.openId
      },
      starter: meetingUserMapper(myInfo),
      invited: [meetingUserMapper(peerInfo)]
    };
    ipcRenderer.invoke("hide-identWin");
    ipcRenderer.invoke("im.bridge.avreal", { action: "call", data: callData });
  };
  const infoData = ref(null);
  const MyVcardRef = ref(null);
  const shareVcard = () => {
    // 发送数字名片
    mainCard().then(res => {
      infoData.value = res.data.data
      MyVcardRef.value.openWin()
      // ipcRenderer.invoke("set-popbv",
      //   {
      //     show: true, type: 'dialogVcard', data: {
      //       val: JSON.stringify(res.data.data)
      //     }
      //   }
      // );
    })
  };
  const share = () => {
    const query = {
      cardId: cardId.value,
      myCardId: myId.value,
      teamId: cardInfo.value.teamId
    };
    ipcRenderer.invoke("open-share", query);
  };

  const showMoreCard = ref(false);
  const viewMore = async () => {
    await loadAcountIdentityCards();
    showMoreCard.value = true;
  };

  const applyLanguageInfo = ref(null);
  const setApplyLanguage = _.throttle((item) => {
    applyLanguageInfo.value = item;
    showExpandType.value = 'applyLanguage';
  }, 500);

  const businessData = ref({}) // 商协会数据
  const viewOrgBusiness = _.throttle((item) => {
    businessData.value = item
    showExpandType.value = 'business'
    console.log('===>', showExpandType.value, businessData.value)
  }, 500)

  const viewOrg = _.throttle(async (item) => {
    if (item.type === 1) {
      const { data } = await getExternalCardNew(
        { cardId: item.relationData.uuid, viewCardId: myId.value }
      );
      relatedOrg.value = {
        ...data.data,
        name: data.data?.options?.find(v => v.type === 'name')?.value,
        avatar: item.team_logo,
      };
      // relatedOrg.value = {
      //   name: data.data.team,
      //   post: data.data?.positions.length
      //     ? `${data.data.positions[0].departmentName}/${data.data.positions[0].jobName}`
      //     : "",
      //   phone: data.data.telephone
      //     ? [{ code: data.data.telCode, number: data.data.telephone }]
      //     : [],
      //   email: data.data.email || "--"
      // };
    } else {
      relatedOrg.value = await relatedOrgInfo(item.organizeId);
    }
    relatedOrg.value["type"] = item.type;
    showExpandType.value = 'org'
    // drawerVisible.value = true;
    console.log('=====>', relatedOrg.value);
  }, 500);

  watch(() => drawerVisible.value, (val) => {
    changeModelValue(val);
  });
  watch(() => applyVisible.value, (val) => {
    if (val) {
      setIdentityCardFocus(true);
      ipcRenderer.invoke("set-window-sise", {
        window: "identWin",
        width: 488,
        height: 489,
        smooth: false
      });
    } else {
      setIdentityCardFocus(false);
      ipcRenderer.invoke("set-window-sise", {
        window: "identWin",
        width: 352,
        height: 560,
        smooth: false
      });
    }
  });


  const toEdit = _.throttle((type) => {
    showExpandType.value = type
    console.log('=====>', showExpandType.value);
  }, 500);
  // 獲取廣場權限
  const getPrivilege = async (squareId) => {
    showExpandType.value = ''
    const [err, res] = await to(getPostPrivilege(squareId));
    if (err) return;
    squareInfo.value.postPrivilege = res.data.postPrivilege;
  };

  const closeEdit = async () => {
    showExpandType.value = ''
    remarkInfo.value = await cardRemark(cardId.value);
    notifyChatInfoUpdate();
  };
  const gotoEditPage = () => {
    ipcRenderer.invoke('identity-expand', { show: false, expandWidth: 0 });
    router.replace({
      path: "/identitycard/identityCardEdit",
      query: {
        ...route.query,
        showMoreCard: isShowMoreCard.value ? "showMoreCard" : "",
        cardId: cardId.value
      }
    });
  };

  const gotoPersonalPage = () => {
    ipcRenderer.invoke('identity-expand', { show: false, expandWidth: 0 });
    // 切换个人身份时清除群昵称
    const useStore = new Store({ accessPropertiesByDotNotation: true });
    useStore.set("window.identity", { comment: "" });
    router.replace({
      path: `/identitycard/view/${encodeURIComponent(cardInfo.value.openid)}/${encodeURIComponent(getOpenid())}`,
      query: {
        ...route.query,
        showMoreCard: isShowMoreCard.value ? "showMoreCard" : "",
      }
    });
  }

  const reportCard = () => {
    showDialog('dialogReport', {
      key: cardId.value,
      name: remarkInfo.value.remarks || cardInfo.value.name,
      source: 3,
    })
  }

  const fl = () => {
    follow({selfCardId: myId.value, followCardId: cardId.value})
      .then((res) => {
        ipcRenderer.invoke("update-contact");
        getFollowList();
      })
      .catch((err) => {
        MessagePlugin.error(
          err.response.data?.message || t("identity.operationFailed")
        );
      });
  };

  const delFl = () => {
    delFollow({selfCardId: myId.value, followCardId: cardId.value})
      .then(() => {
        ipcRenderer.invoke("update-contact");
        getFollowList();
      })
      .catch((err) => {
        MessagePlugin.error(
          err.response.data?.message || t("identity.operationFailed")
        );
      });
  };

  const getFollowList = () => {
    followList().then((res) => {
      followListCardId.value = res.data.data.follows;
      checkFollow();
      ipcRenderer.invoke("update-contact");
    });
  };


  const checkFollow = () => {
    if (!followListCardId.value || followListCardId.value.length === 0) {
      return false;
    }
    return !!~followListCardId.value.findIndex((v) => v.followCardId === cardId.value);
  };
const agreeApply = async (id, hide = false) => {
  origin =
      cardIdType(cardId.value) === "outer" || cardIdType(myId.value) === "outer"
        ? "BUSINESS"
        : "FRIEND";
  acceptFriend({id})
      .then(() => {
        hide && ipcRenderer.invoke("hide-identWin");
        rela.value = origin;
        ipcRenderer.invoke("update-contact");
      })
      .catch((err) => {
        MessagePlugin.error(
          err.response.data?.message || t("identity.addContactFailed")
        );
      });
  };

  const delSuc = () => {
    ipcRenderer.invoke("update-contact");
    checkRela();
  };

  const checkRela = async () => {
     const data = await cardRelation(cardId.value, myId.value);
     rela.value = data.rela
      checkPair.value =data.checkPair
  };

  // 更新聊天身份卡备注
  const notifyChatInfoUpdate = () => {
    if (
      cardInfo.value?.openid &&
      cardInfo.value?.cardId &&
      remarkInfo.value?.remarks != null
    ) {
      const data: IChatCardUpdateNotificationItem = {
        cardId: cardInfo.value.cardId,
        openId: cardInfo.value.openid,
        avatar: cardInfo.value.avatar,
        staffName: cardInfo.value.name,
        comment: remarkInfo.value.remarks,
        describe: remarkInfo.value.describe,
      };
      ipcRenderer.invoke("im.card.update", data);
    }
  };

  const permissionPosition = ref([])
  const initData = async (data?) => {
    showExpandType.value = '';
    showMoreCard.value = false;
    tabValue.value = "basicInfo";

    projectList.value = [];
    relatedList.value = [];
    relatedBusinessList.value = [];
    innerGovermentList.value = [];
    innerTenantList.value = [];
    innerAssociationList.value = [];
    innerList.value = [];
    drawerVisible.value = false;
    tagVisible.value = false;
    contactAuth.value = true;

    squareConfirmDia.value?.destroy();
    delVisible.value = false;
    applyVisible.value = false;
    editDes.value = false;
    setIdentityCardFocus(false);
    ipcRenderer.invoke('focus-identWin');
    // 是否通过新的联系人同意操作打开的身份卡信息
    const info = getApplyCards();
    if (info) {
      // addAgree.value = info.agree;
      applyInfo.value = info;
    }
    // cardId.value = decodeURIComponent(route.params.cardId as string);
    // myId.value = decodeURIComponent(route.params.myId as string);
    cardInfo.value = await cardData(cardId.value, myId.value);
    console.log(cardInfo.value,'66666666666666666');

    // 检查用户是否可见组织部门
    await getPostPermission(cardInfo.value, cardId.value)

    remarkInfo.value = await cardRemark(cardId.value);
    notifyChatInfoUpdate();

    checkRela()
    console.log('===>cardRelation', rela.value);

    removed.value = cardInfo.value?.removed || false;
    checkAllApplyList();
    getFollowList();
    ["platform"].includes(cardIdType(cardId.value)) && getInnerList() && getInnerGovermentList() && getInnerAssociationList() && getInnerTenantList();
    ['inner'].includes(cardIdType(cardId.value)) && (projectList.value = await getProjectList(
      +cardId.value.split("$")[1],
      cardInfo.value.teamId
    ));
    !['inner', "platform"].includes(cardIdType(cardId.value)) &&
      (relatedList.value = await getRelatedOrganize({ cardId: cardId.value, viewCardId: myId.value })) &&
      (relatedBusinessList.value = await getRelatedBusiness({ cardId: cardId.value, viewCardId: myId.value }));
    setApplyCards({ agree: false, remarks: "", source: "" });

    // cardIdType(cardId.value) === "personal" && (accountsDetail.value = await getAccountsDetailData(cardId.value));

    // 查询标签， 在有关系的情况下显示标签，rela代表关系， isSameExternal代表两个人在同一个外部组织下，理应是同事关系，现在暂时跟app保持同步不能聊天但是显示标签（之前需求设计的遗漏）
    tagList.value = [];
    if (rela.value || isSameExternal.value) {
      getTags();
    };
    const windSize = { width: 352, height: 560 };
    if (route.path?.indexOf("identityCardEdit") !== -1) {
      windSize.width = 464;
      windSize.height = 600;
      ipcRenderer.invoke("set-window-sise", {
        window: "identWin",
        ...windSize,
        smooth: false
      });
    } else {
      ipcRenderer.invoke("set-window-sise", {
        window: "identWin",
        ...windSize,
        smooth: false
      });
    }
    !route.query.fromIdentityPage && ipcRenderer.invoke("set-identWin-pos", windSize);

    const useStore = new Store({ accessPropertiesByDotNotation: true });
    groupComment.value = data?.comment || useStore.get("window.identity").comment;
    loading.value = false;
  };

  ipcRenderer.on("load-card", (e, val) => {
    loading.value = true;
  });

  ipcRenderer.removeAllListeners('show-card');
  ipcRenderer.on("show-card", (e, val) => {
    loading.value = true;
    if (val?.route_path) {
      router.replace({
        path: `/identitycard/contactValidate/${val.cardId}/${val.myId}`,
        query: {
          applyId: val.applyId
        }
      });
      applyId.value = val.applyId
    } else {
      cardId.value = decodeURIComponent(val.cardId);
      myId.value = decodeURIComponent(val.myId);
      if (route.path?.indexOf("identityCardEdit") !== -1) {
        router.replace({
          path: `/identitycard/view/${cardId.value}/${myId.value}`,
          query: {
            showMoreCard: route.query.showMoreCard,
          }
        });
      }
      isShowMoreCard.value = Boolean(val?.showMoreCard);
      isTopSticky.value = false
      initData(val);
      if (val?.openMoreCard) {
        setTimeout(() => {
          viewMore();
        }, 500);
      }
    }
  });

  console.log('=====> close-expand');
  ipcRenderer.on('close-expand', (e, val) => {
    showExpandType.value = '';
  })

  watch(
    () => route.path,
    async (newValue, oldValue) => {
      if (~route.path.indexOf("identitycard/view")) {
        loading.value = true;
        cardId.value = decodeURIComponent(route.params.cardId as string);
        myId.value = decodeURIComponent(route.params.myId as string);
        isShowMoreCard.value = Boolean(route.query?.showMoreCard);
        if (route?.query?.route_path) {
          router.replace({
            path: `/identitycard/contactValidate/${encodeURIComponent(cardId.value)}/${encodeURIComponent(myId.value)}`,
            query: route.query
          });
        } else {
          initData();
        }
      }
    },
    { immediate: true }
  );

  const noDrag = ref(false);
  const changeModelValue = (val) => {
    noDrag.value = val
  };
  const isScroll = ref(true) // 是否触发滚动监听
  const jumpTo = (type) => {
    tabValue.value = type;
    isScroll.value = false;
    const element = document.querySelector(`#${type}`).getBoundingClientRect()
    const basicInfo = document.querySelector(`#basicInfo`).getBoundingClientRect()
    const mainEl = document.querySelector(`#main-content`) as HTMLElement
    const offsetTop = type === 'basicInfo' ? 0 : element.top - basicInfo.top + 100;
    nextTick(() => {
      mainEl.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    })
    setTimeout(() => {
      isScroll.value = true;
    }, 600);
  };
  const topFix = ref(null)  //顶部tab $ref
  const isTopSticky = ref(false)  // 是否触发顶部Sticky
  const scrollTab = () => {
    if (topFix.value) {
      const rect = topFix.value?.getBoundingClientRect();
      isTopSticky.value = rect.top < 2
    }
    if (isScroll.value) {
      let otherOrgOffsetTop = document.querySelector('#otherOrg')?.getBoundingClientRect().top || 0
      let businessOffsetTop = document.querySelector('#businessOrg')?.getBoundingClientRect().top || 0
      if (businessOffsetTop !== 0 && businessOffsetTop < 180) {
        tabValue.value = 'businessOrg';
      } else if (otherOrgOffsetTop !== 0 && otherOrgOffsetTop < 180) {
        tabValue.value = 'otherOrg';
      } else {
        tabValue.value = 'basicInfo';
      }
    }
  };

  const getPostPermission = async (cardInfo, cardId) => {
    // 外部身份卡不用跳转
    if (cardIdType(cardId) === "outer") return false

    const departmentIds = []
    cardInfo.options.map(val => {
      if (val.type === "positions") {
        val.value?.map(val => departmentIds.push(val.departmentId))
      }
    })
    // 获取我的组织列表是否包含当前组织，检查用户是否可见组织部门。
    if (departmentIds.length > 0 && cardInfo.teamId) {
      const res = await getDepartmentPermission({ teamId: cardInfo.teamId, departmentIds })
      permissionPosition.value = res.data.data?.departmentIds
    }
  }
  const copy = (text: string) => {
    toClipboard(text);
    MessagePlugin.success(t("identity.copySuc"));
  }
  onMounted(() => {
    window.addEventListener('scroll', scrollTab, true);
  });
  onUnmounted(() => {
    window.removeEventListener('scroll', scrollTab);
  });
</script>

<style lang="less" scoped>
  @import "../../style/mixins.less";
  .editor-item {
    img {
      display: none;
    }
    &:hover {
      img {
        display: block;
      }
    }
  }
  :deep(.identity_apply-dialog) {
    .t-dialog__mask {
      border-radius: 16px;
    }

    .t-dialog__wrap {
      border-radius: 16px;
    }

    .t-dialog__position {
      border: 1px solid;
    }
  }

  :deep(.expand) {
    border: none;
  }

  ::-webkit-scrollbar {
    width: 0 !important;
  }

  .contactAuthBox {
    background: var(--color-button_primary-kyy_color_button_primary_bg_disabled, #C9CFFF) !important;
  }

  .contactAuthBox:hover {
    background: var(--color-button_primary-kyy_color_button_primary_bg_disabled, #C9CFFF) !important;
  }

  .top-bg-box {
    position: absolute;
    top: 0;
    width: 100%;
    height: 243px;
    background: url('../../assets/identity/topBg_v2.png') no-repeat;
    background-size: cover;

    .top-bg2 {
      background: url('../../assets/identity/topBg2.png') no-repeat;
      background-size: cover;
      height: 80px;
      width: 100%;
      position: absolute;
      bottom: 0;
    }
  }

  .no-drag {
    -webkit-app-region: no-drag !important;
  }

  .load {
    height: 100vh;
    width: 100%;
    background-color: #f1f2f5;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .mt4 {
    margin-top: 4px;
  }

  .main-card {
    width: 352px;
    height: 100vh;
    position: relative;
    overflow: hidden;
    z-index: 3;
    padding-bottom: 12px;
    background: var(--cyan-kyy_color_cyan_hover, #3CC9C0);

    /* kyy_shadow_m */
    // box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);

    #main-content {
      height: 100%;
      overflow-y: scroll;
    }

    .w-header {
      padding: 12px;
      -webkit-app-region: drag;
      position: relative;
      padding-top: 40px;

      .more-card-box {
        display: flex;
        align-items: center;
        margin-left: 8px;
      }

      .more-card {
        -webkit-app-region: no-drag;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 600;
        text-align: center;
        color: #fff;
        line-height: 28px;
        padding: 0 8px 0 2px;
        border-radius: 4px;
        margin-left: 8px;
        cursor: pointer;
        display: flex;

        .item-icon {
          font-size: 22px;
          color: #fff;
        }

        &:hover {
          background-color: #EAECFF;
          color: #707EFF;

          .item-icon {
            color: #707EFF;
          }
        }
      }

      .box-avatar {
        width: 44px;
        height: 44px;
        position: relative;
        cursor: pointer;

        .avatar-btn {
          width: 20px;
          height: 20px;
          background-image: url(@/assets/identity/0.icon_avatar.png);
          background-size: cover;
          position: absolute;
          right: 0;
          bottom: 0;
        }

        .avatar {
          width: 44px;
          height: 44px;
          border-radius: 10px;
          background-size: cover;
          -webkit-app-region: no-drag;
        }
      }

      .w-1 {
        width: 100%;
        display: flex;
        flex-direction: column;

        .nicknameBox {
          display: flex;
          align-items: center;

          img {
            margin-left: 4px;
            width: 16px;
            height: 16px;
          }
        }

        .nickname {
          max-width: 248px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 16px;
          font-weight: 700;
          color: var(--text-kyy-color-text-white, #fff);
          line-height: 24px;
          cursor: text;
          -webkit-app-region: no-drag;
        }


      }
    }

    .btns {
      -webkit-app-region: no-drag;
      display: flex;
      justify-content: flex-end;
      height: 28px;
      margin-bottom: 8px;
      width: fit-content;
      position: absolute;
      top: 12%;
      right: 8px;

      :deep(.btn-item) {
        width: 20px;
        height: 20px;
        padding: 4px;
        cursor: pointer;
        color: #717376;
        box-sizing: content-box;
        background-size: 80% 80%;
        background-repeat: no-repeat;
        background-position: center;
        border-radius: 4px;

        .item-icon {
          font-size: 20px;
          color: #fff;
        }

        &.viewInfo {
          background-image: url(@/assets/identity/viewInfo.svg);
        }


        &:hover {
          background-color: #EAECFF;

          .item-icon {
            color: #707EFF;
          }

          .more-btns-wrap {
            visibility: visible;
            transition: all 0.2s linear;
          }
        }
      }

    }

    .more-btns {
      position: relative;

      .more-btns-wrap {
        visibility: hidden;
        position: absolute;
        top: 30px;
        right: 0;
        z-index: 1000;
        padding: 4px;
        background: white;
        box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.12);
        border-radius: 8px;
        width: 130px;
        transition: all 0.2s linear;
        -webkit-app-region: no-drag;

        .label-item {
          display: flex;
          color: var(--kyy_color_tabbar_item_text, #1a2139);
          font-size: 14px;
          line-height: 32px;
        }

        .label-icon {
          font-size: 18px;
          color: #828DA5;
          margin: 0 12px 0 8px;
        }
      }

    }

    .top-fixed {
      position: sticky;
      top: 0;
      z-index: 999;

      .person-info {
        display: flex;
        align-items: center;

        .nickname {
          max-width: 152px;
          margin-left: 8px;
          font-size: 16px;
          font-weight: 700;
          color: var(--kyy_color_tabbar_item_text, #1a2139);
        }

        .identity-text {
          background-color: #E0F2E5;
          color: #499D60;
          padding: 0 4px;
          font-size: 12px;
          line-height: 20px;
          height: 20px;
          margin-left: 4px;
        }
      }
    }

    .fixed-top-info {
      background-color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background-image: url(@/assets/identity/topBg4.png);
      background-size: 100% 100%;
      transition: transform 5s linear;

      .right-item {
        -webkit-app-region: no-drag;
        display: flex;

        :deep(.btn-item) {
          width: 16px;
          height: 16px;
        }

        :deep(.item-icon) {
          color: #828DA5;
          font-size: 16px;
        }

      }

      .more-card-box {
        display: flex;
        align-items: center;
        margin-left: 6px;
        height: 24px;
      }

      .more-card {
        margin-left: 6px;
        padding: 4px;
        width: 18px;
        height: 18px;
        border-radius: 4px;
        box-sizing: content-box;

        &:hover {
          background-color: #EAECFF;

          .item-icon {
            color: #707EFF;
          }
        }

        .item-icon {
          font-size: 18px;
          color: #828DA5;
        }

      }


    }

    .personal_tabs {
      background-color: #FFFFFF;
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 5px;
      border-radius: 8px;
      margin: 0 12px;
      -webkit-app-region: no-drag;


      .default-tab-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 28px;

        .t-button {
          padding: 0 10px;
          color: var(--kyy_color_tabbar_item_text, #1a2139);
          text-align: center;

          /* kyy_fontSize_2/regular */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
          border: none !important;
        }

        .tab-item-border {
          width: 28px;
          height: 3px;
          margin-top: 3px;
          border-radius: 1.5px;
          background: transparent;
        }
      }

      .active-tab-item {
        .t-button {
          font-weight: 600;
          color: var(--brand-kyy-color-brand-default, #4d5eff) !important;
        }

        .tab-item-border {
          width: 28px;
          height: 3px;
          border-radius: 1.5px;
          background: var(--brand-kyy-color-brand-default, #4d5eff);
        }
      }
    }

    .top-sticky {
      margin: 0;
      padding: 8px 12px;
      padding-top: 0;
      border-radius: 0;
      box-shadow: 0 5px 10px -5px rgba(0, 0, 0, 0.12);
    }

    .info-box {
      width: 100%;
      padding: 8px;
      border-radius: 8px;
      margin-top: 12px;
      background: #fff;

      .t-item {
        padding: 0 8px 0;
        margin-bottom: 4px;
        margin-top: 4px;
        width: 100%;
        overflow: hidden;
      }
    }

    .content {
      padding: 0 12px;
      overflow-x: hidden;
      position: relative;
      z-index: 3;

      .info-wrap:last-child {
        min-height: calc(100vh - 126px);
      }
    }

    .tab-header-title {
      color: var(--text-kyy-color-text-3, #828da5);

      /* kyy_fontSize_2/bold */
      font-family: "PingFang SC";
      padding-left: 8px;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      // margin-bottom: 8px;
    }

    .emptyDataTips {
      color: var(--text-kyy-color-text-3, #828da5);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }

    .w-info {
      -webkit-app-region: no-drag;
      background-color: #fff;
      border-radius: 8px;
      padding: 8px 0 0 0;

      .com-title {
        font-size: 14px;
        font-weight: 700;
        text-align: left;
        color: #13161b;
        line-height: 22px;
        margin-bottom: 12px;
        padding-left: 8px;

        .com-logo {
          width: 24px;
          height: 24px;
          margin-right: 8px;
          border-radius: 50%;
        }
      }
    }

    .contacts {
      width: 304px;
      background-color: #fff;
      margin-top: 8px;
      border-radius: 4px;
      //padding: 8px 16px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .related {
      width: 100%;

      .related-item {
        background-color: #fff;
      }

      .related-item:not(:last-child) {
        padding-bottom: 8px;
        border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
      }
    }

    .w-footer {
      width: 100%;
      padding: 8px 20px;
      position: absolute;
      bottom: 0;
      z-index: 9;
      background-color: #fff;
    }

    .personalHeightContent,
    .heightContent {
      padding-bottom: 40px;
    }
  }

  .flex {
    display: flex;
  }

  .flex-1 {
    flex-grow: 1;
  }

  .voiceBtn {
    padding: 0;
    width: 32px;
    height: 32px;
    border-radius: 50% !important;
    background-color: transparent;
    border-color: transparent !important;

    .icon {
      width: 32px;
      height: 32px;
      margin-right: 0;

      img {
        width: 32px;
        height: 32px;
      }
    }
  }

  .flex-column {
    display: flex;
    flex-direction: column;
  }

  .t-item-hover {
    // padding: 4px 8px;
    border-radius: 8px;

    &:hover {
      background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
    }
  }

  .t-item {
    justify-content: flex-start;
    align-items: flex-start!important;
    &+.t-item {
      // margin-top: 4px;
    }

    .t-title {
      display: inline-block;
      // height: 28px;
      width: 80px;
      word-wrap: break-word;
      flex-shrink: 0;
      // overflow: hidden;
      color: var(--text-kyy-color-text-3, #828da5);
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      padding-top: 4px;
    }

    .t-text {
      min-height: 28px;
      display: inline-block;
      margin-left: 8px;
      color: var(--text-kyy-color-text-1, #1a2139);
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 28px;
      flex-grow: 1;
      overflow: hidden;
    }

    .t-arrow {
      width: 16px;
      height: 16px;
      justify-self: flex-end;
      cursor: pointer;
    }

    .phone-number,
    .link-id {
      align-items: center;
      cursor: pointer;

      .copy-item {
        visibility: hidden;
        width: 24px;
        height: 24px;
        padding: 4px;
        color: #717376;
        border-radius: 4px;
        text-align: center;
        margin-left: 4px;

        .item-icon {
          font-size: 16px;
        }

        &:hover {
          background-color: #EAECFF;

          .item-icon {
            color: #707EFF;
          }
        }
      }

    }

    .phone-number:hover,
    .link-id:hover {
      .copy-item {
        visibility: visible;
      }
    }
  }

  .overflowEllipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .ellipsis1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box !important;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-all;
  }

  .ellipsis2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box !important;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
  }

  .icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  .f-blue {
    color: #2069e3 !important;
  }

  :deep(.t-drawer__body) {
    padding: 0;
    background-color: #f1f2f5;
  }

  :global(.drawerVisible) {
    border-radius: 16px;
  }

  :global(.drawerVisible .t-drawer__content-wrapper) {
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-top: none;
  }

  :global(.drawerVisible .t-drawer__content-wrapper .t-drawer__body) {
    background-color: #fff;
  }

  .removed-text {
    color: red;
    font-size: 12px;
    line-height: 16px;
  }

  .identity-text {
    margin-right: 10px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: rgba(255, 255, 255, 0.16);
    padding: 0px 4px;
    color: var(--text-kyy-color-text-white, #fff);
    text-align: center;

    /* kyy_fontSize_1/regular */
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
  }

  .list {
    &-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      border-radius: 8px;
      padding: 8px;
      background: var(--bg-kyy-color-bg-light, #FFF);
      margin-bottom: 8px;


      .item-title {
        margin-bottom: 4px;
      }

      .li {
        display: flex;
        line-height: 28px;
        margin-top: 4px;

        .label {
          color: var(--text-kyy-color-text-3, #828DA5);
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          width: 80px;
        }
      }
    }
  }

  .bussiness {
    color: var(--text-kyy-color-text-1, #1A2139);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;

    // .tab-header-title{
    //   margin-bottom: 8px;
    // }
    .list-item:not(:last-child) {
      border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
    }
  }

  .bu-box {
    margin-top: 16px;

    .bu-tit {
      color: var(--text-kyy-color-text-1, #1a2139);

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .tag-item {
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1A2139);
    text-align: center;
    text-overflow: ellipsis;
    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
</style>

<style>
  .all-card-drawer {
    max-width: 352px;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid #ddd;

  }

  .all-card-drawer-border-radius {
    border-top-right-radius: 0;
  }

  .all-card-drawer .t-drawer__body {
    padding: 0;
  }

  .all-card-drawer .t-drawer__content-wrapper {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: none;
  }

  .pdxy48 {
    padding: 4px 8px;
    border-radius: 8px;

    &:hover {
      background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
    }
  }

  .can-click {
    color: var(--brand-kyy_color_brand_default, #4D5EFF);
    cursor: pointer;
  }
  .new-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .copy-item {
      margin-top: 5px;
      display: block;
      width: 24px;
      height: 24px;
      padding: 4px;
      color: #717376;
      border-radius: 4px;
      text-align: center;
      margin-left: 4px;
      opacity: 0;
      cursor: pointer;
      .item-icon {
        font-size: 16px;
      }

      &:hover {
        background-color: #EAECFF;

        .item-icon {
          color: #707EFF;
        }
      }
    }
    &:hover {
      .copy-item {
        opacity: 1;
      }
    }
  }
  .edit-btn {
    display: flex;
    width: 64px;
    height: 24px;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border-radius: 99px;
    border: 1px solid var(--border-kyy_color_border_white, #FFF);
    background: var(--icon-kyy_color_icon_black, rgba(0, 0, 0, 0.08));
    cursor: pointer;
    -webkit-app-region: no-drag;
    &:hover {
      background: var(--icon-kyy_color_icon_transparent, #1A21395C);
    }
    .edit-btn-icon {
      display: flex;
      width: 20px;
      height: 20px;
      padding: 4px 4px 3px 3px;
      justify-content: center;
      align-items: center;
    }
    .edit-btn-text {
      color: var(--text-kyy_color_text_white, #FFF);
      text-align: center;

      /* kyy_fontSize_1/bold */
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px; /* 166.667% */
    }
  }
</style>
