// 仅示例
import { client_orgRequest, squareRequest } from '@renderer/utils/apiRequest';
import { TransferRequest } from "@/api/approval/model/approvalData";

// 新建分组
export function kefuAdmins(data, headers?) {
  return client_orgRequest({
    url: `/kefu/admins`,
    method: 'POST',
    data,
    headers: { teamId: localStorage.getItem("workBenchTeamid"), ...headers },
  });
}

// 分组列表
export function kefuAdminsList(data, headers?) {
  return client_orgRequest({
    url: '/kefu/admins',
    method: 'GET',
    headers: { teamId: localStorage.getItem("workBenchTeamid"), ...headers },
    data,
  });
}

// 设置客服
export function updateKeFuMember(data, headers?) {
  return client_orgRequest({
    url: '/kefu/admins/updateKeFuMember',
    method: 'PUT',
    headers: { teamId: localStorage.getItem("workBenchTeamid"), ...headers },
    data,
  });
}

// 修改客服组
export function updateKeFuGroup(data, headers?) {
  return client_orgRequest({
    url: '/kefu/admins/updateKeFuGroup',
    method: 'PUT',
    headers: { teamId: localStorage.getItem("workBenchTeamid"), ...headers },
    data,
  });
}


// 删除客服组
export function deleteKeFuGroup(id, data?) {
  return client_orgRequest({
    url: `/kefu/admins/${id}`,
    method: 'DELETE',
    data,
    headers: { teamId: localStorage.getItem("workBenchTeamid") },

  });
}


// 渠道列表
export function channelListsKeFuGroup(data?) {
  return client_orgRequest({
    url: `/kefu/admins/channelLists`,
    method: 'GET',
    data,
    headers: { teamId: localStorage.getItem("workBenchTeamid") },

  });
}

// 修改渠道
export function updateKeFuChannel(data?) {
  return client_orgRequest({
    url: `/kefu/admins/updateKeFuChannel`,
    method: 'PUT',
    data,
    headers: { teamId: localStorage.getItem("workBenchTeamid") },

  });
}

// 套餐联系客服获取客服组列表
export const getOfficialKeFuGroupList = (params: {
  /**
   * 渠道标识（package：套餐）
   */
  channel_type: 'package';
}) => client_orgRequest.get('/kefu/getOfficialKeFuGroupList', {
  params,
  headers: { teamId: localStorage.getItem("workBenchTeamid") },
});

// 套餐联系客服创建会话
export const createOfficialKeFuSession = (params: {
  /**
   * 客服组id
   */
  groupId: number;
  /**
   * 客户身份卡标识
   */
  main: string;
}) => client_orgRequest.get('/kefu/createOfficialKeFuSession', {
  params,
  headers: { teamId: localStorage.getItem("workBenchTeamid") },
});

// 获取年费套餐
export function annualFeePackage(data?) {
  return squareRequest.get(`/v1/shared/team_annual_fee`, {
    params: data,
    headers: { teamId: localStorage.getItem("workBenchTeamid") },
  });
}

export function keFuChangeStatus(data) {
  return client_orgRequest({
    url: `/kefu/admins/changeStatus`,
    method: 'PUT',
    data,
    headers: { teamId: localStorage.getItem("workBenchTeamid") },
  });
}

export function keFuCreate(data) {
  return client_orgRequest({
    url: `/kefu/admins`,
    method: 'post',
    data,
    headers: { teamId: localStorage.getItem("workBenchTeamid") },
  });
}

export function keFuUpdate(data) {
  return client_orgRequest({
    url: `/kefu/admins/updateKeFuMember`,
    method: 'PUT',
    data,
    headers: { teamId: localStorage.getItem("workBenchTeamid") },
  });
}

export function keFuSort(data) {
  return client_orgRequest({
    url: `/kefu/admins/changeSort`,
    method: 'PUT',
    data,
    headers: { teamId: localStorage.getItem("workBenchTeamid") },
  });
}

// 获取客服组
export const getKeFuGroupList = (params, teamId) => client_orgRequest.get('/kefu/getKeFuGroupList', { params, headers: { teamId } });

// 创建会话
export const createSession = (params, teamId) => client_orgRequest.get('/kefu/createSession', { params, headers: { teamId } });
// 解除咨询会话
export const terminationSession = (data) => client_orgRequest.post('/kefu/termination', data );
// 获取红点
export const getWorkshopRedMenuTotal = (data, teamId?) => client_orgRequest.post('/workshop/app/menu_total', data);
// 获取数字平台红点
export const getDigitalPlatformRedMenuTotal = (data, teamId?) => client_orgRequest.post('/platform/app/menu-red-dot', data);
