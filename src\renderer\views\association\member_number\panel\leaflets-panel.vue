<template>
  <!-- show-overlay -->
  <!-- <t-loading size="medium" class="memberLoading" :loading="!isAShow"   text="加载中..." > -->

  <div  v-lkloading="{ show:!isAShow, height:false, opacity:true }" class="pageTop">
    <!-- <div class="fixTop" :class="{isOpacity: scrolledDistance > 450}">
      <div  class="header">

        <img src="@/assets/association/icon/linker.svg">
        <span class="bot">·</span>
        <span class="title">{{ $t('member.govert.i') }}</span>


      </div>
      <t-button theme="warning" @click="onCreateMember">{{ $t('member.govert.k') }}</t-button>
    </div> -->
    <div class="backTop cursor" v-show="scrolledDistance > 30" :class="{isOpacity: scrolledDistance > 60}" @click="scrollToTop">
      <!-- <iconpark-icon name="iconarrowup" class="iconarrowup"></iconpark-icon> -->
      <iconpark-icon class="iconarrowup" name="icontopinside"></iconpark-icon>

    </div>
    <div style="background-color: #fff; height: inherit;" >
      <div ref="containerFlets"  class="leaflets">
        <div class="content">
          <!-- <span class="global"></span> -->
          <div class="box">
            <div class="header">
              <img src="@/assets/politics/icon/linker.svg">
              <span class="bot">·</span>
              <span class="title">{{$t('association.workbench.a')}}</span>
              <span class="tip">{{ $t('member.regular.selected_products') }}</span>
            </div>
            <div class="desc">
              <!-- 连接数字社群，升级私域价值 -->
              {{ $t('association.aso.o') }}
              <!-- 群聊场景的革命性升级，开启财富增长新时代 -->
            </div>
            <div class="tips">

              <!-- 增强商品和服务流通，打破地域限制，促进资源优化配置，提高市场效率 -->
              <!-- {{ $t('member.govert.j') }} -->
              {{ $t('association.aso.p') }}
            </div>
            <div class="create cursor" @click="onCreateMember">
              {{$t('association.workbench.d')}}
              <!-- {{ $t('member.govert.k') }} -->
            </div>

            <div class="cards">
              <div v-for="card in cards" :key="card.icon" :class="onGearateClass(card.icon)">
                <div class="card-name">

                  <i :class="[`i-svg-color:${card.icon}`, 'svg']"></i>
                  <span class="c">
                    <span class="text">     {{ card.label }}</span>
                    <span class="tip">     {{ card.tip }}</span>
                  </span>


                </div>
                <div class="card-desc" v-html="card.value">

                </div>
              </div>
            </div>


          </div>
          <div class="exampleT">
            <div class="example h-100%">
              <div class="example-desc">
                <div class="toLeft">
                  <div class="top"> {{ $t('member.regular.imported_member') }}</div>
                  <div class="tip">  {{ $t('association.aso.i') }}</div>
                </div>
                <div class="toRight">
                  <span class="btn" @click="onJoinDigital">
                    <iconpark-icon name="iconpeopleadd" class="add"></iconpark-icon>
                    {{ $t('association.aso.q') }}
                  </span>
                </div>
              </div>
              <div class="example-row mt-28px">

                <div v-for="(menu, menuIndex) in memberList" :key="menuIndex" class="bofox">

                  <div class="public">
                    <img v-lazy="menu.avatar || ORG_DEFAULT_AVATAR" class="logo">
                    <span class="public-name line-1 mt-12px">{{ menu.name }}</span>
                    <span class="public-tip line-2 mt-4px">{{ menu.intro }}</span>
                  </div>
                  <!-- <span class="bofox-tip">{{ menu.tab_one_desc }}</span> -->

                  <div class="description">
                    <div class="relate">
                      <img v-lazy="menu.avatar || ORG_DEFAULT_AVATAR" class="logo">
                      <div class="title line-1"> {{ menu.name }}</div>
                      <span class="square cursor" @click="onGoSquarePage(menu)">{{ $t('member.long.org_2') }}</span>
                    </div>
                  </div>
                </div>

              </div>
              <div class="example-more mt-24px">
                <span v-if="(!page) || (page && page.nextPageToken )" class="more cursor" @click="onMore"> {{ isLoading ? '加载中...': '加载更多' }} </span>
                <span v-else class="noempty">
                  <span class="line"></span><span class="toText">{{ $t('member.long.org_3') }}</span>  <span class="line"></span>
                </span>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- </t-loading> -->

    <!-- </t-loading> -->
    <AnnualFeeDialog
    v-if="renewalDialogVisible"
    v-model="renewalDialogVisible"
    :upgrade="upgrade"

    :team-id="currentTeamId"
    @success="renewalSuccess"
  />
  <!-- 组织未创建，先创建组织再购买套餐 -->
  <OpenSquare
  v-if="openSquareVisible"
  v-model="openSquareVisible"
  @success="openSquareVisible = false" />
  <!-- 组织已创建（传 open 标识），直接选择套餐购买 -->
  <AnnualFeeDialog
    v-if="openVisible"
    v-model="openVisible"
    open
    :team-id="currentTeamId"
    :invite-code="''"
    @success="buySuccess"
  />

  <!-- 套餐升级（传 upgrade 标识） -->
  <AnnualFeeDialog
    v-if="annualFeeDialogUpgradeVisible"
    v-model="annualFeeDialogUpgradeVisible"
    upgrade
    :team-id="currentTeamId"
    @success="upgradeLoaded"
  />

  <!-- 套餐续费 -->
  <AnnualFeeDialog
    v-if="annualFeeDialogRenewVisible"
    v-model="annualFeeDialogRenewVisible"
    :team-id="currentTeamId"
    @success="renewalSuccess"
  />

  <CreateOrganizeModel ref="createOrganizeModelRef" :from="originType.CBD" @sucess="onCreateOrganizeSuccess"/>
  <SuccessOpenMember ref="successOpenMemberRef" :teamId="currentTeamId"/>
  <ExpirePackage ref="expirePackageRef" :teamId="currentTeamId" @toVerifyWeb="onOpenRewal" @toClose="onCloseUpgrade"/>
  <UpgradePackage ref="upgradePackageRef" :teamId="currentTeamId" @toVerifyWeb="onOpenUpgrade" @toClose="onCloseUpgrade"/>
  <!-- <CbdAndAssociationModal ref="cbdAndAssociationModalRef"/> -->
  <OpenSecondAssociationModal ref="openSecondAssociationModalRef" @onOk="onRunningAnnel"/>
  <joinDrawer  ref="joinDrawerRef"/>
</template>

<script setup lang="ts">
import { ref, toRaw, watch, onMounted, onUnmounted, computed, Ref } from "vue";
// import anhui from '@renderer/assets/member/icon/left-anhui.png';
// import aomen from '@renderer/assets/member/icon/left-aomen.png';
// import company from '@renderer/assets/member/icon/left-company.png';
// import jiangsu from '@renderer/assets/member/icon/left-jiangsu.png';
import CreateOrganizeModel from "@renderer/components/contacts/dialog/createOrganize.vue"
import SuccessOpenMember from '@renderer/views/association/member_number/modal/success-open-member.vue';
import ExpirePackage from '@renderer/views/member/member_number/modal/expire-package.vue';
import UpgradePackage from '@renderer/views/member/member_number/modal/upgrade-package.vue';
// import CbdAndAssociationModal from '@renderer/views/association/member_number/modal/cbd-and-association-modal.vue';
import joinDrawer from "@renderer/views/digital-platform/components/join-drawer.vue";

import OpenSecondAssociationModal from '@renderer/views/association/member_number/modal/open-second-association-modal.vue'


import { getSquaresAxios } from "@renderer/api/association/api/businessApi";
import { checkExpiration, getResponseResult } from "@renderer/utils/myUtils";
import { useI18n } from "vue-i18n";
import { toSquareHome } from "@renderer/views/square/utils/ipcHelper";
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute, useRouter } from "vue-router";
import { platform, originType } from "@renderer/views/digital-platform/utils/constant";
import { getAssociationTeamID } from "@renderer/views/association/utils/auth";
import { SquareType } from "@renderer/api/square/enums";
import { getAuthorityCheckAxios, getCommonAppAuthAxios, getOpenApp, getTeamAnnualFee } from "@renderer/api/digital-platform/api/businessApi";
import to from "await-to-js";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
// import { useAssociationStore } from "../../store/association";
import AnnualFeeDialog from "@/views/square/components/annual-fee/AnnualFeeDialog.vue";
import OpenSquare from "@/views/square/components/OpenSquare.vue";
import { FeePackageItemType } from "@renderer/api/square/models/fee";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import { onActivated } from "vue";
import { useApi } from "@renderer/views/association/hooks/api";
import { isLimitMasPlatformDialog } from '@renderer/views/digital-platform/utils/auth.ts'


const router = useRouter();
const { menuList, routeList, roleFilter } = useRouterHelper("digitalPlatformIndex");
const openSquareVisible = ref(false);
const renewalDialogVisible = ref(false);
const openVisible = ref(false);

const upgrade = ref(false);
const { onActionSquare } = useApi();

const digitalPlatformStore = useDigitalPlatformStore();
// const store = useAssociationStore();
const { t } = useI18n();
const isAShow = ref(false);
const route = useRoute();
setTimeout(()=> {isAShow.value = true}, 500 )
const cards = ref([
    {
        icon: 'association_card1',
        label: t('association.aso.r'),
        value: t('association.aso.s'),
        tip: t('association.aso.t')
    },
    {
        icon: 'association_card2',
        label: t('association.aso.u'),
        value: t('association.aso.v'),
        tip: t('association.aso.w')
    },
    {
        icon: 'association_card3',
        label: t('association.aso.x'),
        value: t('association.aso.y'),
        tip: t('association.aso.z')
    },
    {
        icon: 'association_card4',
        label: t('association.leaft.a'),
        value: t('association.leaft.b'),
        tip: t('association.leaft.c'),
    },
    {
        icon: 'association_card5',
        label: t('association.leaft.d'),
        value: t('association.leaft.e'),
        tip: t('association.leaft.f'),
    },
    {
        icon: 'association_card6',
        label: t('association.leaft.g'),
        value: t('association.leaft.h'),
        tip: t('association.leaft.i'),
    },
]);
const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})


// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else {
    return  getAssociationTeamID()
  }
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    // return store.activeAccount
  }
})

// const menuList = ref([
//    {
//        icon: jiangsu,
//        name: '江苏省山东商会',
//        tab_one_tip: '江苏省民政厅4A级资信\n会员单位和团体会员六千余家',
//        tab_two: '获评:江苏省民政厅4A级资信至2021年底，商会拥有会员单位和团体会员6100余家。会员企业产业分布广泛，主要有建筑工程、化工、新能源、房地产、金融投资、IT业、商贸、文化、农业、物流等。'
//    },
//    {
//        icon: anhui,
//        name: '广东省安徽商会',
//        tab_one_tip: '广东省5A级社会组织\n广东年度经济风云商会称号',
//        tab_two: '获评：广东省5A级社会组织当选：广州市异地商会联合会执行会长单位荣晋:广东省社会组织总会执行会长单位荣获：广东年度经济风云商会称号(广东省唯一获此殊荣的异地商会)'
//    },
//    {
//        icon: anhui,
//        name: '澳门建造商会',
//        tab_one_tip: '团结建造业从业员及商号\n建立与政府的沟通渠道',
//        tab_two: '团结建造业从业员及商号,推动从业员培训及考核制度,建立与政府的沟通渠道 '
//    },
//    {
//        icon: company,
//        name: '新海通机械租赁有限公司',
//        tab_one_tip: '澳门机械租赁行业领导者\n澳门泵送行业第一品牌',
//        tab_two: '《新海通机械租赁有限公司》——澳门机械租赁行业领导者2005年，新海通成立，主要从事建筑机械设备的租赁及销售行业等。2013年，成为澳门泵送行业第一品牌。2015年，成为澳门机械安装业务主要服务商，多次获得各施工项目的安全奖项。2019年，开展鑽切服务，向建筑工程拆除领域进军。2020年至今，服务澳门新濠影汇2期、银河3A、银河3D酒店等多个大型工程项目。 '
//    },
// ]);
const joinDrawerRef = ref(null);
const onJoinDigital = () => {
  joinDrawerRef.value?.onOpen();
}

const isLoading = ref(false);
const memberList = ref([]);
const page = ref(null);
const onGetSquaresAxios = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  isLoading.value = true;
  try {
    result = await getSquaresAxios({ 'page.size': 25, square_type: SquareType.Enterprise, 'page.next_page_token': page.value ? page.value.nextPageToken : '' }, currentTeamId.value);
    result = getResponseResult(result);
    isLoading.value = false;
    if (!result) {

      return;
    }
    memberList.value = memberList.value.concat(result.items);
    page.value = result.page;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
  //   MessagePlugin.error(errMsg);
    console.log(errMsg);
  }
  isLoading.value = false;


};
const onMore = () => {
  onGetSquaresAxios();
};
const onSearch = () => {
  memberList.value = [];
  onGetSquaresAxios();
};
onSearch();
const onGearateClass = (type) => {
    const obj = {
        card: true,
    };
    obj[type] = true;
    return obj;
};
const onGoSquarePage = (square) => {
  console.log(square);
  onActionSquare({}, square?.squareId)
};


watch(() => digitalPlatformStore.openAssociationPackage, (newValue) => {
  console.log('openAssociationPackage', newValue)
  if (newValue) {
    setTimeout(()=> {
      onCreateMember();
      digitalPlatformStore.setOpenAssociationPackage(false)
    })
  }
}, {
  immediate: true,
})
const annualFeeDialogUpgradeVisible: Ref<any> = ref(false);
const annualFeeDialogRenewVisible: Ref<any> = ref(false);
const expirePackageRef = ref(null);
const upgradePackageRef = ref(null);
const successOpenMemberRef = ref(null);
const createOrganizeModelRef = ref(null);

// mac商店类型
const isMas = ref(__APP_ENV__.VITE_APP_MAS);
const openSecondAssociationModalRef = ref(null);
const onCreateMember = async () => {
  // return createOrganizeModelRef.value?.beginCreate();
  // （1）若用户没有加入任何组织，弹窗创建组织。>>创建组织
  if(!currentTeamId.value) {
    createOrganizeModelRef.value?.beginCreate();
    return;
  }

  if(isMas.value) {
    isLimitMasPlatformDialog();
    return;
  }

  // （2）若当前组织已开启任意数字平台应用，提示：该组织已开启【数字XXX】
  const [err, res] = await to(getCommonAppAuthAxios({teamId:currentTeamId.value}, currentTeamId.value));
  if(err) {
    return;
  }
  const { data } = res;
  console.log('hah', data)
  if(data.data.find(v => v.uuid === 'government')?.auth) {
    MessagePlugin.warning('该组织已开启【数字城市】');
    return;
  } else if(data.data.find(v => v.uuid === 'member')?.auth) {
    MessagePlugin.warning('该组织已开启【数字商协】');
    return;
  } else if(data.data.find(v => v.uuid === 'cbd')?.auth) {
    MessagePlugin.warning('该组织已开启【数字CBD】');
    return;
  } else if(data.data.find(v => v.uuid === 'association')?.auth) {
    MessagePlugin.warning('该组织已开启【数字社群】');
    return;
  }
  // （3）若用户没有应用管理权限，提示：需要有“应用管理”权限的管理员才能开启
  const [err1, res1] = await to(getAuthorityCheckAxios({items: ['application']}, currentTeamId.value));
  if(err1) {
    MessagePlugin.error(err1?.message)
    return;
  }
  console.log(res1?.data)
  if(res1?.data?.data?.application !== 1) {
    MessagePlugin.warning('需要有“应用管理”权限的管理员才能开启');
    return;
  }
  // （4）若当前组织类型与开启的应用类型不匹配，弹窗提示去开启组织类型对应的数字平台。>>组织类型不匹配
  console.log(activeAccount.value)
  if(![1, 3].includes(activeAccount.value?.teamType)) { // 不是企业类型和个体户类型
    if(activeAccount.value?.teamType === 2) { // 商协会
      isOpenMemberAppDialog() // 仅支持开通商协会
    } else if(activeAccount.value?.teamType === 4) { // 政企
      // isOpenCBDAndAssociationAppDialog(); // 仅支持开通CBD
      isOpenGovernmentAppDialog();
      // cbdAndAssociationModalRef.value?.onOpen();
    } else {
      isUnOpenDigitalAppDialog() // 不支持开通平台
    }
    return;
  }


  // 增加二次确认环节
  openSecondAssociationModalRef.value?.onOpen();
  // return;


  // （5）若当前组织未购买另可年费套餐，弹窗购买套餐。>>购买
  // （6）若当前组织购买的套餐已过期，弹窗提示套餐已过期。>>套餐已过期
  // （7）若当前组织购买的套餐未勾选数字平台搭建权益，则弹窗提示权益不足需升级套餐。>>套餐升级
  // （8）若组织购买的套餐已勾选数字平台搭建权益，则提示应用开启成功，同时工作通知发送一条开启成功通知
  // onRunTeamAnnualFee();

  // successOpenMemberRef.value.onOpen()

};

const onRunningAnnel = ()=> {
  onRunTeamAnnualFee();
}

const onOpenRewal = () => {
  expirePackageRef.value.onSetVisible(false);
  annualFeeDialogRenewVisible.value = true;
}
const onOpenUpgrade = () => {
  upgradePackageRef.value.onSetVisible(false);
  annualFeeDialogUpgradeVisible.value = true;
}

const onCloseUpgrade = () => {
  // upgradePackageRef.value = false;
  // onAfterSet();
}

const onAfterSet = () => {
  // onClose();
  digitalPlatformStore.removeAllTab();
  digitalPlatformStore.switchTab(0);
  if(digitalPlatformStore.tabs.length > 0) {
    router.push(digitalPlatformStore.tabs[0].fullPath)
  }
};


// 升级后的回调
const upgradeLoaded = () => {
  console.log('upgradeLoaded')
  // 升级后重新触发流程
  upgradePackageRef.value.onClose();
  MessagePlugin.success('购买成功')

  // onRunTeamAnnualFee()
}

// 购买套餐成功
const buySuccess = (val?) => {
  console.log(val)
  MessagePlugin.success('购买成功')

  // 购买成功提示弹窗
  // successOpenMemberRef.value.onOpen()
  // onRunTeamAnnualFee();
}

const onCreateOrganizeSuccess = (params, datas)=> {
  console.log(params, datas);
  MessagePlugin.success('创建成功');
  setTimeout(()=> {
    if(platformCpt.value === platform.digitalPlatform) {
      digitalPlatformStore.setGoTeam(datas.teamId)
    }
    //  else {
    //   store.setGoTeam(teamId); // 切换teamid到相应组织界面

    // }
  }, 500)
}


const renewalSuccess = (e) => {
  console.log(e, "操作成功");
  // renewalDialogVisible.value = false;
  // openSquareVisible.value = false;
  // getTeamAnnualFeeRun(store.activeAccount?.teamId);
  // onRunTeamAnnualFee();
  MessagePlugin.success('购买成功')

};


const onRunTeamAnnualFee = async () => {
  const [err2, res2]:any = await to(getTeamAnnualFee({team_id: currentTeamId.value,annual_fee_detail:true}, currentTeamId.value));
  if(err2) {
    return;
  }
  const { data } = res2;
  console.log(data)
  // data.annualFeeExpiredAt = '2023-05-15T07:25:50Z'

  if(data?.annualFeeDetail?.trial) { // 表明在体验套餐范畴
    // 当前是否在体验套餐有效期内
    if(checkExpiration(data?.annualFeeDetail?.expiredAt)) { // 不在体验套餐有效期内
      // 判断是否购买正式套餐
      // MessagePlugin.error('体验套餐已过期，请购买正式套餐');
      openVisible.value = true;
      // const confirmDiaexpExpire = DialogPlugin({
      //   header: '连接数不足',
      //   theme: 'info',
      //   body: '体验套餐已过期，连接数不足，请先购买套餐',
      //   closeBtn: null,
      //   cancelBtn: '取消',
      //   confirmBtn: '立即购买',
      //   className: 'delmode',
      //   onConfirm: async () => {
      //     confirmDiaexpExpire.hide();
      //     // resolve(TeamAnnualTypeError.PurchaseOfficialPackage); // 购买正式套餐

      //   },
      //   onClose: () => {
      //     // reject('');
      //     confirmDiaexpExpire.hide();
      //   },
      // });

    } else if(data?.annualFeeDetail?.package?.items?.length > 0 && data.annualFeeDetail.package.items.some(v=>v.itemType === FeePackageItemType.DigitalPlatform)) { // 在体验套餐有效期内
      // 冯伟乐那边判断连接数
      // MessagePlugin.error('冯伟乐那边判断连接数');

      // 当前体验套餐是否勾选数字平台搭建 8.8
      onOpenMember();

      // const bodyRender2 = {

      // }
      // onConnectPandalAxios(bodyRender2, currentTeamId.value).then((resPanel)=> {

      //   if(resPanel === 'update') { // 购买正式套餐
      //     // resolve(TeamAnnualTypeError.PurchaseOfficialPackage)
      //     openVisible.value = true;
      //   }
      // })
      // reject();
      return;
    } else { //购买套餐
      openVisible.value = true;
      return;
    }
  } else {
    if(!data.opened) { //（5）若当前组织未购买另可年费套餐，弹窗购买套餐。>>购买
      openVisible.value = true;
    } else if (checkExpiration(data.annualFeeExpiredAt)){ // （6）若当前组织购买的套餐已过期，弹窗提示套餐已过期。>>套餐已过期
      expirePackageRef.value.onOpen();

    } else if(!(data?.annualFeeDetail?.package?.items?.length > 0 && data.annualFeeDetail.package.items.some(v=>v.itemType === FeePackageItemType.DigitalPlatform)) ) { //
      // （7）若当前组织购买的套餐未勾选数字平台搭建权益，则弹窗提示权益不足需升级套餐。>>套餐升级
      upgradePackageRef.value.onOpen();
    } else { // （8）若组织购买的套餐已勾选数字平台搭建权益，则提示应用开启成功，同时工作通知发送一条开启成功通知
      // renewalDialogVisible.value = true;
      // successOpenMemberRef.value.onOpen()
      onOpenMember();
    }
  }





  // if(data?.is_annual_fee === 0) {
  //   const confirmDia = DialogPlugin({
  //     header: '请先购买套餐',
  //     theme: 'info',
  //     body: '请先购买套餐',
  //     closeBtn: null,
  //     cancelBtn: '暂不购买',
  //     confirmBtn: '去购买',
  //     className: 'delmode',
  //   })
  // }
};

// 开启应用操作
const onOpenMember = async () => {

  const [err, res]:any = await to(getOpenApp({uuid: "association"}, currentTeamId.value));
  if(err) {
    MessagePlugin.error(err?.message)
    return;
  } else {
    successOpenMemberRef.value.onOpen()
  }
// const { data } = res;
};

const isOpenCBDAndAssociationAppDialog = ()=> {
  const confirmDia = DialogPlugin({
    header: '该组织类型仅能开启数字CBD或数字社群',
    theme: 'info',
    body: '该组织类型为企业，仅能开启数字CBD或数字社群',
    closeBtn: null,
    cancelBtn: '暂不开启',
    confirmBtn: '开启数字CBD',
    className: 'delmode',
    onConfirm: async () => {
      // 删除字段操作
      confirmDia.hide();
        // 跳转到数字商协
      const key = 'digital_platform_cbd_leaflets';
      const searchMenu = routeList.find((v) => v.name === key);
      searchMenu.query = {...searchMenu.query, platform: platform.digitalPlatform};
      // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
      router.push({ path: searchMenu.fullPath, query: searchMenu.query  });

      digitalPlatformStore.setOpenCbdPackage(true);
      digitalPlatformStore.addTab(toRaw(searchMenu), true);
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
}


const isOpenGovernmentAppDialog = ()=> {
  const confirmDia = DialogPlugin({
    header: '该组织类型仅能开启数字城市',
    theme: 'info',
    body: '该组织类型为政府单位，仅能开启数字城市',
    closeBtn: null,
    cancelBtn: '暂不开启',
    confirmBtn: '开启数字城市',
    className: 'delmode',
    onConfirm: async () => {
      // 删除字段操作
      confirmDia.hide();
        // 跳转到数字城市
      const key = 'digital_platform_politics_leaflets';
      const searchMenu = routeList.find((v) => v.name === key);
      searchMenu.query = {...searchMenu.query, platform: platform.digitalPlatform};
      // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
      router.push({ path: searchMenu.fullPath, query: searchMenu.query  });

      digitalPlatformStore.setOpenPoliticsPackage(true);
      digitalPlatformStore.addTab(toRaw(searchMenu), true);
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
}

const isOpenMemberAppDialog = ()=> {
  const confirmDia = DialogPlugin({
    header: '该组织类型仅能开启数字商协',
    theme: 'info',
    body: '该组织类型为商协会，仅能开启数字商协',
    closeBtn: null,
    cancelBtn: '暂不开启',
    confirmBtn: '开启数字商协',
    className: 'delmode',
    onConfirm: async () => {
      confirmDia.hide();
      // 跳转到数字商协
      const key = 'digital_platform_member_leaflets';
      const searchMenu = routeList.find((v) => v.name === key);
      searchMenu.query = {...searchMenu.query, platform: platform.digitalPlatform};
      // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
      router.push({ path: searchMenu.fullPath, query: searchMenu.query  });

      digitalPlatformStore.setOpenMemberPackage(true);
      digitalPlatformStore.addTab(toRaw(searchMenu), true);
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
}
const isUnOpenDigitalAppDialog = ()=> {
  const confirmDia = DialogPlugin({
    header: '该组织类型暂不支持开启数字平台',
    theme: 'info',
    body: '',
    cancelBtn: null,

    confirmBtn: '我知道了',
    className: 'delmode',
    onConfirm: async () => {
      // 删除字段操作
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
}









const scrolledDistance = ref(0); // 滚动距离
const containerFlets = ref(null);
const handleScroll = (event) => {
  console.log(event.target.scrollTop);
  scrolledDistance.value = event.target.scrollTop;
  // scrolledDistance.value = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
};
let animationId = null;
const scrollToTop = () => {
  console.log(containerFlets.value.scrollTop);
  // containerFlets.value.scrollTop = 0; // 滚动容器到顶部
  cancelAnimationFrame(animationId); // 取消之前的动画

      const scrollTop = containerFlets.value.scrollTop;
      console.log(containerFlets.value.scrollTop);
      const step = Math.ceil(scrollTop / 6); // 每帧滚动的步长
      console.log(step);
      const animate = () => {
        if (containerFlets.value.scrollTop > 0) {
          containerFlets.value.scrollTop -= step;
          animationId = requestAnimationFrame(animate); // 请求下一帧动画
        } else {
          cancelAnimationFrame(animationId); // 动画结束，取消请求
        }
      };

      animationId = requestAnimationFrame(animate); // 开始动画
};
onActivated(()=> {
  if(digitalPlatformStore.openMemberPackage) {
    digitalPlatformStore.setOpenMemberPackage(false);
  }
  if(digitalPlatformStore.openPoliticsPackage) {
    digitalPlatformStore.setOpenCbdPackage(false);
  }
  if(digitalPlatformStore.openCbdPackage) {
    digitalPlatformStore.setOpenCbdPackage(false);
  }
})
onMounted(() => {
  containerFlets.value?.addEventListener('scroll', handleScroll); // 监听滚动事件
});
onUnmounted(() => {
  containerFlets.value?.removeEventListener('scroll', handleScroll); // 取消监听滚动事件
});

</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";
.memberLoading {
  height: 100%;
}
.pageTop {
  height: calc(100% - 13px);
  overflow-y:auto;
  // padding-bottom: 24px;
  background-color: #fff;

}
.backTop {
  position: fixed;
  right: 16px;
  bottom: 32px;
  opacity: 0;
  transition:all 0.25s linear;

  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  // border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
  background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.50));

  .iconarrowup {
    font-size: 20px;
    color: #1A2139;
  }
  .text {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}
.isOpacity {
  opacity: 1 !important;
  transition:all 0.25s linear;
}
.fixTop {

  position: fixed;
  height: 56px;
  left: 0;
  right: 0;
  // background: var(--bg-kyy_color_bg_default, #FFF);
  background: linear-gradient(153deg, #C83137 16.99%, #B11C13 94.03%);
  opacity: 0;
  z-index: 10;
  /* kyy_shadow_s */
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
  transition:all 0.25s linear;

  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  .header {
    display: flex;
    align-items: center;
    letter-spacing: 1px;
    img {
      // background-position:cover;
      // width: 136px;
      height: 32px;
    }
    .bot {
        margin: 0 12px;
    }
    .title, .bot {
      color: var(--kyy_color_toopltip_text, #FFF);
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px; /* 144.444% */
    }
    .tip {
        margin-left: 8px;
        border-radius: 12px 12px 12px 0px;
        background: var(--warning-kyy_color_warning_hover, #FD9D4F);
        padding: 0 10px;
        color: #FFF;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
    }
  }
}
.leaflets {
    // background-image: url('@renderer/assets/member/icon/bg_img.png');
    width: 100%;
    // height: inherit;
    // overflow-y: auto;
    overflow-x: hidden;
    // background-size: cover;
    // background-repeat: no-repeat;
    // background-position: 50% 30%;
    // background-position: center;
    display: flex;
    justify-content: center;
    // background-color: #fff;
    // background: linear-gradient(153deg, #C83137 16.99%, #B11C13 94.03%);
    background: linear-gradient(153deg, #5839C7 16.99%, #5032B5 94.03%);
    height: inherit;



    .content {
        display: flex;
        // justify-content: center;
        flex-direction: column;
        // width: 1216px;
        // min-width: 1152px;
        // max-width: 1216px;
        width: 1216px;

        // position: relative;
        // width: 1216px;

        // .global {
        //     position: absolute;
        //     z-index: 1;
        //     background-image: url('@/assets/member/icon/bg_img.png');
        //     width: 1920px;
        //     overflow: hidden;
        //     height: 700px;
        //     background-position: center;
        //     background-size: contain;
        // }
        .box {
            // position: absolute;
            // z-index: 2;

            padding: 24px 32px;
            padding-top: 32px;
            padding-bottom: 32px;
            // margin-bottom: 32px;
            background-image: url('@renderer/assets/association/icon/bg_img.png');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: 50% 30%;
            .header {
                display: flex;
                align-items: center;
                letter-spacing: 1px;
                img {
                  // background-position:cover;
                  width: 136px;
                }
                .bot {
                    margin: 0 12px;
                }
                .title, .bot {
                  color: var(--kyy_color_toopltip_text, #FFF);
                  font-size: 18px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 26px; /* 144.444% */
                }
                .tip {
                    margin-left: 8px;
                    border-radius: 12px 12px 12px 0px;
                    background: var(--warning-kyy_color_warning_hover, #FD9D4F);
                    padding: 0 10px;
                    color: #FFF;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 20px; /* 166.667% */
                }
            }
            .desc {
                color: #FFF;
                font-size: 32px;
                font-style: normal;
                font-weight: 600;
                line-height: 40px; /* 125% */
                margin-top: 24px;
                letter-spacing: 1px;
            }
            .tips {
              color: #FFF;
              font-size: 18px;
              font-style: normal;
              font-weight: 400;
              line-height: 26px; /* 144.444% */
                margin-top: 12px;
                letter-spacing: 1px;

            }
            .create {
              border-radius: 40px;

              border-radius: 40px;
              background: linear-gradient(90deg, #21CAF9 0%, #36A1FF 100%);
              /* kyy_shadow_m */
              box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);

              color: var(--text-kyy_color_text_white, #FFF);
              font-size: 18px;
              font-style: normal;
              font-weight: 600;
              line-height: 26px; /* 144.444% */
              padding: 8px 24px;
              margin-top: 24px;
              display: inline-block;
              letter-spacing: 1px;
            }
            .cards {
                margin-top: 36px;
                display: flex;
                // gap: 12px;
                flex-wrap: wrap;
                .card {
                    flex:1;
                    margin-right: 12px;
                    margin-bottom: 12px;
                    &:nth-child(3n) {
                      margin-right: 0;
                    }
                    &:nth-child(4) {
                      margin-bottom: 0;
                    }
                    &:nth-child(5) {
                      margin-bottom: 0;
                    }
                    &:nth-child(6) {
                      margin-bottom: 0;
                    }

                    // min-width: 262.5px;
                    height: 148px;
                    // max-width: 279px;
                    // min-width: 372px;
                    // max-width: 372px;
                    min-width: 374px;
                    max-width: 376px;
                    // width: auto;
                    padding: 20px 16px;
                    background-size: contain;
                    object-fit: cover;
                    background-repeat: no-repeat;
                    display: flex;
                    flex-direction: column;
                    gap:  16px;
                    &-name {
                        display: flex;
                        align-items:center;
                        gap: 12px;
                        .svg {
                            // width: 48px;
                            // height: 48px;
                            font-size: 48px;
                        }

                        .c {
                          display: flex;
                          flex-direction: column;
                          gap: 2px;
                          .tip {
                            color: var(--text-kyy_color_text_3, #828DA5);
                            font-size: 16px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 24px; /* 150% */
                          }
                          .text {
                            color: var(--text-kyy-color-text-2, #516082);

                            font-size: 18px;
                            font-style: normal;
                            font-weight: 600;
                            line-height: 26px; /* 144.444% */
                          }
                        }

                    }
                    &-desc {
                        position: relative;
                        padding-left: 10px;
                        color: var(--text-kyy_color_text_2, #516082);
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 157.143% */
                        &::before {
                            display: inline-block;
                            position: absolute;
                            left: 0;
                            // bottom: 4px;
                            bottom: 0;
                            top: 0;
                            content: ' ';
                            width: 2px;
                            // background-color: var(--brand-kyy-color-brand-hover, #707EFF);

                        }
                    }

                }

            }


        }
        .exampleT {
          // margin-top: 32px;
          // background-color: #D5E6FC;
          // background: linear-gradient(153deg, #C83137 16.99%, #B11C13 94.03%);
          background: linear-gradient(153deg, #5839C7 16.99%, #5032B5 94.03%);
        }
        .example {

            // background-image: url('@renderer/assets/member/icon/bg_square_img.png');
            // background-repeat: no-repeat;

            // background: linear-gradient(153deg, #C83137 16.99%, #B11C13 94.03%);
            background: linear-gradient(153deg, #5839C7 16.99%, #5032B5 94.03%);
            display: flex;
            flex-direction: column;
            padding: 24px 32px 24px;
            // padding-bottom: 0;
            // gap: 48px;
            &-desc {

              display: flex;
                justify-content: space-between;
                .toLeft {
                  display: flex;
                  flex-direction: column;
                  gap: 8px;
                }
                .toRight {
                  display: flex;
                  align-items: center;
                  .btn {
                    display: flex;
                    gap: 4px;
                    height: 32px;
                    padding: 0px 12px 0px 8px;
                    align-items: center;
                    user-select: none;
                    cursor: pointer;

                    color: var(--text-kyy_color_text_2, #516082);
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; /* 157.143% */


                    border-radius: 16px;
                    border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
                    background: var(--bg-kyy_color_bg_light, #FFF);
                    transition: all 0.15s linear;
                    .add {
                      transition: all 0.15s linear;
                      color: #828DA5;
                      font-size: 20px;
                    }


                    &:hover {
                      transition: all 0.15s linear;
                      color: var(--brand-kyy_color_brand_hover, #707EFF);
                      .add {
                        transition: all 0.15s linear;
                        color: var(--brand-kyy_color_brand_hover, #707EFF);
                      }
                    }
                  }
                }
                .top {
                  color: #fff;
                  font-size: 18px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 26px; /* 144.444% */
                }
                .tip {
                  color: #fff;

                  font-size: 14px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 22px; /* 157.143% */
                }
            }
            &-row {
                display: flex;
                flex-wrap: wrap;
                gap: 12px;
                width: 100%;

                .bofox {
                    width: calc((100% - 48px) / 5 );
                    // width: 220px;
                    height: 162px;

                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    position: relative;

                    border-radius: 8px;
                    // gap: 6px;
                    background: var(--bg-kyy-color-bg-default, #FFF);

                    .description {
                        position: absolute;
                        // width: 221px;
                        top: 0;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        z-index: 2;
                        background-color:#fff;
                        visibility: hidden;
                        // transition: all 0.25s linear;
                        // padding: 32px 16px 12px;

                        border-radius: 8px;
                        // background: var(--bg-kyy-color-bg-default, #FFF);

                        // background: var(--brand-kyy_color_brand_default, #4D5EFF);
                        background: #0F8EFF;

                        /* kyy_shadow_s */
                        // box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);



                        .relate {
                            width: 100%;
                            position: relative;
                            background-image: url('@renderer/assets/association/icon/association_box.svg');
                            background-size: cover;
                            background-repeat: no-repeat;
                            height: 100%;
                            border-radius: 8px;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            gap: 12px;
                            padding: 16px;

                            .logo {
                                left: 0;
                                right: 0;
                                margin: 0 auto;
                                top: -56px;

                            }
                            .title {

                              width: inherit;
                              color: var(--text-kyy_color_text_white, #FFF);
                              text-align: center;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: 600;
                              line-height: 22px; /* 157.143% */
                            }
                            .square {

                              border-radius: var(--radius-kyy_radius_button_s, 4px);
                              border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
                              background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
                              padding: 4px 16px;
                              color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
                              text-align: center;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: 600;
                              line-height: 22px; /* 157.143% */
                            }

                        }

                    }


                    &:hover {
                        .description {
                            visibility: visible;

                            // transition: all 0.25s linear;
                            // height: 400

                        }

                    }
                    .logo {
                        // position: absolute;
                        // top: -24px;
                        width: 48px;
                        height: 48px;
                        border-radius: 48px;
                        object-fit: cover;
                    }
                    .public {
                      width: 100%;
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                      padding: 16px;
                      // gap: 4px;



                      &-name {
                          flex: 1;
                          width: inherit;
                          color: var(--text-kyy-color-text-1, #1A2139);
                          text-align: center;

                          font-size: 14px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 22px; /* 157.143% */
                      }
                      &-tip {
                              overflow: hidden;
                              color: var(--text-kyy-color-text-3, #828DA5);
                              text-align: center;
                              text-overflow: ellipsis;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: 400;
                              line-height: 22px; /* 157.143% */
                      }
                    }

                }
            }
            &-more {
              display: flex;
              align-items: center;
              justify-content: center;
              .more {
                border-radius: var(--radius-kyy_radius_button_s, 4px);
                border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
                background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
                color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
                text-align: center;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
                padding: 4px 16px;
              }
              .noempty {
                color: var(--text-kyy_color_text_2, #516082);
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
                display: flex;
                align-items: center;
                width: 100%;
                gap: 12px;
                .toText {
                  flex: none;
                  color: #D5DBE4;
                }
                .line {
                  height: 1px;
                  background: var(--divider-kyy_color_divider_deep, #D5DBE4);
                  width: 100%;
                }
              }
            }
        }

    }

}

.association_card1 {
  //  background-image: url('@renderer/assets/association/icon/card1.svg');
  border-radius: 16px;
  border: 1px solid #FFF;
  background: linear-gradient(180deg, #D1E8FF 0%, #FFF 100%), var(--bg-kyy_color_bg_default, #FFF);

  .card-desc {

  //   border-left: 2px solid var(--brand-kyy-color-brand-hover, #707EFF);
    &::before {
      // display: inline-block;
      // position: absolute;
      // left: 0;
      // bottom: 4px;
      // top: 0;
      // content: ' ';
      // width: 2px;
      background-color: var(--brand-kyy-color-brand-hover, #707EFF);

    }
  }
}

.association_card2 {
  //  background-image: url('@renderer/assets/association/icon/card2.svg');
  border-radius: 16px;
  border: 1px solid #FFF;
  background: linear-gradient(180deg, #C8EFF8 0%, #FFF 100%), var(--bg-kyy_color_bg_default, #FFF);
   .card-desc {

    //   border-left: 2px solid var(--brand-kyy-color-brand-hover, #707EFF);
        &::before {

        background-color: #46C7FF;

        }
    }

}
.association_card3 {
  //  background-image: url('@renderer/assets/cbd/icon/card3.svg');
  border-radius: 16px;
  border: 1px solid #FFF;
  background: linear-gradient(180deg, #E5E8FF 0%, #FFF 100%), var(--bg-kyy_color_bg_default, #FFF);
   .card-desc {

   //   border-left: 2px solid var(--brand-kyy-color-brand-hover, #707EFF);
       &::before {

           background: #704DFF;

       }
   }
}
.association_card4 {
  //  background-image: url('@renderer/assets/cbd/icon/card4.svg');
  border-radius: 16px;
  border: 1px solid #FFF;
  background: linear-gradient(180deg, #FFFCE8 0%, #FFF 100%), var(--bg-kyy_color_bg_default, #FFF);
   .card-desc {

   //   border-left: 2px solid var(--brand-kyy-color-brand-hover, #707EFF);
       &::before {
         background: #F5D303;

       }
   }

}

.association_card5 {
  //  background-image: url('@renderer/assets/cbd/icon/card4.svg');
  border-radius: 16px;
  border: 1px solid #FFF;
  background: linear-gradient(180deg, #FFECDA 0%, #FFF 100%), var(--bg-kyy_color_bg_default, #FFF);
   .card-desc {

   //   border-left: 2px solid var(--brand-kyy-color-brand-hover, #707EFF);
       &::before {
         background: #FC7777;

       }
   }

}
.association_card6 {
  //  background-image: url('@renderer/assets/cbd/icon/card4.svg');
  border-radius: 16px;
  border: 1px solid #FFF;
  background: linear-gradient(180deg, #F0D6F9 0%, #FFF 100%), var(--bg-kyy_color_bg_default, #FFF);   .card-desc {

   //   border-left: 2px solid var(--brand-kyy-color-brand-hover, #707EFF);
       &::before {
         background: #D857DA;

       }
   }

}
</style>
