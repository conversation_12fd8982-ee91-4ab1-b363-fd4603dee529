<template>
  <div class="add-remind-dialog">
    <div class="drawerBox">
      <!-- 头部 -->
      <div class="drawerHeader">
        <div class="drawerHeader-left">
          {{
            props.isAdd
              ? t("zx.remind.addRemind")
              : formData.template
              ? t("zx.remind.editRemindTemplate")
              : t("zx.remind.editRemind")
          }}
        </div>
        <div @click="close" style="cursor: pointer;">
          <img style="height: 16px" src="@/assets/im/im_close.png" />
        </div>
      </div>
      <!-- 提醒来源 -->
      <div class="drawerFrom" v-if="chatName && sessionId">
        <img
          src="@renderer/assets/img/icon_info.svg"
          style="width: 20px; height: 20px; margin-right: 8px; vertical-align: middle"
        />
        <span>来自{{ conversation == 1 ? "单聊" : conversation == 3 ? "群聊" : "群聊" }}：{{ chatName }}</span>
      </div>
      <div class="drawerContent" @scroll="isShowRemind = false">
        <t-form
          ref="formRef"
          :rules="formRules"
          :data="formData"
          labelWidth="0"
          resetType="initial"
          scrollToFirstError="smooth"
        >
          <!-- 提醒标题 -->
          <t-form-item label="" name="title" requiredMark class="form-item">
            <div class="form-item-label-box">
              <div style="display: flex;gap: 4px;">
                <div style="color: red">*</div>
                <div class="form-item-label">{{ t("zx.remind.title") }}</div>
              </div>
              <div v-if="props.isAdd" class="form-item-label-tip" @click="isShowTemplate = true">使用模版</div>
            </div>
            <t-input v-model="formData.title" v-focus :placeholder="t('zx.remind.inputTip')" :maxlength="255" />
          </t-form-item>
          <!-- 提醒描述 -->
          <t-form-item :label="t('zx.remind.desc')" name="desc" class="form-item">
            <div class="form-item-label-box">
              <div class="form-item-label">{{ t("zx.remind.desc") }}</div>
            </div>
            <t-textarea
              v-model="formData.desc"
              :placeholder="t('zx.remind.inputTip')"
              :maxlength="255"
              :autosize="{ minRows: 3, maxRows: 5 }"
            />
          </t-form-item>
          <!-- 提醒事项 -->
          <t-form-item class="form-item">
            <div class="f-between form-item-between" style="width: 100%">
              <div class="form-item-label">提醒事项</div>
              <img
                class="add-addpend"
                src="@renderer/assets/zhixing/icon_addpend.svg"
                alt=""
                @click="addNoticeMatter"
              />
            </div>
            <draggable :list="formData.noticeMatters" animation="300" itemKey="index" @end="onEnd" style="width: 100%">
              <template #item="{ element, index }">
                <div class="f-between" style="margin-top: 12px">
                  <iconpark-icon name="icondrag"></iconpark-icon>
                  <t-input style="margin: 0 12px" v-model="element.name" :maxlength="100"></t-input>
                  <img
                    class="add-addpend"
                    src="@renderer/assets/zhixing/icon_delete.svg"
                    alt=""
                    @click="removeNoticeMatter(index)"
                  />
                </div>
              </template>
            </draggable>
          </t-form-item>
          <!-- 提醒到人 -->
          <t-form-item label="" name="mentionUsers" class="form-item" requiredMark>
            <div class="f-between" style="width: 100%">
              <div class="form-item-label"><span style="color: red">*</span>提醒到人</div>
            </div>
            <t-popup
              :on-visible-change="onPopupVisibleChange"
              :visible="isShowRemind"
              trigger="click"
              attach=".user-container"
              overlayClassName="fixed-popup"
            >
              <template #content>
                <div class="user-container-popup">
                  <div class="user-container-popup-input" v-if="conversation !== 1">
                    <t-input @change="handleSearch" :placeholder="t('account.search')">
                      <template #prefixIcon>
                        <iconpark-icon name="iconsearch" style="font-size: 20px; color: #828da5"></iconpark-icon>
                      </template>
                    </t-input>
                  </div>
                  <div class="user-container-popup-content">
                    <div class="user-container-popup-content-name" v-if="sessionId">
                      {{ conversation === 3 ? "群聊成员" : "单聊成员" }}
                    </div>
                    <div class="user-container-popup-content-name" v-else-if="!conversation">最近联系人</div>
                    <div
                      class="user-container-popup-content-box"
                      v-if="pickListData.length && (chatingSession?.inSession || !sessionId)"
                    >
                      <div class="user-container-popup-box-list" v-for="(item, index) in pickListData" :key="index">
                        <div class="user-container-popup-box-list-left">
                          <t-checkbox :checked="item.done" v-model="item.done" @change="onChangePick"></t-checkbox>
                        </div>
                        <div class="user-container-popup-box-list-right">
                          <avatar
                            class="avatar-icon"
                            :imageUrl="item.avatar"
                            :userName="item.name"
                            avatarSize="32px"
                          />
                          <div class="popup-box-list-right-title">
                            <div class="popup-box-list-right-title-name">
                              {{ item.name }}
                              <span v-if="!~item.cardId.indexOf('$') && !~item.cardId.indexOf('#')">好友</span>
                            </div>
                            <template
                              v-if="item.team_name && (~item.cardId.indexOf('$') || ~item.cardId.indexOf('#'))"
                            >
                              <i
                                :style="{
                                    color: ~item.cardId.indexOf('#') ? '#FC7C14' : '#49BBFB',
                                  }"
                              >{{ item.team_name }}</i
                              >
                            </template>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="popup-more" v-else>
                      <img src="@/assets/zhixing/no-more.png" style="width: 200px; height: 200px" alt="" />
                      <span>{{ chatingSession?.inSession ? "搜索无数据" : "暂无人员" }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </t-popup>
            <div class="user-container">
              <div class="user-container-left">
                <div class="container-left-list" v-for="item in formData.mentionUsers" :key="item.openid">
                  <avatar class="avatar-icon" :imageUrl="item.avatar" :userName="item.name" avatarSize="24px" />
                  <div class="container-left-list-name">{{ item.name }}</div>
                  <iconpark-icon
                    name="iconerror"
                    class="icon-delete"
                    @click="removeUser(item)"
                    v-if="item.cardId !== getOpenid()"
                  ></iconpark-icon>
                </div>

                <div class="container-left-add" @click="isShowRemind = !isShowRemind">
                  <span style="width: 100%">添加提醒到人</span>
                </div>
              </div>
              <div class="user-container-line"></div>
              <div class="user-container-right" style="padding-right: 12px">
                <img class="add-addpend" src="@renderer/assets/zhixing/icon_addpend.svg" alt="" @click="addRemind" />
              </div>
            </div>
          </t-form-item>
          <!-- 提醒频次 -->
          <t-form-item label="" name="noticeTyp" class="form-item">
            <div class="form-item-label-box">
              <div class="form-item-label">{{ t("zx.remind.frequency") }}</div>
            </div>
            <div class="form-item-time-content">
              <t-select
                v-model="formData.noticeTyp"
                :placeholder="t('zx.remind.frequencyTip')"
                @change="
                  () => {
                    formData.time = '';
                  }
                "
              >
                <t-option v-for="item in frequencyOptions" :key="item.value" :value="item.value" :label="item.label">
                  <template #content>
                    <t-radio :checked="formData.noticeTyp === item.value">{{ item.label }}</t-radio>
                  </template>
                </t-option>
              </t-select>
              <t-select
                v-if="showDatePicker('EVERY_MONTH')"
                v-model="formData.day"
                class="date-picker"
                :placeholder="t('zx.remind.frequencyTip')"
              >
                <template #suffixIcon>
                  <iconpark-icon name="icondate" style="font-size: 20px; color: #828da5"></iconpark-icon>
                </template>
                <t-option
                  v-for="item in moment().daysInMonth() ?? 31"
                  :key="item"
                  :value="item"
                  :label="item + t('zx.remind.day')"
                ></t-option>
              </t-select>
              <t-select
                v-else-if="showDatePicker('EVERY_WEEK')"
                :multiple="true"
                v-model="formData.week"
                class="date-picker"
                :placeholder="t('zx.remind.frequencyTip')"
              >
                <template #valueDisplay="{ value }">
                  <div
                    v-if="value"
                    style="
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      height: 24px;
                      max-width: 172px;
                      display: inline-block;
                      line-height: 24px;
                      vertical-align: -10px;
                    "
                  >
                    {{ value && Array.isArray(value) && value.map((v) => v.label).join("、") }}
                  </div>
                  <span v-else style="color: #b1b8c3">{{ t("zx.remind.frequencyTip") }}</span>
                </template>
                <!-- <template #suffixIcon>
                  <iconpark-icon name="icondate" style="font-size: 20px; color: #828da5"></iconpark-icon>
                </template> -->
                <t-option
                  class="week-box-list-option"
                  v-for="item in weekOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                ></t-option>
              </t-select>
              <t-cascader
                v-else-if="showDatePicker('EVERY_YEAR')"
                v-model="formData.year"
                :options="yearOptions"
                class="date-picker"
              >
                <template #valueDisplay="{ value, selectedOptions }">
                  <div v-if="value">
                    <span>{{ selectedOptions[0]?.displayValue }}</span>
                  </div>
                </template>
              </t-cascader>
            </div>
          </t-form-item>
          <!-- 提醒时间 -->
          <t-form-item label="" name="time" class="form-item">
            <div class="form-item-label-box">
              <div class="form-item-label"><span style="color: red">*</span>{{ t("zx.remind.timeDate") }}</div>
            </div>
            <div class="form-item-time-box">
              <t-date-picker
                v-if="showDatePicker('ONCE')"
                enable-time-picker
                :onFocus="onRemindFocus"
                format="YYYY-MM-DD HH:mm"
                style="width: 100%"
                :time-picker-props="{
                  steps: [1, 5, 1],
                  format: 'HH:mm',
                }"
                allow-input
                clearable
                class="date-picker"
                v-model="formData.date"
                :disableDate="{ before: moment().add(-1, 'day').format('YYYY-MM-DD') }"
              />
              <t-time-picker
                v-if="!showDatePicker('ONCE')"
                :onFocus="onRemindTimeFocus"
                :class="['time-picker', showTimePicker ? 'timePickerWidth' : '']"
                v-model="formData.time"
                format="HH:mm"
                :steps="[1, 5, 1]"
              />
            </div>
          </t-form-item>
          <!-- 发送到群 -->
          <t-form-item class="form-item" v-if="props.isAdd">
            <div class="f-between" style="width: 100%">
              <div class="form-item-label">发送到群</div>
              <img
                class="add-addpend"
                src="@renderer/assets/zhixing/icon_addpend.svg"
                alt=""
                @click="selectGroupVisible = true"
              />
            </div>
            <!-- <div> -->
            <div v-for="item in selectGroups" :key="item.targetId" class="group-info">
              <span>
                {{ item.name }}
              </span>
              <iconpark-icon class="icon" name="iconerror" @click="removeGroup(item)"></iconpark-icon>
            </div>
            <!-- </div> -->
          </t-form-item>
        </t-form>
        <div class="card-box" v-if="formData.content" @click="openCard">
          <img class="avatar" :src="formData.content?.icon" alt="" />
          <div class="text">{{ formData.content?.title }}</div>
        </div>
      </div>
      <!-- 添加提交 -->
      <div class="drawerFooter" v-if="props.isAdd">
        <t-button theme="default" variant="outline" @click="emits('saveTemplate',formData,infoRemindDetail)" style="margin-right: 8px"
          >保存为模板</t-button
        >
        <t-button theme="primary" variant="base" @click="toSubmit" :loading="isLoadingShow" style="width: 80px">提交</t-button>
      </div>
      <!-- 编辑提交 -->
      <div class="drawerFooter" v-else>
        <t-button theme="default" variant="outline" @click="close" style="margin-right: 8px; width: 80px"
          >取消</t-button
        >
        <t-button
          theme="primary"
          variant="base"
          @click="emits('saveTemplate',formData,infoRemindDetail)"
          v-if="formData.template"
          style="width: 80px"
          >保存</t-button
        >
        <t-button theme="primary" variant="base" @click="onSubmit" v-else style="width: 80px">保存</t-button>
      </div>
    </div>

    <selectMember
      v-model:visible="selectMemberVisible"
      :change-menus="true"
      :extend-from="['noPlatform']"
      v-model:select-list="selectListId"
      @confirm="noticeUserList"
    />
    <selectGroup v-model:visible="selectGroupVisible" :select-list="selectGroupIds" @confirm="groupList" />
    <pop-ups v-model:visible="isShowTemplate" attach=".add-remind-dialog" size="90%" :full-round="true">
      <template #box>
        <reminder-template @close="isShowTemplate = false"
          @go-click="selectGoClickTemplate"
          :options="{
            showTabClose: true,
            isIm: false,
            showEditSave:false,
            showText:true,
            selectTemplate: 1,
            showTitle: false,
          }" />
      </template>
    </pop-ups>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { sendApplicationMsg, MsgShareType } from "@renderer/utils/share";
// import saveTem from "../components/saveTem.vue";
import avatar from "@renderer/components/kyy-avatar/index.vue";
import selectMember from "@renderer/components/rk-business-component/select-member/common-add-members.vue";
import selectGroup from "../components/selectGroup.vue";
import draggable from "vuedraggable";
import { ref, watch, reactive, computed, onMounted } from "vue";
import { useZhixingStore } from "@/views/zhixing/store";
import moment from "moment";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { getOpenid, getProfilesInfo, getImCardIds } from "@renderer/utils/auth";
import to from "await-to-js";
import { addNotice, editNoticeAfter, getNotice, editNotice } from "@/api/zhixing/api/remind";
import { useI18n } from "vue-i18n";
import { useMessageStore } from "@renderer/views/message/service/store";
import selectData from "@/components/rk-common/rk-select-member/src/data";
import { useLaterEvents } from "@renderer/views/message/scene/components/later";
import { vFocus } from "@renderer/views/zhixing/directive";
import { format_knock_at, _transData_knock_at, setRemindParams } from "./utils/add-remind-dialog";
import PopUps from "@renderer/views/zhixing/common/pop-ups.vue";
import ReminderTemplate from "@renderer/views/zhixing/common/reminder-template.vue";
import { getRemindMinute, getRemindMinuteByTime } from "@/views/zhixing/newUtils";
import {uniqBy} from "lodash";

const { deleteLaterEvent } = useLaterEvents("pending");
const msgStore = useMessageStore();
const { chatingSession } = storeToRefs(msgStore);

const chatName = ref("");
const conversation = ref(1);
const { t } = useI18n();
const selfInfo = getProfilesInfo();
const zhixingStore = useZhixingStore();
// 获取提醒详情
const infoRemindDetail:any = ref({});
// 最近列表人列表
const recentList = ref<any[]>([]);
const pickList = ref<any[]>([]);
const pickListData = ref<any[]>([]);
// 保存稍后处理id
const laterId = ref("");
// 是否展示提醒人
const isShowRemind = ref(false);
// 是否展示模版弹窗
const isShowTemplate = ref(false);
// 避免多次点击
const isLoadingShow = ref(false);

const props = defineProps({
  // 是否为新增提醒
  isAdd: {
    type:Boolean,
    default: () => true
  },
  // 详情id
  openid: {
    type: String,
    default: () => ""
  },
  // 参数
  editInfo: {
    type: Object,
    default: () => {}
  }
})
// 参数表单
const formData = reactive({
  title: "",
  desc: "",
  noticeTyp: "ONCE",
  week: [],
  day: 1,
  year: "1-1",
  date: "",
  time: moment().add(5, "minutes").format("HH:mm"),
  knockAt: "" as string | number,
  openid: "",
  content: null,
  noticeMatters: [],
  mentionUsers: [
    {
      name: selfInfo?.title || "",
      avatar: selfInfo?.avatar || "",
      cardId: selfInfo.openid,
      openid: selfInfo.openid,
    },
  ],
  template: false,
  templateName: "",
});

// 表单验证规则
const formRules = ref({
  title: [{ required: true, message: t("zx.remind.required") }],
  noticeTyp: [{ required: true, message: t("zx.remind.required") }],
  date: [{ required: true, message: t("zx.remind.required") }],
  time: [
    { required: false, message: t("zx.remind.required") },
    {
      trigger: "blur",
      validator: () => {
        let label = frequencyOptions.value.find((item) => formData.noticeTyp === item.value)?.formDataLabel;
        return Boolean(formData[label] && (label === "date" || formData.time));
      },
      message: t("zx.remind.required"),
      type: "error",
    },
  ],
  mentionUsers: [{ required: true, message: "" }],
});
const formRef = ref(null);
const frequencyOptions = ref([
  { label: t("zx.remind.ONCE"), value: "ONCE", formDataLabel: "date" },
  { label: t("zx.remind.EVERY_DAY"), value: "EVERY_DAY", formDataLabel: "time" },
  { label: t("zx.remind.EVERY_WEEK"), value: "EVERY_WEEK", formDataLabel: "week" },
  { label: t("zx.remind.EVERY_MONTH"), value: "EVERY_MONTH", formDataLabel: "day" },
  { label: t("zx.remind.EVERY_YEAR"), value: "EVERY_YEAR", formDataLabel: "year" },
  { label: t("zx.remind.EVERY_WORKDAY"), value: "EVERY_WORKDAY", formDataLabel: "time" },
  { label: t("zx.remind.EVERY_MON_SAT"), value: "EVERY_MON_SAT", formDataLabel: "time" },
  { label: t("zx.remind.EVERY_SUN_FRI"), value: "EVERY_SUN_FRI", formDataLabel: "time" },
]);

const weekOptions = ref([
  { label: t("zx.remind.mon"), value: 1 },
  { label: t("zx.remind.tue"), value: 2 },
  { label: t("zx.remind.wed"), value: 3 },
  { label: t("zx.remind.thu"), value: 4 },
  { label: t("zx.remind.fri"), value: 5 },
  { label: t("zx.remind.sat"), value: 6 },
  { label: t("zx.remind.sun"), value: 7 },
]);
const selectMemberVisible = ref(false);
const selectListId = ref([]);
const selectGroupVisible = ref(false);
const selectGroupIds = ref([]);
const selectGroups = ref([]);
const sessionId = ref("");

const yearOptions = computed(() => {
  let m = Array.from(Array(12).keys(), (n) => n + 1);
  return m.map((items) => {
    // 设置每月天数
    let day = [2].includes(items) ? 28 : [1, 3, 5, 7, 8, 10, 12].includes(items) ? 31 : 30;
    let d = Array.from(Array(day).keys(), (n) => n + 1);
    return {
      label: items + t("zx.remind.month"),
      value: items,
      children: d?.map((item) => {
        return {
          label: item + t("zx.remind.day"),
          value: items + "-" + item,
          displayValue: `${items < 10 ? ('0' + items) : items}${t("zx.remind.month")}${item < 10 ? ('0' + item) : item}${t("zx.remind.day")}`
        };
      }),
    };
  });
});
const emits = defineEmits(["addSuccess","close",'saveTemplate']);

const showDatePicker = (type) => {
  return formData.noticeTyp == type;
};
const showTimePicker = computed(
  () => !["ONCE", "EVERY_WEEK", "EVERY_MONTH", "EVERY_YEAR"].includes(formData.noticeTyp),
);


// 提交
const onSubmit = async () => {
  const valid = await formRef.value.validate();
  if (!formData.template && typeof valid !== "boolean") return isLoadingShow.value = false;
  let { title } = formData;
  if (!formData.template && !title){
    isLoadingShow.value = false;
    return MessagePlugin.warning("标题不能为空");
  }
  if(showDatePicker('EVERY_WEEK') && formData.week.length < 1){
    isLoadingShow.value = false;
    return MessagePlugin.warning('每周提醒频次不能为空');
  }
  if (formData.template) {
    formRef.value.clearValidate();
  }
  // const msgInfo = zhixingStore.addRemindDialogData.props?.fromMsgInfo;
  // 拼装参数
  let data = setRemindParams(formData,infoRemindDetail.value,props.isAdd);
  //保存为模板不做过期提示 并且只有单次提醒才会提示
  if (
    moment(moment(formData.date).format("YYYY-MM-DD HH:mm")).valueOf() <= moment().valueOf() &&
    !formData.template &&
    formData.noticeTyp == "ONCE"
  ) {
    const confirmDia = DialogPlugin.confirm({
      header: t("account.tip"),
      body: props.isAdd ? t("zx.remind.expiredTimeTip") : t("zx.remind.pastTimeTip"),
      theme: "warning",
      confirmBtn: {
        content: t("identity.confirm"),
      },
      onConfirm: () => {
        confirmDia.destroy();
        saveRemind(data);
      },
      onClose: () => {
        confirmDia.hide();
        isLoadingShow.value = false;
      },
    });
  } else {
    saveRemind(data);
  }
};

// 保存提醒
const saveRemind = async (data:any) => {
  const [err, res] = props.isAdd ? await to(addNotice(data)) : await to(editNoticeAfter(data));
  isLoadingShow.value = false;
  if (err) return;
  MessagePlugin.success(props.isAdd ? t("zx.remind.addSuccess") : t("zx.remind.editSuccess"));
  if (props.isAdd) emits("addSuccess");
  // 如果设置的有提醒到群，则通知IM
  if (selectGroups.value.length && !formData.template) {
    sendApplicationMsg(MsgShareType.zx_remind_detail, res.data.data, selectGroups.value);
  }
  if (laterId.value) {
    deleteLaterEvent(laterId.value);
  }
}

//提醒到人
const addRemind = () => {
  selectMemberVisible.value = true;

  isShowRemind.value = false;
};
// 关闭弹窗
const close = () => {
  formRef.value.reset();
  emits("close");
  // resetForm();
};

const openCard = () => {
  // console.log("openCard", zhixingStore.addRemindDialogData);
};
// 数据初始化
const _transData = (data:any) => {
  let dataTimeObj = _transData_knock_at(data); //提醒频次
  formData.title = data?.title || "";
  formData.desc = data?.desc || "";
  formData.noticeTyp = data?.noticeTyp || "ONCE";
  formData[dataTimeObj.label] = dataTimeObj.value;
  formData.time = moment(Number(data.knockAt)).format("HH:mm");
  formData.openid = data?.openid || "";
  formData.content = data?.content || "";
  formData.mentionUsers = data?.mentionUsers || [];
  formData.week = data?.chooseWeekdays || [];
  formData.template = data?.template || false;
  formData.templateName = data?.templateName || "";
};
const addNoticeMatter = () => {
  formData.noticeMatters.push({ name: "" });
};
const removeNoticeMatter = (index) => {
  formData.noticeMatters.splice(index, 1);
};
// 选择到人
const noticeUserList = (list) => {
  const selectedList = list.reduce(
    (acc, cur) => {
      const user = {
        name: cur.name,
        avatar: cur.avatar,
        cardId: cur.cardId,
        openid: cur.openId,
      };
      acc.push(user);
      if(!selectListId.value.includes(cur.cardId)){
        selectListId.value.push(cur.cardId);
      }
      const isDone = pickListData.value.findIndex((v) => v.cardId === cur.cardId);
      if (isDone !== -1) {
        pickListData.value[isDone]["done"] = true;
      }
      return acc;
    },
    [
      {
        name: selfInfo?.title || "",
        avatar: selfInfo?.avatar || "",
        cardId: selfInfo.openid,
        openid: selfInfo.openid,
      },
    ],
  )
  formData.mentionUsers = uniqBy([...formData.mentionUsers, ...selectedList], 'cardId');
};

const groupList = (list) => {
  console.log(list);
  selectGroups.value = list;
  selectGroupIds.value = list.map((v) => v.targetId);
};

// 删除选中的用户
const removeUser = (user) => {
  formData.mentionUsers = formData.mentionUsers.filter((v) => v.cardId !== user.cardId);
  selectListId.value = selectListId.value.filter((v) => v !== user.cardId);
  console.log("user**", user);
  const isDone = pickListData.value.findIndex((v) => v.openid === user.openid);
  if (isDone !== -1) {
    pickListData.value[isDone]["done"] = false;
  }
  // selectGroupId.value = selectGroupId.value.filter(v => v !== user.cardId);
};

const removeGroup = (group) => {
  selectGroups.value = selectGroups.value.filter((v) => v.targetId !== group.targetId);
  selectGroupIds.value = selectGroupIds.value.filter((v) => v !== group.targetId);
};

//页面点击提交
const toSubmit = async () => {
  if (isLoadingShow.value) return;
  const valid = await formRef.value.validate();
  if (typeof valid !== "boolean") return MessagePlugin.warning(t("zx.remind.pleaseRequiredFields"));
  // 取消模版
  formData.template = false;
  // 取消模版名称参数
  formData.templateName = "";
  // 触发点击loading事件
  isLoadingShow.value = true;
  onSubmit();
};

const onEnd = () => {};

const getTemData = (data) => {
  formData.title = data.title;
  formData.desc = data.desc;
  formData.noticeMatters = data.noticeMatters || [];
  formData.noticeTyp = data.noticeTyp;
  laterId.value = data.later_id;
  let dataTimeObj = _transData_knock_at(data);
  formData[dataTimeObj.label] = moment().format("YYYY-MM-DD");
  formData.date = "";
  formData.knockAt = format_knock_at(formData);
};

onMounted(() => {
  // props.editInfo && Object.keys(props.editInfo).length && _transData(props.editInfo);
  props.editInfo && Object.keys(props.editInfo).length && getTemData(props.editInfo);
  // 判断是否是im这边有群聊editParams
  const msgInfo = zhixingStore.addRemindDialogData.props?.fromMsgInfo;
  conversation.value = msgInfo?.conversationType;
  sessionId.value = msgInfo?.fromId ?? "";
  chatName.value = msgInfo?.fromName ?? "";
  selectListId.value = [];
  getRecentList(msgInfo?.fromId);
  // 获取详情
  props.openid && getNoticeDetail(props.openid);
});

// 获取最近联系人
const getRecentList = async (fromId?) => {
  try {
    const list = await selectData.recentList(getImCardIds());
    recentList.value = list.map((v: any) => {
      const vFind = v.attachment?.member.find((item: any) => v.openId === item.openId);
      return {
        name: v.name,
        avatar: v.avatar,
        cardId: v.cardId,
        openid: v.openId,
        team_name: vFind?.teamName || "",
        done: false,
      };
    });
    // 判断组件是否在消息(im)展示-->如果在im的话就显示群成员,反之最近联系人
    if (fromId) {
      pickList.value = getRecentListParams();
    } else {
      pickList.value = recentList.value;
    }
    pickListData.value = uniqBy(pickList.value, 'cardId').filter(item => item.openid !== getOpenid());
  } catch (error) {
    console.log(error);
  }
};

// 返回参数列表
const getRecentListParams = () => {
  const chatingSessionMembers = msgStore.chatingSessionMembers;
  const arr = Array.from(chatingSessionMembers, ([key, value]) => value).map((v: any) => {
    return {
      name: v.staffName,
      avatar: v.avatar,
      cardId: v.cardId,
      openid: v.openId,
      team_name: v.teamName || "",
      done: false,
    };
  });
  return arr.filter((v: any) => v.openid !== getOpenid()) || [];
};

// 选择人
const onChangePick = (e, context) => {
  if (pickListData.value.length === 0) return;
  for (const key in pickListData.value) {
    const data = pickListData.value[key];
    const index = formData.mentionUsers.findIndex((v) => v.openid === data.openid && v.cardId === data.cardId);
    if (index !== -1 && !data["done"]) {
      formData.mentionUsers.splice(index, 1);
      const selectIndex = selectListId.value.findIndex((v) => v === data.openid);
      if (selectIndex !== -1) selectListId.value.splice(selectIndex, 1);
    } else if (index === -1 && data["done"]) {
      formData.mentionUsers.push(data);
      selectListId.value.push(data.openid);
    }
  }
};

// 搜索
const handleSearch = (value) => {
  pickListData.value = pickList.value?.filter((v) => v.name.indexOf(value) !== -1) || [];
};

// 获取提醒详情
const getNoticeDetail = async (id:string) => {
  try {
    let res = await getNotice(id);
    const resData = res.data?.data;
    resData.knockAt = Number(resData.knockAt);
    infoRemindDetail.value = resData;
    _transData(resData);
  } catch (error) {
    console.log('error',error);
  }
}

const onPopupVisibleChange = (e, trigger) => {
  if (trigger) {
    const tri = trigger.trigger;
    if (tri === "document") return (isShowRemind.value = false);
  }
};

// 选择模版
const selectGoClickTemplate = (item) => {
  // _transData(item);
  getTemData(item);
  isShowTemplate.value = false;
}

const onRemindFocus = () => {
  if (formData.date) return;
  formData.date = getRemindMinute();
};

const onRemindTimeFocus = () => {
  if (formData.time) return;
  formData.time = getRemindMinuteByTime();
};
</script>

<style lang="less" scoped>
@import "./style/add-remind-dialog.less";
</style>
