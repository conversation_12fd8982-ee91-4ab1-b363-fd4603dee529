<template>
  <t-loading
    size="medium"
    class="memberLoading"
    :loading="isLoading"
    show-overlay
    text="加载中..."
  >
    <div class="container">
      <div class="header-box">
        <div class="tip">{{$t('member.bolit.e')}}</div>
        <div v-show="settingInfo && settingInfo.contact_add && (props.currentRow?.is_contact === 0)">
          <!--        <t-button theme="primary" variant="base" @click="onAddMember">-->
          <!--          {{$t('member.sv17.s_8')}}-->
          <!--          <template #icon>-->
          <!--            <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>-->
          <!--          </template>-->
          <!--        </t-button>-->
          <t-button
            theme="primary"
            style="margin-left: 8px"
            variant="base"
            @click="onAddContact"
          >
            <template #icon>
              <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
            </template>
            {{ $t('member.sv17.s_9') }}</t-button>
        </div>
      </div>
      <div class="body">
        <div class="body-content">
          <div class="table" v-if="memberData.length > 0">
            <t-table
              cell-empty-content="--"
              row-key="id"
              :columns="memberColumns"
              :data="memberData"
              class="setTable"
            >
              <template #empty>
                <div class="empty">
                  <!-- <noData :text="$t('engineer.no_data')" /> -->
                  <Empty v-show="!isNetworkError && !isLoading" :tip="$t('engineer.no_data')" />
                  <Empty v-show="isNetworkError && !isLoading" name="offline">
                    <template #tip>
                      <div class="tipEmpty">
                        <span class="text">网络链接失败，请检查网络后重试</span>
                        <t-button theme="primary" class="btn" @click="onSearch">点击重试</t-button>
                      </div>
                    </template>
                  </Empty>

                </div>
              </template>
              <template #photo="{row}">
                <template v-if="!row?.is_representative">
                  <t-image class="logo cursor" :src="row?.photo" @click="preview(row)">
                    <template #loading>
                      <img class="logo" src="@renderer/assets/member/svg/avatar_default.svg"/>
                    </template>
                    <template #error>
                      <img class="logo" src="@renderer/assets/member/svg/avatar_default.svg"/>
                    </template>
                  </t-image>
                </template>
              </template>
              <template #email="{ row }">
                <template v-if="!row?.is_representative">
                  <div class="main_body">
                     {{ $filters.isPeriodEmpty(row?.email)  }}
                  </div>
                </template>
              </template>
              <template #job="{ row }">
                <template v-if="!row?.is_representative">
                  <div class="main_body">
                     {{ $filters.isPeriodEmpty(row?.job)  }}
                  </div>
                </template>
              </template>
              <template #telephone="{ row }">
                <template v-if="!row?.is_representative">
                  <div class="main_body">
                    +{{ $filters.isPeriodEmpty(row.telcode) }}&nbsp;{{
                      $filters.isPeriodEmpty(row.telephone)
                    }}
                  </div>
                </template>
              </template>

              <template #name="{ row }">
                <div style="display: flex; align-items: center">
                  <kyyAvatar
                    data-id="isclick"
                    style="margin-right: 12px"
                    :image-url="row.avatar"
                    avatar-size="44px"
                    :user-name="row.name"
                    :shape="'circle'"
                  />
                  <span class="ban">
                    <span class="line-1 max-w-98">{{ row.name || "--" }} </span>
                    <span class="green" v-if="row?.is_representative">代表人</span>
                    <span class="blue" v-else>联系人</span>
                  </span>
                </div>
              </template>

              <!-- <template #telephone="{ row }">
                <div class="main_body">
                  +{{ $filters.isPeriodEmpty(row.telcode) }}&nbsp;{{
                    $filters.isPeriodEmpty(row.telephone)
                  }}
                </div>
              </template> -->
              <!-- <template #status="{ row }">
                  {{ filterStatusText(row.status) }}
                </template> -->

              <!-- <template #status="{ row }">
                <div :class="showClassStatus(row.status)">
                  {{ filterStatusText(row.status) }}
                </div>
              </template> -->

              <template #operate="{ row }">
                <template v-if="!row?.is_representative">
                  <span v-if="row.status === 1"  class="operates">
                    <template v-if="row.is_it_me  || !props.currentRow?.is_contact">
                      <span  class="operates-item cursor"    @click="onEditContact(row)">
                        <t-link
                          theme="primary"
                          hover="color"
                          class="edit"
                        >
                          {{ $t("member.edit") }}
                        </t-link>
                      </span>
                    </template>
                    <template v-if="(!row.is_it_me  && !props.currentRow?.is_contact) || (row.is_it_me && !props.currentRow?.is_contact)">
                      <span  class="operates-item cursor"  @click="onDeleteContact(row)">
                        <t-link
                          style="color: #d54941;"
                          hover="color"
                          class="del"
                        >
                          {{ $t("member.delete") }}
                        </t-link>
                      </span>
                    </template>
                  </span>
                  <div v-else-if="row.status === 5" class="refuse">
                    <div class="org-tag" >待审核</div>
                    <template v-if="!props.currentRow?.is_contact">
                      <!-- 通过拒绝 -->
                      <span class="primaryBtn cursor" @click="onPass(row)">通过</span>
                      <span class="primaryBtn cursor" @click="onReject(row)">拒绝</span>
                    </template>
                  </div>
                  <div v-else-if="row.status === 2" class="refuse">
                    <div class="org-tag">待审核</div>
                  </div>
                  <div v-else class="refuse">
                    <div v-if="row.status === 0 "  class="org-tag">待成员处理</div>
                    <div v-else-if="row.status === 6 "  class="org-tag">已拒绝</div>
                    <div v-else class="org-tag"> 成员已拒绝</div>
                    <t-link
                      style="color: #d54941;"
                      hover="color"
                      v-show="(!row.is_it_me  && !props.currentRow?.is_contact) || (row.is_it_me && !props.currentRow?.is_contact)"
                      class="operates-item"
                      @click="onDeleteContactRefuse(row)"
                    >
                      {{ $t("member.delete") }}
                    </t-link>
                  </div>
                </template>
                <template v-else>
                  <!-- <t-link
                    style="color: #4D5EFF;"
                    hover="color"
                    v-show="row.status === 1 && row.is_it_me === 1"
                    class="operates-item"
                    @click="onChangeMoveRespector(row)"
                  >
                    转移代表人
                  </t-link> -->
                  <span
                    v-show="row.status === 1 && row.is_it_me === 1"
                    @click="onChangeMoveRespector(row)"
                    class="primaryBtn cursor">
                    转移代表人
                  </span>
                </template>
              </template>

              <!-- <template #operate="{ row }">

                <span class="operates">
                  <a @click="onEditContact(row)">
                    {{ $t("member.edit") }}
                  </a>
                  <a
                    style="margin-left: 16px; color: #d54941"
                    @click="onDeleteContact(row)"
                  >
                    {{ $t("member.delete") }}
                  </a>
                </span>
                <div class="org-tag">{{ row.status === 0 ?'待成员处理' : '成员已拒绝' }}</div>
              </template> -->
            </t-table>
          </div>
          <div v-else class="empty">
            <Empty v-show="!isNetworkError && !isLoading" :tip="$t('engineer.no_data')" name="no-friend-list"/>
            <Empty v-show="isNetworkError && !isLoading" name="offline">
              <template #tip>
                <div class="tipEmpty">
                  <span class="text">网络链接失败，请检查网络后重试</span>
                  <t-button theme="primary" class="btn" @click="onSearch">点击重试</t-button>
                </div>
              </template>
            </Empty>
          </div>
        </div>
      </div>
    </div>
  </t-loading>
  <AddContactModal
    ref="addContactModalRef"
    :member-id="props.currentRow ? props.currentRow.id : 0"
    :platform="platformCpt"
    :is-member="1"
    @reload="onSearch"
  />
  <AddContactOrganizeMemberModal
    ref="addContactOrganizeModalRef"
    :member-id="props.currentRow ? props.currentRow.id : 0"
    :relate-team-id="props.currentRow ? props.currentRow?.relation_team_id : 0"
    :is-member="1"
    :platform="platformCpt"
    :currentRow="props.currentRow"
    @reload="onSearch"
  />
  <AddOrganizeMemberModal
    ref="addOrganizeMemberModalRef"
    :member-id="props.currentRow ? props.currentRow.id : 0"
    :platform="platformCpt"
    @reload="onSearch"
  />
  <SelectContactorModal
    ref="selectContactorModalRef"
    :originType="originType.Uni"
    :options="optionsMembers"
    :isOnly="true"
    @sub-form="onListenMembers"/>
</template>

<script setup lang="ts">
import { computed, reactive, ref, toRaw, watch } from "vue";
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { MessagePlugin, DialogPlugin } from "tdesign-vue-next";
import AddContactModal from "@renderer/views/uni/member_home/panel/regular-member-panel/modal/add-contact-modal.vue";
import AddContactOrganizeMemberModal from "@renderer/views/uni/member_home/panel/regular-member-panel/modal/add-contact-organize-modal.vue";
import AddOrganizeMemberModal from "@renderer/views/uni/member_home/panel/regular-member-panel/modal/add-organize-member-modal.vue";
import { getResponseResult } from "@renderer/utils/myUtils";
import { AddIcon } from "tdesign-icons-vue-next";
import Empty from "@renderer/components/common/Empty.vue";

import {
  delContactAxios,
  getMemberContactListAxios,
  onGetContactListSelectOneAxios,  // 新增API
  onTransferBySelfAxios,            // 新增API
  onApplyReviewAxios,
} from "@renderer/api/uni/api/businessApi";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import SelectContactorModal from '@renderer/views/digital-platform/modal/select-contactor-modal.vue';
import { originType }  from "@renderer/views/digital-platform/utils/constant"
import { to } from "await-to-js";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const store = useUniStore();
const emits = defineEmits(["onSetCurrentPanel", "onSetCurrentRow", 'reload']);
const isNetworkError = ref(false);
const isLoading = ref(false);

const props = defineProps({
  currentRow: {
    type: Object,
    default: () => null
  },
  settingInfo: {
    type: Object,
    default: () => null
  },
  platform: {
    type: String,
    default: '',
  },
});

watch(
  () => props.currentRow,
  (val) => {
    if (val) {
      // console.log(val)
      // updateAllCount();
      onSearch();
    }
  },
  {
    deep: true
    // immediate: true
  }
);
// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       console.log(val)
//       // updateAllCount();
//       onSearch();
//     }
//   },
//   {
//     deep: true
//     // immediate: true
//   }
// );

const memberColumns = ref([]);

const initColumns = () => {
  memberColumns.value = [
    { colKey: "name", title: "姓名", width: "136px" },
    { colKey: "photo", title: "照片", width: "120px", ellipsis: false },
    {
      colKey: "job",
      title: "所在单位岗位",
      width: "120px",
      ellipsis: true,
      align: "left"
    },

    { colKey: "telephone", title: "手机号码", width: "160px", ellipsis: false },
    {
      colKey: "email",
      title: "邮箱",
      width: "176px",
      ellipsis: true
    },
    { colKey: "operate", title: "操作", width: "130px" }
  ];
};
initColumns();

const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();
// 平台类型 目前只有digital-platform
const platformCpt: any = computed(() => props.platform || route.query?.platform);


const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  }
    return getUniTeamID();

});


const addContactModalRef = ref(null);
const addContactOrganizeModalRef = ref(null);
const memberData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;

    // getMemberList({});
    onSearch();
  }
});
const onSearch = () => {
  const params = {};
  getMemberList(params);
};

// 获取列表
const getMemberList = async (params) => {
  console.log(props.currentRow);
  // teamId
  // params.page = pagination.current;
  // params.pageSize = pagination.pageSize;
  // params.member_id = props.currentRow ? props.currentRow.id : 0;
  params.uni_id = props.currentRow ? props.currentRow.id : 0;
  params.relate_teamId = props.currentRow?.relation_team_id


  // 缓存信息存储
  const caches = store.getStorageDatas;
  const cache = caches.find((v) => v.teamId === currentTeamId.value);
  if (!cache) {
    isLoading.value = true;
  }

  try {
    let result = await getMemberContactListAxios(params, currentTeamId.value);
    console.log(result);
    isLoading.value = false;
    result = getResponseResult(result);
    isNetworkError.value = false;
    if (!result) {
      return;
    }
    memberData.value = result.data;

    // 缓存处理 start
    const memberContacts = {
      items: toRaw(memberData.value),
      // page: result.page
    };
    if (cache) {
      cache.memberContacts = memberContacts;
    } else {
      caches.push({ teamId: currentTeamId.value, memberContacts });
    }
    console.log('caches: ', caches);
    store.setStorageDatas(caches);

    // pagination.total = result.data.total;
    console.log(memberData.value);
  } catch (error) {
    isLoading.value = false;

    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === 'Network Error') {
      isNetworkError.value = true;
      if (cache) {
        memberData.value = cache.memberContacts?.items || [];
      }
    } else {
      MessagePlugin.error(errMsg);
    }
  }
};

onSearch();

// 添加以下新方法
const selectContactorModalRef = ref(null);
const optionsMembers = ref([]);

const getAppMemberList = () => {
  return new Promise(async (resolve, reject) => {
    try {
      const result = await onGetContactListSelectOneAxios({
        main_body_id: props.currentRow?.id,
        status: 1
      }, currentTeamId.value);
      optionsMembers.value = getResponseResult(result)?.data || [];
      resolve("success");
    } catch (error) {
      MessagePlugin.error(error.message);
      reject();
    }
  });
};

const onChangeMoveRespector = (row) => {
  getAppMemberList().then(() => {
    selectContactorModalRef.value.onOpen();
  });
};

const onPass = async (row) => {
  const [err, res] = await to(onApplyReviewAxios({
        contact_id: row?.id,
        is_agree: 1
      }));
  if (err) {
    MessagePlugin.error(err.message);
    return;
  }
  onSearch();
  MessagePlugin.success('操作成功');
}
const onReject =async (row) => {
  const [err, res] = await to(onApplyReviewAxios({
        contact_id: row?.id,
        is_agree: 0
      }));
  if (err) {
    MessagePlugin.error(err.message);
    return;
  }
  onSearch();
  MessagePlugin.success('操作成功');
}

const onListenMembers = async (arr) => {
  if (!arr?.length) return;
  try {
    await onTransferBySelfAxios({
      main_body_id: props.currentRow?.id,
      contact_id: arr[0]
    }, currentTeamId.value);
    MessagePlugin.success('操作成功');
    // onSearch();
    emits('reload');
  } catch (error) {
    MessagePlugin.error(error.message);
  }
};


const onAddContact = () => {
  console.log("添加联系人");
  if(memberData.value?.filter(v=>!v.is_representative)?.length > 4) {
    return MessagePlugin.error('最多添加5位联系人')
  }
  addContactOrganizeModalRef.value.onOpen();
};

const addOrganizeMemberModalRef = ref(null);
const onAddMember = () => {
  addOrganizeMemberModalRef.value.onOpen();
};

const onEditContact = (row) => {
  addContactModalRef.value.onOpen(row);
};
const onDeleteContact = (row) => {
  const confirmDia = DialogPlugin({
    header: "确定删除联系人",
    theme: "info",
    body: "删除后该联系人无法管理该组织信息，并不再接收组织相关通知",
    closeBtn: null,
    confirmBtn: "确定删除",
    className: "delmode",
    onConfirm: async () => {
      // 删除字段操作
      onDelContactorAxios(row).then(() => {
        confirmDia.hide();
        onSearch();
      });
    },
    onClose: () => {
      confirmDia.hide();
    }
  });
};
const onDelContactorAxios = (val) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await delContactAxios(val.id, { is_member: 1 }, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) return;
      MessagePlugin.success("删除成功");
      resolve(result);
      // rejectModalRef.value.onClose();
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const onGoBack = () => {
  emits("onSetCurrentPanel", "PRegular");
};


const preview = (row) => {
  // const imgs = attrs.value.value.map((item) => item.file_name);
  // imageFiles.value = imgs;
  // viewer.value = true;
  if(!row.photo) return;

  const temp =  [
    {
      url: row.photo,
      // imgIndex: i,
    }
  ]
  ipcRenderer.invoke("view-img", JSON.stringify(temp));

};

const onDeleteContactRefuse = (row) => {
  const confirmDia = DialogPlugin({
    header: "提示",
    theme: "info",
    body: "成员还未加入，确定删除该条记录吗？删除后，之前发送地短信链接会失效，需重新添加该成员。",
    closeBtn: null,
    confirmBtn: "确定删除",
    className: "delmode",
    onConfirm: async () => {
      // 删除字段操作
      onDelContactorAxios(row).then(() => {
        confirmDia.hide();
        onSearch();
      });
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};


</script>

<style lang="less" scoped>
@import "@renderer/views/member/member_number/components/contact-comp.less";
</style>
