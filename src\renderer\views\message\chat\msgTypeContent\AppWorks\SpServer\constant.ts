/**
 * 场景类型枚举
 */
export enum SceneType {
  /**
   * 申请
   */
  INFORMATION_REVIEW_INITIATE = 9001,
  /**
   * 通过
   */
  INFORMATION_REVIEW_APPROVED = 9002,
  /**
   * 拒绝
   */
  INFORMATION_REVIEW_REJECTED = 9003,
  // 广场号推广到文旅发起申请时
  RECOMMEND_APPLICATION = 9004,
  // 广场号下架时
  RECOMMEND_DELISTED = 9005,

}

export const getTitle = (scene, title) => {
  switch (scene) {
    case SceneType.INFORMATION_REVIEW_REJECTED:
      return '特色服务内容已被拒绝';
    case SceneType.INFORMATION_REVIEW_APPROVED:
      return '特色服务内容已通过';
    case SceneType.INFORMATION_REVIEW_INITIATE:
      return '特色服务内容发布申请';
    case SceneType.RECOMMEND_APPLICATION:
      return '特色服务申请';
    case SceneType.RECOMMEND_DELISTED:
      return '广场号下架通知';
    default:
      return title;
  }
};

export const headerColorMap = {
  9001: 'secondary',
  9002: 'success',
  9003: 'danger',
  9004: 'secondary',
  9005: 'secondary',
};
export const SpServerSceneTypeArr = Object.values(SceneType).filter((value) => typeof value === 'number');
