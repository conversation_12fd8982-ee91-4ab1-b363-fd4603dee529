<template>
  <div class="rk-search-wrap">
    <Input v-model="search" :placeholder="t('account.search')" @change="searchPerson">
      <template #prefix-icon>
        <!-- <SvgIcon name="im-history" class="svg-size20" /> -->
        <i class="i-svg:im-history text-20 color-text-3" />
      </template>
    </Input>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { Input } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import SvgIcon from "@/components/SvgIcon.vue";
import { storeToRefs } from "pinia";
import { useRkSmStore } from "../store";
import debounce from "lodash/debounce";
import { match } from 'pinyin-pro';
import { dedupByKey, getCurItemAllPerson } from "../utils";

const store = useRkSmStore();
const { breadOptions, currentSelect, options } = storeToRefs(store);

const { t } = useI18n();

const search = ref('');
const originalSelect = ref(null);

const searchPerson = debounce((val) => {
  if (!val) {
    clearSearch();
    return;
  }

  // 保存原始状态
  if (!originalSelect.value) {
    originalSelect.value = JSON.parse(JSON.stringify(currentSelect.value));
  }

  // 如果有面包屑导航，说明已进入具体部门，只搜索当前部门下的人员
  if (breadOptions.value.length > 0) {
    // 搜索当前部门下的人员
    const currentDepartment = JSON.parse(JSON.stringify(currentSelect.value));
    const allStaffs = getCurItemAllPerson(currentDepartment);

    // 过滤符合搜索条件的人员
    const filteredStaffs = allStaffs.filter(staff => {
      const normal = staff.name.toLowerCase().includes(val.toLowerCase());
      const pinyin = match(staff.name, val.toLowerCase())?.length > 0;
      return normal || pinyin;
    });

    currentDepartment.staffs = dedupByKey(filteredStaffs, 'targetId');
    currentDepartment.children = [];
    // 更新搜索结果
    store.setCurrentOption(currentDepartment);
  } else {
    // 未进入具体部门，从 personList 中根据当前 options 的 type 搜索人员
    let allStaffs = [];

    // 遍历当前 options，从 personList 中获取对应 type 的人员数据
    options.value.forEach(option => {
      const type = option.type === 'organize' ? option?.id: option.type;
      const typeData = store.personList[type];
      if (typeData) {
        allStaffs = [...allStaffs, ...getCurItemAllPerson(typeData)];
      }
    });

    const filteredStaffs = allStaffs.filter(staff => {
      const normal = staff.name.toLowerCase().includes(val.toLowerCase());
      const pinyin = match(staff.name, val.toLowerCase())?.length > 0;
      return normal || pinyin;
    });

    // 创建新的搜索结果对象
    const searchResult = {
      type: 'search',
      name: t('account.search'),
      staffs: dedupByKey(filteredStaffs, 'targetId'),
      children: []
    };

    // 更新搜索结果
    store.setCurrentOtherData(searchResult);
  }
}, 200);

const clearSearch = () => {
  search.value = '';
  if (breadOptions.value.length > 0 && originalSelect.value) {
    store.setCurrentOtherData(originalSelect.value);
    originalSelect.value = null;
    return;
  }
  store.setBreadOptions([]);
  store.setCurrentOption(null);
};

defineExpose({
  clearSearch,
});
</script>
<style lang="less" scoped>
.rk-search-wrap {
  padding: 0 8px 12px;
}
</style>
