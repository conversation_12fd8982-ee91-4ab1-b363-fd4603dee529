<template>
  <div
    v-infinite-scroll="onLoading"
    class="page-content"
    :infinite-scroll-immediate-check="false"
    :infinite-scroll-distance="1800"
    :infinite-scroll-disabled="scrollDisabled"
    infinite-scroll-watch-disabled="scrollDisabled"
  >
    <div class="header">
      <span class="back" @click="backIndex(route)">
        <iconpark-icon name="iconarrowlift" class="icon" /> {{ $t('square.return') }}
      </span>
      <div class="friend" @click="() => goHomePage({ squareId: route.query.squareId, name: square.name })">
        <SquareAvatar
          :square="square"
          size="24px"
          class="avatar"
          :symbol="false"
        />
        <span class="name">{{ square.remark || square.name }}</span>
      </div>
    </div>

    <div v-if="isSelf" ref="dateWrap" class="date-wrap">
      <DatePicker ref="datePickerRef" timeline="true" @select="dateSelect" />
    </div>

    <div class="content-wrap">
      <div v-if="loadingUp || wheelLoading" class="loading-up">
        <t-loading size="small" text="加载中..." />
      </div>
      <PostList
        ref="postListRef"
        :key="refreshKey"
        :hide-top="true"
        :params="{ square_id: square.squareId, posted_at: postedAt }"
        class="trends-list"
        :api="getSquareTimeline"
        :square="square"
        view-observer="HOME_PAGE"
        go-detail
        hide-more-nav
        from="friend-detail"
        @removed="refreshKey++"
        @load="loaded"
        @on-show="onPostShow"
      />
    </div>
  </div>
</template>

<script lang="ts" setup name="SquareFriendDetail">
import { useRoute } from 'vue-router';
import { onActivated, ref, watch, computed, onMounted, onUnmounted } from 'vue';
import debounce from 'lodash/debounce';
import PostList from '../components/post/PostList.vue';
import { getSquareTimeline } from '@/api/square/post';
import useNavigate from '@/views/square/hooks/navigate';
import SquareAvatar from '@/views/square/components/SquareAvatar.vue';
import DatePicker from '../homepage/components/DatePicker.vue';
import { useSquareStore } from '@/views/square/store/square';
import { usePreload } from '../homepage/hooks';

const route = useRoute();
const { goHomePage, backIndex } = useNavigate();
const square = ref({});
const squareStore = useSquareStore();

const isSelf = computed(() => squareStore.isSelfSquare(square.value.squareId));

watch(() => route.query, (val) => {
  if (!val || !val.square) return;
  try {
    square.value = JSON.parse(decodeURIComponent(val.square) || '{}');
  } catch (e) {
    console.log(e);
  }
}, { immediate: true });

const datePickerRef = ref();
const {
  loadingUp,
  load,
} = usePreload();

const loadPrevData = async (top?) => {
  if (top >= 1) return;
  load(postListRef.value, 24);
};

/**
 * 当文章在页面顶部附近时触发
 */
const onPostShow = (item) => {
  if (item.stickOnTop) return;
  const post = item.post;
  const date = new Date(post.postedAt);
  datePickerRef.value.setCurrYear(date.getFullYear());
  datePickerRef.value.setCurrMonth(date.getMonth() + 1);
};

const loaded = () => {
  // 取消红点
  // store.unreadPostCount = 0;
  // updateSidebarCount(0 || store.newsStats.total);
};

const postListRef = ref(null);
const scrollDisabled = computed(() => postListRef.value?.status === 'finished');
const onLoading = () => {
  postListRef.value?.loading();
};

const postedAt = ref('');
// 选择年月后定位到指定动态
const dateSelect = (year: number, month: number) => {
  let y = year;
  let m = month;

  if (m === 12) {
    y += 1;
    m = 0;
  }

  // 查找有指定年月的已加载的数据
  const pageContent = document.querySelector('.page-content');
  const dataList = postListRef.value.dataList;
  let hasDataForDate = false;
  let index = 0;

  for (const item of dataList) {
    if (item.stickOnTop) {
      continue;
    }

    const date = new Date(item.post.postedAt);
    if (date.getFullYear() === y && date.getMonth() + 1 === m) {
      hasDataForDate = true;
      break;
    }
    index++;
  }

  // 找到则滚动定位
  if (hasDataForDate) {
    const list = document.querySelector('.trends-list') as HTMLElement;
    const target = list?.children[index] as HTMLElement;
    const headerHeight = 103 + 12;
    const top = target.offsetTop + list.offsetTop - headerHeight;
    pageContent.scrollTo({ top, behavior: 'smooth' });
  } else {
    pageContent.scrollTo({ top: 0 });
    // 没找到即未加载过指定月份，直接加载对应年月的动态
    postedAt.value = new Date(`${y}/${m + 1}/1`).toISOString();
    refreshKey.value++;
  }
};

let wheelLoading = false;
// 加载之前的动态
const onWheel = debounce(async (e) => {
  if (wheelLoading || loadingUp.value || e.deltaY >= 0) return;

  wheelLoading = true;
  await loadPrevData(e.deltaY);
  setTimeout(() => {
    wheelLoading = false;
  }, 1000);
}, 400);

onMounted(() => {
  document.addEventListener('wheel', onWheel);
});

onUnmounted(() => {
  document.removeEventListener('wheel', onWheel);
});

/**
 * 监听滚动条位置
 */
//  watch(() => props.scrollTop, loadPrevData);

// 保证每次刷新数据
const refreshKey = ref(0);
onActivated(async () => {
  refreshKey.value++;
});
</script>

<style lang="less" scoped>
.header {
  display: flex;
  align-items: center;
  width: 100%;
  height: 56px;
  padding: 10px 16px;
  font-size: 14px;
  color: @kyy_font_1;
  position: sticky;
  top: 0;
  z-index: 99;
  background-color: var(--bg-kyy-color-bg-deep, #f5f8fe);
  &::after {
    content: " ";
    clear: both;
  }

  .back {
    height: 100%;
    font-weight: 400;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: var(--text-kyy_color_text_2, #516082);
    .icon {
      color: #516082;
      font-size: 20px;
    }
  }

  .friend {
    margin: 0 auto;
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 400;
    color: var(--text-kyy_color_text_2, #516082);
    border-radius: 99px;
    background: var(--bg-kyy_color_bg_light, #FFF);
    cursor: pointer;
    padding: var(--checkbox-kyy_radius_checkbox, 2px) 8px var(--checkbox-kyy_radius_checkbox, 2px) var(--checkbox-kyy_radius_checkbox, 2px);
    transform: translateX(-24px);
    .avatar {
      width: 24px;
      height: 24px;
      border-radius: 5px;
    }
    .name {
      //flex: 1;
      //width: 0;
      max-width: 300px;
      font-size: 14px;
      .ellipsis();
    }
  }
}

.content-wrap {
  padding: 0 16px 16px;
}

.date-wrap {
  position: sticky;
  top: 56px;
  display: flex;
  width: 100%;
  padding: 8px 12px;
  align-items: center;
  gap: 4px;
  border-bottom: 1px solid var(--divider-kyy_color_divider_deep, #D5DBE4);
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  z-index: 99;
}
.loading-up {
  text-align: center;
}
</style>
