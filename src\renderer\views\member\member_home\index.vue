<template>
  <div v-if="isAdminValue && (isAdminValue.super || isAdminValue.isAdmin || isAdminValue.superAdmin)" class="main">
    <div class="left-mu">
      <div class="top">
        <div>
          <template v-for="(tree, treeIndex) in treeData" :key="treeIndex" :class="{
              'mu-item-active': currentPanel?.panel === tree?.panel,
            }">
            <div class="mu-header cursor" :class="{
              'mu-item-active': currentPanel?.panel === tree.panel,
            }" @click="onSetActivePanel(tree)" v-if="tree.noChildren">
              <img v-if="tree.icon === 'icon-llr'" src="/assets/member/icon_people.svg" class="icon-color" />
              <img v-else class="icon-color" src="/assets/member/icon_set.svg" />
              <div class="mu-text">{{ tree.label }}</div>
            </div>

            <div v-else class="mu-header cursor" @click="onExpand(tree, tree?.noChildren ? tree : null)">
              <img v-if="tree.icon === 'icon-color-member'" src="/assets/member/icon_vip.svg" class="icon-color" />
              <img
                v-else-if="tree.icon === 'iconadvertisement'"
                src="@/assets/member/icon_advertisement.svg"
                class="icon-color"
              />
              <img v-else-if="tree.icon === 'icon-forum'" src="/assets/member/icon_forum.svg" class="icon-color" />
              <iconpark-icon v-else-if="tree.icon === 'commodity'" style="font-size: 20px;" name="commodity"></iconpark-icon>
              <img v-else class="icon-color" src="/assets/member/icon_set.svg" />
              <div class="mu-text">{{ tree.label }}</div>
              <span class="mu-tree" v-if="!tree?.noChildren">
                <iconpark-icon v-show="!tree.expand" name="iconarrowdown" class="iconarrowdown"></iconpark-icon>
                <iconpark-icon v-show="tree.expand" name="iconarrowup-a960jjb9" class="iconarrowdown"></iconpark-icon>
              </span>
              <span v-if="tree?.noChildren && tree.value" class="tree-item-num">
                {{ tree.value }}
              </span>
            </div>
            <div v-for="(treeItem, treeItemIndex) in tree.children" v-show="treeItem.expand" :id="treeItem.panel"
              :key="treeItemIndex" class="mu-item cursor" :class="{
                'mu-item-active': currentPanel?.panel === treeItem.panel,
              }" @click="onSetActivePanel(treeItem)">
              <div class="text">{{ treeItem.label }}</div>
              <div v-if="treeItem.count" class="count">{{ treeItem.count > 99 ? '99+' : treeItem.count }}</div>
            </div>
          </template>
        </div>
        <!-- <img
          v-if="proxy.$i18n.locale === 'zh-cn'"
          class="ringkol cursor"
          src="@renderer/assets/member/icon/member_entry.png"
          @click="goLeaf"
        >
        <img
          v-else
          class="ringkol cursor"
          src="@renderer/assets/member/icon/member_entry_hk.png"
          @click="goLeaf"
        > -->
      </div>
      <!-- 平台专属名称 -->
      <div class="boxName mb-10 mt-10" v-if="specificNameInfo && false">
        <div class="onlyname">
          <div class="onlyname-top">
            <div class="onlyname-top-left">

              <span class="text">平台专属名称</span>
            </div>
            <div class="onlyname-top-right" v-if="![0].includes(specificNameInfo?.exclusive_status)">
              <t-popup
                theme="default"
                placement="bottom-right"
                overlayClassName="regular activity"
                :cancel-btn="null"
                :confirm-btn="null"
                :zIndex="200"
                v-model="moreVisible"
              >
                <template #icon />
                <template #content>
                  <div class="pop" >
                    <span class="pop-item" @click="onLookMoreRecord">
                      <iconpark-icon
                        name="iconrepair"
                        class="icon"
                      />
                      变更记录
                    </span>
                  </div>
                </template>
                <div class="iconMore" :class="{'iconActive': moreVisible}">
                  <iconpark-icon class="icon cursor" name="iconmore-a961a3l6"></iconpark-icon>
                </div>
              </t-popup>
            </div>
          </div>
          <div class="onlyname-btw" v-if="[0, 2].includes(specificNameInfo?.exclusive_status)">
            <div class="onlyname-btw-left">
              <span class="noget">未获取</span>
            </div>
            <div class="onlyname-btw-right" @click="onGetExclusiveName">
              <span class="toget">去获取</span>
              <iconpark-icon class="icon" name="iconarrowright"></iconpark-icon>
            </div>
          </div>
          <div class="onlyname-btw" v-if="specificNameInfo?.exclusive_status === 1">
            <div class="onlyname-btw-left">
              <span class="noget line-1 w-176px"><MyTooltipComp :text="specificNameInfo?.exclusive_name"></MyTooltipComp></span>
            </div>

          </div>
          <div  class="tag mt-4px" v-if="specificNameInfo?.exclusive_status === 1">
            <span class="green">专属</span>
            <span class="yellow">生效中</span>
          </div>
        </div>
      </div>

      <!-- v-show="isAdminValue.member" -->
      <div class="bottom cursor" @click="onSelectPlatform">
        <!-- <iconpark-icon name="iconpeople" class="iconpeople"></iconpark-icon> -->
        <img src="@renderer/assets/member/icon/icon_member.png" class="iconpeople" />
        <!-- <span class="text">会员端</span> -->
        <span class="text">{{ $t("member.winter.member_platform") }}</span>
      </div>
    </div>

    <div class="right">
      <template v-if="currentPanel?.panel.includes('ForumAdmin')">
        <component
          v-if="store.isForumAdmin && store.ownerId"
          :is="panels[currentPanel.panel]"
          :key="currentPanel.panel"
        />
        <no-forum-admin-permission v-else-if="store.isForumAdmin === false" />
      </template>
      <component
        v-else
        :is="panels[currentPanel ? currentPanel.panel : '']"
        :count="currentPanel?.count"
        @on-page="onListenPage"
        @get-red-num="getRedNum"
        :key="currentPanel?.panel"
      />
      <!-- <keep-alive>

      </keep-alive> -->
    </div>
  </div>
  <div v-else-if="!isLoading" class="empty">
    <t-loading size="medium" class="memberLoading" :loading="isLoading" show-overlay text="加载中..." />
    <!-- <noData :text="'你没有会员管理权限'" /> -->
  </div>
  <AdminAnnualConnect ref="adminAnnualConnectRef" :activeAccount="activeAccount" :tabList="props.tabList"
    @deltabItem="onDelTabItem" />

  <t-guide v-model="current" :steps="steps" :z-index="5000" :finish-button-props="{
    content: '去设置',
  }" @change="handleChange" @prev-step-click="handlePrevStepClick" @next-step-click="handleNextStepClick"
    @finish="handleFinish" @skip="handleSkip" />

  <Tricks v-if="currentPanel?.uuid" :offset="{ x: '-32', y: '-40' }" :uuid="currentPanel?.uuid" :key="currentPanel?.uuid"/>
  <changeRecord ref="changeRecordRef" :activeAccount="activeAccount" @onRenew="onRenew"></changeRecord>
  <exclusiveSetting ref="exclusiveSettingRef" :activeAccount="activeAccount"></exclusiveSetting>

</template>

<script setup lang="ts" name="bench_member_home">
import {ref, toRaw, watch, computed, getCurrentInstance, onMounted, Ref, onBeforeUnmount, nextTick} from "vue";
import { useI18n } from "vue-i18n";
// import SvgIcon from "@/components/SvgIcon.vue";
import { panels } from "@renderer/views/member/member_home/panel";

import CountBadgeComp from "@renderer/components/member/components/CountBadgeComp.vue";
import { useMemberStore } from "@renderer/views/member/store/member";
import { updateAllCount } from "@renderer/views/member/hooks/total";
import { useRoute, useRouter } from "vue-router";
import { checkExpiration, getResponseResult, platFn } from "@renderer/utils/myUtils";
import { MessagePlugin, DialogPlugin } from "tdesign-vue-next";
import { checkIsAdminAxios, workShopAppAxios, dynamicCountAppAxios, getStatistics } from "@renderer/api/member/api/businessApi";
import { allRoutersUuid } from "@renderer/constants/index";
import noData from "@renderer/components/common/Empty.vue";
import { getProfilesInfo, getGuidesInfo, setGuides } from "@renderer/utils/auth";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
// import { getPoliticsTeamID } from "@renderer/views/politics/utils/auth";
import { getMemberTeamID, goToDigitalPlatform_member } from "@renderer/views/member/utils/auth";
import { onActivated } from "vue";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
// import { getTeamAnnualFee } from "@renderer/api/digital-platform/api/businessApi";
import to from "await-to-js";
// import { FeePackageItemType } from "@renderer/api/square/models/fee";
// import { isLimitMasPlatformDialog } from '@renderer/views/digital-platform/utils/auth'
import AdminAnnualConnect from "@renderer/views/digital-platform/components/AdminAnnualConnect.vue";
import changeRecord from '@renderer/views/digital-platform/modal/changeRecord.vue';
import exclusiveSetting from '@renderer/views/digital-platform/modal/exclusiveSetting.vue';
import MyTooltipComp from "@renderer/components/engineer/components/MyTooltipComp.vue";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const router = useRouter();

const { proxy } = getCurrentInstance() as any;
const profile = getProfilesInfo();

const store = useMemberStore();

const guides = getGuidesInfo();
// 引导
const current = ref(-1);


const { t } = useI18n();

const treeData = ref(null);
let treePlatData = null;
const currentPanel = ref(null);
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();

const isLoading = ref(false);
const { menuList, routeList, roleFilter } = useRouterHelper("memberIndex");

setTimeout(() => {
  if (profile && guides && Array.isArray(guides) && guides.length > 0) {
    const isGu = guides.some((v) => v.openid === profile.openid);
    if (!isGu) {
      current.value = 0;
      guides.push({ openid: profile.openid, value: 1 });
      setGuides(guides);
    }
  } else {
    current.value = 0;
    let arr = [];
    arr.push({ openid: profile.openid, value: 1 });
    setGuides(arr);
  }
  // current.value = 0;
}, 1100);

const steps = [
  {
    element: "#PRegularMemberPanel",
    title: " ",
    // body: '添加会员、管理会员信息、审核会员入会申请、记录会费',
    body: t("member.impm.set_1"),
    placement: "right",
    highlightPadding: 58,

    // 示例代码有效
    // highlightContent: () => <div style={{ width: '500px', height: '150px' }}>自定义引导</div>
    // 示例代码有效
    // highlightContent: <div style={{ width: '500px', height: '150px' }}>自定义引导</div>,
  },
  {
    element: "#importList",
    title: "",
    // body: '可通过添加、邀请、批量导入等多种方式新增会员',
    body: t("member.impm.set_2"),
    placement: "bottom",
  },
  {
    element: "#applyList",
    title: "",
    // body: '查看和审核会员入会申请、激活申请',
    body: t("member.impm.set_3"),

    placement: "right",
  },
  {
    element: "#PMembershipSettingPanel",
    title: "",
    // body: '入会申请设置、会员到期提醒设置',
    body: t("member.impm.set_4"),

    placement: "right",
    highlightPadding: 0,
  },
  {
    element: "#PMembershipPositionPanel",
    title: "",
    // body: '设置会员职务与会员等级后才能添加/邀请会员入会',
    body: t("member.impm.set_5"),

    placement: "right",

    highlightPadding: 0,
  },
];

// const store: any = computed(() => {
//   if (platformCpt.value === platform.digitalPlatform) {
//     return digitalPlatformStore;
//   }
//     return useMemberStore();

// });
const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
  activationGroupItem: {
    type: Object,
    default: null,
  },
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
  activeIndex: {
    type: Number,
    default: 0,
  },
});

// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform;
});

const activeAccount = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    let user_ids = {};
    if (route.query.user_ids) {
      user_ids = JSON.parse(route.query.user_ids);
    }
    return { ...route.query, user_ids };
  } else {
    return store.activeAccount;
  }
});

const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query?.teamId || 0;
  } else {
    if (!getMemberTeamID()) {
      return route.query?.teamId || 0;
    }
    return getMemberTeamID();
  }
});

const goLeaf = () => {
  // router.push({
  //   path: "/memberIndex/member_manage",
  //   query: {
  //     // projectId: props.projectId
  //   }
  // });
  if (platformCpt.value === platform.digitalPlatform) {
    // 平台
    console.log("数字平台");
  } else if (platformCpt.value === platform.digitalWorkbench) {
    // 平台
    console.log("数字工作台");
  } else {
    const searchMenu = routeList.find((v) => v.name === "member_leaflets");
    // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
    router.push({ path: searchMenu.fullPath, query: {} });
    store.addTab(toRaw(searchMenu));
  }
};

const handleChange = (current, { e, total }) => {
  console.log(current, e, total);
};

const handlePrevStepClick = ({ e, prev, current, total }) => {
  console.log(e, prev, current, total);
};

const handleNextStepClick = ({ e, next, current, total }) => {
  console.log(e, next, current, total);
};

const handleFinish = () => {
  // visible.value = false;
  // console.log(e, current, total);
  current.value = -1;
  store.setQuickCreatePosTag(true);
  store.setCurrentPanel("PMembershipPositionPanel"); // 跳转到职务
};

const handleSkip = ({ e, current, total }) => {
  // visible.value = false;
  console.log(e, current, total);
};

const showReceiptMenu = computed(() => {
  const { teamRegion } = props.activationGroupItem;
  return teamRegion === 'CN';
});

const initTreeData = () => {
  treeData.value = [
    {
      label: t("member.impm.set_6"),
      expand: true, // 默认是否展开
      icon: "icon-color-member",
      value: "",
      children: [
        {
          label: t("member.winter.admin_platform_tip"), // 正式会员
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "PRegularMemberPanel",
          uuid: '数字商协-管理后台-会员管理'
        },
        {
          label: '平台成员', // 平台成员
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "PlatformMembersPanel",
          uuid: '数字商协-管理后台-平台成员'
        },
        // {
        //   label: t("member.menu.application"), // 入会申请
        //   expand: true, // 默认是否展开
        //   icon: "icon-organization",
        //   value: "",
        //   // count: store.getAllTeamApplyCount,
        //   children: [],
        //   panel: "PApplyMemberPanel"
        // },
        // {
        //   label: t("member.menu.active_vip"), // 激活会员
        //   expand: true, // 默认是否展开
        //   icon: "",
        //   value: "",
        //   // count: store.getAllTeamActiveCount,
        //   panel: "PActiveMemberPanel",
        //   children: []
        // },
        {
          label: t("member.menu.fee_record"), // 会费记录
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "PMembershipFeePanel",
          uuid: '数字商协-管理后台-会费记录'
        },
        {
          label: t("member.winter.pos_manage"), // 会员职务
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "PMembershipPositionPanel",
          uuid: '数字商协-管理后台-职务管理'
        },
        {
          label: t("member.squarek.h"), // 电子会刊
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "EbookPanel",
          uuid: '数字商协-管理后台-电子会刊'
        },
        {
          label: t("member.eb.n"), // 平台动态
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "TrendsPanel",
          uuid: '数字商协-管理后台-平台动态'
        },
        {
          label: '活动管理', // 活动管理
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "ActivityPlatformAdminPanel",
          uuid: '数字商协-管理后台-活动管理',
          count: activityRedNum,
        },
      ],
    },
    {
      label: `联络人`, // 分组管理
      expand: true, // 默认是否展开
      icon: "icon-llr",
      value: "",
      children: [],
      panel: "ContactPersonPanel",
      noChildren: true,
      uuid: '数字商协-管理后台-联络人'
    },
    {
      label: t("ad.gg"), // 市场广告
      expand: true, // 默认是否展开
      icon: "iconadvertisement",
      children: [
        {
          label: '市场店铺广告',
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          count: marketShopAdNum,
          panel: "MarketShopAdvertising",
          uuid: '数字商协-管理后台-市场店铺广告'
        },
        {
          label: t("ad.scgg"),
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          count: marketAdNum,
          panel: "MarketAdvertising",
          uuid: '数字商协-管理后台-市场广告'
        },
        {
          label: t("ad.ptgg"), //
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          count: platformAd,
          children: [],
          panel: "PlatformAdvertising",
          uuid: '数字商协-管理后台-平台广告'
        },
      ],
      // noChildren: true,
    },
    {
      label: t("forum.forumManager"), // 论坛管理
      expand: true,
      icon: "icon-forum",
      children: [
        {
          label: t("forum.postManager"),
          expand: true,
          icon: "",
          value: "",
          children: [],
          panel: "ForumAdminPostManagerPanel",
          uuid: "数字商协-管理后台-论坛管理-内容管理"
        },
        {
          label: t("forum.topicManager"), // 话题管理
          expand: true,
          icon: "",
          value: "",
          children: [],
          panel: "ForumAdminTopicManagerPanel",
          uuid: "数字商协-管理后台-论坛管理-话题管理"
        },
        {
          label: t("forum.userManager"), // 成员管理
          expand: true,
          icon: "",
          value: "",
          children: [],
          panel: "ForumAdminUserManagerPanel",
          uuid: "数字商协-管理后台-论坛管理-成员管理"
        },
        {
          label: t("forum.generalSetting"), // 通用设置
          expand: true,
          icon: "",
          value: "",
          children: [],
          panel: "ForumAdminGeneralSettingPanel",
          uuid: "数字商协-管理后台-论坛管理-通用设置"
        },
      ],
    },
    {
      label: '市场', // 市场
      expand: true, // 默认是否展开
      icon: "commodity",
      value: "commodity",
      children: [
        {
          label: '市场商品管理', // 入会设置 改成通用设置，与提醒设置合并为一
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "MarketGoodManagerPanel",
          uuid: "数字商协-管理后台-市场-商品管理"
        },
        {
          label: '佣金资金', // 入会设置 改成通用设置，与提醒设置合并为一
          expand: showReceiptMenu.value ? true : false, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "PlatformSettlementFund",
          uuid: "数字商协-管理后台-市场-佣金资金"
        },
        {
          label: '结算佣金', // 入会设置 改成通用设置，与提醒设置合并为一
          expand: showReceiptMenu.value ? true : false, // 默认是否展开, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "PlatformSettlementOrder",
          uuid: "数字商协-管理后台-市场-结算佣金"
        },
      ],
    },
    {
      label: t("member.menu.setting"), // 设置
      expand: true, // 默认是否展开
      icon: "icon-color-setup",
      value: "",
      children: [
        {
          label: t("member.winter.tong_setting"), // 入会设置 改成通用设置，与提醒设置合并为一
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "PMembershipSettingPanel",
          uuid: "数字商协-管理后台-通用设置"
        },

        // {
        //   label: t("member.menu.remind_setting"), // 提醒设置
        //   expand: true, // 默认是否展开
        //   icon: "",
        //   value: "",
        //   children: [],
        //   panel: "PReminderSettingPanel"
        // },
        {
          label: t("member.winter.manage_setting"), // 应用管理
          expand: true, // 默认是否展开
          icon: "",
          value: "",
          children: [],
          panel: "PAppAdminPanel",
          uuid: "数字商协-管理后台-管理员设置"
        },
      ],
    },
  ];
  if(route.query?.teamRegion === 'MO') {
    // 帮我从treeData.value中去掉commodity这部分
    treeData.value = treeData.value.filter((v) => v.value !== 'commodity');
    console.log( treeData.value, 'ff')
  }


  // if (route.query.panel) {
  //   currentPanel.value = route.query.panel;
  // } else {
  //   currentPanel.value = treeData.value[0].children[0];
  // }
  treePlatData = platFn(treeData.value);
  // 为了记住点击到哪了
  // 来自消息的跳转
  if (goMemberAdmin()) return;
  if (route.query && route.query.origin === "forum" && !store.isForumAdminRedirected) {
    // 来自论坛的跳转，在onMountedOrActivated中处理
    return;
  }
  if (route.query && route.query?.origin?.includes("MarketGoodManagerPanel")) {
    // 来自论坛的跳转，在onMountedOrActivated中处理
    return;
  }

  if (store.currentPanel) {
    const panelItem = treePlatData ? treePlatData.find((v) => v.panel === store.currentPanel) : null;
    currentPanel.value = panelItem || currentPanel.value;
  } else {
    currentPanel.value = treeData.value[0].children[0];
  }
  if(currentPanel.value) {
    store.setCurrentPanel(toRaw(currentPanel.value?.panel));
  } else {
    currentPanel.value = treeData.value[0].children[0];
  }
};

import { adwaitreviewcount } from "@renderer/api/member/api/ebookApi";
import NoForumAdminPermission from "./panel/forum-admin/noPermission.vue";
import emitter from "@/utils/MittBus";
import { refreshRegularMemberBus } from "@renderer/views/digital-platform/utils/eventBus";
import { getExclusiveSettingAxios, getPendingAuditCountAxios } from "@renderer/api/digital-platform/api/businessApi";
import {dPCountPendingReview} from "@/api/activity/platform";

const marketShopAdNum = ref(0);
const marketAdNum = ref(0);
const platformAd = ref(0);
const moreVisible = ref(false);

const activityRedNum = ref(0);
/**
 *
 * @param idStaff 获取应用统计
 */
const appCount = ref(0);
const onWorkShopAppAxios = async () => {
  let res: any = null;
  try {
    res = await workShopAppAxios({ uuids: ["member"] }, currentTeamId.value);
    res = getResponseResult(res);
    if (!res) return;
    console.log(res);
    const { data } = res;
    console.log(data);
    // appCount.value = data && (data.length > 0) ? data[0].count: 0;
    appCount.value = data || 0;

    const applyPanel = treePlatData ? treePlatData?.find((v) => v.panel === "PRegularMemberPanel") : null;
    if (applyPanel) applyPanel.count = appCount.value;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === "Network Error") {
    } else {
      // MessagePlugin.error(errMsg);
      console.log(errMsg);
    }
  }
};

/**
 *
 * @param idStaff 获取应用统计
 */
//  const appCount = ref(0);
const onDynamicCountAppAxios = async () => {
  let res: any = null;
  try {
    res = await dynamicCountAppAxios({}, currentTeamId.value);
    res = getResponseResult(res);
    if (!res) return;
    console.log(res);
    const { data } = res;
    console.log(data);
    // appCount.value = data && (data.length > 0) ? data[0].count: 0;
    let count = data?.pending_review_total || 0;

    const applyPanel = treePlatData ? treePlatData?.find((v) => v.panel === "TrendsPanel") : null;
    if (applyPanel) applyPanel.count = count;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === "Network Error") {
    } else {
      // MessagePlugin.error(errMsg);
      console.log(errMsg);
    }
  }
};


const onRingkolReviewMarketCount = async () => {
  let res: any = null;
  try {
    res = await getPendingAuditCountAxios(currentTeamId.value);
    res = getResponseResult(res);
    if (!res) return;
    console.log(res);
    const { data } = res;
    console.log('onRingkolReviewMarketCount',data);
    // appCount.value = data && (data.length > 0) ? data[0].count: 0;
    let count = data?.count || 0;

    const applyPanel = treePlatData ? treePlatData?.find((v) => v.panel === "MarketGoodManagerPanel") : null;
    if (applyPanel) applyPanel.count = count;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === "Network Error") {
    } else {
      // MessagePlugin.error(errMsg);
      console.log(errMsg);
    }
  }
};



// dynamicCountAppAxios



const getRedNum = () => {
  //刷新红点
  console.log("刷新全部红点");
  getMarketAdNum();
  // onWorkShopAppAxios(); // 刷新组织管理红点
  onDynamicCountAppAxios();
  getStatisticsReq();
  onRingkolReviewMarketCount();
  getActivityRedNum();
};
const getMarketAdNum = () => {
  //市场广告红点
  adwaitreviewcount(currentTeamId.value, {
    platform_type: 1,
  }).then((res) => {
    marketAdNum.value = res.data.data.market_count;
    marketShopAdNum.value = res.data.data.shop_count;

    platformAd.value = res.data.data.platform_count;
  });
};

const getActivityRedNum = async () => {
  const res = await dPCountPendingReview({
    teamId: currentTeamId.value,
  });
  activityRedNum.value = res.data.data.pendingReviewCount;
}

ipcRenderer.on('IM-refresh', () => {
  if (activeAccount.value) {
    getRedNum();
  }
});

// getPlatformAd
// 进入会员管理的逻辑
const goMemberAdmin = () => {
  const tag = "PRegularMemberPanel";
  const trend = "TrendsPanel";
  const plat = "PlatformMembersPanel";
  const contactor = 'ContactPersonPanel'; // 联络人
  if (route.query && route.query.origin === "message") {
    console.log(route.query, "哈哈哈");
    const { teamId, redirect } = route.query;
    if (teamId && redirect && redirect.includes(tag)) {
      const panelItem = treePlatData ? treePlatData.find((v) => v.panel === tag) : null;
      currentPanel.value = panelItem || currentPanel.value;
      store.setCurrentPanel(toRaw(currentPanel.value.panel));
      return true;
    } else if (teamId && redirect.includes(trend)) {
      const panelItem = treePlatData ? treePlatData.find((v) => v.panel === trend) : null;
      currentPanel.value = panelItem || currentPanel.value;
      toRaw(currentPanel.value.panel);
      return true;
    } else if (teamId && redirect.includes(plat)) {
      const panelItem = treePlatData ? treePlatData.find((v) => v.panel === plat) : null;
      currentPanel.value = panelItem || currentPanel.value;
      store.setCurrentPanel(toRaw(currentPanel.value.panel));
      return true;
    } else if (teamId && redirect.includes(contactor)) {
      const panelItem = treePlatData ? treePlatData.find((v) => v.panel === contactor) : null;
      currentPanel.value = panelItem || currentPanel.value;
      store.setCurrentPanel(toRaw(currentPanel.value.panel));
      return true;
    }
  }
  return false;
};

const changeRecordRef = ref(null);
const onLookMoreRecord = () => {
  changeRecordRef.value?.onOpen()
}

const exclusiveSettingRef = ref(null);
const onGetExclusiveName = () => {
  exclusiveSettingRef.value?.onOpen();
}

// 专属名称续期
const onRenew = () => {
  exclusiveSettingRef.value?.onOpen( {etype: 2});
}



// watch(
//   () => store.getAllTeamApplyAndActive,
//   (val) => {
//     // if (val) {
//     //   console.log(val, "入会申请增加了呀");
//     //   // 设置申请入会store数量监听
//     //   const applyPanel = treePlatData.find(
//     //     (v) => v.panel === "PApplyMemberPanel"
//     //   );
//     //   if (applyPanel) applyPanel.count = store.getAllTeamApplyCount;
//     // }
//     const applyPanel = treePlatData
//       ? treePlatData?.find((v) => v.panel === "PRegularMemberPanel")
//       : null;
//     if (applyPanel) applyPanel.count = store.getAllTeamApplyAndActive;
//   },
//   {
//     deep: true,
//     immediate: true
//   }
// );

/* watch(
  () => store.getAllTeamApplyCount,
  (val) => {
    // if (val) {
    //   console.log(val, "入会申请增加了呀");
    //   // 设置申请入会store数量监听
    //   const applyPanel = treePlatData.find(
    //     (v) => v.panel === "PApplyMemberPanel"
    //   );
    //   if (applyPanel) applyPanel.count = store.getAllTeamApplyCount;
    // }
    const applyPanel = treePlatData
      ? treePlatData?.find((v) => v.panel === "PApplyMemberPanel")
      : null;
    if (applyPanel) applyPanel.count = store.getAllTeamApplyCount;
  },
  {
    deep: true,
    immediate: true
  }
);
watch(
  () => store.getAllTeamActiveCount,
  (val) => {
    // if (val) {
    //   console.log(val, "入会申请增加了呀");
    //   // 设置激活会员store数量监听
    //   const activePanel = treePlatData.find(
    //     (v) => v.panel === "PActiveMemberPanel"
    //   );
    //   if (activePanel) activePanel.count = store.getAllTeamActiveCount;
    // }
    const activePanel = treePlatData
      ? treePlatData?.find((v) => v.panel === "PActiveMemberPanel")
      : null;
    if (activePanel) activePanel.count = store.getAllTeamActiveCount;
  },
  {
    deep: true,
    immediate: true
  }
); */

const emits = defineEmits(["selectPlatform", "deltabItem"]);
const onSelectPlatform = () => {
  const confirmDia = DialogPlugin({
    header: t("member.winter.sure_switch_app"),
    theme: "info",
    body: t("member.winter.weihu_member_info"),
    closeBtn: null,
    confirmBtn: t("member.winter.sure_to_way"),
    className: "delmode",
    onConfirm: async () => {
      // selectApp('member');
      // emits('selectPlatform', 'member');
      confirmDia.hide();
      goToDigitalPlatform_member(currentTeamId.value);
      // ipcRenderer.invoke('IM-refresh');
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

const selectApp = (type) => {
  const arr = store.getStorePlatforms;
  const result = arr.find((v) => v.openId === profile.openid && v.teamId === activeAccount.value?.teamId);
  if (result) {
    result.apply = type;
  } else {
    arr.push({ openId: profile.openid, teamId: activeAccount.value?.teamId, apply: type });
  }
  store.setStorePlatforms(arr);
};

const onDelTabItem = () => {
  const index = props.tabList.findIndex((e: any) => e.path === "/workBenchIndex/member_home");

  emits("deltabItem", index, true);
};

const isAdminValue = ref(null);

// 判断当前用户是否为管理员
const onCheckIsAdmin = async (params) => {
  console.log(params, "paramsparams1111111");
  console.log(route.query, "paramsparams1111111query");

  if (!params.idStaff) return;
  let res: any = null;
  try {
    res = await checkIsAdminAxios(params, currentTeamId.value);
    isLoading.value = false;
    res = getResponseResult(res);
    if (!res) return;

    isAdminValue.value = res.data;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === "Network Error") {
    } else {
      MessagePlugin.error(errMsg);
    }
  }
  isLoading.value = false;
};

const isManage = computed(
  () => activeAccount && (isAdminValue.value.super || isAdminValue.value.isAdmin || isAdminValue.value.superAdmin),
);

// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       isLoading.value = true;
//       updateAllCount();

//       // ipcRenderer.send(
//       //   "update-nume-index",
//       //   allRoutersUuid.findIndex((v) => v === "member")
//       // );
//       setTimeout(() => {
//         onCheckIsAdmin(val?.staffId);
//       });
//     }
//   },
//   {
//     // deep: true,
//     immediate: true
//   }
// );

watch(
  () => store.currentPanel,
  (val) => {
    if (val) {
      console.log("触发到我了吗哈哈哈！", val);
      // currentPanel.value = val;
      const panelItem = treePlatData ? treePlatData.find((v) => v.panel === val) : null;
      currentPanel.value = panelItem || currentPanel.value;
      // updateAllCount();
      // ipcRenderer.send(
      //   "update-nume-index",
      //   allRoutersUuid.findIndex((v) => v === "member")
      // );
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

initTreeData();

const onExpand = (val, tree) => {
  if (tree) {
    store.setCurrentPanel(toRaw(val.panel));
    return;
  }
  if (val.children && val.children.length > 0) {
    val.expand = !val.expand;
    val.children.map((v) => (v.expand = val.expand));
  }
};

const onSetActivePanel = (val) => {
  // currentPanel.value = val;
  store.setCurrentPanel(toRaw(val.panel));
  getRedNum();
  console.log(val);
};







const onListenPage = (val) => {
  console.log(val);
  // const resultItem = treePlatData.value.find((v) => v.panel === val);
  // if (resultItem) {
  //   currentPanel.value = resultItem;
  //   onSetActivePanel(val);
  // }
  onSetActivePanel({ panel: val });
};

const adminAnnualConnectRef = ref(null);
const isOnRunTeamAnnualFee = (): Promise<any> => {
  adminAnnualConnectRef.value?.onConnectPlatform();
};

// const openVisible = ref(false);
// const annualFeeDialogUpgradeVisible: Ref<any> = ref(false);
// const annualFeeDialogRenewVisible: Ref<any> = ref(false);

// const expirePackageRef = ref(null);
// const upgradePackageRef = ref(null);
// // mac商店类型
// const isMas = ref(__APP_ENV__.VITE_APP_MAS);

// 购买套餐权益判断, 能到这里说明已开启
// const isOnRunTeamAnnualFee = (): Promise<any> => {
//   return new Promise(async (resolve, reject) => {
//     const [err2, res2]: any = await to(
//       getTeamAnnualFee({ team_id: currentTeamId.value, annual_fee_detail: true }, currentTeamId.value),
//     );
//     if (err2) {
//       reject(err2);
//       return;
//     }
//     const { data } = res2;
//     console.log(data);
//     // data.annualFeeExpiredAt = '2023-05-15T07:25:50Z'

//     if (!data.opened) {
//       //（5）若当前组织未购买另可年费套餐，弹窗购买套餐。>>购买
//       openVisible.value = true;
//     } else if (checkExpiration(data.annualFeeExpiredAt)) {
//       // （6）若当前组织购买的套餐已过期，弹窗提示套餐已过期。>>套餐已过期
//       expirePackageRef.value.onOpen();
//     } else if (
//       !(
//         data?.annualFeeDetail?.package?.items?.length > 0 &&
//         data.annualFeeDetail.package.items.some((v) => v.itemType === FeePackageItemType.DigitalPlatform)
//       )
//     ) {
//       //
//       // （7）若当前组织购买的套餐未勾选数字平台搭建权益，则弹窗提示权益不足需升级套餐。>>套餐升级
//       console.log('hahh', isMas.value)
//       if(isMas.value) {

//         isLimitMasPlatformDialog().then(()=> {
//           onCloseUpgrade();
//         }).catch(()=> {
//           onCloseUpgrade();
//         });
//         return;
//       } else {
//         upgradePackageRef.value.onOpen();
//       }
//     } else {
//       // （8）若组织购买的套餐已勾选数字平台搭建权益，则提示应用开启成功，同时工作通知发送一条开启成功通知
//       // renewalDialogVisible.value = true;
//       // successOpenMemberRef.value.onOpen()
//       resolve("ok");
//       upgradePackageRef.value.onSetVisible(false);
//       expirePackageRef.value.onSetVisible(false);
//       return;
//     }
//     reject();
//   });
// };

// const onOpenUpgrade = () => {
//   annualFeeDialogUpgradeVisible.value = true;
// };
// // 升级成功后的回调
// const upgradeLoaded = () => {
//   console.log("upgradeLoaded");
//   // 升级后重新触发流程
//   // onRunTeamAnnualFee()
//   upgradePackageRef.value.onClose();
//   isOnRunTeamAnnualFee();
// };

// // 购买套餐成功
// const buySuccess = (val?) => {
//   console.log(val);
//   // 购买成功提示弹窗
//   // successOpenMemberRef.value.onOpen()
//   isOnRunTeamAnnualFee();
// };

// const renewalSuccess = (e) => {
//   console.log(e, "操作成功");
//   isOnRunTeamAnnualFee();
// };
// const onOpenRewal = () => {
//   annualFeeDialogRenewVisible.value = true;
// };

// const onCloseUpgrade = () => {
//   console.log(props.tabList);
//   // emits("deltabItem", props.tabIndex, true)
//   const index = props.tabList.findIndex((e: any) => e.path === "/workBenchIndex/member_home");
//   console.log(index, props.activeIndex);
//   // if (props.activeIndex === index) {
//   //   emits("deltabItem", index, true);
//   // } else {
//   //   emits("deltabItem", index);
//   // }
//   emits("deltabItem", index, true);
// };
const goForumAdmin = () => {
  if (route.query && route.query.origin === "forum" && !store.isForumAdminRedirected) {
    // 来自论坛的跳转，定位到论坛管理
    currentPanel.value = {
      label: "内容管理",
      expand: true, // 默认是否展开
      icon: "",
      value: "",
      children: [],
      panel: "ForumAdminPostManagerPanel",
    };
    store.setCurrentPanel(toRaw(currentPanel.value.panel));
    store.setForumAdminRedirected(true);
    document.getElementById('ForumAdminPostManagerPanel').scrollIntoView({ behavior: 'smooth', block: 'start' });
  } else if(route.query && route.query?.origin?.includes("MarketGoodManagerPanel")) {
    const panelItem = treePlatData ? treePlatData.find((v) => v.panel === 'MarketGoodManagerPanel') : null;
    currentPanel.value = panelItem || currentPanel.value;
  }
}

const resetForumAdminPermission = () => {
  store.setForumAdminPermission(false);
}

const specificNameInfo = ref();

const onGetExclusiveSettingAxios = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getExclusiveSettingAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }

      specificNameInfo.value = result?.data;
      resolve(result?.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};


onMounted(() => {
  emitter.on('noForumAdminPermission', resetForumAdminPermission);
  emitter.on('refreshActivityRedNum', getActivityRedNum);
  refreshRegularMemberBus.on(async (event: string) => {
    console.log('refreshRegularMemberBus')
    if (event === 'refreshRegular') {
      console.log('getRedNum')
      getRedNum();
    }
  });
})

onBeforeUnmount(() => {
  emitter.off('noForumAdminPermission', resetForumAdminPermission);
  emitter.off('refreshActivityRedNum', getActivityRedNum);
})

let isWaiting = false;
onMountedOrActivated(() => {
  if (isWaiting) return;

  isWaiting = true;
  store.loadOwnerId(activeAccount.value);
  console.log("dd", activeAccount.value);
  setTimeout(async () => {
    await onCheckIsAdmin({ ...activeAccount.value?.user_ids });
    await nextTick();
    goForumAdmin();
  });

  onGetExclusiveSettingAxios(); // 获取专属名称信息

  getMarketAdNum();
  getActivityRedNum();
  // onWorkShopAppAxios();
  onDynamicCountAppAxios();
  onRingkolReviewMarketCount();
  getStatisticsReq();
  setTimeout(() => {
    isWaiting = false;
    isOnRunTeamAnnualFee();
  }, 700);
});

const getStatisticsReq = () => {
  getStatistics(currentTeamId.value).then((res: any) => {
    if (res) {
      // appCount.value = res.data?.data?.visit_apply_count;
      const applyPanel = treePlatData ? treePlatData?.find((v) => v.panel === "PlatformMembersPanel") : null;
      if (applyPanel) {
        applyPanel.count = res.data?.data?.visit_apply_count;
      }
      // 组织管理
      const applyPanelMember = treePlatData ? treePlatData?.find((v) => v.panel === "PRegularMemberPanel") : null;
      if (applyPanelMember) applyPanelMember.count =  res.data?.data?.apply_count;
    }
  });
}

</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";


@bgFFF: #fff;

:deep(.t-badge--circle) {
  right: 0 !important;
}

.main {
  display: flex;
  flex: 1;
  // height: calc(100% - 89px);
  // height: calc(100% - 40px);
  height: 100%;

  // height: 100% !important;
  .left {
    width: 200px;
    flex: none;
    background-color: @bgFFF;
    border-right: 1px solid #e3e6eb;
    height: 100%;
    position: relative;

    .title {
      padding: 20px 16px;
      font-size: 16px;

      font-weight: 700;
      text-align: left;
      color: @kyy_gray_14;
    }

    .menu {
      display: flex;
      flex-direction: column;
      padding: 0 8px;

      &-dir {
        display: flex;
        flex-direction: column;

        &-title {
          padding: 5px 16px;
          display: flex;
          align-items: center;
          margin-top: 4px;

          &:first-child {
            margin-top: 0;
          }

          .icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
            margin-right: 12px;
            color: red;
            flex: inherit;
          }

          .text {
            font-size: 14px;

            font-weight: 400;

            color: @kyy_gray_14;
            flex: 1;
          }

          .updown {
            width: 16px;
            height: 16px;
            color: @kyy_color_icon_3;
          }
        }

        .active {
          background-color: #daecff;
          color: @kyy_color_icon_blue;
        }

        &-item {
          display: flex;
          margin-top: 4px;

          font-size: 14px;

          font-weight: 400;
          color: @kyy_gray_14;
          padding: 5px 0;
          border-radius: 4px;
          padding-left: 48px;
          transition: all 0.2s linear;

          &:hover {
            background-color: #daecff;
            color: @kyy_color_icon_blue;
          }

          .toLabel {
            flex: 1;
            display: flex;
            justify-content: space-between;

            .badge {}
          }

          .toCount {
            // position: relative;
            // display: flex;
            // flex: inherit;
            // background: @kyy_color_red_hover;
            // border-radius: 12px;
            // font-size: 12px;
            //
            // font-weight: 400;
            // text-align: center;
            // color: @kyy_white;
            // padding: 0 2px;
            // line-height: 20px;

            // background: #eeeeee;
            // border: 1px solid #dcdcdc;
            // box-sizing: border-box;
            // border-radius: 3px;
          }
        }
      }
    }
  }

  .right {
    position: relative;
    flex: 1;
    background: #fff;
    overflow: hidden;
  }
}

// :deep(.t-badge) {
//   //   display: inline-flex;
//   position: relative;
//   //   display: flex;
//   //   align-items: center;
// }
// :deep(.t-badge--dot),
// :deep(.t-badge--circle),
// :deep(.t-badge--round) {
//   position: relative;
// }

:deep(.t-badge) {
  margin-left: auto;
  margin-right: 8px;

  .t-badge--circle {
    background-color: #da2d19;
    color: #fff;
  }
}

.empty {
  padding: 12px;
  background: #f1f2f5;
  height: calc(100% - 20px) !important;
  border-left: 1px solid #e3e6eb;
  width: 100%;
  background-color: #fff;

  .nodepan {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    height: 100%;
    width: 100%;
    background: #fff;

    img {
      width: 200px;
      height: 200px;
    }

    div {
      margin-top: 8px;
      height: 24px;
      font-size: 16px;

      font-weight: 700;
      text-align: center;
      color: #13161b;
      line-height: 24px;
    }
  }
}

.tree-item-num {
  color: var(--magenta-kyy_color_magenta_acitve, #e5398c);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.left-mu {
  width: 240px;
  height: 100%;

  align-items: flex-start;
  // gap: 4px;
  flex-shrink: 0;
  border-right: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  background: var(--bg-kyy-color-bg-light, #fff);

  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .top {
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    overflow-y: overlay;
    padding-bottom: 12px;

    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
      // background-color: red;
      background-color: rgba(255, 255, 255, 0);
    }

    /*定义滚动条轨道 内阴影+圆角*/
    &::-webkit-scrollbar-track {
      // box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      // border-radius: 10px;
      background-color: rgba(255, 255, 255, 0);
    }

    /*定义滑块 内阴影+圆角*/
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
      background-color: #d5dbe4;
    }
  }

  .bottom {
    border-top: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
    height: 40px;
    width: 100%;
    display: flex;
    align-items: center;
    padding-left: 16px;
    display: flex;
    gap: 8px;

    .iconpeople {
      font-size: 20px;
      color: #828da5;
      width: 20px;
      height: 20px;
    }

    .text {
      color: var(--text-kyy-color-text-2, #516082) !important;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .mu-header {
    display: flex;
    width: 208px;
    height: 48px;
    padding: 0px 8px 0px 12px;
    align-items: center;
    color: var(--text-kyy-color-text-1, #1a2139);
    margin-bottom: 8px;
    cursor: pointer;
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */

    .mu-text {
      margin: 0 8px;
      width: 132px;
    }

    .mu-tree {
      display: flex;
      align-items: center;
    }

    .iconarrowdown {
      font-size: 20px;
      color: #828da5;
    }
  }

  .mu-item {
    display: flex;
    width: 208px;
    height: 48px;
    padding: 0px 8px 0px 40px;
    align-items: center;
    border-radius: 4px;
    margin-bottom: 8px;
    transition: all 0.15s linear;
    cursor: pointer;

    .text {
      color: var(--text-kyy-color-text-1, #1a2139);

      /* kyy_fontSize_3/bold */
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      /* 150% */
      width: 127px;
    }

    .count {
      display: flex;
      height: 20px;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      color: var(--magenta-kyy-color-magenta-acitve, #e5398c);
      margin-left: 8px;
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .mu-item:hover {
    border-radius: 4px;

    background: var(--bg-kyy-color-bg-list-hover, #f3f6fa);
    transition: all 0.15s linear;
  }

  .mu-item-active {
    border-radius: 4px;
    background: var(--bg-kyy-color-bg-list-foucs, #e1eaff) !important;
  }

  .icon-color {
    width: 20px;
    height: 20px;
  }
}

.ringkol {
  width: 100%;
}
</style>
