/* eslint-disable vue/one-component-per-file */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import Cropper from 'cropperjs';
// import { Upload as TUpload } from 'tdesign-vue-next';
import type { CSSProperties } from 'vue';
import { useResizeObserver } from '@vueuse/core';
import { useTippy, directive as tippy } from 'vue-tippy';
// import { Tooltip } from 'tdesign-vue-next';
import debounce from 'lodash/debounce';
import { ref, unref, computed, PropType, onMounted, onUnmounted, defineComponent } from 'vue';
import { longpress } from '@/directives/longpress';
// import { downloadByBase64 } from "@/views/square/utils/download";

type Options = Cropper.Options;
type Nullable<T> = T | null;
type ElRef<T extends HTMLElement = HTMLDivElement> = Nullable<T>;

const defaultOptions: Options = {
  aspectRatio: 1,
  zoomable: true,
  zoomOnTouch: true,
  zoomOnWheel: true,
  cropBoxMovable: true,
  cropBoxResizable: true,
  toggleDragModeOnDblclick: true,
  autoCrop: true,
  background: true,
  highlight: true,
  center: true,
  responsive: true,
  restore: true,
  checkCrossOrigin: true,
  checkOrientation: true,
  scalable: true,
  modal: true,
  guides: true,
  movable: true,
  rotatable: true,
};

const props = {
  src: { type: String, required: true },
  alt: { type: String },
  circled: { type: Boolean, default: false },
  // 椭圆
  ellipse: { type: Boolean, default: false },
  realTimePreview: { type: Boolean, default: true },
  height: { type: [String, Number], default: '360px' },
  crossorigin: {
    type: String as PropType<'' | 'anonymous' | 'use-credentials' | undefined>,
    default: undefined,
  },
  imageStyle: { type: Object as PropType<CSSProperties>, default: () => ({}) },
  options: { type: Object as PropType<Options>, default: () => ({}) },
};

export default defineComponent({
  name: 'ImgCropper',
  props,
  emits: ['readied', 'cropper', 'error', 'croppering'],
  setup(props, { attrs, emit }) {
    const tippyElRef = ref<ElRef<HTMLImageElement>>();
    const imgElRef = ref<ElRef<HTMLImageElement>>();
    const cropper = ref<Nullable<Cropper>>();
    const isReady = ref(false);
    const imgBase64 = ref();
    const inCircled = ref(props.circled);
    const isEllipse = ref(props.ellipse);
    const inSrc = ref(props.src);
    let scaleX = 1;
    let scaleY = 1;

    const debounceRealTimeCroppered = debounce(realTimeCroppered, 80);

    const getImageStyle = computed(
      (): CSSProperties => ({
        height: props.height,
        maxWidth: '100%',
        ...props.imageStyle,
      }),
    );

    const getClass = computed(() => [
      attrs.class,
      {
        're-circled': inCircled.value,
      },
    ]);

    const iconClass = computed(() => [
      'flex',
      'p-[6px]',
      'h-[30px]',
      'w-[30px]',
      'outline-none',
      'rounded-[4px]',
      'cursor-pointer',
      'hover:bg-[rgba(0,0,0,0.06)]',
    ].join(' '));

    const getWrapperStyle = computed((): CSSProperties => ({ height: `${`${props.height}`.replace(/px/, '')}px` }));

    onMounted(init);

    onUnmounted(() => {
      cropper.value?.destroy();
    });

    useResizeObserver(tippyElRef, () => {
      handCropper('reset');
    });

    async function init() {
      const imgEl = unref(imgElRef);
      if (!imgEl) return;
      cropper.value = new Cropper(imgEl, {
        ...defaultOptions,
        ready: () => {
          isReady.value = true;
          realTimeCroppered();
          setTimeout(() => emit('readied', cropper.value));
        },
        crop() {
          debounceRealTimeCroppered();
        },
        zoom() {
          debounceRealTimeCroppered();
        },
        cropmove() {
          debounceRealTimeCroppered();
        },
        ...props.options,
      });
    }

    function realTimeCroppered() {
      props.realTimePreview && croppered();
    }

    function croppered() {
      if (!cropper.value) return;
      emit('croppering');
      const canvas = inCircled.value || isEllipse.value ? getRoundedCanvas() : cropper.value.getCroppedCanvas();
      if (!canvas) return;

      // https://developer.mozilla.org/zh-CN/docs/Web/API/HTMLCanvasElement/toBlob
      canvas.toBlob((blob) => {
        if (!blob) return;
        const fileReader: FileReader = new FileReader();
        fileReader.readAsDataURL(blob);
        fileReader.onloadend = (e) => {
          if (!e.target?.result || !blob) return;
          imgBase64.value = e.target.result;
          emit('cropper', {
            base64: e.target.result,
            blob,
            info: { size: blob.size, ...cropper.value.getData() },
          });
        };
        fileReader.onerror = () => {
          emit('error');
        };
      });
    }
    function getRoundedCanvas() {
      const sourceCanvas = cropper.value!.getCroppedCanvas();
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d')!;
      const width = sourceCanvas?.width;
      const height = sourceCanvas?.height;
      if (!width || !height) {
        return;
      }
      canvas.width = width;
      canvas.height = height;
      context.imageSmoothingEnabled = true;
      context.drawImage(sourceCanvas, 0, 0, width, height);
      context.globalCompositeOperation = 'destination-in';

      context.beginPath();
      // 增加椭圆
      console.error(width, height);

      if (isEllipse.value) {
        const { aspectRatio } = props.options;
        // 计算椭圆半径，确保完全覆盖画布
        const radiusX = width / 2; // 水平半径等于画布宽度的一半
        const radiusY = radiusX / aspectRatio; // 垂直半径根据比例计算

        context.ellipse(width / 2, height / 2, radiusX, radiusY, 0, 0, 2 * Math.PI, true);
      } else {
        context.arc(width / 2, height / 2, Math.min(width, height) / 2, 0, 2 * Math.PI, true);
      }

      context.fill();
      return canvas;
    }

    function handCropper(event: string, arg?: number | Array<number>) {
      if (event === 'scaleX') {
        arg = scaleX === -1 ? 1 : -1;
        scaleX = arg;
      }
      if (event === 'scaleY') {
        arg = scaleY === -1 ? 1 : -1;
        scaleY = arg;
      }
      arg && Array.isArray(arg) ? cropper.value?.[event]?.(...arg) : cropper.value?.[event]?.(arg);
    }

    /* function beforeUpload(file) {
      const reader = new FileReader();
      reader.readAsDataURL(file.raw);
      inSrc.value = "";
      reader.onload = (e) => {
        inSrc.value = e.target?.result as string;
      };
      reader.onloadend = () => {
        init();
      };
      return false;
    } */

    const menuContent = defineComponent({
      directives: {
        tippy,
        longpress,
      },
      setup() {
        // const sourceCanvas = cropper.value!.getCroppedCanvas();
        return () => (
          <div class="flex flex-wrap w-[60px] justify-between">
            {/* <TUpload
              accept="image/*"
              show-file-list={false}
              before-upload={beforeUpload}
            >
                          <i
              class={`i-svg-cropper-upload ${iconClass.value}`}
              v-tippy={{
                content: "上传",
                placement: "left-start"
              }}
            />
            </TUpload>
            <i
              class={`i-svg-cropper-download ${iconClass.value}`}
              v-tippy={{
                content: "下载",
                placement: "right-start"
              }}
              onClick={() => downloadByBase64(imgBase64.value, {
                width: sourceCanvas.width,
                height: sourceCanvas.height,
                title: "cropping.png",
              })}
            />
            <i
              class={`i-svg-cropper-change ${iconClass.value}`}
              v-tippy={{
                content: "圆形、矩形裁剪",
                placement: "left-start"
              }}
              onClick={() => {
                inCircled.value = !inCircled.value;
                realTimeCroppered();
              }}
            /> */}
            <div
              class={iconClass.value}
              v-tippy={{
                content: '上移（可长按）',
                placement: 'left-start',
              }}
              v-longpress={[() => handCropper('move', [0, -10]), '0:100']}
            >
              <i class="i-svg-cropper-arrow-up text-18" />
            </div>

            <div
              class={iconClass.value}
              v-tippy={{
                content: '下移（可长按）',
                placement: 'right-start',
              }}
              v-longpress={[() => handCropper('move', [0, 10]), '0:100']}
            >
              <i class="i-svg-cropper-arrow-down text-18" />
            </div>
            <div
              class={iconClass.value}
              v-tippy={{
                content: '左移（可长按）',
                placement: 'left-start',
              }}
              v-longpress={[() => handCropper('move', [-10, 0]), '0:100']}
            >
              <i class="i-svg-cropper-arrow-left text-18" />
            </div>
            <div
              class={iconClass.value}
              v-tippy={{
                content: '右移（可长按）',
                placement: 'right-start',
              }}
              v-longpress={[() => handCropper('move', [10, 0]), '0:100']}
            >
              <i class="i-svg-cropper-arrow-right text-18" />
            </div>
            <div
              class={iconClass.value}
              v-tippy={{
                content: '水平翻转',
                placement: 'left-start',
              }}
              onClick={() => handCropper('scaleX', -1)}
            >
              <i class="i-svg-cropper-arrow-h text-18" />
            </div>
            <div
              class={iconClass.value}
              v-tippy={{
                content: '垂直翻转',
                placement: 'right-start',
              }}
              onClick={() => handCropper('scaleY', -1)}
            >
              <i class="i-svg-cropper-arrow-v text-18" />
            </div>
            <div
              class={iconClass.value}
              v-tippy={{
                content: '逆时针旋转',
                placement: 'left-start',
              }}
              onClick={() => handCropper('rotate', -45)}
            >
              <i class="i-svg-cropper-rotate-left text-18" />
            </div>
            <div
              class={iconClass.value}
              v-tippy={{
                content: '顺时针旋转',
                placement: 'right-start',
              }}
              onClick={() => handCropper('rotate', 45)}
            >
              <i class="i-svg-cropper-rotate-right text-18" />
            </div>
            <div
              class={iconClass.value}
              v-tippy={{
                content: '放大（可长按）',
                placement: 'left-start',
              }}
              v-longpress={[() => handCropper('zoom', 0.1), '0:100']}
            >
              <i class="i-svg-cropper-search-plus text-18" />
            </div>
            <div
              class={iconClass.value}
              v-tippy={{
                content: '缩小（可长按）',
                placement: 'right-start',
              }}
              v-longpress={[() => handCropper('zoom', -0.1), '0:100']}
            >
              <i class="i-svg-cropper-search-minus text-18" />
            </div>
            <div
              class={iconClass.value}
              v-tippy={{
                content: '重置',
                placement: 'right-start',
              }}
              onClick={() => handCropper('reset')}
            >
              <i class="i-svg-cropper-reload text-18" />
            </div>
          </div>
        );
      },
    });

    function onContextmenu(event) {
      event.preventDefault();

      const { show, setProps } = useTippy(tippyElRef, {
        content: menuContent,
        arrow: false,
        theme: 'light',
        trigger: 'manual',
        interactive: true,
        appendTo: 'parent',
        // hideOnClick: false,
        animation: 'perspective',
        placement: 'bottom-end',
      });

      setProps({
        getReferenceClientRect: () => ({
          width: 0,
          height: 0,
          top: event.clientY,
          bottom: event.clientY,
          left: event.clientX,
          right: event.clientX,
        }),
      });

      show();
    }

    return {
      inSrc,
      props,
      imgElRef,
      tippyElRef,
      getClass,
      getWrapperStyle,
      getImageStyle,
      isReady,
      croppered,
      onContextmenu,
      cropper,
      handCropper,
    };
  },

  render() {
    const { inSrc, isReady, getClass, getImageStyle, onContextmenu, getWrapperStyle } = this;
    const { alt, crossorigin } = this.props;

    return inSrc ? (
      <div
        // @ts-ignore
        ref="tippyElRef"
        class={getClass}
        style={getWrapperStyle}
        onContextmenu={(event) => onContextmenu(event)}
      >
        <img
          v-show={isReady}
          // @ts-ignore
          ref="imgElRef"
          style={getImageStyle}
          src={inSrc}
          alt={alt}
          crossorigin={crossorigin}
        />
      </div>
    ) : null;
  },
});
