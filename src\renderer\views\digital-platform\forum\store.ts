import { OwnerType, PlatformType } from '@renderer/api/forum/models/forum';
import { CardInfo, AtMembers } from '@renderer/api/forum/models/user';
import { defineStore } from 'pinia';
import to from 'await-to-js';
import { getOwnerId, getSelfCards, getConfig, getTeamsMember } from '@renderer/api/forum/user';
import { loadUnreadStats } from '@renderer/api/forum/notice';
import { MessagePlugin } from 'tdesign-vue-next';
import { set } from 'lodash';
import { HEADER_FORUM_KEY } from '@renderer/constants/localKey';
import { getOpenid } from '@renderer/utils/auth';
import { getPlatformType } from './utils/business';
import { useDigitalPlatformStore } from '../store/digital-platform-store';

/**
 * 论坛状态
 */
interface State {
  /** 组织id */
  teamId: string;
  /** 平台类型 */
  platformType: PlatformType;
  /** 所有者id */
  ownerId: string;
  /** 当前身份卡 */
  currCard: CardInfo | null;
  /** 身份卡列表 */
  cardList: CardInfo[];
  /** 帖子详情维护的当前所有者（因为可切换） */
  postOwner: CardInfo | null;
  unreadData: any;
  forumConfig: any;
  activeNav: string;
  /** 用于刷新列表中的用户备注 */
  refreshRemark: { cardId: string; remark: string };
  allMember: AtMembers;
}
let currentTeamId = '';

export const useForumStore = defineStore('forum', {
  state: (): State => ({
    teamId: '',
    platformType: null,
    ownerId: '',
    currCard: null,
    cardList: [],
    postOwner: null,
    unreadData: {},
    forumConfig: {},
    activeNav: 'forumHome',
    refreshRemark: { cardId: '', remark: '' },
    allMember: { ptMember: [], staffMember: [] },
  }),

  getters: {
  },

  actions: {
    // 核心数据预加载
    async preload(teamId: string) {
      const digitalStore = useDigitalPlatformStore();
      console.info('forumStore.preload', this.teamId, teamId, this.activeNav);

      this.teamId = teamId !== undefined ? teamId : currentTeamId || this.teamId;
      if (!currentTeamId) {
        currentTeamId = this.teamId;
      }

      this.platformType = digitalStore.platformType;
      if (!this.platformType) {
        await this.getPlatformType();
      }
      await this.getCardList();
      await this.getOwnerId(this.currCard?.card);
      this.teamId && this.getAllMembers();
    },

    // 初始化数据
    async init() {
      /**
       * 如果当前团队id和论坛团队id不一致，则重置论坛状态
       * fix: https://www.tapd.cn/69781318/bugtrace/bugs/view/1169781318001044586
       */
      if (currentTeamId && currentTeamId !== this.teamId) {
        currentTeamId = this.teamId;
        this.activeNav = 'forumHome';
      }
      this.getUnreadStats();
      // this.loadConfig();
    },

    // 获取论坛配置信息
    setKeyValue({ key, value }) {
      set(this, key, value);
    },

    /**
     * @deprecated
     * 获取论坛配置信息
     */
    async loadConfig() {
      const [err, data] = await to(getConfig(this.teamId));
      if (err || !data) {
        return null;
      }
      this.forumConfig = data?.data?.data;
    },

    // 获取消息红点
    async getUnreadStats() {
      const [err, data] = await to(loadUnreadStats({
        team_id: this.teamId,
      }));
      if (err || !data) {
        console.error('获取消息红点失败');
        return null;
      }
      this.unreadData = data?.data?.data;
    },

    // 获取平台类型
    async getPlatformType() {
      const [err, platformType] = await to(getPlatformType(this.teamId));
      if (err || !platformType) {
        console.error('获取平台类型失败');
        return null;
      }

      this.platformType = platformType;
      return platformType;
    },

    // 获取所有者id
    async getOwnerId(card: string) {
      const [err, res] = await to(getOwnerId({
        ownerType: OwnerType.DigitalPlatform,
        teamId: this.teamId,
        cardId: card,
        appUuid: this.platformType,
      }));

      if (err) {
        console.log(err);
        const { code, message } = err.data;
        if (code === 2001) {
          MessagePlugin.error('切换失败，暂无相关权限');
          return;
        }
        MessagePlugin.error(message);
        return false;
      }

      const { ownerId } = res.data.data;
      localStorage.setItem(HEADER_FORUM_KEY, ownerId);

      this.ownerId = ownerId;
      this.currCard = this.cardList.find((v: CardInfo) => v.card === card);

      // 同步到身份卡列表（每个身份卡对应一个 ownerId）
      this.currCard.id = ownerId;
      this.updateOwnerIdToList(ownerId, this.currCard?.card);
      return true;
    },

    // 更新身份卡列表中的 ownerId
    updateOwnerIdToList(id: string, card?: CardInfo) {
      const index = this.cardList.findIndex((v) => v.card === card);
      if (index > -1) {
        this.cardList[index].id = id;
      }
    },

    // 获取身份卡列表
    async getCardList() {
      const [err, res] = await to(getSelfCards(this.platformType));
      console.log(res, '热飒飒飒事情');

      if (err) {
        console.error('获取身份卡列表失败：', err);
        return;
      }
      // 高校1.0迭代发现接口data.data返回的是对象增加items数组
      const cardData = res.data.data as any;
      const cardList = Array.isArray(cardData) ? cardData : cardData.items || [];
      this.cardList = cardList.map((v: CardInfo) => ({
        ...v,
        _flagText: { staff_platform: '平台', staff: '员工' }[v.flag] || '访客',
      }));

      // 维持之前选中的身份卡
      const idx = this.cardList.findIndex((v) => v.card === this.currCard?.card);
      this.currCard = this.cardList[idx > -1 ? idx : 0];
      this.ownerId = this.currCard?.id || this.ownerId;
    },

    // 切换身份卡
    async switchCard(card: string) {
      const valid = await this.getOwnerId(card);
      if (!valid) return;

      this.currCard = this.cardList.find((v: CardInfo) => v.card === card);
      MessagePlugin.success('切换成功');
    },

    async getAllMembers() {
      this.allMember = { ptMember: [], staffMember: [] };
      const [err, res] = await to(getTeamsMember(this.teamId));
      if (err) {
        console.error('获取成员列表失败：', err);
        return;
      }
      const myOpenId = getOpenid();
      const { pt_staff_list = [], staff_list = [] } = res.data?.data;
      const ptMember = pt_staff_list?.filter((item) => item.openid !== myOpenId);
      const staffMember = staff_list?.filter((item) => item.openid !== myOpenId);
      console.log('-012', ptMember, staffMember);
      this.allMember = { ptMember, staffMember };
    },
  },
});
