export const getNowDigitalType = () => {
  const url = window.location.href;
  if (url.includes("digital_platform_member")) {
    return "member";
  }else if (url.includes("digital_platform_cbd")) {
    return "cbd";
  }else if (url.includes("digital_platform_association")) {
    return "association";
  } else if (url.includes("digital_platform_uni")) {
    return "uni";
  }
  return "politics";
};

export const checkContentWidth = (width: number, content: string) => {
  // 创建一个临时的div元素
  const tempDiv = document.createElement("div");
  // 设置临时div的样式，使其不可见且不影响布局
  tempDiv.style.position = "absolute";
  tempDiv.style.visibility = "hidden";
  tempDiv.style.width = "auto";
  tempDiv.style.whiteSpace = "nowrap"; // 确保文本不会换行
  tempDiv.style.overflow = "hidden"; // 隐藏溢出的内容
  // 将传入的content设置为临时div的文本内容
  tempDiv.textContent = content;
  // 将临时div添加到文档中
  document.body.appendChild(tempDiv);
  // 获取div的宽度
  const contentWidth = tempDiv.clientWidth;
  // 从文档中移除临时div
  document.body.removeChild(tempDiv);
  // 判断content的宽度是否大于传入的width
  return contentWidth > width;
};
