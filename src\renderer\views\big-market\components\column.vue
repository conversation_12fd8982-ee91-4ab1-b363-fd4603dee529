<template>
    <div class="colbox"  :class="{
      colfixed: props.colfixdHeader,
      between2: props.classify,
    }">
      <div v-if="!columns.length" class="colboxex colboxmar2" > 
        <div class="sort-box">
          <div class="btn-item" :class="{ active: props.orderBy === 1 }" @click="clickSort(1)">附近</div>
          <div class="btn-item" :class="{ active: props.orderBy === 2 }" @click="clickSort(2)">访问量</div>
          <div class="btn-item" :class="{ active: props.orderBy === 3 }" @click="clickSort(3)">粉丝数</div>
          <div class="btn-item" :class="{ active: props.orderBy === 4 }" @click="clickSort(4)">点赞数</div>
        </div>
        <div v-if="props.classify" class="searchi-box" @keyup.enter="handleSearch">
          <t-input
            placeholder="搜索广场号"
            v-model="keyword"
            @clear="handleSearch"
            clearable
            style="width: 360px"
            :maxlength="30"
          >
            <template #prefix-icon>
              <iconpark-icon name="iconsearch" class="iconbusiness" />
            </template>
          </t-input>
          <div class="sbtn" @click="handleSearch">搜索</div>
        </div>
      </div>

      <div v-else class="colboxex" :class="{
      colboxmar: props.colfixdHeader,
    }">
        <!-- 专栏栏 -->
        <!-- 左滚动箭头 -->
        <div ref="leftArrowRef" class="scroll-arrow left-arrow" @click="scrollColumns('left')">
          <iconpark-icon name="iconarrowright-a960jjbo" class="iconbusiness" style="transform: rotate(180deg)" />
        </div>
        <div class="column-scroll" ref="columnScrollRef">
          <div class="column-list" ref="columnListRef">
            <div
              v-for="column in columns"
              :key="column.id"
              class="column-item"
              :class="{
                active: activeColumn === column.id,
              }"
              @click="switchColumn(column.id, $event.currentTarget)"
            >
              <img class="cicon" :src="column.icon" alt="" />
              <span>{{ column.name }}</span>
            </div>
          </div>
        </div>
        <!-- 右滚动箭头 -->
        <div ref="rightArrowRef" class="scroll-arrow right-arrow" @click="scrollColumns('right')">
          <iconpark-icon name="iconarrowright-a960jjbo" class="iconbusiness" />
        </div>

        <!-- 排序项 -->
        <div class="sort-container">
          <div
            class="sort-current"
            @mouseleave="mouseleaveRun"
            @mouseenter="mouseMoreRun"
            :class="{ currentacv: showSortDropdown }"
          >
            <span>{{ sortOptions.find((o) => o.id === props.orderBy)?.name }}</span>
            <iconpark-icon name="iconarrowdown" class="iconbusiness" />
          </div>
          <div class="sort-dropdown" v-if="showSortDropdown" @mouseleave="mouseleaveRun" @mouseenter="mouseMoreRun">
            <div
              v-for="option in sortOptions"
              :key="option.id"
              class="sort-option"
              :class="{ active: props.orderBy === option.id }"
              @click="switchSort(option.id)"
            >
              {{ option.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useWindowScroll } from '@vueuse/core';
import { listMarketColumns } from '../apis';
const props = defineProps({
  colfixdHeader: {
    type: Boolean,
    default: false,
  },
  orderBy: {
    type: Number,
    default: 1,
  },
  activeColumn: {
    type: String || Number,
    default: 0,
  },
  classify: {
    type: Boolean,
    default: false,
  },
  showCol: {
    type: Boolean,
    default: true,
  },
  keyword: {
    type: String,
    default: '',
  },
});
const columns = ref([]);
const keyword = ref(props.keyword);
watch(
  () => props.keyword,
  (newVal) => {
    keyword.value = newVal;
  },
);
// 排序选项
const sortOptions = ref([
  { id: 1, name: '附近' },
  { id: 2, name: '访问量' },
  { id: 3, name: '粉丝数' },
  { id: 4, name: '点赞数' },
]);

// 状态管理
const defaultMode = ref(true);
const isSortContainerHovered = ref(false);
const isDropdownHovered = ref(false);
const showSortDropdown = computed(() => isSortContainerHovered.value || isDropdownHovered.value);
const columnScrollRef = ref<HTMLDivElement>(null);
const columnListRef = ref<HTMLDivElement>(null);
const leftArrowRef = ref<HTMLDivElement>(null);
const rightArrowRef = ref<HTMLDivElement>(null);
const { y: scrollY } = useWindowScroll();
const emits = defineEmits(['clickSort', 'switchColumn', 'handleSearch']);
// 滚动控制
const scrollStep = ref(3); // 每次滚动的专栏数量
const showArrows = ref(false);
const arrowSize = 32; // 箭头大小(px)
const rightArrowClicked = ref(false);

const defData = {
  columnPosition: 0,
  icon: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/icon_like_fill.png',
  id: 0,
  isUnlimited: true,
  name: '推荐专栏',
  sequence: 10,
  showTimeEnd: '0',
  showTimeStart: '0',
};

onMounted(async () => {
  // if (leftArrowRef.value) {
  //   leftArrowRef.value.style.display = 'none';
  // }
  if (!props.classify && props.showCol) {
    const res = await listMarketColumns();
    console.log(res, 'marketColumns');
    columns.value = res.data.data?.marketColumns || [];
    if (columns.value.length) {
      columns.value.unshift(defData);
      setTimeout(() => {
        checkShowArrows();
      }, 500);
    }
  }
  window.addEventListener('resize', checkShowArrows);
  columnScrollRef.value?.addEventListener('scroll', () => requestAnimationFrame(updateArrowVisibility), {
    passive: true,
  });
});

// 切换专栏
const switchColumn = (columnId: string, element?: HTMLElement) => {
  // activeColumn.value = columnId;
  emits('switchColumn', columnId);
  // 切换专栏时滚动到顶部
  if (columnScrollRef.value) {
    columnScrollRef.value.scrollTop = 0;
  }
  rightArrowClicked.value = true;
  // 点击专栏时的滚动逻辑
  if (element && columnScrollRef.value && columnListRef.value) {
    const container = columnScrollRef.value;
    const containerRect = container.getBoundingClientRect();
    const elementRect = element.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const elementLeft = elementRect.left - containerRect.left;
    const elementRight = containerRect.right - elementRect.right;
    const columnItems = columnListRef.value.querySelectorAll('.column-item');
    const columnWidth = columnItems.length > 0 ? (columnItems[0] as HTMLElement).offsetWidth : 200; // 默认宽度
    const scrollAmount = columnWidth * 3; // 移动2-3个专栏宽度

    // 判断是否需要滚动
    if (container.scrollWidth > containerWidth) {
      // 元素更靠右，向左滚动
      if (elementLeft > elementRight) {
        container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      }
      // 元素更靠左，向右滚动
      else if (elementRight > elementLeft) {
        container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
      }
    }
  }
  // TODO: 实际项目中添加数据加载逻辑
  setTimeout(() => {
    updateArrowVisibility();
  }, 300);
};

// 切换排序
const switchSort = (sortId: string) => {
  emits('clickSort', sortId);
  showSortDropdown.value = false;
  // TODO: 实际项目中添加数据排序逻辑
};

// 计算是否显示滚动箭头
const checkShowArrows = () => {
  if (columnScrollRef.value && columnListRef.value) {
    const containerWidth = columnScrollRef.value.clientWidth;
    const contentWidth = columnListRef.value.scrollWidth;
    showArrows.value = contentWidth > containerWidth + 10; // 增加10px容差
  }
  updateArrowVisibility();
};

// 更新箭头可见性
const updateArrowVisibility = () => {
  if (columnScrollRef.value && leftArrowRef.value && rightArrowRef.value) {
    const { scrollLeft, scrollWidth, clientWidth } = columnScrollRef.value;
    console.log(scrollLeft, scrollWidth, clientWidth);
    leftArrowRef.value.style.display = rightArrowClicked.value && scrollLeft > 1 ? 'flex' : 'none';
    rightArrowRef.value.style.display = scrollLeft + clientWidth + 1 < scrollWidth ? 'flex' : 'none';
  }
};

// 滚动指定数量的专栏
const scrollColumns = (direction: 'left' | 'right') => {
  if (direction === 'right') rightArrowClicked.value = true;
  if (columnScrollRef.value && columnListRef.value) {
    const columnItems = columnListRef.value.querySelectorAll('.column-item');
    if (columnItems.length === 0) return;

    // 使用实际专栏宽度计算滚动距离
    const columnWidth = (columnItems[0] as HTMLElement).offsetWidth;
    const scrollDistance = columnWidth * scrollStep.value;
    const currentScroll = columnScrollRef.value.scrollLeft;
    const maxScrollLeft = columnListRef.value.scrollWidth - columnScrollRef.value.clientWidth;

    // 计算并限制目标滚动位置在有效范围内
    const targetLeft =
      direction === 'left'
        ? Math.max(currentScroll - scrollDistance, 0)
        : Math.min(currentScroll + scrollDistance, maxScrollLeft);

    // 仅当目标位置变化时执行滚动
    if (Math.abs(targetLeft - currentScroll) > 1) {
      columnScrollRef.value.scrollTo({ left: targetLeft, behavior: 'smooth' });
    }
    setTimeout(() => {
      updateArrowVisibility();
    }, 300);
    // requestAnimationFrame(updateArrowVisibility);
    // requestAnimationFrame(updateArrowVisibility);
  }
};

onUnmounted(() => {
  window.removeEventListener('resize', checkShowArrows);
  columnScrollRef.value?.removeEventListener('scroll', updateArrowVisibility);
});

// 滚动到当前选中的专栏
const scrollToActiveColumn = () => {
  if (columnScrollRef.value) {
    const activeElement = columnScrollRef.value.querySelector('.column-item.active');
    if (activeElement) {
      columnScrollRef.value.scrollLeft = (activeElement as HTMLElement).offsetLeft - 100;
    }
  }
};
const sortMouseleave = () => {
  setTimeout(() => {
    isSortContainerHovered.value = false;
  }, 500);
};
const dropdownMouseleave = () => {
  isDropdownHovered.value = false;
  setTimeout(() => {
    isSortContainerHovered.value = false;
    isDropdownHovered.value = false;
  }, 500);
};
const Dropdownenter = () => {};

const setTimeoutTemp = ref(null);
const mouseleaveRun = () => {
  setTimeoutTemp.value = setTimeout(() => {
    isSortContainerHovered.value = false;
  }, 300);
};
const mouseMoreRun = () => {
  console.log('mouseMoreRun');
  clearTimeout(setTimeoutTemp.value);
  isSortContainerHovered.value = true;
};

const clickSort = (type: number) => {
  emits('clickSort', type);
};
const handleSearch = () => {
  emits('handleSearch', keyword.value);
};
</script>

<style lang="less" scoped>
.colbox {
  background: #fff;
  position: relative;
}
.colboxex{
  display: flex;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  position: relative;
}
/* 专栏栏样式 */
.column-scroll {
  flex: 1;
  overflow-x: auto;
  scrollbar-width: none;
  /* 隐藏滚动条 */
  display: flex;

  /* 为箭头留出空间 */
  &::-webkit-scrollbar {
    display: none;
    /* 隐藏滚动条 */
  }
}

.colfixed {
  margin-bottom: 16px;
  position: fixed;
  top: 112px;
  z-index: 100;
  left: 0px;
  padding: 0 16px 16px 16px;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: center;
  width: 100vw;
}
.colboxmar{
  width: 1184px;
}
.colboxmar2{
  width: 1184px;
  justify-content: space-between;
}
/* 滚动箭头样式 */
.scroll-arrow {
  top: 50%;
  width: 28px;
  height: 36px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s;
  display: flex;
  padding: 8px 4px;
  align-items: center;
  gap: 4px;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
}

.scroll-arrow.left {
  display: none;
}

.scroll-arrow:hover {
  background: var(--tagBG-kyy_color_tagBg_warning, #ffe5d1);
  color: var(--warning-kyy_color_warning_default, #fc7c14);
  .iconbusiness {
    color: var(--warning-kyy_color_warning_default, #fc7c14);
  }
}

.icon-arrow {
  width: 16px;
  height: 16px;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTAgMTRIMlYySDExVjE0WiIgZmlsbD0iIzMzMyIvPjxwYXRoIGQ9Ik02IDZoNnYxSDVWNiIvPjwvc3ZnPg==')
    no-repeat center;
}

.left-arrow {
  position: absolute;
  left: 0px;
  top: 0px;
  // display: none;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  box-shadow: 4px 0px 8px 0px rgba(133, 60, 0, 0.12);
}
.left-arrow2 {
  position: absolute;
  left: 0px;
  top: 0px;
  // display: none;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  box-shadow: 4px 0px 8px 0px rgba(133, 60, 0, 0.12);
}
.left-arrow2,
.left-arrow:hover {
  border: 1px solid var(--warning-kyy_color_warning_default, #fc7c14);
  background: var(--tagBG-kyy_color_tagBg_warning, #ffe5d1);
}
.right-arrow {
  position: absolute;
  top: 0px;
  right: 92px;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);

  /* 橙色投影-左 */
  box-shadow: -4px 0px 8px 0px rgba(232, 35, 0, 0.12);
  .icon-arrow {
    transform: rotate(180deg);
  }
}
.right-arrow:hover {
  border-radius: 8px;
  border: 1px solid var(--warning-kyy_color_warning_default, #fc7c14);
  background: var(--tagBG-kyy_color_tagBg_warning, #ffe5d1);
  /* 橙色投影-左 */
  box-shadow: -4px 0px 8px 0px rgba(232, 35, 0, 0.12);
}

.column-list {
  display: flex;
  gap: 4px;
  white-space: nowrap;
}

.column-item {
  padding: 7px 8px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-kyy_color_text_2, #516082);
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;

  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);

  .cicon {
    width: 20px;
    height: 20px;
  }

  &:hover {
    border-radius: 8px;
    background: var(--tagBG-kyy_color_tagBg_warning, #ffe5d1);
  }

  &.active {
    border-radius: 8px;
    background: var(--tagBG-kyy_color_tagBg_warning, #ffe5d1);
    color: var(--warning-kyy_color_warning_default, #fc7c14);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }

  &.recommend {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 6px;
      height: 6px;
      background: #ff4d4f;
      border-radius: 50%;
    }
  }
}

.recommend-tag {
  margin-left: 4px;
  font-size: 10px;
  padding: 0 2px;
  border-radius: 2px;
  background: #ff4d4f;
  color: #fff;
}

/* 排序项样式 */
.sort-container {
  position: relative;
  width: 80px;
}

.sort-current {
  width: 66px;
  display: flex;
  align-items: center;
  justify-content: end;
  cursor: pointer;
  font-size: 14px;
  color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;

  /* 157.143% */
}
.sort-current:hover {
  color: var(--brand-kyy_color_brand_acitve, #707eff);
  text-align: center;

  /* kyy_fontSize_2/regular */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  .iconbusiness {
    color: var(--brand-kyy_color_brand_acitve, #707eff);
  }
}
.icon-arrow-down {
  width: 16px;
  height: 16px;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTAgMTFIMlYySDExVjExWiIgZmlsbD0iIzMzMyIvPjxwYXRoIGQ9Ik01IDZoNnYxSDRWNiIvPjwvc3ZnPg==')
    no-repeat center;
  transition: transform 0.2s;
}

.sort-dropdown {
  position: absolute;
  top: 28px;
  right: 0;
  cursor: pointer;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  display: inline-flex;
  padding: 4px;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  border-radius: var(--kyy_radius_dropdown_m, 8px);
  background: var(--kyy_color_dropdown_bg_default, #fff);

  /* kyy_shadow_m */
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
}

.sort-option {
  display: flex;
  height: 32px;
  min-width: 136px;
  min-height: 32px;
  max-height: 32px;
  padding: 0px 8px;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  border-radius: var(--kyy_radius_dropdown_s, 4px);
  background: var(--kyy_color_dropdown_bg_default, #fff);

  &:hover {
    border-radius: var(--kyy_radius_dropdown_s, 4px);
    background: var(--kyy_color_dropdown_bg_active, #e1eaff);
  }

  &.active {
    color: var(--kyy_color_dropdown_text_active, #4d5eff);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
}

.iconbusiness {
  font-size: 20px;
  color: #516082;
}

.btn-item {
  display: flex;
  padding: 4px 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  color: var(--text-kyy_color_text_2, #516082);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  cursor: pointer;
}

.btn-item:hover {
  background: var(--tagBG-kyy_color_tagBg_brand, #eaecff);
}

&.active {
  border-radius: 8px;
  background: var(--tagBG-kyy_color_tagBg_brand, #eaecff);
  color: var(--brand-kyy_color_brand_default, #4d5eff);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
}

.sort-box {
  display: flex;
  gap: 8px;
}
.between {
  justify-content: space-between;
}
.between2 {
  display: flex;
justify-content: center;
}
:deep(.colbox .t-input) {
  padding-right: 4px;
}
.sbtn {
  display: flex;
  height: 24px;
  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #eaecff);
  color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4d5eff);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  cursor: pointer;
}
.searchi-box {
  position: relative;
  .sbtn {
    position: absolute;
    right: 4px;
    top: 4px;
    z-index: 105;
  }
}
:deep(.searchi-box .t-input) {
  padding-right: 54px;
}
.currentacv {
  .iconbusiness {
    transform: rotate(180deg);
  }
}
.flexb {
  width: 100vw;
  display: flex;
  justify-content: center;
  background: #fff;
}
</style>
