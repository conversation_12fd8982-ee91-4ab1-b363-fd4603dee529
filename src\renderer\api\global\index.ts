import { ringkolRequest } from "@renderer/utils/apiRequest";
import { AxiosResponse } from "axios";
import { BaiduMapSuggestionParams } from './models/baiduMap';

// const BAIDU_REDIRECT = '/baidumap/';
const Api = {
  // baiduMap: `${BAIDU_REDIRECT}place/v2/suggestion`,
  baiduMap: '/global/v1/baidu/api/map',
  // ipLocation: `${BAIDU_REDIRECT}location/ip`,
};

const mapPath = Object.freeze({
  abroad: '/place_abroad/v1/search',
  suggestion: '/place/v2/suggestion',
});

// 地点输入提示
// https://lbsyun.baidu.com/faq/api?title=webapi/place-suggestion-api
export const baiduMapApi = (
  params: BaiduMapSuggestionParams,
  type: keyof typeof mapPath = 'suggestion'
): Promise<AxiosResponse> => ringkolRequest.post(Api.baiduMap, {
  path: mapPath[type],
  query: { ...params, output: 'json' },
});

// 普通IP定位
// export const baiduMapIPLocation = (params?: { ip?: string }): Promise<AxiosResponse> => request.get(Api.ipLocation, { params });
