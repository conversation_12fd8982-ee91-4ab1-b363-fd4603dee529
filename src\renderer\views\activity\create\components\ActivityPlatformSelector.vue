<template>
  <div v-if="readonly">{{ platforms.length ? platforms.map((platform) => platform.name).join('、') : '未选择' }}</div>
  <div v-else class="w-full px-12 py-4 border-1 border-solid border-[#D5DBE4] rounded-4">
    <div class="flex items-center gap-8 flex-wrap">
      <t-button
        class="bg-[#EAECFF] h-24 pl-8 pr-12"
        variant="outline"
        theme="primary"
        @click="openSelectDialog"
      >
        <img src="@renderer/assets/activity/icon_add_blue.svg" alt="">
        <span class="ml-2">添加</span>
      </t-button>

      <template v-if="platformIds.length">
        <div v-for="(platform) in platforms" :key="platform.teamId" class="h-24 px-8 py-2 rounded-4 bg-[#ECEFF5] flex items-center">
          <span class="ml-4 leading-20">{{ platform.name }}</span>
          <img
            class="ml-8 cursor-pointer"
            src="@renderer/assets/activity/icon_error.svg"
            alt=""
            @click="remove(platform)"
          >
        </div>
      </template>
    </div>

    <t-dialog
      attach="body"
      class="activity-platform-select-dialog"
      :header="false"
      :visible="visible"
      :close-btn="false"
      :footer="false"
    >
      <div class="p-24 flex items-center">
        <div class="text-16 text-[#1A2139] leading-24 font-600">选择数字平台</div>
      </div>

      <RScrollArea :max-height="408" type="always">
        <div class="px-24">
          <t-checkbox-group v-model="selectedIds" class="activity-platform-select-group">
            <t-checkbox
              v-for="option in options"
              :key="option.team_id"
              :label="option.team_name"
              :value="option.team_id"
            />
          </t-checkbox-group>
        </div>
      </RScrollArea>

      <div class="p-24 flex justify-between items-center">
        <div class="text-16 text-[#1A2139] leading-24">已选：{{ selectedIds.length }}</div>

        <div class="flex gap-8">
          <t-button class="w-80 h-32" theme="default" @click="visible = false">
            <span class="font-600">取消</span>
          </t-button>
          <t-button
            class="w-80 h-32"
            theme="primary"
            :disabled="selectedIds.length === 0"
            @click="submit"
          >
            <span class="font-600">确定</span>
          </t-button>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { RScrollArea } from '@rk/unitPark';

const props = defineProps({
  platforms: {
    type: Array,
    required: true,
  },
  options: {
    type: Array,
    default: () => [],
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:platforms', 'change']);

const platformIds = computed({
  get() {
    return props.platforms.map((platform) => (platform.teamId));
  },
  set(value) {
    emit('update:platforms', props.options.filter((item) => value.includes(item.team_id)).map((item) => ({
      teamId: item.team_id,
      name: item.team_name,
    })));
  },
});

const selectedIds = ref([]);

const visible = ref(false);

const openSelectDialog = () => {
  visible.value = true;

  selectedIds.value = platformIds.value;
};

const submit = () => {
  platformIds.value = selectedIds.value;

  visible.value = false;

  setTimeout(() => {
    selectedIds.value = [];
  }, 300);
};

const remove = async (platform) => {
  platformIds.value = platformIds.value.filter((item) => item !== platform.teamId);

  emit('change');
};
</script>

<style lang="less">
.activity-platform-select-dialog{
  .t-dialog {
    padding: 0;
    width: 440px;

    .t-dialog__body {
      padding: 0;

      .activity-platform-select-group{
        flex-direction: column;
        gap: 12px;

        .t-checkbox{
          align-items: flex-start;
          color: #1A2139;
          font-size: 14px;
          line-height: 22px;

          .t-checkbox__input{
            margin-top: 3px;
          }
        }
      }
    }
  }
}
</style>
