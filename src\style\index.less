@import "base";
@import "tdesign/index";

#app {
  margin: 0 auto;
  height: 100%;
  font-weight: normal;

  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
}

.van-cascader__option {
  span {
    color: #1A2139 !important;
  }
}

// pc端 固定尺寸展示
@media screen and (min-width: 500px) {
  #app {
    height: auto;
    min-height: 100vh;
    overflow: hidden;
  }

  #app>.containerPc {
    max-width: 375px;
    min-width: 375px;
    max-height: 734px;
    min-height: 734px;
    transform: translateY(32px);
  }

  #app>.home-container {
    max-height: 734px;
    min-height: 734px;
    height: auto;
  }

  #app>.container {}

  .area-container {
    max-width: 375px;
    max-height: 734px;


  }
}

.t-cascader__item-label {
  width: 90% !important;
  font-size: 14px;
  font-family: Microsoft YaHei, Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: left;
  color: #13161b;
}

.t-date-picker__panel .t-time-picker__panel,
.t-date-range-picker__panel .t-time-picker__panel {
  width: auto !important;
}

.cursor {
  user-select: none;
  cursor: pointer;
}

.t-select__list .t-select-option.t-is-selected {
  color: rgba(0, 0, 0, .9);
  background-color: transparent;
}

.line-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  display: -moz-box;
  -moz-line-clamp: 3;
  -moz-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

.line-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -moz-box;
  -moz-line-clamp: 2;
  -moz-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

.line-1 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
  // -webkit-line-clamp: 1;
  // -webkit-box-orient: vertical;
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 4px;
  // height: 2px;
  // background-color: #f5f5f5;
  background-color: #fff;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  // background-color: #e3e6eb;
  background-color: #fff;

}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #c8c8c8;
}


.t-table:not(.t-table--striped) .t-table__footer>tr {
  background: #fff !important;

  .t-table__row-full-element {
    padding-left: 15px !important;
  }
}

.t-table__empty-row {
  td {
    border-bottom: none !important;

  }
}

.clouddisk-home-table {
  z-index: 50;
}

//table
.t-table__header th {
  background: var(--kyy-color-table-hrading-bg, #E2E6F5) !important;
  border-right: 1px solid var(--border-kyy-color-border-white, #FFF);
  color: var(--kyy-color-table-hrading-text, #516082) !important;
  /* kyy_fontSize_2/bold */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 600 !important;
  line-height: 22px;
  /* 157.143% */
}

.t-table__header th:last-child {
  border-right: none !important;

}


.t-button--theme-primary {
  // background: var(--brand-kyy-color-brand-default, #4D5EFF);
}

.formDesign-form .t-form__label {
  color: var(--text-kyy-color-text-3, #828DA5);
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

// .t-input{
//   border: 1px solid var(--kyy_color_datePicker_border_lose_foucs, #D5DBE4);
// }

.t-button--variant-base.t-button--theme-primary {
  border-color: var(--color-button-primary-kyy-color-button-primary-bg-default, #4D5EFF);
  background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4D5EFF);
}

.t-button--variant-base.t-button--ghost.t-button--theme-primary {
  background: #fff;
}

.organizeDatas .t-dialog--default {
  padding: 24px;
  max-width: 374px;
  margin: 0 16px !important;
}

.organizeDatas .t-dialog__header-content {
  color: var(--kyy_color_modal_title, #1A2139);
  font-family: PingFang SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  /* 150% */
}

.t-input.t-is-readonly .t-input__inner {
  color: var(--text-kyy-color-text-5, #ACB3C0);
}

.t-select-input {

  // color: var(--text-kyy-color-text-5, #ACB3C0);
  .t-input.t-is-readonly .t-input__inner {
    color: var(--input-kyy-color-input-text-completed, #1A2139);
  }
}

.t-input__inner::placeholder {
  color: var(--select-kyy-color-select-text-default, #ACB3C0);
}

a {
  color: #4D5EFF;
}

.detailRequired-label .t-form__label {
  display: none;
}

.content-box-top .t-form__label {
  margin-bottom: 8px;
}

.content-box-top .t-form__label--top {
  min-height: 20px;
}


.flipbook {
  .bounding-box {
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.3) !important;
  }

  .click-to-flip {
    display: none;
  }
}

.ebook-desc-dialog .t-dialog {
  padding: 24px;
}

.ebook-mobile .viewport {
  width: 100% !important;
  // height: calc(100vh - 118px) !important;
}

// .flipbook .page {
//   background: #fff;
// }

// .flipbook .bounding-box {
//   background: #fff !important;
// }

// .ebook-mobile .bounding-box {
//   width: 100% !important;
//   height: calc(100vh - 200px) !important;
// }

// .ebook-mobile .flipbook img {
//   height: calc(100vh - 200px) !important;
// }
.page[data-v-5c04eecf],
.polygon[data-v-5c04eecf] {
  background: #fff;
}

.t-input__extra {
  position: unset;
}

.t-input__inner {
  color: var(--input-kyy_color_input_text_completed, #1A2139) !important;
}

.t-input {
  border-color: #D5DBE4;
}

.niche-detail-editors {
  .ql-container {
    border-top: none !important;
    position: unset !important;
  }

  .ql-editor {
    overflow-y: inherit !important;
    padding: 0 !important;
  }
}


@import "cropperjs/dist/cropper.css";
// @import "tippy";

.d-upload-avatar {
  .t-dialog__body {
    overflow: hidden ;
  }
}
.ql-container .ql-editor img {
  max-width: 100% !important;
  // width: auto!important;
}

.details-editor-special {
  font-size: 18px;
  color: #1a2139;
    h1 {
    font-size: 24px !important;
    color: #1a2139 !important;
    line-height: 32px;
    font-weight: 600;
  }
  h2 {
    font-size: 22px !important;
    color: #1a2139 !important;
    line-height: 30px;
    font-weight: 600;
  }
  h3 {
    font-size: 20px !important;
    color: #1a2139 !important;
    line-height: 28px;
    font-weight: 600;
  }
}



input[type="file"]::-webkit-media-capture {
  display: none;
}

.niche-detail-readOnly .link-bubble{
  display: none !important;
}


.t-popup__content {
  max-width: 280px;

}

.details-editor-special div.ql-editor p{
  font-size: 18px;
  color: #1a2139;
}

