<template>
  <div>
    <div
      v-if="organizationCertInfo.certStatus === OrganizationCertStatus.Uncertified"
      class="top-status"
    >
      <div class="top-status--box">
        <iconpark-icon name="iconattention" fill="#fb6f21" size="24" />
        <p class="top-status--text">{{ $t("square.square.goAuthTip") }}</p>
        <div class="top-status--btn" @click="handleVertify">{{ $t("square.square.goAuth") }}</div>
      </div>
    </div>
    <div class="page-content">
      <t-form
        ref="formRef"
        :rules="FORM_RULES"
        :data="formData"
        label-align="top"
        show-error-message
      >
        <div class="title">{{ $t('square.square.squareInfoSetting') }}</div>
        <div class="name-wrap">
          <div class="uploader" @click="uploadAvatarRef.open(formData.avatar)">
            <KyyAvatar
              class="avatar"
              avatar-size="88px"
              :image-url="formData.avatar"
              :user-name="squareName"
              shape="circle"
            />
            <!--<div class="icon-wrap">
              <iconpark-icon name="camera" class="icon" />
            </div>-->
          </div>
          <UploadAvatar ref="uploadAvatarRef" @confirm="uploadConfirm" />

          <t-form-item required-mark class="w-full">
            <template #label>
              <div class="inline-flex flex-items-center">
                {{ $t('square.name') }}
                <template v-if="nameRecords.length">
                  <t-button
                    theme="default"
                    size="small"
                    class="mx-4"
                    @click="openNameRecord"
                  >
                    修改记录
                  </t-button>
                  <iconpark-icon v-if="nameAuditReject" name="iconattention" style="color: #D54941" />
                </template>
              </div>
            </template>

            <template v-if="expiredNameTip">
              {{ squareName }}
              <t-tooltip :content="expiredNameTip"><iconpark-icon name="iconattention" style="color: #D54941" /></t-tooltip>
            </template>
            <template v-else>
              <div v-if="!!pendingName || organizationProfile?.nameUpdateTimesThisYear >= 2" class="t-input__wrap">
                <div class="t-input t-is-disabled gap-10">
                  <span class="color-text-1">{{ squareName }}</span>
                  <t-link
                    v-if="pendingName"
                    theme="primary"
                    hover="color"
                    @click="revokeUpdateName"
                  >
                    撤销修改
                  </t-link>
                  <t-tag v-if="pendingName" theme="warning" variant="light">审核中</t-tag>
                </div>
              </div>
              <t-input
                v-else
                v-model="squareName"
                :status="nameInputStatus"
                :maxlength="30"
                :placeholder="$t('square.enterPlease')"
                @change="nameInputChange"
                @click="() => !pendingName && toUpdateName()"
              />
            </template>
          </t-form-item>

          <UpdateNameRecord ref="nameRecordRef" />
        </div>
        <div v-if="updating" class="tips ml-72 mt-4 text-12" :class="{ 'color-danger': nameInputStatus === 'error' }">
          长度限制4-30个字符，一年内可修改两次。
          <template v-if="organizationProfile?.nameUpdateTimesThisYear">今年已修改{{ organizationProfile?.nameUpdateTimesThisYear }}次</template>
        </div>
        <UpdateInput
          v-if="canUpdateName"
          confirm-name="提交审核"
          :disabled="!squareName?.trim()"
          @reset="resetUpdateName(); nameInputStatus = undefined"
          @confirm="confirmUpdateName"
        />

        <t-form-item :label="$t('square.intro')" name="intro" class="w-full">
          <t-textarea
            v-model="formData.intro"
            class="intro-textarea"
            :maxlength="200"
            :placeholder="$t('square.enterPlease')"
            clearable
            autosize
            @focus="toUpdateField('intro')"
          />
        </t-form-item>
        <EditActions v-if="canFieldUpdate('intro')" @cancel="resetField" @submit="updateField" />

        <t-row class="row-container" :gutter="16">
          <t-col :span="6">
            <t-form-item :label="$t('square.square.squareId')">
              <t-input :value="store.squareInfo?.square?.pid" disabled />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item :label="$t('square.square.feeExpiredAt')">
              <t-input :value="feeExpiredAtFormat" disabled placeholder="" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="封面图（用于组织主页中展示）">
              <div class="flex gap-8 flex-items-end">
                <t-image
                  v-if="store.squareInfo.square.coverImage"
                  :src="store.squareInfo.square.coverImage"
                  lazy
                  class="cover"
                />

                <t-button
                  variant="outline"
                  theme="primary"
                  class="plain"
                  @click="cropperRef.open(imgSelected)"
                >
                  <template #icon><iconpark-icon name="iconupload" /></template>
                  {{ store.squareInfo.square.coverImage ? '重新上传': '上传' }}
                </t-button>

                <t-button
                  variant="outline"
                  theme="primary"
                  class="plain"
                  @click="orgPreviewVisible = true"
                >
                  <template #icon><iconpark-icon name="iconpreciewopen" /></template>预览
                </t-button>
              </div>

              <CropperDialog ref="cropperRef" @confirm="cropperConfirm" />

              <OrgPreview v-if="orgPreviewVisible" v-model="orgPreviewVisible" :square-data="store.squareInfo" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item :label="$t('square.square.qrcodeEntry')">
              <t-button
                variant="outline"
                theme="primary"
                class="plain"
                @click="qrCodeDownload.open()"
              >
                <template #icon><iconpark-icon name="icondimensional" /></template>
                {{ $t('square.square.downloadQrcode') }}
              </t-button>
            </t-form-item>
            <QRCodeDownload ref="qrCodeDownload" />
          </t-col>

          <t-col :span="6">
            <t-form-item class="location-wrap">
              <template #label>
                <div class="flex">
                  <span class="mr-8">组织所在位置</span>
                  <t-link theme="primary" hover="color" @click="switchAddressMode">{{ isManualAddress ? '地图定位': '手动输入' }}</t-link>
                  <t-tooltip :content="isManualAddress ? '使用地图定位，用户查看时显示地图位置，并可进行导航': '手动输入时，用户查看时显示地址文本内容'">
                    <iconpark-icon name="iconattention" />
                  </t-tooltip>
                  <div v-if="isManualAddress" class="flex flex-1 justify-end cursor-pointer">
                    <iconpark-icon name="iconclosecircle" class="icon remove" @click="removeLocationTip" />
                  </div>
                </div>
              </template>

              <div v-if="isManualAddress" class="w-full">
                <RegionSelect v-model="regionCodes" :clearable="false" @change="onRegionChange" />

                <t-input
                  v-model="formData.address.address"
                  :maxlength="255"
                  class="mt-8"
                  @focus="toUpdateField('address.address')"
                />
                <EditActions v-if="canFieldUpdate('address.address')" @cancel="resetField" @submit="updateField" />
              </div>
              <BaiduMapCard
                v-else
                :location="formData?.address?.location"
                removable
                @confirm="mapConfirm"
                @remove="removeLocationTip"
              />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="门牌号">
              <t-input v-model="formData.houseNumber" :maxlength="50" @focus="toUpdateField('houseNumber')" />
            </t-form-item>
            <EditActions v-if="canFieldUpdate('houseNumber')" @cancel="resetField" @submit="updateField" />
          </t-col>
          <t-col :span="6">
            <t-form-item name="phoneNumbers" :label="$t('square.csPhone')">
              <div class="phone-number">
                <div v-for="(item, index) in formData.phoneNumbers" :key="index" class="phone-item">
                  <t-select
                    v-model="item.countryCode"
                    v-replace-svg
                    :options="countryCodeOpts"
                    class="w-88"
                  />
                  <t-input
                    v-model="formData.phoneNumbers[index].number"
                    class="flex-1"
                    :maxlength="20"
                    @focus="toUpdateField('phoneNumbers')"
                  />
                  <iconpark-icon
                    v-if="index === 0"
                    name="iconaddpend"
                    class="icon add"
                    @click="addNumber"
                  />
                  <iconpark-icon
                    v-if="index > 0"
                    name="iconclosecircle"
                    class="icon remove"
                    @click="delNumber(index)"
                  />
                </div>
              </div>
            </t-form-item>
            <EditActions
              v-if="canFieldUpdate('phoneNumbers')"
              class="!-mt-12"
              @cancel="resetField"
              @submit="updateField"
            />
          </t-col>
        </t-row>

        <t-divider class="mt-0!" />
        <div class="title mb-24">关联组织</div>
        <OrgConnect />
        <t-divider />

        <div class="title mb-24">认证信息</div>
        <t-row :gutter="16" class="row-container">
          <t-col :span="6">
            <t-form-item class="cert-name-wrap">
              <template #label>
                {{ $t('square.square.orgName') }}
                <t-tooltip
                  v-if="organizationCertInfo.certStatus === OrganizationCertStatus.Expired"
                  :content="$t('square.square.certExpired')"
                  :overlay-style="{ width: '270px' }"
                  placement="right"
                  show-arrow
                >
                  <!-- <SvgIcon name="square-error-circle-filled" class="error-icon" /> -->
                  <i class="i-svg:error-circle-filled text-24" />
                </t-tooltip>
              </template>
              <div class="t-input__wrap">
                <div class="t-input t-is-disabled">
                  <span class="t-input__inner mr-4" style="flex: initial">{{ organizationCertInfo.name }}</span>
                  <SquareCertIcon :square="{ certStatus: organizationCertInfo.certStatus, squareType: store.squareInfo?.square?.squareType }" />
                </div>
              </div>
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item :label="$t('square.square.orgType')">
              <t-input v-model="typeText" disabled placeholder="" />
            </t-form-item>
          </t-col>
          <t-col v-if="visibleInfo.showOrgCode" :span="6">
            <t-form-item :label="$t('square.square.orgCode')">
              <t-input v-model="organizationCertInfo.code" disabled />
            </t-form-item>
          </t-col>
          <t-col v-if="visibleInfo.showValidity" :span="6">
            <t-form-item :label="$t('square.expiredAt')">
              <t-input v-model="expiredAtFormat" disabled placeholder="" />
            </t-form-item>
          </t-col>
        </t-row>
        <t-form-item v-if="visibleInfo.showBusiness" :label="$t('square.square.business')" class="mt-0! mb-16!">
          <t-input v-model="organizationCertInfo.business" disabled placeholder="" />
        </t-form-item>
        <t-row v-if="visibleInfo.showIndustry" :gutter="16">
          <t-col :span="6">
            <t-form-item>
              <template #label>
                {{ $t('square.square.industry') }}
                <t-link
                  theme="primary"
                  hover="underline"
                  class="ml-12"
                  @click="openUrl"
                >
                  {{ $t('square.goSetting') }}
                </t-link>
              </template>
              <t-input v-model="organizationCertInfo.industry" disabled placeholder="" />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>

    <!--
    <BusinessAssociation
      v-model:visible="businessIndexVisible"
      :team-id="store.squareInfo?.organizationProfile?.teamId"
      :org-type="store.squareInfo?.organizationProfile?.type"
      :region="store.squareInfo?.square?.regionCode"
      @back="onVertifyBack"
      @success="verifySuccess"
    />
    <BusinessNormal
      v-model:visible="businessVisible"
      :team-id="store.squareInfo?.organizationProfile?.teamId"
      :org-type="store.squareInfo?.organizationProfile?.type"
      :region="store.squareInfo?.square?.regionCode"
      @back="onVertifyBack"
      @success="verifySuccess"
    />
    <BusinessGovernment
      v-model:visible="businessGovernmentVisible"
      :team-id="store.squareInfo?.organizationProfile?.teamId"
      :org-type="store.squareInfo?.organizationProfile?.type"
      :region="store.squareInfo?.square?.regionCode"
      @back="onVertifyBack"
      @success="verifySuccess"
    />
    -->

    <orgAuth
      v-model:visible="orgAuthVisible"
      show-type="edit"
      :region="store.squareInfo?.square?.regionCode"
      :team-id="store.squareInfo?.organizationProfile?.teamId"
      :org-type="store.squareInfo?.organizationProfile?.type"
      @back="onVertifyBack"
      @success="verifySuccess"
    />

    <Tricks :offset="{ x: '-32', y: '-40' }" uuid="组织广场-广场号信息" />
  </div>
</template>

<script setup lang="ts" name="AccountInfo">
import { computed, inject, ref } from 'vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import { AxiosError, AxiosResponse } from 'axios';
import moment from 'moment';
import { useI18n } from 'vue-i18n';
import get from 'lodash/get';
import set from 'lodash/set';
import { getTeamCertStatus } from '@renderer/api/square/common';
import orgAuth from '@renderer/components/orgAuth/orgAuth.vue';
import BaiduMapCard from '@renderer/components/common/map/BaiduMapCard.vue';
import { updateOrganization } from '@/api/square/home';
import KyyAvatar from '@/components/kyy-avatar/index.vue';
import { DATE_FORMAT, DATE_TIME_FORMAT, formatDate } from '@/utils/date';
import { useErrorTip } from '@/views/square/hooks/tip';
import UploadAvatar from '@/components/common/UploadAvatar.vue';
import { useSquareStore } from '@/views/square/store/square';
import QRCodeDownload from '@/views/square/account-info/components/QRCodeDownload.vue';
import { OrganizationCertInfo, OrganizationProfileUpdateParams, Type } from '@/api/square/models/square';
import { OrganizationCertStatus, OrganizationType } from '@/api/square/enums';
import { jumpWeb } from '@/views/contacts/utils';
import RegionSelect from '@/components/common/region/RegionSelect.vue';
import { regionToArray, regionToObject } from '@/components/common/region/utils';
import { Region } from '@/api/square/models/common';
import UpdateInput from '@/views/square/account-info/components/UpdateInput.vue';
import { useUpdateName, useVisibleInfo } from '@/views/square/hooks/squareInfo';
import { getNameUpdateRecords, revokeNameUpdate, saveSquareCover } from '@/api/square/square';
import OrgPreview from '@/views/square/account-info/components/OrgPreview.vue';
import UpdateNameRecord from '@/views/square/account-info/components/UpdateNameRecord.vue';
import { AuditStatus } from '@/api/square/models/report';
import { errReasonMap, ORG_DEFAULT_AVATAR, ROUTE_REFRESH_INJECT_KEY, squareTypeMap } from '@/views/square/constant';
import CropperDialog from '@/views/square/components/CropperDialog.vue';
import { useExpiredNameTip } from '@/views/square/utils/business';
import SquareCertIcon from '@/views/square/components/SquareCertIcon.vue';
// import BusinessAssociation from '@/components/orgAuth/business.vue';
// import BusinessNormal from '@/components/orgAuth/index.vue';
// import BusinessGovernment from '@/components/orgAuth/government.vue';
import OrgConnect from '@/views/square/account-info/components/OrgConnect.vue';
import EditActions from '@/views/square/account-info/components/EditActions.vue';
import { onMountedOrActivated } from '@/hooks/onMountedOrActivated';
import { ErrorResponseReason } from '../enums';

const { expiredNameTip } = useExpiredNameTip();
const store = useSquareStore();
const { t } = useI18n();
const routeRefresh = inject(ROUTE_REFRESH_INJECT_KEY);

// 个人资料
const formRef = ref(null);
const organizationCertInfo = ref<OrganizationCertInfo>({} as OrganizationCertInfo);
const organizationProfile = computed(() => store.squareInfo?.organizationProfile);

const visibleInfo = computed(() => {
  const { squareType, regionCode } = store.squareInfo.square;
  return useVisibleInfo(squareType, regionCode);
});

const formData = ref<OrganizationProfileUpdateParams>({
  orgType: OrganizationType.Enterprise,
  teamId: '',
  avatar: '',
  intro: '',
  circleRingkolIntro: '',
  phoneNumbers: [{ countryCode: '+86', number: '' }],
  address: {
    region: {} as Region,
    location: {},
  },
});

const FORM_RULES = {
  phoneNumbers: [{
    validator: (val) => {
      if (!val) return { result: true };

      const errLen = val.map((v) => v.number).filter((v) => !!v && !/^[0-9-]*$/.test(v)).length;
      if (errLen > 0) return { result: false, message: '只能输入数字、“-”', type: 'error' };
      return { result: true, type: 'success' };
    },
    trigger: 'change',
  }],
};
const nameRecordRef = ref(null);
const qrCodeDownload = ref(null);
const orgPreviewVisible = ref(false);
// const businessIndexVisible = ref(false);
// const businessVisible = ref(false);
// const businessGovernmentVisible = ref(false);
const orgAuthVisible = ref(false);

const verifySuccess = () => {
  // 屏蔽多余提示
  // MessagePlugin.success(t('square.square.openTip4'));
};
const onVertifyBack = (isEdit) => {
  !isEdit && routeRefresh();
};

const handleVertify = async () => {
  const steamId = store.squareInfo.organizationProfile.teamId;
  const [err, res] = await to(getTeamCertStatus(steamId));
  if (err) return;
  if (!res.data.certifiable) {
    const confirmDia = DialogPlugin.confirm({
      header: t('square.tip'),
      body: t('square.square.openTip3'),
      theme: 'warning',
      cancelBtn: null,
      closeBtn: false,
      onConfirm: async () => {
        confirmDia.destroy();
      },
    });
    return;
  }

  orgAuthVisible.value = true;

  // // 商协会与普通类型调用不同的组件 新增政企类型
  // const orgType = store.squareInfo.organizationProfile.type;
  // if (orgType === SquareType.BusinessAssociation) {
  //   businessIndexVisible.value = true;
  // } else if (orgType === SquareType.Government) {
  //   businessGovernmentVisible.value = true;
  // } else {
  //   businessVisible.value = true;
  // }
};

// 头像上传
const uploadAvatarRef = ref(null);
const uploadConfirm = (url) => {
  formData.value.avatar = url;
  submit('avatar');
};

// 更新名称
const squareName = ref('');
const nameInputStatus = ref();
const pendingName = computed(() => store.squareInfo?.organizationProfile?.pendingName);
const {
  updating, updateName, toUpdateName, resetUpdateName,
} = useUpdateName(squareName);
const canUpdateName = computed(() => updating.value && organizationProfile.value?.nameUpdateTimesThisYear < 2);
const confirmUpdateName = async () => {
  if (!squareName.value) {
    await MessagePlugin.warning('请输入广场号名称');
    return;
  }
  if (squareName.value.length < 4) {
    nameInputStatus.value = 'error';
    return;
  }
  if (store.squareInfo.square.name === squareName.value) {
    await MessagePlugin.warning('该名称与原来一致，请重新输入');
    return;
  }

  await updateName();
};

const nameInputChange = () => {
  if (squareName.value.length >= 4) {
    nameInputStatus.value = undefined;
  }
};

const showTip = useErrorTip();

// 封面图片裁剪
const imgSelected = ref(store.squareInfo.square.coverImage);
const cropperRef = ref(null);
const cropperConfirm = async (url: string) => {
  imgSelected.value = url;
  const [err] = await to<AxiosResponse, AxiosError<{ reason: string }>>(saveSquareCover({ url }));
  if (err) {
    showTip(err);
    return;
  }
  store.squareInfo.square.coverImage = url;
  await MessagePlugin.success('上传封面图成功');
};

const isManualAddress = computed(() => formData.value?.address?.type === Type.Input);
const switchAddressMode = () => {
  if (isManualAddress.value) {
    formData.value.address.type = Type.Gps;
    submit('address');
    return;
  }
  if (!formData.value?.address?.location?.latLng) {
    formData.value.address.type = Type.Input;
    submit('address');
    return;
  }

  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: '是否同步已选择的地址到输入框？',
    theme: 'warning',
    onConfirm: () => {
      confirmDia.destroy();
      formData.value.address.type = Type.Input;
      formData.value.address.address = formData.value.address.location.address || formData.value.address.address;
      submit('address');
    },
    onClose: () => {
      confirmDia.hide();
      formData.value.address.type = Type.Input;
      submit('address');
    },
  });
};

// 区域选择
const regionCodes = ref<string[]>([]);
const regionNames = ref<string[]>([]);
const onRegionChange = (val, ctx, names) => {
  regionNames.value = names;
  formData.value.address.region = regionCodes.value.length ? regionToObject({ names: regionNames.value, codes: regionCodes.value }) : undefined;
  submit('address.location.address');
  updatingField.value = '';
};

const inputChangeConfirm = () => new Promise((resolve, reject) => {
  const confirmDia = DialogPlugin.confirm({
    header: '修改确认',
    body: '是否保存当前修改内容？',
    theme: 'info',
    closeBtn: false,
    onConfirm: () => {
      confirmDia.destroy();
      resolve(true);
    },
    onClose: () => {
      confirmDia.destroy();
      resetField();
      reject();
    },
  });
});

// 更改名称 TODO 重构：提取 EditableInput 组件
const updatingField = ref('');
let nameBak = '';

// 开始编辑
const toUpdateField = async (field: string) => {
  if (updatingField.value === field) return;

  // 提示是否保存当前修改的内容
  const lastFieldChanged = updatingField.value && nameBak !== get(formData.value, updatingField.value);
  if (lastFieldChanged) {
    const sure = await inputChangeConfirm();
    if (sure) {
      const valid = await updateField();
      if (!valid) return;
    }
  }

  const val = get(formData.value, field);
  nameBak = typeof val === 'object' ? JSON.parse(JSON.stringify(val)) : val;
  updatingField.value = field;
};

// 取消编辑
const resetField = () => {
  if (updatingField.value === 'phoneNumbers') {
    formData.value = JSON.parse(JSON.stringify(profileBak));
    // 很诡异，下面的代码执行后，profileBack 还是会跟 formData 的 phoneNumbers 建立引用关系
    // set(formData.value, updatingField.value, (profileBak?.phoneNumbers || []).slice());
    formRef.value.validate([updatingField.value]);
  } else {
    set(formData.value, updatingField.value, nameBak);
  }

  updatingField.value = '';
};

// 确定保存
const updateField = async () => {
  const valid = await formRef.value.validate();
  console.log(valid);
  if (typeof valid !== 'boolean') return false;

  await submit(updatingField.value);
  updatingField.value = '';
  return true;
};

// 指定输入框是否可更新
const canFieldUpdate = (field: string) => {
  if (updatingField.value !== field) return false;

  if (field === 'phoneNumbers' && profileBak?.phoneNumbers) {
    return JSON.stringify(profileBak.phoneNumbers) !== JSON.stringify(get(formData.value, field));
  }

  return nameBak !== get(formData.value, field);
};

// 撤销修改名称修改
const revokeUpdateName = () => {
  const confirmDia = DialogPlugin.confirm({
    header: '确定撤销修改？',
    body: '撤销后，可重新修改名称',
    confirmBtn: '确定',
    cancelBtn: { content: t('square.action.cancel'), theme: 'default', variant: 'outline' },
    closeOnOverlayClick: false,
    theme: 'info',
    onConfirm: async () => {
      confirmDia.destroy();

      const [err] = await to<AxiosResponse, AxiosError<{ reason: string }>>(revokeNameUpdate());
      if (err) {
        const errReason = err.response.data.reason;
        if (errReason === ErrorResponseReason.StatusChangeIrrevocable) {
          const dia = DialogPlugin.confirm({
            header: '状态已更新，无法撤销',
            confirmBtn: '确定',
            cancelBtn: null,
            closeOnOverlayClick: false,
            theme: 'info',
            onConfirm: () => {
              store.getSquaresList();
              getOrganizationInfo();
              dia.destroy();
            },
          });

          return;
        }

        await MessagePlugin.warning(errReasonMap[errReason]);
        return;
      }

      await MessagePlugin.success('撤销成功');
      await getOrganizationInfo();
      await getNameRecord();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

// 备份，用于恢复数据
let profileBak: typeof formData.value;
const showAuditTip = useErrorTip();

const submit = async (updateField: string) => {
  const profile = {
    ...formData.value,
    phoneNumbers: formData.value.phoneNumbers.filter((v) => !!v.number),
  };
  const [err] = await to<AxiosResponse, AxiosError<{ reason: string }>>(updateOrganization({ profile }));
  if (err) {
    showAuditTip(err, () => {
      set(formData, updateField, get(profileBak, updateField));
      // formData[updateField] = profileBak[updateField];
    });
    return;
  }

  // 更新广场号信息
  store.squareInfo.square.avatar = formData.value.avatar;
  store.squareInfo.square.intro = formData.value.intro;
  store.squareInfo.square.name = squareName.value;

  profileBak = JSON.parse(JSON.stringify(formData.value));
  await MessagePlugin.success('修改成功');
};

const typeText = ref('');
const expiredAtFormat = ref('');
const feeExpiredAtFormat = ref('');

const getOrganizationInfo = async () => {
  await store.getSquareInfo();

  const { square, organizationProfile } = store.squareInfo;
  feeExpiredAtFormat.value = formatDate(organizationProfile.expiredAt, DATE_TIME_FORMAT);

  formData.value = organizationProfile;
  organizationCertInfo.value = store.squareInfo.organizationCertInfo;
  const expiredAt = moment(organizationCertInfo.value.expiredAt);
  expiredAtFormat.value = expiredAt.isValid() ? expiredAt.format(DATE_FORMAT) : t('square.square.alwaysValid');

  typeText.value = squareTypeMap[square.squareType];

  // 基础信息
  squareName.value = organizationProfile.pendingName || square.name;
  formData.value.avatar = square.avatar || ORG_DEFAULT_AVATAR;
  formData.value.intro = square.intro;
  formData.value.circleRingkolIntro = square.circleRingkolIntro;

  // 地址初始化
  const { address, phoneNumbers } = organizationProfile;
  if (address) {
    if (address?.region) {
      const { codes, names } = regionToArray(address.region);
      regionCodes.value = codes;
      regionNames.value = names;
    }
    formData.value.address = address;
  } else {
    formData.value.address = {
      region: undefined,
      location: {},
      type: Type.Gps,
      address: '',
    };
  }

  // 初始化客服电话
  if (!phoneNumbers || !phoneNumbers.length) {
    formData.value.phoneNumbers = [{ countryCode: '+86', number: '' }];
  }

  profileBak = JSON.parse(JSON.stringify(formData.value));
};

const openUrl = () => {
  jumpWeb('/#/setting/setting-info/setting-info-detail', { teamId: store.squareInfo.organizationProfile.teamId });
};

const LOCAL_REJECT_RECORD = 'RECORD_LOCAL_NAME';
let localRecord: any;
try {
  localRecord = JSON.parse(localStorage.getItem(LOCAL_REJECT_RECORD));
} catch (e) { console.log(e); }

// 审核不通过显示红点后，点击就消失
const setNameAuditReject = (store = false) => {
  const lastItem = nameRecords.value[0];
  if (lastItem && lastItem.auditStatus === AuditStatus.Reject && localRecord?.id !== lastItem.id) {
    nameAuditReject.value = lastItem.auditStatus === AuditStatus.Reject;
    if (store) localStorage.setItem(LOCAL_REJECT_RECORD, JSON.stringify(lastItem));
  }
};

// 获取修改名称审核状态
const nameAuditReject = ref(false);
const nameRecords = ref([]);
const getNameRecord = async () => {
  const [err, res] = await to(getNameUpdateRecords());
  if (err) return;

  nameRecords.value = res.data.items;

  setNameAuditReject();
  const lastItem = nameRecords.value[0];
  if (lastItem && lastItem.auditStatus === AuditStatus.Reject && localRecord?.id !== lastItem.id) {
    nameAuditReject.value = lastItem.auditStatus === AuditStatus.Reject;
  }
};

// 查看修改记录
const openNameRecord = () => {
  nameRecordRef.value.open();
  setNameAuditReject(true);
  nameAuditReject.value = false;
};

const mapConfirm = (data) => {
  formData.value.address.location = data;
  submit('address');
};

const removeLocationTip = () => {
  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: '确定删除该组织所在位置信息？',
    theme: 'warning',
    onConfirm: async () => {
      confirmDia.destroy();
      if (isManualAddress.value) {
        formData.value.address.region = undefined;
        formData.value.address.address = '';
      } else {
        formData.value.address.location = {};
      }
      await submit('address');
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

const countryCodeOpts = ref([
  { label: '+86', value: '+86' },
  { label: '+853', value: '+853' },
  { label: '+852', value: '+852' },
]);
const addNumber = () => {
  formData.value.phoneNumbers.push({ countryCode: '+86', number: '' });
};
const delNumber = (index) => {
  formData.value.phoneNumbers.splice(index, 1);
  updatingField.value = 'phoneNumbers';
  formRef.value.validate(['phoneNumbers']);
};

onMountedOrActivated(async () => {
  await getOrganizationInfo();
  await getNameRecord();
});
</script>

<style lang="less" scoped>
.page-content {
  margin: 12px 16px 12px 16px;
  padding: 24px;
  overflow-y: auto !important;
  border-radius: 8px;
  border: 1px solid var(--lingke-gray-1, #ECEFF5);
  background-color: #fff;
}

.top-status {
  width: 100%;
  padding: 12px 0;
  margin-bottom: -12px;
  position: sticky;
  top: 0;
  z-index: 999;
  background-color: #F3F7FE;
  &--box {
    height: 40px;
    margin: 0 12px;
    padding: 0 12px;
    display: flex;
    align-items: center;
    border-radius: 8px;
    background-color: #ffe1cc;
  }
  &--text {
    color: #2D2D2D;
    margin-left: 8px;
  }
  &--btn {
    color: #0616f8;
    margin-left: 12px;
    position: relative;
    cursor: pointer;
    &:hover::before {
      width: 100%;
    }
    &::before {
      content: '';
      position: absolute;
      bottom: 4px;
      width: 0;
      height: 1px;
      background-color: #0616f8;
    }
  }
}

:deep(.t-form) {
  padding-bottom: 40px;
  > .t-form__item {
    margin-top: 24px;
  }
  .t-form__label {
    float: none;
    margin-bottom: 8px;
  }
  .t-is-disabled {
    background-color: transparent !important;
    .t-input__inner {
      color: var(--input-kyy_color_input_text_disabled, #ACB3C0) !important;
    }
  }
}

:deep(.t-textarea) {
  min-height: 82px !important;
  max-height: 185px !important;
}
:deep(.t-textarea__inner) {
  min-height: 82px !important;
  max-height: 185px !important;
  resize: none;
}

:deep(.intro-textarea .t-textarea__info_wrapper_align) {
  pointer-events: none;
}

.title {
  font-size: 16px;
  line-height: 24px;
  color: var(--text-kyy-color-text-1, #1A2139);
}

.ty-2 {
  transform: translateY(2px);
}

.name-wrap {
  display: flex;
  margin: 24px 0;
}

.tips {
  margin-top: -28px;
  margin-left: 112px;
}
.cert-name-wrap {
  .icon {
    width: 20px;
    height: 20px;
  }
  .error-icon {
    width: 24px;
    height: 24px;
    color: #d4251a;
    margin-bottom: -6px;
  }
}

.uploader {
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  width: 88px;
  height: 88px;
  border-radius: 999px;
  background: #eee;
  margin-right: 24px;
  cursor: pointer;
  .avatar {
    border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
    border-radius: 50%;
  }
  .icon-wrap {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    border: 1px solid #ffffff;
    border-radius: 50%;
    background: #1e60da;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    .icon {
      font-size: 12px;
      color: #fff;
    }
  }
}

.date {
  width: 100%;
  padding-right: 16px;
}

.list {
  :deep(.t-list-item) {
    padding: 8px 16px;
  }
  :deep(.t-list-item__meta-title) {
    font-weight: 400;
    font-size: 14px;
    color: #13161b;
  }
  :deep(.t-list-item__meta-description) {
    color: #717376;
  }
}

:deep(.t-textarea__info_wrapper) {
  margin-top: -20px;
  margin-right: 10px;
}

.location-wrap {
  :deep(.t-form__label) {
    padding-right: 0;
  }
}

.phone-number {
  display: flex;
  flex-direction: column;
  width: 100%;
  .phone-item {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    .w-88 {
      width: 88px;
    }
  }
  .icon {
    font-size: 20px;
    cursor: pointer;
    &.add {
      color: #4D5EFF;
    }
    &.remove {
      color: #D54941;
    }
  }
}

.row-container {
  > .t-col {
    margin-bottom: 16px;
  }
}

.cover {
  /* @apply w-78 h-78 border-rd-8; */
  width: 78px;
  height: 78px;
  border-radius: 8px;
  border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
}
</style>
