import { followSearch, ids } from "../model/follow";
import { iam_srvRequest, client_orgRequest, ringkolRequestApi } from '@/utils/apiRequest'

export function followList(params?: followSearch, teamId?: string) {
  return ringkolRequestApi({
    method: "get",
    url: "/im/v1/friend/follow/listFriendFollows",
    params,
    headers: {
      teamId
    },
  });
}

export function innerCardsDetails(data: ids) {
  return client_orgRequest({
    method: "post",
    url: "/staffs/batch-details",
    data,
  });
}

export function platformCardsDetails(data: ids) {
  return client_orgRequest({
    method: "post",
    url: "/member/platform/detailByIds",
    data,
  });
}

export function outerCardsDetails(data: ids) {
  return client_orgRequest({
    method: "post",
    url: "/external/idCard/batch-details",
    data,
  });
}

export function personalCardsDetails(data: ids) {
  return iam_srvRequest({
    method: "post",
    url: "/v1/profiles/batch/search",
    data,
  });
}
