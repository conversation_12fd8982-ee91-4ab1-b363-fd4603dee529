<template>
  <AppCard>
    <AppCardHeader :theme="headerColorMap[sceneData?.scene]">
      {{ getTitle(sceneData?.scene, sceneData?.content?.title) }}
    </AppCardHeader>
    <AppCardBody>
      <template v-if="[SceneType.RECOMMEND_APPLICATION].includes(sceneData?.scene)">
        <div class="overflow-hidden mb-12">
          <AppCardText type="info" class="mb-4">广场号名称</AppCardText>
          <AppCardText class="flex-y-center! flex-1 ellipsis-1">
            <img :src="sceneData?.header?.team.Logo || BizUnion" class="w-20 h-20 rounded-full mr-4">
            {{ sceneData?.header?.team.name }}
          </AppCardText>
        </div>
        <div v-for="(item, index) in sceneData?.content?.body" :key="index" class="mb-12">
          <AppCardText type="info" class="text-label mb-4">
            {{ item.key }}
          </AppCardText>
          <AppCardText class="text-value ellipsis-2">{{ item.value }}</AppCardText>
        </div>
      </template>
      <template v-else-if="[SceneType.INFORMATION_REVIEW_INITIATE].includes(sceneData?.scene)">
        <div v-for="(item, index) in sceneData?.content?.body" :key="index" class="flex gap-16 overflow-hidden mb-8">
          <AppCardText type="info" class="text-label w-88">
            {{ item.key }}
          </AppCardText>
          <AppCardText class="flex-1 text-value ellipsis-2">{{ item.value }}</AppCardText>
        </div>
      </template>
      <template v-else>
        <AppCardText class="flex-1 text-value">{{ Array.isArray(sceneData.content?.body) ? sceneData.content?.body.join('') : sceneData.content?.body }}</AppCardText>
      </template>
      <div class="h-1 w-full mt-16 bg-[#ECEFF5]" />
    </AppCardBody>
    <AppCardFooter>
      <Button class="w-full fw-600! text-[#516082]!" variant="outline" @click="goToDetail">
        查看详情
      </Button>
    </AppCardFooter>
  </AppCard>
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue';
import { MessageToSave } from 'customTypes/message';
import { MessagePlugin, Button } from 'tdesign-vue-next';
import { AppCard, AppCardBody, AppCardHeader, AppCardFooter, AppCardText } from '@renderer/views/message/chat/MessageAppCard';
import { useChatExtendStore } from '@renderer/views/message/service/extend';
import { getReviewCheck, getInformationDetail } from '@renderer/api/spServer';
import { useChatActionStore } from '@renderer/views/message/service/actionStore';
import LynkerSDK from '@renderer/_jssdk';
import {
  getOpenid,
} from '@renderer/utils/auth';
import BizUnion from '@/assets/im/biz_union.png';

import { i18nt } from '@/i18n';
import { getTitle, headerColorMap, SceneType } from './constant';

const props = defineProps({
  msg: { type: Object as PropType<MessageToSave>, default: null },
});

const sceneData = computed(() => props.msg?.contentExtra?.data);
const goToDetail = async () => {
  console.log('🚀 ~ goToDetail ~ data:', sceneData.value.scene);
  useChatExtendStore().hideChatDialog();
  // 文旅资讯
  if ([SceneType.INFORMATION_REVIEW_INITIATE, SceneType.INFORMATION_REVIEW_APPROVED, SceneType.INFORMATION_REVIEW_REJECTED].includes(sceneData.value.scene)) {
    applyClick();
  }
  // 另可文旅申请
  // setTimeout(() => {
  //   cultureApplyClick();
  // }, 0);
};

// 文旅资讯申请
const applyClick = async () => {
  // 点击通过和拒绝，先校验文章是否存在，
  try {
    const { serviceId, reviewerCardId, teamId, publisherCardId } = sceneData.value?.extend || {};
    const params = { id: serviceId, 'me.openId': getOpenid() as string, 'me.cardId': reviewerCardId || publisherCardId, 'me.teamId': teamId, notError: true };
    if ([SceneType.INFORMATION_REVIEW_APPROVED, SceneType.INFORMATION_REVIEW_REJECTED].includes(sceneData.value.scene)) {
      const { data } = await getInformationDetail(params, teamId);
      const { code } = data;
      if (code === 15002) {
        return MessagePlugin.warning(i18nt('im.public.noCheckAuth'));
      }
      if (code === 15007) {
        return MessagePlugin.warning(i18nt('im.public.contentDeleted'));
      }
      if (code === 15011) {
        return MessagePlugin.warning(i18nt('im.public.ctRevoked'));
      }
      showDialog(serviceId, teamId, reviewerCardId || publisherCardId);
      return;
    }
    const res = await getReviewCheck(params);
    const result = res.data;
    console.log('await', res, result);
    const code = result?.code;
    // 申请去验证页面
    if ([SceneType.INFORMATION_REVIEW_INITIATE].includes(sceneData.value.scene)) {
      // 申请打开安全验证页, status 14002 无审核权限 ；14013资讯已被删除；14018 资讯已被撤回
      let status = result.data?.status;
      if (code === 15002) {
        status = 'StatusNoAuth';
      } else if (code === 15007) {
        status = 'StatusDeleted';
      } else if (code === 15011) {
        status = 'StatusRevocation';
      }
      const remark = status === 'StatusRejected' ? result.data?.reviewRemark : '';
      const param = { status, remark, ...sceneData.value, openId: getOpenid() };
      useChatExtendStore().showChatDialog('sp-server', param);
      return;
    }
    if (code === 15002) {
      return MessagePlugin.warning(i18nt('im.public.noCheckAuth'));
    }
    if (code === 15007) {
      return MessagePlugin.warning(i18nt('im.public.contentDeleted'));
    }
    if (code === 15011) {
      return MessagePlugin.warning(i18nt('im.public.ctRevoked'));
    }
    if (result?.code === 0) {
      showDialog(serviceId, teamId, reviewerCardId || publisherCardId);
      return;
    }
    MessagePlugin.warning(result.message);
  } catch (error) {
    console.log('error', error.status);
    if (error.status === 404) {
      MessagePlugin.warning('内容不存在');
    }
  }
};
const showDialog = (serviceId, teamId, reviewerCardId) => {
  const url = LynkerSDK.getH5UrlWithParams('/service/index.html#/preview', {
    id: serviceId,
    teamId,
    cardId: reviewerCardId,
  });
  useChatActionStore().showDialogIframe(true, {
    url,
    id: serviceId,
    name: '特色服务',
  });
};
// const cultureApplyClick = () => {
//   const me = { openId: getOpenid(), cardId: sceneData.value.extend.cardId, teamId: sceneData.value.extend.teamId };
//   useChatExtendStore().showChatDialog('culture-apply', { ...sceneData.value.extend, type: sceneData.value.type, me });
// };
</script>
<style scoped lang="scss">
.text-label{
  flex:none;
  white-space: normal;
}
.text-value {
  word-break: break-word;
  white-space: normal;
}
</style>
