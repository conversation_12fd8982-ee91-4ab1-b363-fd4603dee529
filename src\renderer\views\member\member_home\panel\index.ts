import { defineAsyncComponent } from 'vue';
const MarketGoodManagerPanel = defineAsyncComponent(() => import('./market-good-manager.vue'));
const MarketShopAdvertising = defineAsyncComponent(() => import('./market-shop-advertising/index.vue'));
const MarketAdvertising = defineAsyncComponent(() => import('./mark-advertising/index.vue'));
const PlatformAdvertising = defineAsyncComponent(() => import('./platform-mark-advertising/index.vue'));

const PActiveMemberPanel = defineAsyncComponent(() => import('./active-member-panel.vue'));
const PApplyMemberPanel = defineAsyncComponent(() => import('./apply-member-panel.vue'));
const PMembershipFeePanel = defineAsyncComponent(() => import('./membership-fee-panel.vue'));
const PMembershipSettingPanel = defineAsyncComponent(() => import('./membership-setting-panel.vue'));
const PRegularMemberPanel = defineAsyncComponent(() => import('./regular-member-panel.vue'));

const PReminderSettingPanel = defineAsyncComponent(() => import('./reminder-setting-panel.vue'));

const PAppAdminPanel = defineAsyncComponent(() => import('./app-admin-panel.vue'));

const PMembershipPositionPanel = defineAsyncComponent(() => import('./membership-position-panel.vue'));

const EbookPanel = defineAsyncComponent(() => import('./ebook/ebook-panel.vue'));
const TrendsPanel = defineAsyncComponent(() => import('./trends-panel.vue'));
const ContactPersonPanel = defineAsyncComponent(() => import('./contact-person/contact-person-panel.vue'));
const PlatformMembersPanel = defineAsyncComponent(() => import('./platform-members-panel.vue'));

const PlatformSettlementFund = defineAsyncComponent(() => import('./settlement/index.vue'));
const PlatformSettlementOrder = defineAsyncComponent(() => import('./settlement/SettlementOrder.vue'));

const ForumAdminPostManagerPanel = defineAsyncComponent(() => import('./forum-admin/postManager.vue'));
const ForumAdminTopicManagerPanel = defineAsyncComponent(() => import('./forum-admin/topicManager.vue'));
const ForumAdminUserManagerPanel = defineAsyncComponent(() => import('./forum-admin/userManager.vue'));
const ForumAdminGeneralSettingPanel = defineAsyncComponent(() => import('./forum-admin/generalSetting.vue'));

const ActivityPlatformAdminPanel = defineAsyncComponent(() => import('@/views/activity/platform-activity-admin/index.vue'));

export const panels = {
  MarketGoodManagerPanel, // 市场商品管理
  MarketShopAdvertising, // 市场店铺
  PActiveMemberPanel, // 激活会员
  PApplyMemberPanel, // 入会申请
  PMembershipFeePanel, // 会费记录
  PMembershipSettingPanel, // 入会设置
  PRegularMemberPanel, // 正式会员
  MarketAdvertising, // 市场广告
  PlatformAdvertising, // 平台广告
  PReminderSettingPanel, // 提醒设置
  PAppAdminPanel, // 应用管理
  PMembershipPositionPanel, // 会员职务
  EbookPanel,
  TrendsPanel, // 平台动态
  ActivityPlatformAdminPanel, // 数字平台活动管理
  ContactPersonPanel, // 联络人
  PlatformMembersPanel, // 平台成员
  ForumAdminPostManagerPanel, // 论坛管理（内容管理）
  ForumAdminTopicManagerPanel, // 论坛管理（话题管理）
  ForumAdminUserManagerPanel, // 论坛管理（成员管理）
  PlatformSettlementFund, // 佣金资金
  PlatformSettlementOrder, // 结算佣金
  ForumAdminGeneralSettingPanel, // 论坛管理（通用设置）
};
