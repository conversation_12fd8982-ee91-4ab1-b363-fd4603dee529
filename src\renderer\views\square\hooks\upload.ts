import { computed, ref } from 'vue';
import { LoadingPlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import { PutObjectResult } from 'ali-oss';
import MP4Box from 'mp4box';
import upload, { multipartUpload } from '@/views/square/utils/upload';

interface UploadOptions {
  rename: boolean;
  rootDir: string;
  onProgress?: (progress: number) => void;
  partSize?: number;
}

// 图片上传
export function useImageUpload(uploadSuccess?, opts = { rename: true, rootDir: 'common' }) {
  const images = ref([]);
  const { t } = useI18n();
  const uploading = ref(false);

  const uploadImage = (file) => new Promise((resolve) => {
    uploading.value = true;
    upload(Array.isArray(file) ? file : file.raw, opts)
      .then((res: PutObjectResult[] | PutObjectResult) => {
        const response: { files?: { url: string, name: string }[], url?: string } = {};
        if (Array.isArray(res)) response.files = res;
        else response.url = res.url;

        uploadSuccess?.({ ...response, file }, images);
        resolve({ status: 'success', response });
      })
      .catch((res) => {
        resolve({ status: 'fail', error: t('square.uploadFail'), res });
      }).finally(() => {
        uploading.value = false;
      });
  });

  const removeImage = (index) => {
    images.value.splice(index, 1);
  };

  return {
    images,
    uploading,
    uploadImage,
    removeImage,
  };
}

// 图片上传（带进度显示）
export function useImageUploadWithProgress(uploadSuccess?, opts: UploadOptions = { rename: true, rootDir: 'common' }) {
  const images = ref<{ url: string }[]>([]);
  const { t } = useI18n();
  const uploading = ref(false);
  const uploadProgress = ref(0);

  const uploadImage = (file) => new Promise((resolve) => {
    uploading.value = true;
    uploadProgress.value = 0;

    const uploadFile = Array.isArray(file) ? file[0] : file;
    const fileList = images.value || [];
    const index = fileList.length;
    images.value = [...fileList, { url: '' }];
    multipartUpload(uploadFile, {
      ...opts,
      partSize: 102400,
      onProgress: (p) => {
        const progress = Math.floor(p * 100);
        uploadProgress.value = progress;
        opts.onProgress?.(progress);
      },
    })
      .then((res: any) => {
        console.log('multipartUpload res: ', index, res);
        const response = { url: res.url };
        // const newFileList = [...fileList, { url: res.url }];
        images.value[index] = { url: res.url };
        uploadSuccess?.({ ...response, file }, [...images.value]);
        resolve({ status: 'success', response });
      })
      .catch((res) => {
        resolve({ status: 'fail', error: t('square.uploadFail'), res });
      }).finally(() => {
        uploading.value = false;
        uploadProgress.value = 0;
      });
  });

  const removeImage = (index) => {
    if (images.value) {
      images.value.splice(index, 1);
    }
  };

  return {
    images,
    uploading,
    uploadProgress,
    uploadImage,
    removeImage,
  };
}

// 视频上传
export function useVideoUpload() {
  const { t } = useI18n();
  const videos = ref([]);
  // const videos = ref([{ url: 'https://img.kuaiyouyi.com/square/f03648ed61bb6572be67bd46f8832acb/20250114/ios/a7a5d646962dcdc642a07ba5d561bec7.MOV', status: 'success' }]);
  // const videos = ref([{ url: '', status: 'loading' }]);
  const videoUrl = computed(() => videos.value?.[0]?.url || '');
  const videoPosterUrl = computed(() => {
    if (!videoUrl.value) return '';
    return `${videoUrl.value}?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast`;
  });
  const videoPreviewVisible = ref(false);
  const videoUploadBtnRef = ref(null);
  const uploading = ref(false);

  const uploadVideo = (file) => new Promise((resolve) => {
    // eslint-disable-next-line no-param-reassign
    file.percent = 0;
    const uploadLoading = LoadingPlugin({
      attach: () => videoUploadBtnRef.value,
      showOverlay: true,
      size: '20px',
    });
    uploading.value = true;

    upload(file.raw)
      .then((res) => {
        resolve({ status: 'success', response: res });
        // eslint-disable-next-line no-param-reassign
        file.percent = 100;
        uploadLoading.hide();
      })
      .catch((res) => {
        console.log({ res });
        resolve({ status: 'fail', error: t('square.uploadFail'), res });
        uploadLoading.hide();
      }).finally(() => {
        uploading.value = false;
      });
  });

  const removeVideo = () => {
    videos.value = [];
    videoPreviewVisible.value = false;
  };

  return {
    videos,
    videoUrl,
    uploadVideo,
    videoPosterUrl,
    videoPreviewVisible,
    videoUploadBtnRef,
    removeVideo,
    videoUploading: uploading,
  };
}

/**
 * 坚持视频的编码格式是否正确
 */
export const useValidVideoType = () => {
  /**
   * 检查video的格式
   * @param file 视频文件
   * @returns Promise
   */
  const checkVideoCode = async (file) => new Promise((resolve, reject) => {
    const mp4boxFile = MP4Box.createFile();
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = (e) => {
      const arrayBuffer: any = e.target.result;
      arrayBuffer.fileStart = 0;
      mp4boxFile.appendBuffer(arrayBuffer);
    };
    mp4boxFile.onReady = (info) => {
      resolve(info);
    };
    mp4boxFile.onError = (info) => {
      reject(info);
    };
  });

  /**
   * 获取视频格式是否被支持
   * @param str 视频的mime值
   */
  const getCodecValid = (str) => {
    const arr = str.split(';');
    return !!arr[1].includes('avc1');
  };

  return {
    checkVideoCode,
    getCodecValid,
  };
};
