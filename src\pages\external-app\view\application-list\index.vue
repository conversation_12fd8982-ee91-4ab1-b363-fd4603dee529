<template>
  <div class="px-16 flex flex-col h-full">
    <div class="flex items-center justify-between py-[16px]">
      <div class="text-[#1A2139] font-600 text-18 leading-26">应用列表</div>

      <div class="flex items-center gap-12">
        <t-button
          v-if="marginRecordData?.data?.items.length"
          variant="outline"
          theme="primary"
          @click="openMarginRecord"
        >
          <template #icon>
            <iconpark-icon name="iconsecure" class="text-20"></iconpark-icon>
          </template>
          <span class="ml-4 text-#4D5EFF">保证金记录</span>
        </t-button>

        <t-button variant="outline" theme="primary" @click="marginDescRef?.show()">
          <template #icon>
            <iconpark-icon name="iconhelp" class="text-20"></iconpark-icon>
          </template>
          <span class="ml-4 text-#4D5EFF">保证金说明</span>
        </t-button>

        <t-button class="w-108" theme="primary" @click="userAuth ? appFormDrawerRef?.open() : null">
          <iconpark-icon class="text-[#fff] text-20" name="iconadd" />
          <span class="ml-4 font-600">新建应用</span>
        </t-button>

        <!-- <t-button variant="outline" theme="primary" @click="handleCorporatePayment">
          <iconpark-icon class="text-[#fff] text-20" name="iconadd" />
          <span class="ml-4 font-600">支付ui调整</span>
        </t-button> -->
      </div>
    </div>
    <NotAuth v-if="!userAuth" />
    <RTable
      v-else
      ref="RTableRef"
      :table="table"
      empty-data="暂无应用"
      :is-sticky-header="true"
      @change="onTableChange"
    ></RTable>
    <AppFormDrawer ref="appFormDrawerRef" @refresh="handleRefresh" />
    <MarginRecord ref="marginRecordRef" />
    <MarginDesc ref="marginDescRef" />
    <PayRecord ref="payRecordRef" />

    <PaymentDialog ref="paymentDialogRef" @payment-callback="handleRefresh" />
  </div>
</template>

<script setup lang="tsx">
import { h, ref } from 'vue';
import { useRouter } from 'vue-router';
import { RTable } from '@rk/unitPark';
import { debounce } from 'lodash';
import dayjs from 'dayjs';
import { MessagePlugin } from 'tdesign-vue-next';
import TableOperate from './components/TableOperate.vue';
import AppFormDrawer from '@pages/external-app/view/application-list/components/AppFormDrawer.vue';
import {
  getExternalAppList,
  deleteExternalApp,
  ExternalAppListDataItem,
  payAnnualFee,
  getMarginRecord,
} from '@pages/external-app/api';
import { IOptions } from '@axios/types';
import { Utils } from '@utils/index';
import external_app from '@pages/external-app/assets/external_app.svg';
import NotAuth from '@pages/external-app/components/notAuth.vue';
import MarginRecord from './components/MarginRecord.vue';
import MarginDesc from './components/MarginDesc.vue';
import PayRecord from './components/PayRecord.vue';

import PaymentDialog from '@pages/common/view/components/paymentDialog/index.vue';
import { BondStatusEnum } from '../../constants/enum';
import { useMutation, useQuery } from '@tanstack/vue-query';
import to from 'await-to-js';
import { AxiosError } from 'axios';

const router = useRouter();

const userAuth = ref(true);

const appFormDrawerRef = ref<InstanceType<typeof AppFormDrawer> | null>(null);

const marginRecordRef = ref<InstanceType<typeof MarginRecord> | null>(null);

const marginDescRef = ref<InstanceType<typeof MarginDesc> | null>(null);

const payRecordRef = ref<InstanceType<typeof PayRecord> | null>(null);

const paymentDialogRef = ref<InstanceType<typeof PaymentDialog> | null>(null);

const options = ref<IOptions>({
  env: (router.currentRoute.value.query.env as string) || Utils.config.env,
  teamId: (router.currentRoute.value.query.teamId as string) || '',
  openId: (router.currentRoute.value.query.openId as string) || '',
  token: (router.currentRoute.value.query.token as string) || Utils.config.token,
});

const getBondStatusText = (type: BondStatusEnum) => {
  switch (type) {
    case BondStatusEnum.NoNeedPay:
      return '无需支付';
    case BondStatusEnum.PendingPay:
      return '待支付';
    case BondStatusEnum.Paid:
      return '已支付';
    case BondStatusEnum.Refunded:
      return '已退款';
    default:
      return '-';
  }
};

const { mutateAsync: payMutation, isLoading: payLoading } = useMutation({
  mutationFn: (app_id: number) => payAnnualFee({ app_id }, options.value),
});

const { data: marginRecordData, refetch } = useQuery({
  queryKey: ['marginRecord', options],
  queryFn: () => getMarginRecord(options.value),
});

const table = ref<{
  attrs: {
    rowKey: string;
  };
  columns: {
    title: string;
    width: number;

    cell: (_h: typeof h, { row }: { row: ExternalAppListDataItem }) => any;
  }[];
  list: ExternalAppListDataItem[];
  pagination: {
    pageSize: number;
    current: number;
    total: number;
  };
}>({
  attrs: {
    rowKey: 'app_id',
  },
  columns: [
    {
      title: '应用信息',
      width: 172,
      cell: (_, { row }) => (
        <div class="flex items-center gap-8">
          <img class="w-36 h-36 object-cover rounded-12" src={row.picture_linking || external_app} alt="" />
          <div>{row.name || '未命名应用'}</div>
        </div>
      ),
    },
    {
      title: '应用类型',
      width: 120,
      cell: (_, { row }) => <div>{getTypeText(row.type || '')}</div>,
    },
    {
      title: '创建时间',
      width: 160,
      cell: (_, { row }) => <div>{row.created_at ? dayjs(row.created_at).format('YYYY-MM-DD HH:mm') : '-'}</div>,
    },
    {
      title: '展示渠道',
      width: 172,
      cell: (_, { row }) => <div>{getChannelText(row.channels || [])}</div>,
    },
    {
      title: '保证金',
      width: 120,
      cell: (_, { row }) => <div>{getBondStatusText(row.bond_status || BondStatusEnum.NoNeedPay)}</div>,
    },
    {
      title: '操作',
      width: 184,
      cell: (_, { row }) => (
        <TableOperate
          bondStatus={row.bond_status || BondStatusEnum.NoNeedPay}
          payLoading={payLoading.value}
          onEdit={() => {
            appFormDrawerRef.value?.open(row);
          }}
          onDelete={() => handleDelete(row)}
          onPayRecord={() => {
            if (!row.app_id) {
              return;
            }
            payRecordRef.value?.open(row.app_id);
          }}
          onPay={debounce(async () => {
            if (!row.app_id) {
              return;
            }

            const [err, res] = await to(payMutation(row.app_id));
            if (err) {
              const error = err as AxiosError<{ message: string }>;

              MessagePlugin.error(error?.response?.data?.message || err?.message || '保证金状态异常，请刷新后重试');
              return;
            }

            paymentDialogRef.value?.openWin({
              sn: res?.data?.bond_sn,
              region: res?.data?.region || 'CN',
              amount: parseFloat(res?.data?.bond_amount || '0'),
              orderType: 3,
            });
          }, 300)}
          onRefundRecord={() => {
            if (!row.app_id) {
              return;
            }
            payRecordRef.value?.open(row.app_id, true);
          }}
        />
      ),
    },
  ],
  list: [],
  pagination: {
    pageSize: 10,
    current: 1,
    total: 0,
  },
});

const onTableChange = debounce(async ({ pageInfo }: any, name: string) => {
  if (name === 'table') {
    table.value.pagination.current = pageInfo.current;
    table.value.pagination.pageSize = pageInfo.pageSize;
    table.value.pagination.total = pageInfo.total;
  } else {
    table.value.pagination.current = 1;
    table.value.pagination.pageSize = 10;
  }

  loadData();
});

const loadData = async () => {
  try {
    const response = await getExternalAppList(
      {
        page: table.value.pagination.current,
        pageSize: table.value.pagination.pageSize,
      },
      options.value,
    );
    if (response.code === 1010) {
      userAuth.value = false;
      return;
    }
    userAuth.value = true;
    if (response.code === 0) {
      // 直接使用API返回的数据
      table.value.list = response.data.items;
      table.value.pagination.total = response.data.total;
    } else {
      console.error('获取应用列表失败:', response.message);
    }
  } catch (error: any) {
    if (error?.response?.data?.code === 1010) {
      userAuth.value = false;
    }
    MessagePlugin.error(error?.response?.data?.message || error?.message || '获取应用列表失败');
    console.error('获取应用列表出错:', error);
  }
};

// 获取应用类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    app: 'App跳转',
    h5: '网页H5跳转',
    wechat_official: '微信公众号',
    mini_program: '微信小程序',
  };
  return typeMap[type] || '未知类型';
};

// 获取展示渠道文本
const getChannelText = (channels: any[]) => {
  if (!channels || channels.length === 0) return '无';

  const channelMap: Record<string, string> = {
    workshop: '数智工场',
    square: '广场号',
    digital: '数字平台',
  };

  return channels.map((channel) => channelMap[channel.channel_type] || channel.channel_type).join('、');
};

const handleRefresh = () => {
  loadData();
  refetch();
};

// 删除应用
const handleDelete = async (row: any) => {
  try {
    // 调用删除API
    const response = await deleteExternalApp({ app_id: row.app_id }, options.value);

    if (response.code === 0) {
      MessagePlugin.success('应用已删除');
      // 刷新列表
      loadData();
      refetch();
    } else {
      MessagePlugin.error(response.message || '应用删除失败，请刷新后重试');
    }
  } catch (error: any) {
    console.error('删除应用失败:', error);
    MessagePlugin.error(error?.response?.data?.message || '应用删除失败，请刷新后重试');
  }
};

// 保证金列表
const openMarginRecord = () => {
  const items = marginRecordData?.value?.data?.items || [];

  if (items.length === 0) {
    MessagePlugin.error('当前应用无保证金记录');
    return;
  }

  marginRecordRef.value?.show(marginRecordData?.value?.data!);
};

loadData();
</script>

<style lang="less" scoped>
:deep(.RK-Table) {
  height: calc(100vh - 74px);
  overflow: auto;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .t-table {
    td:last-child {
      padding: 8px !important;
    }
  }
}
</style>
