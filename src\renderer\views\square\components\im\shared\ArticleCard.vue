<template>
  <div>
    <div class="article-card">
      <div class="card-header">
        <kyy-avatar
          class="avatar"
          avatar-size="32px"
          :image-url="square.avatar"
          :user-name="square.name"
        />
        <div class="name">{{ square.name }}</div>
        <div class="type">
          <!-- <svg-icon name="square-link" class="icon" /> -->
          <i class="i-svg:link icon" />&nbsp;{{ $t('square.article.article') }}
        </div>
      </div>
      <div class="card-img">
        <t-image class="flex-1" :src="article.img" />
      </div>
      <div class="card-footer">
        <div class="title">{{ article.title }}</div>
      </div>
    </div>

    <!--    <article-preview v-model="articlePreviewVisible" :square="square" :article="article" />-->
  </div>
</template>

<script setup lang="ts">
import KyyAvatar from '@/components/kyy-avatar/index.vue';
import { SquareArticle } from '@/api/square/models/post';
import { SquareSquare } from '@/api/square/models/common';

defineProps<{
  square: SquareSquare;
  article: SquareArticle;
}>();
</script>

<style scoped lang="less">
.article-card {
  width: 376px;
  border: 1px solid #e3e6eb;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

.card-header {
  display: flex;
  align-items: center;
  height: 56px;
  padding: 0 16px;
  .avatar {
    margin-right: 8px;
    flex-shrink: 0;
  }
  .name {
    flex: 1;
    width: 0;
    color: #2a9da1;
    .ellipsis();
  }
  .type {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    .icon {
      // width: 16px;
      // height: 16px;
      font-size: 16px;
    }
  }
}

.card-img {
  width: 100%;
  height: 210px;
  display: flex;
  :deep(img) {
    -webkit-user-drag: none;
  }
}

.card-footer {
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 68px;
  .title {
    .multi-ellipsis(2);
  }
}
</style>
