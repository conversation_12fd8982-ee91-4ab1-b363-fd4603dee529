<template>
  <div class="content-section">
    <div class="section-title">{{ title }}</div>
    <div class="applicant-info">
      <div v-if="tag" class="tag personal-tag">{{ tag }}</div>
      <span class="applicant-name">{{ value }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  value: string
  tag?: string
}

defineProps<Props>();
</script>

<style scoped>
.content-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.section-title {
  width: 88px;
  height: 22px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.571;
  color: #828da5;
}

.applicant-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.333;
  height: 20px;
}

.personal-tag {
  background: #e8f5e8;
  color: #52c41a;
}

.applicant-name {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.571;
  color: #1a2139;
  flex: 1;
}
</style>
