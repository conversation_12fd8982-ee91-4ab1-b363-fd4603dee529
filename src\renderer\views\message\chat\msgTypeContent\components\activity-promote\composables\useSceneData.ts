import { computed, ComputedRef } from 'vue';
import moment from 'moment';

interface SceneDataProps {
  msg?: any;
}

interface UseSceneDataReturn {
  sceneData: ComputedRef<any>;
  time: ComputedRef<string>;
  extendData: ComputedRef<any>;
  handleActivityData: (data: any) => {
    time: string;
    type: 'danger' | 'primary' | 'success' | 'secondary' | 'warning' | 'none';
    title: string;
    position: string;
  };
}

const rejectButton = {
  key: 'reject',
  text: '拒绝',
  variant: 'outline' as const,
  theme: 'danger' as const,
};
const approveButton = {
  key: 'agree',
  text: '通过',
  variant: 'outline' as const,
  theme: 'primary' as const,
};
const infoButton = {
  key: 'info',
  text: '查看活动详情',
  variant: 'outline' as const,
  theme: 'default' as const,
};
const cancelButton = {
  key: 'cancel',
  text: '已取消申请',
  variant: 'outline' as const,
  theme: 'default' as const,
  disabled: true,
};

enum Status {
  PLATFORM_STATUS_DEFAULT = 'PLATFORM_STATUS_DEFAULT',
  PLATFORM_STATUS_PENDING_REVIEW = 'PLATFORM_STATUS_PENDING_REVIEW',
  PLATFORM_STATUS_AGREED = 'PLATFORM_STATUS_AGREED',
  PLATFORM_STATUS_REJECTED = 'PLATFORM_STATUS_REJECTED',
  PLATFORM_STATUS_CANCELLED = 'PLATFORM_STATUS_CANCELLED',
  PLATFORM_STATUS_INVALIDED = 'PLATFORM_STATUS_INVALIDED',
}

// 消息类型，1-已通过，2-已拒绝，3-已取消
enum MessageType {
  AGREED = 1,
  REJECTED = 2,
  CANCELLED = 3,
}

export const headerColorMap = {
  [MessageType.AGREED]: 'success',
  [MessageType.REJECTED]: 'danger',
  [MessageType.CANCELLED]: 'secondary',
};

export const headerTitleMap = {
  [MessageType.AGREED]: '活动推广审核通过',
  [MessageType.REJECTED]: '活动推广审核不通过',
  [MessageType.CANCELLED]: '活动推广已取消',
};

export const statusMap = {
  [Status.PLATFORM_STATUS_DEFAULT]: {
    buttons: [rejectButton, approveButton],
  },
  [Status.PLATFORM_STATUS_PENDING_REVIEW]: {
    buttons: [rejectButton, approveButton],
  },
  [Status.PLATFORM_STATUS_AGREED]: {
    buttons: [infoButton],
  },
  [Status.PLATFORM_STATUS_REJECTED]: {
    buttons: [infoButton],
  },
  [Status.PLATFORM_STATUS_CANCELLED]: {
    buttons: [cancelButton],
  },
  [Status.PLATFORM_STATUS_INVALIDED]: {
    buttons: [infoButton],
  },
};

const getTime = (startTime: number, endTime: number) => {
  if (!startTime || !endTime) {
    return '--';
  }
  return `${moment.unix(startTime).format('MM-DD HH:mm')} ~ ${moment.unix(endTime).format('MM-DD HH:mm')}`;
};

const getMap = (map, key) => map[key] || '--';

export const getTitle = (key) => getMap(headerTitleMap, key);

// 处理活动的数据
const handleActivityData = (data: any) => {
  const { duration, promotion_message_type, digital_platform } = data;
  const result = {
    time: '--',
    type: getMap(headerColorMap, promotion_message_type) || 'secondary',
    title: getMap(headerTitleMap, promotion_message_type) || '',
    // 推广位置
    position: `数字平台：${digital_platform?.name}`,
  };
  if (duration?.start_time && duration?.end_time) {
    result.time = getTime(duration?.start_time, duration?.end_time);
  }
  return result;
};
/**
 * 提取场景数据和时间格式化的公共逻辑
 */
export function useSceneData(props: SceneDataProps): UseSceneDataReturn {
  const extendData = computed(() => props.msg?.contentExtra?.data?.extend);
  const sceneData = computed(() => props.msg?.contentExtra?.data);

  const time = computed(() => {
    if (extendData.value?.duration?.start_time && extendData.value?.duration?.end_time) {
      const { time } = handleActivityData(extendData.value);
      return time;
    }
    return '--';
  });

  return {
    extendData,
    time,
    sceneData,
    handleActivityData,
  };
}
