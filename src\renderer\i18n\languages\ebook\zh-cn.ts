export const ebook_CN = {
  load: '加载中...',
  ss: '搜索会刊名称',
  sskw: '搜索刊物名称',
  loadmore: '加载更多',
  neterr: '网络链接失败，请检查网络后重试',
  again: '点击重试',
  nodata: '暂无刊物',
  addebook: '添加会刊',
  view: '查看',
  cr: '会刊生成中',
  crok: '已发布',
  crerr: '生成失败',
  ebookname: '会刊名称',
  status: '状态',
  upat: '更新时间',
  del: '删除',
  delebook: '删除会刊',
  deltip: '删除后不可恢复，是否继续删除？',
  delsucc: '删除成功！',
  uptip: '上传格式为pdf，大小不超过100MB！',
  edsucc: '编辑成功',
  edsucc2: '设置成功',
  addsucc: '正在为你生成电子会刊，生成后将自动发布，请稍后查看',
  namerq: '请输入会刊名称',
  namerq2: '请输入刊物名称',
  up: '点击上传',
  ebfile: '会刊文件',
  cover: '会刊封面',
  ebdesc: '会刊描述',
  input: '请输入会刊名称',
  covtip: '上传格式为png，jpeg，jpg，大小不超过5MB',
  editeb: '编辑会刊',
  addeb: '添加会刊',
  addse: '添加成功',
  validatorTip: '请上传会刊封面',
  validatorTipkw: '请上传刊物封面',
  validatorTip2: '请上传会刊文件',
  validatorTip23: '请上传刊物文件',
  plin: '请输入内容',
  noreg: '未找到匹配会刊',

  gri: '分组信息',
  gai: '添加分组',
  gdi: '编辑分组',
  grn: '分组名称',
  grni: '请输入分组名称',
  llr: '联络人',
  llri: '请选择联络人',
  llri2: '选择联络人',
  ad: '添加',
  grq: '分组群',
  gre: '解散该群',
  qn: '群名称',
  qno: '群主',
  qrs: '群人数',
  gr2q: '当成员数2名后，会自动创建群聊',
  zqw: '暂无群聊',
  cjq: '创建群聊',
  cjqz: '创建分组群',
  auc: '创建一个关联此分组的群，如果有新成员进入分组会自动加入该群',
  tip1: '1、当分组中分配了联络人，并且的成员数有2名后才会创建此群聊。',
  tip2: '2、新进入分组的成员会自动加入到群聊。',
  tip3: '3、如果成员被移除了当前分组，群聊中也会移除此成员。',
  tip4: '4、创建群聊时会把联络人作为群主。群主可在群管理中转移群主身份。',
  es: '编辑成功',
  exs: '解散成功',
  ex: '解散',
  ext: '解散该群，群聊中的聊天记录，图片和文件将同时被删除',
  sxz: '选择群主',
  sxzt: '提示：群主仅能从联络人中进行选择',
  cs: '创建成功',
  cst: '确定要创建群聊吗？',
  changet: '该分组群的群主将由【{name}】变更为【{name2}】，是否确定？',
  grs: '设置分组',
  sgt: '请选择分组',
  grsu: '设置成功',
  lrr: '联络人',
  fz: '分组',
  fz2: '分组：',
  ssllr: '搜索联络人',
  nlllr: '暂无联络人',
  alrr: '添加联络人',
  dllr: '删除联络人',
  dllrt: '是否删除该联络人？',
  asu: '添加成功',
  zug: '暂无分组',
  cys: '成员数',
  de1: '确定要删除该分组？如果该分组有分组群，则分组群的聊天信息和文件都会删除。',
  de2: '请先移除分组内除联络人以外的所有平台成员再进行删除分组',
  fzck: '分组查看设置',
  bc: '保存',
  fzfw: '分组查看范围',
  fw1: '1、可查看全部分组：平台成员和联络人可在名录、通讯录中查看所有分组下的成员。',
  fw12: '2、仅可查看所在分组：平台成员和联络人在名录、通讯录中仅可查看到自己所在分组下的成员。',
  all: '可查看全部分组',
  only: '仅可查看所在分组',
  liw: '以下人员例外',
  dgx: '当勾选了“仅可查看所在分组”后，可单独设置成员依然可查看全部分组',
  tianj: '添加',
  ttipss1: '成员数2名后才可以创建群聊',
  wfz: '未分组',
  setsu: '设置成功',
  leader: '请选择群主',
  grsu2: '设置分组成功',
  seterr: '设置分组失败',
  qd: '确定',
  lz: '已离职',
  zug1: '暂无联络人',
  mset1: '确定关闭【平台动态审核】？',
  mset2: '关闭后，申请推广的平台动态无需审核直接通过',
  mset3: '确定',
  mset4: '取消',
  mset5: '平台动态设置',
  mset6: '动态审核',
  mset7: '开启后，申请推广的平台动态均要管理员审核',

  visback: '返回',
  vist: '访客申请',
  visp: '请选择',
  vispass: '通过',
  visrefu: '拒绝',
  viyt: '已通过',
  vrefuse: '已拒绝',
  sx: '已失效',
  nos: '搜索无结果',
  nod: '暂无数据',
  vzc: '正常',
  vjr: '未加入',
  vtype1: '全部',
  vtype2: '待审核',
  vtype3: '已通过',
  vtype4: '已拒绝',
  vtype5: '自动通过',
  vtype6: '已失效',
  vname: '姓名',
  vphone: '手机号码',
  voperatorer: '邀请人',
  vtime: '申请时间',
  voperate: '操作',
  vsc: '操作成功',
  ver: '操作失败',
  vssxm: '搜索姓名',
  vfk: '访客申请列表',
  vyq: '邀请访客',
  vst: '状态：',
  vcy: '成员类型：',
  vph: '手机号：',
  vxq: '详情',
  vst2: '状态',
  vcy2: '成员类型',
  vph2: '手机号',
  vzs: '正式',
  vfk2: '访客',
  vat: '加入时间',
  vemail: '邮箱',
  vb: '基础信息',
  vzty: '关联主体',
  delf: '删除访客',
  vfz: '负责人',
  vxx: '联系人',
  vdb: '代表人',
  vdelt: '确定删除当前访客？',
  vdelc: '删除后，该访客不可再访问数字平台，已产生的数据不可恢复',
  vdsu: '删除成功',
  vfk3: '访客状态异常',
  vipu: '请输入',

};
