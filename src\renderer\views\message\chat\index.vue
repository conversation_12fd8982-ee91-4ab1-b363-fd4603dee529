<template>
    <chat-header></chat-header>
    <div class="drop-content select-none">
      <div class="drop-content-body flex flex-grow-2 h-[0] w-full" id="dropZone">
        <chat-content></chat-content>
      </div>
      <div v-show="isShowEditor()">
        <chat-editor ref="refEditor" />
      </div>
    </div>
    <div v-if="store.showNotFriends" class="not-in-group">
        {{t('im.public.addContact1')}}<a @click="openCard">{{ getConversationName(store.chatingSession)}}</a>{{t('im.public.addContact2')}}<a @click="openCard">{{t('im.public.addContact3')}}</a>
    </div>

    <div v-if="store.showNotInGroup" class="not-in-group">
      {{ store.chatingSession.unregistered ? t('im.public.logoutTip') : t('im.public.exitTip') }}
    </div>

    <!-- 提示被移除群聊 -->
    <t-dialog v-if="store.showGroupRemoveAlert"
    :visible="store.showGroupRemoveAlert"
    :header="t('im.public.tips')"
    :confirmBtn="t('im.public.known')"
    :cancelBtn="null"
    @close="store.onGroupDeletedConfirm(store.chatingSession)"
    @confirm="store.onGroupDeletedConfirm(store.chatingSession)"
    >
        <t-icon name="error-circle-filled" style="color: #E66800" />
      {{ DissolutionOrKickOut[store.isDissolutionOrKickOut ?? 'kickOut'] }}
    </t-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import ChatHeader from './ChatHeader.vue';
import ChatContent from './ChatContent.vue';
import ChatEditor from './ChatEditor.vue';
import { useMessageStore } from '../service/store';
import useChatSendStore from '../service/sendStore';
import { getConversationName } from '../service/msgUtils';
import { useI18n } from 'vue-i18n';
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer, fs } = LynkerSDK;
const refEditor = ref<any>(null);

const { t } = useI18n();

const store = useMessageStore();
const DissolutionOrKickOut = {
  dissolution: t('im.public.dismissTip'),
  kickOut: t('im.public.removeTip'),
}

const isShowEditor = () => {
    if (store.chatingSession?.conversationType === 1) {
        return !store.chatingSession.unregistered && store.chatingSession.inSession;
    }

    return (['0', '1', '2', '3', '10', '15', '20', '22', '23']?.includes(store.chatingSession?.relation) || store.chatingSession.localSessionId === 'assistant8app8file8helper') && store.chatingSession?.inSession;
}

// const showNotInGroup = () => {
//     if (store.chatingSession?.conversationType === 1) {
//         return store.chatingSession.unregistered;
//     }

//     return !store.isAssistantSession && !store.chatingSession?.inSession;
// }

// const showNotFriends = () => {
//     return store.chatingSession?.conversationType === 1 && !store.chatingSession.inSession;
// }

const openCard = () => {
    const cardId = store.chatingSession.targetCardId;
    const myId = store.chatingSession.myCardId;
    if (cardId && myId) {
        ipcRenderer.invoke("identity-card", { cardId, myId });
    }
}
onMounted(() => {
    let dropZone = document.getElementById('dropZone');

    // 阻止默认行为，允许放置
    dropZone?.addEventListener("dragover", function (e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('dragover');
    });

    // // 拖入元素时添加样式
    // dropZone.addEventListener("dragenter", function (e) {
    //     console.log('dragenter');
    //     e.preventDefault();
    //     e.stopPropagation();
    //     dropZone.classList.add('drag-over');
    // });

    // // 拖出元素时移除样式
    // dropZone.addEventListener("dragleave", function (e) {
    //     console.log('dragleave');
    //     e.preventDefault();
    //     e.stopPropagation();
    //     dropZone.classList.remove('drag-over');
    // });

    // 处理放置文件
    dropZone?.addEventListener("drop", function (e) {
        e.preventDefault();
        e.stopPropagation();
        // dropZone.classList.remove('drag-over');

        // 获取文件列表
        const files = e.dataTransfer.files;
        if (files && files.length > 0) {
            // 处理文件上传
            console.log('dropZonefiles',files)
            handleDropFiles(files);
        }
    });
});
// 处理拖放的文件
const handleDropFiles = async(files) => {
    const sendStore = useChatSendStore();

    // 检查是否在可发送消息的状态
    if (!isShowEditor()) {
        return;
    }
    if( !files[0]?.path ) return;
    const stats = await fs.promises.stat(files[0].path);
    if (stats.isDirectory()) {
      MessagePlugin.error('不支持文件夹上传!');
      return;
    }
    let imageListLength = 0;
    files.forEach((file) => {
      if (file.type.startsWith('image/') && file?.size < 20 * 1024 * 1024) {
        imageListLength++;
      }
    });
    const hasOtherType = imageListLength < files?.length && imageListLength > 0;
    files.forEach((file) => {
      if (hasOtherType) {
        sendStore.sendFile(file, store.chatingSession);
      } else {
        console.log('sendFilefile',file)
        if (file.type.startsWith('image/')){

          return
        };
        sendStore.sendFile(file, store.chatingSession);
      }
    });
}
</script>

<style lang="less" scoped>

.not-in-group {
    background-color: @kyy_red_1;
    padding: 20px;
    text-align: center;
}
.drop-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: stretch;
    height: 100%;
    max-height: 100%;
    overflow: hidden;
     // 添加拖拽样式
     &.drag-over {
        border: 2px dashed #409eff;
        background-color: rgba(64, 158, 255, 0.1);
    }
}
</style>
