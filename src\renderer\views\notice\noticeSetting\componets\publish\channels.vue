<template>
  <div class="channge">
    <div v-if="squareShow" class="item">
      <t-checkbox v-model="channelDetail.to_square" @change="seChange">{{ t('notice.gc') }}</t-checkbox>
    </div>
    <div class="item">
      <div class="ch-type"><t-checkbox v-model="channelDetail.work_shop" @change="seChange">{{ t('notice.szgc') }}</t-checkbox></div>
      <div v-if="channelDetail.work_shop" class="ch-box">
        <div class="radio">
          <t-radio-group v-model="channelDetail.work_shop_type" @change="onChangeWorkShop">
            <t-radio :value="1">{{ t('notice.qbcy') }}</t-radio>
            <t-radio :value="2">{{ t('notice.bfcy') }}</t-radio>
          </t-radio-group>
        </div>
        <div v-if="channelDetail.work_shop_type === 2" class="plsit" :class="{errCla: check && !channelDetail.work_shop_receiver.length}">
          <div class="mbtn" @click="addRun(1)">
            <iconpark-icon name="iconadd" style="font-size: 20px" class="name-icon" /> {{ t('notice.pltj') }}
          </div>
          <t-tag
            v-for="(tag, index) in channelDetail.work_shop_receiver"
            :key="index"
            class="tagText"
            theme="default"
            :closable="true"
            :max-width="124"
            @close="handleClose(index)"
          >
            <template #icon>
              <kyy-avatar
                round-radius
                avatar-size="20px"
                :image-url="tag.avatar"
                :user-name="tag.name"
                style="margin-right: 8px;"
              />
            </template>
            {{ tag.name }}
          </t-tag>
        </div>
        <div v-if="props.type === 1 && channelDetail.work_shop_type === 2 && check && !channelDetail.work_shop_receiver.length" class="error-tip">
          {{ t('notice.qxzcy') }}
        </div>
      </div>
    </div>
    <div v-if="platformShow" class="item">
      <div class="ch-type"><t-checkbox v-model="channelDetail.platform" @change="seChange">数字平台</t-checkbox></div>
      <div v-if="channelDetail.platform" class="ch-box">
        <div class="radio">
          <t-radio-group v-model="channelDetail.platform_type" @change="onChangePlatform">
            <t-radio :value="1">{{ t('notice.qbcy') }}</t-radio>
            <t-radio :value="2">{{ t('notice.bfcy') }}</t-radio>
          </t-radio-group>
        </div>
        <div v-if="channelDetail.platform_type === 2" class="plsit" :class="{errCla: check && !channelDetail.platform_receiver.length}">
          <div class="mbtn" @click="addRun(2)">
            <iconpark-icon name="iconadd" style="font-size: 20px" class="name-icon" /> 批量添加
          </div>
          <t-tag
            v-for="(tag, index) in channelDetail.platform_receiver"
            :key="index"
            class="tagText"
            theme="default"
            :closable="true"
            :max-width="124"
            @close="handleClosePlatform(index)"
          >
            <template #icon>
              <kyy-avatar
                round-radius
                avatar-size="20px"
                :image-url="tag.avatar"
                :user-name="tag.name"
                style="margin-right: 8px;"
              />
            </template>
            {{ tag.name }}
          </t-tag>
        </div>
        <div v-if="props.type === 1 && channelDetail.platform_type === 2 && check && !channelDetail.platform_receiver.length" class="error-tip">
          {{ t('notice.qxzcy') }}
        </div>
      </div>
    </div>
  </div>

  <selectMember
    v-model:visible="selectMemberVisible"
    :select-list="selectList"
    :disabled-card="true"
    :menu="menus"
    :active-card-id="cardId"
    :team-id="activationGroupItem.teamId"
    :change-menus="false"
    @confirm="selectMemberConfirm"
  />
  <!-- 'recent', 'friend', 'orgcontacts', 'groups',  -->
</template>

<script setup lang="ts">
import { getAppsState } from "@renderer/views/niche/apis/create";
import to from "await-to-js";
import { ref, watch } from "vue";
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const check = ref(false);
const platformShow = ref(false);
const squareShow = ref(false);
import selectMember from '@renderer/components/rk-business-component/select-member/common-add-members.vue';
import { useNoticeStore } from "@renderer/store/modules/notice";
const teamId = ref(localStorage.getItem("workBenchTeamid"));
const noticeStore = useNoticeStore();
const activationGroupItem = ref(noticeStore.activationGroupItem);
console.log("noticeStore.activationGroupItem", activationGroupItem.value);
const cardId = ref([]);
const menus = ref([]);
const selectList = ref([]);
const emits = defineEmits(["change"]);

const props = defineProps({
  chdata: {
    type: Object,
    default: () => {},
  },
  type: {
    type: Number,
    default: 1,
  },
});
// watch(
//   () => props.chdata,
//   (newValue, oldValue) => {
//     console.log('props.chdata', newValue, oldValue);
//     channelDetail.value.to_square = !!newValue.to_square;
//     channelDetail.value.work_shop = !!newValue.work_shop_type;
//     channelDetail.value.work_shop_type = newValue.work_shop_type === 2 ? 2 : 1;
//     channelDetail.value.work_shop_receiver = newValue.work_shop_receiver;
//     channelDetail.value.platform = !!newValue.platform_type;
//     channelDetail.value.platform_type = newValue.platform_type === 2 ? 2 : 1;
//     channelDetail.value.platform_receiver = newValue.platform_receiver;
//     check.value = false;
//   },
// );

const setData = (newValue) => {
  channelDetail.value.to_square = !!newValue.to_square;
  channelDetail.value.work_shop = !!newValue.work_shop_type;
  channelDetail.value.work_shop_type = newValue.work_shop_type === 2 ? 2 : 1;
  channelDetail.value.work_shop_receiver = newValue.work_shop_list;
  channelDetail.value.platform = !!newValue.platform_type;
  channelDetail.value.platform_type = newValue.platform_type === 2 ? 2 : 1;
  channelDetail.value.platform_receiver = newValue.platform_list;
  check.value = false;
};

const getAppAuth = async () => {
  const [err, res] = await to(getAppsState(teamId.value));
  if (err) {
    return;
  }
  const { data } = res;
  if (data.data?.government || data.data?.member || data.data?.cbd|| data.data?.association|| data.data?.uni) {
    channelDetail.value.platform = false;
    platformShow.value = true;
  } else {
      channelDetail.value.platform = false;
      platformShow.value = false;
  }
  squareShow.value = data.data?.square;
  channelDetail.value.to_square = false;
};
getAppAuth();

const channelDetail = ref({
  to_square: false,
  work_shop: false,
  work_shop_type: 1,
  work_shop_receiver: [],
  platform: false,
  platform_type: 1,
  platform_receiver: [],
});
const resData = () => {
  const data = channelDetail.value;
  const temp:any = {};
  temp.to_square = squareShow.value ? data.to_square ? 1 : 0 : 0;
  temp.work_shop_type = data.work_shop ? data.work_shop_type : 0;
  temp.platform_type = data.platform ? data.platform_type : 0;
  temp.work_shop_list = data.work_shop_receiver;
  temp.platform_list = data.platform_receiver;
  temp.work_shop_receiver = data.work_shop_receiver.map((item) => item.cardId.replace(/\$/g, ''));
  temp.platform_receiver = data.platform_receiver.map((item) => item.cardId.replace(/\PT/g, ''));
  return temp;
};
const checkFunc = () => {
  check.value = true;
};

const onChangeWorkShop = (e) => {
  check.value = false;
  const res = resData();
  emits('change', res);
};
const onChangePlatform = (e) => {
  check.value = false;
  const res = resData();
  emits('change', res);
};
const seChange = (e) => {
  const res = resData();
  emits('change', res);
};

const selectMemberVisible = ref(false);
const selectType = ref(1);

const addRun = (type) => {
  selectType.value = type;
  // cardId.value = ["PT1304"];
  cardId.value[0] = activationGroupItem.value?.cardId;
  if (type === 1) {
    menus.value = ["organize"];
    selectList.value = channelDetail.value.work_shop_receiver.map((item) => item.cardId || item.openid);
  } else {
    menus.value = ["platform"];
    selectList.value = channelDetail.value.platform_receiver.map((item) => item.cardId || item.openid);
  }
  console.log(cardId.value);
  selectMemberVisible.value = true;
};
const selectMemberConfirm = (e) => {
  console.log(e);
  check.value = false;
  if (selectType.value === 1) {
    channelDetail.value.work_shop_receiver = e;
  } else {
    channelDetail.value.platform_receiver = e;
  }
  const res = resData();
  emits('change', res);
};

const handleClose = (index) => {
  channelDetail.value.work_shop_receiver.splice(index, 1);
};

const handleClosePlatform = (index) => {
  channelDetail.value.platform_receiver.splice(index, 1);
};
defineExpose({
  resData,
  setData,
  checkFunc,
});
</script>

<style lang="less" scoped>
.channge {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
  .item {
    width: 100%;
    .ch-type {
    }
    .radio {
      margin: 8px 0;
    }
    .ch-box {
      padding-left: 28px;

      .plsit {
        width: 100%;
        min-height: 26px;
        max-height: 96px;
        overflow-y: auto;
        display: flex;
        padding: 4px 12px;
        align-items: flex-start;
        align-self: stretch;
        border-radius: var(--input-kyy_radius_input, 4px);
        border: 1px solid #d5dbe4;
        gap: 8px;
        flex-wrap: wrap;
        align-content: flex-start;
        .mbtn {
          display: flex;
          cursor: pointer;
          height: 24px;
          min-height: 24px;
          max-height: 24px;
          padding: 0px 12px 0px 8px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: var(--radius-kyy_radius_button_s, 4px);
          border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4d5eff);
          background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #eaecff);
          color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4d5eff);
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }

        .tagText {
          display: flex;
          height: 24px;
          min-height: 24px;
          max-height: 24px;
          padding: 2px 8px;
          align-items: center;
          border-radius: 4px;
          background: var(--kyy_color_tag_bg_gray, #ECEFF5);
        }
      }
    }
  }
}
.errCla{
  border: 1px solid var(--color-button_secondaryError-kyy_color_button_secondaryError_border_dedault, #D54941) !important;
}
.error-tip{
  margin-top: 8px;
  color: var(--kyy_color_tag_text_error, #D54941);
font-family: "PingFang SC";
font-size: 12px;
font-style: normal;
font-weight: 400;
line-height: 20px; /* 166.667% */
}
</style>
