<template>
  <!-- 配送模板管理页面 -->
  <div class="delivery-template-wrapper">
    <RTable
      ref="RTableRef"
      :table="table"
      :filter="filter"
      :is-change-clean="true"
      :is-sticky-header="true"
      @change="change"
    >
      <template #toolbarContent>
        <t-button class="font-600!" theme="primary" @click="handleCreateTemplate">
          <t-icon name="add" />
          新建模板
        </t-button>
      </template>
      <template v-if="!isLoading" #empty>
        <REmpty tip="暂无模板" :name="isNoData ? 'no-result' : 'no-data'" />
      </template>
    </RTable>
  </div>
  <RelatedProducts ref="relatedProductsRef" />
  <DeliveryTemplateDialog ref="deliveryTemplateDialogRef" @confirm="handleTemplateConfirm" />
</template>

<script setup lang="tsx">
import { ref, onMounted, computed, watch } from 'vue';
import { RTable } from '@rk/unitPark';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { REmpty } from '@rk/unitPark';
import RelatedProducts from './components/RelatedProducts.vue';
import DeliveryTemplateDialog from './components/DeliveryTemplateDialog.vue';
import {
  getIntraCityDeliveryTemplateList,
  setIntraCityDeliveryTemplate,
  delIntraCityDeliveryTemplate,
} from '@pages/shop/api/delivery';
import { useQuery } from '@tanstack/vue-query';
import { useShopStore } from '../../store';
import { formatDateTime } from '@utils/date';
import { IntraCityDeliveryTemplate, ListIntraCityDeliveryTemplateRequest } from '@/types/api/store/delivery.model';
import to from 'await-to-js';
import { refDebounced } from '@vueuse/core';
import { highlightKeyword } from '@utils/index';
import TooltipOverflow from '@components/common/TooltipOverflow.vue';

const RTableRef = ref(null);
const isNoData = ref(false);
const relatedProductsRef = ref<InstanceType<typeof RelatedProducts>>();
const deliveryTemplateDialogRef = ref<InstanceType<typeof DeliveryTemplateDialog>>();
const shopStore = useShopStore();

const me = {
  openId: shopStore.openId,
  teamId: shopStore.teamId,
  cardId: shopStore.cardId,
};

// 操作函数
const handleCreateTemplate = () => {
  currentTemplate.value = null;
  deliveryTemplateDialogRef.value?.open();
};

const handleLinkProducts = (template: IntraCityDeliveryTemplate) => {
  if (template?.relationCount === 0 || (template?.relationCount && template?.relationCount < 0)) {
    return;
  }
  console.log('关联商品', template);
  relatedProductsRef.value?.open(template);
};

const currentTemplate = ref<IntraCityDeliveryTemplate | null>(null);
const handleEdit = (template: IntraCityDeliveryTemplate) => {
  console.log('编辑模板', template);
  currentTemplate.value = template;
  deliveryTemplateDialogRef.value?.open(template);
};

const handleDelete = async (template: IntraCityDeliveryTemplate) => {
  if ((template?.relationCount || 0) > 0) {
    MessagePlugin.warning('模板已关联商品，暂不支持删除');
    return;
  }
  const dialog = DialogPlugin.confirm({
    header: '提示',
    theme: 'info',
    body: '确定删除该模板吗？',
    closeBtn: false,
    onConfirm: async () => {
      dialog.destroy();
      const params = {
        templateId: {
          id: Number(template.id),
        },
        me,
      };
      const [err] = await to(delIntraCityDeliveryTemplate(params));
      if (err) {
        MessagePlugin.error(err.message || '删除失败，请重试');
        return;
      }
      MessagePlugin.success(`删除成功`);
      refetch();
    },
  });
};

const handleSetDefault = async (template: IntraCityDeliveryTemplate) => {
  const params = {
    template: {
      ...template,
      isDefault: true,
    },
    me,
  };
  const [err] = await to(setIntraCityDeliveryTemplate(params));
  if (err) {
    MessagePlugin.error(err.message || '设置默认模板失败，请重试');
    return;
  }
  refetch();
};

// 模板Dialog事件处理
const handleTemplateConfirm = async () => {
  refetch();
};

const pageInfo = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});

const change = (info: any) => {
  name.value = info.filter.searchVal;

  pageInfo.value.current = info.pageInfo.current;
  pageInfo.value.pageSize = info.pageInfo.pageSize;
};

const name = ref('');
const searchValueDebounced = refDebounced(name, 500);
// 计算查询参数，集中管理所有参数
const queryParams = computed(
  (): ListIntraCityDeliveryTemplateRequest => ({
    'storeId.id': shopStore.store?.id,
    'pagination.size': pageInfo.value.pageSize,
    'pagination.number': pageInfo.value.current,
    'me.cardId': shopStore.cardId,
    'me.openId': shopStore.openId,
    'me.teamId': shopStore.teamId,
    name: searchValueDebounced.value,
  }),
);

// 使用useQuery获取数据
const { data, isLoading, refetch } = useQuery({
  queryKey: ['delivery-template-list', queryParams],
  queryFn: () => getIntraCityDeliveryTemplateList(queryParams.value),
  retry: false,
  refetchOnWindowFocus: false,
  refetchOnReconnect: false,
  refetchOnMount: true,
});
// RTable 配置
const filter = {
  attrs: {
    size: 'small',
    labelWidth: '80px',
    placeholder: '搜索模板名称',
  },
};

const table = ref({
  attrs: {
    'row-key': 'id',
    loading: isLoading,
    tableLayout: 'auto',
    hover: true,
  },
  list: [],
  columns: [
    {
      title: '模板名称',
      colKey: 'templateName',
      width: 350,
      cell: (h: any, { row }: { row: IntraCityDeliveryTemplate }) => {
        return <div class="ellipsis-1" v-html={highlightKeyword(row.templateName, searchValueDebounced.value)}></div>;
      },
    },
    {
      title: '创建人',
      colKey: 'creatorName',
      width: 160,
      cell: (h: any, { row }) => {
        return <TooltipOverflow class-name="ellipsis-1" text={row.creatorName} />;
      },
    },
    {
      title: '编辑时间',
      colKey: 'editTime',
      width: 160,
      cell: (h: any, { row }: { row: IntraCityDeliveryTemplate }) => {
        return <span class="time-text">{formatDateTime(Number(row.updatedAt) * 1000)}</span>;
      },
    },
    {
      title: '操作',
      colKey: 'operate',
      cell: (h: any, { row }: { row: IntraCityDeliveryTemplate }) => {
        return (
          <div class="opt-box">
            <div class={row?.relationCount <= 0 ? 'disabled' : 'opt-hover'} onClick={() => handleLinkProducts(row)}>
              关联商品
            </div>
            <div class="opt-hover" onClick={() => handleEdit(row)}>
              编辑
            </div>
            <div class="opt-hover" onClick={() => handleDelete(row)}>
              删除
            </div>
            {row.isDefault ? (
              <t-tag theme="warning" variant="light" size="small" class="default-tag">
                默认模板
              </t-tag>
            ) : (
              <div class="opt-hover" onClick={() => handleSetDefault(row)}>
                默认模板
              </div>
            )}
          </div>
        );
      },
    },
  ],
  pagination: pageInfo.value,
});

// 响应式更新表格数据
watch(
  data,
  (newData: any) => {
    const responseData = newData?.data;
    if (responseData?.templates?.length) {
      table.value.list = responseData.templates;
      pageInfo.value.total = responseData.pagination.total;
    } else {
      table.value.list = [];
      pageInfo.value.total = 0;
      isNoData.value = !!searchValueDebounced.value;
    }
  },
  { deep: true },
);

onMounted(() => {
  console.log('配送模板页面已挂载');
});
</script>

<style scoped lang="less">
:deep(.opt-box) {
  display: flex;
  align-items: center;
  height: 22px;
  gap: 8px;
  .disabled {
    color: #c9cfff;
    padding: 4px;
    cursor: not-allowed;
  }
  .opt-hover {
    color: #4d5eff;
    width: max-content;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    &:hover {
      background-color: #f3f6fa;
    }
  }
}
.delivery-template-wrapper {
  position: relative;
  height: 100%;
  padding: 0 16px;
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .RTable {
    flex: 1;
  }
}
:deep(.t-button .t-button__text) {
  align-items: center;
}
</style>
