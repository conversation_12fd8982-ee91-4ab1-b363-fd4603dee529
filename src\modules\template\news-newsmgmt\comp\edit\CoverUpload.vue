<template>
  <FormField
    :label="config.text.coverLabel"
    :name="name"
    :required="required"
    :tooltip="tooltip"
    :hint="hint"
    :width="width"
    :show-label="false"
  >
    <div class="cover-upload">
      <div class="cover-upload__label">
        <span v-if="required" class="cover-upload__required">*</span>
        <span>{{ config.text.coverLabel }}</span>
      </div>
      <div class="cover-upload__content !items-end">
        <div class="cover-upload__box" @click="handleUploadClick">
          <t-image v-if="modelValue" :src="modelValue" fit="cover" class="cover-upload__image" />
          <div v-else class="cover-upload__placeholder">
            <t-image :src="config.defaultCover" fit="cover" class="cover-upload__image" />
          </div>
          <div class="cover-upload__mask">
            <span>更新封面</span>
          </div>
        </div>

        <div class="text-[#ACB3C0] text-12 leading-20">
          <div>温馨提示：</div>
          <div>1、建议上传图片尺寸1080*720，大小不超2M</div>
          <div>2、支持格式： jpg/jpeg/png/gif/bmp/webp</div>
        </div>
      </div>
    </div>
  </FormField>
  <RCropperDialog
    ref="cropperRef"
    :size-limit="{
      size: sizeLimit,
      message: '文件过大，请上传{sizeLimit}M以下图片',
    }"
    :options="{ aspectRatio: 4 / 3 }"
    :accept="acceptTypes"
    @confirm="cropperConfirm"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { RCropperDialog } from '@rk/unitPark';
import { FormField } from '@components/form';
import { EditConfig } from '@modules/template/news-newsmgmt/types.ts';

interface Props {
  modelValue: string;
  name?: string;
  required?: boolean;
  sizeLimit?: number;
  acceptTypes?: string;
  tooltip?: string;
  hint?: string;
  width?: string | number;
  defaultCover?: string;
  config: EditConfig['config'];
}

const props = withDefaults(defineProps<Props>(), {
  name: 'cover',
  required: false,
  sizeLimit: 2,
  acceptTypes: () => 'image/jpg,image/jpeg,image/png,image/gif,image/bmp,image/webp',
  tooltip: '',
  hint: '',
  width: '100%',
  defaultCover: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/h5/culture/news_default.png',
  config: () => {},
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
  change: [value: string];
}>();

const cropperRef = ref<InstanceType<typeof RCropperDialog>>();

const handleUploadClick = () => {
  cropperRef.value?.open(props.modelValue || '');
};

const cropperConfirm = (url: string) => {
  emit('update:modelValue', url);
  emit('change', url);
};
</script>

<style lang="less" scoped>
.cover-upload {
  width: 100%;

  &__label {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
    color: var(--text-kyy_color_text_3, #828da5);
    font-size: 14px;
    font-weight: 400;
  }

  &__required {
    color: var(--kyy_color_error, #d54941);
  }

  &__content {
    display: flex;
    align-items: flex-start;
    gap: 16px;
  }

  &__box {
    position: relative;
    width: 128px;
    height: 96px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    background: var(--kyy_color_upload_bg, #fff);

    &:hover {
      .cover-upload__mask {
        display: flex;
      }
    }
  }

  &__image {
    width: 100%;
    height: 100%;
  }

  &__placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: var(--kyy_color_upload_text_default, #516082);
    font-size: 14px;
    gap: 4px;
  }

  &__mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    display: none;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 14px;

    span {
      display: inline-flex;
      padding: 4px 8px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      border-radius: 4px;
      background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.5));
      color: #fff;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
}
</style>
