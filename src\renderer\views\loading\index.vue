<template>
  <div id="loading-box">
    <span class="loader"></span>
    <div class="loader-text">加载中...</div>
  </div>
</template>

<script setup lang="ts">

</script>

<style lang="less" scoped>
#loading-box {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: center;
  align-items: center;
  user-select: none;
  // background-color: rgba(255,255,255,.7);
  // background-color: #fff;
}
/* HTML: <div class="loader"></div> */
/* HTML: <div class="loader"></div> */
.loader {
  width: 28px;
  padding: 8px;
  aspect-ratio: 1;
  border-radius: 50%;
  background:  #4C5EFF;
  --_m:
    conic-gradient(#0000 10%,#000),
    linear-gradient(#000 0 0) content-box;
  -webkit-mask: var(--_m);
          mask: var(--_m);
  -webkit-mask-composite: source-out;
          mask-composite: subtract;
  animation: l3 1s infinite linear;
}
@keyframes l3 {to{transform: rotate(1turn)}}
.loader-text {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  text-align: left;
}
</style>
