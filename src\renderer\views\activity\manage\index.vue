<template>
  <div
    class="activity-manage-wrap"
    :class="{ 'manage-scene-wrapper': isAloneWindow, 'manage-scene-wrapper-edit-disabled': !isAllowEdit }"
  >
    <div class="left-box w-200 bg-[#fff] flex flex-col shrink-0">
      <div class="sidebar" @scroll="onSideBarScroll">
        <div v-for="item in manageMenus" :key="item" class="mb-8">
          <div class="title h-40 flex-y-center px-8 mb-8">
            <img class="w-20 h-20" :src="item.icon" alt="">
            <div class="mx-8 flex-1">{{ item.name }}</div>
          </div>
          <div v-for="subitem in item.sub" :key="subitem">
            <div
              class="h-40 flex-y-center pl-36 pr-8 mb-8 item"
              :class="{ active: subitem.key === activePanelKey }"
              @click="changePanel(subitem.key, subitem.hasPermission)"
            >
              {{ subitem.name }}
            </div>
          </div>
        </div>
      </div>

      <div class="action-bar px-12 py-8 flex justify-center items-center gap-16">
        <activity-manage-button-dropdown
          :permission="operatePermission"
          @del="del"
          @cancel-success="init"
          @change-panel="key => changePanel(key, true)"
        />
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <div v-if="loading" class="p-12 h-full">
        <div class="rounded-8 p-12 bg-[#fff] h-full">
          <detail-skeleton />
        </div>
      </div>
      <div v-else class="h-full overflow-hidden" :class="{ 'w-1016': isAloneWindow }">
        <template v-if="activePanelKey === 'goodsServe'">
          <div class="iframe-wrap">
            <DynamicIframe :key="goodsServerIframe" class="goodsServerIframe" :src="goodsServerIframe" />
          </div>
        </template>
        <template v-else>
          <keep-alive :include="keepAliveComponents">
            <component
              :is="managePanelComponentMap[activePanelKey]"
              :activity-id="activityId"
              :data="activityDetailData"
              @update="init"
              @change-panel="key => changePanel(key, true)"
            >
              <template v-if="isScene" #scene>
                <div class="scene-box px-24 py-8 w-full mb-16 bg-[#EAECFF] rounded-8 flex gap-8">
                  <span class="text-[#1A2139]">来自{{ sceneConversationType === '1' ? '单聊' : '群聊' }}：{{ sceneConversationName }}</span>
                  <span class="text-[#4D5EFF] cursor-pointer" @click="openSceneConversation">点击查看</span>
                </div>
              </template>
            </component>
          </keep-alive>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onActivated, onBeforeUnmount, onDeactivated, onMounted, provide, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import _ from 'lodash';
import { jsbridge } from '@rk/unitPark';
import LynkerSDK from '@renderer/_jssdk';
import { liteMenus, managePanelComponentMap, menus, professionalMenus } from './constant';
import { useActivityStore } from '@/views/activity/store';
import { activityDelete, getActivityDetail, getActivityListActors, myMenu } from '@/api/activity';
import { getOpenid } from '@/utils/auth';
import { initialRegisterForms } from '@/views/activity/utils';
import ActivityManageButtonDropdown from '@/views/activity/manage/components/ActivityManageButtonDropdown.vue';
import { useActivityNavigate } from '../hooks';
import emitter from '@/utils/MittBus';
import DetailSkeleton from '@/views/activity/components/DetailSkeleton.vue';
import { openChat } from '@/utils/share';
import DynamicIframe from '@/components/DynamicIframe/index.vue';
import { checkMerchantStatus } from '@/api/activity/product';
import { useCheckAuth } from '../hooks/useCheckAuth';

const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();

const emit = defineEmits(['setActiveIndexAndName', 'settabItem', 'setActiveIndex', 'deltabItem']);

const route = useRoute();
const router = useRouter();

const { toComment } = useActivityNavigate();
// const iframeUrl = 'http://*************:8080/goodsServe/index.html#/manage';
const iframeUrl = `${LynkerSDK.getH5Url('goodsServe/index.html#/manage')}`;

const authParams = ref('');
const teamIdRef = ref(route.query.teamId as string);
const { getAuthParams, checkStatusAndRedirect } = useCheckAuth({ teamId: teamIdRef });

const goodsServerIframe = computed(() => {
  const goodsPermissionKey = 2; // 商品服务权限key
  const goodsOperatePermissionValue = 1 << 1; // 修改权限值
  const hasGoodsOperatePermission = (myPermission.value[goodsPermissionKey] & goodsOperatePermissionValue) === goodsOperatePermissionValue;

  return `${iframeUrl}?teamId=${route.query.teamId}&cardId=${route.query.cardId}&openId=${getOpenid()}&activityId=${route.params.id}&hasOperatePermission=${hasGoodsOperatePermission}&isMerchantBind=${isMerchantBind.value}&${authParams.value}`;
});

const keepAliveComponents = [
  'ManageActivityBasic',
  'ManageActivityParticulars',
  'ManageActivityMembers',
  'ManageActivityProcess',
  'ManageActivityAdvanced',
  'ManageActivityLiteInfo',
];

// 加载状态
const loading = ref(false);

// 此活动id
const activityId = Number(route.params.id);
// 此活动的归属组织，个人身份创建的活动teamId为空
const teamId = route.query.teamId;
// 当前在此活动下的身份卡id，个人身份创建的活动cardId为空
const cardId = route.query.cardId;
// 活动创建类型 0-标准版 1-极速版 2-专业版
const mold = Number(route.query.mold);
// 当前是否是专业版活动
const isProfessional = route.query?.mold === '2';

// 是否是独立窗口打开（创建活动isScene时必定是独立弹窗，而此处也可以在活动应用中打开，因此通过路径判断），独立窗口会另外应用一套样式
const isAloneWindow = route.path.includes('layoutActivity');
// 是否是场景新建活动，这里通过查询到的活动详情中是否有chatId来判断
const isScene = ref(null);
// 场景活动会话类型 1单聊 3群聊
const sceneConversationType = ref(null);
// 场景活动会话名称
const sceneConversationName = ref(null);
// 场景新建活动会话id
const sceneConversationId = ref(null);

// 当前组织是否已经绑定组织收款
const isMerchantBind = ref(false);

const activityStore = useActivityStore();

const manageMenus = computed(() => {
  let list:any = [];

  if (mold === 2) {
    // 专业版活动，根据权限值过滤
    list = professionalMenus.map((menu) => {
      const groupPermissionValue = myPermission.value[menu.permissionKey];
      return {
        ...menu,
        sub: menu.sub.map((subMenu) => ({ ...subMenu, hasPermission: (groupPermissionValue & subMenu.permissionValue) === subMenu.permissionValue })),
      };
    });
  } else if (mold === 0) {
    list = menus;
  } else {
    list = liteMenus;
  }

  // 非个人的入口
  const keysList = ['fee', 'goodsServe'];
  // 如果当前活动归属是个人，那要过滤掉 费用明细 和 商品/服务  这两个菜单
  list = list
    .map((menu) => {
      // 先过滤子菜单
      const filteredSub = menu.sub.filter((subMenu) => {
        const isExcludedMenu = keysList.includes(subMenu.key);
        return !(isExcludedMenu && !teamId);
      });
      return { ...menu, sub: filteredSub };
    })
    // 再过滤空父菜单
    .filter((menu) => menu.sub.length > 0);

  return list;
});

const activePanelKey = ref(null);

// 活动详情数据
const activityDetailData = ref({});

// 专业版活动个人权限值
const myPermission = ref({
  1: 0,
  2: 0,
  3: 0,
  4: 0,
  5: 0,
  6: 0,
});

// 操作按钮权限值
const operatePermission = computed(() => {
  if (mold === 2) {
    // 操作按钮权限key
    const operatePermissionKey = 6;
    return myPermission.value[operatePermissionKey];
  }
  return 0;
});

// 当前活动是否可修改（未开始和进行中的活动可修改，已结束或已取消的活动不可修改）
const isAllowEdit = computed(
  () => activityDetailData.value.basic?.timeStatus === 'TimeUnderway'
    || activityDetailData.value.basic?.timeStatus === 'TimeFuture',
);

// 记录组件是否正在编辑中
const componentEditingState = reactive({
  basic: false,
  particulars: false,
  process: false,
  advanced: false,
  liteInfo: false,
});

const hasPermissionWithKey = (key) => {
  if (mold === 2) {
    // 查找该key对应的菜单项和权限
    const menuItem = manageMenus.value.find((menu) => menu.sub.some((subMenu) => subMenu.key === key));
    if (menuItem) {
      const subMenuItem = menuItem.sub.find((subMenu) => subMenu.key === key);
      if (subMenuItem && !(myPermission.value[menuItem.permissionKey] & subMenuItem.permissionValue)) {
        MessagePlugin.warning('暂无权限');
        return false;
      }
    }
  }
  return true;
};

// 存储路由参数，用于传递给子组件
const tabParams = ref({
  keyword: '',
  key: '',
  // 用于标记 jsbridge 参数已设置
  loaded: false,
});

// key和params的类型
type KeyParams = {
  keyword: string;
  [key: string]: any;
};

const changeTabParams = (params: KeyParams) => {
  tabParams.value = {
    ...tabParams.value,
    ...params,
  };
};

const changePanelWithParams = (key: string, params: KeyParams) => {
  if (!key) {
    return;
  }
  // 专业版活动需判断权限
  const hasPermission = hasPermissionWithKey(key);
  if (!hasPermission) {
    return;
  }
  activePanelKey.value = key;

  tabParams.value = {
    key,
    loaded: true,
    ...params,
  };
};

jsbridge.useJsBridge((data:any) => {
  const action = data?.action;
  if (action === 'goods-info') {
    changePanelWithParams('fee', { keyword: data?.data.title });
  }

  // 主办方-活动详情-商品/服务-跳转商户入网申请
  if (action === 'goods:receipt-redirect') {
    checkStatusAndRedirect();
  }
});

// 注入是否是活动详情管理
provide('isManage', true);
// 注入是否专业版活动
provide('isProfessional', isProfessional);
// 注入活动创建类型
provide('mold', mold);
// 注入活动详情数据
provide('activityDetailData', activityDetailData);
// 注入是否场景活动状态
provide('isScene', isScene);
// 注入活动是否可修改状态
provide('isAllowEdit', isAllowEdit);
// 注入组件是否正在编辑中，由子组件更新这个状态
provide('componentEditingState', componentEditingState);
// 注入个人权限值
provide('myPermission', myPermission);
// 注入路由参数
provide('tabParams', tabParams);
// 注入路由参数
provide('changeTabParams', changeTabParams);

const changePanel = (key, hasPermission = true) => {
  if (mold === 2) {
    if (!hasPermission) {
      MessagePlugin.warning('暂无权限');
      return;
    }
  }

  if (key === 'comment') {
    // 点击评论管理，跳转活动详情
    toComment(activityDetailData.value, null, isAloneWindow);
    return;
  }
  activePanelKey.value = key;
};

const init = async () => {
  loading.value = true;

  await activityStore.getGroupList();

  const res = await getActivityDetail(activityId, {
    openId: getOpenid(),
    teamId,
    cardId,
  });
  const data = res.data.data;

  isScene.value = !!data.chatId;

  // 没有活动归属，则默认选中个人
  data.basic.teamId = data.basic.teamId || getOpenid();
  data.basic.trueTeamId = data.basic.teamId || teamId;
  // 处理分类id数据类型
  data.basic.categoryId = data.basic.categoryId ? data.basic.categoryId.toString() : null;

  // 将时间戳都转为int（报名时间如果为0，则转为null）
  data.basic.duration.startTime = Number(data.basic.duration.startTime);
  data.basic.duration.endTime = Number(data.basic.duration.endTime);
  data.advanced.registerTime.startTime = data.advanced.registerTime.startTime === '0' ? null : Number(data.advanced.registerTime.startTime);
  data.advanced.registerTime.endTime = data.advanced.registerTime.endTime === '0' ? null : Number(data.advanced.registerTime.endTime);

  // 报名费相关字段数据类型处理
  if (data.advanced.registFeeEnable) {
    data.advanced.registFee.value = Number(data.advanced.registFee.value);
  } else {
    data.advanced.registFee = {
      currency: 'CNY',
      value: null,
    };
  }

  if (mold === 0 || mold === 2) {
    // 标准版或专业版处理以下字段
    // 如果报名人数返回为0，则设置为null
    data.advanced.quota = data.advanced.quota || null;
    // 未开启报名，则使用初始化的报名表单
    if (data.advanced.openRegist) {
      data.advanced.registerForms = data.advanced.registerForms.map((registerFormItem) => ({
        ...registerFormItem,
        // 设置姓名和手机号默认禁用
        disabled: registerFormItem.type === 'TypeName' || registerFormItem.type === 'TypePhone',
      }));
    } else {
      data.advanced.registerForms = _.cloneDeep(initialRegisterForms);
    }
    data.process.processItems = data.process.processItems.map((processItem) => ({
      ...processItem,
      lastTime: Number(processItem.lastTime),
    }));
  } else {
    // 极速版在初始时查询参与人员
    const res = await getActivityListActors({
      activityId,
      replyStatus: '-1', // 固定查询全部报名状态
      role: -2, // 固定查询普通参与者与报名人员
      approveStatus: -1, // 固定查询普通参与者与报名人员
    });

    data.members = {
      actors: res.data.data.actors.map((actor) => ({
        ...actor,
        targetId: actor.cardId || actor.openId,
      })),
      guests: [],
      staffs: [],
    };
  }

  // 场景活动会话信息赋值
  if (isScene.value) {
    sceneConversationType.value = data.chatId.groupId ? '3' : '1';
    sceneConversationName.value = data.chat.name;
    sceneConversationId.value = data.chatId[sceneConversationType.value === '1' ? 'cardId' : 'groupId'];
  }

  activityDetailData.value = data;

  try {
    authParams.value = await getAuthParams();
  } catch (error) {
    console.error('获取权限参数失败', error);
  }

  loading.value = false;
};

// 专业版活动获取权限
const getPermission = async () => {
  await getMyPermission();

  await nextTick();

  // 定位到第一个有权限的菜单
  activePanelKey.value = manageMenus.value.find((menu) => menu.sub.find((subMenu) => subMenu.hasPermission))?.sub[0].key;
};

// 专业版活动查询菜单权限
const getMyPermission = async () => {
  const res = await myMenu({
    activityId,
    'me.openId': getOpenid(),
    'me.teamId': teamId,
    'me.cardId': cardId,
  });

  const permission = res.data.data.permission;
  if (permission) {
    myPermission.value = JSON.parse(permission);
  }
};

// 删除活动
const del = async () => {
  const confirmDia = DialogPlugin.confirm({
    header: activityDetailData.value.advanced.registFeeEnable ? '删除活动后不可恢复，确定删除？' : t('activity.activity.tip'),
    theme: 'info',
    body: activityDetailData.value.advanced.registFeeEnable ? '若需进行退款请前往【活动管理-费用明细】进行退款操作' : '删除活动后不可恢复，确定删除？',
    confirmBtn: '确定删除',
    cancelBtn: '取消',
    onConfirm: async () => {
      confirmDia.destroy();

      await activityDelete({
        id: activityId,
        openId: getOpenid(),
        teamId,
        cardId,
      });

      MessagePlugin.success('删除成功');

      if (isScene.value) {
        ipcRenderer.invoke('refresh-scence-activity');
      }

      closeTab();
    },
    onCancel: () => {
      confirmDia.destroy();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
    },
  });
};

// 打开场景活动会话
const openSceneConversation = () => {
  openChat({
    main: activityDetailData.value.basic?.cardId || getOpenid(),
    [sceneConversationType.value === '1' ? 'peer' : 'group']: sceneConversationId.value,
  });
};

// 获取当前选择的组织是否开通组织收款
const getSelectedTeamMerchantStatus = async () => {
  const res = await checkMerchantStatus({
    teamId,
  });
  isMerchantBind.value = res.data.data.result;
};

const setTabItem = () => {
  if (!activityStore.tabList?.some((tab) => tab.tag === route.path)) {
    const newTabItem = {
      path: route.path,
      name: route.path,
      title: route.query.subject,
      query: route.query,
      tag: route.path,
      type: 13,
    };

    emit('settabItem', newTabItem);
    emit('setActiveIndex', activityStore.tabList.length - 1);
  }
};

// 关闭当前tab
const closeTab = (path = route.path) => {
  if (isScene.value && route.path.includes('layoutActivity')) {
    // 场景活动独立弹窗，则关闭弹窗
    ipcRenderer.invoke('close-dialog');
  } else {
    const index = activityStore.tabList.findIndex((e) => e.path === path);
    emit('deltabItem', index, true);
  }
};

// 监听tab关闭（因为在两个生命周期都进行了监听，可能导致重复触发，使用debounce避免）
const onDelTab = _.debounce(async (path) => {
  const currentTabPath = `/activity/manage/${activityId}`;
  // 如果关闭的不是当前tab，不用处理
  if (path !== currentTabPath) {
    return;
  }

  // 判断当前是否由处于编辑中的组件，如果由则需要用户确认，如果路由不在当前tab，先定位到当前tab，再弹窗
  if (
    componentEditingState.basic
    || componentEditingState.particulars
    || componentEditingState.process
    || componentEditingState.advanced
    || componentEditingState.liteInfo
  ) {
    if (route.path !== currentTabPath) {
      await router.push({
        path: currentTabPath,
        query: route.query,
      });
    }
    const confirmDia = DialogPlugin.confirm({
      header: t('activity.activity.tip'),
      theme: 'info',
      body: '确认离开此页面，离开后编辑中数据将不被保存',
      confirmBtn: '离开',
      cancelBtn: '取消',
      onConfirm: () => {
        closeTab(path);
        confirmDia.destroy();
      },
      onCancel: () => {
        confirmDia.destroy();
      },
      onCloseBtnClick: () => {
        confirmDia.destroy();
      },
    });
  } else {
    closeTab(path);
  }
}, 100);

// 左侧滚滑动条滚动事件
const onSideBarScroll = () => {
  const sidebar = document.querySelector('.sidebar');
  if (sidebar.scrollTop > 0) {
    // 显示滚动条
    sidebar.classList.add('scrolled');
  } else {
    // 隐藏滚动条
    sidebar.classList.remove('scrolled');
  }
};

onMounted(() => {
  if (route.query?.panelRedirect) {
    activePanelKey.value = route.query?.panelRedirect;
  } else if (mold === 0) {
    activePanelKey.value = 'basic';
  } else if (mold === 1) {
    activePanelKey.value = 'liteInfo';
  }

  setTabItem();
  init();
  if (mold === 2) {
    getPermission();
    if (teamId) {
      // 如果是组织创建的专业版活动，查询其有没有开通组织收款
      getSelectedTeamMerchantStatus();
    }
  }
  emitter.on('del-activity-manage-tab', onDelTab);
  emitter.on('activity-manage-change-panel', changePanel);
});

onBeforeUnmount(() => {
  emitter.off('del-activity-manage-tab', onDelTab);
  emitter.off('activity-manage-change-panel', changePanel);
});

onActivated(() => {
  emitter.on('del-activity-manage-tab', onDelTab);
  emitter.on('activity-manage-change-panel', changePanel);
});

onDeactivated(() => {
  emitter.off('del-activity-manage-tab', onDelTab);
  emitter.off('activity-manage-change-panel', changePanel);
});
</script>

<style scoped lang="less">
.activity-manage-wrap {
  display: flex;
  width: 100%;
  background: #f5f8fe;
  height: calc(100vh - 40px);

  :deep(.activity-lite-create-form-group){
    padding: 0;
    margin: 0;
  }
}
.iframe-wrap {
  margin: 12px;
  border-radius: 8px;
  background: #fff;
  height: -webkit-fill-available;
  width: auto;
  .goodsServerIframe {
    height: 100%;
  }
}
.manage-scene-wrapper {
  background: url('https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/caadb0b/bg.png') no-repeat center / 100% 100%;
  padding: 12px 0;
  justify-content: center;

  .left-box {
    height: 636px;
    border-radius: 8px;
    border-right: none;
  }

  .content {
    flex: none;
  }

  :deep(.activity-manage-content) {
    padding-top: 0;
    padding-right: 0;
  }

  :deep(.operate-box) {
    box-shadow: none;
    border-radius: 8px;
    margin-left: 12px;
  }

  &.manage-scene-wrapper-edit-disabled {
    :deep(.activity-manage-content) {
      padding-bottom: 0;
    }
  }
}

.left-box {
  border-right: 1px solid #eceff5;
}
/* 左侧菜单栏 */
.sidebar {
  padding: 16px;
  flex: 1;
  overflow-y: overlay;

  &.scrolled {
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
  }

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .title {
    color: #222;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
  .item {
    color: #1a2139;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
    cursor: pointer;
    &.active {
      border-radius: 4px;
      background: #e1eaff;
      color: #4d5eff;
    }
    &:hover {
      border-radius: 4px;
      background: #e1eaff;
      color: #4d5eff;
    }
  }
}

:deep(.action-bar) {
  border-top: 1px solid #eceff5;
}

/* 内容区域 */
.content {
  flex: 1;

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
  }
}
</style>
