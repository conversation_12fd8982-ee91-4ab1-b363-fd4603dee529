<template>
  <div class="order-page">
    <!-- 状态标签 -->
    <orderTabs ref="orderTabsRef" :tabs="tabs" :tab-current-index="tabCurrentIndex" @tab-change="tabChange" />
    <!-- 搜索框 -->
    <div class="search-bar">
      <div class="left">
        <t-input v-model="orderno" style="width: 304px" clearable placeholder="搜索订单编号" @change="titleChange">
          <template #prefix-icon>
            <iconpark-icon name="iconsearch" class="icon20" style="color: #828da5" />
          </template>
        </t-input>
        <superSearch
          :filter-params="filterParams"
          :active="filterParamsValue.length > 0"
          @search="search"
          @reset="reset"
        />
      </div>
      <div class="right">
        <t-button v-if="props.orderType === 'pickup'" theme="primary" @click="codeVerify">取货码验证</t-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-box">
      <div v-if="filterParamsValue.length" class="filter-bar">
        <div class="filter-res">
          <div class="tit">高级筛选结果：</div>
          <div v-for="item in filterParamsValue" :key="item.key" class="stat te">
            <span>
              <span>{{ item.label }}：</span>
              <span v-if="item.type === 'input'">{{ item.value }}</span>
              <span v-if="item.type === 'date'">{{ item.value[0] }} ~ {{ item.value[1] }}</span>
              <span v-if="item.type === 'select'">{{ getValueByOpt(item.options, item.value) }}</span>
            </span>
            <span class="close2" @click="clearFilter(item.key)">
              <iconpark-icon name="iconerror" style="font-size: 20px" />
            </span>
          </div>
          <div class="icon" @click="clearAllFilters">
            <iconpark-icon name="icondelete" style="font-size: 20px; color: #4d5eff" />
            <a>清空</a>
          </div>
        </div>
      </div>

      <t-table :data="tableData" :columns="columns" row-key="orderNumber" @row-click="toDetail">
        <template #sn="{ row }">
          <div class="order-number">
            <span>{{ row.sn }}</span>
            <div v-if="row.refund_status === 1" class="status-rd">退款中</div>
            <span v-if="row.refund_status === 3" class="status-rd">退款成功</span>
            <span v-if="row.refund_status === 2" class="status-rd">部分退款</span>
          </div>
        </template>

        <template #buyer_team_name="{ row }">
          <t-tooltip :show-arrow="true" :content="row.buyer_team_name || row.buyer_name">
            <div class="buyerbox">
              {{ row.buyer_team_name || row.buyer_name }}
            </div>
          </t-tooltip>
        </template>
        <template #orderTime="{ row }">
          <span>{{ dayjs(row.created_at * 1000).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
        <template #origin="{ row }">
          <span>{{ row.origin === 'store_product' ? '店铺订单' : '活动收款' }}</span>
        </template>
        <template #paymentAmount="{ row }">
          <span>￥{{ addCommasToNumber(row.pay_amount) }}</span>
        </template>
        <template #productInfo="{ row }">
          <p v-if="props.orderType === 'pickup'">店铺订单自提</p>
          <p v-if="props.orderType === 'delivery'">店铺订单配送</p>
          <p v-if="props.orderType === 'logistics'">店铺订单物流</p>
          <div class="temname">{{ row.store_name }}</div>
        </template>
        <template #status="{ row }">
          <span :class="`status-text${row.status}`">{{ statusText(row.status) }}</span>
        </template>
        <template #operation="{ row }">
          <!-- <slot name="operation" :row="row"></slot> -->
          <template v-if="orderType === 'pickup'">
            <div v-if="row.status === 2" class="abtn" @click.stop="pickupReceivingRun(row)">接单</div>
            <div v-if="false" class="abtn" @click.stop="pickupCompleteRun(row)">完成订单</div>
            <div v-if="row.status === 4" class="abtn" @click.stop="pickupShipmentRun(row)">出货</div>
            <div
              v-if="![1, 0].includes(row.status) && row.refund_status !== 3"
              class="abtn"
              @click.stop="openRefundDrawer(row)"
            >
              申请退款
            </div>
          </template>
          <template v-else-if="orderType === 'delivery'">
            <div v-if="row.status === 2" class="abtn" @click.stop="deliveryReceiving(row)">接单</div>
            <div v-if="row.status === 4 && row.delivery_type === 1" class="abtn" @click.stop="rider2sellerRun(row)">
              商家自配
            </div>

            <div v-if="row.status === 4 && row.delivery_type === 0" class="abtn" @click.stop="seller2riderRun(row)">
              骑手配送
            </div>
            <div v-if="row.status === 4" class="abtn" @click.stop="pickupShipmentRun(row)">出货</div>
            <div v-if="false" class="abtn" @click.stop="deliveryCompleteRun(row)">完成订单</div>
            <div
              v-if="![1, 0].includes(row.status) && row.refund_status !== 3"
              class="abtn"
              @click.stop="openRefundDrawer(row)"
            >
              申请退款
            </div>
          </template>
          <template v-else>
            <div v-if="row.status === 7" class="abtn" @click.stop="openLogisticsSet(row, 1)">发货</div>
            <div v-if="row.status === 8" class="abtn" @click.stop="openLogisticsSet(row, 2)">修改物流</div>
            <div v-if="row.status === 8 || row.status === 100" class="abtn" @click.stop="openLogisticsInfo(row)">
              查看物流
            </div>
            <div
              v-if="![1, 0].includes(row.status) && row.refund_status !== 3"
              class="abtn"
              @click.stop="openRefundDrawer(row)"
            >
              申请退款
            </div>
          </template>
        </template>

        <template #empty>
          <div class="empty-box">
            <REmpty
              :name="filterParamsValue.length || orderno ? 'no-result' : 'no-data'"
              :tip="filterParamsValue.length || orderno ? '搜索无结果' : '暂无数据'"
            />
          </div>
        </template>
      </t-table>
      <div v-if="totalItems > 10" class="pagination">
        <t-pagination
          :total="totalItems"
          :page-size="pageSize"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          show-jumper
          @change="handlePageChange"
        />
      </div>
    </div>
    <!-- 分页 -->

    <!-- 配送方式  -->
    <deliveryMethod ref="deliveryMethodRef" @refresh="refreshRun" />
    <!-- 配送方式  -->
    <!-- 骑手改自配 -->
    <modifyDelivery ref="modifyDeliveryRef" @refresh="refreshRun" />
    <!-- 骑手改自配 -->

    <!-- 配送订单完成  -->
    <deliveryComplete ref="deliveryCompleteRef" />
    <!-- 配送订单完成  -->

    <!-- 提货码验证 -->
    <pickupCode ref="pickupCodeRef" @refresh="refreshRun" />
    <!-- 提货码验证 -->

    <!-- 退款 -->
    <RefundDrawer ref="RefundDrawerRef" @refresh="refreshRun" />
    <!-- 退款 -->

    <!-- 物流发货 -->
    <logisticsSet ref="logisticsSetRef" @refresh="refreshRun" />
    <!-- 物流发货 -->
    <!-- 物流信息 -->
    <logisticsLog ref="LogisticsInfoRef" />
    <!-- 物流信息 -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import superSearch from './superSearch.vue';
import orderTabs from './orderTabs.vue';
import deliveryMethod from './delivery/deliveryMethod.vue';
import deliveryComplete from './delivery/deliveryComplete.vue';
import pickupCode from './pickup/pickupCode.vue';
import RefundDrawer from './refund/RefundDrawer.vue';
import logisticsSet from './logistics/logisticsSet.vue';
import logisticsLog from './logistics/logisticsLog.vue';
import { pickupShipment, pickupComplete, pickupReceiving } from './pickup/func';
import MessagePlugin from 'tdesign-vue-next';
import { sellerOrderList, sellerOrderStatusStatistics } from '../apis/index';
import { seller2rider } from './delivery/func';
import modifyDelivery from './delivery/modifyDelivery.vue';
import { REmpty } from '@rk/unitPark';

import sdk from '@lynker-desktop/web';
import { addCommasToNumber } from '@pages/common/utils/index';
import shopIcon from '@assets/shop/shop.svg';
import dayjs from 'dayjs';

const props = defineProps({
  tabsConfig: {
    type: Array,
    default: () => [],
  },
  filterParamsConfig: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
  apiUrl: {
    type: String,
    default: '',
  },
  orderType: {
    type: String,
    default: 'pickup',
  },
});
const tabsMap = {
  pickup: [
    { value: 9999, label: '全部', count: 0 },
    { value: 2, label: '待接单', count: 0 },
    { value: 4, label: '已接单', count: 0 },
    { value: 5, label: '已出货', count: 0 },
    { value: 100, label: '已完成', count: 0 },
    { value: 1, label: '待付款', count: 0 },
    { value: 0, label: '已关闭', count: 0 },
  ],
  delivery: [
    { value: 9999, label: '全部', count: 0 },
    { value: 2, label: '待接单', count: 0 },
    { value: 4, label: '已接单', count: 0 },
    { value: 5, label: '已出货', count: 0 },
    { value: 6, label: '配送中', count: 0 },
    { value: 100, label: '已完成', count: 0 },
    { value: 1, label: '待付款', count: 0 },
    { value: 0, label: '已关闭', count: 0 },
  ],
  logistics: [
    { value: 9999, label: '全部', count: 0 },
    { value: 7, label: '待发货', count: 0 },
    { value: 8, label: '已发货', count: 0 },
    { value: 100, label: '已完成', count: 0 },
    { value: 1, label: '待付款', count: 0 },
    { value: 0, label: '已关闭', count: 0 },
  ],
};

const tabs = ref(tabsMap[props.orderType]);
const filterParams = ref(props.filterParamsConfig);
const tabCurrentIndex = ref(0);
const orderno = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);
const tableData = ref([]);
const columns = ref(props.columns);
const emits = defineEmits(['refresh']);

onMounted(() => {
  fetchData();
});
const titleChange = () => {
  currentPage.value = 1;
  fetchData();
};
const orderTypeMap = {
  delivery: 1,
  pickup: 2,
  logistics: 3,
};
const orderTabsRef = ref(null);
const fetchData = async () => {
  const temp = {};
  filterParams.value.map((item) => {
    if (item.type === 'date') {
      temp[item.key] = item.value.join(',');
    } else if (item.type === 'input') {
      temp[item.key] = item.value.trim();
    } else {
      temp[item.key] = item.value;
    }
  });
  const reqData = {
    page: currentPage.value,
    pageSize: pageSize.value,
    keyword1: orderno.value,
    order_mode: orderTypeMap[props.orderType],
    status: tabCurrentIndex.value === 0 ? undefined : tabsMap[props.orderType][tabCurrentIndex.value].value,
    ...temp,
  };
  console.log(reqData, 'reqData');
  try {
    const res = await sellerOrderList(reqData);
    console.log(res, 'res');
    if (res?.data) {
      tableData.value = res?.data?.list || [];
      totalItems.value = res?.data?.total || 0;
    }
    const stParams = {
      ...reqData,
      status: undefined,
      statistic_status_list: tabs.value.map((item) => item.value),
    };
    const statistics = await sellerOrderStatusStatistics(stParams);
    if (statistics?.data) {
      tabs.value.forEach((item) => {
        statistics.data.items.forEach((statistic) => {
          if (item.value === statistic.status) {
            item.count = statistic.count;
          }
        });
      });
      orderTabsRef.value.updateTagPosition();
    }
  } catch (error) {
    MessagePlugin.error(error.response?.data?.message || '请求失败');
  }
};
const statusText = (status) => {
  const statusMap = {
    0: '已关闭',
    1: '待付款',
    2: '待接单',
    4: '已接单',
    5: '已出货',
    6: '配送中',
    7: '待发货',
    8: '已发货',
    100: '已完成',
  };
  return statusMap[status] || '';
};
// 分页
const handlePageChange = (newPage) => {
  console.log('Current page:', newPage);
  currentPage.value = newPage.current;
  pageSize.value = newPage.pageSize;
  fetchData();
};

/**
 * 筛选 start
 */
// 定义一个计算属性，返回filterParams中所有有值的的数组，需要判断类型input和select的就是'',date的就是[]
const filterParamsValue = computed(() => {
  return filterParams.value.filter((item) => {
    if (item.type === 'input' || item.type === 'select') {
      return item.value !== '' && item.value !== undefined;
    } else if (item.type === 'date') {
      return item.value.length > 0;
    }
  });
});

// 定义一个映射对象，用于存储不同类型的默认值，避免重复判断
const defaultValueMap = {
  date: [],
  input: '',
  select: '',
};
const search = (params) => {
  console.log('Search params:', params);
  filterParams.value = params;
  currentPage.value = 1;

  fetchData();
};
const refresh = () => {
  fetchData();
};
const reset = (params) => {
  console.log('Reset search', params);
  filterParams.value.forEach((item) => {
    if (defaultValueMap[item.type] !== undefined) {
      item.value = defaultValueMap[item.type];
    }
  });
  currentPage.value = 1;
  fetchData();
};

const clearFilter = (key) => {
  filterParams.value.forEach((item) => {
    if (item.key === key) {
      item.value = defaultValueMap[item.type];
    }
  });
  currentPage.value = 1;
  fetchData();
};

const clearAllFilters = () => {
  console.log('Clear all filters');
  filterParams.value.forEach((item) => {
    if (defaultValueMap[item.type] !== undefined) {
      item.value = defaultValueMap[item.type];
    }
  });
  currentPage.value = 1;
  fetchData();
};
const getValueByOpt = (options, value) => {
  const option = options.find((opt) => opt.value === value);
  return option ? option.label : '';
};
/**
 * 筛选 end
 */

/**
 * 标签切换 start
 */
const tabChange = (index, value) => {
  console.log('Selected tab:', index, value);
  tabCurrentIndex.value = index;
  currentPage.value = 1;
  fetchData();
};

const RefundDrawerRef = ref(null);
const openRefundDrawer = (row) => {
  console.log(row, 'row');
  RefundDrawerRef.value.show(row);
};
/**
 * 自提订单 start-------------------------------
 */
const pickupCodeRef = ref(null);
const codeVerify = () => {
  pickupCodeRef.value.openDialog();
};
const refreshRun = () => {
  console.log('提货码验证成功');
  refresh();
};
const pickupShipmentRun = (receiveInfo) => {
  pickupShipment(receiveInfo?.sn, () => {
    emits('refresh');
    refresh();
  });
};
const pickupCompleteRun = (receiveInfo) => {
  pickupComplete(receiveInfo?.sn, () => {
    emits('refresh');
    refresh();
  });
};
const pickupReceivingRun = (receiveInfo) => {
  pickupReceiving(Number(receiveInfo?.store_id), receiveInfo?.sn, () => {
    emits('refresh');
    refresh();
  });
};
/**
 * 自提订单 end-------------------------------------
 */

/**
 * 物流订单 start---------------------------------
 */
const logisticsSetRef = ref(null);
const openLogisticsSet = (row, type) => {
  logisticsSetRef.value?.openDialog(row, type);
};
const LogisticsInfoRef = ref(null);
const openLogisticsInfo = (row) => {
  LogisticsInfoRef.value?.show(row.sn);
};
/**
 * 物流订单 end-----------------------------------
 */

/**
 * 配送订单 start----------------------------------
 */
const deliveryMethodRef = ref(null);
const deliveryReceiving = (row) => {
  console.log('接单');
  deliveryMethodRef.value.openDialog(row);
};
const modifyDeliveryRef = ref(null);
const rider2sellerRun = (row) => {
  modifyDeliveryRef.value.openDialog(row);
};
const seller2riderRun = (row) => {
  console.log('骑手自配');
  seller2rider(row.sn, () => {
    refresh();
  });
};
const deliveryCompleteRef = ref(null);
const deliveryCompleteRun = (row) => {
  if (row.delivery_type === 0) {
    //商家自配
    pickupCompleteRun(row.sn);
  } else {
    deliveryCompleteRef.value.openDialog(row.sn);
  }
  console.log('完成订单');
};

/**
 * 配送订单 end----------------------------------
 */
// const router = useRouter();
const toDetail = async (e) => {
  console.log(e.row, 'row');
  const path = `/shop/index.html#/${props.orderType}OrderDetail?sn=${e.row.sn}`;
  // router.push(path);
  await sdk.workBench_openTabForWebview({
    title: '订单详情',
    icon: new URL(shopIcon, import.meta.url).href,
    url: location.origin + path,
  });
};
</script>

<style scoped>
.order-page {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  flex: 1 0 0;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #fff);
}

.status-tabs {
}

.abtn {
  display: flex;
  padding: 4px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  border-radius: 4px;
  cursor: pointer;
  width: fit-content;
}
.abtn:hover {
  border-radius: 4px;
  background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
}
.icon20 {
  font-size: 20px;
}
.search-bar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  .left {
    display: flex;
    align-items: center;
    gap: 8px;
    .filter-box {
      display: flex;
      width: 32px;
      height: 32px;
      min-height: 32px;
      max-height: 32px;
      padding: 6px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      cursor: pointer;
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #d5dbe4);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
    }
    .filter-box:hover {
      background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
      .iconfi {
        color: #4d5eff;
      }
    }
  }
}
.table-box {
  padding: 0 16px;
  height: calc(100vh - 146px);
  overflow-y: auto;
}
.table-box::-webkit-scrollbar {
  width: 0px;
}
:deep(.t-table tr:hover) {
  background-color: #f3f6fa !important;
}
:deep(.t-table tr) {
  cursor: pointer;
}
:deep(.t-table .t-table__empty-row:hover) {
  background-color: #fff !important;
}
.filter-bar {
  margin-bottom: 16px;
}
.filter-res {
  display: flex;
  flex-wrap: wrap;
  row-gap: 8px;
  .tit {
    color: var(--text-kyy-color-text-2, #516082);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }
  .ov-time {
    display: flex;
    min-width: 290px;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }
  .close2 {
    display: flex;
    align-items: center;
    color: #828da5;
    img {
      width: 10px;
      height: 10px;
    }
  }
  .te {
    color: var(--kyy-color-tag-text-black, #1a2139);
    cursor: pointer;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    border-radius: 4px;
    background: var(--kyy-color-tag-bg-gray, #eceff5);
    margin-right: 8px;
  }
  .stat {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }
  .kword {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }
  .icon {
    display: flex;
    margin-left: 4px;
    cursor: pointer;
    img {
      width: 14px;
      height: 14px;
      margin-top: 4px;
      margin-right: 4px;
    }
  }
}
.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 16px;
}
:deep(.t-pagination__total) {
  flex: none;
  margin-right: 16px;
  color: var(--kyy_color_pagination_text_illustrate, #516082);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.order-number {
  position: relative;
  .status-rd {
    position: absolute;
    top: -22px;
    left: -11px;
    display: flex;
    padding: 0px 4px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 0px 0px 8px 0px;
    background: #fc7c14;
    color: #fff;
    font-family: 'PingFang SC';
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 200% */
  }
}
.status-text0 {
  color: var(--text-kyy_color_text_2, #516082);

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.status-text1 {
  color: var(--warning-kyy_color_warning_default, #fc7c14);

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.status-text2,
.status-text7 {
  color: var(--warning-kyy_color_warning_default, #fc7c14);

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.status-text4,
.status-text8 {
  color: var(--success-kyy_color_success_active, #499d60);

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.status-text100,
.status-text5 {
  color: var(--success-kyy_color_success_active, #499d60);

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.status-text3 {
  color: var(--blue-kyy_color_blue_default, #4093e0);

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.status-text5 {
}
.status-text6 {
  color: var(--blue-kyy_color_blue_default, #4093e0);

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.text-box {
  color: var(--kyy_color_modal_content, #516082);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.sele-box {
  display: flex;
  align-items: center;
  align-self: stretch;
  margin-top: 12px;
}
/* :deep(.t-table) {
  min-height: 448px;
} */

.buyerbox {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  flex: 1 0 0;
  overflow: hidden;
  color: var(--kyy_color_table_text, #1a2139);
  text-overflow: ellipsis;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.empty-box {
  margin-top: 80px;
}
.temname {
  color: #828da5;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 101%;
}
:global(.ellipsis-light .temname) {
  color: #fff !important;
  width: 100%;
  overflow: inherit;
  text-overflow: initial;
  white-space: break-spaces;
}
</style>
