<template>
  <div
    ref="pageContent"
    v-infinite-scroll="onLoading"
    class="page-content"
    :infinite-scroll-immediate-check="false"
    :infinite-scroll-distance="1800"
    :infinite-scroll-disabled="scrollDisabled"
    infinite-scroll-watch-disabled="scrollDisabled"
    @scroll.once="pageScroll"
  >
    <t-alert
      v-if="store.hasPostFailed && !offlineAddNoCached"
      theme="error"
      close
      class="mt-16 mx-16"
      @close="store.hasPostFailed = false"
    >
      <template #icon>
        <i class="i-svg:error-fill text-16 text-white" />
      </template>
      {{ $t('square.post.err2Draft') }} <t-link theme="primary" @click="router.push('/square/drafts'); store.hasPostFailed = false">{{ $t('square.clickSee') }}</t-link>
    </t-alert>

    <PostList
      ref="postListRef"
      :key="refreshKey"
      go-detail
      class="trends-list"
      :api="getNearby"
      :params="{
        'ip_region': JSON.stringify(store?.ipInfo) || '',
        'lat_lng.longitude': stateStore.position[0] || '',
        'lat_lng.latitude': stateStore.position[1] || '',
      }"
      :need-observe-query-time="false"
      :hide-more-nav="true"
      :show-follow="true"
      :cache-key="SQUARE_CACHE.timeline"
      @removed="refreshKey++"
      @load="loaded"
      @publish="postDialogVisible = true"
      @offline-empty="offlineEmpty"
    />

    <t-back-top
      container=".page-content"
      shape="circle"
      size="small"
      :visible-height="50"
      :offset="['383px', '40px']"
      style="position: fixed"
    >
      <div class="back-top">
        <!-- <t-icon name="arrow-up" class="icon" /> -->
        <img src="@renderer/assets/square/icon-top.svg" alt="" class="icon">
      </div>
    </t-back-top>

    <!-- 外部跳转查看动态详情-->
    <PostDetail v-if="detailVisible" :id="detailId" v-model="detailVisible" />

    <t-dialog
      v-model:visible="settingVisible"
      :header="$t('square.privacy.title')"
      :cancel-btn="null"
      :confirm-btn="null"
      :close-on-overlay-click="false"
      @close="onCloseDialog"
    >
      <div class="setting-dialog-content">
        <t-alert class="tip" theme="info" :message="$t('square.privacy.tip')" />
        <t-list class="list">
          <t-list-item>
            <t-list-item-meta
              :title="$t('square.privacy.allowShowInNearby')"
              :description="$t('square.privacy.allowShowInNearbyTip')"
            />
            <template #action>
              <t-switch v-model="settingFormData.postHideInNearby" @change="submitSetting" />
            </template>
          </t-list-item>
        </t-list>
      </div>
    </t-dialog>

    <BMap :height="0" @initd="onMapInit" />
  </div>
</template>

<script setup lang="ts" name="FriendCircle">
import { inject, onMounted, provide, reactive, ref, watch, computed } from 'vue';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import { getSquareInfo, updateIndividualSetting } from '@renderer/api/square/home';
import to from 'await-to-js';
import { useBaiduMap } from '@renderer/components/common/map/hooks';
import PostList from '../components/post/PostList.vue';
import { useSquareStore } from '@/views/square/store/square';
import PostDetail from '@/views/square/components/post/PostDetail.vue';
import { POST_REFRESH_KEY, SQUARE_CACHE } from '@/views/square/constant';
import { getNearby } from '@/api/square/post';
import { useStateStore } from '../store/state';

const store = useSquareStore();
const stateStore = useStateStore();
const router = useRouter();

const refreshKey = ref(0);
const settingVisible = ref(false);
const settingFormData = reactive({
  postHideInNearby: false,
});
// 保存个人设置
const submitSetting = async () => {
  const setting = { ...settingFormData };
  await updateIndividualSetting({ setting });
  // MessagePlugin.success(t('square.successTip'));
};

// 外部跳转查看动态详情
const route = useRoute();
const detailVisible = ref(false);
const detailId = ref('');
const postDialogVisible = ref(false);
const postSubmitKey = ref(1);
provide(POST_REFRESH_KEY, postSubmitKey);

watch(
  () => route.query.postId,
  async (id) => {
    if (!id) return;
    detailId.value = id as string;
    detailVisible.value = true;
  },
  { immediate: true },
);

const postPublishKey = inject(POST_REFRESH_KEY, ref(0));
watch(() => postPublishKey.value, () => {
  refreshKey.value++;
});

const postListRef = ref(null);
const scrollDisabled = computed(() => postListRef.value?.status === 'finished');
const onLoading = () => {
  postListRef.value?.loading();
};

const loaded = () => {
  // setTimeout(() => {
  //   store.unreadPostCount = 0;
  //   unReadPost();
  // }, 1000);
};

const offlineAddNoCached = ref(false);
const offlineEmpty = (flag: boolean) => {
  offlineAddNoCached.value = flag;
};

const pageContent = ref(null);
const scroll = ref(0);
onBeforeRouteLeave((to, from, next) => {
  scroll.value = pageContent.value?.scrollTop;
  next();
});

const pageScroll = () => {
  // store.unreadPostCount = 0;
  // setTimeout(() => store.fetchNewsStats(), 400);
};

// 地图 IP 定位
const { markerInfo, onMapInit } = useBaiduMap({
  onPointGeocoder() {
    updatePosition();
  },
});

const onCloseDialog = () => {
  store.showPostHideInNearbyDialog = true;
};

// 获取用户信息、设置
const getIndividualInfo = async () => {
  let [err, res] = await to(getSquareInfo({ square_id: store.squareId }));
  if (err) return;

  const { individualSetting } = res.data.info;
  Object.assign(settingFormData, individualSetting);
};

const updatePosition = () => {
  if (stateStore.position.length) return;
  const { lng, lat } = markerInfo.value.location;
  stateStore.updatePosition([lng, lat]);
};

onMounted(async () => {
  // 组织广场中，不可设置附近入口开关及不允许动态展示的逻辑
  if (store.isPersonal && !store.showPostHideInNearbyDialog) {
    await getIndividualInfo();
    settingVisible.value = true;
  }
});
</script>

<style lang="less" scoped>
.scrollbar(6px);

.page-content {
  position: relative;
  overflow-y: auto;
  padding: 0 8px 16px 16px;
  margin-right: 6px;
}

:deep(.t-upload__card) {
  max-width: 350px;
  .t-upload__card-item {
    margin-right: 5px;
  }
}

:deep(.t-alert--error) {
  padding: 8px 16px;
  align-items: center;
  background-color: #FFEEE8;
  .t-alert__description {
    color: #13161B;
  }
}

:deep(.trends-list) {
  overflow-y: inherit;
  height: inherit;
}

:deep(.t-back-top) {
  background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.50)) !important;
  border: none;
  box-shadow: none;
}

.back-top {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  cursor: pointer;
  .icon {
    color: #fff;
    width: 20px;
    height: 20px;
  }
}

.album-title{
  color: #333333;
  font-size: 16px;
  margin: 0 16px 16px 16px;
  cursor: pointer;
  padding-top: 8px;
  .album-title-icon{
    font-size: 24px;
    vertical-align: middle;
  }
  .album-title-span{
    margin-left: 8px;
    vertical-align: middle;
  }
}

.setting-dialog-content {
  .tip {
    padding: 8px 24px;
  }
}

.list {
  margin: 24px 0;
  :deep(.t-list-item) {
    padding: 0;
  }
  :deep(.t-list-item__meta-title) {
    font-weight: 400;
    font-size: 14px;
    color: #13161b;
  }
  :deep(.t-list-item__meta-description) {
    color: #717376;
  }
}
</style>
