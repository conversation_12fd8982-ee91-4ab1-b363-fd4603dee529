import { defineStore } from 'pinia';

export const useTabStore = defineStore('tabStore', {
  state: ()=> ({
    // 侧边栏的tab下标
    currentTab: -1,
    // 组织设置顶部栏的tab下标
    topTab: {
      growth: 0,
      introduce: 0,
      honor: 'published'
    },
    // 关闭tab时是否需要弹窗保存草稿弹窗
    saveDraftWhenCloseTab: false,
    // 是否需要进行草稿的保存
    isSaveDraft: {
      introduce: false,
      growth: false
    },
    resetIntroduceFormTag: 0,
    resetGrowthFormTag: 0,

    isOpenCertifyModal: false, // 是否打开认证弹窗
  }),
  getters: {
  },
  actions: {
    setOpenCertifyModal(val) {
      this.isOpenCertifyModal = val;
    },
    setCurrentTab(index) {
      this.currentTab = index;
    },
    setTabStatus(key, status) {
      this.topTab[key] = status;
    },
    resetTopTab() {
      this.topTab.growth = 0;
      this.topTab.introduce = 0;
      this.topTab.honor = 'published';
    },
    setSaveDraftWhenCloseTab(val) {
      this.saveDraftWhenCloseTab = val;
    },
    saveDraft(type, val) {
      this.isSaveDraft[type] = val;
    }
  },
})
