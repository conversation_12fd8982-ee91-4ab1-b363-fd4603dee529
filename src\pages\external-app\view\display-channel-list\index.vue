<template>
  <div class="px-16 flex flex-col h-full">
    <NotAuth v-if="!userAuth" />
    <RTable v-else ref="RTableRef" :tabs="tabs" :table="table" :is-sticky-header="true" @change="onTableChange">
      <template #topContent>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-16">
            <t-button class="w-108" theme="primary" :disabled="!hasCurrentChannelPermission" @click="openSelectApp">
              <iconpark-icon class="text-[#fff] text-20" name="iconadd" />
              <span class="ml-4 font-600">添加应用</span>
            </t-button>
            <div v-if="sortLoading" class="flex items-center gap-8 text-12 text-gray-500">
              <t-loading size="small" />
              <span>保存排序中...</span>
            </div>
          </div>
        </div>
      </template>
    </RTable>

    <!-- 选择应用弹窗 -->
    <SelectApp
      v-model="showSelectApp"
      :channel-type="currentChannelType"
      :options="options"
      :existing-apps="table.list"
      @confirm="handleSelectAppConfirm"
      @cancel="handleSelectAppCancel"
    />
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, computed, watch } from 'vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import jssdk from '@lynker-desktop/web';
import { RTable } from '@rk/unitPark';
import { useAsyncState } from '@vueuse/core';
import { debounce } from 'lodash';
import TableOperate from './components/TableOperate.vue';
import {
  getExternalAppDisplayChannelList,
  addExternalAppDisplayChannel,
  deleteExternalAppDisplayChannel,
  sortExternalAppDisplayChannel,
  checkSquareOpen,
  checkDigiPlatformOpen,
  checkAnnualFee,
} from '@pages/external-app/api';
import type {
  ExternalAppDisplayChannelListRequest,
  ExternalAppDisplayChannelListDataItem,
  ExternalAppListDataItem,
} from '@pages/external-app/api';
import { IOptions } from '@axios/types';
import { useRouter } from 'vue-router';
import { Utils } from '@utils/index';
import SelectApp from '@pages/external-app/components/select-app.vue';
import external_app from '@pages/external-app/assets/external_app.svg';
import NotAuth from '@pages/external-app/components/notAuth.vue';

const router = useRouter();

const userAuth = ref(true);
const options = ref<IOptions>({
  env: (router.currentRoute.value.query.env as string) || Utils.config.env,
  teamId: (router.currentRoute.value.query.teamId as string) || '',
  openId: (router.currentRoute.value.query.openId as string) || '',
  token: (router.currentRoute.value.query.token as string) || Utils.config.token,
});

// 响应式数据
const RTableRef = ref();

const currentChannelType = ref('workshop');
const previousChannelType = ref('workshop'); // 保存上一次的渠道类型
// 拖拽排序状态管理
const { execute: executeSort, isLoading: sortLoading } = useAsyncState(
  async () => {
    try {
      const sortedAppIds = table.value.list.map((item: ExternalAppDisplayChannelListDataItem) => item.app_id!);
      await sortExternalAppDisplayChannel(
        {
          app_ids: sortedAppIds,
          channel_type: currentChannelType.value,
        },
        options.value,
      );
      MessagePlugin.success('排序保存成功');
      return true;
    } catch (error) {
      console.error('排序保存失败:', error);
      MessagePlugin.error('排序保存失败，请重试');
      // 重新加载数据恢复原始顺序
      await loadDisplayChannelList();
      return false;
    }
  },
  undefined,
  { immediate: false },
);
const showSelectApp = ref(false); // 控制选择应用弹窗显示

// 权限检查状态
const { state: squareOpenState, execute: checkSquareOpenStatus } = useAsyncState(async () => {
  try {
    const response = await checkSquareOpen({ team_id: options.value.teamId }, options.value);
    const isAnnualFee = await checkAnnualFee(options.value);
    return (response?.opened && !isAnnualFee) || false;
  } catch (error) {
    console.error('检查广场号开通状态失败:', error);
    return false;
  }
}, false);

const { state: digitalPlatformOpenState, execute: checkDigitalPlatformOpenStatus } = useAsyncState(async () => {
  try {
    const response = await checkDigiPlatformOpen(options.value);
    return response || false;
  } catch (error) {
    console.error('检查数字平台开通状态失败:', error);
    return false;
  }
}, false);

// 数据加载状态
const { execute: loadDisplayChannelList } = useAsyncState(
  async () => {
    try {
      const response = await getExternalAppDisplayChannelList(queryParams.value, options.value);
      if (response.code === 1010) {
        userAuth.value = false;
        return;
      }
      userAuth.value = true;
      if (response?.data?.items) {
        table.value.list = response.data.items;
        table.value.pagination.pageSize = 10000000;
        table.value.pagination.current = 1;
        table.value.pagination.total = response.data.items.length;
      } else {
        table.value.list = [];
        table.value.pagination.total = 0;
      }
    } catch (error: any) {
      if (error?.response?.data?.code === 1010) {
        userAuth.value = false;
      }
      console.error('获取展示渠道列表失败:', error);
      MessagePlugin.error(error?.response?.data?.message || error?.message || '获取展示渠道列表失败');
      table.value.list = [];
      table.value.pagination.total = 0;
    }
  },
  undefined,
  { immediate: false },
);

// 标签页配置
const tabs = ref({
  defaultInfo: {
    value: 'workshop',
  },
  list: [
    {
      label: '数智工场',
      value: 'workshop',
    },
    {
      label: '广场号',
      value: 'square',
    },
    {
      label: '数字平台',
      value: 'digital',
    },
  ],
});
// 表格列配置，动态控制排序列显示
const columns = computed(() => {
  const baseColumns = [
    {
      title: '应用名称',
      width: 264,
      // @ts-ignore
      cell: (_h: any, { row }: { row: ExternalAppDisplayChannelListDataItem }) => (
        <div class="flex items-center gap-8">
          <img class="w-36 h-36 object-cover rounded-12" src={row.picture_linking || external_app} alt="" />
          <div>{row.name || ''}</div>
        </div>
      ),
    },
    {
      title: '类型',
      width: 120,
      // @ts-ignore
      cell: (_h: any, { row }: { row: ExternalAppDisplayChannelListDataItem }) => (
        <div>{getAppTypeLabel(row.type || '')}</div>
      ),
    },
    {
      title: '操作',
      width: 120,
      // @ts-ignore
      cell: (_h: any, { row }: { row: ExternalAppDisplayChannelListDataItem }) => (
        <TableOperate onEdit={() => handleEdit(row)} onDelete={() => handleDelete(row)} />
      ),
    },
  ];
  if (currentChannelType.value !== 'workshop') {
    baseColumns.unshift({
      title: '排序',
      width: 46,
      // @ts-ignore
      cell: (_h: any, { row: _row }: { row: ExternalAppDisplayChannelListDataItem }) => (
        <div class="flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path
              d="M3.3125 4.97949H16.6458"
              stroke="#828DA5"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M3.3125 9.97949H16.6458"
              stroke="#828DA5"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M3.3125 14.9795H16.6458"
              stroke="#828DA5"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      ),
    });
  }
  return baseColumns;
});

const attrs = computed(() => {
  return {
    dragSort: 'row',
    rowKey: 'app_id',
    dragSortOptions: {
      disabled: currentChannelType.value === 'workshop',
    },
    onDragSort: async (params: any) => {
      if (currentChannelType.value === 'workshop') {
        return;
      }
      // 更新本地数据
      table.value.list = params.newData;
      // 执行排序
      await executeSort();
    },
  };
});

// 表格配置
const table = ref({
  get attrs() {
    return attrs.value;
  },
  get columns() {
    return columns.value;
  },
  pagination: {
    pageSize: 10000000,
    current: 1,
    total: 0,
  },
  list: [] as ExternalAppDisplayChannelListDataItem[],
});

// 查询参数
const queryParams = computed<ExternalAppDisplayChannelListRequest>(() => ({
  channel_type: currentChannelType.value,
}));

// 计算当前渠道是否有权限
const hasCurrentChannelPermission = computed(() => {
  if (currentChannelType.value === 'square') {
    return squareOpenState.value;
  } else if (currentChannelType.value === 'digital') {
    return digitalPlatformOpenState.value;
  }
  // 数智工场默认有权限
  return true;
});

// 权限检查函数
const checkChannelPermission = async (channelType: string): Promise<boolean> => {
  if (channelType === 'square') {
    if (!squareOpenState.value) {
      await checkSquareOpenStatus();
    }
    if (!squareOpenState.value) {
      await showSquareOpenDialog();
      return false;
    }
  } else if (channelType === 'digital') {
    if (!digitalPlatformOpenState.value) {
      await checkDigitalPlatformOpenStatus();
    }
    if (!digitalPlatformOpenState.value) {
      await showDigitalPlatformOpenDialog();
      return false;
    }
  }
  return true;
};

const openAnnualFeeDrawer = async () => {
  const isOpen = await checkSquareOpen({ team_id: options.value.teamId }, options.value);
  const isAnnualFee = await checkAnnualFee(options.value);
  const isOpenAndAnnualFee = isOpen && isAnnualFee;
  return new Promise<void>((resolve) => {
    const dialog = DialogPlugin.confirm({
      theme: 'info',
      closeBtn: false,
      header: '请购买套餐',
      body: isOpenAndAnnualFee ? '套餐已过期，请先购买套餐后操作' : '未购买套餐，请购买套餐后操作',
      cancelBtn: '取消',
      confirmBtn: '立即购买',
      onConfirm: async () => {
        dialog.destroy();
        // await jssdk.mainMenu_openSquare();
        await jssdk.openAnnualFeeDrawer({
          query: {
            open: !isOpenAndAnnualFee,
            teamId: options.value.teamId || '',
          },
        });
        window.location.reload();
        resolve();
      },
      onCancel: () => {
        dialog.destroy();
        resolve();
      },
    });
  });
};

// 显示广场号开通提示
const showSquareOpenDialog = () => {
  openAnnualFeeDrawer();
  // return new Promise<void>((resolve) => {
  //   const dialog = DialogPlugin.confirm({
  //     theme: 'info',
  //     closeBtn: false,
  //     header: '请开通广场号',
  //     body: '当前组织需先开通广场号才能进入',
  //     confirmBtn: '去开通',
  //     onConfirm: async () => {
  //       dialog.destroy();
  //       // await jssdk.mainMenu_openSquare();
  //       openAnnualFeeDrawer();
  //       resolve();
  //     },
  //     onCancel: () => {
  //       dialog.destroy();
  //       resolve();
  //     },
  //   });
  // });
};

// 显示数字平台开通提示
const showDigitalPlatformOpenDialog = () => {
  return new Promise<void>((resolve) => {
    const dialog = DialogPlugin.confirm({
      theme: 'info',
      closeBtn: false,
      header: '请开启数字平台',
      body: '未开启数字平台，请先开启数字平台后操作',
      confirmBtn: '去开启',
      onConfirm: async () => {
        dialog.destroy();
        await jssdk.mainMenu_openDigitalPlatform({
          teamId: options.value.teamId || '',
          query: {
            teamId: options.value.teamId || '',
          },
        });
        window.location.reload();
        resolve();
      },
      onCancel: () => {
        dialog.destroy();
        resolve();
      },
    });
  });
};

// 监听渠道类型变化，重新加载数据
watch(
  currentChannelType,
  () => {
    loadDisplayChannelList();
  },
  { immediate: true },
);

// 工具函数：获取应用类型标签
const getAppTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    app: 'APP跳转',
    h5: '网页H5跳转',
    wechat_official: '微信公众号',
    mini_program: '微信小程序',
  };
  return typeMap[type] || type;
};

// 添加应用
const openSelectApp = async () => {
  try {
    // 检查当前渠道类型的权限
    const hasPermission = await checkChannelPermission(currentChannelType.value);
    if (!hasPermission) {
      // 权限不足，不执行添加操作
      return;
    }

    // 显示选择应用弹窗
    showSelectApp.value = true;
  } catch (error) {
    console.error('打开选择应用弹窗失败:', error);
  }
};

// 处理选择应用确认
const handleSelectAppConfirm = async (selectedApps: ExternalAppListDataItem[]) => {
  try {
    console.log('选中的应用:', selectedApps);
    if (selectedApps.length > 0) {
      // 调用添加应用到渠道的 API
      await addExternalAppDisplayChannel(
        {
          app_ids: selectedApps.map((app) => app.app_id!),
          channel_type: currentChannelType.value,
        },
        options.value,
      );
      // 显示成功提示
      MessagePlugin.success(`成功添加 ${selectedApps.length} 个应用到渠道`);
      // 重新加载数据
      await loadDisplayChannelList();
    }
  } catch (error) {
    console.error('添加应用失败:', error);
    MessagePlugin.error('添加应用失败，请重试');
  }
};

// 处理选择应用取消
const handleSelectAppCancel = () => {
  console.log('用户取消选择应用');
};

// 编辑应用
const handleEdit = async (row: ExternalAppDisplayChannelListDataItem) => {
  try {
    // 检查当前渠道类型的权限
    const hasPermission = await checkChannelPermission(currentChannelType.value);

    if (!hasPermission) {
      // 权限不足，不执行编辑操作
      return;
    }

    console.log('编辑应用:', row);
    // TODO: 实现编辑功能 - 可以打开编辑抽屉或跳转到编辑页面
    DialogPlugin.confirm({
      theme: 'info',
      header: '编辑功能',
      body: '编辑功能正在开发中，敬请期待！',
      confirmBtn: '确定',
      onConfirm: () => {
        // 关闭对话框
      },
    });
  } catch (error) {
    console.error('编辑应用失败:', error);
  }
};

// 删除应用
const handleDelete = async (row: ExternalAppDisplayChannelListDataItem) => {
  try {
    // 检查当前渠道类型的权限
    const hasPermission = await checkChannelPermission(currentChannelType.value);

    if (!hasPermission) {
      // 权限不足，不执行删除操作
      return;
    }

    // 调用删除 API
    await deleteExternalAppDisplayChannel(
      {
        app_id: row.app_id!,
        channel_type: currentChannelType.value,
      },
      options.value,
    );

    console.log('删除应用成功:', row.name);
    // 显示成功提示
    MessagePlugin.success(`应用"${row.name}"移除成功`);
    // 重新加载数据
    await loadDisplayChannelList();
  } catch (error) {
    console.error('移除应用失败:', error);
    MessagePlugin.error('移除应用失败，请重试');
  }
};

// 表格变化处理 - 使用 debounce 优化
const onTableChange = debounce(async (params: any, name: string) => {
  const newChannelType = params.tabs;
  // 检查权限
  const hasPermission = await checkChannelPermission(newChannelType);

  if (!hasPermission) {
    table.value.list = [];
    table.value.pagination.total = 0;
    table.value.pagination.current = 1;
    table.value.pagination.pageSize = 10000000;
    // 权限不足，恢复到上一次的渠道类型
    currentChannelType.value = newChannelType;
    return;
  }

  // 权限通过，更新渠道类型
  previousChannelType.value = newChannelType;
  currentChannelType.value = newChannelType;

  if (name === 'tabs') {
    // 标签页切换时重置分页
    table.value.pagination.current = 1;
    table.value.pagination.pageSize = 10000000;
  } else if (name === 'table') {
    const { pageInfo } = params;
    table.value.pagination.current = 1;
    table.value.pagination.pageSize = 10000000;
    table.value.pagination.total = pageInfo.total;
  }

  // 重新获取数据
  await loadDisplayChannelList();
}, 300);

// 组件挂载时初始化权限检查
onMounted(async () => {
  // 初始化权限检查
  await Promise.all([checkSquareOpenStatus(), checkDigitalPlatformOpenStatus()]);
});
</script>

<style lang="less" scoped>
:deep(.RK-Table) {
  height: calc(100vh - 74px);
  overflow: auto;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .t-table {
    td:last-child {
      padding: 8px !important;
    }
  }
}
</style>
