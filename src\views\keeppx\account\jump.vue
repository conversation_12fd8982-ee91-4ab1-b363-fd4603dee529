<template>
  <div>
    <div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getLinkDetail, getSmsDetail, getUuidDetail } from '@/api/account/login';
import { useAccountStore } from '@/stores/account';
import { setAccesstoken, getAccesstoken } from '@/utils/auth';
import { useRouter } from 'vue-router';
import { getDevice } from '@/utils/storage';
import { setLanguage } from '@/i18n/index';
import CallApp from 'callapp-lib'
const router = useRouter();
const accountStore = useAccountStore();
const codeTip = [10016,10017,10018];
const RingkolApp:any = ref(null);
const urlObj:any = ref();
onMounted(() => {
  // 是否用另可app打开
  // let lang = ['zh-tw', 'zh-hk', 'zh-mo', 'zh-hant-tw', 'zh-hant-hk', 'zh-hant-mo'].includes(navigator.language.toLocaleLowerCase()) ? 'zh-tc' : 'zh-cn';
  // let lang = navigator.language === 'zh-CN' ? 'zh-cn' : 'zh-tc';
  // window.localStorage.setItem('lang', lang);
  urlObj.value = getUrl();
  // setAccesstoken('');
  // console.log()
  if (~navigator?.userAgent?.indexOf?.('RingkolApp')) {
    // accountStore.setRinkol(true);
    // let timer:any = setInterval(() => {
    //   const mywindow = window as any;
    //   RingkolApp.value = mywindow?.RingkolApp || null;

    //   if (RingkolApp.value) {
    //     // 隐藏wenview原生bar
    //     // mywindow.RingkolAppChannel.postMessage(JSON.stringify({ method: "appHideNativeAppBar" }));
    //     // 获取app注入进来的数据
    //     const config = JSON.parse(RingkolApp.value.appConfig());
    //     lang = config.Locale === 'zh-hant-MO' ? 'zh-tc' : 'zh-cn';
    //     window.localStorage.setItem('lang', lang);
    //     clearInterval(timer);
    //     setUser();
    //     timer = null;
    //     // 判断是否有跳转的模块，默认跳到邀请相关的模块，以前的链接已经定好了，就不改了
    //     urlObj.value?.to ? toRoute(urlObj.value.to as string) : toInvite(true);
    //   }
    // }, 1000)
    urlObj.value?.to ? toRoute(urlObj.value.to as string) : toInvite(true);
  } else {
    // 尝试打开app 使用app浏览器
    if (getDevice() === 'mobile') {
      const linkApp = encodeURIComponent(window.location.href);
      // const CallApp = require('callapp-lib');
      // const options = {
      //   scheme: {
      //     protocol: 'ringkol',
      //     host: 'ringkol.com',
      //   },
      //   appstore: 'https://itunes.apple.com/cn/app/id1383186862',
      //   fallback: 'https://www.bing.com',
      // }
      // console.log(linkApp, options, 'aaaaaa');
      // const lib = new CallApp(options);
      // lib.open({
      //   path: 'webview',
      //   param: {
      //     link: linkApp
      //   },
      //   callback: () => {
      //     console.log('打开失败');
      //     urlObj.value?.sms ? smsDetail() : linkDetail(false);
      //   }
      // })
      window.location.href = `ringkol://ringkol.com/webview?link=${linkApp}&loginRequired=true`;
      setTimeout(()=>{
        let doc:any = window.document;
        let hidden = doc.hidden || doc?.webkitHidden || doc?.mosHidden || doc?.msHidden;
        if (typeof hidden === 'undefined' || hidden === false) {
          // accountStore.setRinkol(false);
          urlObj.value?.to ? toRoute(urlObj.value.to as string) : toInvite(false);
        }
      }, 2000)
    } else {
      // accountStore.setRinkol(false);
      urlObj.value?.to ? toRoute(urlObj.value.to as string) : toInvite(false);
    }
  }
  // setLanguage(lang);
});
const toRoute = (routerName:string) => {
  try {
   // 跳到对应的业务并把需要的参数传递过去
    const keys = Object.keys(urlObj.value);
    let query:any = {};
    keys.forEach(v => {
      if (v !== 'to') {
        query[v] = urlObj.value[v];
      }
    })
    routerName && router.replace( {name: routerName, query} );
  } catch (error) {
    console.log(error, 'toRoute error');
  }
};
const toInvite = (RingkolApp:boolean) => {
  // 短信邀请还是链接邀请 // 新增数字平台短信邀请
  if (['member', 'government', 'cbd', 'association','uni'].includes(urlObj.value?.app)) {
    uuidDetail();
  } else if (['staff'].includes(urlObj.value?.app)) {
    smsDetail();
  } else {
    linkDetail(RingkolApp);
  }
};
// 数字平台短信邀请跳转
const uuidDetail = () => {
  let query = {};
  let routerName = '';
  const params = {
    app: urlObj.value.app,
    uuid: urlObj.value.uuid
  };
  getUuidDetail(params).then((res: any) => {
    console.log(res.data)
    if (res.code === 0) {
      switch(res.data.status) {
        case 0:
          routerName = 'accountSms';
          query = {
            app: urlObj.value.app,
            uuid: urlObj.value.uuid,
            isSms: 1
          };
          break;
        case 1:
          routerName = 'accountTip';
          query = {code: 10017, isSms: 1};
          break;
        case 2:
          routerName = 'accountTip';
          query = {code: 10018, isSms: 1};
          break;
      }
      accountStore.setSmsInfo(res.data);
    } else {
      routerName = 'accountExpired';
    }
  }).catch((err:any) => {
    const code = err.response.data.code;
    if (code === -1) {
      routerName = 'accountExpired';
    }
  }).finally(() => {
    routerName && router.push( {name: routerName, query} );
  })
};
// 短信邀请跳转
const smsDetail = () => {
  let query = {};
  let routerName = ''
  getSmsDetail(urlObj.value.uuid || urlObj.value.sms).then((res: any) => {
    console.log(res.data)
    switch(res.data.status) {
      case 0:
        routerName = 'accountSms';
        query = {
          id: +res.data.id,
          app: urlObj.value.app,
          isSms: 1
        };
        break;
      case 1:
        routerName = 'accountTip';
        query = {code: 10017, isSms: 1};
        break;
      case 2:
        routerName = 'accountTip';
        query = {code: 10018, isSms: 1};
        break;
      case 3:
        routerName = 'accountExpired';
        break;
    }
    accountStore.setSmsInfo(res.data);
  }).catch((err:any) => {
    console.log(err, 'getLinkDetail err')
    // 待审核：10016，已加入：10017，已拒绝：10018
    const code = err.response.data.code;
    if (codeTip.includes(code)) {
      routerName = 'accountTip';
      query = {code, isSms: 1};
    }
    if (code === 10015 || code === -1) {
      routerName = 'accountExpired';
    }
    if (code === 4404) {
      routerName = 'accountExpired404';
    }
  }).finally(() => {
    routerName && router.push( {name: routerName, query} );
  })
};
// 链接地址邀请跳转
const linkDetail = (ringkol: boolean) => {
  const params = {
    link: urlObj.value?.link || '',
    openid: urlObj.value?.openid || ''
  }
  let routerName = '';
  let query = {};
  getLinkDetail(params).then((res: any) => {
    if (res.data.expire && Math.trunc(Date.now()/1000) > res.data.expire) {
      routerName = 'accountExpired';
    } else {
      accountStore.setLinkInfo({ ...res.data, openid: urlObj.value?.openid || '' });
      // 保存是否通过二维码邀请状态
      accountStore.setIsQrcode(+urlObj.value?.qrcode || 0);
      routerName = 'accountIndex';
      if (ringkol) {
        routerName =  'accountJoin';
      }
    }
  }).catch(err => {
    console.log(err, 'getLinkDetail err')
    // 待审核：10016，已加入：10017，已拒绝：10018
    const code = err.response.data.code;
    const errTip = err.response.data.message;
    if (codeTip.includes(code)) {
      routerName = 'accountTip';
      query = {code, errTip};
    }
    if (code === 10015 || code === -1) {
      routerName = 'accountExpired';
      query = {code, errTip};
    }
  }).finally(() => {
    routerName && router.push( {name: routerName, query} );
  })
};
const setUser = () => {
  accountStore.setToken(RingkolApp.value.token());
  setAccesstoken(RingkolApp.value.token());
  accountStore.setUserInfo(JSON.parse(RingkolApp.value.userInfo()));
};
const getUrl = () => {
  const url = window.location.href;
  let arr = url.split('?');
  let obj:any = {};
  if (arr.length > 1) {
    let params = arr[1].split('&');
    for(let i=0;i<params.length;i++){
    let param = params[i].split('=');
      obj[param[0]] = param[1];
    }
  }
  return obj;
};
</script>
