<template>
  <home :title="autoText.title">
    <template #content>
      <div class="header" v-if="activeData">
        <!-- <span class="text">：</span> -->
        你正在激活：
        <span class="title">【{{activeData?.exclusive_name || activeData?.team_name}}】</span>
        的{{ autoText.title_text }}
        <!-- <span class="text"></span> -->
      </div>
      <div class="body" v-if="(!(accountStore?.userInfo && !accountStore.userInfo.account_mobile)) && (!isLoading)">
        <div class="boxs" v-if="activeData && activeData.lists?.length && (!isSuccess) ">
            <div class="boxs-item" v-for="item in activeData.lists" :key="item.id">
              <template v-if="item.type === 1">
                <t-image class="logo" :src="ORG_DEFAULT_AVATAR" fit="cover">
                  <template #loading>
                    <img  :src="ORG_DEFAULT_AVATAR"/>
                  </template>
                  <template #error>
                    <img :src="ORG_DEFAULT_AVATAR"/>
                  </template>
                </t-image>
                <span class="text">{{item?.team_name}}</span>
                <span class="tip">组织</span>
              </template>
              <template v-else>
                <BaseAvatar
                  class="logo"
                  :avatar-size="'24px'"
                  :image-url="item?.logo"
                  :user-name="item?.name"
                  :shape="'circle'"
                />
                <span class="text">{{item?.name}}</span>
                <span class="tip tipPerson">个人</span>
              </template>
            </div>

        </div>
        <div class="result"  v-else-if="isSuccess">
           <div class="success" v-if="!isInMiniApp">
              <img class="logo" src="@/assets/img/ok.png" />

              <span class="text mt-12px">激活成功</span>
              <span class="midtext mt-4px">
                你已激活平台身份，请打开另可查看更多平台内容
              </span>
           </div>
           <successPage v-else @click="sendMsgToWx" :organizeData="activeData" ></successPage>
        </div>
        <div class="result"   v-else>
           <div class="success">
              <img class="logo" src="@/assets/img/no.png" />
              <!-- <img class="logo" src="@/assets/img/no.png" /> -->
              <span class="text mt-12px">{{autoText?.no_active_text}}</span>
              <span class="midtext mt-4px">
                {{autoText?.desc}}
              </span>
              <span class="midtext">
                请更换登录手机号或联系秘书处了解具体情况
              </span>
           </div>
        </div>
        <div class="footer" v-show="activeData && activeData.lists?.length && (!isSuccess)">
          <t-button theme="primary" class="btn"  @click="onActive" >
            一键激活
          </t-button>
        </div>
        <template v-if="isSuccess ||  (!activeData) || (activeData && (!activeData.lists?.length))">
          <div v-show="!isRinkol &&  !isRingkolDesktop&&!isInMiniApp" class="footer">
            <div class="toUpload">
              <span class="toUpload-left" >
                <img class="img" src="@/assets/img/linker_name.png" />
              </span>
              <span class="toUpload-right">
                <span class="btn" v-if="deviceInfo.type === 'pc'">
                  <a href="ringkol://ringkol.com/memberIndex/member_number">打开另可</a>
                </span>
                <span class="btn" v-else>
                  <a href="https://ringkol.com/mobile/downloadCenter">打开另可</a>
                </span>
              </span>
            </div>
          </div>
        </template>
      </div>
      <div class="setPhone" v-else-if="!isLoading">
        <set-phone  ref="setPhoneDom"   @confirm="getPhone" />
      </div>
    </template>
  </home>

</template>

<script setup lang="ts">
import home from '@/views/keeppx/homeIndex.vue';
import { computed, ref, type Ref, onMounted, onBeforeUnmount } from 'vue';

import { useRoute, useRouter } from 'vue-router';
import {ORG_DEFAULT_AVATAR} from '@/constants/lss';
import BaseAvatar from '@/views/square/components/BaseAvatar.vue';
import { GetCommonActiveAxios, OnSetCommonActive } from '@/api/member/member';
import { useAccountStore } from '@/stores/account';
import { getProfile, bindAccount } from '@/api/account/login';
import to from "await-to-js";
import { JsBridgeReady } from '@/utils/preload';
import { errorHandlerFilter, getResponseResult, priceDivisorShow } from '@/utils/myUtils';
import {deviceType} from "@/utils/device";
import setPhone from '@/views/keeppx/account/components/setMemberPhone.vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { handleWeixinBridgeReady } from '@/views/square/utils.ts';

import successPage from '@/views/keeppx/org/success.vue';
// 是否在小程序中
import { sendWxMsg } from "@/utils/myUtils";
  const sendMsgToWx = () => {
    console.log('触发这里sendMsgToWx');

    sendWxMsg({
      type: 'FROM_H5',
      status: '200',
      path:'/pages/app-home/index'
    })
  }
const isInMiniApp = ref(false);
handleWeixinBridgeReady(() => {
  isInMiniApp.value = window.__wxjs_environment === 'miniprogram';
});
const router = useRouter();
const route = useRoute();
const query = route.query;
const autoText = computed(() => {
  let typeObj = {
    title: '激活会员',
    title_text: '会员',
    no_active_text: '未找到可激活的会员',
    desc: '你的手机号与秘书处登记的手机号不符'
  };
  // document.title = '激活会员';
  switch (query?.type) {
    case 'member':

      break;

    case 'government':
      document.title = '激活成员';
      typeObj.title_text = '成员';
      typeObj.no_active_text = '未找到可激活的成员';
      typeObj.desc = '你的手机号与管理员登记的手机号不符'
      break;
    case 'cbd':
      document.title = '激活租户';
      typeObj.title_text = '租户';
      typeObj.no_active_text = '未找到可激活的租户';
      typeObj.desc = '你的手机号与管理员登记的手机号不符'
      break;
    case 'association':
      document.title = '激活成员';
      typeObj.title_text = '成员';
      typeObj.no_active_text = '未找到可激活的成员';
      typeObj.desc = '你的手机号与管理员登记的手机号不符'
        case 'uni':
      document.title = '激活成员';
      typeObj.title_text = '成员';
      typeObj.no_active_text = '未找到可激活的成员';
      typeObj.desc = '你的手机号与管理员登记的手机号不符'
    default:
      break;
  }
  typeObj.title =  document.title || typeObj.title;
  return typeObj;
})
const accountStore = useAccountStore();
const isRinkol = accountStore.isRingkol;
const isRingkolDesktop = accountStore.isRingkolDesktop;
const deviceInfo = ref(deviceType(navigator.userAgent))

const activeData = ref(null);

// // status: 申请状态，1：待审核，2：已入会，3：已驳回，0：待提交数据
// const onGetLinkDetail = async () => {
//   const { link } = query;
//   const [err, res]:any = await getLinkDetailAxios({ link, telephone: accountStore.userInfo?.account_mobile });

// };

const isSuccess = ref(false);
const isLoading = ref(false);
const onActive = async () => {
  const ids = activeData.value ? activeData.value.lists.map(item=>item.id) : [];
  const { teamId } = query;
  console.log(ids)
  const [err, res]:any = await to(OnSetCommonActive({ids}, teamId));
  if (err) {
    MessagePlugin.error(err?.message);
    return
  };
  if(res.data) {
    isSuccess.value = true;
  } else {
    MessagePlugin.error('激活失败');
  }
}
const onGetCommonActiveList = async()=> {
  const { teamId } = query;
  const [err, res]:any = await to(GetCommonActiveAxios({telephone: accountStore.userInfo?.account_mobile}, teamId))
  isLoading.value = false;
  console.log(err, res);
  // isLoading.value = false;
  if (err) return;
  const {data} = res;
  activeData.value = data;
  console.log(activeData.value,'activeData.valueactiveData.value');

}

const validUser = () => {
  return new Promise((resolve, reject) => {
    console.log('member validUser')
    getProfile()
    .then((res) => {
      console.log('77777777777777777777777777777',res);
      accountStore.setUserInfo(res);
      resolve(res);
    })
    .catch((err) => {
      console.log('err?', err);
      //    if(err && [401, 403].includes(err.response.status)) {
      //         localStorage.clear();
      //         goLogin();
      //    }
      if (err && err.response.status) {
        console.log(router)
        errorHandlerFilter(err.response.status, err.response, router);
        accountStore.setUserInfo(null);
      }

      router.push({
        name: 'login',
        query: {
          redirect: encodeURIComponent(router.currentRoute.value.fullPath)
        }
      });

      reject(err);
    });
  })
};


const getPhone = async (v:any) => {
  // registerParams.value.email.region = v.region;
  // registerParams.value.email.mobile = v.mobile;
  // registerParams.value.email.mobile_code = v.code;
  // registerAcc();

  // mobile.value = {
  //   region: '86',
  //   mobile: '',
  //   code: '',
  // }
  const [err, res] = await to(bindAccount({
    mobile: v
  }))
  console.log(v)
  if(!err) {
    MessagePlugin.success('绑定成功')
    // localStorage.clear();

    accountStore.userInfo.account_mobile
    const userInfo =  accountStore.userInfo;
    userInfo.account_mobile = v.mobile;
    userInfo.account_mobile_region = v.region;
    accountStore.setUserInfo(userInfo);
    setTimeout(() => {
      onInit();
    }, 0);
    // setTimeout(() => {
    //   window.location.reload();

    // }, 1000);
  } else {
    MessagePlugin.error(err)
  }
};

const onInit = async () => {
  isLoading.value = true;
  await JsBridgeReady();
  await validUser().then(()=> {
    onGetCommonActiveList();

  });
};


onMounted(() => {
  document.title = '激活成员';
  onInit()
})
</script>

<style lang="less" scoped>
// pc端 固定尺寸展示
@width: 420px;
@media screen and (min-width: 500px) {

  #app>.home-container {
    max-height: 734px ;
    min-height: 734px;
    height: auto;
  }
  #app>.container {
    max-width: @width;
    min-width: @width;

  }
  :deep(.footer) {
    max-width: @width;
    min-width: @width;
  }
}
.header {
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  padding: 12px 16px;
  color: var(--text-kyy_color_text_1, #1A2139);
  // display: flex;
  // flex-wrap: wrap;
  .text {
    flex: none;
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    // width: 9.8vw;
  }
  .title {
    color: var(--warning-kyy_color_warning_default, #FC7C14);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  padding: 12px 16px;
  padding-bottom: calc(env(safe-area-inset-bottom) + 12px);
padding-bottom: calc(constant(safe-area-inset-bottom) + 12px); 

  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  .btn {
    width: 100%;
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4D5EFF);
  }
}

.toUpload {

  background: url('@/assets/img/bg_pic.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 16px;
  width: 100%;
  // height: 144px;
  display: flex;
  justify-content: space-between;
  // position: fixed;
  height: 96px;



  align-items: center;
  padding: 0 20px;

  &-left {
    display: flex;
    align-items: center;

    .img  {
        height: 80px;
    }
  }
  &-right {
    .btn {
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_active, #4D5EFF);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_hover, #EAECFF);
      padding: 4px 16px;
      a {
        color: var(--color-button_border-kyy_color_buttonBorder_text_active, #3E4CD1);
        text-align: center;

        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }


    }

  }
}
.setPhone {
  display: flex;
  justify-content: center;
}
.body {
  padding-bottom: 56px;
  .boxs {
    padding: 12px 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    &-item {
      border-radius: 8px;
      border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
      background: var(--bg-kyy_color_bg_default, #FFF);
      padding: 12px;


      display: flex;
      align-items: center;
      gap: 8px;
      .logo {
        width: 24px;
        height: 24px;
        border-radius: 50%;
      }
      .text {
        color: var(--text-kyy_color_text_1, #1A2139);

        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
      .tip {
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_kyyBlue, #E4F5FE);
        color: var(--kyy_color_tag_text_kyyBlue, #21ACFA);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
        padding: 0 4px;
        display: flex;
        align-items: center;
        height: 20px;


      }
      .tipPerson {
        color: #499D60;
      }
    }
  }
  .result {
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding: 16px;


    .success {
      padding-top: 40%;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      .logo {
        width: 64px;
        height: 64px;
      }
      .text {
        color: var(--text-kyy_color_text_1, #1A2139);
        /* kyy_fontSize_3/bold */
        font-family: "PingFang SC";
        font-size: 17px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px; /* 152.941% */
      }
      .midtext {
        color: var(--text-kyy_color_text_2, #516082);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
  }
}
</style>
