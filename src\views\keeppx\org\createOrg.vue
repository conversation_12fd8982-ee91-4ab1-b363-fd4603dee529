<template>
  <home :title="t('zx.contacts.createOrg')" className="containerPc">
    <template #content>
      <!-- <div @click="sendmsg1">
        20000000000000000000000
      </div>
      <div @click="sendmsg2">
        40333333333333333
      </div> -->
      <div>
        <div class="tip" v-if="!suc">
          <div class="bold">{{ t('zx.contacts.setOrgInfo') }}</div>
          <div>{{ t('zx.contacts.setOrgInfoTip') }}</div>
        </div>
        <div class="tform" v-if="!suc">
          <!-- <t-form
            ref="form"
            reset-type="initial"
            label-align="top"
            :data="formData"
            :rules="formRules"
          >
            <t-form-item class="t-form-item-margin" label="组织类型" name="type">
              <div class="tabs">
                <div :class="['tab-item', formData.type === item.value ? 'active' : '']" v-for="item in orgType" :key="item.value" @click="changeType(item)">
                  <img :src="item.icon" alt="">
                  <span>{{ item.name }}</span>
                  <img v-if="formData.type === item.value" class="tab-sel" src="@/assets/img/0.icon_sel.png" alt="">
                </div>
              </div>
            </t-form-item>
            <div class="split"></div>
            <t-form-item label="组织LOGO" style="margin-top: 16px;">
              <upload @updateUpload="uploadImg"/>
              <span class="upload-tip">
                建议上传图片尺寸为200*200，不超过1M，支持格式：svg、jpg、png、webp
              </span>
            </t-form-item>
            <t-form-item label="组织名称" name="fullName">
              <t-input type="text" v-model="formData.fullName" ></t-input>
            </t-form-item>
            <t-form-item label="组织归属地" name="region">
              <t-select v-model="formData.region">
                <t-option v-for="item in countList" :key="item.code" :value="item.code" :label="item.name"></t-option>
              </t-select>
            </t-form-item>
            <t-form-item>
              <t-checkbox v-model="formData.teamMailApproved"></t-checkbox>
              <span>允许员工通过企业邮箱加入</span>
            </t-form-item>
            <t-form-item v-if="formData.teamMailApproved" label="企业邮箱域名" name="teamMailDomain">
              <t-input v-model="formData.teamMailDomain"></t-input>
            </t-form-item>
          </t-form> -->
          <van-form ref="form" label-align="top" validate-trigger="onChange">
            <van-field class="kyy-cell orgTypeCell" :label="t('zx.contacts.orgType')" required>
              <template #input>
                <div class="tabs">
                  <div v-for="item in orgType" :key="item.value"
                    :class="['tab-item', formData.type === item.value ? 'active' : '']" @click="changeType(item)">
                    <img :src="item.icon" alt="" />
                    <span>{{ item.name }}</span>
                    <img v-if="formData.type === item.value" class="tab-sel" src="@/assets/svg/select_1.svg" alt="" />
                  </div>
                </div>
              </template>
            </van-field>
            <div class="split"></div>
            <van-field class="kyy-cell" :label="`${t('zx.contacts.org')}LOGO`" style="margin-top: 2px">
              <template #input>
                <div style="display: flex; align-items: center">
                  <van-upload @updateUpload="updateLogo" />
                  <span class="upload-tip">
                    {{ t('zx.contacts.uploadLogoTip') }}
                  </span>
                </div>
              </template>
            </van-field>
            <van-field v-model="formData.fullName" class="kyy-cell"  maxlength="50" :label="t('zx.contacts.orgName')"
              :placeholder="t('contacts.pleaseInput')" required
              :rules="[{ required: true, message: t('contacts.pleaseInput') }]">
            </van-field>
            <van-field v-model="regionName" class="kyy-cell" :label="t('zx.contacts.orgRegion')"
              :placeholder="t('contacts.pleaseSelect')" is-link readonly required
              :rules="[{ required: true, message: t('contacts.pleaseSelect') }]" @click="showPicker = true">
            </van-field>

            <van-popup v-model:show="showPicker" round position="bottom" :confirm-button-text="t('account.confirm')"
              :cancel-button-text="t('account.cancel')">
              <van-picker :columns="countList" :columns-field-names="{ text: 'name', value: 'code' }"
                @cancel="showPicker = false" @confirm="onConfirm" />
            </van-popup>
            <van-field v-model="fieldValue" class="kyy-cell" is-link required readonly
              :rules="[{ required: true, message: t('contacts.pleaseSelect') }]" label="行业" placeholder="请选择所在行业"
              @click="clickIndustry" />
            <!-- <van-icon name="passed" /> -->
            <van-popup v-model:show="showcascader" round position="bottom">
              <van-cascader class="industry_cascader" v-model="formData.industry" title="选择行业" placeholder="请选择"
                :field-names="fieldNames" :options="options" @close="showcascader = false" @finish="onFinish">
                <template #option="{ option, selected }">
                  <div class="current-level" style="color: rgb(44, 62, 80);">{{ option.name }}</div>
                  <div style="color: rgb(44, 62, 80);">
                    <van-icon name="arrow" v-if="option.children?.length" />
                    <img v-else-if="selected" src="@/assets/svg/icon_selected.svg" alt="">
                  </div>
                </template>
              </van-cascader>
            </van-popup>

            <van-field class="kyy-cell">
              <template #input>
                <van-checkbox v-model="formData.teamMailApproved">{{ t('zx.contacts.allowEmail') }}</van-checkbox>
              </template>
            </van-field>
            <van-field v-if="formData.teamMailApproved" v-model="formData.teamMailDomain" class="kyy-cell"
              :label="t('zx.contacts.orgEmailDomain')" :placeholder="t('contacts.pleaseInput')" required
              :rules="[{ required: true, message: t('contacts.pleaseInput') }]">
            </van-field>
          </van-form>
          <div style="margin-top: 25px;" :class="[keybordType === 'up' ? 'keybordType-box' : '']">
            <!-- <t-button style="width: 100%" @click="next">下一步</t-button> -->
            <van-button class="keybordType-button" type="primary" block :loading="nextDisabled" :disabled="nextDisabled"
              @click="next">{{ t('contacts.nextStep') }}</van-button>
          </div>
        </div>
        <div class="suc-container" v-else>
          <div class="f-c-c">
            <img class="suc-icon" src="@/assets/img/icon_success.svg" alt="">
            <div class="suc-bold">{{ t('zx.contacts.createSuc') }}</div>
            <div class="suc-text">{{ t('zx.contacts.orgManager') }}</div>
            <template v-if="isRinkol || isRingkolDesktop">
              <div class="suc-auth" @click="goOrgAuth">
                <img class="suc-auth-logo" :src="formData.logo || defaultLogo" alt="">
                <div class="suc-auth-text">
                  <div class="suc-auth-title">
                    {{ t('zx.account.orgAuth') }}
                    <div v-if="showCreatOrgTag" class="tips"><img src="@/assets/square/org_steer.gif" alt=""></div>
                  </div>
                  <div class="suc-auth-tip">
                    {{ t('zx.account.orgAuthTip') }}
                  </div>
                </div>
                <img class="suc-auth-icon" src="@/assets/svg/icon__right.svg" alt="">
              </div>
            </template>
            <div
              style="display: flex;justify-content: space-around;align-items: center;gap: 12px;width: calc(100% - 32px);position: fixed;bottom: 42px;">

              <template v-if="isRinkol || isRingkolDesktop">
                <van-button type="default" style="min-width: 80px;padding: 7px 24px;flex: 1; font-size: 17px;" block
                  @click="inviteMember">{{ t('zx.account.inviteMember') }}</van-button>
              </template>
              <van-button type="primary" @click="createSuc"
                style="min-width: 80px;padding: 7px 24px;flex: 1; font-size: 17px;" block>{{ t('contacts.finish')
                }}</van-button>
            </div>
          </div>
        </div>
      </div>
    </template>
  </home>
</template>

<script setup lang="ts">
  console.log('进这个页面了');

  import {
    Form as VanForm,
    Field as VanField,
    Checkbox as VanCheckbox,
    Picker as VanPicker,
    Cascader as VanCascader,
    Popup as VanPopup,
    Button as VanButton,
    Icon as VanIcon
  } from 'vant';
  import { MessagePlugin } from 'tdesign-vue-next';
  import home from '../homeIndex.vue';
  import vanUpload from '@/components/keeppx/account/VanUploader.vue';
  import { ref, onMounted, watch } from 'vue';
  import company from '@/assets/img/company.svg';
  import business from '@/assets/img/business.svg';
  import selfemployed from '@/assets/img/selfemployed.svg';
  import other from '@/assets/img/other.svg';
  import government from '@/assets/svg/government.svg';
  import upload from '@/components/keeppx/upload.vue';
  import { countList } from '../account/constant';
  import { createOrg } from '@/api/member/member';
  import { debounce } from "lodash";
  import defaultLogo from "@/assets/img/logo_img_logo.png";

  import { classifyTree } from '@/api/business/index';

  import { useOrganizeStore } from '@/stores/org';
  import { useI18n } from 'vue-i18n';
  import { useMemberStore } from '@/stores/member';

  import { listenKeybord, activeElementScrollIntoView } from '@/views/keeppx/account/util';
  const accountStore = useAccountStore();
  const { t } = useI18n();
  const store = useOrganizeStore();
  import { useAccountStore } from '@/stores/account';
  const isRinkol = accountStore.isRingkol;
  const isRingkolDesktop = accountStore.isRingkolDesktop;
  import { sendWxMsg } from "@/utils/myUtils";

  import { useRoute, useRouter } from 'vue-router';
  import { getTrialAnnualFee } from '@/api/square/post';
  import to from 'await-to-js';
  // localStorage.setItem('main_token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************.QiTWQTSDUj7qzsJ92XIGLbp_ZdB6haEHMrhu5qAhumE');
  // localStorage.setItem('appConfig', '{"Brightness":"system","Locale":"zh-hans-CN"}');
  // localStorage.setItem('lang', 'zh-hans-CN');
  // localStorage.setItem('openid', '0pgjk00015ydqslzry');
  // localStorage.setItem('userInfo', '{"cellphone":"***********","region":"86","area":"CN","avatar":"https://img.kuaiyouyi.com/avatar/f03648ed61bb6572be67bd46f8832acb/********/ios/bea60c4aa34f80b7e01dcb99fb7e5a08.jpg","email":"","openid":"0pgjk00015ydqslzry","attachments":null,"gender":0,"hidden":null,"title":"🦉","telephone":"","slogan":"","birthday":0,"link_id":"RK-481296","address":"","removed":false,"account_email":"","account_mobile":"***********","account_mobile_region":"86","psn_auth":false}');
  const router = useRouter();
  const route = useRoute();
  const showcascader = ref(false);
  const fieldValue = ref('');
  const fieldNames = { text: 'name', value: 'id', children: 'children' };
  const sendmsg1 = () => {
    sendWxMsg({
      type: 'FROM_H5',
      status: '200',
      path: '/pages/app-home/index'
    })
  }
  const sendmsg2 = () => {
    sendWxMsg({
      type: 'FROM_H5',
      status: '200',
      path: '/pages/app-home/index'
    })
  }
  const nextDisabled = ref(false);

  const suc = ref(false);
  console.log('进这个页面了44444');
  const showCreatOrgTag = ref(false);
  watch(() => suc.value, async (val: boolean) => {
    console.log(val, '打印了这里');

    if (val) {
      const teamId = createTeamId.value;
      const [err, res] = await to(getTrialAnnualFee(teamId));
      if (err) return;
      if (res?.total) {
        showCreatOrgTag.value = true;
      }
    }
  });
  console.log('进这个页面了555555');

  const getProfileInfo = () => {
    try {

      let userInfo = JSON.parse(window.localStorage.getItem('userInfo') as string);
      const staffPhone = userInfo?.account_mobile;
      const staffTelCode = userInfo?.account_mobile_region;
      const staffName = userInfo?.title;
      if (staffPhone && staffTelCode) {
        return { staffName, staffTelCode, staffPhone, code: '' };
      }
      return null;
    } catch (error) {
      return null;

    }
  }

  const redirect = (route.query.redirect as string) || '';
  const from = (route.query.from as string) || '';
  const inviteCode = (route.query.inviteCode as string) || '';
  const orgType = [
    // {
    //   icon: government,
    //   name: t('zx.contacts.government'),
    //   value: 4,
    // },
    {
      icon: business,
      name: t('zx.contacts.association'),
      value: 2,
    },
    {
      icon: company,
      name: t('zx.contacts.firm'),
      value: 1,
    },
    {
      icon: selfemployed,
      name: t('zx.contacts.individual'),
      value: 3,
    },
    {
      icon: other,
      name: t('zx.setting.other'),
      value: 0,
    },
  ];
  const form: any = ref(null);
  const formData: any = ref({
    type: 2,
    logo: '',
    industry: '',
    fullName: '',
    region: '',
    teamMailApproved: false,
    teamMailDomain: '',
  });
  let options = ref([]);
  console.log('进这个页面了56666666');

  const getClassifyTree = (val?) => {
    classifyTree(val).then((res: any) => {
      console.log(res, 'resssq');
      options.value = removeEmptyChildren(res?.data);
      console.log(options.value, '223232');
    });
  };
  const removeEmptyChildren = (res: any) => {
    if (res?.length === 0) {
      return res;
    }
    // eslint-disable-next-line @typescript-eslint/prefer-for-of
    for (let i = 0; i < res.length; i++) {
      const item = res[i];
      if (Array.isArray(item.children) && item.children.length === 0) {
        delete item.children;
      } else {
        item.children = removeEmptyChildren(item.children);
      }
    }
    return res;
  };
  console.log('进这个页面了77777');

  getClassifyTree();
  const formRules = ref({
    type: [{ required: true, message: t('contacts.pleaseSelect'), type: 'error' }],
    fullName: [{ required: true, message: t('contacts.pleaseInput'), type: 'error' }],
    region: [{ required: true, message: t('contacts.pleaseSelect'), type: 'error' }],
    teamMailDomain: [{ required: true, message: t('contacts.pleaseInput'), type: 'error' }],
  });
  const onFinish = ({ selectedOptions }) => {
    console.log(formData.value, 'formData.vavaluelue');

    console.log(selectedOptions, 'selectedOptionsselectedOptions');

    showcascader.value = false;
    fieldValue.value = selectedOptions.map((option) => option.name).join('/');
  };
  console.log('进这个页面了77888888');

  const showPicker = ref(false);
  const regionName = ref('');

  const changeType = (item: any) => {
    formData.value.type = item.value;
  };
  const uploadImg = (val: any) => {
    formData.value.logo = val[0].url;
  };
  const back = () => {
    router.go(-1);
  };
  const updateLogo = (v) => {
    formData.value.logo = v.url;
  };
  console.log('进这个页面了9999999');

  const next = debounce(() => {
    const fromType = route.query.from;
    console.error(fromType, 'fromType');
    // 判断页面是否直接返回上一页/还是直接下一步
    const isCodeVerification = fromType === 'codeVerification';
    nextDisabled.value = true;
    form.value.validate().then((res: any) => {
      if (!res) {
        !formData.value.teamMailApproved && (formData.value.teamMailDomain = '');
        store.setOrgInfo(formData.value);

        const profile = getProfileInfo();
        if (!profile) {
          router.push({
            name: 'orgPersonal',
            query: {
              redirect,
              from,
              inviteCode,
            },
          });
          nextDisabled.value = false;
        } else {
          const params = { ...store.orgInfo, ...profile };
          params.teamMailApproved = Number(params.teamMailApproved);
          createOrg(params).then(({ data }) => {
            createTeamId.value = data?.teamId;
            nextDisabled.value = false;
            if (isCodeVerification) {
              console.log('codeVerification');
              try {
                window.RingkolAppChannel.postMessage(JSON.stringify({ "method": "appRoutePop", "data": { teamId: data?.teamId } }))
              } catch (err) {
                console.error(err, 'err');
              }
            } else if (from === 'square') {
              window.location.href = `ringkol://ringkol.com/square-tissue?teamId=${data?.teamId}&type=${params.type}&region=${params.region}&inviteCode=${inviteCode}`
            } else {
              suc.value = true;
            }
          }).catch((err: any) => {
            nextDisabled.value = false;
            MessagePlugin.error({
              content: err?.response?.data?.message || err?.message,
              duration: 3000,
            })
          });
        }
      }
    }).catch(() => {
      nextDisabled.value = false;
    });
  }, 500);
  const onConfirm = ({ selectedOptions }) => {
    formData.value.region = selectedOptions[0].code;
    regionName.value = selectedOptions[0].name;
    showPicker.value = false;

    fieldValue.value = '';
    getClassifyTree(formData.value.region);
  };

  const createSuc = () => {
    // 在app中，如果有redirect，跳h5路由，否则app返回上一页
    if (isRinkol) {
      if (redirect) {
        router.push(decodeURIComponent(redirect));
        return;
      }
      window.RingkolAppChannel?.postMessage(JSON.stringify({ "method": "appRoutePop" }));
      return;
    }
    router.push(decodeURIComponent(redirect));
  }
  console.log('进这个页面了************');

  const clickIndustry = () => {
    regionName.value ? (showcascader.value = true) : MessagePlugin.warning({
      content: t('zx.account.industryTip'),
      duration: 3000,
    });
  };
  const inviteMember = () => {
    // 跳转
    isRinkol ?
      window.RingkolAppChannel?.postMessage(JSON.stringify({
        method: "jumpTpTeamInvite",
        data: {
          teamId: createTeamId.value,
          teamName: formData.value.fullName,
          logo: formData.value.logo,
          isAdmin: 1,
          canAdmin: 1
        },
      })) :
      router.push(decodeURIComponent(redirect));
  };
  const createTeamId = ref('');
  const goOrgAuth = () => {
    // window.location.href = `ringkol://ringkol.com/square-tissue?teamId=${createTeamId.value}&type=${formData.value.type}&region=${formData.value.region}&inviteCode=${inviteCode}`
    isRinkol ?
      window.RingkolAppChannel?.postMessage(JSON.stringify({
        method: "appOrgCertified",
        data: {
          teamId: createTeamId.value,
          type: formData.value.type,
          region: formData.value.region,
        },
      })) :
      router.push(decodeURIComponent(redirect));
  };
  const keybordType = ref < 'up' | 'down' > ('down');
  const listenKeybordCb = (element, type) => {
    keybordType.value = type;
    if (type === 'up') {
      activeElementScrollIntoView(element, 10);
    }
  };
  console.log('进这个页面了2232319999999');

  onMounted(() => {

    console.log('进这个页面onMounted');

    const $inputs = document.querySelectorAll('input[type="text"]');

    for (let i = 0; i < $inputs?.length; i++) {
      listenKeybord($inputs[i], listenKeybordCb);
    }
  });
</script>

<style lang="less" scoped>
  @import url('../css/base.less');

  .tip {
    width: 100%;
    height: 66px;
    background-color: #f0f8ff;
    font-size: 12px;
    // font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;
    text-align: left;
    color: #2069e3;
    padding: 12px 16px 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .bold {
      font-size: 14px;
      font-weight: 700;
    }
  }

  .tform {
    padding: 2px 16px 16px;

    .split {
      width: calc(100% + 32px);
      margin-left: -16px;
      height: 8px;
      background-color: #f1f2f5;
    }
  }

  :deep(.orgTypeCell) {
    ::-webkit-scrollbar {
      display: none;
    }

    .van-cell__value {
      width: 100%;
      overflow-x: auto;
    }
  }

  .tabs {
    display: flex;
    justify-content: space-between;
    width: 100%;

    .tab-item {
      width: 62px;
      height: 62px;
      background: var(--bg-kyy_color_bg_deep, #F5F8FE);
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
      padding: 4px 4px 8px 4px;
      position: relative;

      color: var(--text-kyy_color_text_1, #1A2139);
      text-align: center;
      font-feature-settings: 'clig' off, 'liga' off;

      /* kyy_fontSize_1/regular */
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      /* 166.667% */
    }

    .active {
      border: 1px solid #2069e3;
    }

    .tab-sel {
      width: 20px;
      height: 20px;
      position: absolute;
      bottom: -1px;
      right: -1px;
    }
  }

  :deep(.t-form-item-margin) {
    margin-bottom: 8px;
  }

  :deep(.t-form__item) {
    margin-bottom: 16px;
  }

  :deep(.van-cascader__title) {
    width: 100%;
    text-align: center;
    padding-left: 22px;
  }

  :deep(.van-cascader__option) {
    border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  }

  /* :deep(.van-icon-success::before) {
    content: '';
    width: 20px;
    height: 20px;
    background-image: url('../../../assets/empty.png');
  }
:deep(.van-cascader__selected-icon) {

} */
  :deep(.van-tab--active) {
    span {
      color: #4D5EFF;
    }
  }

  :deep(.van-tabs__line) {
    background: #4D5EFF;
  }

  :deep(.van-cascader__option) {
    height: 50px;
  }

  :deep(.van-cascader__selected-icon) :deep(.van-icon-success:before) {
    content: '@/assets/images/icon_other.png';
    width: 20px;
    height: 20px;
  }

  .upload-tip {
    font-size: 12px;
    // font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;
    text-align: left;
    color: #a1a2a4;
    line-height: 20px;
    margin-left: 12px;
  }

  :deep(.van-picker__confirm) {
    color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff);
  }


  .f-c-c {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .suc-container {
    height: 100%;
    max-width: 600px;

    padding: 32px 16px;
  }


  .suc-icon {
    width: 64px;
    height: 64px;
  }

  .suc-bold {
    color: var(--text-kyy_color_text_1, #1A2139);
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;

    /* kyy_fontSize_3/bold */
    font-family: "PingFang SC";
    font-size: 17px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px;
    /* 152.941% */
    margin: 16px 0 4px;
  }

  .suc-text {
    color: var(--text-kyy_color_text_3, #828DA5);
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    margin-bottom: 32px;
  }

  :deep(.industry_cascader) {
    .van-icon.van-icon-success.van-cascader__selected-icon {
      display: none;
    }
  }

  .suc-auth {
    display: flex;
    padding: 12px 16px;
    align-items: center;
    gap: 12px;
    align-self: stretch;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    cursor: pointer;

    .suc-auth-logo {
      border-radius: 50%;
      width: 44px;
      height: 44px;
    }

    .suc-auth-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .suc-auth-title {
      position: relative;
      display: flex;
      color: var(--text-kyy_color_text_1, #1A2139);
      font-feature-settings: 'clig' off, 'liga' off;

      /* kyy_fontSize_3/bold */
      font-family: "PingFang SC";
      font-size: 17px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px;

      /* 152.941% */
      .tips {
        display: flex;
        align-items: center;
        margin-left: 5px;

        img {
          height: 20px;
        }
      }
    }

    .suc-auth-tip {
      color: var(--text-kyy_color_text_3, #828DA5);
      font-feature-settings: 'clig' off, 'liga' off;

      /* kyy_fontSize_2/regular */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .suc-auth-icon {
      width: 24px;
      height: 24px;
    }
  }

  .keybordType-box {
    height: 25px;

    .keybordType-button {
      position: fixed;
      bottom: 10px;
    }
  }
</style>
