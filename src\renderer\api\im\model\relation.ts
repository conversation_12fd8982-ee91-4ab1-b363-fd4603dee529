import { GroupToSave } from "customTypes/message";

/**
 * 身份卡信息
 */
export interface Card {
    avatar: string;
    cardId: string;
    cardName: string;
    cellphone: string[];
    created: string;
    departments: string[];
    internalTeamId: string;
    internalTeamName: string;
    lid: string;
    openid: string;
    openImUserID: string;
    platform: string[];
    removed: string;
    roles: string[];
    slogan: string;
    teamId: string;
    teamName: string;
    updated: string;
}
export interface IRelationPrivate {
    sync_msg_at: any;
    /**
     * 接受者认可的时间戳
     */
    approved?: number;
    /**
     * 创建时间
     */
    created?: number;
    /**
     * 所有者个人标识符
     */
    main?: string;
    /**
     * 来源描述
     */
    origin?: string;
    /**
     * 接受者个人标识符
     */
    peer?: string;
    /**
     * 删除时间
     */
    removed?: number;
    /**
     * 机构ID
     */
    team?: string;
    /**
     * 更新时间
     */
    updated?: number;
    /**
     * 对方是否解除了关系
     */
    hasDel?: boolean;
    /**
     * 免打扰
     */
    noDisturb?: boolean;
    /**
     * 置顶
     */
    pin?: boolean;
    /**
     * 备注
     */
    comment?: string;
    /**
     * 描述
     */
    describe?: string;
    /**
     * 对方是否注销
     */
    unregistered?: boolean;
    mainOpenImID?:string;
    peerOpenImID?:string;
    conversationId?: string;
    /**
     * 附加数据
     */
    attachment?: {
        // 关系建立者
        creatorCardId?: string;
        relation?: 'CO_WORKER' | 'FRIEND' | 'BUSINESS' | 'CONSULT' | 'TEMPORARY' | 'PLATFORM_FRIEND';
        member: ConversationMemberToSave[];
    }
    card?: Card;// 对方身份卡信息
    myCard?: Card;// 我的身份卡信息
    groupType?: number;// 0 1 常用分组
}

export interface IRelationGroup extends GroupToSave {
    group: string;
    conversationId: string;

    /**
     * 身份卡群成员(对方)
     */
    /**
     * 身份卡群成员(自己)
     */
    members: IGroupMember[];
    /**
     * 我自己的身份卡
     */
    myCards?: IGroupMember[];
    syncMsgAt?:string
    syncMsgDone?:string
}

export interface IGroupMember {
    /**
     * 群设置附加数据
     * 如果群的类型是 知行助手、云盘助手、审批助手、广场助手、单聊申请、小秘书商务关节处、新成员申请，则 attachments为空
     * 如果群的类型是 普通群、部门群、公司群、内部群、外部群，则 attachments 为群设置附加数据
     */
    // attachments: ConversationMemberToSave;
    group: string;
    group_type?: number;// GT_DEFAULT 默认分组，常用分组
    /**
     * 此处的 openid 是身份卡id，
     * openId 在 attachments
     */
    openid: string;
    memberid: string;
    /**
     * 加入时间戳
     */
    joined?: number;
    /**
     * 认证、徽章
     */
    badges?: string[];
    /**
     * 群内角色
     * 管理员:ADMIN，群主:OWER，群员:MEMBER
     */
    roles?: string[];
    /**
     * @deprecated
     */
    banned?: number;
    /**
     * @deprecated
     */
    channel?: string;
    /**
     * @deprecated
     */
    disabled?: number;
    /**
     * 最后活跃时间
     */
    latest?: number;
    /**
     * 退出、移除时间戳（即退群）
     */
    removed?: number;
    /**
     * 是否添加到通讯录
     */
    addr?: boolean;
    /**
     * 是否删除聊天历史
     */
    history?: boolean,
    /**
     * 是否自己退群
     */
    self_delete?: boolean,
    /**
     * 是否农历
     * @type {boolean}
     * @memberof IGroupMember
     */
    lunar?: boolean;
    /**
     * 出生时间戳
     * @type {number}
     * @memberof IGroupMember
     */
    born_at?: number;
    /**
     * 生日
     * @type {string}
     * @memberof IGroupMember
     */
    birthday?: string;
    /**
     * 是否开启天气通知
     * @type {boolean}
     * @memberof IGroupMember
     */
    weather?: boolean;
    /**
     * 纬度
     * @type {string}
     * @memberof IGroupMember
     */
    lat?: string;
    /**
     * 经度
     * @type {string}
     * @memberof IGroupMember
     */
    lon?: string;
    /**
     * 地址
     * @type {string}
     * @memberof IGroupMember
     */
    address?: string;
    /**
     * 提醒时间
     * @type {number}
     * @memberof IGroupMember
     */
    notice_at?: number;
    /**
     * 位置模式
     * @type {string}
     * @memberof IGroupMember
     * 自动:AUTO、自定义设置:SET
     */
    geo_mode?: string;
    /**
     * 群身份标签ID
     * @type {string}
     * @memberof IGroupMember
     */
    label_id?:string

    openImId?:string
    pin: boolean; // 置顶
    noDisturb: boolean; // 免打扰

}

/**
 * 群组信息
 */
export interface IGroupInfo extends IRelationGroup {
    /**
     * 创建人
     */
    creator: string;
    /**
     * 组织归属地
     */
    host: string;
    /**
     * 删除时间
     */
    removed: number;
    /**
     * 0:非限定 1:会话级 2:关系链
     */
    scene: number;
    /**
     * 所属组织，为空表示跨组织
     */
    team: string;
    /**
     * 更新时间
     */
    updated: number;
}

export interface IGroupCreate {
    /**
     * 群组信息
     */
    group: IGroupInfo;
    /**
     * 邀请的成员
     */
    members: IGroupMember[];
    /**
     * 融云ID
     */
    rids: string[];
}

/**
 * 群更新
 */

export type IGroupUpdate = Pick<GroupToSave, 'group' | 'name'  | 'sync_disk' | 'disk_folder' | 'attachment' | 'birthday_notify' | 'birthday_style'> & {card_id: string};

/**
 * 群角色更新
 */
export interface IGroupMemberRoleUpdate {
    group: string,
    members: string[],
    roles: string[]
}

/**
 * 群成员添加
 */
export interface IGroupMemberAdd {
    // 群id
    group: string,
    // 融云id
    rids: string[],
    // 群成员信息
    members: IGroupMember[],
    // 操作者身份卡id
    op_card_id: string
}

/**
 * 群成员移除
 */
export interface IGroupMemberRemove {
    // 群 id
    group: string,
    // 融云ID，最后一个身份卡才传
    rids: string[],
    // 身份卡ID
    ids: string[],
    // 操作者身份卡ID
    op_card_id: string
}

export type GroupInviteStatus =
| "SEND" //发起申请
| "REJECT" //拒绝
| "PASS" //通过

// 提交群成员邀请
export interface IGroupInvite {
    gid: string,
    applicant: string,
    typ: 0,
    handler: string[],
    card_ids:string[],
    members: IGroupMember[],
    /**
     * SEND   发起申请
     * REJECT 拒绝
     * PASS   通过
     */
    status?: GroupInviteStatus,
    content?: string,
}

// 群成员邀请申请列表
export interface IGroupInviteQuery {
    // 管理员和群主
    card_ids: string[]
    gid: string,
    status: GroupInviteStatus
    typ: 0,
}

// 同意、拒绝群成员邀请
export interface IGroupInviteApprove {
    id: string,
    status: GroupInviteStatus
    op_card_id: string
}

export interface IGroupInviteItem {
    openid: string,
    applicant: string,
    handler: string[],
    typ: number,
    status: GroupInviteStatus,
    card_ids: string[],
    content: string,
    gid: string,
    members: IGroupMember[]
}

// 助手列表返回的数据
export interface MyAssistants {
    assistantId: string;
    name: string;
    avatar: string;
    assistantType?: number;
    stayOn?: number;
    noDisturb?: number;
    openid: string;
    conversationId: string;
    createdAt: string;
    updatedAt: string;
    deletedAt?: string;
}

export type GroupTabKey = 'all' | 'friend' | 'internal' | 'external' | 'community'

export type GroupTabItem = {
  image: string
  key: GroupTabKey
  sort: number
  value: string
  count: number
}
