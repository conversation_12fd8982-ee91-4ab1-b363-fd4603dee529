<template>
  <div class="advertising-space" ref="elementRef">
    <div class="swiper-box" :class="placeType1[current]?.source_type===1?'guanggao':''">
      <!-- 商铺广告位 -->
      <t-swiper animation="fade" class="big-ad-swiper" :class="placeType1.length < 2 ? 'not-dian' : ''"
        :autoplay="autoplay" trigger="click" @change="onChanges" v-if="placeType1.length > 0 && showFlag"
        v-model:current="current" :duration="600"
        :navigation="{ type: 'dots', showSlideBtn: placeType1.length < 2 ? 'never' : 'hover' }" loop :interval="5000">
        <t-swiper-item @click="viewDetail(item,current)" style="border-radius: 8px;height: 308px;"
          v-for="item in placeType1" :key="item.id">
          <img class="swiper-ad-img" :src="item.image_url" />

        </t-swiper-item>
      </t-swiper>
      <!-- 默认图片 -->
      <img v-else class="swiper-ad-img" style="height: 308px;" :src="curLang ? 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/Rectangle34626377.png' : 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/ftsclb.png'" />

    </div>
    <div class="big-ad-img-box">
      <div :class="placeType2[setMarketplaceRightTopIndex]?.source_type===1?'guanggao':''"
        style="width: 224px; height: 148px;">
        <img v-if="placeType2.length > 0 && setMarketplaceRightTopIndex !== null"
          @click="viewDetail(placeType2[setMarketplaceRightTopIndex],setMarketplaceRightTopIndex)"
          :src="placeType2[setMarketplaceRightTopIndex]?.image_url" />
        <!-- <img v-else :src="curLang ? jtscys : ftscys" /> -->
        <img v-else :src="curLang ? 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/scdpys.png' : 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/scdpys.png'" />

      </div>
      <div :class="placeType3[setMarketplaceRightBottomIndex]?.source_type===1?'guanggao':''"
        style="width: 224px; height: 148px;">

        <img v-if="placeType3.length > 0 && setMarketplaceRightBottomIndex !== null"
          @click="viewDetail(placeType3[setMarketplaceRightBottomIndex],setMarketplaceRightBottomIndex)"
          :src="placeType3[setMarketplaceRightBottomIndex]?.image_url" />
        <img v-else :src="curLang ? 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/scdpyx.png' : 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/ftyx.png'" />

      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="advertising-space">
  import { ref, watch, watchEffect, onMounted, onUnmounted, toRaw } from "vue";
  import { useRoute, useRouter } from "vue-router";
  import { adfrontadshow, adfrontclickshow } from "@/views/big-market/apis";
  import { getProfilesInfo } from "@renderer/utils/auth";
  import { toSquareHome } from "@renderer/views/square/utils/ipcHelper";
  import useNavigate from "@renderer/views/square/hooks/navigate";
  import { getOpenid } from "@renderer/utils/auth";
  import { getSquareByopenId } from "@/api/business/manage";
  import { ClientSide } from "@renderer/types/enumer";
  import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
  import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
  import { getNowDigitalType } from "@renderer/views/digital-platform/marketplace/utils/index.ts"; 
  import { platform } from "@renderer/views/digital-platform/utils/constant";
  import { getLang } from "@renderer/utils/auth";
  import LynkerSDK from "@renderer/_jssdk";
  const { ipcRenderer } = LynkerSDK;
  import { configInfo, isNotMac } from '@renderer/views/setting/util';
  import { useTabsStore } from "@renderer/components/page-header/store";

  const route = useRoute();
  const tabStore = useTabsStore(); // 广场那边用到
  const isSquare = route?.query?.platform === 'square';

  const ftsclb = new URL("@/assets/member/ptsc/ftsclb.png", import.meta.url);
  const jtsclb = new URL("@/assets/member/ptsc/jtsclb.png", import.meta.url);

  const jtscys = new URL("@/assets/member/ptsc/jtscys.png", import.meta.url);

  const ftscys = new URL("@/assets/member/ptsc/ftscys.png", import.meta.url);

  const jtscyx = new URL("@/assets/member/ptsc/jtscyx.png", import.meta.url);

  const ftscyx = new URL("@/assets/member/ptsc/ftscyx.png", import.meta.url);

  import to from "await-to-js";
  const digitalPlatformStore = useDigitalPlatformStore();
  const digitalRouter = useRouterHelper("digitalPlatformIndex");
  const { goHomePage } = useNavigate();

  const props = defineProps({
    teamId: {
      type: String,
      default: "",
    },
  });
  const autoplay = ref(true);
  const elementRef = ref(null);
  const isInViewport = ref(false);
  let current = ref(0);
  const profileInfo = getProfilesInfo();
  const setMarketplaceRightTopIndex = ref(null);
  const setMarketplaceRightBottomIndex = ref(null);
  const MarketplaceSwiper = ref(window.localStorage.getItem("MarketplaceSwiperIndexMemberShop")); //轮播索引
  const placeType1 = ref([]);
  const placeType2 = ref([]);
  const placeType3 = ref([]);
  const placeType1Length = ref([]);
  const placeType2Length = ref([]);
  const placeType3Length = ref([]);
  const onChanges = (val, val1) => {
    console.log(autoplay.value, current.value, placeType1.value[current.value].ad_id, "有触发吗");
    if (autoplay.value) {
      setMarketplaceSwiper();
      setViewNumSwiper(); //增加访问数
    }
  };
  let curLang = getLang() == "zh-cn" || !getLang();
  const setMarketplaceSwiper = () => {
    window.localStorage.setItem("MarketplaceSwiperIndexMemberShop", current.value);
  };
  const setMarketplaceRightTop = () => {
    window.localStorage.setItem("setMarketplaceRightTopIndexMemberShop", setMarketplaceRightTopIndex.value);
  };
  const setMarketplaceRightBottom = () => {
    window.localStorage.setItem("setMarketplaceRightBottomIndexMemberShop", setMarketplaceRightBottomIndex.value);
  };
  const setViewNumRightBottom = () => {

    if (placeType3Length.value.includes(setMarketplaceRightBottomIndex.value)) {
      if (placeType3.value[setMarketplaceRightBottomIndex.value]?.ad_id) {

        adfrontclickshow(
          {
            ad_id: placeType3.value[setMarketplaceRightBottomIndex.value].ad_id,
            add_show: true,
            add_click: false,
          },
          props.teamId,
        );
      }
    }
  };

  const setViewNumRightTop = () => {
    if (placeType2Length.value.includes(setMarketplaceRightTopIndex.value)) {
      adfrontclickshow(
        {
          ad_id: placeType2.value[setMarketplaceRightTopIndex.value].ad_id,
          add_show: true,
          add_click: false,
        },
        props.teamId,
      );
    }
  };
  const setViewNumSwiper = () => {
    if (placeType1Length.value.includes(current.value)) {
      adfrontclickshow(
        {
          ad_id: placeType1.value[current.value].ad_id,
          add_show: true,
          add_click: false,
        },
        props.teamId,
      );
      const index = placeType1Length.value.indexOf(current.value);
      if (index > -1) {
        placeType1Length.value.splice(index, 1);
      }
    }
  };
  const isSelfSquare = async () => {
    const [err, res] = await to(getSquareByopenId(getOpenid()));
    if (err) return false;
    const { data } = res;
    console.log("data", data);
    return data.opened && data.selfOpened;
  };
  const router = useRouter();
  const viewDetail = async (row, itemIndex) => {
    console.log(row, "riwwww");
    adfrontclickshow(
      {
        ad_id: row.ad_id,
        add_show: false,
        add_click: true,
      },
      props.teamId,
    );
    // const token = localStorage.getItem('main_token');
    if (row.skip_type === 5) {
      if (isSquare) {
        console.log('走的广场的');

        const url = LynkerSDK.getH5UrlWithParams(`shop/index.html#/product-detail/${row.skip_param}`, {
          // team_id: route.query?.team_id,
          // team_name: route.query?.team_name,
          platformTeamId: props.teamId,
          teamId: props.teamId,
          // platform: route.query?.platform,
          origin: route.query?.origin,
          tabId: `${route.query?.team_id}-${row.skip_param}`,
          // tabTitle: '商品详情' + '-' + route.query?.team_name,
          platformTeamName: route.query?.team_name,
        });
        LynkerSDK.square.openTabForWebview({ title: `商品详情-` + route.query?.team_name, url: url, path_uuid: `productDetailTab-${route.query?.team_id}-${row.skip_param}` })
        return;
      } else {

        const url = LynkerSDK.getH5UrlWithParams(`shop/index.html#/product-detail/${row.skip_param}`, { teamId: props.teamId, platformTeamId: props.teamId });
        console.log(url, '啊实打实');
        console.log(route?.query, '啊实打实route?.query');

        console.log(props.teamId, '啊实打实teamId');

        LynkerSDK.digitalPlatform.openTabForWebview({ title: `商品详情`, url: url, path_uuid: `productDetailTab-${row.skip_param}` })
      }

    }

    if (row.skip_type === 1) {
      // console.log("viewDetail", row);
      // const path = "/bigMarketIndex/bigMarketDetailReadOnly";
      // const query = { uuid: row.skip_param.toString(), from: "big-market" };
      // ipcRenderer.invoke("set-big-market-tab-item", {
      //   path: `/bigMarketIndex/bigMarketDetailReadOnly`,
      //   path_uuid: "bigMarket",
      //   title: "商机详情",
      //   addNew: true,
      //   query,
      //   name: "bigMarketDetailReadOnly",
      //   type: ClientSide.BIGMARKET,
      // });
      // router.push({ path, query });
      const type = getNowDigitalType();
      if (isSquare) {
        const pageKey = `square_digital_platform_${type}_rich_detail`;
        const fullPath = `/square-alone/${pageKey}?platform=square&team_id=${route?.query?.team_id}&uuid=${row.uuid}`;
        console.log(fullPath)
        tabStore.addTab({ label: `${row.title}`, fullPath, icon: 'marketvip' });
        router.push(fullPath);
        return;
      }
      const pageKey = `digital_platform_${type}_rich_detail`;
      const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(pageKey));
      searchMenu.query = { uuid: row.skip_param, platform: platform.digitalPlatform, from: type };
      router.push({ path: searchMenu.fullPath, query: searchMenu.query });
      searchMenu.title = "商机详情";
      digitalPlatformStore.addTab(toRaw(searchMenu), true);
    }
    if (row.skip_type === 2) {
      const data = { ip_region: "" };
      const res = await isSelfSquare();
      if (res) {
        toSquareHome(row.skip_param);
      } else {
        goHomePage({ squareId: row.skip_param });
      }
    }
    if (row.skip_type === 3) {
      LynkerSDK.shell.openExternal(row.skip_param);
    }

  };
  onUnmounted(() => { });
  // ipcRenderer.on("setMarketplaceSwiperIndex-swiper", () => {
  //   setMarketplaceSwiper();
  //   console.log("setMarketplaceSwipersetMarketplaceSwiper");
  // });
  let showFlag = ref(false);
  onMounted(async () => {
    await getList();
    placeType1Length.value = [];
    placeType2Length.value = [];
    placeType3Length.value = [];
    for (let index = 0; index < placeType1.value.length; index++) {
      placeType1Length.value.push(index);
    }
    for (let index = 0; index < placeType2.value.length; index++) {
      placeType2Length.value.push(index);
    }
    for (let index = 0; index < placeType3.value.length; index++) {
      placeType3Length.value.push(index);
    }
    if (MarketplaceSwiper.value) {
      let num = MarketplaceSwiper.value - 0 + 1;
      if (num > placeType1.value.length - 1) {
        current.value = 0;
      } else {
        current.value = num;
      }
    } else {
      current.value = 0;
    }

    showFlag.value = true;
    setMarketplaceSwiper();
    setViewNumSwiper();

    let numys = window.localStorage.getItem("setMarketplaceRightTopIndexMemberShop");
    if (numys || numys == 0) {
      if (numys >= placeType2.value.length - 1) {
        setMarketplaceRightTopIndex.value = 0;
      } else {
        setMarketplaceRightTopIndex.value = numys - 0 + 1;
      }
    } else {
      setMarketplaceRightTopIndex.value = 0;
    }
    let numyx = window.localStorage.getItem("setMarketplaceRightBottomIndexMemberShop");
    if (numyx || numyx == 0) {
      if (numyx >= placeType3.value.length - 1) {
        setMarketplaceRightBottomIndex.value = 0;
      } else {
        setMarketplaceRightBottomIndex.value = numyx - 0 + 1;
      }
    } else {
      setMarketplaceRightBottomIndex.value = 0;
    }
    setMarketplaceRightTop();
    setViewNumRightTop();
    setViewNumRightBottom();
    setMarketplaceRightBottom();
    const observer = new IntersectionObserver((entries) => {
      const entry = entries[0];
      isInViewport.value = entry.isIntersecting;
    });
    if (elementRef.value) {
      // 确保元素已经存在
      observer.observe(elementRef.value);
    }
  });
  const editAdSwiper = (val) => {
    console.log("开始或者暂停", val);
    if (isInViewport.value && val) {
      autoplay.value = true;
    } else {
      autoplay.value = false;
    }
  };

  const getList = async () => {
    console.log(props.addressData, "propspropsaddressDataaddressData");
    const type = getNowDigitalType() === "uni" ? 5: getNowDigitalType() === "association" ? 4 : getNowDigitalType() === "member" ? 1 : getNowDigitalType() === "politics" ? 2 : 3;
    const res1 = await adfrontadshow(
      {
        platform_type: type,
        place_type: 9,
      },
      props.teamId,
    );
    placeType1.value = res1.data.data.list;
    const res2 = await adfrontadshow(
      {
        platform_type: type,
        place_type: 10,
      },
      props.teamId,
    );
    placeType2.value = res2.data.data.list;

    const res3 = await adfrontadshow(
      {
        platform_type: type,
        place_type: 11,
      },
      props.teamId,
    );
    placeType3.value = res3.data.data.list;
  };
  // watch(
  //   () => route.path,
  //   () => {
  //     console.log(" route.path", route.path);
  //     if (route.path === "/bigMarketIndex/home" && route.query?.refresh) {
  //       console.log("homeRefresh");
  //     }
  //   },
  // );

  watch(
    () => props.addressData,
    () => {
      getList();
      console.log(props.addressData, "props.addressDataprops.addressData");
    },
  );
  watchEffect(() => {
    if (!isInViewport.value) {
      // 不在可视区时
      autoplay.value = false;
    } else {
      autoplay.value = true;
    }
    console.log(route, "路由");
    console.log(isInViewport.value, "再可视区吗");
  });
  defineExpose({
    editAdSwiper,
  });
</script>
<style lang="less" scoped>
  .not-dian {
    :deep(.t-swiper__navigation) {
      display: none;
    }
  }

  :deep(.t-swiper__content) {
    border-radius: 8px !important;
  }

  .guanggao {
    position: relative;
  }

  .guanggao::after {
    content: "广告";
    position: absolute;
    width: 32px;
    height: 20px;
    z-index: 999;
    right: -1px;
    top: -1px;
    border-radius: 0px 8px;
    background: #F5F8FE;
    color: #ACB3C0;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    padding: 0 4px;
  }

  .advertising-space {
    height: 308px;
    display: flex;
    gap: 12px;
    -webkit-user-select: none;
    /* Chrome/Safari */
    -moz-user-select: none;
    /* Firefox */
    -ms-user-select: none;
    /* IE10+ */
    user-select: none;
    /* Standard */
  }

  .swiper-ad-img {
    width: 836px;
    object-fit: cover;
    height: 308px;
    transform: translateZ(0);
  }

  .big-ad-img-box {
    display: flex;
    cursor: pointer;
    flex-wrap: wrap;
    gap: 12px;

    img {
      width: 320px;
      height: 148px;
      border-radius: 8px;
      flex-shrink: 0;
    }
  }

  :global(.big-ad-swiper .t-swiper__navigation-item) {
    scale: 2;
    margin: 0 8px;
  }

  .big-ad-swiper {
    width: 836px;
    height: 308px;
  }

  .swiper-box {
    border-radius: 8px;
  }

  :deep(.t-swiper__arrow-left) {
    width: 32px;
    height: 32px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
    border-radius: 50%;
    color: #fff;
  }

  :deep(.t-swiper__arrow-right) {
    width: 32px;
    height: 32px;
    color: #fff;
    border-radius: 50%;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }
</style>
