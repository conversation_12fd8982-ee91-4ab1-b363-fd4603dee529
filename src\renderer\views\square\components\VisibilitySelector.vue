<template>
  <t-popup
    v-model="visible"
    attach="body"
    destroy-on-close
    overlay-inner-class-name="d-square-o-setting"
    placement="left-top"
    v-bind="$attrs"
  >
    <slot :current-type="currentType" />

    <template #content>
      <div class="dropdown-select visibility">
        <div
          v-for="item in options"
          :key="item.value"
          :class="['item item-visible line-1', { active: item.value === currentType.value }]"
          @click="clickItem(item)"
        >
          <div class="content row line-1">
            <iconpark-icon class="icon" size="20" :name="iconDict[item.value]" />
            {{ item.content }}
            <template v-if="item.value === updateType && groupNames.length">（{{ groupNames.join('、') }}）</template>
          </div>
          <iconpark-icon class="check" size="20" name="iconcheckone" />
        </div>
      </div>
    </template>
  </t-popup>

  <FansGroupSelector
    v-if="props.useGroupSelector && groupVisible"
    v-model="groupVisible"
    v-model:fans-group-id="groupIds"
    :title="fansGroupTitle"
    @confirm="groupConfirm"
  />
</template>

<script lang="tsx">
export default {
  inheritAttrs: false,
};
</script>

<script lang="tsx" setup>
import {
  computed, inject, nextTick, ref, unref, watch,
} from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import { POST_IS_FROM_OUTER, POST_TEAM_ID, Visibility } from '@/views/square/constant';
import FansGroupSelector from '@/views/square/components/FansGroupSelector.vue';
import { useSquareStore } from '@/views/square/store/square';

const props = defineProps({
  modelValue: {
    type: String,
    default: Visibility.PUBLIC,
  },
  fansGroupId: {
    type: Array,
    default: () => [],
  },
  // 已选的部分可见/不给谁看的分组内名称列表
  groupNames: {
    type: Array,
    default: () => [],
  },
  // 为 true 时，在组件内处理粉丝组选择逻辑。
  // 否则，在外部独立处理（解决当 VisibilitySelector 被 t-popup 包裹时，FansGroupSelector 无法正常使用）
  useGroupSelector: {
    type: Boolean,
    default: true,
  },
  // 显示的选项（不指定显示全部）
  include: {
    type: Array,
    default: () => [],
  },
  teamId: String,
  stickOnTop: Boolean,
  promoting: Boolean,
  // 点击前确认
  onBeforeClick: Function,
});

const emit = defineEmits(['update:modelValue', 'update:fansGroupId', 'confirm']);
const visible = ref(false);
const lastVisibleType = ref<Visibility>();

const { t } = useI18n();
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit('update:modelValue', value);
  },
});

const groupIds = computed({
  get() {
    return props.fansGroupId;
  },
  set(value) {
    emit('update:fansGroupId', value);
  },
});

const store = useSquareStore();
const fansGroupId = ref(props.fansGroupId);
watch(() => props.fansGroupId, (val) => {
  fansGroupId.value = val;
});

const isFromOuter = inject(POST_IS_FROM_OUTER, false);
const injectTeamId = inject(POST_TEAM_ID, props.teamId || '');
const isPersonal = computed(() => {
  if (injectTeamId) return unref(injectTeamId) === '-1';
  return store.isPersonal || isFromOuter;
});

const allOptions = [
  { content: t('square.visibility.public'), simpleContent: t('square.visibility.publicSimple'), value: Visibility.PUBLIC },
  { content: t('square.visibility.fansOnly'), simpleContent: t('square.visibility.fansOnlySimple'), value: Visibility.FANS_ONLY },
  { content: t('square.visibility.private'), simpleContent: t('square.visibility.privateSimple'), value: Visibility.PRIVATE },
  { content: t('square.visibility.partiallyVisible'), simpleContent: t('square.visibility.partiallyVisible'), value: Visibility.PARTIALLY_VISIBLE },
  { content: t('square.visibility.partiallyInVisible'), simpleContent: t('square.visibility.partiallyInVisible'), value: Visibility.PARTIALLY_INVISIBLE },
];
const iconDict = {
  [Visibility.PUBLIC]: 'kejianxing-20',
  [Visibility.FANS_ONLY]: 'kejianxing-201',
  [Visibility.PRIVATE]: 'kejianxing-202',
  [Visibility.PARTIALLY_VISIBLE]: 'kejianxing-203',
  [Visibility.PARTIALLY_INVISIBLE]: 'kejianxing-204',
};
const options = computed(() => {
  if (props.include?.length) return allOptions.filter((v) => props.include.includes(v.value));
  if (isPersonal.value) return allOptions;
  return allOptions.filter((v) => [Visibility.PUBLIC, Visibility.PRIVATE].includes(v.value));
});

const updateType = ref(props.modelValue);
const currentType = computed(() => options.value.find((v) => v.value === value.value) || options.value[0]);

const tmpValue = ref('');
// const fansGroupTitle = computed(() => (tmpValue.value === Visibility.PARTIALLY_VISIBLE ? t('square.post.partVisible') : t('square.post.partInvisible')));
const fansGroupTitle = ref('');

const clickItem = async (item) => {
  if (props.onBeforeClick) {
    const valid = await props?.onBeforeClick?.(item);
    if (!valid) return;
  }

  visible.value = false;

  if (store.squareInfo.square.silenced) {
    await MessagePlugin.warning(t('square.square.limitTip'));
    return;
  }

  if (props.stickOnTop) {
    await MessagePlugin.warning('置顶动态不可修改可见范围');
    return;
  }

  // 由私密状态切为公开
  item.is2Public = currentType.value.value !== Visibility.PUBLIC && item.value === Visibility.PUBLIC;
  tmpValue.value = item.value;

  if (!props.useGroupSelector) {
    emit('confirm', item);
    return;
  }

  const needSelectGroup = [Visibility.PARTIALLY_VISIBLE, Visibility.PARTIALLY_INVISIBLE].includes(item.value);
  if (needSelectGroup) {
    // 判断上次选择的是否也是“部分可见”或者“不给谁看”，如果不是，则清空默认选项
    if (lastVisibleType.value !== item.value) {
      groupIds.value = [];
    }
    lastVisibleType.value = item.value;
    groupVisible.value = true;
    fansGroupTitle.value = item.value === Visibility.PARTIALLY_VISIBLE ? t('square.post.partVisible') : t('square.post.partInvisible');
  } else {
    value.value = item.value;
    // 第三个参数传 []，以清除上一次选择的 fansGroupId
    emit('confirm', item, []);
  }
};

// 选择分组
const groupVisible = ref(false);
const groupConfirm = async (ids, list) => {
  value.value = tmpValue.value;
  await nextTick();

  emit('confirm', currentType.value, ids, list);
  updateType.value = currentType.value.value;
};
</script>

<style lang="less">
.square-d-partially-invisible {
  .t-dialog {
    padding: 16px 16px 0;
  }
  .t-dialog__body {
    padding-bottom: 0;
  }
  .active {
    background-color: #F0F8FF;
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    .btn-icon {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      color: #2069e3;
    }
    .btn-wrap {
      flex: 1;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #13161b;
      cursor: pointer;
    }
  }
}
</style>

<style lang="less" scoped>
@import "../common";
</style>
