<template>
  <t-dialog
    v-model:visible="visible"
    :header="null"
    :close-btn="false"
    width="384"
    attach="body"
    class="dialogSet dialogSet-noBodyPadding"
    :close-on-overlay-click="false"
    :close-on-esc-keydown="false"
  >
    <template #body>
      <div class="suc-body">
        <div class="suc-icon">
          <img src="@renderer/assets/svg/icon_success.svg" class="w-48px h-48px" alt="" />
        </div>
        <div class="tip !mt-12px">{{$t('association.workbench.z')}}</div>
        <div class="descTip !mt-8px">{{$t('association.aso.a')}}<br>{{$t('association.aso.b1')}}</div>
      </div>
    </template>
    <template #footer>
      <div class="footer mt-16px">
        <t-button
          class="btn confirm"
          variant="base"
          theme="default"
          style="width: 80px"
          @click="onAfterSet"
        > {{$t('association.aso.c')}}</t-button>

        <t-button
          class="btn confirm"
          theme="primary"
          variant="base"
          style="width: 80px"
          @click="toVerifyWeb"
        > {{$t('association.aso.d')}}</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { useDigitalPlatformStore } from '@renderer/views/digital-platform/store/digital-platform-store';
import { goToAdmin } from '@renderer/views/uni/utils/auth';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
const store = useDigitalPlatformStore();
const props = defineProps({
  teamId: {
    type: Boolean,
    default: false,
  },
});
const router = useRouter();
const visible = ref(false);
const emits = defineEmits(['toVerifyWeb']);
const onClose = () => {
  visible.value = false;
};

const onOpen = () => {
  visible.value = true;
};

const onAfterSet = () => {
  onClose();
  store.removeAllTab();
  store.switchTab(0);
  if(store.tabs.length > 0) {
    router.push(store.tabs[0].fullPath)
  }
  bus.emit({ name: 'work-area-refresh' });

};

const toVerifyWeb = () => {
  emits('toVerifyWeb');
  console.log(props.teamId)
  goToAdmin(props.teamId)
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
.suc-body {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .suc-icon {

  }
  .tip {
    color: var(--text-kyy_color_text_1, #1A2139);
    text-align: center;

    /* kyy_fontSize_2/bold */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .descTip {
    color: var(--text-kyy_color_text_3, #828DA5);
    text-align: center;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }

}
.footer {
  display: flex;
  justify-content: center;
}
</style>
