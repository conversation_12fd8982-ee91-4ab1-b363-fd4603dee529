<template>
  <div>
    <t-dialog v-model:visible="curStep[0]" class="selectOrgType" :header="t('contacts.selectOrgType')" width="384"
      :footer="false" :zIndex="zIndex" :closeBtn="false" :close-on-overlay-click="false" :close-on-esc-keydown="false"
      :on-close="closeStep">
      <template #header>
        <div class="custom-header ">
          <span> {{ t('contacts.selectOrgType') }} </span>
          <div class="tricks">
            <Tricks size="small" :isDrag="false" uuid="通讯录-创建组织" />
          </div>
        </div>
        <iconpark-icon name="iconerror" class="btn-close" @click="curStep[0] = false; closeStep()" />
      </template>
      <template #body>
        <div style="min-height: 235px;">
          <div class="body-tip mb24">
            {{ t("contacts.pleaseSelectOrgType") }}
          </div>
          <div class="org-types">
            <div v-for="item in typeOptions" :key="item.value" class="type-item cursor-p"
              @click="chooseOrgType(item.value)">
              <img class="type-img" :src="item.icon" />
              <div class="label">{{ item.label }}</div>
            </div>
          </div>
          <!-- <t-form
            ref="formStep1"
            reset-type="initial"
            label-align="top"
            :data="formData"
            :rules="formRules"
          >
            <t-form-item :label="t('contacts.orgType')" name="type">
              <t-select v-replace-svg  v-model="formData.type">
                <t-option :label="t('contacts.other')" :value="0" />
                <t-option :label="t('contacts.enterprise')" :value="1" />
                <t-option :label="t('zx.contacts.association')" :value="2" />
                <t-option :label="t('zx.contacts.individual')" :value="3" />
              </t-select>
            </t-form-item>
          </t-form> -->
        </div>
      </template>
      <!-- <template #footer>
        <div>
          <t-button
            class="btn cancel"
            variant="outline"
            theme="default"
            @click="curStep[0] = false"
          >{{ t("account.cancel") }}</t-button>
          <t-button
            class="btn confirm"
            theme="primary"
            variant="base"
            :disabled="disabledArr[0]"
            @click="nextStep(1)"
          >{{ t("contacts.nextStep") }}</t-button>
        </div>
      </template> -->
    </t-dialog>
    <t-dialog v-model:visible="curStep[1]" class="selectOrgType height" :header="t('contacts.setOrgInfo')" width="384"
      :zIndex="zIndex" :close-on-overlay-click="false" :close-on-esc-keydown="false" :on-close-btn-click="closeStep">
      <!-- 通讯录创建组织 -->
      <template #body>
        <div>
          <div class="body-tip mb24">{{ t("contacts.setOrgTip") }}</div>
          <t-form ref="formStep2" reset-type="initial" label-align="top" :data="formData" :rules="formRules">
            <t-form-item :label="t('contacts.orgLogo')">
              <upload ref="uploadLogo" @update-upload="uploadImg" />
              <div class="logo-tips">
                {{ t("contacts.logoTip") }}
              </div>
            </t-form-item>
            <t-form-item :label="t('contacts.orgName')" name="fullName">
              <t-input v-model="formData.fullName" :maxlength="50" :placeholder="t('contacts.pleaseInput')" />
            </t-form-item>
            <t-form-item :label="t('contacts.orgAddress')" name="region">
              <t-select v-model="formData.region" v-replace-svg :placeholder="t('contacts.orgLocation')"
                @change="changeSelect">
                <t-option v-for="item in countList" :key="item.code" :value="item.code" :label="item.name"></t-option>
              </t-select>
            </t-form-item>
            <!-- 改动 -->
            <t-form-item label="行业" name="industry" class="nonebt24">
              <t-cascader v-model="formData.industry" trigger="hover" :disabled="!formData.region"
                :options="workOptions" :keys="{ label: 'name', value: 'id', children: 'children' }" clearable />
            </t-form-item>
            <!-- 改动 -->
            <t-checkbox v-model="formData.teamMailApproved" :class="[formData.teamMailApproved ? 'mt8 mb24' : 'mt8']">{{
              t("contacts.allowEmai")
              }}</t-checkbox>
            <t-form-item v-if="formData.teamMailApproved" :label="t('contacts.emailDomain')" name="teamMailDomain">
              <t-input v-model="formData.teamMailDomain" :maxlength="100" :placeholder="t('contacts.pleaseInput')" />
            </t-form-item>
          </t-form>
        </div>
      </template>
      <template #footer>
        <div style="padding-top: 16px;">
          <template v-if="![originType.Member,originType.Politics, originType.CBD].includes(props.from)">
            <t-button class="btn cancel" variant="outline" theme="default" @click="preStep(1)">{{
              t("contacts.preStep")
              }}</t-button>
          </template>
          <t-button class="btn confirm" theme="primary" variant="base" :disabled="disabledArr[1]"
            @click="nextStep(2)">{{ t("contacts.nextStep") }}</t-button>
        </div>
      </template>
    </t-dialog>
    <t-dialog v-model:visible="curStep[2]" class="selectOrgType" :header="t('contacts.bindMobile')" width="384"
      :zIndex="zIndex" :close-on-overlay-click="false" :close-on-esc-keydown="false">
      <template #body>
        <div>
          <div class="body-tip mb24">{{ t("contacts.bindMobileTip") }}</div>
          <t-form ref="formStep3" reset-type="initial" label-align="top" :data="formData" :rules="formRules">
            <!-- <t-form-item :label="t('contacts.userName')" name="staffName">
              <t-input v-model="formData.staffName" :maxlength="50" :placeholder="t('contacts.pleaseInput')" />
            </t-form-item> -->
            <t-form-item :label="t('contacts.tel')" name="staffPhone" class="tel">
              <t-input-adornment>
                <template #prepend>
                  <div>
                    <area-code v-model="formData.staffTelCode" />
                  </div>
                </template>
                <t-input v-model="formData.staffPhone" :placeholder="t('account.inputTel')" />
              </t-input-adornment>
            </t-form-item>
            <t-form-item :label="t('contacts.verifyCode')" name="code">
              <t-input v-model="formData.code" :placeholder="t('account.inputCode')">
                <template #suffix>
                  <div style="display: flex; align-items: center">
                    <div class="suffix--line" />
                    <div v-if="countdown <= 0" class="verify pointer" aria-role="button" @click="checkSM">
                      {{ t("account.sendCode") }}
                    </div>
                    <div v-else class="verify">
                      {{ `${t("account.resend")}${countdown}` }}
                    </div>
                  </div>
                </template>
              </t-input>
            </t-form-item>
            <!-- <t-checkbox v-model="agreeProto" style="margin-top: 15px">
              <span>{{ t('account.read') }}</span><a href="javascript:void(0)" @click="showIframe('PlatformServices')">{{ t('account.agreement') }}</a><a href="javascript:void(0)" @click="showIframe('PrivacyPolicy')">{{ t('account.privacy') }}</a>
              </t-checkbox
            > -->
          </t-form>
        </div>
      </template>
      <template #footer>
        <div>
          <t-button class="btn cancel" variant="outline" theme="default" @click="preStep(2)">{{
            t("contacts.preStep")
            }}</t-button>
          <t-button class="btn confirm" theme="primary" variant="base" style="width: 78px" :disabled="disabledArr[2]"
            @click="nextStep(3)">{{ props.from === "square" ? t("contacts.nextStep") : t("identity.confirm")
            }}</t-button>
        </div>
      </template>
    </t-dialog>
    <t-dialog v-model:visible="curStep[3]" class="selectOrgType" :header="false" :close-btn="false" width="384"
      :zIndex="zIndex" :close-on-overlay-click="false" :close-on-esc-keydown="false">
      <template #body>
        <div class="suc-body">
          <div class="suc-icon">
            <img src="@renderer/assets/svg/icon_success.svg" alt="" />
          </div>
          <div class="tip tac mt8">{{ t("contacts.createOrgSuc") }}</div>
          <div class="sub-tip tac">
            {{ t("contacts.createOrgSucTip") }}
          </div>
          <div class="go-back" @click="openAdmin">
            <div class="flex-ac">
              <img class="left-img" src="@renderer/assets/contacts/background_icon.svg" />
              <div class="right-info">
                <div class="tip">{{ t("contacts.manageBack") }}</div>
                <div class="sub-tip tips">{{ t("contacts.createOrgSucTip") }}</div>
              </div>
            </div>
            <img class="arrow" src="@renderer/assets/approval/icons/<EMAIL>" alt="" />
          </div>
          <div class="go-verify mt-12" @click="toVerifyWeb">
            <div class="flex-ac">
              <img class="left-img" src="@renderer/assets/contacts/organization_icon.svg" />
              <div class="right-info">
                <div class="tip">
                  {{ t("contacts.orgVerify") }}
                  <!-- <div v-if="showCreatOrgTag && hasChange" :class="['tip2', hasChange ? 'ani' : '']" @animationend="handleAnimationEnd">{{ hasChange ? t('contacts.createOrgTip2') : t('contacts.createOrgTip1') }}</div> -->
                  <div v-if="showCreatOrgTag" class="tips2"><img src="@renderer/assets/identity/org_steer.gif" alt="">
                  </div>
                </div>
                <div class="sub-tip tips">{{ t("contacts.createOrgSucTip2") }}</div>
              </div>
            </div>
            <img class="arrow" src="@renderer/assets/approval/icons/<EMAIL>" alt="" />
          </div>
          <!-- v-show="props.from !== originType.Connect" -->
          <!-- <div class="go-verify mt-12px" @click="openDigitalPlatform">
            <div class="flex-ac">
              <img class="left-img" src="@renderer/assets/member/svg/number_app.svg" />
              <div class="right-info">
                <div class="tip">{{ t("member.digital.k") }}</div>
                <div class="sub-tip tips">{{ t("member.digital.l") }}</div>
              </div>
            </div>
            <img class="arrow" src="@renderer/assets/approval/icons/<EMAIL>" alt="" />
          </div> -->

        </div>
      </template>
      <template #footer>
        <div>
          <t-button class="btn cancel" variant="outline" theme="default" @click="finishInvite">{{
            t("contacts.inviteMember")
            }}</t-button>
          <t-button class="btn confirm" theme="primary" variant="base" @click="finish">完成</t-button>
        </div>
      </template>
    </t-dialog>

    <!-- <t-dialog
      class="selectOrgType"
      v-model:visible="notifivisible"
      theme="info"
      width="384"
      :header="t('account.tip')"
      :close-btn="false"
      :close-on-overlay-click="false"
      :close-on-esc-keydown="false"
    >
      <div>
        <span style="color: #717376">{{ t("account.pleaseAgree") }}</span>

        <a href="javascript:void(0)" @click="showIframe('PlatformServices')">{{ t('account.agreement') }}</a><a href="javascript:void(0)" @click="showIframe('PrivacyPolicy')">{{ t('account.privacy') }}</a>

      </div>
      <template #footer>
        <div style="margin-right: 8px">
          <t-button class="btn cancel" variant="outline" theme="default" @click="notifivisible = false">{{
            t("account.cancel")
          }}</t-button>
          <t-button class="btn confirm" theme="primary" variant="base" @click="confirmAgree">{{
            t("account.agree")
          }}</t-button>
        </div>
      </template>
    </t-dialog> -->

    <tip v-model:visible="tipVisible" :tip="checkPhoneTip" :btn-confirm="t('zx.other.confirmChange')"
      :btn-cancel="t('account.cancel')" @onconfirm="changeRegion" />
    <invite-dialog ref="invite" :setting="teamSettingDetail" :team="queryOrganizaInfo" :position="myCardInfo.position"
      :invite="inviteType" />

    <orgAuth v-model:visible="orgAuthVisible" show-type="edit" from="address_book" :region="orgAuthDetail.region"
      :team-id="orgAuthDetail.teamId" :org-type="orgAuthDetail.orgType" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, nextTick, watch, onMounted } from "vue";
  import { useI18n } from "vue-i18n";
  import tip from "@renderer/views/setting/dialog/tip.vue";
  import { MessagePlugin } from "tdesign-vue-next";
  import {
    createOrg,
    marketClassifytree,
    teamSetting,
    getOrganizeList,
  } from "@renderer/api/contacts/api/organize";
  import { getTeamsDetailAxios } from "@renderer/api/member/api/businessApi";
  import { getInnerCardInfo } from "@renderer/api/contacts/api/common";
  import { getIdentifyCode, getProfile } from '@renderer/api/account';

  import { putAccount } from '@renderer/api/setting/index';
  import areaCode from "@renderer/components/account/AreaCode.vue";
  import { getProfilesInfo, setProfilesInfo, loadProfile } from "@renderer/utils/auth";
  import { checkPhoneAndMatch } from "@renderer/components/account/util";
  import { countList } from "@renderer/views/account/constant";
  import { jumpWeb } from "@renderer/views/contacts/utils";

  import _ from "lodash";

  import inviteDialog from "@renderer/components/contacts/dialog/invite.vue";
  import enterprisePng from "@renderer/assets/contacts/enterprise.svg";
  import associationPng from "@renderer/assets/contacts/businessAssociation.svg";
  import governmentPng from "@renderer/assets/contacts/government.svg";
  import individualPng from "@renderer/assets/contacts/individual.svg";
  import otherOrgPng from "@renderer/assets/contacts/otherOrg.svg";
  import { originType } from "@renderer/views/digital-platform/utils/constant";
  import { goToDigitalPlatform_member } from "@renderer/views/member/utils/auth";
  import orgAuth from "@renderer/components/orgAuth/orgAuth.vue";
  import { orgTypeMap } from '@renderer/components/orgAuth/utils';
  import { getTrialAnnualFee } from "@renderer/api/square/square";
  import to from "await-to-js";
  import { error } from "console";
  import { getSMCaptcha, getSMCaptchaResult } from "@renderer/utils/shumei";
  import { loadAcountCards } from "@/views/message/service/accountUtils";
  import upload from "./upload.vue";
  import LynkerSDK from '@renderer/_jssdk';

  const workOptions = ref([]);
  const { t } = useI18n();

  // 创建的组织类型
  const typeOptions = [
    // { label: t("contacts.government1"), value: 4, icon: governmentPng },
    { label: t("zx.contacts.association"), value: 2, icon: associationPng },
    { label: t("contacts.enterprise"), value: 1, icon: enterprisePng },
    { label: t("zx.contacts.individual"), value: 3, icon: individualPng },
    { label: t("contacts.other"), value: 0, icon: otherOrgPng },
  ];

  const closeStep = () => {
    emits('close');
  };
  const finish = () => {
    curStep.value[3] = false;
    emits('finish');
  };
  const invite = ref(null);

  const curStep = ref([false, false, false, false]);
  const showCreatOrgTag = ref(false);
  watch(() => curStep.value[3], async (val) => {
    if (val) {
      const teamId = queryOrganizaInfo.value?.teamId;
      const [err, res] = await to(getTrialAnnualFee(teamId));
      if (err) return;
      if (res.data?.total) {
        showCreatOrgTag.value = true;
      }
    }
  });

  // const hasChange = ref(false);
  // const handleAnimationEnd = (e) => {
  //   if (e.animationName.includes('flip')) {
  //     hasChange.value = !hasChange.value;
  //   }
  // };

  const disabledArr = ref([true, true, true, true]);
  /**
   * 创建组织：选择组织类型 form
   */
  const formStep1 = ref(null);
  /**
   * 创建组织：组织名、所在地、行业、是否允许邮箱 form
   */
  const formStep2 = ref(null);
  /**
   * 账号联系人信息 form
   */
  const formStep3 = ref(null);
  const uploadLogo = ref(null);

  const uProfile = getProfilesInfo();
  const formData = ref({
    type: null,
    logo: "",
    fullName: "",
    region: null,
    industry: "",
    teamMailApproved: false,
    teamMailDomain: "",
    staffName: uProfile.title || "",
    staffTelCode: uProfile.account_mobile_region || "86",
    staffPhone: uProfile.account_mobile || "",
    code: "" as number | string,
  });
  const formRules = ref({
    type: [{ required: true, message: t("contacts.pleaseSelect"), type: "error" }],
    fullName: [{ required: true, message: t("contacts.pleaseInput"), type: "error" }],
    region: [{ required: true, message: t("contacts.pleaseSelect"), type: "error" }],
    industry: [{ required: true, message: t("contacts.pleaseSelect"), type: "error" }],

    teamMailDomain: [{ required: true, message: t("contacts.pleaseInput"), type: "error" }],
    staffName: [{ required: true, message: t("contacts.pleaseInput"), type: "error" }],
    staffPhone: [{ required: true, message: t("contacts.pleaseInput"), type: "error" }],
    code: [{ required: true, message: t("contacts.pleaseInput"), type: "error" }],
  });
  const countdown = ref(0);
  const agreeProto = ref(false);
  const notifivisible = ref(false);
  const tipVisible = ref(false);
  const checkPhoneTip = ref("");
  const loadingNext = ref(false);
  let teamId = "";
  let checkRegion = 0;
  const emits = defineEmits(["sucess", 'close', 'finish']);
  const props = defineProps({
    // 目前只针对来自广场创建组织的处理
    // 新增来自商协会、政企组织的处理 originType
    from: {
      type: String,
      default: "",
    },
    zIndex: {
      type: Number,
      default: 2500
    }
  });
  // 改动
  const getWkOption = (val) => {
    marketClassifytree(val).then((res) => {
      workOptions.value = res.data.data;
      loadingNext.value = false;
    }).catch(() => {
      loadingNext.value = false;
    });
  };

  const showIframe = (uuid) => {
    LynkerSDK.ipcRenderer.invoke('create-iframe', uuid);
  };

  const changeRegion = () => {
    tipVisible.value = false;
    formData.value.staffTelCode = checkRegion.toString();
    formData.value.code ? nextStep(3) : getCode();
  };
  const uploadImg = (val) => {
    formData.value.logo = val[0].url;
  };
  // const confirmAgree = () => {
  //   agreeProto.value = true;
  //   notifivisible.value = false;
  //   nextStep(3);
  // };
  const getUserInfo = () => {
    const userInfo = getProfilesInfo();
    return userInfo;
  };

  const showPreStep = ref(true);
  const beginCreate = () => {

    const userInfo = getUserInfo();
    formData.value = {
      type: null,
      logo: "",
      fullName: "",
      region: null,
      industry: "",
      teamMailApproved: false,
      teamMailDomain: "",
      staffName: userInfo.title || "",
      staffTelCode: userInfo.account_mobile_region || "86",
      staffPhone: userInfo.account_mobile || "",
      code: "" as number | string,
    };

    // 商协会、政企处理
    if (props.from === originType.Member) {
      formData.value.type = 2;
    } else if (props.from === originType.Politics) { // 政企
      formData.value.type = 4;
    } else if (props.from === originType.CBD) { // 企业
      formData.value.type = 1;
    }

    countdown.value = 0;
    agreeProto.value = false;
    disabledArr.value = [true, true, true, true];
    nextTick(() => {
      const formArr = [formStep2, formStep3];
      uploadLogo.value?.clear();
      formArr.forEach((form) => {
        form.value?.clearValidate();
        form.value?.reset();
      });


      if ([originType.Member, originType.Politics, originType.Uni, originType.CBD].includes(props.from)) { // 商协会、政企处理
        showPreStep.value = false;
        curStep.value[1] = true;
        // curStep.value[3] = true;
      } else {
        curStep.value[0] = true;
        // curStep.value[3] = true;
      }
    });

  };
  const changeSelect = (val) => {
    formData.value.industry = "";
    getWkOption(val);
  };
  const chooseOrgType = (val) => {
    curStep.value[0] = false;
    curStep.value[1] = true;
    formData.value.type = val;
    getWkOption(formData.value.region);
  };
  const setOrgToActCode = (res) => {
    console.log(res, 'dayinrescode');
    LynkerSDK.ipcRenderer.invoke("set-activate-code-org", res.data.teamId);


  }
  const nextStep = (i) => {
    if (loadingNext.value) {
      return;
    }
    loadingNext.value = true;
    const formArr = [formStep1, formStep2, formStep3];
    formArr[i - 1].value.validate().then((result) => {
      if (!result) {
        loadingNext.value = false;
        return;
      }
      if (i === 2 || i === 3) {
        const params = _.cloneDeep(formData.value);
        params.teamMailApproved = +formData.value.teamMailApproved as any;
        if (i === 2 && params.staffTelCode && params.staffPhone) {
          confirmOrgCreate(params).then((res) => {
            console.log('confirmOrgCreate', res);

            // 重载身份卡信息
            loadAcountCards(false);
          });
        } else if (i === 3) {
          // 未绑定手机号，先绑定手机号
          putAccount({
            mobile: {
              region: formData.value.staffTelCode,
              mobile: formData.value.staffPhone,
              code: `${formData.value.code}`,
            }
          }).then((res) => {
            res.status === 200 && confirmOrgCreate(params) && loadProfile();
          }).catch((err) => {
            loadingNext.value = false;
            MessagePlugin.error({
              content: err.response.data?.message || t('zx.account.codeInvalid'),
              duration: 3000,
              offset: ["0", "20"],
            });
          });
        } else {
          goNextStep(i);
        }
      } else {
        goNextStep(i);
      }
    }).catch((err) => {
      loadingNext.value = false;
    });
  };
  const preStep = (i) => {
    curStep.value[i - 1] = true;
    curStep.value[i] = false;
  };

  const goNextStep = (i) => {
    curStep.value[i - 1] = false;
    curStep.value[i] = true;
    getWkOption(formData.value.region);
  };

  const confirmOrgCreate = (params) =>
    // 跳过手机验证
    createOrg(params)
      .then(({ data }) => {
        loadingNext.value = false;
        console.log(data, "create aaaaa");
        setOrgToActCode(data)
        if (data.code === 0) {
          emits("sucess", params, data.data);
          getOrgInfo();
          teamId = data.data.teamId;
          queryOrganizaInfo.value.teamId = teamId;
          curStep.value[1] = false;
          curStep.value[2] = false;
          if (!['square', originType.Member, originType.Politics, originType.CBD].includes(props.from)) {
            curStep.value[3] = true;
          }
        }
      })
      .catch((err) => {
        loadingNext.value = false;
        MessagePlugin.error({
          content: err.response.data?.message || t("zx.contacts.createError"),
          duration: 3000,
          offset: ["0", "20"],
        });
      });
  const setProfiles = () => {
    getProfile().then((res) => {
      if (res.status === 200) {
        setProfilesInfo(res.data);
      }
    }).catch(() => {
      loadingNext.value = false;
    });
  };

  const checkPhone = () => {
    checkRegion = checkPhoneAndMatch(+formData.value.staffTelCode, formData.value.staffPhone);
    if (!checkRegion) {
      MessagePlugin.error({
        content: t("zx.account.phoneIllegal"),
        duration: 3000,
        offset: ["0", "20"],
      });
      return false;
    }
    if (checkRegion !== +formData.value.staffTelCode) {
      (checkPhoneTip.value = `${t("zx.account.checkPhoneRegion1")}“+${checkRegion}”，${t(
        "zx.account.checkPhoneRegion2",
      )}`),
        (tipVisible.value = true);
      return false;
    }
    return true;
  };
  const getCode = (data?) => {
    if (!checkPhone()) return;
    const params = {
      typ: 'ACCOUNT_CHANGE',
      mobile: { mobile: formData.value.staffPhone, region: formData.value.staffTelCode },
    };
    if (data) {
      params.captcha = {
        code: data?.rid,
        mode: 'slide',
      };
    }
    getIdentifyCode(params)
      .then((res) => {
        if (res.status === 200) {
          countdown.value = 60;
          formStep3.value.clearValidate(['staffPhone']);
          const timer = setInterval(() => {
            countdown.value--;
            if (countdown.value <= 0) {
              clearInterval(timer);
            }
          }, 1000);
        }
      })
      .catch((err) => {
        console.log(err);
        const reason = err.response.data?.reason;
        if (reason === 'USER_EXISTED') {
          formStep3.value.setValidateMessage({
            staffPhone: [{
              type: 'error',
              message: t('zx.account.alreadyBindPhone')
            }]
          });

        } else {
          MessagePlugin.error({
            content: err.response.data?.message || t("zx.other.getCodeError"),
            duration: 3000,
            offset: ["0", "20"],
          });
        }
      });
  };
  const queryOrganizaInfo = ref({
    teamId: "",
    team: "",
    avatar: "",
    cardId: "",
    isAdmin: 0,
    canAdmin: 0,
  });
  const allowInvite = ref(false);
  const teamSettingDetail = ref();
  const inviteType = ref(["teamQrcode", "teamLink", "teamCode", "phone"]);
  const myCardInfo = ref({
    avatar: "",
    openId: "",
    name: "",
    position: [],
  });
  // 获取组织信息
  const getOrgInfo = async () => {
    const orgList = await getOrganizeList();
    queryOrganizaInfo.value = orgList.data.data.find((item) => item.teamId === teamId);
    console.log('=====>', queryOrganizaInfo.value, orgList);
    // 获取组织设置
    teamSetting({ type: 1 }, queryOrganizaInfo.value.teamId).then(({ data }) => {
      console.log(data, "teamSetting");
      if (data.code === 0) {
        // 第一优先级查看该组织是否开启邀请功能，第二优先级看是否是管理员，第三优先级看是否允许非管理员邀请成员 ["teamLink", "phone", "teamQrcode", "teamCode"]
        allowInvite.value = data.data.enable && (queryOrganizaInfo.value.isAdmin || data.data.notManagerInviteEnable);
        data.data.notManagerInviteEnable &&
          (inviteType.value = data.data.notManagerInvite
            ? ["teamQrcode", "teamLink", "teamCode", "phone"].filter((v) => data.data.notManagerInvite.includes(v))
            : []);
        queryOrganizaInfo.value.isAdmin && (inviteType.value = ["teamQrcode", "teamLink", "teamCode", "phone"]);
        teamSettingDetail.value = data.data;
      }
    });
    getInnerCardInfo(+queryOrganizaInfo.value.cardId.slice(1)).then(({ data }) => {
      console.log(data, "personal cardInfo");
      if (data.code === 0) {
        myCardInfo.value = data.data;
      }
    });
  };

  const orgAuthVisible = ref(false);
  const orgAuthDetail = ref({});
  const toVerifyWeb = () => {
    orgAuthDetail.value = {
      region: formData.value.region,
      teamId: queryOrganizaInfo.value.teamId,
      orgType: orgTypeMap.get(formData.value.type)?.orgType
    };
    orgAuthVisible.value = true;
    // jumpWeb("/#/setting/setting-info/setting-info-detail", { teamId });
  };
  const openDigitalPlatform = () => {
    goToDigitalPlatform_member(teamId);
    curStep.value[3] = false;
  };
  const openAdmin = async () => {
    jumpWeb("", { teamId });
  };
  const showInvite = () => {
    invite.value.show();
  };
  const finishInvite = () => {
    // 创建组织后设置props数据
    queryOrganizaInfo.value.team = formData.value.fullName;
    showInvite();
  };
  defineExpose({
    beginCreate,
  });
  watch(
    formData,
    (newValue) => {
      if (typeof newValue.type === "number") {
        disabledArr.value[0] = false;
      }
      if (
        newValue.fullName &&
        newValue.region &&
        (!newValue.teamMailApproved || (newValue.teamMailApproved && newValue.teamMailDomain))
      ) {
        disabledArr.value[1] = false;
      } else {
        disabledArr.value[1] = true;
      }
      if (newValue.staffName && newValue.staffPhone && newValue.code) {
        disabledArr.value[2] = false;
      } else {
        disabledArr.value[2] = true;
      }
    },
    {
      deep: true,
    },
  );

  const checkSM = () => {
    if (!SMCaptcha.value) {
      return;
    }
    getSMCaptchaResult(SMCaptcha.value, getCode);
  };

  const SMCaptcha = ref(null);
  onMounted(async () => {
    try {
      SMCaptcha.value = await getSMCaptcha({ width: 300 });
      console.error(SMCaptcha.value);
    } catch (error) {
      console.error(error);
    }
  });
</script>

<style lang="less" scoped>
  @import "@renderer/views/engineer/less/common.less";

  .custom-header {
    flex: 1;
    position: relative;

    .tricks {
      position: absolute;
      top: 0;
      left: 108px;
    }
  }

  .btn-close {
    font-size: 24px;
    font-weight: normal;
    cursor: pointer;
    color: #516082;
  }

  /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar {
    width: 6px;
    // height: 2px;
    background-color: var(--divider-kyy_color_divider_deep, #D5DBE4);
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    background-color: #FFFFFF;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: var(--divider-kyy_color_divider_deep, #D5DBE4);
  }

  :global(.selectOrgType .t-dialog__header .t-dialog__header-content) {
    color: var(--kyy_color_modal_title, #1a2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }

  :global(.selectOrgType .t-dialog__body) {
    padding: 12px 0;
  }

  :global(.selectOrgType .t-form .t-form__label) {
    color: var(--text-kyy_color_text_3, #828da5);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  :deep(.selectOrgType .t-dialog) {
    transform-origin: right top !important;
  }

  .btn {
    min-width: 80px;
  }

  :global(.height .t-dialog) {
    height: 562px;
  }

  :global(.height .t-form) {
    height: 388px;
    overflow-y: scroll;
  }

  .mb24 {
    margin-bottom: 24px;
  }

  .mt12 {
    margin-top: 12px;
  }

  .mt16 {
    margin-top: 16px;
  }

  .mt8 {
    margin-top: 8px;
  }

  .mb4 {
    margin-bottom: 4px;
  }

  .mb24 {
    margin-bottom: 24px;
  }

  .upload-logo {
    display: flex;
    align-items: center;
  }

  .logo-tips {
    margin-left: 12px;
    color: var(--text-kyy-color-text-5, #acb3c0);

    /* kyy_fontSize_1/regular */
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
  }

  .t-form {
    :deep(.t-form__item) {
      margin-bottom: 24px;

      .t-form__label--top {
        min-height: auto;
        margin-bottom: 8px;
      }
    }

    .nonebt24 {
      margin-bottom: 0;
    }
  }

  .t-upload {
    .upload-wrap-icon {
      // width: 60px;
      // height: 60px;
      border-radius: 4px;
      border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
      background: var(--kyy_color_upload_bg, #fff);

      .t-icon {
        margin-bottom: 0;
      }

      .t-size-s {
        display: none;
      }
    }
  }

  .suffix--line {
    width: 1px;
    height: 24px;
    background-color: #f6f6f6;
    margin-right: 16px;
  }

  .tel {
    :deep(.t-input--auto-width) {
      min-width: 82px;
    }

    :deep(.t-input-adornment__prepend) {
      background: #fff;
    }
  }

  .t-input-adornment {
    width: 100%;
  }

  .verify {
    font-size: 14px;

    color: #2069e3;
    line-height: 22px;
  }

  .pointer {
    cursor: pointer;
  }

  .suc-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    // margin-top:-12px;
    margin-bottom: 16px;

    .suc-icon {
      font-size: 0;
      width: 48px;
      height: 48px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .go-verify,
    .go-back {
      width: 100%;
      padding: 12px 14px;
      border-radius: 8px;
      border: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:hover {
        border: 1px solid var(--border-kyy_color_border_active, #4d5eff);
      }
    }

    .go-back {
      margin-top: 24px;
      margin-bottom: 12px;
    }

    .arrow {
      width: 20px;
      height: 20px;
    }

    .tip {
      margin-bottom: 4px;
      color: var(--text-kyy-color-text-1, #1a2139);
      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }

    .sub-tip {
      color: var(--text-kyy_color_text_2, #516082);
      /* kyy_fontSize_2/regular */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .tips {
      color: var(--text-kyy_color_text_3, #828da5);
    }

    .left-img {
      width: 44px;
      height: 44px;
      margin-right: 4px;
    }

    .right-info {
      .tip {
        position: relative;
        display: flex;

        .tips2 {
          display: flex;
          align-items: center;
          margin-left: 5px;

          img {
            height: 20px;
          }
        }
      }
    }
  }

  .footer {
    display: flex;
    justify-content: center;
  }

  a {
    color: #2069e3;
  }

  a:hover {
    color: #2069e3;
  }

  .body-tip {
    color: var(--font-kyy-font-gy-2, #516082);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .tac {
    text-align: center;
  }

  .flex-ac {
    display: flex;
    align-items: center;
  }

  .cursor-p {
    cursor: pointer;
  }

  .org-types {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: -12px;

    .type-item {
      text-align: center;
      border: 1px var(--bg-kyy_color_bg_deep, #f5f8fe) solid;
      width: 104px;
      padding: 8px;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);

      &:hover {
        border: 1px solid var(--border-kyy_color_border_active, #4d5eff);
      }

      .type-img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }

      .label {
        color: var(--text-kyy_color_text_1, #1a2139);
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }
    }
  }
</style>
