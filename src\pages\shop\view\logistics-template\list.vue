<template>
  <!-- 配送模板管理页面 -->
  <div class="delivery-template-wrapper">
    <RTable
      ref="RTableRef"
      :table="table"
      :filter="filter"
      :is-change-clean="true"
      :is-sticky-header="true"
      @change="change"
    >
      <template #toolbarContent>
        <t-button class="font-600!" theme="primary" @click="handleCreateTemplate">
          <t-icon name="add" />
          新建模板
        </t-button>
      </template>
      <template v-if="!isLoading" #empty>
        <REmpty tip="暂无模板" :name="isNoData ? 'no-result' : 'no-data'" />
      </template>
    </RTable>
  </div>
  <LogisticsTemplateDialog ref="logisticsTemplateDialogRef" @confirm="refetch" />
  <RelatedProducts ref="relatedProductsRef" />
</template>

<script setup lang="tsx">
import { ref, onMounted, computed, watch } from 'vue';
import { RTable } from '@rk/unitPark';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { ListLogisticsDeliveryTemplateRequest, LogisticsDeliveryTemplate } from '@/types/api/store/delivery.model';
import { useQuery } from '@tanstack/vue-query';
import { useShopStore } from '../../store';
import {
  getLogisticsDeliveryTemplateList,
  delLogisticsDeliveryTemplate,
  setLogisticsDeliveryTemplate,
} from '@pages/shop/api/delivery';
import LogisticsTemplateDialog from './components/LogisticsTemplateDialog.vue';
import RelatedProducts from './components/RelatedProducts.vue';
import { REmpty } from '@rk/unitPark';
import { PRICING_METHOD_MAP } from './constant';
import { formatDateTime } from '@utils/date';
import to from 'await-to-js';
import { refDebounced } from '@vueuse/core';
import { highlightKeyword } from '@utils/index';

const shopStore = useShopStore();
const me = {
  cardId: shopStore.cardId,
  openId: shopStore.openId,
  teamId: shopStore.teamId,
};

const RTableRef = ref(null);
const isNoData = ref(false);
const logisticsTemplateDialogRef = ref<InstanceType<typeof LogisticsTemplateDialog>>();
const relatedProductsRef = ref<InstanceType<typeof RelatedProducts>>();

// 操作函数
const handleCreateTemplate = () => {
  logisticsTemplateDialogRef.value?.open();
};

const handleLinkProducts = (template: LogisticsDeliveryTemplate) => {
  if (template?.relationCount === 0 || (template?.relationCount && template?.relationCount < 0)) {
    return;
  }
  console.log('关联商品', template);
  relatedProductsRef.value?.open(template);
};

const handleEdit = (template: LogisticsDeliveryTemplate) => {
  logisticsTemplateDialogRef.value?.open(template);
};

const handleDelete = async (template: LogisticsDeliveryTemplate) => {
  if ((template?.relationCount || 0) > 0) {
    MessagePlugin.warning('模板已关联商品，暂不支持删除');
    return;
  }
  const dialog = DialogPlugin.confirm({
    header: '提示',
    body: '确定删除该模板？',
    closeBtn: false,
    onConfirm: async () => {
      dialog.destroy();
      const params = {
        templateId: {
          id: Number(template.id),
        },
        me,
      };
      const [err] = await to(delLogisticsDeliveryTemplate(params));
      if (err) {
        MessagePlugin.error(err.message || '删除失败，请重试');
        return;
      }
      MessagePlugin.success(`删除成功`);
      refetch();
    },
  });
};

const handleSetDefault = async (template: LogisticsDeliveryTemplate) => {
  const params = {
    template: {
      ...template,
      isDefault: true,
    },
    me,
  };
  const [err] = await to(setLogisticsDeliveryTemplate(params));
  if (err) {
    MessagePlugin.error(err.message || '设置默认模板失败，请重试');
    return;
  }
  refetch();
};

const name = ref('');
const searchValueDebounced = refDebounced(name, 500);
const pageInfo = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});
// 计算查询参数，集中管理所有参数
const queryParams = computed(
  (): ListLogisticsDeliveryTemplateRequest => ({
    'storeId.id': shopStore.store?.id,
    'pagination.size': pageInfo.value.pageSize,
    'pagination.number': pageInfo.value.current,
    'me.cardId': shopStore.cardId,
    'me.openId': shopStore.openId,
    'me.teamId': shopStore.teamId,
    name: searchValueDebounced.value,
  }),
);

// const queryParams = ref({
//   'storeId.id': shopStore.store?.id,
//   'pagination.size': pageInfo.value.pageSize,
//   'pagination.number': pageInfo.value.current,
//   'me.cardId': shopStore.cardId,
//   'me.openId': shopStore.openId,
//   'me.teamId': shopStore.teamId,
//   name: name.value,
// });

// 使用useQuery获取数据
const { data, isLoading, refetch } = useQuery({
  queryKey: ['logistics-template-list', queryParams],
  queryFn: () => getLogisticsDeliveryTemplateList(queryParams.value),
  retry: false,
  refetchOnWindowFocus: false,
  refetchOnReconnect: false,
  refetchOnMount: true,
});

watch(data, (newData: any) => {
  const responseData = newData?.data;
  if (responseData?.templates?.length) {
    table.value.list = responseData.templates.map((v: any) => ({
      ...v,
    }));
    table.value.pagination.total = responseData.pagination.total;
  } else {
    table.value.list = [];
    table.value.pagination.total = 0;
    isNoData.value = !!searchValueDebounced.value;
  }
});

// RTable 配置
const filter = {
  attrs: {
    size: 'small',
    labelWidth: '80px',
    placeholder: '搜索模板名称',
    clearable: true,
  },
};

const table = ref({
  attrs: {
    'row-key': 'id',
    loading: isLoading,
    tableLayout: 'auto',
    hover: true,
  },
  pagination: pageInfo.value,
  list: [],
  columns: [
    {
      title: '模板名称',
      colKey: 'templateName',
      width: 167,
      cell: (h: any, { row }: { row: any }) => {
        return (
          <t-tooltip content={row.templateName}>
            <div class="ellipsis-2" v-html={highlightKeyword(row.templateName, searchValueDebounced.value)}></div>
          </t-tooltip>
        );
      },
    },
    {
      title: '计费方式',
      colKey: 'pricingMethod',
      width: 103,
      cell: (h: any, { row }: { row: any }) => {
        if (row?.freightType === 'FREIGHT_TYPE_FIXED') {
          return <div>固定运费</div>;
        }
        return <div>{PRICING_METHOD_MAP[row.pricingMethod as keyof typeof PRICING_METHOD_MAP]}</div>;
      },
    },
    {
      title: '发货地址',
      colKey: 'address',
      width: 115,
      cell: (h: any, { row }: { row: any }) => {
        return (
          <t-tooltip content={`${row.shippingRegion.province}-${row.shippingRegion.city}`}>
            <div class="ellipsis-1">
              {row.shippingRegion.province}-{row.shippingRegion.city}
            </div>
          </t-tooltip>
        );
      },
    },
    {
      title: '运费(元)',
      colKey: 'fee',
      width: 87,
      cell: (h: any, { row }: { row: any }) => {
        return (
          <div>
            {row.freightType === 'FREIGHT_TYPE_FIXED' ? (
              <div>{(row.unifiedShippingFee / 100).toFixed(2)}</div>
            ) : (
              <div>{(row.priceRule.firstFee / 100).toFixed(2)}</div>
            )}
          </div>
        );
      },
    },
    {
      title: '创建人',
      colKey: 'creatorName',
      width: 95,
      ellipsis: true,
    },
    {
      title: '编辑时间',
      colKey: 'updatedAt',
      width: 105,
      cell: (h: any, { row }: { row: any }) => {
        return <div>{formatDateTime(row.updatedAt * 1000)}</div>;
      },
    },
    {
      title: '操作',
      colKey: 'operate',
      cell: (h: any, { row }: { row: any }) => {
        return (
          <div class="opt-box">
            <div class={row?.relationCount <= 0 ? 'disabled' : 'opt-hover'} onClick={() => handleLinkProducts(row)}>
              关联商品
            </div>
            <div class="opt-hover" onClick={() => handleEdit(row)}>
              编辑
            </div>
            <div class="opt-hover" onClick={() => handleDelete(row)}>
              删除
            </div>
            {row.isDefault ? (
              <t-tag theme="warning" variant="light" size="small" class="default-tag">
                默认模板
              </t-tag>
            ) : (
              <div class="opt-hover" onClick={() => handleSetDefault(row)}>
                默认模板
              </div>
            )}
          </div>
        );
      },
    },
  ],
});

const change = (info: any, key: string) => {
  console.log('change', info, key);
  name.value = info.filter.searchVal;

  pageInfo.value.current = info.pageInfo.current;
  pageInfo.value.pageSize = info.pageInfo.pageSize;
};

onMounted(() => {
  console.log('配送模板页面已挂载');
});
</script>

<style scoped lang="less">
:deep(.opt-box) {
  display: flex;
  align-items: center;
  height: 22px;
  gap: 8px;
  .disabled {
    color: #c9cfff;
    cursor: not-allowed;
    padding: 4px;
  }
  .opt-hover {
    color: #4d5eff;
    width: max-content;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    &:hover {
      background-color: #f3f6fa;
    }
  }
}
.delivery-template-wrapper {
  position: relative;
  height: 100%;
  padding: 0 16px;
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .RTable {
    flex: 1;
  }
}
:deep(.t-button .t-button__text) {
  align-items: center;
}
</style>
