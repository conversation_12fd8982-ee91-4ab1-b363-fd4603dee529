import { getOpenid } from "@renderer/utils/auth";
import { getSharedSquaresAxios, getIndividualSquareAxios } from "@renderer/api/uni/api/businessApi";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import to from "await-to-js";
import { toSquareHome } from "@/views/square/utils/ipcHelper";

export function useApi() {
  /**
 * 1、当在数字城市点击跳转广场号时，判断个人是否启用广场；
  （1）若已启用，则直接跳转广场号主页
  （2）若未启用，则弹窗提示启用广场：你当前未启用广场应用，确定启用？【取消】【确定】
  （3）点击取消，关闭弹窗，不跳广场号主页
  （4）点击确定，启用个人广场号并继续跳转广场号主页
 * @param val
 */
  const onActionSquare = async (obj:any = {team_id: '', open_id: '', square_id: ''}, goSquare?) => {

    const params = {
      team_id: obj.team_id , // 组织 ID（获取组织广场号时使用）
      open_id: obj.open_id || getOpenid(), // 获取个人广场号时使用
    };
    const [err, res] = await to(getSharedSquaresAxios(params)); 
    if (err) {
      console.log(err?.message);
      return;
    }
    console.log(res,'getSharedSquaresAxios');

    // return;
    const { data } = res;

    if (!data?.selfOpened) {
      const confirmDia = DialogPlugin({
        header: "提示",
        theme: "info",
        body: "您当前未启用广场应用，确定启用？",
        closeBtn: null,
        confirmBtn: "确定",
        className: "delmode",
        onConfirm: async () => {
          // 删除字段操作
          confirmDia.hide();
          onOpenPersonSquare(goSquare);
        },
        onClose: () => {
          confirmDia.hide();
        },
      });
    } else {
      const squareId = goSquare || obj.square_id || data.square.squareId;
      if(squareId) {
        onGoSquarePage(squareId)
      } else {
        MessagePlugin.error('缺少广场ID')
      }
    }
  };

  const onOpenPersonSquare = async (goSquare?) => {
    const params = {};
    const [err, res] = await to(getIndividualSquareAxios(params));
    if (err) {
      MessagePlugin.error(err?.message);
      return;
    }
    const { data } = res;
    // 启用成功后，继续跳广场
    const squareId = goSquare || data?.info?.square?.squareId
    if(squareId) {
      onGoSquarePage(squareId)
    } else {
      MessagePlugin.error('缺少广场ID')
    }
  };

  const onGoSquarePage = (squareId) => {
    console.log('onGoSquarePage', squareId)
    // toSquareHome(squareId, { needShowDialog: true })
    toSquareHome(squareId, { skipSwitchToPersonal: 'true' })

  }

  return {
    onActionSquare
  }
}


