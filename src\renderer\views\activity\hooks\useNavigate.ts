import { useRouter } from 'vue-router';
import LynkerSDK from '@renderer/_jssdk';
import { ClientSide } from '@/types/enumer';

const { ipcRenderer } = LynkerSDK;

interface Activity {
  /** 活动ID */
  id: string;
  /** 活动主题 */
  subject: string;
  /** 其他属性 */
  [key: any]: any;
}

interface Options {
  /** 活动 */
  activity: Activity;
  /** tab类型 */
  type: Type;
  /** 参与组织Id */
  teamId: string;
  /** 参与身份卡Id */
  cardId: string
}

// tab类型
enum Type {
  /** 数字平台 */
  PLATFORM = 'PLATFORM',
  /** 数智工厂 */
  WORKBENCH = 'WORKBENCH',
}

export const useNavigate = () => {
  const router = useRouter();

  // 打开参与者活动详情tab
  const openParticipantDetailTab = (options: Options) => {
    const { activity, teamId, cardId, type } = options;
    if (type === Type.WORKBENCH) {
      router.push({
        path: `/workBenchIndex/activityParticipantDetail/${activity.id}`,
        query: {
          subject: activity.subject,
          teamId,
          cardId,
        },
      });
      ipcRenderer.invoke('set-work-bench-tab-item', {
        path: `/workBenchIndex/activityParticipantDetail/${activity.id}`,
        path_uuid: 'bench_activityParticipantDetail',
        title: activity.subject,
        activeIcon: 'workshop',
        icon: 'workshop',
        name: `/workBenchIndex/activityParticipantDetail/${activity.id}`,
        type: ClientSide.ACTIVITY,
      });
    }
  };

  return {
    openParticipantDetailTab,
  };
};
