import '@lynker-desktop/electron-sdk/preload';
import core from '@lynker-desktop/electron-sdk/renderer';

// import { monitorRenderer } from '@lynker-desktop/devtron/monitorRenderer'
// console.log('monitorRenderer: ', monitorRenderer);
// try {
//   monitorRenderer()
// } catch (error) {
//   console.log('monitorRenderer error: ', error);
// }

try {
  // @ts-ignore
  window.LynkerSDKPreload = {
    isMain: true,
    webFrame: window?.require('electron').webFrame,
    core: core,
    si: core.remote.require('systeminformation') as typeof import('systeminformation'),

    fs: window?.require('fs') as typeof import('node:fs'),

    os: window?.require('os') as typeof import('node:os'),

    path: window?.require('path') as typeof import('node:path'),

    child_process: core.remote.require('child_process') as typeof import('node:child_process'),

    remote: core.remote,

    ipcRenderer: core.ipc.ipc<PERSON>enderer,

    ipc: core.ipc,

    windowManager: core.windowManager,

    shell: core.remote.shell,

    dialog: core.remote.dialog,

    clipboard: core.remote.clipboard,

    nativeImage: core.remote.nativeImage,

    require: core.remote.require,

    log: core.log,

    app: core.remote.app,

    tiff: core.remote.require('tiff.js') as typeof import('tiff.js'),

    eStore: core.remote.require('electron-store') as typeof import('electron-store'),

    eLog: core.remote.require('electron-log') as typeof import('electron-log'),

    downloader: core.remote.require('nodejs-file-downloader') as typeof import('nodejs-file-downloader'),
  }
} catch (error) {
  console.error(error);
}
