<template>
  <div>
    <t-dialog  attach="body"
     class="apply_for_friends"
     :closeOnOverlayClick="false"
     :visible="visible"
      :showOverlay="showOverlay"
      :closeBtn="false"
     :class="{'dialog_border': !showOverlay}" width="488" :footer="false" >
     <template #header>
      <div style="display: flex;justify-content: space-between;width: 100%;">
        <div style="color: #1A2139;font-size: 16px; font-weight: 600;">
          {{t('identity.contactVerification_new')}}
        </div>
        <iconpark-icon name="iconerror" class="btn-close" @click="close()" />
      </div>
    </template>
      <template #body>
        <div class="apply_for_friends_box">
          <div class="f-align ">
            <kyy-avatar
            @click="openCard()"
              avatarSize="44px"
              :imageUrl="cardInfo.avatar"
              :userName="cardInfo.name"
              style="font-size: 14px;"
              roundRadius
            ></kyy-avatar>
            <div class="apply_for_friends_name_box" style="margin-left: 8px;">
              <div class="name">{{ props.cardInfo.name }}</div>
              <div v-if="props.cardInfo.teamName" class="team">{{ props.cardInfo.teamName }}</div>
            </div>
          </div>
          <div class="remark ">
            <div class="form_label">{{ t('identity.notesInfo') }}</div>
            <t-textarea class="text-area" v-model="remark" :autosize="{ minRows: 2, maxRows: 2 }" :maxlength="200" clearable :placeholder="t('contacts.pleaseInput')"></t-textarea>
          </div>
          <!-- <div class="remark pd-lr-8">
            <div class="form_label">{{ t('identity.introduce') }}</div>
            <t-textarea class="text-area" v-model="introduce" :maxlength="50" clearable :placeholder="t('contacts.pleaseInput')"></t-textarea>
          </div> -->
          <div class="remark businessCard ">
            <div class="form_label">{{ t('identity.businessCard') }}</div>
              <UploadImage v-model="businessCardImages" :max-count="3" root-dir="common" @preview="handlePreview"/>
          </div>
        </div>
        <div style="text-align: right;" class="btn-box">
          <t-button style="width:80px" :loading="loading" @click="confirm">{{ t('identity.send') }}</t-button>
        </div>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { applyContact, getApplyMsg } from '@renderer/api/identity/api/card';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import {MessagePlugin, DialogPlugin} from "tdesign-vue-next";
import { checkContactAuth } from '@renderer/utils/auth';
import { cardPairType } from '@renderer/views/identitycard/data';
import UploadImage from '@/components/common/UploadImage.vue';

import LynkerSDK from "@renderer/_jssdk";

const { t } = useI18n();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  cardInfo: { type: Object, default: () => {}},
  myId: { type: String, default: '' },
  showOverlay: {
    type: Boolean,
    default: false,
  }
})
const remark = ref('');
const introduce = ref('');


watch(() => props.visible, (val) => {
  if (val && props.myId) {

    getApplyMsg(encodeURIComponent(props.myId)).then(res => {
      if (res?.status === 200) {
        remark.value = res?.data?.message;
        businessCardImages.value = res?.data?.pics || [];
      }
    })
  }
}, { immediate: true })
const businessCardImages = ref([])

const openCard=()=>{
  console.log(props.cardInfo,'cardInfocardInfocardInfo');

  const { ipcRenderer } = LynkerSDK;
  // // const cardId = senderInfo?.cardId || msg.contentExtra?.senderId;

    ipcRenderer.invoke("identity-card", {
      cardId:props.cardInfo.openid,
      myId:props.myId,
    });
}
const removeImage = (item, index) => {
  // const confirmDia = DialogPlugin.confirm({
  //   header: t('activity.activity.delete'),
  //   theme: 'danger',
  //   body: t('activity.activity.confirmDelete'),
  //   closeBtn: null,
  //   confirmBtn: t('activity.activity.confirm'),
  //   onConfirm: () => {
  //     confirmDia.hide();
      businessCardImages.value.splice(index, 1);
  //   },
  //   onClose: () => {
  //     confirmDia.hide();
  //   },
  // });
};
const handlePreview = (url: string) => {
    LynkerSDK.previewImage({
      images: [url],
      index: 0,
      url: url,
    });
};

const emits = defineEmits(['update:visible', 'onconfirm', 'onApplyContact']);
const close = () => {
  // uploadRef.value.value = [];
  businessCardImages.value = [];
  remark.value = '';
  emits('update:visible', false);
};
const loading = ref(false);
const confirm = async () => {
  if (loading.value) return;
  loading.value = true;
  const info = props.cardInfo;
  const teamId = info.teamId || info.internal_teamId;
  const pairType = cardPairType(props.myId, info.cardId, teamId);
  const params = {
    cardIdApply: props.myId,
    cardIdSelf: info.cardId,
    source: 'FRIEND_APPLY_SOURCE_PHONE',   //目前都是通过手机
    remark: remark.value,
    introduce:'',
    pairType,
    businessCard:[]
  }
  if (introduce.value) {
    params.introduce = introduce.value
  }
  if (businessCardImages.value && businessCardImages.value?.length) {
    params.businessCard = businessCardImages.value.filter(v => !!v)
  }
  if (!await getContactAuth({ from: props.myId, to: props.cardInfo?.cardId || props.cardInfo?.openid })) {
    loading.value = false;
    return;
  }
  applyContact(params).then((res) => {
    res.data.data.isAutoPass === 1 && emits('onconfirm',res.data.data.id);
    MessagePlugin.success(t('identity.contactRequestSent_new'));
    emits('onApplyContact');
    close();
    loading.value = false;
  });
};

const getContactAuth = async (data) => {
  let res = true;
  res = await checkContactAuth(data);
  if (!res) MessagePlugin.warning({
      content: t("identity.contactAuthTips"),
      style: {width: '280px'}
    });
  return res
}
</script>

<style lang="less" scoped>
:global(.apply_for_friends .t-dialog){

    background: url('../../../assets/img/addprebg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
:global(.apply_for_friends .t-dialog__wrap){
    border-radius: 16px;
}
:global(.apply_for_friends .t-dialog__body){
  /* margin: 0 24px 8px; */
  padding-bottom: 0 !important;
}
:global(.apply_for_friends .t-dialog--default){
  padding: 24px 0 0 !important;
  // border-radius: 8px !important;
}
:global(.apply_for_friends .t-dialog__header){
  margin: 0 24px 8px;
}
:global(.apply_for_friends .t-dialog__close){
  font-size: 24px;
    color: #1A2139;
    width: 24px;
    height: 24px;
}
.btn-box{
  text-align: right;
    background: #fff;
    height: 80px;
    border-radius: 0 0 16px 16px;
    display: flex;
    align-items: center;
    justify-content: end;
    padding-right: 24px;
}
.apply_for_friends_box{
  border-radius: 8px;
    background:  #FFF;
    padding: 12px;margin: 0 12px 12px;
}
.apply_for_friends_name_box{
  display: flex;
    gap: 4px;
    flex: 1;
    width: 0;
    flex-direction: column;
}
.form_label{
  margin-bottom: 8px;
  color: var(--text-kyy_color_text_3, #828DA5);

  /* kyy_fontSize_2/regular */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.btn-close {
  font-size: 24px;
  font-weight: normal;
  cursor: pointer;
  color: #1A2139;
}
:deep(.businessCardUpload) {
      display: flex;
      align-items: center;
      .t-upload__card {
        flex-wrap: nowrap;
        gap: 0;
        .t-upload__card-mask-item-divider {
          margin: 0 5px;
        }
        .t-link.t-upload__card-name{
          display: none;
        }
        .t-upload__card-item{
          height: 78px;
        }
      }
      .t-upload__card-content {
        display: none;
        width: 78px;
        height: 78px;
        border: 1px solid var(--kyy_color_upload_border_default, #D5DBE4);
        border-radius: 8px;
      }
      .t-upload__card-container {
        width: 78px;
        height: 78px;
        border: none;
        border-radius: 8px;

        background-image: url(@/assets/identity/upload.svg);
        background-size: cover;
        svg{
          display: none;
        }
        .t-size-s {
          display: none;
          color: var(--kyy_color_upload_text_default, #516082);
          text-align: center;

          /* kyy_fontSize_1/regular */
          font-family: PingFang SC;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px; /* 166.667% */
        }
        .t-icon {
          margin-bottom: 0;
        }
      }
      .t-upload__tips {
        margin-left: 8px;
        margin-top: 0;
        text-align: left;

        color: var(--text-kyy_color_text_3, #828DA5);

        /* kyy_fontSize_1/regular */
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
    }

:deep(.dialog_border .t-dialog) {
  border: 1px solid rgba(0, 0, 0, 0.12);
}
:deep(.t-dialog ) {
  .t-dialog__header-content{
    color: var(--kyy_color_modal_title, #1A2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
  .t-dialog__body{
    padding: 24px 0 0 0;
  }
}

.f-align {
  display: flex;
  align-items: center;
}

.f-c {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  width: 0;
}
.remark {
  margin: 12px 0;
  :deep(.t-textarea .no-resize__inner) {
    padding: 12px;
    color: var(--textarea-kyy_color_textarea_text_completed, #1A2139);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
}
.businessCard{
  margin-bottom: 24px;
  display: flex;
  flex-wrap: wrap;
  .form_label{
    width: 100%;
  }
}
.img-wrap{
  width: 78px;
  height: 78px;
  margin-left: 8px;
  border-radius: 8px;
  border: 1px solid var(--kyy_color_upload_border_default, #D5DBE4);
  .image-viewer{
    width: 100%;
    height: 100%;
    position: relative;
    .tdesign-demo-image-viewer__ui-image--img{
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }
  }
  .image--hover{
    cursor: pointer;
    border-radius: 8px;
    background: var(--bg-kyy-color-bg-mask, rgba(0, 0, 0, 0.20));
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    img {
      position: absolute;
      top: 0;
      right: 0;
      width: 20px;
      height: 20px;
    }
  }
}

.name {
  color:  #1A2139;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 56%;
}
.team {
  color:  #FC7C14;
font-size: 14px;
font-style: normal;
font-weight: 400;
line-height: 22px; /* 157.143% */
}
:deep(.t-textarea__inner) {
  resize: none;
}

:deep(.t-upload__card-item > .t-upload__card-container) {
  display: none;
}

:deep(.t-upload__card-item > .t-upload__image-add) {
  display: flex;
}
.text-area{
  :deep(.t-textarea__inner){
    padding-bottom: 20px;
    border: 1px solid var(--textarea-kyy_color_textarea_border_default, #D5DBE4);
  }
  :deep(.t-textarea__info_wrapper){
    position: absolute;
    bottom:4px;
    right:4px;
    color: var(--text-kyy_color_text_5, #ACB3C0);
    text-align: right;
    font-family: "PingFang SC";
    font-size: 12px;
  }
}
</style>
