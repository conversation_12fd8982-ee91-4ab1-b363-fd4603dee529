<template>
  <!-- 内容区域 -->
  <div class="news-warp">
    <RTable
      ref="RTableRef"
      :filter="filter"
      :table="table"
      :is-sticky-header="true"
      :is-change-clean="false"
      @change="change"
    >
      <template #toolbarContent>
        <t-button @click="handleRelease">
          <div class="flex-y-center">
            <t-icon name="add" />
            <span class="ml-4 font-600">新建资讯</span>
          </div>
        </t-button>
      </template>
      <template #customContent>
        <div class="tabs-warp">
          <div
            v-for="(tab, index) in tabList"
            :key="index"
            :class="['tabs-warp-list', tab.value === status ? 'active-tab' : '']"
            @click="changeTab(tab.value)"
          >
            {{ tab.label }}
            <span v-if="tab.count">({{ tab.count }})</span>
          </div>
        </div>
      </template>
      <template #empty>
        <REmpty v-if="isNoData" name="no-result" tip="搜索无结果" />
        <template v-else>
          <REmpty name="no-data" tip="暂无资讯内容" />
        </template>
      </template>
    </RTable>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, watch } from 'vue';
import { RTable, REmpty } from '@rk/unitPark';
import { refDebounced } from '@vueuse/core';
import { TabsList, resTotalMap, resApprovedMap, resReviewingMap, InformationStatusEnum, sortReqMap } from './constant';
import {
  getInformationList,
  deleteInformation,
  revokeInformation,
  getInformationDetail,
} from '@pages/culture/api/news';
import { useQuery } from '@tanstack/vue-query';
import { useCultureStore } from '@pages/culture/store';
import { Information, InformationStatus } from '@/types/api/culture/news';
import { formatDateTime } from '@utils/date';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import { highlightKeyword } from '@utils/index';
import { useNavigate } from '@pages/culture/composables/useNavigate';

const { navigateToCreateNews, navigateToDetailNews, navigateToEditNews } = useNavigate();

const cultureStore = useCultureStore();

const RTableRef = ref(null);
const isNoData = ref(false);

const keyword = ref('');
const status = ref(TabsList[0].value);
const tabList = ref(TabsList);
const searchValueDebounced = refDebounced(keyword, 200);
const pageInfo = ref({
  total: 0,
  pageSize: 10,
  current: 1,
});
const tableList = ref([]);

const me = computed(() => ({
  'me.openId': cultureStore.query?.openId,
  'me.cardId': cultureStore.query?.cardId,
  'me.teamId': cultureStore.query?.teamId,
}));

const change = (info: any, key: any) => {
  if (key === 'filter') {
    keyword.value = info.filter.searchVal || '';
    // 延迟触发页码变更，保证searchVal更新完成后再触发页码更新
    setTimeout(() => {
      pageInfo.value.current = 1;
    }, 300);
    return;
  }
  if (key === 'table') {
    pageInfo.value.current = info.pageInfo.current;
    pageInfo.value.pageSize = info.pageInfo.pageSize;
    return;
  }
};

// 计算查询参数，集中管理所有参数
const queryParams = computed(() => ({
  keyword: searchValueDebounced.value,
  teamId: cultureStore.query?.teamId,
  ...me.value,
  'page.size': pageInfo.value.pageSize,
  'page.number': pageInfo.value.current,
  status: status.value,
  descFlag: true,
  sort: sortReqMap[status.value as keyof typeof sortReqMap],
}));

// 使用useQuery获取数据
const { data, refetch } = useQuery({
  queryKey: ['news-list', queryParams],
  queryFn: () => getInformationList(queryParams.value, cultureStore.query?.teamId),
  retry: false,
  refetchOnWindowFocus: true,
  refetchOnReconnect: false,
  refetchOnMount: true, // 组件挂载时自动获取数据
});

// 响应式更新表格数据
watch(
  data,
  (newData: any) => {
    const responseData = newData?.data;
    if (responseData?.information?.length) {
      tableList.value = responseData.information;
      pageInfo.value.total = responseData.count[resTotalMap[status.value as keyof typeof resTotalMap]];
    } else {
      tableList.value = [];
      pageInfo.value.total = 0;
    }
    isNoData.value = !!searchValueDebounced.value;
    // 更新tabList
    tabList.value = tabList.value.map((item) => ({
      ...item,
      count: responseData?.count[resTotalMap[item.value as keyof typeof resTotalMap]],
    }));
  },
  { deep: true },
);

const changeTab = (value: InformationStatus) => {
  status.value = value;

  pageInfo.value.current = 1;
};

const handleDelete = async (template: Information) => {
  const dialog = DialogPlugin.confirm({
    header: '确认删除吗？',
    theme: 'info',
    body: '删除后，该信息将不可恢复',
    closeBtn: false,
    onConfirm: async () => {
      dialog.destroy();
      const params = {
        id: Number(template.id),
        me: cultureStore.query,
      };
      const [err] = await to(deleteInformation(params));
      if (err) {
        return;
      }
      MessagePlugin.success(`删除成功`);
      // 如果删除的是下一页的最后一条数据，则删除后，需要将当前页码减1
      if (pageInfo.value.current > 1 && table.value.list.length === 1) {
        pageInfo.value.current -= 1;
      }
      refetch();
    },
  });
};

const handleRevoke = async (template: Information) => {
  console.log('撤回', template);
  const body =
    status.value === InformationStatusEnum.StatusApproved
      ? '撤回后，当前内容将转移至草稿箱。'
      : '撤回后，审核人将无法再次进行审核，并将内容转移到草稿箱中。';
  const successMsg = status.value === InformationStatusEnum.StatusApproved ? '撤回成功' : '内容已撤回';
  const dialog = DialogPlugin.confirm({
    header: '确认撤回吗？',
    theme: 'info',
    body,
    closeBtn: false,
    onConfirm: async () => {
      dialog.destroy();
      if (!(await getInformationDetailInfo(template.id))) {
        refetch();
        return;
      }
      const params = {
        id: Number(template.id),
        me: cultureStore.query,
      };
      const [err] = await to(revokeInformation(params));
      if (err) {
        return;
      }
      MessagePlugin.success(successMsg);
      if (pageInfo.value.current > 1 && table.value.list.length === 1) {
        pageInfo.value.current -= 1;
      }
      refetch();
    },
  });
};

const handleRelease = () => {
  navigateToCreateNews();
};

const handleEdit = async (row: Information) => {
  if (!(await getInformationDetailInfo(row.id))) {
    refetch();
    return;
  }
  navigateToEditNews(row.id.toString(), 'createAgain');
};

const getInformationDetailInfo = async (id: number) => {
  const [err, res] = await to(getInformationDetail({ id, ...me.value }, cultureStore.query?.teamId));
  if (err) {
    MessagePlugin.error(err.message || '获取资讯详情失败，请重试');
    return false;
  }
  const { status: resStatus } = res.data.information;
  if (
    status.value === InformationStatusEnum.StatusApproved &&
    resApprovedMap[resStatus as keyof typeof resApprovedMap]
  ) {
    MessagePlugin.error(resApprovedMap[resStatus as keyof typeof resApprovedMap]);
    return false;
  } else if (
    status.value === InformationStatusEnum.StatusReviewing &&
    resReviewingMap[resStatus as keyof typeof resReviewingMap]
  ) {
    MessagePlugin.error(resReviewingMap[resStatus as keyof typeof resReviewingMap]);
    return false;
  }
  return true;
};

const handleRowClick = async (row: any) => {
  if (!(await getInformationDetailInfo(row.id))) {
    refetch();
    return;
  }
  navigateToDetailNews(row.id.toString());
};

const filter = {
  attrs: {
    size: 'small',
    labelWidth: '80px',
    placeholder: '搜索标题/作者/专栏',
  },
};

const table = computed(() => ({
  attrs: {
    'row-key': 'id',
    tableLayout: 'auto',
    hover: true,
    // loading: isLoading,
    onRowClick: ({ row }: { row: any }) => {
      handleRowClick(row);
    },
  },
  list: tableList.value,
  pagination: isNoData.value ? false : pageInfo.value,
  columns: [
    {
      title: '资讯名称',
      width: 448,
      colKey: 'title',
      cell: (h: any, { row }: { row: any }) => {
        const defaultImageUrl = 'https://' + 'ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/h5/wp15100676.webp';

        return (
          <div class="flex gap-12">
            <div class="news-image">
              <img src={row.cover || defaultImageUrl} class="w-96px h-72px rounded-4px object-cover" />
            </div>
            <div class="flex-col gap-4">
              <t-tooltip content={row.title}>
                <div class="text-#1A2139 ellipsis-2" v-html={highlightKeyword(row.title, keyword.value)}></div>
              </t-tooltip>
              {row.author && <div class="text-#828DA5 ellipsis-1">作者：{row.author || ''}</div>}
              <div class="text-#828DA5">展示时间：{formatDateTime(Number(row.displayAt)) || ''}</div>
            </div>
          </div>
        );
      },
    },
    {
      title: '专栏',
      width: 120,
      colKey: 'columnName',
    },
    {
      title: '创建人',
      width: 104,
      colKey: 'creatorName',
    },
    {
      title: () => {
        if (status.value === InformationStatusEnum.StatusReviewing) {
          return '创建时间';
        }
        if (status.value === InformationStatusEnum.StatusRejected) {
          return '审核时间';
        }
        return '发布时间';
      },
      width: 120,
      colKey: 'publishedAt',
      cell: (h: any, { row }: { row: any }) => {
        const time =
          status.value === InformationStatusEnum.StatusReviewing
            ? row.createdAt
            : status.value === InformationStatusEnum.StatusRejected
              ? row.reviewedAt
              : row.publishedAt;
        return <div>{formatDateTime(Number(time))}</div>;
      },
    },
    {
      title: '操作',
      width: 136,
      colKey: 'operate',
      cell: (h: any, { row }: { row: any }) => {
        return (
          <div class="flex gap-8">
            {status.value === InformationStatusEnum.StatusRejected ? (
              <>
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEdit(row);
                  }}
                  class="text-#4D5EFF w-max p-4px rounded-4px cursor-pointer hover:bg-#EAECFF"
                >
                  再次发起
                </div>
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete(row);
                  }}
                  class="text-#4D5EFF w-max p-4px rounded-4px cursor-pointer hover:bg-#EAECFF"
                >
                  删除
                </div>
              </>
            ) : (
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  handleRevoke(row);
                }}
                class="text-#4D5EFF w-max p-4px rounded-4px cursor-pointer hover:bg-#EAECFF"
              >
                撤回
              </div>
            )}
          </div>
        );
      },
    },
  ],
}));

// 窗口聚焦时刷新数据（使用防抖机制避免重复调用）
// useWindowFocus(() => {
//   refetch?.();
// });
</script>

<style scoped lang="less">
.news-warp {
  position: relative;
  height: 100%;
  padding: 0 16px;
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .RTable {
    flex: 1;
  }
}

.tabs-warp {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #eceff5;

  .tabs-warp-list {
    display: flex;
    height: 32px;
    min-width: 80px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #d5dbe4);
    background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
    cursor: pointer;
    color: var(--icon-kyy_color_icon_deep, #516082);
    text-align: center;
    font-size: 14px;
    font-weight: 600;

    &:hover {
      color: #707eff;
      border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_hover, #707eff);
      background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_hover, #dbdfff);
    }
  }

  .active-tab {
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4d5eff);
    color: var(--lingke-White-100, #fff);
    text-align: center;
    font-feature-settings:
      'clig' off,
      'liga' off;
    font-size: 14px;
    font-weight: 600;
    border: 1px solid transparent;
  }
}
</style>
