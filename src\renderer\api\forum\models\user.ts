export interface CardInfo {
  // 高校1.0迭代发现的变化接口没有data.data的数组变成了data.data.items
  items?: CardInfo[];
  /**
   * 头像
   */
  avatar?: string;
  /**
   * 是否可评论
   */
  can_comment?: boolean;
  /**
   * 是否可发帖
   */
  can_post?: boolean;
  /**
   * 身份卡
   */
  card?: string;
  /**
   * 背景封面
   */
  cover?: string;
  /**
   * 身份类型，平台成员：staff_platform
   * 员工：staff
   * 访客：visitor
   * 匿名：anonymous
   */
  flag?: 'staff_platform' | 'staff' | 'visitor' | 'anonymous';
  /**
   * 姓名
   */
  name?: string;
  openid?: string;
  /**
   * 组织标识
   */
  team_id?: string;
  telcode?: string;
  /**
   * 手机号
   */
  telephone?: string;

  /** 身份类型 ownerId */
  id?: string;
  /** 身份类型 */
  _flagText?: string;
}

/**
 * 权限信息
 */
export interface Perms {
  /**
   * 可评论
   */
  canComment?: boolean;
  /**
   * 可管理
   */
  canManage?: boolean;
  /**
   * 可发帖
   */
  canPost?: boolean;
  /**
   * 可使用，存在平台成员身份、员工身份
   */
  canUse?: boolean;
}
/**
 * 权限信息
 */
export interface CoverParams {
  /**
   * 身份卡
   */
  card: string;
  /**
   * 背景封面地址
   */
  cover: string;
}

export interface PermRequest {
  /**
   * 应用标识
   */
  app_uuid?: string;
  /**
   * 身份卡 ID
   */
  card_id?: string;
  owner_id?: string;
  /**
   * 需要获取动态状态时使用
   */
  post_id?: string;
}

/**
 * 用户信息公共部分
 */
export interface UserInfoCommon {
  /**
   * 家乡
   */
  address: { [key: string]: any };
  /**
   * 家乡展示
   */
  address_show: number;
  /**
   * 背景封面
   */
  bg_cover: string;
  /**
   * 生日
   */
  birth: string;
  /**
   * 生日展示
   */
  birth_show: number;
  /**
   * 简介
   */
  intro: string;
  /**
   * 1男 2女
   */
  sex: number;
  /**
   * 0隐藏 1展示 性别展示
   */
  sex_show: number;
  /**
   * 标签展示
   */
  tag_show: number;
  ip_belong_address: string;
}

/**
 * 保存用户信息
 */
export interface UserInfo extends UserInfoCommon {
  /**
   * 标签数组id，删除标签的话 把剩余的标签传过来(不行再沟通)
   */
  tag?: number[];
  [property: string]: any;
}

/**
 * 用户信息详情
 */
export interface UserDetail extends UserInfoCommon {
  /**
   * 根据身份卡获取的头像
   */
  avatar: string;
  /**
   * 身份
   */
  identity: string;
  /**
   * 根据身份卡获取的名称
   */
  name: string;
  /**
   * 查看他人页面时  对他人的备注
   */
  remark?: string;
  /**
   * 标签id数组
   */
  tag: string[];
  /**
   * 标签列表
   */
  tag_list: TagList[];
  [property: string]: any;
}

export interface TagList {
  colour: string;
  name: string;
  type: string;
  value_id: string;
  [property: string]: any;
}
interface MemberInfo {
  avatar: string;
  card_id: string;
  name: string;
  openid: string;
  remark: string;
  staff_id?: string;
  platform_staff_id?: string;
}

export interface AtMembers {
  /**
   * 平台成员
   */
  ptMember: MemberInfo[];
  /**
   * 内部员工
   */
  staffMember: MemberInfo[];
}


export interface AnonDetail {
  /**
   * 头像
   */
  avatar: string;
  created_at: number;
  /**
   * 名称
   */
  name: string;
  openid: string;
  platform_type: string;
  team_id: string;
  updated_at: number;
}
