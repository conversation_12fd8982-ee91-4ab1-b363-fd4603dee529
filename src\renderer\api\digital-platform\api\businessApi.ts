import {
  activitiesRequest,
  lssClientOrganizeMemRequest as client_orgRequest,
  im_syncRequest,
  lssSquareMemRequest,
  squareRequest,
  ringkolRequestForExport,
} from "@renderer/utils/apiRequest";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
import { AxiosResponse } from "axios";

// 会员管理
const Api = {
  digit212StaffListApi: '/digit212/getStaffList',
  digit212CommonTeamsApi: '/digit212/common/getSortTeams',
  commonTeamsApi: "/common/teams", // 获取成员的组织列表
  commonAppAuth: "/common/app-auth", // 根据teamId获取全部应用权限

  authorityCheck: '/teams/authority/check', // 校验组织权限
  teamAnnualFee: '/v1/shared/team_annual_fee', //
  openApp: '/common/open-app', // 开启应用
  digitalMembers: '/common/digital/members', // 获取群成员列表
  digitalOwner:  '/common/digital/owner', // 设置群主
  commonDigitalInfo:  '/common/digital/info', // 群信息

  culturalWallcreate:  '/workshop/cultural-wall/create', // 创建文化墙
  getCulturalDetail: '/workshop/cultural-wall/detail', // 获取文化墙详情
  honorListSwitch:'/honor/list-switch', // 判断 荣誉 组织介绍 历程是否 存在数据

  commonDigitalConnect:  '/common/digital/connect', // 数字平台连接
  commonDigitalCheckConnect: '/common/digital/check-connect', // 判断是否能建立连接
  commonDigitalCreateConnect: '/common/digital/relation-connect', // 建立关联组织连接

  commonTrialAnnualFee: '/v1/organization/trial_annual_fee', // 段佳宁那边，获取可用体验年费套餐
  setSquareApply: '/common/digital/square-apply', // 设置数字平台广场申请入会入口

  byAppUuidGetTeamApi: '/visitor/byAppUuidGetTeam', // 访客获取应用组织
  visitorCardsApi: '/visitor/cards', // 获取访客卡片身份


  setTeamSortApi: '/digit212/common/teamSort', // 数字平台2.1.2设置排序组织

  platformRedDotApi: '/platform/app/red-dot', // 获取数字平台应用红点

  getLabelOrTitleApi: '/label', // 获取标签或者头衔

  // delLabelOrTitleApi: '/label/{valueId}', // 删除标签或者头衔
  updateLabelOrTitleApi: '/label/update', // 修改标签或者头衔


  // 获取设置联络方式信息
  contactInformationDetail: '/liaisons/contactInformationDetail', // 获取设置联络信息
  setContactInformation: '/liaisons/setContactInformation', // 設置聯絡方式

  getExclusiveSetting: '/exclusive/team', // 获取专属名称

  getExclusiveRegion: '/exclusive/region', // 获取专属区域列表
  getExclusiveFeaturesTypes: '/exclusive/features/type', // 获取专属特性类型列表

  getExclusiveFeatures: '/exclusive/features', // 获取专属特性列表

  getExclusiveTeamrenew: '/exclusive/team/renew', // 获取组织续期信息
  getExclusive: '/exclusive/team/order', // 获取组织变更列表
  getExclusiveTeam: '/exclusive/team', // 获取组织专属信息
  createExclusiveConsult: '/exclusive/consult', // 创建咨询单
  checkExclusiveConsult: '/exclusive/consult/check', // 校验规则：咨询
  checkExclusiveOrder: '/exclusive/order/check', // 校验规则：转订单
  exclusiveOrderChange: '/exclusive/order', // 转订单
  exclusiveOrderCalculate: '/exclusive/order/calculate', // 计算金额

  byOpenIdGetStaffsPlatform: '/member/platform/byOpenIdGetStaffsPlatform', //获取当前用户所有组织的专属名称
  deliverCreate: '/business-card/deliver/create', // 透传记录创建


  // 工场1.6
  getTeamLabelSetting: '/team/label/setting', // 获取组织标签， 保存标签设置
  bindLabelRelation:'/team/label/value/relation', // 员工绑定标签
  sortLabelTeam: '/team/label/value/sort', // 排序标签值
  LabelTeam: '/team/label/value', // 标签删除，更新, 添加

  teamDirectoryList: '/team/directory/list', // 目录列表
  teamDirectorySave: '/team/directory/save', // 目录保存
  teamDirectoryDetail: '/team/directory/detail', // 目录详情
  teamDirectoryInside: '/team/directory/inside-list', // 组织架构图
  teamDirectorySaveImg:'/team/directory/save-img', // 名录设置头像

  policyChannelInfo: '/policy/channel-info', // 获取用户访问（红点、入口控制、是否有审核权限

  policyDraftListApi:'/policy/draft-list', // 数智工场-直通车-草稿箱

  getPolicyManageListApi: '/policy/manage-list', // 数智工场-直通车-管理列表-政策
  delPolicyDeleteApi: '/policy/delete', // 数智工场-直通车-删除政策

  getInterpretDraftListApi:  '/interpret/draft-list', // 数智工场-解读列表-草稿箱
  delInterpretDeleteApi: '/interpret/delete', // 数智工场-解读列表-删除政策

  getInterpretDraftDetailApi:  '/interpret/draft-detail', // 数智工场-解读列表-详情
  getPolicyDraftDetailApi:  '/policy/draft-detail', // 数智工场-政策列表-详情


  getPendingAuditCountApi: '/storeProduct/v1/productPromotion/getPendingAuditCount', // 数智工场-市场商品管理-待审核-其他平台


  getListSquareIndustriesApi: '/square/v1/digitalBusiness/listSquareIndustries', //ListSquareIndustries 查询广场行业分类列表
};

export function getListSquareIndustriesAxios(teamId) {
  return ringkolRequestForExport({
    method: "get",
    url: Api.getListSquareIndustriesApi,
    headers: {
      teamId
    }
  });
}


// 
export function getPendingAuditCountAxios(teamId:string) {
  return ringkolRequestForExport({
    method: "get",
    url: Api.getPendingAuditCountApi,
    params: {
      teamId
    }
  });
}

// 数智工场-政策列表-详情
export function getPolicyDraftDetailAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getPolicyDraftDetailApi+ `/${params.id}`,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}

// 数智工场-解读列表-详情
export function getInterpretDraftDetailAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getInterpretDraftDetailApi + `/${params.id}`,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}

// 数智工场-直通车-管理列表-政策
export function delInterpretDeleteAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.delInterpretDeleteApi,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


// 数智工场-解读列表-草稿箱
export function getInterpretDraftListAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getInterpretDraftListApi,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}

// 数智工场-直通车-管理列表-政策
export function delPolicyDeleteAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.delPolicyDeleteApi,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}

// 数智工场-直通车-管理列表-政策
export function getPolicyManageListAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getPolicyManageListApi,
    params: {
   ...params,
    },
    headers: {
      teamId
    }
  });
}


// 获取草稿列表
export function getPolicyDraftListAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.policyDraftListApi,
    params: {
   ...params,
    },
    headers: {
      teamId
    }
  });
}


// 获取用户访问（红点、入口控制、是否有审核权限
export function policyChannelInfoAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.policyChannelInfo,
    params: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}


// 名录设置头像
export function teamDirectorySaveImgAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.teamDirectorySaveImg,
    data: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}

// 工场1.6 组织架构图, params无需传，{}即可
export function getTeamDirectoryInsideAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.teamDirectoryInside,
    params: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}


// 工场1.6 目录详情
/**
 * 名录员工id
 * @type {number} const id_staff: number;
 */
export function getTeamDirectoryDetailAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.teamDirectoryDetail,
    params: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}



/**
 * @description 个人信息相关参数
 * @property {string} [img] - 图片地址，可选
 * @property {{ telephone: string; phone_code: string }[]} [telephone_info] - 电话号码信息数组，可选
 * @property {string} telephone - 电话号码，必需
 * @property {string} phone_code - 电话区号，必需
 * @property {1 | 2} [sex] - 性别（1: 男，2: 女），可选
 * @property {string} [email] - 邮箱地址，可选
 * @property {string} [hobby] - 爱好，可选
 * @property {{ name: string; value: string }[]} [customize_info] - 自定义信息数组，可选
 * @property {string} name - 自定义信息名称，必需
 * @property {string} value - 自定义信息值，必需
 * @property {number[]} personal_value_ids - 个人标签值 ID 数组，必需
 * @property {number[]} team_value_ids - 团队标签值 ID 数组，必需
 */
export function teamDirectorySaveAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.teamDirectorySave,
    data: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}


// 工场1.6 目录列表
export function getTeamDirectoryListAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.teamDirectoryList,
    params: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}




/**
 * @description 某个对象的参数描述
 * @property {number} [enable] - 是否启用（1：是，0：否），可选
 * @property {string} [name] - 名称，可选
 * @property {string} [explain] - 说明，可选
 */
export function saveLableTeamSettingAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.getTeamLabelSetting + `/${params?.id}`,
    data: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}


/**
 * 工场1.6 添加标签值
 * @description 标签设置相关参数
 * @property {number} [setting_id] - 标签设置id，可选
 * @property {string} [name] - 名称，可选
 * @property {string} [colour] - 颜色，可选
 * @returns
 */
export function addLableTeamAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.LabelTeam,
    data: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}

// 工场1.6 更新标签值
export function updateLableTeamAxios(params, teamId?) {
  return client_orgRequest({
    method: "put",
    url: Api.LabelTeam + `/${params?.id}`,
    headers: {
      teamId,
    },
  });
}


// 工场1.6 删除标签值
export function deleteLabelTeamAxios(params, teamId?) {
  return client_orgRequest({
    method: "delete",
    url: Api.LabelTeam + `/${params?.id}`,
    data: {
      ...params,
    },
    headers: {
      teamId,
    },
  });
}


// 工场1.6 员工绑定标签
/**
 * @description 工场1.6 员工绑定标签参数
 * @property {number[]} [personal_value_ids] - 个人标签值ids，可选
 * @property {number[]} [team_value_ids] - 平台标签值ids，可选
 */
export function bindLabelRelationAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.bindLabelRelation,
    data: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}


// 工场1.6 排序标签值
/**
      "setting_id": 0,标签设置id
      "value_ids": [标签值ids
        0
      ]
 */
export function sortLabelTeamAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.sortLabelTeam,
    data: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}

// 工场1.6 获取组织标签
export function getTeamLabelSettingAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getTeamLabelSetting,
    params: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}




// 透传记录创建
export function deliverCreateAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.deliverCreate,
    data: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}

// 获取当前用户所有组织的专属名称
export function byOpenIdGetStaffsPlatformAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.byOpenIdGetStaffsPlatform,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}

// 获取组织续期信息
export function getExclusiveTeamrenewAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getExclusiveTeamrenew,
    params: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}

// 获取组织变更列表
export function getExclusiveAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getExclusive,
    params: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}

/**
 * 计算金额
 */
export function exclusiveOrderCalculateAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.exclusiveOrderCalculate,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}

/**
 * 转订单
 */
export function exclusiveOrderChangeAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.exclusiveOrderChange,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}
/**
 * 校验规则：转订单
 */
export function checkExclusiveOrderAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.checkExclusiveOrder,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}



// 校验规则：咨询
export function checkExclusiveConsultAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.checkExclusiveConsult,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


// 创建咨询单
export function createExclusiveConsultAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.createExclusiveConsult,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


// 获取专属特性列表
export function getExclusiveFeaturesAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getExclusiveFeatures,
    params: {
     ...params,
    },
    headers: {
      teamId
    }
  });
}


// 获取专属特区类型列表
export function getExclusiveFeaturesTypesAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getExclusiveFeaturesTypes,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


// 获取专属区域列表

export function getExclusiveRegionAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getExclusiveRegion,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}

export function getExclusiveSettingAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getExclusiveSetting,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}



export function setContactInformationAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.setContactInformation,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}



export function getContactInformationDetailAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.contactInformationDetail,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


export function updateLabelOrTitle(params, teamId?) {
  return client_orgRequest({
    method: "put",
    url: Api.getLabelOrTitleApi + `/${params?.id}`,
    headers: {
      teamId,
    },
    data: {
      ...params,
    },

  });
}


export function deleteLableOrTitle(id, teamId?) {
  return client_orgRequest({
    method: "delete",
    url: Api.getLabelOrTitleApi + `/${id}`,
    headers: {
      teamId,
    },
  });
}



export function setLabelOrTitleApiAxios(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.getLabelOrTitleApi,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}

export function getLabelOrTitleApiAxios(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getLabelOrTitleApi,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


export function getPlatformRedDotApi(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.platformRedDotApi,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


// 获取应用入口列表
export function getAppEntries(square_id, team_id) {
  return lssSquareMemRequest({
    method: "get",
    url: `/v1/square/${square_id}/app_entries`,
    headers: {
      teamId: team_id,
    },
  });
}


// 获取访客卡片身份
export function getVisitorCardsApi(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.visitorCardsApi,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


export function getByAppUuidGetTeamApi(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.byAppUuidGetTeamApi,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}

// 设置数字平台广场申请入会入口
export function setSquareApplyAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.setSquareApply,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId
    }
  });
}


// 段佳宁那边，获取可用体验年费套餐
export function getCommonTrialAnnualFee(params, teamId?) {
  return squareRequest({
    method: "get",
    url: Api.commonTrialAnnualFee,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}



// 建立关联组织连接
export function SetCommonDigitalCreateConnect(params, teamId) {
  return client_orgRequest({
    method: "post",
    url: Api.commonDigitalCreateConnect,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


// 数字平台连接
export function digitalPlatformConnect(params, teamId) {
  return client_orgRequest({
    method: "post",
    url: Api.commonDigitalConnect,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


export function getCommonDigitalCheckConnect(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.commonDigitalCheckConnect,
    params: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}


// 获取文化墙详情
export function getCulturalDetail(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getCulturalDetail,
    params: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

// 创建文化墙
export function setCulturalWallcreate(params, teamId) {
  return client_orgRequest({
    method: "post",
    url: Api.culturalWallcreate,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}




export const getGroupInfoApi = (id: string) =>
  im_syncRequest({
    url: `/v1/groups/${id}`,
    method: "get",
  });

// 群信息
export function commonDigitalInfoAxios(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.commonDigitalInfo,
    params: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

// 设置群主
export function setDigitalOwner(params, teamId) {
  return client_orgRequest({
    method: "post",
    url: Api.digitalOwner,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


export function getDigitalMember(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.digitalMembers,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}

// 开启应用
export function getOpenApp(params, teamId) {
  return client_orgRequest({
    method: "post",
    url: Api.openApp,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


export function setTeamSort(params, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.setTeamSortApi,
    data: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}


export function getTeamAnnualFee(params, teamId?) {
  return squareRequest({
    method: "get",
    url: Api.teamAnnualFee,
    params: {
      ...params,
    },
    headers: {
      teamId
    }
  });
}

export function getPlatformDig212TeamsAxios(params, ) {
  return client_orgRequest({
    method: "get",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.digit212StaffListApi,
    params: {
      ...params,
    },
    // headers: {
    //   teamId: getMemberTeamID(),
    // },
  });
}


export function getCommon212TeamsAxios(params) {
  return client_orgRequest({
    method: "get",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.digit212CommonTeamsApi,
    params: {
      ...params,
    },
    // headers: {
    //   teamId: getMemberTeamID(),
    // },
  });
}


// 获取成员的组织列表
export function getCommonTeamsAxios(params) {
  return client_orgRequest({
    method: "get",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.commonTeamsApi,
    params: {
      ...params,
    },
    // headers: {
    //   teamId: getMemberTeamID(),
    // },
  });
}

export function getCommonAppAuthAxios(params, teamId) {
  return client_orgRequest({
    method: "get",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.commonAppAuth,
    params: {
      ...params,
    },
    headers: {
      teamId,
    },
  });
}

// 校验组织权限
export function getAuthorityCheckAxios(params, teamId) {
  return client_orgRequest({
    method: "get",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.authorityCheck,
    params: {
      ...params,
    },
    headers: {
      teamId,
    },
  });
}

export function visitorDel(id, teamId) {
  return client_orgRequest({
    method: "delete",
    url: `/visitor/platformDelete/${id}`,
    headers: {
      teamId,
    },
  });
}


