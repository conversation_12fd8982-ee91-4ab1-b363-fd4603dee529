<template>
  <div>
    <t-dialog
      class="visible"
      v-model:visible="contactsStore.addContactsVisible"
      :confirmBtn="null"
      :cancelBtn="null"
      :closeOnEscKeydown="false"
      :closeOnOverlayClick="false"
      :closeBtn="false"
      @close="onclose"
      @opened="onOpened"
    >
      <template #header>
        <div class="custom-header ">
          <span> {{ t('contacts.addContacts') }} </span>
          <div class="tricks">
            <Tricks :isDrag="false" size="small" uuid="通讯录-添加好友" />
          </div>
        </div>
        <iconpark-icon name="iconerror" class="btn-close" @click="onclose" />
      </template>
      <template #body>
          <guide-page v-if="showAddContactsGuidePage" type="addContacts" @close="know" />


          <div class="card-box">
            <div class="tips">{{ cardIdType(identityId) === 'personal' ? t('contacts.guide_add_tip1') : t('contacts.guide_add_tip2') }}</div>
            <t-select v-replace-svg
              v-model="identityId"
              autoWidth
              borderless
              :readonly="!cards?.length"
              v-model:popupVisible="popupVisible"
              @change="identityIdChange"
              @popup-visible-change="popupChange"
              :popup-props="{overlayClassName: 'addContactIdentityId', overlayInnerStyle:{ width: '480px', borderRadius: '8px'} , placement: 'bottom', attach: '.card-box'}"
              >
              <t-option-group
                v-for="(items, indexs) in identityGroupOptions"
                :key="indexs"
                :label="typeof items.group === 'object' ? items.group.label : items.group"
              >
                <t-option
                  v-for="(item, index) in items.children"
                  :key="item.uuid"
                  :label="item.name"
                  :value="item.uuid"
                >
                  <div :class="{'optionSelected': item.uuid === identityId, personal: !item.team}" style="display: flex;align-items: center;justify-content: space-between;">
                    <div class="option-box" style="display: flex;align-items: center;flex: 1;">
                      <kyy-avatar
                        avatarSize="24px"
                        :imageUrl="item.avatar"
                        :userName="item.name"
                        roundRadius
                      ></kyy-avatar>
                      <div class="flex-col">
                        <div>
                          <span
                        class="name"
                        style="
                        margin: 0 4px 0 8px;
                        font-size: 14px;
                        font-weight: 400;
                        color: var(--kyy_color_modal_title, #1A2139);
                        line-height: 22px;
                        max-width: 150px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;"
                      >
                        {{ item.name }}
                      </span>
                      <span
                        v-if="item.team"
                        class="org changeCardOrgStyle"
                        style="font-size: 12px;
                        padding: 0 4px;
                        font-weight: 400;
                        color: #e66800;
                        line-height: 22px;
                        max-width: 200px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;"
                      >
                        {{ item.team }}
                      </span>
                      <span
                        v-else
                        class="org changeCardOrgStyle"
                        style="font-size: 12px;
                        padding: 0 4px;
                        font-weight: 400;
                        color: var(--kyy_color_tag_text_success, #499D60);
                        background: var(--kyy_color_tag_bg_success, #E0F2E5);
                        line-height: 22px;
                        max-width: 200px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;"
                      >
                        {{ t('contacts.guide_profile') }}
                      </span>
                    </div>

                      <div v-if="item.positions?.length && item.positions[0].departmentName" class="jobs">
                        <div class="item">{{ item.positions[0].departmentName }}</div>
                        <div v-if="item.positions[0].jobName" class="divider"></div>
                        <div class="item">{{ item.positions[0].jobName }}</div>
                      </div>
                    </div>
                  </div>

                    <img v-if="item.uuid === identityId" src="@renderer/assets/im/radio_checkbox.svg" alt="">
                  </div>

                </t-option>
              </t-option-group>
              <template #valueDisplay="{ value }">
                <div v-if="value" class="option-box" style="display: flex;align-items: center;">
                  <kyy-avatar
                    avatarSize="24px"
                    :imageUrl="format(value, 'avatar')"
                    :userName="format(value, 'name')"
                    roundRadius
                  ></kyy-avatar>
                  <span
                    class="name"
                    style="
                    margin: 0 4px 0 8px;
                    font-size: 14px;
                    font-weight: 400;
                    color: var(--kyy_color_modal_title, #1A2139);
                    line-height: 22px;
                    max-width: 150px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;"
                  >
                    {{ format(value, 'name') }}
                  </span>
                  <span
                    v-if="format(value, 'team')"
                    class="org changeCardOrgStyle"
                    style="font-size: 12px;
                    padding: 0 4px;
                    font-weight: 400;
                    line-height: 22px;
                    max-width: 200px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;"
                  >
                    {{ format(value, 'team') }}
                  </span>
                  <span
                    v-else
                    class="org changeCardOrgStyle"
                    style="font-size: 12px;
                    padding: 0 4px;
                    font-weight: 400;
                    color: var(--kyy_color_tag_text_success, #499D60);
                    background: var(--kyy_color_tag_bg_success, #E0F2E5);
                    line-height: 22px;
                    max-width: 200px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;"
                  >
                    {{ t('contacts.guide_profile') }}
                  </span>
                </div>
                <div v-else>
                  {{ t('contacts.selectPlaceholder') }}
                </div>
              </template>
              <template #suffixIcon>
                <caret-down-small-icon style="color: rgb(130, 141, 165);" v-show="!selectPopupVisible"/>
                <caret-up-small-icon style="color: rgb(76, 94, 255);" v-show="selectPopupVisible"/>
              </template>
            </t-select>
          </div>
          <div class="input-box">
            <t-input v-model="searchValue" :disabled="!identityId" :placeholder="t('contacts.searchLkIdPlaceholder')" autofocus clearable @clear="clear" @enter="refresh">
              <template #prefixIcon>
                <img style="width: 20px;height: 20px;" src="@renderer/assets/im/icon_search.svg" alt="">
              </template>
            </t-input>
            <t-button :disabled="!searchValue" :loading="searchLoading" @click="refresh">{{ t('account.search') }}</t-button>
          </div>
          <div v-loading="searchLoading" style="flex:1;overflow-y: auto;">
          <div v-if="searchList.length" class="listPage-box">
            <div class="contacts-card" v-for="(item, index) in searchList" :key="index" @click="gotoDetail(item)">
              <kyy-avatar
                avatarSize="46px"
                :imageUrl="item.avatar"
                :userName="item.name"
                roundRadius
              ></kyy-avatar>
              <div class="info">
                <div class="name-item" style="display: flex;gap:4px;">
                  <span class="name">{{ item.name }}</span>
                  <span class="org changeCardOrgStyle" v-if="item.teamName">{{ item.teamName }}</span>
                </div>
                <div class="phone-item">
                  <span class="region" v-if="item.telCode">+{{ item.telCode }}</span>
                  <span class="phone">{{ item.telephone }}</span>
                </div>
              </div>
              <div class="button">
                <template v-if="!hideButton(item)">
                  <div v-if="item.status === 'FRIEND_APPLY_STATUS_ACCEPTED'">{{ t('contacts.addStatusPassed') }}</div>
                  <div v-else-if="item.status === 'FRIEND_APPLY_STATUS_PENDING'">{{ t('contacts.addStatusPassing') }}</div>
                  <t-button v-else @click.stop="add(item)" style="min-width: auto;">{{ t('contacts.addStatusAdd') }}</t-button>
                </template>
              </div>

              <div v-if="index > 0" class="divider"></div>
            </div>
          </div>
          <div v-else-if="searchValue && hasSearch" class="defaultPage-box">
            <Empty :name="searchError ? 'offline' : 'no-search-contact'" :tip="searchError ? '网络异常，请稍后再试' : '用户不存在'"></Empty>
          </div>

          <div v-if="!searchValue" style="text-align: center; margin-top: 16px">
            <div class="btn-my-qr" @click="openQr">
              {{ t('contacts.myQr') }}<iconpark-icon name="icondimensional-ed5ocma4" class="icon" />
            </div>
          </div>
        </div>
      </template>



    </t-dialog>

    <CardQr :visible="Boolean(qrItem)" :card-id="qrItem?.card" :card-info="qrItem?.info" @close=" qrItem = null" destroyOnClose></CardQr>

    <apply-dialog v-model:visible="applyVisible" :cardInfo="cardInfo" :myId="identityId" @onApplyContact="refresh" @onconfirm="onconfirm" />
  </div>
</template>

<script setup lang="ts">
import guidePage from '@renderer/components/selectMember/guide-page.vue';
import applyDialog from '@renderer/views/identitycard/dialog/applyContact.vue';
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { useContactsStore } from '@/store/modules/contacts'
import {computed, onMounted, ref, watch} from 'vue'
import { CaretDownSmallIcon, CaretUpSmallIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from "tdesign-vue-next";
import { useI18n } from 'vue-i18n';
import { getProfilesInfo, getCards, getOpenid } from '@renderer/utils/auth';
import { searchProfilesCardList, searchExternalCardList, canApplyFriend } from "@/api/contacts/api/friend";
import {openChat} from "@/utils/share";
import {cardIdType, loadAcountIdentityCards} from "@/views/identitycard/data";
import { acceptFriend } from '@renderer/api/im/api';

import Empty from "@renderer/components/common/Empty.vue";
import CardQr from '@renderer/views/identitycard/components/CardQr.vue';
import LynkerSDK from '@renderer/_jssdk';
const Store = LynkerSDK.eStore;

const selfInfo = getProfilesInfo();
const profilesInfo = ref(getProfilesInfo());
const cards = ref(getCards());
const { t } = useI18n();
const contactsStore = useContactsStore()
const identityOptions = computed(() => [..._transData('personalInfo', profilesInfo.value), ...cards.value]);
const identityGroupOptions = computed(() => {
  if (cards.value?.length) {
    return [
      {
        group: t('contacts.guide_profile_card'),
        children: _transData('personalInfo', profilesInfo.value)
      },
      {
        group: t('contacts.guide_out_card'),
        children: cards.value
      },
    ]
  } else {
    return [
      {
        group: t('contacts.guide_profile_card'),
        children: _transData('personalInfo', profilesInfo.value)
      }
    ]
  }
})
const identityId = ref('')
const searchValue = ref('')
const searchList = ref([])
const applyVisible = ref(false);
const cardInfo = ref({});
const timer = ref(null);
const searchError = ref(false);
const searchLoading = ref(false);
const hasSearch = ref(false);
const popupVisible = ref(false);

const props = defineProps({
  searchValueProps: {
    type: String,
    default: '',
  },
})

const store = new Store();
const checkHideAddContactsGuidePage = () => {
  const data = Array.isArray(store.get("hideAddContactsGuidePage")) ? store.get("hideAddContactsGuidePage") : [];
  return data?.includes(getOpenid());
};
const hideAddContactsGuidePageStore = ref(true);
const showAddContactsGuidePage = computed(() => {
  if (identityOptions.value?.length > 1) {
    return !hideAddContactsGuidePageStore.value
  } else {
    return false;
  }
});
const know = () => {
  hideAddContactsGuidePageStore.value = true
  popupVisible.value = true
}

const selectPopupVisible = ref(false);
const popupChange = (visible) => {
  selectPopupVisible.value = visible;
}
watch(() => searchValue.value, (newVal, oldVal) => {
  if (oldVal && !newVal) {
    hasSearch.value = false
  }
})

const identityIdChange = (val) => {
  searchValue.value = ''
  searchList.value = []
  store.set('addContactsDialogActiveIdentityId', val);
}
// 转换数据模型通用
const _transData = (cardType, data) => {
  if (cardType === 'personalInfo') {
    return [{
      isPersonalInfo: true,
      avatar: data?.avatar || '',
      name: data?.title || '',
      linkId: data.link_id,
      cardId: data.openid,
      openid: data.openid,
      uuid: data.openid,
      teamId: '',
      team: '',
      jobs: '',
      staffId: data.openid,
      logo: '',
      positions: [],
    }]
  }
  if (cardType === 'toExternalCard') {
    return data?.openid
      ? [{
        staffId: '',
        staffName: '',
        staffAvatar: '',
        avatar: data?.avatar || '',
        name: data?.title || '',
        telCode: data?.region || '',
        telephone: data?.cellphone || data?.telephone || '',
        no: '',
        email: data?.email || '',
        uuid: '',
        teamId: '',
        positions: [],
        staffOpenId: data?.openid || '',
        cardId: data?.openid || '',
        teamName: ''
      }]
      : []
  }
}
const formatSearchList = async(searchProfilesCard, searchExternalCardList,card?:string) => {
  console.log(searchProfilesCard, searchExternalCardList)
  let cardList = [..._transData('toExternalCard', searchProfilesCard), ...searchExternalCardList]
  console.log('=====>',cardList,identityId.value,card);
  
   for (const items of cardList) {
    if(card && items.cardId === card){
      items.status = 'FRIEND_APPLY_STATUS_ACCEPTED';
      continue
    }
    const applyres =  await canApplyFriend({main:identityId.value,peer:items?.cardId})
    const { data } = applyres.data
    if(data.isFriend) {
      items.status = 'FRIEND_APPLY_STATUS_ACCEPTED';
      continue
    }
    if(!data.apply) continue
    if(data.apply.status === "FRIEND_APPLY_STATUS_PENDING") {
      if(data.apply.cardIdSelf === identityId.value){
        // 对方发起的申请，表示存在互加的情况
        items.status = 9999 // 自定义9999互加状态
      } else {
        items.status = data.apply.status
      }
    } else {
      items.status = data.apply.status
    }
    items.applyId = data.apply.id
  }
  console.log('-----cardList',cardList)
  return cardList
}
const gotoDetail = (item) => {
  LynkerSDK.ipcRenderer.invoke("identity-card", {
    cardId: item?.cardId || item.staffOpenId,
    myId: identityId.value
  });
}

const agreeApply = async (id, cardId) => {

  acceptFriend({id})
    .then(() => {
      refresh(cardId);
      LynkerSDK.ipcRenderer.invoke("update-contact");
    })
    .catch((err) => {
      MessagePlugin.error(
        err.response.data?.message || t("identity.addContactFailed")
      );
    });
};

const qrItem = ref<{ card: string, info: any}>();
const qrCodeImgVisible = ref(false);
const openQr = () => {
  qrItem.value = {
    card: identityId.value,
    info: identityOptions.value.find((i) => i.uuid === identityId.value),
  };
  qrCodeImgVisible.value = true;
};

const add = async (item) => {
  if (item.status === 9999) {
    // 前端自定义互加状态
    agreeApply(item.applyId,item.cardId);
  } else {
    console.log(item)
    cardInfo.value = { ...item, openid: item?.staffOpenId }
    applyVisible.value = true
  }
}
const clear = () => {
  searchList.value = []
}
const hideButton = (item) => {
  let identityTeamId = identityOptions.value?.find(it => it.uuid === identityId.value)?.teamId
  let isSameExternal = (identityTeamId && (item.teamId == identityTeamId))
  let isSelf = (item.cardId == identityId.value || item.staffOpenId == selfInfo.openid)
  return isSameExternal || isSelf
}
const onconfirm = async (id) => {
  acceptFriend({id}).then(() => { 
    openChat({ main: identityId.value, peer: cardInfo.value.cardId });
  }).catch(err => {
    MessagePlugin.error(err.response.data?.message || '添加联系人失败');
  });
}
//9999 card用作互加判断
const refresh = (card?:string) => {
  onChange(searchValue.value,card);
}
const onChange = (val,card?:string) => {
  searchError.value = false;
  if (!val.trim()) return
  if (identityId.value) {
    searchLoading.value = true;
    hasSearch.value = true;
    let teamId = format(identityId.value, 'teamId')
    Promise.all([
      searchProfilesCardList({ cond: val }),
      searchExternalCardList({ keyword: val }, teamId),
    ]).then(async res => {
      let searchProfilesCard = [], searchExternalCardList = [], allApplyList = []
      if (res[0]?.status === 200) {
        searchProfilesCard = res[0]?.data
      }
      if (res[1]?.status === 200) {
        searchExternalCardList = res[1]?.data?.data || []
      }
      searchList.value = await formatSearchList(searchProfilesCard, searchExternalCardList,card)

      console.log('=====>11',searchList.value);
    }).catch(() => {
      searchError.value = true;
    }).finally(() => {
      searchLoading.value = false;
    })
  } else {
    MessagePlugin.warning({
      content: t('contacts.changeIdentityTips'),
      duration: 2000,
    })
  }
};
const emits = defineEmits(['close']);
const onclose = () => {
  searchValue.value = ''
  searchList.value = []
  contactsStore.addContactsVisible = false;
  emits('close')
}
const onOpened = async () => {
  await loadAcountIdentityCards();
  hideAddContactsGuidePageStore.value = checkHideAddContactsGuidePage();
  profilesInfo.value = getProfilesInfo();
  cards.value = getCards();
  if (props.searchValueProps) {
    identityId.value = identityOptions.value[0].uuid;
    searchValue.value = props.searchValueProps;
    refresh();
    return;
  }
  if (identityOptions.value?.length === 1) {
    identityId.value = identityOptions.value[0].uuid
  } else {
    const id = store.get('addContactsDialogActiveIdentityId');
    identityId.value = identityOptions.value?.find(item => item.uuid === id)?.uuid || identityOptions.value[0].uuid;
  }
}
// 防抖
const debounce = (fn, wait, params) => {
  if (timer.value !== null) {
    clearTimeout(timer.value)
  }
  timer.value = setTimeout(() => {
    fn(params)
  }, wait)
}
const format = (value, key) => {
  return identityOptions.value?.find(item => item.uuid == value)?.[key]
}

LynkerSDK.ipcRenderer.on('refresh-add-contacts', () => {
  refresh()
})

defineExpose({
  open: () => {
    contactsStore.addContactsVisible = true;
  },
})

onMounted(() => {
  onOpened()
})
</script>

<style lang="less">
.changeCardOrgStyle {
  padding: 1px 5px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_warning, #FFE5D1);
  color: var(--kyy_color_tag_text_warning, #FC7C14);
  font-family: PingFang SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  max-width: 60%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: middle;
}
</style>

<style lang="less" scoped>
.custom-header {
  flex: 1;
  position: relative;
  .tricks {
    position: absolute;
    top: 0px;
    left: 76px;
  }
}
.btn-close {
  font-size: 24px;
  font-weight: normal;
  cursor: pointer;
  color: #516082;
}
::-webkit-scrollbar {
  width:5px;
  height: 8px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius:3px;
  background:rgba(0,0,0,0.1);
  -webkit-box-shadow:inset006pxrgba(0,0,0,0.5);
}
:global(.addContactIdentityId .t-popup__content){
  width: 480px;
  max-height: 360px;
}
:global(.addContactIdentityId .t-popup__content .t-select-option-group__header){
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0 8px;
  color: var(--text-kyy_color_text_2, #516082);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
:global(.addContactIdentityId .t-popup__content .t-select__list .t-select__list){
  padding: 0 !important;
}
:global(.addContactIdentityId .t-popup__content .t-select-option-group__divider + .t-select-option-group__divider::before) {
    content: "";
    width: 100%;
    height: 1px;
    padding: 0 var(--td-comp-paddingLR-s);
    position: absolute;
    top: 0;
    left: 0;
    box-sizing: border-box;
    background-color: var(--divider-kyy_color_divider_light, #ECEFF5);
    background-clip: content-box;
}
:global(.addContactIdentityId .t-popup__content .t-select-option-group + .t-select-option-group) {
    padding-top: 8px;
    margin-top: 8px;
}
:global(.addContactIdentityId .t-popup__content .optionSelected){
  border-radius: 8px;
}
:global(.addContactIdentityId .t-popup__content .t-select-option){
  height: auto !important;
  border-radius: 8px !important;
  padding: 0 !important;
  margin: 4px 0;
}
:global(.addContactIdentityId .t-popup__content .t-select-option > span){
  width: 100%;
  display: inline-block;
  border-radius: 8px !important;
  padding: 8px !important;
}
:global(.addContactIdentityId .t-popup__content .t-select-option .jobs) {
  margin-top: 4px;
  margin-left: 8px;
  color: var(--text-kyy_color_text_3, #828DA5);
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}
:global(.addContactIdentityId .t-popup__content .t-select-option .jobs .item) {
  max-width: 150px;
  .ellipsis();
}
:global(.addContactIdentityId .t-popup__content .t-select-option .jobs .divider) {
  width: 1px;
  height: 16px;
  background-color: #D5DBE4;
}
:global(.my-qr-wrap) {
  text-align: center;
}
:global(.btn-my-qr) {
  display: inline-flex;
  padding: 4px 16px;
  align-items: center;
  gap: 4px;
  border-radius: 99px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  color: var(--text-kyy_color_text_2, #516082);
}
:global(.btn-my-qr .icon) {
  font-size: 32px;
  color: #4D5EFF;
}
:global(.addContactIdentityId .t-popup__content .t-select-option) {
  background: linear-gradient(180deg, #FFF1E5 0.03%, #FFF 99.97%) !important;
}
:global(.addContactIdentityId .t-popup__content .t-select-option:has(.personal)),
:global(.addContactIdentityId .t-popup__content .t-select-option:hover:has(.personal)) {
  background: linear-gradient(180deg, #E0F2E5 0%, #FFF 100%), var(--bg-kyy_color_bg_default, #FFF) !important;
  border-bottom: 0;
}
:global(.addContactIdentityId .t-popup__content .t-select-option:hover){
  box-shadow: inset 0px 0px 0px 1px var(--border-kyy_color_border_hover, #707EFF)
}
:global(.addContactIdentityId .t-popup__content .t-is-selected){
  background: linear-gradient(180deg, #FFF1E5 0.03%, #FFF 99.97%) !important;
}
:global(.addContactIdentityId .t-popup__content .t-is-selected > span){
  width: 100%;
}
:global(.addContactIdentityId .t-popup__content .t-is-selected > span){
  width: 100%;
}
  :deep(.visible .t-dialog){
    width: 560px;
    height: 556px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    padding: 0 !important;
    .t-dialog__header-content{
      align-items: center;
      justify-content: space-between;
      padding: 24px !important;
      padding-bottom: 0px !important;
    }
    .t-dialog__body{
      flex: 1;
      padding: 24px 0;
      padding-bottom: 16px;
      display:flex;
      flex-direction: column;
    }
    .t-dialog__footer {
      display: none;
    }
    .card-box{
      display: flex;
      padding: 8px 16px;
      margin: 0 24px;
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
      align-self: stretch;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_deep, #F5F8FE);
      margin-bottom: 8px;

      .tips{
        color: var(--text-kyy_color_text_3, #828DA5);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
    .input-box{
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      margin: 0 24px 16px;
      .t-button{
        width: 80px;
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4D5EFF) !important;
        color: var(--color-button_primary-kyy_color_button_primary_text, #FFF) !important;
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
    }
    .title{
      white-space: nowrap;
      color: var(--kyy_color_modal_title, #1A2139);
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
    }
    .t-select__wrap{
      // text-align: center;
      .t-select{
        // width: auto;
        .t-input__wrap{
          width: 100% !important;
        }
        .t-input{
          padding: 0 8px;
          height: 40px;
          border: none !important;
          // max-width: 420px;
          .t-input__inner{
            flex: none;
          }
          .t-input__prefix:not(:empty){
            margin-right: 0;
          }
        }
        .t-input:hover{
          cursor: pointer;
          border-radius: 8px;
          background: var(--bg-kyy_color_bg_light, #FFF);
        }
        .t-input.t-is-focused{
          box-shadow: none;
          border-radius: 8px;
          background: var(--bg-kyy_color_bg_light, #FFF);
        }
      }
    }
    .contacts-card:hover{
      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
      border-radius: 6px;
    }

    .contacts-card{
      margin: 0;
      padding: 12px;
      display: flex;
      align-items: center;
      cursor: pointer;
      gap: 12px;
      position: relative;
      .info{
        width: 0;
        flex: 1;
        .name-item{
          height: 22px;
          .name{
            padding-right: 4px;

            color: var(--text-kyy_color_text_1, #1A2139);

            /* kyy_fontSize_2/regular */
            font-family: PingFang SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            text-align: left;
            // max-width: 35%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
          }
          .org{
            //font-size: 12px;
            //
            //font-weight: 400;
            //text-align: left;
            //color: #e66800;
            //line-height: 22px;
            //max-width: 60%;
            //white-space: nowrap;
            //overflow: hidden;
            //text-overflow: ellipsis;
            //display: inline-block;
            // display: flex;
            height: 20px;
            min-height: 20px;
            max-height: 20px;
            padding: 1px 8px;
            // justify-content: center;
            // align-items: center;
            gap: 10px;
            border-radius: var(--kyy_radius_tag_full, 999px);
            background: var(--kyy_color_tag_bg_warning, #FFE5D1);
          }
        }
        .phone-item{
          margin-top: 2px;
          .region{
            padding-right: 4px;
            color: var(--text-kyy_color_text_2, #516082);

            /* kyy_fontSize_2/regular */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            text-align: left;
          }
          .phone{
            color: var(--brand-kyy_color_brand_default, #4D5EFF);

            /* kyy_fontSize_2/regular */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            text-align: left;
          }
        }
      }
      .button {
        color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */

        .t-button{
          width: 60px;
          border-radius: var(--radius-kyy_radius_button_s, 4px);
          background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4D5EFF) !important;
          color: var(--color-button_primary-kyy_color_button_primary_text, #FFF) !important;
          text-align: center;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }

      .divider {
        display: block;
        height: 1px;
        margin: 2px 0;
        background: var(--divider-kyy-color-divider-light, #ECEFF5);
      }

      &:not(:first-child) {
        padding-right: 0;
      }

      &:last-child {
        padding-right: 12px !important;
      }
    }
    // .contacts-card::after {
    //   content: '';
    //   display: block;
    //   position: absolute;
    //   height: 1px;
    //   left: 12px;
    //   right: 0;
    //   bottom: 0;
    //   background: var(--divider-kyy-color-divider-light, #ECEFF5);
    // }
    .contacts-card:nth-last-of-type(1) .divider {
      display: none;
    }

    .listPage-box{
      height: 100%;
      padding: 0 12px;
    }
    .defaultPage-box{
      height: calc(100% - 90px);
      font-size: 14px;

      font-weight: 400;
      text-align: center;
      color: #13161b;
      line-height: 22px;

      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      img {
        width: 200px;
        height: 200px;
        margin-bottom: 10px;
      }
    }
  }

.card-popup {
  width: 330px;
}

.iconhover{
  color: var(--text-kyy_color_text_2, #516082);
  border-radius: 4px;
  width:24px;
  height:24px;
}

.iconhover:hover{
  background: #F3F6FA;
  cursor: pointer;
}

</style>
