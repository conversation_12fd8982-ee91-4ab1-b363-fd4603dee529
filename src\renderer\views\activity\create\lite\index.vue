<template>
  <div class="activity-lite-create-wrapper h-full py-16 flex flex-col gap-12">
    <div class="scroll-content px-2 flex-1">
      <div class="scroll-content h-full">
        <div v-if="isScene" class="w-872 mx-auto px-24 py-8 mb-16 bg-[#EAECFF] rounded-8 flex gap-8">
          <span class="text-[#1A2139]">来自{{ sceneConversationType === '1' ? '单聊' : '群聊' }}：{{ sceneConversationName }}</span>
          <span class="text-[#4D5EFF] cursor-pointer" @click="openSceneConversation">点击查看</span>
        </div>

        <activity-lite-info ref="liteInfoRef" />
      </div>
    </div>

    <div class="operate-box w-872 m-auto rounded-8">
      <t-button
        class="publish-button font-bold"
        theme="default"
        :loading="saveLoading"
        :disabled="saveLoading || publishLoading"
        @click="saveDraft"
      >
        {{ t('activity.activity.saveDraft') }}
      </t-button>
      <t-button class="font-bold" theme="default" @click="preview">{{ t('activity.activity.preview') }}</t-button>
      <t-button
        class="publish-button"
        theme="primary"
        :loading="publishLoading"
        :disabled="saveLoading || publishLoading"
        @click="publish"
      >
        {{ t('activity.activity.publish') }}
      </t-button>
    </div>

    <activity-publish-success-dialog
      ref="activityPublishSuccessDialogRef"
      :attach="isScene ? '.create-form-wrapper' : 'body'"
      :success-id="publishSuccessId"
      @close="closeTab"
      @back-list="onBackList"
      @go-detail="onGoDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, computed, watchEffect, ref, provide, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import _ from 'lodash';
import moment from 'moment/moment';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import LynkerSDK from '@renderer/_jssdk';
import { cardIdType } from '@/views/identitycard/data';
import { getOpenid, setAccountAuthRouters } from '@/utils/auth';
import {
  activityActorBatch,
  activityRelease,
  activitySaveDraft,
  activityUpdate,
  getActivityDetail,
} from '@/api/activity';
import emitter from '@/utils/MittBus';
import { useActivityStore } from '@/views/activity/store';
import ActivityPublishSuccessDialog from '@/views/activity/create/components/ActivityPublishSuccessDialog.vue';
import { openChat } from '@/utils/share';
import ActivityLiteInfo from '@/views/activity/create/components/ActivityLiteInfo.vue';

const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();

const emit = defineEmits(['setActiveIndexAndName', 'settabItem', 'setActiveIndex', 'deltabItem']);

const route = useRoute();
const router = useRouter();

const activityStore = useActivityStore();

const liteInfoRef = ref(null);

const activityPublishSuccessDialogRef = ref(null);

// 当前页面是否激活状态（当前路由是否定位到了此页面）
const isActive = computed(() => {
  if (isDraft.value) {
    return route.path === `/activity/activityLiteCreate/${activityFormData.id}`;
  }
  return route.path === '/activity/activityLiteCreate';
});

// 当前活动是否是草稿，是否有活动id来判断
const isDraft = computed(() => !!activityFormData.id);

// 当前活动预览页的路径（非场景活动情况）
const previewPath = computed(() => (isDraft.value ? `/activity/activityLitePreview/${activityFormData.id}` : '/activity/activityLitePreview'));

// 存草稿按钮的loading状态
const saveLoading = ref(false);
// 发布按钮loading状态
const publishLoading = ref(false);

// 活动表单数据
const activityFormData = reactive({
  // 活动主键，草稿箱状态的活动才有此主键
  id: route.params?.id || null,
  // 基础信息
  basic: {
    // 活动创建人
    creator: null,
    // 活动归属（草稿箱列表进入或场景活动独立窗口打开时会在路由参数中默认带入）
    teamId: route.query?.teamId || null,
    // 活动归属下的身份卡
    cardId: '',
    // 活动主题
    subject: null,
    // 活动类型
    categoryId: null,
    // 主题图片
    assetUrl: null,
    // 活动时间
    duration: {
      startTime: null,
      endTime: null,
    },
    // 活动地点
    location: {
      title: null,
    },
    // 活动主办方
    sponsor: [],
    // 参与人员范围 Publish-公开；Internal-组织内部/我的好友
    actorScope: 'Internal',
    // 活动创建模型类型：0-标准版，1-极速版 这里始终是极速版
    mold: 1,
  },
  // 详情设置
  particulars: {
    // 活动详情
    content: null,
    // 活动附件
    files: {
      files: [],
    },
  },
  // 成员管理
  members: {
    // 活动参与人
    actors: [],
    // 活动嘉宾
    guests: [],
    // 工作人员
    staffs: [],
  },
  // 活动流程
  process: {
    // 活动流程项列表
    processItems: [],
  },
  // 高级设置
  advanced: {
    // 是否开启报名
    openRegist: false,
    // 报名时间
    registerTime: {
      startTime: null,
      endTime: null,
    },
    // 报名人数
    quota: null,
    // 开启报名审核
    registApprove: false,
    // 报名表单
    registerForms: [],
    // 是否开启报名费
    registFeeEnable: false,
    // 报名费金额
    registFee: {
      // 货币单位，这里固定用cny
      currencyCode: 'CNY',
      // 金额
      value: null,
    },
    // 发布渠道
    publishChannels: [],
    // 发布到数字平台
    platforms: [],
    // 是否开启评论
    enableComment: false,
    // 活动现场图
    sceneDrawing: null,
    // 活动提醒
    reminders: [],
  },
  // 创建场景活动的chat信息
  chatId: null,
});

// 当前选中的活动归属组织
const selectedTeam = computed(
  () => activityStore.groupList.find((group) => group.teamId === activityFormData.basic.teamId) || {},
);

// 当前活动归属是否是个人身份
const isPersonal = computed(() => !selectedTeam.value.uuid || cardIdType(selectedTeam.value.uuid) === 'personal');

// 是否是场景新建活动（场景新建活动为layoutActivity独立窗口打开）
const isScene = route.path.indexOf('layoutActivity') !== -1;
// 场景新建活动会话类型 1单聊 3群聊
const sceneConversationType = ref(route.query?.conversationType);
// 场景新建活动会话名称
const sceneConversationName = ref(route.query?.targetName);
// 场景新建活动会话id
const sceneConversationId = ref(route.query?.targetId);

// 注入活动表单数据
provide('activityFormData', activityFormData);
// 注入活动归属组织信息计算值
provide('selectedTeam', selectedTeam);
// 注入活动归属是否是个人身份计算值
provide('isPersonal', isPersonal);
// 注入活动是否是场景活动
provide('isScene', isScene);

// 提交的表单数据
const submitFormData = computed(() => ({
  ...activityFormData,
  basic: {
    ...activityFormData.basic,
    teamId: isPersonal.value ? '' : activityFormData.basic.teamId,
  },
  advanced: {
    ...activityFormData.advanced,
    // 报名费，开启报名费则将金额转为字符串，否则整个对象传空
    registFee: activityFormData.advanced.registFeeEnable ? {
      ...activityFormData.advanced.registFee,
      value: activityFormData.advanced.registFee.value.toString(),
    } : null,
  },
}));

// 原始表单数据JSON，用于对比是否修改
const originalFormData = ref({});

// 发布成功后的响应活动id
const publishSuccessId = ref(null);

// 存草稿
const saveDraft = async () => {
  saveLoading.value = true;

  // 更新草稿使用更新方法，新的草稿使用保存方法
  const saveMethod = isDraft.value ? activityUpdate : activitySaveDraft;

  // 修改非公开活动草稿调用批量设置参与人接口
  if (isDraft.value && activityFormData.basic.actorScope === 'Internal') {
    const [actorError] = await to(
      activityActorBatch({
        activityId: activityFormData.id,
        actors: activityFormData.members.actors,
      }),
    );

    if (actorError) {
      saveLoading.value = false;
      return;
    }
  }

  const [error] = await to(saveMethod(submitFormData.value));

  saveLoading.value = false;

  if (error) {
    return;
  }

  MessagePlugin.success('保存成功');

  updateCacheTeamId();

  if (isScene) {
    ipcRenderer.invoke('refresh-scence-activity');
  }

  closeTab();

  setTimeout(() => {
    router.push('/activity/activityListDraft');
  }, 0);
};

// 预览
const preview = async () => {
  const result = await liteInfoRef.value.validate();
  if (result !== true) {
    return;
  }

  // 预览数据的key，草稿数据为活动id，否则为固定key：LITE_NEW
  const previewKey = isDraft.value ? activityFormData.id : 'LITE_NEW';
  activityStore.insertActivityPreviewData(previewKey, _.cloneDeep(submitFormData.value));

  if (isScene) {
    // 场景活动跳转activityPreviewLayout
    const scenePreviewPath = isDraft.value
      ? `/layoutActivity/activityLitePreviewLayout/${activityFormData.id}`
      : '/layoutActivity/activityLitePreviewLayout';
    router.push({
      path: scenePreviewPath,
      query: {
        targetName: sceneConversationName.value,
        conversationType: sceneConversationType.value,
      },
    });
  } else {
    router.push(previewPath.value);
  }
};

// 发布
const publish = async () => {
  const result = await liteInfoRef.value.validate();
  if (result !== true) {
    return;
  }

  await startTimeConfirm();

  publishLoading.value = true;

  // 提交数据
  const [error, res] = await to(activityRelease(submitFormData.value));

  publishLoading.value = false;

  if (error) {
    return;
  }

  updateCacheTeamId();

  // 给发布成功的活动主键赋值
  publishSuccessId.value = res.data.data.id;

  // 打开发布成功弹窗
  activityPublishSuccessDialogRef.value.open();

  if (isScene) {
    ipcRenderer.invoke('refresh-scence-activity');
  }
};

// 发布成功后返回活动列表
const onBackList = async () => {
  if (isScene) {
    // 场景活动打开活动应用
    await setAccountAuthRouters('click-menu-item');
    await ipcRenderer.invoke('click-menu-item', {
      url: '/activity/activityList',
    });
    ipcRenderer.send('update-nume-index', 8);
    closeTab();
  } else {
    closeTab();
    setTimeout(() => {
      router.push('/activity/activityListCreated');
    }, 0);
  }
};

// 发布成功后跳转活动详情
const onGoDetail = () => {
  closeTab();

  const queryTeamId = isPersonal.value ? '' : activityFormData.basic.teamId;
  const queryCardId = isPersonal.value ? '' : activityFormData.basic.cardId;

  if (isScene) {
    // 场景活动通过独立弹窗打开详情
    ipcRenderer.invoke('create-dialog', {
      url: `layoutActivity/activityParticipantDetailLayout/${publishSuccessId.value}?subject=${encodeURIComponent(
        activityFormData.basic.subject,
      )}&teamId=${encodeURIComponent(queryTeamId)}&cardId=${encodeURIComponent(queryCardId)}`,
      opts: {
        x: 50,
        y: 50,
        width: 1296,
        minWidth: 1296,
        height: 720,
      },
    });
  } else {
    setTimeout(() => {
      router.push({
        path: `/activity/manage/${publishSuccessId.value}`,
        query: {
          subject: activityFormData.basic.subject,
          teamId: queryTeamId,
          cardId: queryCardId,
          mold: activityFormData.basic.mold,
        },
      });
    }, 0);
  }
};

// 活动开始时间小于当前系统时间的确认
const startTimeConfirm = () => new Promise((resolve, reject) => {
  const currentTime = moment().unix();
  if (activityFormData.basic.duration.startTime > currentTime) {
    resolve();
    return;
  }
  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: '活动开始时间小于当前系统时间，确定发布？',
    theme: 'info',
    confirmBtn: '确定',
    cancelBtn: '选择时间',
    onConfirm: async () => {
      confirmDia.destroy();
      resolve();
    },
    onCancel: () => {
      // 跳转基本信息
      currentStep.value = 'basic';
      confirmDia.destroy();
      reject();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
      reject();
    },
  });
});

const init = async () => {
  if (isScene) {
    // 如果是场景活动，查询一次组织信息（场景活动是独立窗口打开，可能活动应用中的store还没获取值）
    await activityStore.getGroupList();
  }

  // 新建场景活动，根据传入的对话类型给chatId赋值
  if (isDraft.value) {
    // 如果是编辑草稿，则获取活动详情
    const res = await getActivityDetail(route.params.id, {
      openId: getOpenid(),
      teamId: activityFormData.basic.teamId,
      cardId: activityFormData.basic.cardId,
    });

    const data = res.data.data;

    // 处理获取的数据（不处理活动成员，成员信息在活动创建后时通过接口单独查询，详情接口中不返回成员信息）
    // 没有活动归属，则默认选中个人
    data.basic.teamId = data.basic.teamId || getOpenid();
    // 处理分类id数据类型
    data.basic.categoryId = data.basic.categoryId ? data.basic.categoryId.toString() : null;
    // 处理活动地址
    data.basic.location = data.basic.location ?? {};

    // 将时间戳都转为int（报名时间如果为0，则转为null）
    data.basic.duration.startTime = Number(data.basic.duration.startTime);
    data.basic.duration.endTime = Number(data.basic.duration.endTime);

    // 活动主键赋值
    activityFormData.id = data.id;
    // 活动基本信息数据赋值
    activityFormData.basic = data.basic;
    // 活动详情数据赋值
    activityFormData.particulars = data.particulars;
    // 高级设置数据赋值
    activityFormData.advanced = data.advanced;
    // 场景活动会话信息赋值
    if (isScene) {
      activityFormData.chatId = data.chatId;
      sceneConversationType.value = data.chatId.groupId ? '3' : '1';
      sceneConversationName.value = data.chat.name;
      sceneConversationId.value = data.chatId[sceneConversationType.value === '1' ? 'cardId' : 'groupId'];
    }

    liteInfoRef.value.editorRenderContent();
    // 如果是非公开活动，通过接口查询活动参与人员
    if (activityFormData.basic.actorScope === 'Internal') {
      await liteInfoRef.value.loadActors();
    }
  } else if (isScene) {
    // 新建场景活动，根据传入的对话类型给chatId赋值
    activityFormData.chatId = sceneConversationType.value === '1' ? { cardId: sceneConversationId } : { groupId: sceneConversationId };
  }

  if (!isDraft.value && !isScene) {
    // 非草稿箱且非场景活动，则加载缓存的活动归属
    await loadCacheTeamId();
  }

  // 给原始表单数据赋值
  originalFormData.value = _.cloneDeep(activityFormData);
};

// 加载缓存中选择过的活动归属
const loadCacheTeamId = async () => {
  // 缓存活动归属没用则使用个人
  const cacheTeamId = JSON.parse(localStorage.getItem('activityFormTeamId') || '{}')[getOpenid()] || getOpenid();

  if (!cacheTeamId) {
    activityFormData.basic.teamId = getOpenid();
    return;
  }

  // 判断缓存的归属值是否还在所属组织中，如果已经退出缓存的组织，则选中个人
  try {
    await activityStore.checkIsInGroup(cacheTeamId, false, null, false, false);
    activityFormData.basic.teamId = cacheTeamId;
  } catch {
    activityFormData.basic.teamId = getOpenid();
  }
};

// 更新活动归属缓存
const updateCacheTeamId = () => {
  const newCache = {
    ...JSON.parse(localStorage.getItem('activityFormTeamId') || '{}'),
    [getOpenid()]: activityFormData.basic.teamId,
  };
  localStorage.setItem('activityFormTeamId', JSON.stringify(newCache));
};

// 打开场景活动会话
const openSceneConversation = () => {
  openChat({
    main: activityFormData.basic.cardId || getOpenid(),
    [sceneConversationType.value === '1' ? 'peer' : 'group']: sceneConversationId.value,
  });
};

// 监听页面关闭
const onDelActivityLiteCreateTab = async (path) => {
  const formFields = ['basic', 'particulars', 'process', 'advanced'];

  // 获取表单的JSON数据
  const getFormJSON = (data) => {
    const formData = formFields.reduce((acc, field) => {
      acc[field] = data[field];
      return acc;
    }, {});

    if (!isDraft.value) {
      // 如果是新建，则需要比对成员信息（草稿中的成员信息在修改时会直接调接口更新，因此不需要在这里对比）
      formData.members = data.members;
    }

    return JSON.stringify(formData);
  };

  // 关闭确认弹窗
  const showDelTabConfirm = () => {
    const confirmDia = DialogPlugin.confirm({
      header: t('activity.activity.tip'),
      theme: 'info',
      body: '是否保存已编辑的内容',
      confirmBtn: t('activity.activity.save'),
      cancelBtn: `不${t('activity.activity.save')}`,
      onConfirm: async () => {
        saveDraft();
        confirmDia.destroy();
      },
      onCancel: () => {
        confirmDia.destroy();
        closeTab(path);
      },
      onCloseBtnClick: () => {
        confirmDia.destroy();
      },
    });
  };

  const originalFormJSON = getFormJSON(originalFormData.value);
  const activityFormJSON = getFormJSON(activityFormData);

  const isModified = originalFormJSON !== activityFormJSON;

  if (isModified) {
    // 有过更改，则弹窗提示（如果当前路由不在当前tab，先定位到当前tab，再弹窗）
    if (isActive.value) {
      showDelTabConfirm();
    } else {
      await router.push(path);
      showDelTabConfirm();
    }
  } else {
    // 无更改，直接关闭
    closeTab(path);
  }
};

// 设置当前tab
const setTabItem = () => {
  if (!activityStore.tabList?.some((tab) => tab.tag === (isDraft.value ? route.path : 'activityLiteCreate'))) {
    const newTabItem = {
      path: isDraft.value ? route.path : '/activity/activityLiteCreate',
      name: route.path,
      title: isDraft.value ? t('activity.activity.editActivity') : t('activity.activity.addActivity'),
      tag: isDraft.value ? route.path : 'activityLiteCreate',
      type: 13,
    };

    emit('settabItem', newTabItem);
    emit('setActiveIndex', activityStore.tabList.length - 1);
  }
};

// 关闭当前tab
const closeTab = (path = route.path) => {
  if (isScene) {
    // 场景活动独立弹窗，则关闭弹窗
    ipcRenderer.invoke('close-dialog');
  } else {
    // 关闭对应预览页tab
    const previewIndex = activityStore.tabList.findIndex((e) => e.path === previewPath.value);
    if (previewIndex !== -1) {
      emit('deltabItem', previewIndex, true);
    }

    setTimeout(() => {
      const index = activityStore.tabList.findIndex((e) => e.path === path);
      emit('deltabItem', index, true);
    }, 0);
  }
};

watchEffect(() => {
  // 根据活动归属，设置归属下的身份卡信息
  if (activityFormData.basic.teamId) {
    // 个人身份创建活动，身份卡设置为空，组织身份创建则使用在组织中的uuid
    activityFormData.basic.cardId = isPersonal.value ? '' : selectedTeam.value.uuid;
  } else {
    // 清空活动归属，则把身份卡也清空
    activityFormData.basic.cardId = '';
  }
});

onMounted(() => {
  setTabItem();
  init();

  emitter.on('del-activity-lite-create-tab', onDelActivityLiteCreateTab);
});

onBeforeUnmount(() => {
  emitter.off('del-activity-lite-create-tab', onDelActivityLiteCreateTab);
});
</script>

<style lang="less" scoped>
.activity-lite-create-wrapper {
  background: url('https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/caadb0b/bg.png') no-repeat center / 100% 100%;

  .scroll-content {
    overflow: overlay;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      margin-right: 2px;
      border-radius: 4px;
      background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
    }

    &::-webkit-scrollbar-track {
      margin-right: 2px;
      background-color: transparent;
    }
  }

  .operate-box {
    background: #fff;
    padding: 16px 0;
    display: flex;
    justify-content: center;
    gap: 8px;

    .t-button {
      width: 88px;

      &.publish-button {
        min-width: 88px;
        width: auto;
      }
    }
  }
}
</style>
