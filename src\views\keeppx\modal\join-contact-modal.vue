<template>
  <t-dialog
    v-model:visible="visible"
    :z-index="2500"
    attach="body"
    width="384px"
    class="organizeDatas dialogSet20240801"
  >
    <template #header>
      <div class="title">组织已存在</div>
    </template>
    <template #body>
      <div class="toBody mt-24px">
        <div class="tip">
          组织【{{organizeDatas?.team_name}}】已激活，你可申请成为其联系人，代表人通过后即加入成功
        </div>
        <div class="respector mt-10">
          <span class="label">
            代表人：
          </span>
          <span class="value">
            <BaseAvatar
              class="rd-10"
              :avatar-size="'24px'"
              :image-url="organizeDatas?.avatar"
              :user-name="organizeDatas?.name"
              :shape="'circle'"
            />
            <span class="text">{{ organizeDatas?.name }}</span>
          </span>
        </div>
        <!-- <div v-if="organizeDatas && organizeDatas.length > 0" class="data">
          <div
             v-for="(orItem, orIndex) in organizeDatas"
             :key="orIndex"
             :class="{'data-item':true, 'cursor':true, active: currentActive&& currentActive.idTeam === orItem.idTeam}"
             @click = "onSetCurrentActive(orItem)">


            <BaseAvatar
                class="rd-10"
                :avatar-size="'24px'"
                :image-url="orItem.teamLogo || ORG_DEFAULT_AVATAR"
                :user-name="orItem.teamFullName"
                :shape="'circle'"
              />

            <span class="name line-1">{{ orItem.teamFullName }}</span>
            <img v-show="currentActive&& currentActive.idTeam === orItem.idTeam" style="width: 20px;height: 20px;" src="@/assets/img/radioButton_active.svg" alt="">
          </div>
        </div>
        <div v-else class="noData">
          未加入任何组织，请先创建或加入组织
        </div> -->

      </div>
    </template>
    <template #closeBtn>
      <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg>
    </template>
    <template #footer>
      <div  class="footer">
        <t-button theme="primary" class="btn" @click="onCreateOrganize">申请加入</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script lang="ts" setup>
/**
 * @description 选择组织
 * <AUTHOR>
 */
// import { debounce } from "lodash";
// import type { Ref} from "vue";
import { reactive, ref } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
// import router from "@/router";
import BaseAvatar from '@/views/square/components/BaseAvatar.vue';
// import {ORG_DEFAULT_AVATAR} from '@/constants/lss';
import { inviteContactorAxios as inviteContactorAxiosMember } from '@/api/member/member';
import { inviteContactorAxios as inviteContactorAxiosCBD } from '@/api/cbd/cbd';
import { inviteContactorAxios as inviteContactorAxiosGovernment } from '@/api/politics/politics';
import { inviteContactorAxios as inviteContactorAxiosAssociation } from '@/api/association/association';
import { inviteContactorAxios as inviteContactorAxiosUni } from '@/api/uni/uni';
import {
  // eslint-disable-next-line vue/no-dupe-keys
  originType
} from "@/views/keeppx/account/constant";

import { to } from "await-to-js";

import { useAccountStore } from '@/stores/account';
const props = defineProps({
  originType: {
    type: String,
    default: originType.Member,
  },
});


const emits = defineEmits(["onSuccess"]);


const accountStore = useAccountStore();


const objType: any = {
  onInviteFunc: () => Promise.resolve({} as any)
}
const initData = () => {
  switch (props.originType) {
    case originType.Member:
      objType.onInviteFunc = (data: any) => inviteContactorAxiosMember(data);
      break;
    case originType.Government:
      // 这里确保函数参数一致，统一使用 (data: any) 形式
      objType.onInviteFunc = (data: any) => inviteContactorAxiosGovernment(data);
      break;
    case originType.Association:
      // 这里确保函数参数一致，统一使用 (data: any) 形式
      objType.onInviteFunc = (data: any) => inviteContactorAxiosAssociation(data);
      break;
    case originType.CBD:
      // 这里确保函数参数一致，统一使用 (data: any) 形式
      objType.onInviteFunc = (data: any) => inviteContactorAxiosCBD(data);
      break;
       case originType.Uni:
      // 这里确保函数参数一致，统一使用 (data: any) 形式
      objType.onInviteFunc = (data: any) => inviteContactorAxiosUni(data);
      break;
      
    default:
      // 可以添加一些默认处理逻辑，例如抛出警告
      console.warn(`Unsupported originType: ${props.originType}`);
      break;
  }
}
initData();
const visible = ref(false);
const organizeDatas = ref(null);

/**
 *
 * @param data 值不为空说明为编辑状态
 */
const onOpen = (data: any) => {
  organizeDatas.value = data;

  console.log(data);
  // originData = origin;
  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

const onCreateOrganize = async ()=> {
  // router.push({
  //   name: 'createOrg',
  //   query: {
  //     redirect: encodeURIComponent(router.currentRoute.value.fullPath)
  //   }
  // })
  const [err, res] = await to(objType.onInviteFunc({
    main_body_id: organizeDatas.value?.main_body_id,
    phone: accountStore.userInfo?.account_mobile,
  }));

  if (err) {
    MessagePlugin.error(err.message);
    onClose();
    return;
  }
  MessagePlugin.success('申请成功');
  onClose();
  emits('onSuccess', true);
}


defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
@import url('@/views/keeppx/member/modals/select-organize.less');
.title {
  color: var(--text-kyy_color_text_1, #1A2139);
  font-size: 16px;
  font-weight: 600;
  line-height: 24px; /* 150% */
}
.respector {
  display: flex;
  flex-direction: column;
  padding: 12px 16px;
  border-radius: 8px;
  gap: 10px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  .value {
    display: flex;
    align-items: center;
    gap: 8px;
    .text {
      color: var(--text-kyy_color_text_1, #1A2139);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
  }
}


</style>
