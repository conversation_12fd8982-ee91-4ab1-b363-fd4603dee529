import {
  im_syncRequest,
  client_orgRequest,
  iam_srvRequest,
  ringkolRequest,
  ringkolRequestApi
} from "@/utils/apiRequest";
import { cardId } from "../model/card";

// 关联商协会列表
export function relatedBusiness(params) {
  return client_orgRequest({
    method: "get",
    url: "/addressbook/identity/related-business",
    params
  });
}

// 批量更新关联商协会排序
export function setRelatedBusinessSort(data) {
  return client_orgRequest({
    method: "get",
    url: "/addressbook/identity/related-business/sort",
    data
  });
}
// 更新关联商协会可见
export function setBusinessDisplay(data) {
  return client_orgRequest({
    method: "post",
    url: "/addressbook/identity/related-business/display",
    data
  });
}

// 获取内部成员会员信息
export function getInnerMemberCard(id: number | String) {
  return client_orgRequest({
    method: "get",
    url: `/addressbook/identity/related-business/${id}`
  });
}


// 获取内部成员城市会员信息
export function getInnerQovernmentCard(params) {
  return client_orgRequest({
    method: "get",
    url: `/addressbook/identity/related-government`,
    params
  });
}
// 获取内部成员社群会员信息
export function getInnerAssociationCard(params) {
  return client_orgRequest({
    method: "get",
    url: `/addressbook/identity/related-association`,
    params
  });
}

// 获取平台成员租户信息
export function getInnerTenantCard(params) {
  return client_orgRequest({
    method: "get",
    url: `/addressbook/identity/related-cbd`,
    params
  });
}


export function getProfilesCard(id: number | String) {
  return iam_srvRequest({
    method: "get",
    url: `/v1/profiles/${id}`
  });
}

export function getExternalCard(id: number | String) {
  return client_orgRequest({
    method: "get",
    url: `/external/idCard/${id}`
  });
}

export function getExternalCardNew(params) {
  return client_orgRequest({
    method: "get",
    url: `/addressbook/identity/card`,
    params
  });
}

export function setExternalCard(id: number | string, data) {
  return client_orgRequest({
    method: "patch",
    url: `/external/idCard/${id}`,
    data
  });
}

export function getInternalCard(params) {
  return client_orgRequest({
    method: "get",
    url: "/addressbook/identity/organize",
    params
  });
}

export function getPlatformCard(params) {
  return client_orgRequest({
    method: "get",
    url: "/addressbook/identity/platform",
    params
  });
}

export function getCardRemark(params: cardId) {
  return ringkolRequest.get("/im/v1/cardRemark/getCardRemark", {params:{ cardId: params.cardId }});
}

export function setCardRemark(params: any) {
  return ringkolRequest.post("/im/v1/cardRemark/updateCardRemark", params);
}

export function setProfilesCard(data) {
  return iam_srvRequest({
    method: "put",
    url: `/v1/profiles/me`,
    data
  });
}

export function setInnerCard(data) {
  return client_orgRequest({
    method: "patch",
    url: `/addressbook/identity/options`,
    data
  });
}

export function checkRepeatLinkId(data) {
  return iam_srvRequest({
    method: "post",
    url: `/v1/accounts/check`,
    data
  });
}

export function setLinkId(data) {
  return iam_srvRequest({
    method: "put",
    url: `/v1/link`,
    data
  });
}

export function checkLinkId() {
  return iam_srvRequest({
    method: "get",
    url: `/v1/link/check`
  });
}

export function addRelatedOrganize(data) {
  return client_orgRequest({
    method: "post",
    url: "/addressbook/identity/related-organize",
    data
  });
}

export function setRelatedOrganizeDisplay(data) {
  return client_orgRequest({
    method: "post",
    url: "/addressbook/identity/related-organize/display",
    data
  });
}

export function setRelatedOrganizeSort(data) {
  return client_orgRequest({
    method: "post",
    url: "/addressbook/identity/related-organize/sort",
    data
  });
}

export function setRelatedOrg(organizeId: number | String, data) {
  return client_orgRequest({
    method: "put",
    url: `/addressbook/identity/related-organize/${organizeId}`,
    data
  });
}

export function deleteRelatedOrg(organizeId: number | String) {
  return client_orgRequest({
    method: "delete",
    url: `/addressbook/identity/related-organize/${organizeId}`
  });
}

export function relatedOrganize(params) {
  return client_orgRequest({
    method: "get",
    url: "/addressbook/identity/related-organize",
    params
  });
}

export function projectList(params: { idStaff: number }, teamId: string) {
  return client_orgRequest({
    method: "get",
    url: "/project/staff-data",
    params,
    headers: {
      teamId
    }
  });
}

export function getRelatedOrg(organizeId: number | String) {
  return client_orgRequest({
    method: "get",
    url: `/addressbook/identity/related-organize/${organizeId}`
  });
}

export function follow(data: {selfCardId: string, followCardId: string}) {
  return ringkolRequestApi({
    method: "post",
    url: `/im/v1/friend/follow/addFriendFollow`,
    data
  });
}

export function delFollow(data: {selfCardId: string, followCardId: string}) {
  return ringkolRequestApi({
    method: "post",
    url: `/im/v1/friend/follow/deleteFriendFollow`,
    data
  });
}

export function applyContact(data) {
  return ringkolRequestApi({
    method: "post",
    url: `/im/v1/friend/apply/applyFriend`,
    data
  });
}

export function getApplyDetail(data) {
  return client_orgRequest({
    method: "post",
    url: `/addressbook/contacts/apply-detail`,
    data
  });
}

export function getRecentApplyDetail(data) {
  return client_orgRequest({
    method: "post",
    url: `/addressbook/contacts/recent-apply-info`,
    data
  });
}

export function delApply(data, teamId) {
  return client_orgRequest({
    method: "post",
    url: `/addressbook/business-relation/apply`,
    data,
    headers: {
      teamId
    }
  });
}

export function delMsg(main, peer, history) {
  return ringkolRequestApi({
    method: "post",
    url: `/im/v1/friend/deleteFriend`,
    data:{cardIdSelf:main,cardIdFriend:peer,keepHistory:history}
  });
}

export function getAccountsDetail(openid) {
  return iam_srvRequest({
    method: "get",
    url: `/v1/accounts/detail/${openid}`,
  });
}

// 获取广场号是否显示
export const getSquareNumber = (params) => client_orgRequest.get('/addressbook/identity/squareNumberDetail', { params });

// 设置广场号是否显示
export const setSquareNumber = (params) => client_orgRequest.get('/addressbook/identity/setSquareNumber', { params });

// 设置身份卡字段可见性
export function setIdentityVisible(data) {
  return client_orgRequest({
    method: "patch",
    url: `/addressbook/identity/set-visible`,
    data
  });
}

// 设置身份卡个人身份显示
export function setIdentityIsCard(data) {
  return client_orgRequest({
    method: "patch",
    url: `/addressbook/identity/set-isCard`,
    data
  });
}

// 获取外部身份卡的身份卡详情
export function getIdentityCard(params) {
  return client_orgRequest({
    method: "get",
    url: `/addressbook/identity/card`,
    params
  });
}

// 谁可联系我详情
export function getContactConfig(cardId: string) {
  return im_syncRequest({
    method: "get",
    url: `/v1/contact_config/${cardId}`,
  });
}

// 谁可联系我更新
export function setContactConfig(data) {
  return im_syncRequest({
    method: "put",
    url: `/v1/contact_config`,
    data
  });
}

// 是否可联系对方
export function checkContactConfig(data: { from: string; to: string}) {
  return im_syncRequest({
    method: "post",
    url: `/v1/contact_config/search`,
    data
  });
}
// 获取openIMid
export function getOpenIMId(ids:string[]) {
  return im_syncRequest({
    method: "post",
    url: `/v1/card/info`,
    data:{ids}
  });
}


// 更新申请消息模版
export function setApplyMsg(data) {
  return im_syncRequest({
    method: "put",
    url: `/v1/apply_mess_temp`,
    data
  });
}

// 查询申请消息模版
export function getApplyMsg(cardId: string) {
  return im_syncRequest({
    method: "get",
    url: `/v1/apply_mess_temp/${cardId}`,
  });
}
