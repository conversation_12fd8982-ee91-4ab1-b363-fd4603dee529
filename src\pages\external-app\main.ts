import 'virtual:svg-icons-register';
import 'uno.css';
import '../../style/index.less';
import { createApp } from 'vue';
import App from './App.vue';
import TDesign from 'tdesign-vue-next';
import router from './router';
import { i18n } from '@i18n';
import 'tdesign-vue-next/es/style/index.css';
import '@rk/unitPark/dist/assets/style.css';
import { VueQueryPlugin } from '@tanstack/vue-query';

const app = createApp(App);
app.use(TDesign);
app.use(router);
app.use(i18n);
app.use(VueQueryPlugin);
app.mount('#app');

console.log(__APP_ENV__.VITE_WEB_APP_CONFIG_INFO);
