<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>外部应用</title>
    <script src="https://lf1-cdn-tos.bytegoofy.com/obj/iconpark/icons_27918_612.5d2d0b6f51d86a5999627488ce6a5757.js"></script>
    <link rel="stylesheet" crossorigin href="/vendor/tdesign.min.css" />
    <script type="importmap">
      {
        "imports": {
          "vue": "/vendor/vue.js",
          "vue-router": "/vendor/vue-router.js",
          "tdesign-vue-next": "/vendor/tdesign-vue-next.js",
          "tdesign-icons-vue-next": "/vendor/tdesign-icons-vue-next.js"
        }
      }
    </script>
    <script type="module" src="/vendor/vue.js"></script>
    <script type="module" src="/vendor/vue-router.js"></script>
    <script type="module" src="/vendor/tdesign-vue-next.js"></script>
    <script type="module" src="/vendor/tdesign-icons-vue-next.js"></script>
    <style>
      /* fix: mac下点x无反应 */
      .t-drawer__content-wrapper {
        z-index: 999;
        -webkit-app-region: no-drag !important;
      }

      #__web_loading__ {
        position: fixed;
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;
        justify-content: center;
        align-items: center;
        /* background-color: rgba(255, 255, 255, .7); */
        background-color: #fff;
      }

      /* HTML: <div class="loader"></div> */
      /* HTML: <div class="loader"></div> */
      .__web_loading_icon__ {
        width: 14px;
        padding: 8px;
        aspect-ratio: 1;
        border-radius: 50%;
        background: #4c5eff;
        --_m: conic-gradient(#0000 10%, #000), linear-gradient(#000 0 0) content-box;
        -webkit-mask: var(--_m);
        mask: var(--_m);
        -webkit-mask-composite: source-out;
        mask-composite: subtract;
        animation: l3 1s infinite linear;
      }

      @keyframes l3 {
        to {
          transform: rotate(1turn);
        }
      }

      .__web_loading_text__ {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        text-align: left;
      }

      img {
        -webkit-user-drag: none;
      }
    </style>
  </head>
  <body>
    <script>
      console.log('-----外部应用-----');
    </script>
    <div id="app">
      <div id="__web_loading__">
        <span class="__web_loading_icon__"></span>
        <div class="__web_loading_text__">加载中...</div>
      </div>
    </div>
    <script type="module" src="./main.ts"></script>
  </body>
</html>
