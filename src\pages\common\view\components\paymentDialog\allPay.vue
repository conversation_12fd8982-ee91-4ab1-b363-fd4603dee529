<template>
  <div class="all-pay-box">
    <!--  -->
    <t-dialog
      :visible="allPayDialog"
      :close-btn="false"
      :header="true"
      :cancel-btn="null"
      :confirm-btn="null"
      attach="body"
      :z-index="5002"
      width="480"
      :close-on-esc-keydown="false"
      :close-on-overlay-click="false"
    >
      <template #header>
        <div class="flexboxs">
          <div>{{ t('banch.jsy') }}</div>
          <img
            style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="closeAllpayFn"
          />
        </div>
      </template>
      <div class="content-box">
        <div>
          <div class="pay-label-text">{{ t('banch.zffs') }}</div>
          <div class="head-saoma-btn">
            <template v-if="isApplePay">
              <div class="saoma-btn" :class="act - saoma - btn" @click="payFlag = 3">
                <div>{{ t('payment.onlinePayment') }}</div>
                <t-tooltip :z-index="99999999999" :content="t('banch.jhdzzczlzxzf')">
                  <iconpark-icon style="font-size: 20px" name="iconhelp"></iconpark-icon>
                </t-tooltip>
              </div>
            </template>
            <template v-else>
              <div class="saoma-btn" :class="payFlag === 1 ? 'act-saoma-btn' : ''" @click="payFlag = 1">
                <div>{{ t('payment.scanCodePayment') }}</div>
                <t-tooltip :z-index="99999999999" :content="t('banch.jhdzzczlzxzf')">
                  <iconpark-icon style="font-size: 20px" name="iconhelp"></iconpark-icon>
                </t-tooltip>
              </div>
              <div
                v-if="props.rowData?.teamId"
                class="saoma-btn"
                :class="payFlag === 2 ? 'act-saoma-btn' : ''"
                @click="payFlag = 2"
              >
                <div>{{ t('order.dgzf') }}</div>
                <t-tooltip :z-index="99999999999" :content="t('banch.tgptzzzzh15ggzrndz')">
                  <iconpark-icon style="font-size: 20px" name="iconhelp"></iconpark-icon>
                </t-tooltip>
              </div>
            </template>
          </div>
        </div>
        <div>
          <div class="pay-label-text">
            {{ t('banch.fpxx') }}
            <span style="padding-left: 8px">(电子普通发票)</span>
          </div>
          <div class="head-saoma-btn" style="margin-bottom: 12px">
            <t-radio-group v-model="invoiceFlag">
              <t-radio :value="1">
                <span class="radio-text">{{ t('banch.zbkp') }}</span>
              </t-radio>
              <t-radio :value="2">
                <span class="radio-text">{{ t('banch.kfp') }}</span>
              </t-radio>
            </t-radio-group>
          </div>
        </div>
        <div v-if="invoiceFlag === 2" class="saoma-box">
          <div v-if="props.rowData.region === 'CN'" class="saoma-item">
            <!--  -->
            <span class="saoma-item-label">{{ t('banch.fplx') }} :</span>
            <span class="saoma-item-value">
              {{
                defaultInvoice
                  ? defaultInvoice.headerId
                    ? defaultInvoice.invoiceType === 0
                      ? t('banch.ptfp')
                      : t('banch.zzsfp')
                    : '--'
                  : '--'
              }}
            </span>
            <span style="color: #4d5eff; cursor: pointer" @click="editInvoiceFn(defaultInvoice)">修改</span>
          </div>
          <div
            v-if="
              defaultInvoice &&
              defaultInvoice.invoiceType === 0 &&
              defaultInvoice.headerId &&
              props.rowData.region === 'CN'
            "
            class="saoma-item"
          >
            <span class="saoma-item-label">{{ t('banch.ttlx') }} :</span>
            <span class="saoma-item-value">{{ defaultInvoice.headerType === 1 ? t('banch.gr') : t('banch.dw') }}</span>
          </div>
          <div v-if="props.rowData.region === 'MO' || (defaultInvoice && defaultInvoice.headerId)" class="saoma-item">
            <span class="saoma-item-label">{{ t('banch.fptt') }} :</span>
            <span class="saoma-item-value">{{ defaultInvoice?.title ? defaultInvoice.title : '--' }}</span>
            <span
              v-if="props.rowData.region === 'MO'"
              style="color: #4d5eff; cursor: pointer"
              @click="editInvoiceFn(defaultInvoice)"
            >
              修改
            </span>
          </div>
          <!-- <div class="saoma-item" v-if="defaultInvoice">
						<span class="saoma-item-label">mail :</span
						><span class="saoma-item-value">{{ defaultInvoice.mail ? defaultInvoice.mail : '--' }}</span>
					</div> -->
        </div>
      </div>
      <template #footer>
        <div class="flex-end">
          <div>
            <t-button
              :disabled="(defaultInvoice?.headerId && invoiceFlag) || invoiceFlag === 1 ? false : true"
              style="width: 83px"
              @click="resubmit"
            >
              {{ t('banch.ljzf') }}
            </t-button>
          </div>
        </div>
      </template>
    </t-dialog>
    <corporatePayment
      ref="corporatePaymentRef"
      v-model:corporate-payment-flag="corporatePaymentFlag"
      :row-data="rowData"
      :invoice-flag="invoiceFlag"
      :default-invoice="defaultInvoice"
      @sub-cookie-success="subCookieSuccess"
      @get-data-list="getDataList"
      @payment-callback="paymentCallback('corporate')"
    />
    <paymentDialog
      ref="paymentDialogRef"
      :invoice-flag="invoiceFlag"
      :default-invoice="defaultInvoice"
      :custom-success-dialog="props.customSuccessDialog"
      @payment-callback="paymentCallback('online')"
    />
    <selectInvoice
      ref="selectInvoiceRef"
      v-model:select-invoice-flag="selectInvoiceFlag"
      :row-data="rowData"
      :pay-info="payInfo"
      :default-invoice="defaultInvoice"
      @set-default-invoice="setDefaultInvoice"
    />
    <t-dialog
      :visible="tipFlag"
      :close-btn="false"
      :header="true"
      :cancel-btn="null"
      attach="body"
      :confirm-btn="null"
      width="480"
      :z-index="999999999"
      :close-on-esc-keydown="false"
      :close-on-overlay-click="false"
    >
      <template #header>
        <div class="flexboxs">
          <div>提示</div>
          <img
            style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="tipFlag = false"
          />
        </div>
      </template>
      <div class="content-box">
        <div class="content-box-tip">
          <img src="@/assets/img/jingao.png" />
          <span>{{ t('banch.zzwrzbnkp') }}</span>
        </div>
      </div>
      <template #footer>
        <div class="flex-end">
          <div>
            <t-button style="width: 83px" @click="subTip">{{ t('banch.qd') }}</t-button>
          </div>
        </div>
      </template>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { Utils } from '@utils';
import selectInvoice from './selectInvoice.vue';
import { invoiceList, groupInvoiceList } from '@renderer/api/myOrder/api/myInvoice';
import { getTeamCertStatus } from '@renderer/api/square/common';
import to from 'await-to-js';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { getTeams } from '@/api/contacts/api/organize';
import corporatePayment from './corporatePayment.vue';
import { ref, watch } from 'vue';
import paymentDialog from './index.vue';
import { useI18n } from 'vue-i18n';
const paymentDialogRef = ref(null);
const selectInvoiceRef = ref(null);
const tipFlag = ref(false);
const defaultInvoice = ref({});
const corporatePaymentRef = ref(null);
const corporatePaymentFlag = ref(false);
const selectInvoiceFlag = ref(false);
const invoiceFlag = ref(1);
const payFlag = ref(1);
const { t } = useI18n();
// mac商店类型
// const isMas = ref(__APP_ENV__.VITE_API_ENV === 'DEV' ? true : __APP_ENV__.VITE_APP_MAS);
// const isMas = ref(__APP_ENV__.VITE_APP_MAS);
const isMas = ref(Utils?.config?.viteConfig?.VITE_API_ENV);

console.log('isMas', isMas);
const props = defineProps({
  allPayDialog: {
    type: Boolean,
    default: false,
  },
  rowData: {
    type: Object,
    default() {
      return {
        productType: '',
      };
    },
  },
  payInfo: {
    type: Object,
    default() {
      return {
        publicPayInfo: null,
      };
    },
  },
  customSuccessDialog: {
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits(['update:allPayDialog', 'subCookieSuccess', 'paymentCallback', 'getDataList']);
const certStatus = ref(true);

const isApplePay = ref(false);
const initIsApplePay = () => {
  // 是否是待支付订单
  const isHasOrder = typeof props.rowData?.channel !== 'undefined';
  // 是不是mac应用商店的订单 订单为2 苹果应用内的订单,notUseApplePay为true，不使用苹果支付
  const isMacOrder = isHasOrder && props.rowData?.channel === 2 && !props.rowData?.notUseApplePay;
  // 如果不是待支付订单 或者 是待支付订单在mac商店类型包下的单,且当前设备是mac商店类型
  if ((!isHasOrder || isMacOrder) && isMas.value) {
    isApplePay.value = true;
  }
};

watch(
  () => props.rowData?.notUseApplePay,
  () => {
    initIsApplePay();
  },
);

watch(
  () => props.allPayDialog,
  (newvalue) => {
    if (newvalue) {
      invoiceFlag.value = 1;
      console.log('打开');
      getList();
      initIsApplePay();
      if (props.rowData.teamId) {
        getTeamStatus();
      }
    }
  },
);
const getTeamStatus = async () => {
  const [err, res] = await to(getTeamCertStatus(props.rowData?.teamId));
  if (err) return;
  certStatus.value = res.data.certifiable;
};
const subCookieSuccess = () => {
  emits('subCookieSuccess');
};
const openCookiesFn = () => {
  if (props.payInfo && props.payInfo.publicPayInfo) {
    corporatePaymentRef.value.openCookiesFn({ id: props.payInfo.publicPayInfo.id }, invoiceFlag.value);
  } else {
    corporatePaymentRef.value.openCookiesFn({ sn: props.payInfo.sn }, invoiceFlag.value);
  }
};
const subTip = () => {
  tipFlag.value = false;
  invoiceFlag.value = 1;
};
const getList = () => {
  console.log(props.rowData, 'props.rowDataprops.rowDataprops.rowData');
  const queryParams = { page: 1, pageSize: 99999, teamId: props.rowData?.teamId };
  const getInvoiceList = queryParams.teamId ? groupInvoiceList : invoiceList;
  getInvoiceList(queryParams)
    .then((res) => {
      const data = res.data.list;
      if (res.status === 200 && data.length > 0) {
        defaultInvoice.value = data.find((element) => element.default);
        defaultInvoice.value.headerId = defaultInvoice.value.id;
      }
    })
    .catch((error) => {
      MessagePlugin.error(error.data.message);
    });
};
// const getList = () => {
//   const response = null;
//   if (props.rowData?.teamId) {
//     groupInvoiceList({ page: 1, pageSize: 99999 },props.rowData?.teamId).then((res) => {
//       if (res.status === 200&&res.data.list.length>0) {
//         defaultInvoice.value = res.data.list.find((element) => element.default);
//         defaultInvoice.value.headerId = defaultInvoice.value.id;
//       }else{
//         MessagePlugin.error(res.data.message);
//       }
//     }).catch((error)=>{
//       MessagePlugin.error(error.data.message);
//     })
//   } else {
//     invoiceList({ page: 1, pageSize: 99999 }).then((res) => {
//       if (res.status === 200&&res.data.list.length>0) {
//         defaultInvoice.value = res.data.list.find((element) => element.default);
//         defaultInvoice.value.headerId = defaultInvoice.value.id;
//       }else{
//         MessagePlugin.error(res.data.message);
//       }
//     }).catch((error)=>{
//       MessagePlugin.error(error.data.message);
//     })

//   }
// };
const openWin = (param) => {
  paymentDialogRef.value.openWin(param, invoiceFlag.value);
};
const checkPayInfo = async (param) => {
  return await paymentDialogRef.value.checkPayInfo(param);
};
// const closeBtn = () => {
//   emits('update:allPayDialog', false);
//   payFlag.value = 1;
// };
const paymentCallback = (val) => {
  emits('paymentCallback', val);
};
const getDataList = (sn) => {
  emits('getDataList', sn);
};
const closeAllpayFn = () => {
  const confirmDia1 = DialogPlugin({
    header: '提示',
    theme: 'info',
    body: t('banch.ddyscqzddglzfdd'),
    closeBtn: null,
    zIndex: 9999999999999,
    confirmBtn: t('banch.qd'),
    cancelBtn: '取消',
    closeOnOverlayClick: false,
    onClose: () => {
      confirmDia1.hide();
    },
    onConfirm: async () => {
      confirmDia1.hide();
      emits('update:allPayDialog', false);
      invoiceFlag.value = 1;
      payFlag.value = 1;
    },
  });
};
let showTipApiFlag = false;
const editInvoiceFn = async () => {
  try {
    if (props.rowData.teamId) {
      if (showTipApiFlag) {
        return;
      }
      showTipApiFlag = true;
      const res = await getTeams({ teamId: props.rowData.teamId });
      showTipApiFlag = false;
      if (res.data.auth !== 1) {
        tipFlag.value = true;
        return;
      }
    }
    selectInvoiceFlag.value = true;
  } catch (error) {
    showTipApiFlag = false;
  }
};
const setDefaultInvoice = (val) => {
  console.log(val, 'valllllllllll');
  // defaultInvoice
  defaultInvoice.value = val;
};

const resubmit = async () => {
  if (payFlag.value === 1 || payFlag.value === 3) {
    // const data = {
    //   ...props.rowData,
    //   product_ids: ['12345678']
    // }
    // const isCanPay = await checkPayInfo(data);
    // if (isCanPay) {
    //   // 可以进行支付流程
    // }

    // console.log('resubmit', payFlag.value, data);
    // console.log('isCanPay', isCanPay)
    await paymentDialogRef.value.openWin(props.rowData);
  } else {
    if (payFlag.value === 1) {
      paymentDialogRef.value.openWin(props.rowData);
    } else {
      corporatePaymentFlag.value = true;
    }
  }
  emits('update:allPayDialog', false);
  payFlag.value = 1;
};

defineExpose({
  openCookiesFn,
  checkPayInfo,
  openWin,
});
</script>

<style lang="less" scoped>
.saoma-btn {
  display: flex;
  height: 32px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  width: 115px;
  border-radius: var(--radius-kyy_radius_button_s, 4px);
  border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #d5dbe4);
  background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
  cursor: pointer;
  color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  gap: 4px;
}
.pay-label-text {
  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
  span {
    color: var(--text-kyy_color_text_3, #828da5);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.act-saoma-btn {
  border: 1px solid var(--brand-kyy_color_brand_acitve, #3e4cd1);
}
.head-saoma-btn {
  display: flex;
  gap: 16px;
  margin-top: 12px;
  margin-bottom: 24px;
}
.flexboxs {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}
.content-box-tip {
  color: var(--text-kyy_color_text_2, #516082);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  display: flex;
  align-items: center;
  flex-direction: column;
  img {
    width: 48px;
    height: 48px;
    margin-bottom: 24px;
  }
}
.saoma-box {
  display: flex;
  padding: 12px;
  width: 471px;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--select-kyy_radius_select_option, 8px);
  align-self: stretch;
  border-radius: var(--select-kyy_radius_select_option, 8px);
  background: #f5f8fe;
}
.saoma-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.saoma-item-value {
  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.saoma-item-label {
  width: 72px;
  color: var(--text-kyy_color_text_3, #828da5);
  text-align: right;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  margin-right: 4px;
  line-height: 22px; /* 157.143% */
}
.all-pay-box {
  .flexboxs {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
  }
  .radio-text {
    color: var(--radioBueeon-kyy_color_radioButton_text_active, #1a2139);
    font-size: 14px;
    margin-right: 8px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .content-item {
    margin-bottom: 16px;
    margin-top: 8px;
  }
  .radio-item {
    color: var(--text-kyy_color_text_3, #828da5);
    font-size: 14px;
    font-style: normal;
    margin-right: 8px;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
</style>
