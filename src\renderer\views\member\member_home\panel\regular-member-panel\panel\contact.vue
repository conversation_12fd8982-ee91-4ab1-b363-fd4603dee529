<template>
  <div class="container">
    <div class="header">
      <!-- 正式会员 -->
      <div class="h">
        <iconpark-icon
          name="iconarrowlift"
          style="font-size: 20px"
          @click="onGoBack"
        ></iconpark-icon>
        <span class="text" @click="onGoBack">代表人管理</span>
      </div>
    </div>
    <div class="body">
      <div class="body-content">
        <div class="btn-gs">
          <div class="cname">{{ teamName }}</div>
          <div class="btns">
            <!--            <t-button theme="primary" variant="base" @click="onAddMember">-->
            <!--              <template #icon><add-icon /></template>添加组织成员-->
            <!--            </t-button>-->
                      <!-- 需求暂时隐藏不要删除 -->
            <!-- <t-button
              style="margin-left: 8px"
              theme="primary"
              variant="base"
              @click="onAddContact"
            >
              添加联系人
              <template #icon><add-icon /></template>
            </t-button> -->
          </div>
        </div>

        <div class="table">
          <t-table
            cell-empty-content="--"
            row-key="id"
            :columns="memberColumns"
            :data="memberData"
          >
            <template #empty>
              <div style="margin-top: 84px;">
                <Empty />
              </div>
            </template>
            <template #name="{ row }">
              <div style="display: flex;align-items: center">
                <kyyAvatar
                  data-id="isclick"
                  style="
                  border-radius: 4px;
                  margin-right: 4px;
                  display: inline-flex;
                "
                  :image-url="row.avatar"
                  avatar-size="32px"
                  :user-name="row.name"
                  :shape="'circle'"
                />
                <span class="line-1 max-w-98">{{
                  $filters.isPeriodEmpty(row.name)
                }}</span>
              </div>
            </template>
            <template #telephone="{ row }">
              <div class="main_body">
                +{{ $filters.isPeriodEmpty(row.telcode) }}&nbsp;{{
                  $filters.isPeriodEmpty(row.telephone)
                }}
              </div>
            </template>
            <!-- <template #status="{ row }">
                {{ filterStatusText(row.status) }}
              </template> -->

            <!-- <template #status="{ row }">
              <div :class="showClassStatus(row.status)">
                {{ filterStatusText(row.status) }}
              </div>
            </template> -->

            <!-- <template #operate="{ row }">
              <span class="operates">
                <t-link
                  theme="primary"
                  hover="color"
                  class="operates-item"
                  @click="onEditContact(row)"
                >
                  {{ $t("member.edit") }}
                </t-link>
                <t-link
                  style="color: #d54941; margin-left: 16px"
                  hover="color"
                  class="operates-item"
                  @click="onDeleteContact(row)"
                >
                  {{ $t("member.delete") }}
                </t-link>
              </span>
            </template> -->
            <template #photo="{row}">
              <t-image class="logo cursor" :src="row?.photo" @click="preview(row)">
                <template #loading>
                  <img class="logo" src="@renderer/assets/member/svg/avatar_default.svg"/>
                </template>
                <template #error>
                  <img class="logo" src="@renderer/assets/member/svg/avatar_default.svg"/>
                </template>
              </t-image>
            </template>
            <template #operate="{ row }">
              <span v-if="row.status === 1" class="operates">
                <!-- <t-link
                  theme="primary"
                  hover="color"
                  class="operates-item"
                  @click="onEditContact(row)"
                >
                  {{ $t("member.edit") }}1111111
                </t-link> -->
                <template v-if="!row.is_representative">
                  <t-link
                    style="color: #d54941; margin-left: 16px"
                    hover="color"
                    class="operates-item"
                    @click="onDeleteContact(row)"
                  >
                    {{ $t("member.delete") }}
                  </t-link>
                </template>
                <template v-else>
                  <t-link
                    style="color: #4D5EFF;"
                    hover="color"
                    class="operates-item"
                    @click="onChangeMoveRespector(row)"
                  >
                    转移负责人
                  </t-link>
                </template>
              </span>
              <div v-else-if="row.status === 5" class="refuse">
                <div class="org-tag">待审核</div>
              </div>
              <div v-else-if="row.status === 2" class="refuse">
                <div class="org-tag">待审核</div>
              </div>
              <div v-else class="refuse">
                <div v-if="row.status === 0 "  class="org-tag">待成员处理</div>
                <div v-else-if="row.status === 6 "  class="org-tag">已拒绝</div>
                <div v-else class="org-tag"> 成员已拒绝</div>
                <t-link
                  style="color: #d54941;"
                  hover="color"
                  class="operates-item"
                  v-show="!row.is_representative"
                  @click="onDeleteContactRefuse(row)"
                >
                  {{ $t("member.delete") }}
                </t-link>
              </div>
            </template>
          </t-table>
        </div>
      </div>
    </div>
  </div>

  <!-- <SelectMemberModal
    ref="selectMemberModalRef"
    :options="optionsMembers"
    :header="$t('member.sadim.s_5')"
    :is-only="true"
    @sub-form="onListenMembers"
  /> -->
  <SelectContactorModal ref="selectContactorModalRef" :originType="originType.Member" :options="optionsMembers"  @sub-form="onListenMembers"/>


  <AddContactModal
    ref="addContactModalRef"
    :member-id="props.currentRow ? props.currentRow.id : 0"
    :is-member="0"
    @reload="onSearch"
  />
  <AddContactOrganizeMemberModal
    ref="addContactOrganizeModalRef"
    :member-id="props.currentRow ? props.currentRow.id : 0"
    :is-member="0"
    :staff="1"
    @reload="onSearch"
  />
  <AddOrganizeMemberModal
    ref="addOrganizeMemberModalRef"
    :member-id="props.currentRow ? props.currentRow.id : 0"
    :is-member="0"
    @reload="onSearch"
  />
</template>

<script setup lang="ts">
import { reactive, ref, toRaw, watch, computed } from "vue";
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { MessagePlugin, DialogPlugin } from "tdesign-vue-next";
import AddContactModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/add-contact-modal.vue";
import AddOrganizeMemberModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/add-organize-member-modal.vue";
import AddContactOrganizeMemberModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/add-contact-organize-modal.vue";
import { getResponseResult } from "@renderer/utils/myUtils";
import { AddIcon } from "tdesign-icons-vue-next";
import { useRoute } from "vue-router";
import {
  delContactAxios,
  getMemberContactListAxios,
  // getAppStaffAxios,
  onTransferByAdminAxios,
  onGetContactListSelectOneAxios,

} from "@renderer/api/member/api/businessApi";
import { useMemberStore } from "@renderer/views/member/store/member";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
import Empty from "@/components/common/Empty.vue";

// import SelectMemberModal from "@renderer/views/member/member_home/panel/membership-setting-panel/modal/select-member-modal.vue";
import SelectContactorModal from '@renderer/views/digital-platform/modal/select-contactor-modal.vue';
import { originType }  from "@renderer/views/digital-platform/utils/constant"
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;


const store = useMemberStore();
const emits = defineEmits(["onSetCurrentPanel", "onSetCurrentRow"]);
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();

const props = defineProps({
  currentRow: {
    type: Object,
    default: () => null,
  },
  platform: {
    type: String,
    default: ""
  }
});

// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       // updateAllCount();
//       onSearch();
//     }
//   },
//   {
//     deep: true
//     // immediate: true
//   }
// );

const memberColumns = ref([]);

const initColumns = () => {
  memberColumns.value = [
    // {
    //   colKey: "row-select",
    //   type: "multiple",
    //   width: "4%"
    // },
    { colKey: "name", title: "姓名", width: "23%" },
    // { colKey: "no", title: "代表人", width: "15%" },
    { colKey: "photo", title: "照片", width: "80px", ellipsis: false },
    {
      colKey: "job",
      title: "所在单位岗位",
      width: "12%",
      ellipsis: false,
      align: "left",
    },
    { colKey: "telephone", title: "手机号码", width: "180px", ellipsis: false },
    {
      colKey: "email",
      title: "邮箱",
      width: "16%",
      ellipsis: false,
    },
    { colKey: "operate", title: "操作", width: "16%" },
  ];
};
initColumns();
const addContactModalRef = ref(null);
const addContactOrganizeModalRef = ref(null);
const memberData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;

    // getMemberList({});
    onSearch();
  },
});
const onSearch = () => {
  const params = {};
  getMemberList(params);
};
const teamName = ref(null);
// 获取列表
const getMemberList = async (params) => {
  console.log('getMemberList', props.currentRow);
  teamName.value = props.currentRow.team_name;
  // teamId
  // params.page = pagination.current;
  // params.pageSize = pagination.pageSize;
  params.member_id = props.currentRow ? props.currentRow.id : 0;
  params.relate_teamId = props.currentRow?.teamId;
  try {
    let result = await getMemberContactListAxios(params, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    memberData.value = result.data;

    // pagination.total = result.data.total;
    console.log(memberData.value);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};
const platformCpt = computed(() => props.platform || route.query?.platform);

const activeAccount = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query;
  }
    return store.activeAccount;

});


const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query?.teamId || 0;
  }
    return getMemberTeamID();

});
onSearch();


// 转移代表人
const onListenMembers = async (arr) => {
  console.log(arr);
  if (arr && arr.length < 1) return;
  let result = null;
  console.log(result);
  try {
    result = await onTransferByAdminAxios({ main_body_id: props.currentRow?.id, contact_id: arr[0]}, currentTeamId.value);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success('操作成功');
    getMemberList({});
    // selectMemberModalRef.value.onClose();
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};


const selectContactorModalRef = ref(null);
const optionsMembers = ref([]);
const getAppMemberList = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await onGetContactListSelectOneAxios({
        main_body_id: props.currentRow?.id,
        status: 1, // 正常
      }, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      optionsMembers.value = result?.data;
      resolve("success");
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};

const onChangeMoveRespector = (row) => {
  getAppMemberList().then(() => {
    selectContactorModalRef.value.onOpen();
  });
};



// const onAddContact = () => {
//   console.log("添加联系人");

//   addContactOrganizeModalRef.value.onOpen();
// };

const addOrganizeMemberModalRef = ref(null);
const onAddMember = () => {
  addOrganizeMemberModalRef.value.onOpen();
};

const onEditContact = (row) => {
  addContactModalRef.value.onOpen(row);
};
const onDeleteContact = (row) => {
  const confirmDia = DialogPlugin({
    header: "确定删除代表人",
    theme: "info",
    body: "删除后该代表人将无法管理该会员信息，并不再接收会员相关通知",
    closeBtn: null,
    confirmBtn: "确定删除",
    className: "delmode",
    onConfirm: async () => {
      // 删除字段操作
      onDelContactorAxios(row).then(() => {
        confirmDia.hide();
        onSearch();
      });
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};
const preview = (row) => {
  // const imgs = attrs.value.value.map((item) => item.file_name);
  // imageFiles.value = imgs;
  // viewer.value = true;
  if(!row.photo) return;

  const temp =  [
    {
      url: row.photo,
      // imgIndex: i,
    }
  ]
  ipcRenderer.invoke("view-img", JSON.stringify(temp));

};
const onDeleteContactRefuse = (row) => {
  const confirmDia = DialogPlugin({
    header: "提示",
    theme: "info",
    body: "成员还未加入，确定删除该条记录吗？删除后，之前发送地短信链接会失效，需重新添加该成员。",
    closeBtn: null,
    confirmBtn: "确定删除",
    className: "delmode",
    onConfirm: async () => {
      // 删除字段操作
      onDelContactorAxios(row).then(() => {
        confirmDia.hide();
        onSearch();
      });
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};
const onDelContactorAxios = (val) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await delContactAxios(val.id, { is_member: 0 }, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) return;
      MessagePlugin.success("删除成功");
      resolve(result);
      // rejectModalRef.value.onClose();
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const onGoBack = () => {
  emits("onSetCurrentPanel", "PRegular");
};
</script>

<style lang="less" scoped>
// @import "@renderer/views/member/member_home/panel/public.less";
.logo {
  width:32px;
  height: 44px;
}
.header {
  display: flex;
  justify-content: space-between;
  background-color: #f5f8fe;
  padding: 16px 16px 0px 16px;
  height: 68px;
  &-title {
    display: flex;
    align-items: center;
    gap: 20px;
    .link {
      color: blue;
    }
  }
  .h {
    display: flex;
    align-items: center;
    width: 100%;
    height: 40px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--divider-kyy_color_divider_deep, #D5DBE4);
    cursor: pointer;
    .text {
      color: var(--text-kyy-color-text-1, #1a2139);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      margin-left: 8px;
    }
  }
}

.body {
  overflow-y: auto;
  height: calc(100vh - 108px);
  background: #f5f8fe;
  padding: 0 16px;
  &-content {
    background-color: #fff;
    height: fit-content;
    min-height: 100%;
    padding: 16px;
    .table {
      padding: 0px;
    }
  }
}
.btn-gs {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  .cname {
    display: flex;
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 12px;
    align-items: center;
    border-radius: var(--input-kyy-radius-input, 4px);
    background: var(--input-kyy-color-input-bg-disabled, #eceff5);
    overflow: hidden;
    color: var(--text-kyy-color-text-1, #1a2139);
    text-overflow: ellipsis;

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

.refuse{
  display: flex;
  align-items: center;
  gap: 16px;
  .org-tag{
    display: flex;
height: 20px;
min-height: 20px;
max-height: 20px;
padding: 1px 8px;
justify-content: center;
align-items: center;
gap: 10px;
color: var(--kyy_color_tag_text_warning, #FC7C14);
text-align: right;
font-family: "PingFang SC";
font-size: 12px;
font-style: normal;
font-weight: 400;
line-height: 20px; /* 166.667% */
border-radius: var(--kyy_radius_tag_full, 999px);
background: var(--kyy_color_tag_bg_warning, #FFE5D1);
  }
}
</style>
