/**
 * 页签项
 */
export interface TabItem {
  path_uuid?: string;
  value?: string;
  // 标签名称
  label: string;
  // 标签图标
  icon?: string;
  // 完整路径
  fullPath: string;
  // 是否显示未读数
  badgeCount?: number;
  // 标签是否可关闭
  closable?: boolean;
  beforeCloseOptions?: {
    title?: string,
    content?: string,
  };
  // 是否缓存
  keepAlive?: boolean;
}

/**
 * 组织/个人账号列表项
 */
export interface AccountItem {
  // 账号id
  id: string;
  // 仅组织账号有 teamId
  teamId?: string;
  // 账号名
  name: string;
  // 账号头像
  avatar?: string;
  // 是否显示未读数
  dotCount?: number;
  // 未读数是否只以红点形式显示
  dot?: boolean;
  // 额外数据
  extra?: Record<string, any>;
}
