import { client_orgRequest, ringkolRequest as request } from '@renderer/utils/apiRequest';

// 获取个人身份加入的数字平台列表
export function platformGetListByOpenid() {
  return client_orgRequest({
    method: "get",
    url: '/platform/get-list-by-openid',
  });
}

// 查询活动列表(管理端、用户端通用)
export function queryActivityPromotion(params, { hideMessage = false } = {}) {
  return request({
    method: "get",
    url: '/campaign/v2/digitalPlatform/queryActivityPromotion',
    params,
    hideMessage,
  });
}

// 查询活动列表(管理端、用户端通用)
export function listDPActivities(params) {
  return request({
    method: "get",
    url: '/campaign/v2/digitalPlatform/listManageActivities',
    params
  });
}

// 查询数字平台展示的活动列表(用户端)
export function listDPActivitiesForUser(params) {
  return request({
    method: "get",
    url: '/campaign/v2/digitalPlatform/ListActivities',
    params
  });
}

// 置顶
export function stickDPActivity(data) {
  return request({
    method: "post",
    url: '/campaign/v2/digitalPlatform/stick',
    data
  });
}

// 取消置顶
export function unStickDPActivity(data) {
  return request({
    method: "post",
    url: '/campaign/v2/digitalPlatform/unStick',
    data
  });
}

// 删除
export function deleteDPActivity(data) {
  return request({
    method: "post",
    url: '/campaign/v2/digitalPlatform/delete',
    data
  });
}

// 统计待审核活动数
export function dPCountPendingReview(params) {
  return request({
    method: "get",
    url: '/campaign/v2/digitalPlatform/countPendingReview',
    params
  });
}

// 审核同意
export function agreeDPActivity(data) {
  return request({
    method: "post",
    url: '/campaign/v2/digitalPlatform/agree',
    data
  });
}

// 审核拒绝
export function rejectDPActivity(data) {
  return request({
    method: "post",
    url: '/campaign/v2/digitalPlatform/reject',
    data
  });
}
