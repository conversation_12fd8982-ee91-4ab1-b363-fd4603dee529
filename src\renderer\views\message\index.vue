<template>
  <div class="chat-root" ref="bodyRef">
    <icon class="icon-specials" :url="iconUrl" name="iconSpecial-graphics" />
      <div class="session-list">
        <!-- <t-skeleton v-if="!sessionList.length" loading animation="gradient" :rowCol="[1, 1, 1]" class="pseudo"></t-skeleton> -->
        <conversations />
      </div>

    <div class="message-root">
      <div v-if="sessionListActive.length === 0 && commonSessionList.length === 0 " class="empty">
        <template v-if="activeGroupType === 'all'">
          <Empty class="empty-cpn" name="chat-init"></Empty>
          <div class="tip-offset">{{t("im.public.welcome")}}!</div>
          <div class="tip-offset">{{t("im.public.welcomeTips")}}!</div>
        </template>
        <Empty class="empty-cpn" v-else/>

          <template v-if="activeGroupType === 'friend'">
            <div class="tip-offset">{{t("im.public.noFriends")}}</div>
            <div class="tip-offset">
              <t-button theme="primary" class="mt-12" @click="handleAddFriend">{{t("im.public.addFriend")}}</t-button>
            </div>
          </template>
          <template v-else-if="activeGroupType === 'internal'">
            <div class="tip-offset">{{t("im.public.noInternalOrg")}}</div>
            <div class="tip-offset">
              <t-button theme="primary" variant="outline" class="mt-12 mr-8" @click="handleJoinOrganization">{{t("im.public.joinOrg")}}</t-button>
              <t-button theme="primary" class="mt-12" @click="handleCreateOrganization">{{t("im.public.createOrg")}}</t-button>
            </div>
          </template>
          <template v-else-if="activeGroupType === 'external'">
            <div class="tip-offset">{{t("im.public.joinOrgTip")}}</div>
            <div class="tip-offset">
              <t-button theme="primary" class="mt-12" @click="handleJoinOrganization">{{t("im.public.joinOrg")}}</t-button>
            </div>
          </template>
          <template v-else-if="activeGroupType === 'community'">
            <div class="tip-offset">{{t("im.public.noDigitalPlatform")}}</div>
            <div class="tip-offset">
              <t-button theme="primary" class="mt-12" @click="handleJoinPlatform">{{t("im.public.joinPlatform")}}</t-button>
            </div>
          </template>
      </div>
      <div v-else-if="!chatingSession" class="empty">
        <template v-if="commonChatActive && commonSessionList.length === 0">
          <Empty class="empty-cpn" ></Empty>
          <div class="tip-offset">{{t("im.public.common_session_emty")}}!</div>
        </template>
        <template v-else>
          <Empty class="empty-cpn" name="chat-init"></Empty>
          <div class="tip-offset">{{t("im.public.welcome")}}!</div>
          <div class="tip-offset">{{t("im.public.welcomeTips")}}!</div>
        </template>
      </div>
      <!-- 稍后处理助手 -->
      <!-- <div v-else-if="chatingSession?.targetId === `assistant8app8pending`"> -->
      <template v-else-if="isAssistant8app8pending">
        <deal-with-later />
      </template>
      <chat-view v-else-if="chatingSession" />
    </div>
    <!-- <template v-if="!isAssistant8app8pending"> -->
      <Tools />
   <drawer ref="drawerRef" :attach="() => bodyRef" />
   <DialogIframe v-if="useChatActionStore().isDialogIframe"/>
    </div>
</template>

<script lang="ts">
export default {
  name: 'MessageRoot',
  inheritAttrs: false,
  customOptions: {}
}
</script>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import Conversations from './conversation/index.vue';
import ChatView from './chat/index.vue';
import DealWithLater from './chat/DealWithLater.vue';
import Tools from './tool/Tool.vue';
import { iconUrl } from "@renderer/plugins/KyyComponents";
import { Icon } from 'tdesign-icons-vue-next';
// import Guide from './guide/index.vue';
import Empty from "@/components/common/Empty.vue";
import { cfgCommon } from "@renderer/utils/auth";
import Drawer from '@renderer/_jssdk/components/drawer/index.vue';
import DialogIframe from './components/DailogIframe.vue';
import { imInit } from './service/rkimSetup';
import { imConnect } from './service/rkimConnect';
import { useMessageStore } from './service/store';
import { useChatActionStore } from './service/actionStore';
import { changeMoreAccountInfo } from "@/utils/auth";
import { MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import to from 'await-to-js';
import { byOpenIdGetStaffsPlatformAxios } from '@renderer/api/digital-platform/api/businessApi';
import {openChat} from "@/utils/share";

import LynkerSDK, { init } from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;
const bodyRef = ref(null);
const drawerRef = ref(null);
const { t } = useI18n();
const msgStore = useMessageStore();
const { sessionList, chatingSession, activeGroupType, commonChatActive, sessionListActive, commonSessionList } = storeToRefs(msgStore);

// 是否是稍后处理助手
const isAssistant8app8pending = computed(() => {
  // return true;
  return chatingSession.value?.targetId === `assistant8app8pending`;
});

// 添加好友
const handleAddFriend = () => {
  ipcRenderer.invoke('set-popbv', {
    show: true,
    type: 'dialogContacts',
  });
}

// 加入组织
const handleJoinOrganization = () => {
  ipcRenderer.invoke('set-popbv', {
    show: true,
    type: 'dialogOrg',
    data: { fnName: 'joinOrg' }
  });
}

// 创建组织
const handleCreateOrganization = () => {
  ipcRenderer.invoke('set-popbv', {
    show: true,
    type: 'dialogOrg',
    data: { fnName: 'createOrg' }
  });
}

// 加入数字平台
const handleJoinPlatform = () => {
  ipcRenderer.invoke('click-menu-item', {
    url: '/digitalPlatformIndex/digital_platform_home',
    query: {
      from: 'message',
      uuid: 'member',
      name: '数字平台',
      jumpPath: '/digitalPlatformIndex/digital_platform_home'
    }
  }).then((res) => {
    if (res) {
      ipcRenderer.send('update-nume-index', 'digital_platform');
    }
  });
}

// 家庭群引导
// const guide = ref()
// const getGuideStatus = async () => {
//   //引导视频区分家庭群和场景社交
//  let guideType:'FamilyGroup' | 'SceneSocial' = msgStore.chatingSession?.relation === '15' ? 'FamilyGroup' : 'SceneSocial'
//     const { data } = await getGuideStatusApi(guideType)
//     if (data && data?.guides?.length) {
//       //根据状态status:false 未完成来主动展示引导
//         guide.value = data.guides.find(item => !item.status && item.url && item.typ === guideType) ?? null;
//         // guide.value = data.guides.find(item =>  item.url && item.typ === 'FamilyGroup') ?? null;
//         if (guide.value) {
//             hideDialog();
//         }
//     }
// }

// 获取专属名称map数据
const onGetByOpenIdGetStaffsPlatformAxios =  () => {
  return new Promise(async(resolve, reject)=> {
    const [err, res] = await to(byOpenIdGetStaffsPlatformAxios({}));
    if(err) {
      console.error('专属名称map报错：', err?.message)
      reject(err?.message)
      return;
    }
    const { data } = res?.data;
    console.log('获取专属名称map数据', data)
    resolve(data)
  })
}


onMounted(async () => {
  await init();
  ipcListeners()
  cfgCommon();

  const  [errP, resP]: any = await to(onGetByOpenIdGetStaffsPlatformAxios());
  if(resP && resP?.length > 0) {
    // 转换为键值对数组 [key, value]
    const entries = resP.map(item => [item.team_id, item.exclusive_name]);
    msgStore.exclusiveNames = new Map(entries as Array<[string, string]>);
    console.log('msgStorekakxi',  msgStore.exclusiveNames)
  }

  // getGuideStatus();
    // 登录初始化网路异常，资源会加载失败打回登录页面
    const start =  Date.now()
    console.log('====>用时messageOnmounted', start);
    if (!navigator.onLine) {
      ipcRenderer.invoke('init-window');
      return;
    }
    // 获取分组配置
    msgStore.getPairGroup();
    // im初始化
    const initRes = await imInit();
    console.log('数据库初始化: ', !initRes.code ? '成功' : '失败');
    if (initRes.code === 0) {
      let firstInit = false;
      if (!sessionList.value.length) {
        // 加载本地数据库历史会话列表
        await msgStore.loadSessionList();
        console.log('====>页面onMounted到加载本地数据库', `[${Math.floor(Date.now() - start)}ms]`);
        firstInit = true;
        console.log('====>用时加载本地数据库',`[${Date.now() - start}]`, Date.now());
      }
      const res = await imConnect();
      console.log('====>页面onMounted到im连接成功', `[${Math.floor(Date.now() - start)}ms]`, Date.now(), res);
      // IM连接成功后拉取服务器会话并关联openIM会话ID
      if (firstInit) {
        msgStore.loadRemoteSessions();
      }
    }
});
const ipcListeners = () => {
  ipcRenderer.removeAllListeners('mainWindow-focused');
  ipcRenderer.removeAllListeners('mainWindow-blurred');
  ipcRenderer.removeAllListeners('refresh-account-tip');
  ipcRenderer.on('refresh-account-tip', () => {
    MessagePlugin.success(t('account.changeAccountSuccess'));
    changeMoreAccountInfo(false, false);
  });
  ipcRenderer.on('mainWindow-focused', () => {
    msgStore.setMainWindowIsFocused(true)
  });
  ipcRenderer.on('mainWindow-blurred', () => {
    msgStore.setMainWindowIsFocused(false)

  });

  // 添加ipc监听
  LynkerSDK.ipc.handleRenderer('message-is-inited', () => {
    return true;
  })
  LynkerSDK.ipc.handleRenderer('message-open-chat', (val: any) => {
    if (val?.main) {
      openChat(val)
    }
    return true;
  })
  LynkerSDK.ipc.handleRenderer('message-open-message', async (val) => {
    let params =
    val.type === "GROUP" ? { main: val.cardId, group: val.fromId } : { main: val.cardId, peer: val.fromId };
    if (await openChat(params)) {
      ipcRenderer.invoke("im.msg.open", { messageUId: val.messageId });
    }
    return true;
  })
  LynkerSDK.ipc.handleRenderer('message-open-drawer-for-webview', (val) => {
    console.log('message-open-drawer-for-webview', 'val');
    if (val.id && val.url) {
      drawerRef.value.open({
        id: val.id,
        title: val.title,
        url: val.url,
      });
    }
  })
  LynkerSDK.ipc.handleRenderer('message-close-drawer-for-webview', (val) => {
    console.log('message-close-drawer-for-webview', 'val');
    if (val.id) {
      drawerRef.value.close(val.id);
    }
  })
}

onUnmounted(() => {
  ipcRenderer.removeAllListeners('mainWindow-focused');
  ipcRenderer.removeAllListeners('mainWindow-blurred');
  ipcRenderer.removeAllListeners('refresh-account-tip');
  console.log('销毁了哦');
  msgStore.setCurrentTime(true);
});
</script>

<style lang="less" scoped>
.icon-specials {
  width: 8px;
  height: 8px;
  position: absolute;
  top: 0;
  left: 0;
  color: var(--bg-kyy-color-bg-software-foucs, #272b4f);
}
.chat-root {
    display: flex;
    height: 100%;
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    position: relative;
}

// 窄导航：aside:64, session:256px, message:(528px 504px)/100%, tool: 448px,
// 宽导航：aside:128, session:256px, message:(464px 168px)/100%, tool: 448px
.session-list {
    display: flex;
    flex-direction: column;
    flex: 0 0 288px;
    height: 100%;
    background-color: white;
    overflow: auto;
    border-right: 1px solid @kyy_gray_3;
}

.session-list::-webkit-scrollbar {
  display: none;
}

.message-list {
    flex: 1;
    min-width: 528px;
}
.message-root {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: stretch;
    height: 100%;
    max-height: 100%;
    overflow: hidden;

    background-color: var(--bg-kyy-color-bg-deep, #F5F8FE);
}

.empty {
  flex:1;
  display:flex;
  flex-direction:column;
  align-items: center;
  justify-content: center;
  // padding-right: 100px;
  color: var(--lingke-contain-fonts, #516082)
}

.tip-offset {
  position: relative;
  // top: -10px;
  // line-height: 24px;
  // padding-left: 20px;
}

.empty-cpn {
  margin-top: 0;
  margin-bottom: 0;
  :deep(.tip) {
    display: none;
  }
}

</style>
