<template>
  <div class="containers">
    <div class="flow">
      <div class="flow-box">
        <div class="header">{{ $t('member.apply_flow.apply_way_1') }}</div>
        <div class="content">
          <div class="content-item">
            <i class="i-svg-color:flow_001 svg" />
            <span class="tap">{{ $t('member.apply_flow.apply_way_2') }}</span>
            <span class="tip">{{ $t('member.apply_flow.apply_way_3') }}</span>
            <span class="btn">
              <span class="set" @click="onAddMember">{{ $t('member.apply_flow.apply_way_4') }}</span>
              <span class="line"></span>
              <span class="set" @click="onImportMember">{{ $t('member.apply_flow.apply_way_5') }}</span>
            </span>
            <img src="@renderer/assets/member/icon/to_line.png" class="toline">
          </div>
          <div class="content-item">
            <i class="i-svg-color:flow_002 svg" />
            <span class="tap">{{ $t('member.apply_flow.apply_way_6') }}</span>
            <span class="tip">{{ $t('member.apply_flow.apply_way_7') }}</span>
            <img src="@renderer/assets/member/icon/to_down_line.png" class="toline" style="top: 50px;">
          </div>
          <div class="content-item">
            <i class="i-svg-color:flow_003 svg" />
            <span class="tap">{{ $t('member.apply_flow.apply_way_8') }}</span>
            <span class="tip">{{ $t('member.apply_flow.apply_way_9') }}</span>
            <img src="@renderer/assets/member/icon/to_line.png" class="toline">
          </div>
          <div class="content-item">
            <i class="i-svg-color:flow_004 svg" />
            <span class="tap">{{ $t('member.apply_flow.apply_way_10') }}</span>
            <span class="tip">{{ $t('member.apply_flow.apply_way_11') }}</span>

          </div>


        </div>
      </div>
      <div class="flow-box">
        <div class="header">{{ $t('member.apply_flow.apply_way_12') }}</div>
        <div class="content">
          <div class="content-item">
            <i class="i-svg-color:flow_001 svg" />
            <span class="tap">{{ $t('member.apply_flow.apply_way_13') }}</span>
            <span class="tip">{{ $t('member.apply_flow.apply_way_14') }}</span>
            <span class="btn">
              <span class="set" @click="onCopyLink">{{ $t('member.apply_flow.apply_way_15') }}</span>
              <span class="line"></span>
              <span class="set" @click="onInviteMember">{{ $t('member.apply_flow.apply_way_16') }}</span>
            </span>
            <img src="@renderer/assets/member/icon/big_to_line.png" class="bigtoline">
          </div>
          <div class="content-item">
            <i class="i-svg-color:flow_002 svg" />
            <span class="tap">{{ $t('member.apply_flow.apply_way_17') }}</span>
            <span class="tip">{{ $t('member.apply_flow.apply_way_18') }}</span>
            <img src="@renderer/assets/member/icon/big_to_line.png" class="bigtoline" style="top: 40px; transform:scaleY(-1);">
          </div>

          <div class="content-item">
            <i class="i-svg-color:flow_004 svg" />
            <span class="tap">{{ $t('member.apply_flow.apply_way_19') }}</span>
            <span class="tip">{{ $t('member.apply_flow.apply_way_20') }}</span>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/SvgIcon.vue";
const emits = defineEmits(['onAddMember', 'onImportMember', "onInviteMember", "onCopyLink"]);
const onAddMember = () => {
   emits('onAddMember');
};
const onImportMember = () => {
   emits('onImportMember');
};

const onInviteMember = () => {
    emits("onInviteMember");
};
const onCopyLink = () => {
    emits("onCopyLink");
};
</script>

<style lang="less" scoped>
.containers {
    // height: auto !important;
    display: flex;
    justify-content: center;
}
.flow {
    width: 944px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    &-box {
        width: inherit;
        border-radius: 16px;
        border: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
        padding-bottom: 20px;
        .header {
            // border: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
            color: var(--text-kyy-color-text-1, #1A2139);
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 157.143% */
            padding: 12px 24px;
            background: var(--bg-kyy-color-bg-list-hover, #F3F6FA);
        }
        .content {
            margin-top: 24px;
            display: flex;
            // gap: 56px;
            justify-content: space-between;
            padding: 0 20px;



            &-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 184px;

                position: relative;

                .toline {
                    position: absolute;


                    right: -104px;
                    top: 26px;
                    height: 12px;
                    width: 152px;


                }
                .bigtoline {
                    position: absolute;
                    right: -227px;
                    top: 15px;

                    width: 272px;
                    height: 24px;
                }

                .svg {
                    font-size: 64px;
                    // width: 64px;
                    // height: 64px;
                    color: #fff;
                }
                .tap {
                    margin-top: 16px;
                    color: var(--text-kyy-color-text-1, #1A2139);
                    text-align: center;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 24px; /* 150% */
                }
                .tip {
                    margin-top: 4px;
                    color: var(--text-kyy-color-text-3, #828DA5);
                    text-align: center;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; /* 157.143% */
                }
                .btn {
                    margin-top: 4px;
                    display: flex;
                    align-items: center;
                    gap: 16px;
                    .line {
                        width: 1px;
                        height: 16px;
                        background: var(--divider-kyy-color-divider-deep, #D5DBE4);
                    }
                    .set {
                        color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4D5EFF);
                        text-align: center;

                        cursor: pointer;
                        user-select: none;

                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 157.143% */
                    }
                }
            }
        }
    }
}
</style>
