<template>
  <div :class="['page-container page-post-detail', { 'is-video': isVideo }]">
    <div class="header">
      <img src="../../../assets/logo.png" alt="" class="logo" />
      <div class="name-wrap">
        <div class="name">Ringkol 另可</div>
        <!-- <div class="desc">另一种可能</div> -->
      </div>

      <wx-open-launch-app
        v-if="isAndroid && isInWeixin && wxLaunchAppAvailable"
        id="launch-btn"
        appid="wx77bf95d26088163a"
        :extinfo="extinfo"
        @error="handleErrorFn"
        @launch="handleLaunchFn"
      >
        <script is="vue:script" type="text/wxtag-template">
          <style>
            .btn {
              padding: 3px 12px;
              background: #4D5EFF;
              border-radius: 4px;
              font-size: 14px;
              font-weight: 400;
              text-align: center;
              color: #ffffff;
              line-height: 22px;
            }
            btn.lg {
              padding: 5px 26px;
            }
          </style>
          <div class="btn" @click="openApp">打开APP</div>
        </script>
      </wx-open-launch-app>
      <div v-else class="btn" @click="openApp">打开APP</div>
    </div>

    <div v-if="isVideo && !postData?.forwardPost" class="full-video-wrap" @click="clickTip">
      <t-image
        :src="`${post?.video}?x-oss-process=video/snapshot,t_10000,f_jpg,w_0,h_0,m_fast,ar_auto`"
        fit="cover"
        class="video-cover"
      >
        <template #error>
          <t-icon name="image-error" class="text-25!" />
        </template>
      </t-image>
      <i-svg-square-play class="play-icon" />
    </div>

    <main class="page-content">
      <div class="content-header">
        <SquareAvatar class="avatar" :square="square as Square" size="44px" @click="clickTip" />
        <div class="name-wrap">
          <div class="mb-2 flex">
            <SquareCertIcon :square="square as Square" class="mr-4" />
            <div class="name" @click="clickTip">{{ square?.name }}</div>
          </div>
          <div class="time">{{ post?.postedAt }}<template v-if="post?.ipRegion">·{{ post?.ipRegion }}</template></div>
        </div>
        <div v-if="!postData?.deleted && !postData?.invisible" class="btn-follow" @click="clickTip"><iconpark-icon name="iconcollect" class="icon" /> 关注</div>
      </div>

      <div class="right-content">
        <Empty v-if="postData?.deleted" tip="这条动态已删除" class="mb-16!" />
        <Empty v-else-if="postData?.invisible" tip="这条动态已设置不可见" class="mb-16!" />

        <template v-else>
          <TextEllipsis
            v-if="post?.text"
            class="text"
            :content="post?.text"
            :rows="isVideo ? 2 : 6"
            :expand-text="isVideo ? ' 展开全文' : ' 全文'"
            :collapse-text="isVideo ? ' 收起全文' : ' 收起'"
          />
          <div v-else-if="isVideo" style="height: 52px"> </div>

          <PostAlbumCard
            v-if="post?.postType === 'ALBUM_NODE'"
            readonly
            :album-node="post?.albumNode"
            :square="square as Square"
            @click="clickTip"
          />

          <DetailCardComponent v-if="postData?.post && isCard" v-bind="cardProps" @click="clickTip" />

          <ArticleCard v-if="postData?.article" :data="postData" @click="clickTip" />
          <!-- <VideoDisplay v-if="post?.video" :video="post.video" /> -->
          <ImageGrid v-else :list="imgList" />
        </template>

        <!--转发的卡片-->
        <div v-if="postData?.forwardPost" class="forward-wrap">
          <div v-if="postData.forwardPost.deleted" style="color: #717376">这条动态已删除</div>
          <div v-else-if="postData.forwardPost.invisible" style="color: #717376">这条动态已设置不可见</div>
          <template v-else>
            <t-link theme="primary" hover="underline">@{{ postData.forwardPost.square?.name }} :</t-link>
            <span v-if="postData?.forwardPost?.post" class="text">{{ postData.forwardPost.post.text }}</span>

            <PostAlbumCard
              v-if="postData.forwardPost.post.postType === 'ALBUM_NODE'"
              readonly
              :album-node="postData.forwardPost.post.albumNode"
              :square="postData.forwardPost.square"
              class="mt-8"
              @click="clickTip"
            />

            <div v-if="forwardMedia" class="attachment mt-8px">
              <VideoDisplay
                v-if="postData.forwardPost.post.video"
                :video="postData.forwardPost.post.video"
                large
              />
              <div v-else @lick.stop>
                <ImageGrid :list="forwardMedia.imageUrls" />
              </div>
            </div>

            <ArticleCard
              v-if="postData.forwardPost?.article"
              class="mt-12px"
              :data="postData.forwardPost.article"
              @click="clickTip"
            />

            <ForwardCardComponent v-if="postData?.forwardPost?.post && isForwardCard" v-bind="forwardCardProps" @click="clickTip" />
          </template>
        </div>

        <LocationShow v-if="postData?.post?.location && !isVideo" :location="postData?.post?.location" />

        <div v-if="isVideo && !postData?.forwardPost" class="reduce-icon-wrap">
          <i-svg-square-reduction v-if="isVideo" class="icon-reduce" />
        </div>
      </div>

      <div v-if="isVideo && !postData?.forwardPost" class="progress-wrap">
        <div class="progress"></div>
      </div>
    </main>

    <div class="footer">
      <div class="footer-wrap">
        <div class="item input" @click="clickTip">写评论...</div>
        <div class="item" @click="clickTip">
          <iconpark-icon name="iconlike" class="icon" />
          <span class="count">{{ stats?.likes || "赞" }}</span>
        </div>
        <div class="item" @click="clickTip">
          <iconpark-icon name="iconreply" class="icon" />
          <span class="count">{{ stats?.comments || "评论" }}</span>
        </div>
        <div class="item" @click="clickTip">
          <iconpark-icon name="iconshare2" class="icon" />
          <span class="count">{{ stats?.forwards || "转发" }}</span>
        </div>
      </div>

      <!-- 底部安全区 -->
      <div class="van-safe-area-bottom"></div>
    </div>

    <div v-if="isAndroid && isInWeixin && wxLaunchAppAvailable" class="fixed-bottom">
      <wx-open-launch-app
        id="launch-btn"
        appid="wx77bf95d26088163a"
        :extinfo="extinfo"
        @error="handleErrorFn"
        @launch="handleLaunchFn"
      >
        <script is="vue:script" type="text/wxtag-template">
          <style>
            .btn {
              padding: 7px 24px;
              background: #4D5EFF;
              border-radius: 4px;
              font-size: 17px;
              font-weight: 500;
              text-align: center;
              color: #ffffff;
              line-height: 26px;
            }
          </style>
          <div class="btn" @click="openApp">打开APP</div>
        </script>
      </wx-open-launch-app>
      <!-- 底部安全区 -->
      <div class="van-safe-area-bottom"></div>
    </div>
    <div v-else class="fixed-bottom">
      <div class="btn lg" @click="openApp">APP内打开</div>
      <!-- 底部安全区 -->
      <div class="van-safe-area-bottom"></div>
    </div>

    <ConfirmPopup v-if="confirmPopupVisible" v-model="confirmPopupVisible" @redirect="redirectToDownload" />
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, onMounted, ref, type ComputedRef } from "vue";
import { useRoute } from "vue-router";
import { getPostDetail } from "@/api/square/post";
import { TextEllipsis, Button as VanButton } from "vant";
import ImageGrid from "@/views/square/post/components/ImageGrid.vue";
import dayjs from "dayjs";
import { computed } from "vue";
import type { PostType, SharedPost } from "@/api/square/models/post";
import { useTitle } from "@vueuse/core";
import VideoDisplay from "@/views/square/post/components/VideoDisplay.vue";
import ArticleCard from "@/views/square/post/components/ArticleCard.vue";
import SquareAvatar from "@/views/square/components/SquareAvatar.vue";
import SquareCertIcon from '@/views/square/components/SquareCertIcon.vue';
import { SNAPSHOT } from "@/views/square/constants";
import PostAlbumCard from "./components/PostAlbumCard.vue";
import { getBaseUrl } from "@/api/requestApi";
import type {Square} from "@/api/square/models/square";
import Empty from "@/components/Empty.vue";
import {useWxConfig} from "@/hooks/useWxConfig";
import axios from 'axios';
import ConfirmPopup from "./components/ConfirmPopup.vue";
import { LoadingComponent, ErrorComponent } from "./components/AsyncComponent";
import LocationShow from "@/views/square/post/components/LocationShow.vue";

const route = useRoute();
const title = useTitle();
title.value = "详情";

const square = ref<SharedPost["square"]>();
const postData = ref<SharedPost>();
const post = ref<SharedPost["post"]>();
const article = ref<SharedPost["article"]>();
const stats = ref<SharedPost["stats"]>();

const confirmPopupVisible = ref(false);
const clickTip = () => {
  confirmPopupVisible.value = true;
}

const postType = computed(() => postData.value?.post?.postType);
const forwardPostType = computed(() => {
  if (postType.value !== 'FORWARD') return undefined;
  return postData.value?.forwardPost?.post.postType;
});
const isVideo = computed(() => !!post?.value?.video);

const imgList = computed(() => {
  if (!post.value || !post.value.picture?.urls?.length) return [];
  return post.value.picture.urls.map((v) => {
    if (v.includes("?")) return v.split("?")[0];
    return v;
  });
});

const forwardMedia = computed(() => {
  if (!postData.value?.forwardPost?.post) return "";
  const { picture, video } = postData.value.forwardPost.post;
  return {
    imageUrls: picture?.urls || [],
    videoUrl: video ? `${video}${SNAPSHOT}` : "",
  };
});

const loaderMapper: Record<string, () => Promise<any>> = {
  'TEAM_HONOR_ROLL': () => import('@/views/square/post/components/TeamHonorRollCard.vue'),
  'TEAM_INTRO': () => import('@/views/square/post/components/TeamIntroCard.vue'),
  'TEAM_HISTORY': () => import('@/views/square/post/components/TeamHistoryCard.vue'),
  'PARTY_BUILDING': () => import('@/views/square/post/components/PartyBuildingCard.vue'),
  'FENGCAI': () => import('@/views/square/post/components/FengcaiCard.vue'),
};
const isCard = computed(() => Object.keys(loaderMapper).includes(postType.value as string));
const isForwardCard = computed(() => Object.keys(loaderMapper).includes(forwardPostType.value as string));

const BaseCardComponent = (typeRef: ComputedRef<PostType | undefined>) => defineAsyncComponent({
  loader: () => {
    const type = typeRef.value;
    return type && loaderMapper[type] ? loaderMapper[type]() : Promise.reject(new Error(`Component for type "${type}" not found`));
  },
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  timeout: 10000,
});

// 卡片动态类型对应的数据字段
const postTypeFieldMap: Record<string, string> = {
  'PARTY_BUILDING': 'partyBuilding',
  'TEAM_INTRO': 'teamIntro',
  'TEAM_HONOR_ROLL': 'teamHonorRoll',
  'TEAM_HISTORY': 'teamHistory',
  'FENGCAI': 'fengcai',
};

const cardProps = computed(() => {
  const post = postData.value?.post;
  if (!post || !postType.value) return {};

  const typeField = postTypeFieldMap[postType.value];
  // @ts-ignore
  return post[typeField];
});

const forwardCardProps = computed(() => {
  const post = postData.value?.forwardPost?.post;
  if (!post || !forwardPostType.value) return {};

  const typeField = postTypeFieldMap[forwardPostType.value];
  // @ts-ignore
  return post[typeField];
});

// 加载卡片详情组件
const DetailCardComponent = BaseCardComponent(postType);
const ForwardCardComponent = BaseCardComponent(forwardPostType);

// ?post_share_token=CNsXEhIwNnhvZzAwMDB3ejUxZnM0OGsaCwjmxZOsBhDagttN
const params = computed(() => Object.fromEntries(new URLSearchParams(window.location.search).entries()));
const getInfo = async () => {
  const share_token = params.value.post_share_token;
  const data = await getPostDetail({ share_token });

  const { square: s, post: p, article: a, stats: st } = data.post;
  postData.value = data.post;
  if (p) {
    p.postedAt = dayjs(p.postedAt).format("MM-DD HH:mm");
  }
  square.value = s;
  post.value = p;
  article.value = a;
  stats.value = st;
};

const wxLaunchAppAvailable = ref(true);
const isAndroid = /android/.test(navigator.userAgent.toLowerCase());
const { isInWeixin, handleLaunchFn } =  useWxConfig({
  onError: () => {
    wxLaunchAppAvailable.value = false;
  }
});

const extinfo = computed(() => 'ringkol://ringkol.com/square-module?' + new URLSearchParams({
  loginRequired: 'true',
  id: post.value?.id as string,
  postType: post.value?.postType as string,
  path: 'square-post-detail',
}).toString());

const redirectToDownload = () => {
  // location.href = `${getBaseUrl('square')}/v1/redirect_to_download?origin=${encodeURIComponent(location.href)}`;
  axios.get(`${getBaseUrl('square')}/v1/redirect_to_download?origin=${encodeURIComponent(location.href)}`).then((res) => {
    console.log(res);
    location.href = res.request.responseURL;
  })
}

const openApp = async () => {
  if (isAndroid) {
    // Android 直接通过唤起App
    location.href = extinfo.value;

    setTimeout(() => {
      redirectToDownload();
    }, 3000);
    return;
  }

  // 重定向到下载页
  redirectToDownload();
}

const handleErrorFn = (e: any) => {
  console.log('handleErrorFn', e);
  wxLaunchAppAvailable.value = false;
  redirectToDownload();
}

onMounted(() => {
  getInfo();
});
</script>

<style scoped lang="less">
.page-container {
  height: 100%;
  background-color: #fff;
}

.page-content {
  padding: 16px;
  padding-bottom: 48px;
  background-color: #fff;

  .content-header {
    display: flex;
    margin-bottom: 12px;
    .name-wrap {
      flex: 1;
    }
    .name {
      font-size: 17px;
      font-weight: 600;
      color: var(--text-kyy_color_text_1, #1A2139);
      line-height: 26px;
      max-width: 150px;
      .ellipsis();
    }
    .time {
      font-size: 12px;
      color: var(--text-kyy_color_text_3, #828DA5);
      line-height: 20px;
    }
    .btn-follow {
      height: 24px;
      align-self: center;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 999px;
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_brand, #4D5EFF);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
      display: flex;
      gap: 4px;
      padding: 0 8px;
      color: var(--color-button_border-kyy_color_buttonBorder_text_brand, #4D5EFF);
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      .icon {
        color: #4D5EFF;
        font-size: 16px;
      }
    }
  }

  .avatar {
    width: 44px;
    height: 44px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .right-content {
    font-weight: 400;
    .text {
      font-size: 17px;
      color: var(--text-kyy_color_text_1, #1A2139);
      line-height: 26px;
      margin-bottom: 8px;
    }
    :deep(.van-text-ellipsis__action) {
      color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF);
    }
  }

  .img-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    margin: 0 -10px;
    .item {
      width: 80px;
      height: 80px;
      text-align: center;
    }
  }

  .forward-wrap {
    margin-top: 12px;
    margin-left: -16px;
    margin-right: -16px;
    padding: 12px 16px;
    background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    &.in-list {
      margin-left: -56px;
      margin-right: -8px;
      padding-left: 56px;
    }
    :deep(.t-link--theme-primary) {
      color: #4D5EFF !important;
      font-size: 17px;
    }
  }
}

.full-video-wrap {
  position: absolute;
  top: 65px;
  bottom: 0;
  left: 0;
  right: 0;
  .video-cover {
    height: 100%;
  }
  .play-icon {
    width: 56px;
    height: 56px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }
}

.page-post-detail.is-video {
  .page-content {
    position: absolute;
    bottom: 42px;
    width: 100%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.40) 100%);
  }

  .page-content {
    z-index: 9;
  }

  .right-content {
    padding-right: 40px;
    .text {
      color: #fff;
      font-size: 14px;
      margin-bottom: 0;
      :deep(.van-text-ellipsis__action) {
        color: #fff;
      }
    }
    .reduce-icon-wrap {
      position: absolute;
      right: 16px;
      bottom: 20px;
      display: flex;
      border-radius: 99px;
      background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
    }
  }

  .progress-wrap {
    display: flex;
    width: calc(100% - 32px);
    height: var(--checkbox-kyy_radius_checkbox, 2px);
    padding: 0px 10px 0px 16px;
    gap: 7px;
    background: rgba(0, 0, 0, 0.20);
    z-index: 9;
    position: absolute;
    bottom: 8px;
    .progress {
      width: 96px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 4px;
      border-radius: 5px;
      background: var(--bg-kyy_color_bg_light, #FFF);
    }
  }

  .content-header {
    :deep(.t-avatar) {
      border-radius: 99px;
      border: var(--checkbox-kyy_radius_checkbox, 2px) solid var(--kyy_avatar_border_default, #FFF);
    }
    .name-wrap {
      display: flex;
      flex: initial;
      align-items: center;
      margin-right: 12px;
    }
    .name {
      color: #fff;
    }
    .time {
      display: none;
    }
    .btn-follow {
      background-color: transparent;
      color: #fff;
      border-color: var(--border-kyy_color_border_default, #D5DBE4);
      .icon {
        color: #fff;
      }
    }
  }
}

.header {
  height: 65px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 10;
  .logo {
    width: 40px;
    height: 40px;
    margin-right: 8px;
  }
  .name-wrap {
    flex: 1;
  }
  .name {
    font-size: 17px;
    font-weight: 500;
    color: #13161b;
    line-height: 26px;
  }
  .desc {
    font-size: 12px;
    font-weight: 400;
    color: #717376;
  }
}

.btn {
  padding: 3px 12px;
  background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4D5EFF);
  border-radius: 4px;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  color: #ffffff;
  line-height: 22px;
  &.lg {
    padding: 7px 24px;
    line-height: 26px;
    min-width: 133px;
    color: var(--color-button_primary-kyy_color_button_primary_text, #FFF);
    text-align: center;
    font-size: 17px;
    font-weight: 500;
    line-height: 26px; /* 152.941% */
  }
}

.footer {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 6px 16px;
  z-index: 9;
  background-color: #fff;
  .footer-wrap {
    display: flex;
    justify-content: space-between;
  }
  .input {
    width: 109px;
    height: 36px;
    flex: initial !important;

    display: flex;
    padding: 5px 16px;
    align-items: center;
    flex: 1 0 0;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    color: var(--text-kyy_color_text_5, #ACB3C0) !important;
    font-feature-settings: 'liga' off, 'clig' off;

    font-size: 17px;
    font-weight: 400;
    line-height: 26px; /* 152.941% */
    margin-right: 8px;
  }
  .item {
    display: flex;
    align-items: center;
    color: var(--text-kyy_color_text_2, #516082);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .icon {
    font-size: 20px;
    margin-right: 4px;
    color: #828DA5;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9;
}
</style>


<style lang="less">
.v-line {
  position: relative;
  margin: 0 12px;
  &::before {
    content: ' ';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 16px;
    background: var(--divider-kyy_color_divider_light, #ECEFF5);
  }
}
</style>
