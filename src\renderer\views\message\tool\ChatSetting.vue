<template>
  <div class="drawer-root" v-if="!showGroupManage && (setting.peerInfo || setting.groupInfo)">
    <div class="chat-drawer-header">
      {{t("im.tools.setting")}}
      <div @click="emits('close')" class="iconhover" style="position:absolute;right:24px;top:18px;">
        <img style="height:16px;" src="@/assets/im/im_close.png">
      </div>
    </div>
    <!-- 单聊 -->
    <div class="drawer-content" v-if="setting.peerInfo">
      <div class="row-item" style="justify-content: flex-start;gap:10px;">
        <chat-avatar :size="44" :src="setting.privatePeer.avatar" :alt="setting.privatePeer.staffName || setting.privatePeer.nickname" />
        <div class="group-name">{{ setting.privatePeer.staffName || setting.privatePeer.nickname }}</div>
      </div>

      <div class="row-item select">
        <div>{{t("im.public.topChat")}}</div>
        <t-switch :value="Boolean(setting.privateSetting.stayOn)" :loading="setting.stayOnLoading" @change="setting.onStayonChange" />
      </div>
      <div class="row-item select">
        <div>{{t("im.public.mute")}}</div>
        <t-switch :value="Boolean(setting.privateSetting.noDisturb)" :loading="setting.noDisturbLoading" @change="setting.onNoDisturbChange" />
      </div>
      <div class="setting-divider"></div>
      <div class="row-item select" @click="showCreateGroup">
        <div>{{t("im.public.addNewMember")}}</div>
        <div class="row-item-right">
          <svg class="svg-size20"><use href="#iconarrowright"></use></svg>
        </div>
      </div>
      <div class="setting-bottom">
        <div class="setting-divider row-item-report-divider"></div>
        <div class="row-item-report select" @click.stop="onPrivateReport">
          <!-- <SvgIcon name="im_setting_report" class="svg-size18" /> -->
          <i class="i-svg:im_setting_report text-18 color-text-3" />
          <div>{{t("im.public.report")}}</div>
        </div>
      </div>

    </div>

    <!-- 助手 -->
    <div class="drawer-content" v-else-if="setting.groupInfo.conversationType === 6">
      <div class="row-item" style="justify-content: flex-start;gap:10px;">
        <chat-avatar :size="44" :src="setting.groupInfo.avatar" :alt="setting.groupInfo.name" />
        <div class="group-name">{{ setting.groupInfo.name }}</div>
      </div>

      <div class="row-item select">
        <div>{{t("im.public.topChat")}}</div>
        <t-switch :value="Boolean(setting.groupInfo?.isTop)" :loading="setting.stayOnLoading" @change="setting.onStayonChange" />
      </div>
      <div class="row-item select">
        <div>{{t("im.public.mute")}}</div>
        <t-switch :value="Boolean(setting.groupInfo?.isMute)" :loading="setting.noDisturbLoading" @change="setting.onNoDisturbChange" />
      </div>
    </div>

    <!-- 群聊 -->
    <div class="drawer-content" v-else-if="!isHelperGroup(setting.groupInfo)">
      <div class="row-item">
        <div class="avatar-group">
          <chat-avatar :size="44" :multi-src="setting.groupInfo?.attachment?.avatar || setting.groupInfo?.avatar" :trimLength="1" />
          <div class="avatar-btn" @click="openUploadAvatar(setting.groupInfo?.attachment?.avatar)" v-if="setting.isGroupOwner || setting.isGroupManager">
            <svg class="svg-size20"><use href="#iconedit"></use></svg>
          </div>
          <UploadAvatar ref="uploadAvatarRef" style="position:absolute;width: 200px;" @confirm="uploadAvatarConfirm" v-if="setting.isGroupOwner || setting.isGroupManager" />
        </div>
        <div v-if="!isEditingGroupName" style="flex:1;overflow: hidden;">
          <div @click="startGroupnameInput()">
            <div class="group-info">
              <div class="group-name">
                {{ setting.groupInfo.name }}
              </div>
              <svg v-if="setting.isGroupOwner || setting.isGroupManager" class="svg-size20"><use href="#iconedit"></use></svg>
            </div>
            <div class="group-type"> {{ getGroupType() }}</div>
          </div>
        </div>
        <div v-else style="flex:1;display:flex;flex-direction: row;align-items: center;height: 48px;">
          <t-input style="flex:1;"  ref="groupNameInput" v-model="groupName" maxlength="20" showLimitNumber @blur="endGroupNameInput" />
        </div>
      </div>
      <div class="row-item select" @click="startMyGroupnameInput('comment')">
        <div>{{t("im.public.groupRemark")}}</div>
        <div class="row-item-right">
          <div class="row-text-line1">
            {{ setting.myGroupCard?.attachments?.comment || '' }}
          </div>
          <svg class="svg-size20"><use href="#iconarrowright"></use></svg>
        </div>
      </div>
      <div v-if="setting.showNotOfComponyPlatallform" class="row-item select" @click="showGroupQrVisible">
        {{t("im.public.groupQRCode")}}
        <div class="row-item-right" >
          <svg class="svg-size20"><use href="#iconarrowright"></use></svg>
        </div>
      </div>
      <div class="row-item select" @click="startMyGroupnameInput('nickName')">
        <div>{{t("im.public.selfGroupRemark")}}</div>
        <div class="row-item-right" >
          <div class="row-text-line1">
            {{ setting.myGroupCard?.attachments?.nickname || setting.myGroupCard?.attachments?.staffName || '' }}
          </div>
          <svg class="svg-size20"><use href="#iconarrowright"></use></svg>
        </div>
      </div>
      <div class="setting-divider"></div>
      <div class="row-item" @click="showGroupMembers = true">
        <div>{{t("im.public.groupMember")}}</div>
        <div class="row-item-right">
          {{ setting.groupInfo.members.length }}
          <svg class="svg-size20"><use href="#iconarrowright"></use></svg>
        </div>
      </div>
      <div class="set-labelId" v-if="showSetIdentity()" @click.stop="openSetIdentity"> {{ t('im.public.setIdentity') }}</div>
      <div class="group-members" >
          <div v-if="setting.showNotOfComponyPlatallform" class="group-member add" @click="showAddMember">
              <t-icon name="add" color="#717376" size="24px" />
          </div>
          <div v-for="member in setting.groupInfo.members" class="group-member">
            <chat-avatar :size="44" :src="member?.attachments?.avatar" @click="openIdentitycard(member)"
              :alt="member?.attachments?.staffName || ''" />
            <!-- <div class="member-name">{{ member?.attachments?.nickname || member?openIdentitycard.attachments?.staffName || '' }}</div> -->
            <svg v-if="showRemoveMember(member)" @click="onDeleteGroupMember(member)" class="remove-icon"><use href="#iconerror"></use></svg>
          </div>
        </div>

      <div class="setting-divider"></div>
      <div class="row-item select" @click="showTagManage = true">
        <div>{{t("im.public.tag")}}</div>
        <div class="row-item-right">
          <svg class="svg-size20"><use href="#iconarrowright"></use></svg>
        </div>
      </div>
      <div class="row-item select" v-if="setting.isGroupOwner || setting.isGroupManager"
        @click="showGroupManage = true">
        <div>{{t("im.public.groupManager")}}</div>
        <div class="row-item-right">
          <svg class="svg-size20"><use href="#iconarrowright"></use></svg>
        </div>
      </div>
      <div class="row-item select">
        <div>{{t("im.public.topChat")}}</div>
        <t-switch :value="Boolean(setting.myGroupCard?.attachments?.stayOn)" :loading="setting.stayOnLoading" :disabled="setting.stayOnLoading" @change="setting.onStayonChange" />
      </div>
      <div class="row-item select">
        <div>{{t("im.public.mute")}}</div>
        <t-switch :value="Boolean(setting.myGroupCard?.attachments?.noDisturb)" :loading="setting.noDisturbLoading" :disabled="setting.stayOnLoading" @change="setting.onNoDisturbChange" />
      </div>
      <div class="row-item select">
        <div>{{t("im.public.saveContact")}}</div>
        <t-switch :value="Boolean(setting.myGroupCard?.addr)" @change="setting.onAddressbookChange" />
      </div>

      <div class="setting-divider"></div>
      <div v-if="setting.showNotQuitGroup" class="row-item select waring" @click="alertQuitGroup = true">
        {{t("im.public.quit_group")}}
      </div>
      <div class="row-item select waring" v-if="setting.showNotOfComponyPlatallform && setting.isGroupOwner"
        @click="deleteGroupAction">
        {{t("im.public.dismissGroup")}}
      </div>
      <div v-if="setting.showNotOfComponyPlatallform" class="setting-divider"></div>
        <div class="row-item-report select" @click.stop="onGroupReport">
          <!-- <SvgIcon name="im_setting_report" class="svg-size18" /> -->
          <i class="i-svg:im_setting_report text-18 color-text-3" />
          <div>{{t("im.public.report")}}</div>
        </div>
    </div>
  </div>

  <!-- 群管理 -->
  <transition>
    <div class="drawer-root" v-if="showGroupManage" style="z-index: 2;">
      <div class="chat-drawer-header">
        <div @click="showGroupManage = false" style="cursor: pointer;display: flex; align-items: center;gap:4px;">
          <svg class="svg-size24"><use href="#iconarrowlift"></use></svg>
          {{t("im.public.groupManager")}}
        </div>
        <div @click="emits('close')">
          <svg class="svg-size24"><use href="#iconerror"></use></svg>
        </div>
      </div>
      <div class="drawer-content">
        <div class="row-item select" v-if="setting.showNotOfPlatallform">
          <div>{{t("im.public.inviteTip")}}</div>
          <t-switch :value="Boolean(setting.groupInfo.attachment.hasInviteMember)"
            @change="setting.onMemberInviteUpdate" />
        </div>
        <div class="row-item select">
          <div>{{t("im.public.historyTip")}}</div>
          <t-switch :value="Boolean(setting.groupInfo.attachment.hasLookForHistory)"
            @change="setting.onNewMemberHistoryUpdate" />
        </div>
        <div class="row-item"
          v-if="[1, 2, 3].includes(setting.groupInfo.type)">
          <div>
            {{t("im.public.syncTip1")}}
            <t-tooltip placement="bottom" :content="t('im.public.syncTip2')">
              <HelpCircleIcon />
            </t-tooltip>
          </div>
          <t-switch :value="Boolean(setting.groupInfo.attachment.openCloudFile)"
            @change="setting.onCloudDiskFileUpdate" />
        </div>
        <div class="row-item"
          v-if="[1, 2, 3].includes(setting.groupInfo.type)">
          <div>
            {{t("im.public.syncTip3")}}
            <t-tooltip placement="bottom" :content="t('im.public.syncTip4')">
              <HelpCircleIcon />
            </t-tooltip>
          </div>
          <t-switch :value="Boolean(setting.groupInfo?.attachment?.openPictureFile)"
            @change="setting.onCloudDiskPictureUpdate" />
        </div>

        <!-- 仅群主可以添加管理员 -->
        <template v-if="!setting.isGroupManager">
          <div class="row-item select" @click="onShowAdminAddList">
            <div>{{t("im.public.groupAdmin")}}</div>
            <div> <img style="height: 16px;" src="@/assets/im/im_setting_arrow.png"></div>
          </div>
          <div class="row-item select" v-if="setting.isGroupOwner" @click="onShowOwnerTransfer">
            <div>{{t("im.public.transferGroup")}}</div>
            <div> <img style="height: 16px;" src="@/assets/im/im_setting_arrow.png"></div>
          </div>

          <div v-if="setting.showNotOfComponyPlatallform" class="setting-divider"></div>
          <div class="row-item select" v-if="setting.isGroupOwner && setting.showNotOfComponyPlatallform"
            @click="deleteGroupAction">
            {{t("im.public.dismissGroup")}}
          </div>
        </template>

      </div>
    </div>
  </transition>

  <!-- 群成员 -->
  <transition>
    <div class="drawer-root" v-if="showGroupMembers" style="z-index: 3">
      <div class="chat-drawer-header">
        <div style="cursor: pointer;display: flex; align-items: center;gap:4px;" @click="showGroupMembers = false">
          <svg class="svg-size24"><use href="#iconarrowlift"></use></svg>
          <span v-if="showAddAdmin">{{t("im.public.groupAdmin")}}</span>
          <span v-else-if="showOwnerTransfer">{{t("im.public.transferGroup")}}</span>
          <span v-else>{{t("im.public.groupMember")}}</span>
        </div>
        <div @click="emits('close')">
          <svg class="svg-size24"><use href="#iconerror"></use></svg>
        </div>
        <t-button v-if="showAddAdmin || showOwnerTransfer" variant="text" theme="primary"
          @click="onSubmitManage">{{t("im.public.confirm")}}</t-button>
      </div>
      <div class="drawer-content">
        <div style="padding: 8px;">
          <t-input v-model="memberSearchText" clearable :placeholder="t('im.public.searchMember')">
            <template #prefix-icon>
              <!-- <SvgIcon name="im-history" class="svg-size20" /> -->
              <i class="i-svg:im-history text-20 color-text-3" />
            </template>
          </t-input>
        </div>
        <div v-if="!groupMembers.length">
          <Empty name="no-result" />
        </div>
        <div v-for="member in groupMembers" :key="member.openid" class="members" @click="memberSelectchange(member.openid)">
          <chat-avatar
            :size="32"
            :src="member?.attachments?.avatar"
            :alt="member?.attachments?.staffName || ''"
            @click="openIdentitycard(member)"
          />
          <div class="flex-column ml-12">
            <div class="flex">
              <div class="members-left">
                <div style=" margin-right: 4px;color: #1A2139;" class="members-name">
                  {{ member?.attachments?.nickname || member?.attachments?.staffName || '' }}
                </div>
                <div v-if="member?.attachments?.teamId && showTeamName && ![20, 22].includes(setting.groupInfo.type)" class="tag-team">
                  {{ member?.attachments.teamName }}
                </div>
                <div v-if="member.roles?.some(i => i === 'OWNER' || i === 'ADMIN')" class="tag-role">
                  {{ member.roles.some(i => i === 'OWNER') ? t('im.public.groupOwner') : t('im.public.groupManager') }}
                </div>
              </div>
              <div @click="showSetIdentity() && chooseIdentity(member)" :class="{'cursor-pointer':showSetIdentity()}" style="margin-left:4px;">
                <div class="member-labelId" v-if="member?.label_id">{{ msgStore.getMemberLabelName(member?.label_id)}}</div>
                <div class="set-labelId" v-else-if="showSetIdentity()"> {{ t('im.public.setIdentity') }}</div>
              </div>
            </div>
            <div v-if="[22].includes(setting.groupInfo.type)" class="leading-[19px]">
              <span v-if="cardIdType(member?.card?.card_id) === 'inner'" class="text-[12px] text-[#828DA5]">
                {{ member?.card?.departments?.[0]?.name || '' }}{{ member?.card?.departments?.[0]?.name ? member?.card?.departments?.[0]?.job_name ? `/${member?.card?.departments?.[0]?.job_name}` : '/--' : '' }}
              </span>
              <span v-if="cardIdType(member?.card?.card_id) === 'platform'" class="text-[12px] text-[#828DA5]">
                {{ member?.card?.platform?.[0]?.relation_team === '个人' ? member?.card?.platform?.[0]?.relation_team : ((member?.card?.platform?.[0]?.relation_team || '') + (member?.card?.platform?.[0]?.relation_job ? `/${member?.card?.platform?.[0]?.relation_job}` : '')) }}
              </span>
            </div>
          </div>
          <div
            v-if="!showAddAdmin && !showOwnerTransfer && showRemoveMember(member)"
            class="member-delete"
            @click="onDeleteGroupMember(member)"
          >
            <t-tooltip :content="t('im.public.delete')" :show-arrow="false" placement="bottom">
              <svg class="svg-size20 delete"><use href="#icondelete"></use></svg>
            </t-tooltip>
          </div>
        </div>
      </div>
    </div>
  </transition>

  <!-- 群管理员列表 -->
  <transition>
    <div class="drawer-root" v-if="groupAdminVisible" style="z-index: 3">
      <div class="chat-drawer-header">
        <div style="cursor: pointer;display: flex; align-items: center;gap:4px;" @click="groupAdminVisible = false">
          <svg class="svg-size24"><use href="#iconarrowlift"></use></svg>
          <span>{{t("im.public.groupAdmin")}}</span>
        </div>
        <div @click="emits('close')">
          <svg class="svg-size24"><use href="#iconerror"></use></svg>
        </div>
      </div>
      <div class="drawer-content">
        <div class="row-item admin-add" @click="onShowAdminAdd">
          <div style="display:flex;align-items: center;gap:4px;">
            <svg class="svg-size20"><use href="#iconadd"></use></svg>
            {{ t('im.public.addGroupManager') }}
          </div>
          {{ groupAdmins.length + '/' + (setting.groupInfo.members.length - 1) }}
        </div>
        <div class="setting-divider"></div>
        <div v-for="member in groupAdmins" :key="member.openid" class="members">
          <chat-avatar
            :size="32"
            :src="member?.attachments?.avatar"
            :alt="member?.attachments?.staffName || ''"
            @click="openIdentitycard(member)"
          />
          <div style="margin-left: 8px; margin-right: 4px;color: #1A2139;">
            {{ member?.attachments?.nickname || member?.attachments?.staffName || '' }}
          </div>
          <div v-if="member?.attachments?.teamId && showTeamName" class="tag-team">
            {{ member?.attachments.teamName }}
          </div>
          <div v-if="member.roles?.some(i => i === 'OWNER' || i === 'ADMIN')" class="tag-role">
            {{ member.roles.some(i => i === 'OWNER') ? t('im.public.groupOwner') : t('im.public.groupManager') }}
          </div>
          <!-- 群主不删除 -->
          <div v-if="setting.myGroupCard.openid !== member.openid" class="member-delete" @click="onRemoveAdmin(member)">
            <t-tooltip :content="t('im.public.delete')" :show-arrow="false" placement="bottom">
              <svg class="svg-size20 delete"><use href="#icondelete"></use></svg>
            </t-tooltip>
          </div>
        </div>
      </div>
    </div>
  </transition>

  <t-dialog v-model:visible="alertDeleteGroup">
    <template #header>
      <div style="font-size: 16px; font-weight: 700;color: #13161b;">
        <t-icon name="help-circle-filled" style="color: #E66800" />
        {{t("im.public.dismiss")}}
      </div>
    </template>

    <template #body>
      <div style="font-size: 14px; font-weight: 400;color: #717376;padding-bottom: 20px;">
        {{t("im.public.dismissTip1")}}
      </div>
    </template>

    <template #footer>
      <t-button variant="outline" @click="alertDeleteGroup = false">{{t("im.public.cancel")}}</t-button>
      <t-button @click="onDeleteGroup">{{t("im.public.dismiss")}}</t-button>
    </template>
  </t-dialog>

  <t-dialog v-model:visible="alertQuitGroup" @cancel="alertQuitGroup = false;" @confirm="quitGroup" :confirm-btn="t('im.public.exit')">
    <template #header>
      <div style="font-size: 16px; font-weight: 700;color: #13161b;">
        <t-icon name="help-circle-filled" style="color: #E66800" />
        {{t("im.public.exit")}}
      </div>
    </template>

    <template #body>
      <div style="font-size: 14px; font-weight: 400;color: #717376;padding-bottom: 20px;">
        {{t("im.public.exitTip1")}}
      </div>
      <div style="display: block;font-size: 14px; font-weight: 400;color: #13161b;">
        <t-checkbox v-model="cleanMsg">{{t("im.public.clearChat")}}</t-checkbox>
      </div>
    </template>
  </t-dialog>

  <t-dialog v-model:visible="visibleGroupName"
  :header="changeGroupType === 'comment' ? t('im.public.groupRemark') : t('im.public.selfGroupRemark')"
  :on-confirm="endMyGroupNameInput">
    <template #body>
      <t-input ref="myGroupNameInput" v-model="myGroupName" maxlength="20" showLimitNumber :placeholder="$t('im.public.groupRemarkTip')" autofocus style="border:1px solid #4D5EFF;border-radius:4px;"/>
    </template>
  </t-dialog>

  <t-dialog v-if="setting.groupInfo" v-model:visible="groupQrVisible" width="320px" class="qr-dialog" :closeBtn="false">
    <template #header>
      <iconpark-icon name="iconerror-a961a3n0" style="font-size: 20px;width: 20px;height:20px;color:rgba(81, 96, 130, 1);position: absolute;right:-20px;top:6px;" @click="groupQrVisible=false" class="closeicon"></iconpark-icon>
    </template>
    <template #body>
      <div style="position: relative;">
        <div class="group-qr-box">
          <div class="qr-header" style="border-bottom:1px solid #ECEFF5">
            <chat-avatar :size="44" :multi-src="setting.groupInfo?.attachment?.avatar" :trimLength="1"/>
            {{ setting.groupInfo?.name }}
          </div>
          <div style="font-size:12px;font-weight:normal;color:#8B8989;text-align:center;padding:6px 0;background:#fff">{{ setting.groupInfo?.type==1||setting.groupInfo?.type==2||setting.groupInfo?.type==3?t("im.public.QigroupQRCode"):t("im.public.DogroupQRCode") }}</div>
          <div v-if="setting.groupInfo" class="group-qr" :id="`group-qr-${setting.groupInfo.group}`">
            <vue-qr :correctLevel="3" :margin="4" :size="148" color-dark="#000000" :logo-src="logoSrc" :logo-margin="4"
              :text="`${getBaseUrl('website')}/downloadCenter?tag=group_qr_code&groupId=${setting.groupInfo.group}`" />
          </div>
          <div class="ringkol" style="text-align:center;width:100%;margin-top:10px;">
            <img src="@renderer/assets/im/ringkollogo.svg"/>
          </div>
        </div>
        <!-- 下载不了头像 -->
        <div class="group-qr-box" ref="qrCodeImg" style="position: absolute;top: 0;left: 0;z-index: -1;width: 100%;">
          <div class="qr-header" style="border-bottom:1px solid #ECEFF5">
            <img src="@renderer/assets/im/group-qr.svg" alt="" style="width: 44px;height: 44px;" crossorigin="anonymous">
            {{ setting.groupInfo?.name }}
            <!-- <iconpark-icon name="iconerror-a961a3n0" style="font-size: 20px;width: 20px;height:20px;color:rgba(81, 96, 130, 1);position: absolute;right:0px;top:0px;" @click="groupQrVisible=false" class="closeicon"></iconpark-icon> -->
          </div>
          <div style="font-size:12px;font-weight:normal;color:#8B8989;text-align:center;padding:6px 0;background:#fff">{{ setting.groupInfo?.type==1||setting.groupInfo?.type==2||setting.groupInfo?.type==3?t("im.public.QigroupQRCode"):t("im.public.DogroupQRCode") }}</div>
          <div v-if="setting.groupInfo" class="group-qr" :id="`group-qr-${setting.groupInfo.group}`">
            <vue-qr :correctLevel="3" :margin="4" :size="158" color-dark="#000000" :logo-src="logoSrc" :logo-margin="4"
              :text="`${getBaseUrl('website')}/downloadCenter?tag=group_qr_code&groupId=${setting.groupInfo.group}`" />
          </div>
          <div class="ringkol" style="text-align:center;width:100%;margin-top:10px;">
            <img src="@renderer/assets/im/ringkollogo.svg"/>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <t-button block theme="primary" variant="base" :loading="saveQrLoading" @click="onSaveGroupQr" style="font-weight:normal">{{ t("im.public.savelocal") }}</t-button>
    </template>
  </t-dialog>
  <tag v-if="msgStore.chatingSession" v-model:visible="showTagManage" :my-id="msgStore.chatingSession.myCardId"
    :relation-id="msgStore.chatingSession.targetId" :relation-type="2" />

  <GroupMemberSelect v-if='setting.groupInfo?.members'
    v-model:visible="groupMemberSelectVisible"
    :title="groupMemberSelectTitle"
    :mode="groupMemberSelectMode"
    :members="needSelectMembers"
    :my-card="setting.myGroupCard"
    :selected="groupMemberSelectedCards"
    @confirm="onSubmitManage"
  />
  <SetMemberIdentity v-model:visible="showSetIdentityVisible" :member="chosenMember"/>

</template>

<script setup lang="ts">
import { ref, computed, watch, watchEffect, nextTick } from 'vue';
import tag from '@renderer/views/identitycard/components/setTag.vue';
import to from "await-to-js";
import ChatAvatar from '../components/ChatAvatar.vue';
import { useMessageStore } from '../service/store';
import { useSessionSetting } from './service/chatSetting';
import { IGroupMember, IRelationGroup } from '@renderer/api/im/model/relation';
import { GroupType } from "customTypes/message";
import { cardIdType } from '@renderer/views/identitycard/data';
import UploadAvatar from "@/components/common/UploadAvatar.vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { HelpCircleIcon } from 'tdesign-icons-vue-next';
import { showDialog } from '@renderer/utils/DialogBV';
import vueQr from "vue-qr/src/packages/vue-qr.vue";
import logoSrc from '@renderer/assets/account/logo_img_logo.svg';
import * as htmlToImage from "html-to-image";
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import SvgIcon from '@/components/SvgIcon.vue';
import GroupMemberSelect from './GroupMemberSelect.vue'
import Empty from "@renderer/components/common/Empty.vue";
import SetMemberIdentity from "./SetMemberIdentity.vue"
import { getGroupRingkolCircleSummary } from "@/api/square/ringkolCircle";
import { getBaseUrl } from '@/utils/apiRequest';
import {
  destoryGroupApi,
  imGroupExit,
  disbandGroup,
} from '@/api/im/api'
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();

const emits = defineEmits(['close']);

const msgStore = useMessageStore();
const setting = useSessionSetting();

const { chatingSession } = storeToRefs(msgStore);

  // 部门群全员群和内部群不显示组织名
const showTeamName = computed(() => !['1', '2', '3'].includes(msgStore.chatingSession.relation));

const showCreateGroup = async () => {
  const hasAuth = await setting.onClickAddMember();
  if (hasAuth) {
    const members = [setting.peerInfo.main, setting.peerInfo.peer];
    showDialog('dialogCreateGroup', { selected: members, myCard: setting.peerInfo.main, conversationType: chatingSession.value.conversationType });
  }
};

const openIdentitycard = (member) => {
  console.log('====>openIdentitycard', member);
  ipcRenderer.invoke("identity-card", {
    cardId: member?.attachments?.cardId || member?.attachments?.openId,
    myId: setting.myGroupCard.attachments?.cardId || setting.myGroupCard.attachments?.openId,
    showMoreCard: '',
    comment: member?.attachments?.nickname || member?.attachments?.staffName || ''
  });
};

const needSelectMembers = computed(() => {
  const members = setting.groupInfo?.members ?? [];
  if (msgStore.chatingSession.relation === '23') {
    // 在分组群中，带$符号的是联络人
    return members.filter((v: any) => cardIdType(v?.card?.card_id) === 'inner');
  }
  return setting.groupInfo?.members ?? [];
});


const uploadAvatarRef = ref(null);
const uploadAvatarConfirm = (url) => {
  setting.groupInfo.attachment.avatar = url;
  setting.groupInfo.attachment.isModifyAvatar = true;
  setting.submitGroup();
};
const openUploadAvatar = (url) => {
  console.log('=====>',url);
  if (!url || !url.includes('http')) {
    uploadAvatarRef.value.open()
    return
  }
  let image = new Image();
  image.src = url;
  image.crossOrigin = "*";
  image.onload = function () {
    let canvas = document.createElement("canvas");
    canvas.width = image.width;
    canvas.height = image.height;
    canvas.getContext("2d").drawImage(image, 0, 0, image.width, image.height);
    let base64 = canvas.toDataURL("image/png");
    uploadAvatarRef.value.open(base64)
  }
  image.onerror = () => {
    uploadAvatarRef.value.open(url)
  }
}

// 加人弹窗
const showAddSelect = ref(false);
const groupMemberIds = computed(() => {
  return setting.groupInfo?.members?.map(item => item.openid) ?? [];
})

const selectType = ref('');
const visibleGroupName = ref(false);
const changeGroupType = ref('');

const quitGroup = () => {
  setting.onQuitGroup(!cleanMsg.value);
  alertQuitGroup.value = false;
};

// 公司群和平台全员群不展示群二维码
// const showGroupQRCode = computed(() => ![2, 22].includes(setting.groupInfo?.type));

const getGroupType = () => {
  if (setting.groupInfo?.type === 0) {
    return t('im.public.normalGroup');
  } else if (setting.groupInfo?.type === 1) {
    return t('im.public.departmentGroup');
  } else if (setting.groupInfo?.type === 2) {
    return t('im.public.companyGroup');
  } else if (setting.groupInfo?.type === 3) {
    return t('im.public.internalGroup');
  } else if (setting.groupInfo?.type === 10) {
    return t('im.public.externalGroup');
  } else if (setting.groupInfo?.type === 15) {
    return t('im.public.familyGroup');
  } else if (setting.groupInfo?.type === 20) {
    return t('im.public.platformGroup');
  } else if (setting.groupInfo?.type === 22) {
    return t('im.public.platformAllGroup');
  } else if (setting.groupInfo?.type === 23) {
    return t('im.public.groupGroups');
  }
  return '';
}

// 是否显示踢人: 拥有者和管理员、非拥有者、非本人、非管理员、非公司内部群
const showRemoveMember = (member) => {
  // 公司群不允许删除
  if (!setting.showNotOfComponyPlatallform) {
    return false;
  }

  // 自己不能删除自己
  if (member?.openid === setting.myGroupCard?.openid) {
    return false;
  }

  if(setting.isGroupManager) {
    if (setting.groupInfo?.type === 23 && cardIdType(member?.openid) === 'inner') {
      return false;
    }
    // 管理员不允许删除其他管理员和群主2
    return !member?.roles?.some(r => r === 'ADMIN' || r === 'OWNER');

  } else if (setting.isGroupOwner) {
    if (setting.groupInfo?.type === 23 && cardIdType(member?.openid) === 'inner') {
      return false;
    }
    // 群主不允许删除群主
    return !member?.roles?.some(r => r === 'OWNER');
  }
  return false;
};

const showAddMember = () => {
  // showAddSelect.value = true;
  showDialog('dialogCreateGroup', { selected: groupMemberIds.value, groupType: chatingSession.value.relation, myCard: chatingSession.value.myCardId, groupId: chatingSession.value.targetId, conversationType: chatingSession.value.conversationType });
}

const showGroupManage = ref(false);

const selectGroupMember = ref<string[]>([]);
const memberSearchText = ref('')
const showGroupMembers = ref(false);
const showAddAdmin = ref(false);
const showOwnerTransfer = ref(false);
const groupMembers = computed(() => {
  console.log('=====>',setting.groupInfo?.members);
  if (!setting.groupInfo) {
    return [];
  }

  if (!memberSearchText.value) {
    return setting.groupInfo?.members;
  }

  return setting.groupInfo?.members.filter(item => {
    const staffName = item?.attachments?.staffName ?? '';
    const nickname = item?.attachments?.nickname ?? '';
    return staffName.includes(memberSearchText.value) || nickname.includes(memberSearchText.value);
  })
})

const groupAdminVisible = ref(false);
const groupMemberSelectTitle = ref('');
const groupMemberSelectVisible = ref(false);
const groupMemberSelectMode = ref<'single' | 'multiple'> ();
const groupMemberSelectedCards = ref<string[]>([])
const groupAdmins = computed(() => {
  if (!setting.groupInfo) {
    return [];
  }

  return setting.groupInfo?.members.filter(item => {
    return item.roles?.includes('ADMIN');
  })
})

const onShowAdminAddList = () => {
  groupAdminVisible.value = true;
}

const onShowAdminAdd = () => {
  groupMemberSelectMode.value = 'multiple';
  groupMemberSelectTitle.value = t('im.public.groupAdmin');
  groupMemberSelectVisible.value = true;
  groupMemberSelectedCards.value = setting.groupInfo.members.filter(item => item.roles?.includes('ADMIN')).map(item => item.openid);
}

const onShowOwnerTransfer = () => {
  groupMemberSelectMode.value = 'single';
  groupMemberSelectTitle.value = t('im.public.transferGroup');
  groupMemberSelectVisible.value = true;
  groupMemberSelectedCards.value = [];
}

const onSubmitManage = (val: IGroupMember[]) => {
  console.log(val);
  if (groupMemberSelectMode.value === 'multiple') {
    setting.onAddGroupAdmin(val.map(item => item.openid));

  } else if (groupMemberSelectMode.value === 'single') {
    setting.onTransferGroupOwner(val[0].openid);
  }

  showGroupMembers.value = false;
  showGroupManage.value = false;
}

const onRemoveAdmin  = (val: IGroupMember) => {
  const admins = groupAdmins.value.filter(item => item.openid !== val.openid).map(item => item.openid);
  setting.onAddGroupAdmin(admins);
  showGroupMembers.value = false;
}

const memberSelectchange = (card: string) => {
  if (setting.groupInfo.owner === card) {
    return;
  }
  if (showAddAdmin.value) {
    const index = selectGroupMember.value.findIndex(item => item === card);
    index === -1 ? selectGroupMember.value.push(card) : selectGroupMember.value.splice(index, 1);

  } else if (showOwnerTransfer.value) {
    selectGroupMember.value.pop();
    selectGroupMember.value.push(card);
  }
}

const isEditingGroupName = ref(false);
const groupName = ref('');
const groupNameInput = ref(null);

const isEditingMyGroupName = ref(false);
const myGroupName = ref('');
const myGroupNameInput = ref(null);

// 提示解散群聊警告
const alertDeleteGroup = ref(false);
const alertQuitGroup = ref(false);
const cleanMsg = ref(false);
const showTagManage = ref(false);
const deleteGroupAction = async()=>{
  if(msgStore.chatingSession.relation === '20'){
      const [err, res] = await to(getGroupRingkolCircleSummary(setting.groupInfo.group));
      if(res.data && !res.data.enable){
          // 另可圈未使用，直接提示解散
          alertDeleteGroup.value = true
          return
      }
      // 另可圈在使用中，解散群聊提示使用中
      const myDialog = DialogPlugin({
          header: t('im.public.dialogHeader'),
          theme: "info",
          body: t("im.public.dissolution"),
          className: "dialog-classp32",
          confirmBtn: '知道了',
          onConfirm: () => {
              alertDeleteGroup.value = true
              myDialog.hide();
          },
          onCancel: () => {
              myDialog.hide();
          },
        });

  }else{
    alertDeleteGroup.value = true
  }
}
//解散群聊
const onDeleteGroup = async () => {
  const groupId = setting.groupInfo.group;
  const groupType = setting.groupInfo.type;
  let res;
  if (groupType === 23) {
    if (setting.groupInfo.team) {
      res = await imGroupExit({ team_id: setting.groupInfo.team, group_im: groupId });
    }
  } else if (groupType === 1) {// 部门群
    res = await disbandGroup({ team_id: setting.groupInfo.team, im_group: groupId });
  } else {
    res = await destoryGroupApi(groupId);
  }
    if (res?.data?.code === 0 || res.status === 200) {
        useMessageStore().onDeleteConversation({targetId: groupId});
        setting.reset();
    }
    alertDeleteGroup.value = false
}

const startGroupnameInput = () => {
  if (!setting.isGroupOwner && !setting.isGroupManager) {
    return;
  }
  groupName.value = setting.groupInfo.name
  isEditingGroupName.value = true;
  nextTick(() => {
    groupNameInput.value.focus();
  })
}

const endGroupNameInput = () => {
  isEditingGroupName.value = false;
  const name = groupName.value.trim();
  if (name && name !== setting.groupInfo.name) {
    setting.groupInfo.name = name;
    setting.submitGroup();
  }
  groupName.value = '';
}

const startMyGroupnameInput = (type) => {
  changeGroupType.value = type;
  visibleGroupName.value = true;
  let groupNickName = changeGroupType.value === 'comment'
    ? setting.myGroupCard?.attachments?.comment || ''
    : setting.myGroupCard?.attachments?.nickname || setting.myGroupCard?.attachments?.staffName || '';
  myGroupName.value = groupNickName;
  nextTick(() => {
    myGroupNameInput.value.focus();
  })
}

const endMyGroupNameInput = () => {
  let groupNickName = changeGroupType.value === 'comment'
    ? setting.myGroupCard?.attachments?.comment
    : setting.myGroupCard?.attachments?.nickname || setting.myGroupCard?.attachments?.staffName || '';
  const name = myGroupName.value.trim();
  if (name !== groupNickName) {
    changeGroupType.value === 'comment'
      ? setting.myGroupCard.attachments.comment = name
      : setting.myGroupCard.attachments.nickname = name;
    setting.submitMyCardChange();
  }
  myGroupName.value = '';
  visibleGroupName.value = false;
}

const onDeleteGroupMember = (member) => {
  const confirmDia = DialogPlugin.confirm({
    header: t('im.public.removeTip1'),
    body: t('im.public.removeTip2', [member.attachments?.nickname || member.attachments?.staffName || '']),
    theme: 'warning',
    confirmBtn: {
      content: t('im.public.remove'),
      theme: 'danger'
    },
    onConfirm: async () => {
      confirmDia.destroy();
      setting.onDeleteGrouMember(member);
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
}

const isHelperGroup = (group: IRelationGroup) => {
  return group && [0, 1, 2, 3, 10, 15, 20, 22, 23].includes(group?.type) === false;
}


const groupQrVisible = ref(false);
const qrCodeImg = ref(null);

const saveQrLoading = ref(false);
const onSaveGroupQr = () => {
  // const ele = document.getElementById(`group-qr-${setting.groupInfo.group}`)
  // const dataImg = ele.querySelector('img').src;
  // ipcRenderer.invoke('download-file', { title: `${setting.groupInfo.name}-群二维码.png`, url:  dataImg});

  saveQrLoading.value = true;
  htmlToImage.toPng(qrCodeImg.value).then((dataUrl) => {
    ipcRenderer.invoke("save-base64", dataUrl);
  }).finally(() => {
    saveQrLoading.value = false;
  });
}

const showGroupQrVisible = () => {
  if (Boolean(setting.groupInfo.attachment.hasInviteMember)) {
    const confirmDia = DialogPlugin.alert({
      header: t("im.public.tips"),
      body: t("im.public.qrCodeTip"),
      theme: 'warning',
      confirmBtn: t("im.public.confirm"),
      onConfirm: async () => {
        confirmDia.destroy();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
    return
  }
  groupQrVisible.value = true
}

// 单聊举报
const onGroupReport = () => {
  showDialog('dialogReport', {
    key: setting.groupInfo.group,
    name: setting.groupInfo.name,
    source: 2,
  })
}

// 群聊举报
const onPrivateReport = () => {
  showDialog('dialogReport', {
    key: setting.privatePeer.cardId,
    name: setting.privatePeer.staffName || setting.privatePeer.nickname,
    source: 2,
  })
}

// 平台群显示设置群身份按钮， 仅限群主 + 群管理 看到设置身份操作入口
const showSetIdentity = () =>{
  return setting.groupInfo?.type === 20 && (setting.isGroupManager || setting.isGroupOwner)
}
const isSetIdentity = ref(false)
// 打开群身份设置窗口
const openSetIdentity = () => {
  showGroupMembers.value = true
  isSetIdentity.value = true
}
const showSetIdentityVisible = ref(false)
const chosenMember = ref()
const chooseIdentity = (member:IGroupMember) =>{
  // 打开设置群身份窗口
  chosenMember.value = member
  showSetIdentityVisible.value = true
}
const openGroupManage = () => {
  showGroupManage.value = true;
}

defineExpose({
  openGroupManage
})

</script>

<style lang="less" scoped>
@import "./style/setting.less";

.close-btn{
    width: 16px;
    height: 16px;
}

.avatar-group {
  position: relative;
}

.group-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;

  & .svg-size20 {
    color: var(--kyy_color_brand_default, #4D5EFF);
  }
}
.closeicon:hover{
  background-color: rgb(243, 243, 243);
  border-radius: 4px;
}
.group-name {
  flex:1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--text-kyy_color_text_1, #1A2139);
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  width: 100%;
}

.group-type {
  color: var(--text-kyy_color_text_3, #828DA5);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  margin-top: 2px;
}

.avatar-btn {
  visibility: hidden;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  & .svg-size20 {
    color: #fff;
  }
}

.avatar-group:hover .avatar-btn {
  visibility: visible;
}

.select {
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: #F1F2F5;
    border-radius: 4px;
  }
}

.waring {

  &:hover {
    color: @kyy_red_4;
  }
}
.set-labelId{
  width: fit-content;
  text-align: center;
  padding:0 12px;
  margin:0 0 12px 12px;
  line-height: 20px;
  border-radius: var(--radius-kyy_radius_button_s, 4px);
  border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4D5EFF);
  background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #EAECFF);
  color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4D5EFF);
  cursor: pointer;
  white-space: nowrap;

}
.group-members {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin-bottom: 16px;
    max-height: 105px;
    overflow: hidden;
    gap: 17px;
    padding: 0 12px;
}

.member-name {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    font-size: 12px;
}

.group-member {
  position: relative;
  overflow: hidden;

  .remove-icon {
    cursor: pointer;
    width: 16px;
    height: 16px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    position: absolute;
    top: 0px;
    right: 0px;
    visibility: hidden;


  }
  .remove {
    position: absolute;
    top: 0;
    right: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &:hover .remove-icon {
    visibility: visible;
  }
}

.add {
  border-radius: 50%;
  border: 1px solid #E3E6EB;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0.8;

  &:hover {
    opacity: 1;
  }
}

.members-left{
  display: flex;
  align-items: center;
  flex-grow: 1;
  overflow: hidden;
}
.members {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 12px;
  font-size: 14px;
  line-height: 22px;
  position: relative;
  overflow: hidden;

  &:hover {
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
  }

  .member-delete {
    position: absolute;
    margin: auto 0;
    width: 24px;
    height: 24px;
    right: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    display: none;
  }

  &:hover .member-delete {
    display: inline;

    &:hover {
      opacity: 0.8;
    }
  }
  .set-labelId{
    margin:0 20px 0 0;

  }
}

.member-labelId{
  color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 157.143% */
  padding:0 12px;
  margin-right: 20px;
  border-radius: var(--radius-kyy_radius_button_s, 4px);
  border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
  background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
  white-space: nowrap;
}
.qr-dialog .t-dialog {
  padding: 32px !important;
}
.qr-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
}

:global(.qr-dialog .t-dialog__body) {
  padding: 0;
}

:global(.qr-dialog .t-dialog__header) {
  position: absolute;
  top: 8px;
  right: 32px;
  z-index: 9;
}

.group-qr-box {
  color: var(--text-kyy-color-text-1, #1A2139);
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  padding:4px;
  background-color: #FFFFFF;


  /* 150% */
  .qr-header {
    // padding-top:7px;
    padding-bottom: 10px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    background-color: #FFFFFF;
  }

  .group-qr {
    padding: 16px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

.group-qr {
  display: flex;
  padding: 16px 64px;
  background-color: #F5F8FE;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  height:176px;
}

.drawer-content {
  padding: 0 12px;
}
.members-name{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tag-team {
  line-height: 20px;
  font-size: 12px;
  font-weight: 400;
  padding: 0px 4px;
  margin-left: 4px;
  white-space: nowrap;
  text-align: center;
  border-radius: var(--kyy_radius_tag_s, 4px);
  color: var(--kyy_color_tag_text_warning, #FC7C14);
  background: var(--kyy_color_tag_bg_warning, #FFE5D1);
}

.tag-role {
  line-height: 20px;
  font-size: 12px;
  font-weight: 400;
  padding: 0px 4px;
  margin-left: 4px;
  text-align: center;
  border-radius: var(--kyy_radius_tag_s, 4px);
  color: var(--kyy_color_tag_text_brand, #4D5EFF);
  background: var(--kyy_color_tag_bg_brand, #EAECFF);
  white-space: nowrap;

}

.delete {
  color: var(--kyy_color_error_default, #D54941);
}

.admin-add {
  font-weight: 400;
  color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF);
  cursor: pointer;
  & .svg-size20 {
    color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF);
  }

  &:hover {
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
  }
}
.t-button .t-button__text{
  font-weight:normal !important;
}
:deep(.qr-dialog .t-button__text){
  font-weight:normal !important;
}
.iconhover{
  border-radius: 4px;
  width:24px;
  height:24px;
  display:flex;
  align-items:center;
  justify-content:center;
  text-align:center;
  img{
    line-height:16px;
  }
}

.iconhover:hover{
  background: #F3F6FA;
}
</style>
