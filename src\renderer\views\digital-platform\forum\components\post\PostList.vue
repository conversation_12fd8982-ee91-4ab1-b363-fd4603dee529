<template>
  <div>
    <div v-if="ownerId && showTotal" class="py-[8px] flex items-center gap-[8px]">
      <h2 class="text-[#1A2139] text-[14px]">全部帖子</h2>
      <span class="text-[#A1A2A4] text-[14px]">({{ total }})</span>
    </div>

    <div :class="['trends-list', { empty: !dataList.length && loaded }]">
      <div
        v-if="!dataList.length && loaded"
        class="empty-container rounded-[8px] border-[1px] border-style-solid border-[#eceff5]"
      >
        <Empty name="no-friend-list" :tip="$t('forum.userPostEmpty')">
          <template v-if="emptyPublish" #bottom>
            <t-button class="mt-12 min-w-80" @click="emit('publish')">去发帖</t-button>
          </template>
        </Empty>
      </div>

      <div v-if="initDataLoading" class="list-loading">
        <t-loading size="small" text="加载中..." />
      </div>

      <template v-if="!initDataLoading">
        <div class="flex flex-col gap-[8px] w-full">
          <template v-for="(item, index) in dataList" :key="item.post.id + item.post.timelineNewPosts">
            <t-divider v-if="item.isLastQuery" class="visited-flag">{{ $t('square.post.lastQuery') }}</t-divider>

            <div v-if="!goDetail && showTime && item.year" class="year">{{ item.year }}年</div>

            <div :class="['w-full', { flex: !goDetail }]">
              <PostItem
                :key="item.post.id"
                :data="item"
                :has-top-post="hasTop"
                :go-detail="goDetail"
                in-list
                :prev-time="dataList[index - 1]?.post.postedAt"
                :hide-top="hideTop"
                :can-head-click="canHeadClick"
                :show-flag="showFlag"
                :hide-setting="hideSetting"
                :self="self"
                @removed="emit('removed')"
                @toggle-top="toggleTop"
                @click-head="clickHead"
                @init="init"
                @click-content="(e) => clickContent(e, index)"
                @toolbar-change="(type) => toolbarClick(type, item, index)"
              />
            </div>
          </template>

          <t-divider
            v-if="loadingMore === 'finished' && dataList.length && !hideNoMore"
            name="finished"
            class="px-80 my-16! color-text-2"
          >
            {{ $slots?.finished || $t('components.infiniteLoading.noMoreData') }}
          </t-divider>

          <t-loading
            v-if="loadingMore === 'loading'"
            name="spinner"
            :text="$t('components.infiniteLoading.loading')"
            size="small"
          />

          <span v-if="loadingMore === 'error'" name="error" class="state-error">
            <span>{{ $slots?.error || $t('components.infiniteLoading.hasErr') }}</span>
            <t-button variant="outline" class="flex-center mt-8">{{ $t('components.infiniteLoading.retry') }}</t-button>
          </span>
        </div>
      </template>

      <PostDetail
        v-if="detailVisible"
        :id="detailId"
        v-model="detailVisible"
        :scroll-to-input="['comment'].includes(defaultToolbar)"
        :default-toolbar="defaultToolbar"
        :can-head-click="canHeadClick"
        :self="self"
        @removed="emit('removed')"
        @click-head="clickHead"
        @refresh-item="() => {}"
        @toggle-top="toggleTop"
        @count-change="countChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref, watch, defineExpose } from 'vue';
import { useRouter } from 'vue-router';
import { AxiosResponse } from 'axios';
import to from 'await-to-js';
import { getPostList } from '@renderer/api/forum/post';
import PostItem from './PostItem.vue';
import { useSquareStore } from '@/views/square/store/square';
import Empty from '@/components/common/Empty.vue';
// import { useRefreshListItem } from '@/views/square/components/post/hooks';
import PostDetail from './PostDetail.vue';
import { getUserStats } from '@renderer/api/forum/user';

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  lastQueryTime: {
    type: String,
    default: '',
  },
  goDetail: {
    type: Boolean,
    default: true,
  },
  api: {
    type: Function,
    default: getPostList,
  },
  params: {
    type: Object,
    default: () => ({
      from: '1',
    }),
  },
  showComplete: {
    type: Boolean,
    default: true,
  },
  showTime: {
    type: Boolean,
    default: true,
  },
  hideTop: Boolean,
  hideNoMore: Boolean,
  from: {
    type: String,
    default: '',
  },
  canHeadClick: {
    type: Boolean,
    default: true,
  },
  showFlag: Boolean,
  // 置顶后是否刷新列表
  toggleTopRefresh: {
    type: Boolean,
    default: true,
  },
  // 隐藏设置
  hideSetting: Boolean,
  // 是否是自已的帖子
  self: Boolean,
  // 无数据时，是否允许显示发布按钮
  emptyPublish: Boolean,
  // 用于查询统计数量
  ownerId: String,
  // 是否显示帖子数量
  showTotal: Boolean,
});
const emit = defineEmits([
  'click-head',
  'removed',
  'load',
  'toggle-top',
  'publish',
  'loadTop',
  'on-show',
  'refresh',
  'publish',
]);

const store = useSquareStore();
const router = useRouter();

const clickHead = (item) => {
  console.log(item);
  emit('click-head', item);
  router.push({
    name: 'forumPostSub',
    query: {
      from: '3',
      target_owner_id: item.owner.id,
      avatar: item.owner.avatar,
      name: item.owner.name,
      remark: item.owner.remark,
      cardId: item.owner.cardId,
      openid: item.owner.openid,
    },
  });
};

// 查看详情
const detailId = ref('');
const detailIndex = ref(0);
const detailVisible = ref(false);

const loadingMore = ref('loaded');
const onLoading = async () => {
  if (loadingMore.value === 'finished') return;
  loadingMore.value = 'loading';
  if (loading.value) return;
  await load();
};

const clickContent = async (item, index: number) => {
  detailVisible.value = true;
  detailId.value = item.post.id;
  detailIndex.value = index;
};

const postMapper = (v, i) => {
  // 记录上次看到哪里
  if (lastQuery && +new Date(v.post.postedAt) < +new Date(lastQuery)) {
    v.isLastQuery = true;
    // 最新一条看过则不显示
    if (i === 0 && currentPage.value === 1) {
      v.isLastQuery = false;
    }
    lastQuery = '';
  }

  // 非当年的其它年份，其年份内第1条动态时间上显示年份
  const year = new Date(v.post.postedAt).getFullYear();
  if (year < currYear && !visitYears.has(year)) {
    v.year = year;
    visitYears.add(year);
  }

  // 高亮
  // v.highlightText = props.highlightText ? highlight(v.post.text, props.highlightText) : v.post.text;
  // if (v.forwardPost?.post?.highlightText) {
  //   const fText = v.forwardPost.post.highlightText;
  //   v.forwardPost.post.highlightText = props.highlightText ? highlight(fText, props.highlightText) : fText;
  // }

  // 提示以下内容对他人不可见
  // const postVisibleDays = store.squareInfo.square.postVisibleDays;
  // if (!postVisibleTip && postVisibleDays > 0 && moment().subtract(postVisibleDays, 'days').isAfter(v.post.postedAt) && !v.stickOnTop) {
  //   postVisibleTip = true;
  //   v.postVisibleTip = true;
  // }

  // if (!v.square) v.square = props.square;

  return {
    ...v,
    isSelfSquare: store.isSelfSquare(v.post.squareId),
  };
};

// 好友圈列表
const pageSize = ref(20);
const dataList = ref([]);
const hasTop = ref(false);
const currentPage = ref(0);
const initDataLoading = ref(true);
let nextPageToken = '';
let lastQuery = '';
// let postVisibleTip = false;
const loaded = ref(false);

const currYear = new Date().getFullYear();
const visitYears = new Set([]);

const offlineEmpty = ref(false);

// const { refreshItem } = useRefreshListItem(dataList);

// 重复代码，待优化
const fetchNew = async () => {
  const params = { 'page.size': pageSize.value, 'page.next_page_token': nextPageToken };
  // 二级好友圈列表
  // if (route.query.id) params['square_id'] = route.query.id;

  currentPage.value++;
  loading.value = true;
  const [err, res] = await to<AxiosResponse, Error>(props.api({ ...params, ...props.params }));
  loading.value = false;
  if (err) {
    console.log(err);
    loadingMore.value = 'error';
    return;
  }

  const data = res.data.data;
  emit('load', data);
  const { page, hasTopPost } = data;
  const posts = data.posts.map((item) => ({ ...item, prevPageToken: page.prevPageToken }));
  nextPageToken = page.nextPageToken;
  lastQuery = data.lastQueryTime || lastQuery;
  const addList = posts.map(postMapper);
  hasTop.value = hasTopPost;
  dataList.value = [...addList];

  await nextTick();
  if (dataList.value.length < pageSize.value) {
    loadingMore.value = 'finished';
  }
};

// 获取帖子数量统计
const total = ref(0);
const getStats = async () => {
  if (!props.ownerId) return;
  // 置顶帖子
  if (props.params.pin === 'true') {
    total.value = dataList.value.length;
    return;
  }

  const [err, res] = await to(getUserStats(props.ownerId));
  if (err) return;

  total.value = res.data.data.posts;
};

const init = async () => {
  initDataLoading.value = true;
  // if (!dataList.value.length && props.cacheKey) {
  //   dataList.value = [...restoreFromCache().map(postMapper)];
  // }
  await fetchNew();
  loaded.value = true;
  initDataLoading.value = false;

  getStats();
};

onMounted(() => {
  init();
});

const loading = ref(false);

// 上滑加载更多
const load = async () => {
  let posts = [];

  if (loading.value) return;
  loading.value = true;

  const params = { 'page.size': pageSize.value, 'page.next_page_token': nextPageToken };
  // 二级好友圈列表
  // if (route.query.id) params['square_id'] = route.query.id;

  currentPage.value++;
  const [err, res] = await to<AxiosResponse, Error>(props.api({ ...params, ...props.params }));
  loading.value = false;
  if (err) {
    console.log(err);
    loadingMore.value = 'error';
    return;
  }

  const { data } = res.data;
  emit('load', data);

  offlineEmpty.value = false;
  // emit('offline-empty', false);

  const { page } = data;
  posts = data.posts.map((item) => ({ ...item, prevPageToken: page.prevPageToken }));
  nextPageToken = page.nextPageToken;
  lastQuery = data.lastQueryTime || lastQuery;

  const addList = posts.map(postMapper);

  if (loaded.value) dataList.value.push(...addList);
  else {
    dataList.value = [...addList];
    // cacheFirstPage(dataList.value);
  }

  loaded.value = true;
  if (posts.length < pageSize.value || !nextPageToken) {
    loadingMore.value = 'finished';
  } else {
    loadingMore.value = 'loaded';
  }
};

// 二级页面动态更新后更新好友圈对应数据
// if (props.from === 'friend-circle') {
//   bus.on((e, postData) => {
//     if (e.name === 'post-item-refresh') {
//       const post = postData.post;
//       const idx = dataList.value.findIndex((v) => v.post.id === post.id);
//       refreshItem(postData.post.id, null, idx);
//     }
//   });
// }

// 打开对应的详情页
const defaultToolbar = ref('');
const toolbarClick = (type, post, index) => {
  defaultToolbar.value = type;

  // 点赞不进详情
  if (type === 'like') return;
  clickContent(post, index);
};

watch(
  () => detailVisible.value,
  (val) => {
    if (!val) {
      defaultToolbar.value = '';
    }
  },
);

// 更新主页列表中的广场号信息
// watch(() => props.square, (val) => {
//   if (!val) return;
//   dataList.value = dataList.value.map((v) => ({
//     ...v,
//     square: Object.keys(v.square).length ? v.square : val,
//   }));
// }, { deep: true });

const toggleTop = () => {
  emit('toggle-top');
  if (!props.toggleTopRefresh) return;

  dataList.value = [];
  loaded.value = false;
  currentPage.value = 0;
  nextPageToken = '';
  // postVisibleTip = false;
  // emit('refresh');
  init();
};

// 更新统计数
const countChange = (data: { comments: number; likes: number }, payload) => {
  const idx = dataList.value.findIndex((v) => v.post.id === detailId.value);
  if (idx > -1) {
    dataList.value[idx].post.comments = data.comments;
    dataList.value[idx].post.likes = data.likes;
    if (payload?.liked !== undefined) {
      dataList.value[idx].liked = payload?.liked;
    }
  }
};

defineExpose({
  dataList,
  loading: onLoading,
  status: loadingMore,
});
</script>

<style lang="less" scoped>
.scrollbar(0);

.empty-container {
  width: 100%;
  flex: 1;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  // margin-bottom: -100px;
}

.trends-list {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  overflow-y: auto;
  border-radius: 8px;
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 252px);

  &.empty {
    height: auto;
    min-height: calc(100vh - 341px);
  }
}

.visited-flag {
  width: 416px;
  color: #a1a2a4;
  margin: 16px auto;
  &::before,
  &::after {
    border-top-color: var(--kyy_color_divider_light, #eceff5);
  }
  :deep(.t-divider__inner-text) {
    color: var(--text-kyy-color-text-2, #516082);
  }
}

.year {
  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 18px;
  font-weight: 600;
  line-height: 26px; /* 144.444% */
}

.list-loading {
  width: 100%;
  display: flex;
  justify-content: center;
}

.loading-tip {
  width: 100%;
  display: flex;
  justify-content: center;
  &:empty {
    display: none;
  }
}
</style>
