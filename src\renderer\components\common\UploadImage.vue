<script setup lang="ts">
import { computed, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import { useImageUploadWithProgress } from '@/views/square/hooks/upload';
import { onUploadValidate } from '@/views/square/utils/upload';
import LynkerSDK from '@renderer/_jssdk';
import {MessagePlugin, DialogPlugin} from "tdesign-vue-next";

interface Props {
  /** 上传根目录 */
  rootDir: string;
  /** 绑定的图片URL */
  modelValue?: string | string[];
  /** 最大上传数量，默认为1 */
  maxCount?: number;
  /** 接受的文件类型 */
  accept?: string;
  /** 文件大小限制 */
  sizeLimit?: {
    size: number;
    unit: 'KB' | 'MB';
    message: string;
  };
  /** 自定义预览图片。用于在多个上传组件中，统一预览图片（若不传，默认处理组件内的图片预览） */
  preview?: {
    images: string[];
    index: number;
    url: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  maxCount: 1,
  accept: 'image/png,image/jpg,image/jpeg',
  sizeLimit: () => ({
    size: 5,
    unit: 'MB',
    message: '文件大小不能超过{sizeLimit}MB',
  }),
});

const emit = defineEmits(['update:modelValue', 'change']);

const modelVal = useVModel(props, 'modelValue', emit, {
  passive: true,
  defaultValue: [],
});

const initialImages = computed(() => {
  if (props.maxCount === 1 && typeof props.modelValue === 'string') return [{ url: props.modelValue }];
  if (Array.isArray(props.modelValue)) return props.modelValue.map(url => ({ url }));
  return [];
});

const { images, uploadImage, removeImage: _removeImage, uploadProgress } = useImageUploadWithProgress((_, fileList) => {
  images.value = fileList || [];
  modelVal.value = (fileList || []).map(v => v.url);

  emit('change', modelVal.value);
}, { rename: true, rootDir: props.rootDir });

const onSelectChange = (files) => {
  console.log(111, files, files?.length)
  if (files?.length + images.value?.length > props.maxCount) {
    MessagePlugin.error(`最多支持上传${props.maxCount}张图片`);
    return false;
  }
}
// 初始化图片
watch(() => props.modelValue, (newVal) => {
  if (!newVal) {
    images.value = [];
    modelVal.value = props.maxCount === 1 ? undefined : [];
    return;
  }

  if (props.maxCount === 1 && typeof newVal === 'string') {
    images.value = [{ url: newVal }];
  } else if (Array.isArray(newVal)) {
    images.value = newVal.map(url => ({ url }));
  }
}, { immediate: true });

// 重写 removeImage 方法，确保删除时同步更新父组件数据
const removeImage = (index: number) => {
  if (!images.value || !Array.isArray(images.value)) {
    images.value = [];
    modelVal.value = props.maxCount === 1 ? undefined : [];
    return;
  }

  _removeImage(index);
  modelVal.value = props.maxCount === 1
    ? images.value[0]?.url
    : images.value.map(v => v?.url).filter(Boolean);
};

// 预览图片
const previewImage = (index: number, url: string) => {
  if (props.preview) {
    LynkerSDK.previewImage(props.preview);
    return;
  }

  LynkerSDK.previewImage({
    images: images.value.map(v => v?.url),
    index,
    url,
  });
};
</script>

<template>
  <div class="uploader">
    <div class="wrap">

      <t-upload
        v-if="!images?.length || images.length < props.maxCount"
        ref="uploadRef"
        v-model="images"
        theme="custom"
        :accept="props.accept"
        :multiple="props.maxCount > 1"
        :abridge-name="[6, 6]"
        :max="props.maxCount"
        :request-method="uploadImage"
        :size-limit="props.sizeLimit"
        v-bind="$attrs"
        allow-upload-duplicate-file
        @validate="onUploadValidate"
        :onSelectChange="onSelectChange"
      >
        <slot>
          <div class="img-wrap btn-upload">
            <iconpark-icon name="iconimg" class="icon" />
            <p>{{ $t('square.upload.clickUpload') }}</p>
          </div>
        </slot>
      </t-upload>
      <template v-if="images?.length">
        <div v-for="(item, index) in images" :key="index" class="img-wrap">
          <t-image v-if="item?.url" :src="item?.url" fit="cover" class="img" @click="previewImage(index, item?.url)" />
          <div v-else-if="!uploadProgress"  class="upload-progress">
            <t-loading size="small" />
            <p>上传中...</p>
          </div>
          <iconpark-icon name="iconclean" class="btn-close" fill="#fff" @click="removeImage(index)" />

          <div v-if="uploadProgress > 0 && index === images.length - 1" class="upload-progress">
            <t-loading size="small" />
            <p>{{ uploadProgress }}%</p>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="less" scoped>
.uploader {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  .wrap{
    display: flex;
  }
  .img-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 78px;
    height: 78px;
    margin-right: 4px;
    border: 1px solid #d5dbe4;
    border-radius: 8px;
    position: relative;
    &:hover .btn-close {
      opacity: 1;
    }
    .img {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }
    .btn-close {
      opacity: 0;
      position: absolute;
      top: 2px;
      right: 2px;
      font-size: 16px;
      cursor: pointer;
      color: #828da5;
      z-index: 1;
      box-shadow: 0 0 1.6px rgba(#fff, 0.16);
      border-radius: 50%;
    }
    .upload-progress {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1;
      display: flex;
      gap: 4px;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--kyy_color_upload_text_default, #516082);
      font-size: 12px;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
    }
    .play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      z-index: 1;
    }
  }
  .icon {
    font-size: 24px;
    color: #97989a;
  }
  .btn-upload {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 78px;
    height: 78px;
    border-radius: 8px;
    border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
    background: var(--kyy_color_upload_bg, #fff);
    font-size: 12px;
  }
  .desc {
    color: var(--text-kyy-color-text-3, #828da5);
    font-size: 12px;
  }
  .max-count {
    color: @kyy_orange_6;
    font-size: 12px;
  }
}
</style>
