<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import to from 'await-to-js';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { AxiosError, AxiosResponse } from 'axios';
import { ErrorResponseReason } from '@renderer/views/square/enums';
import { CommentDetail, CommentType, ResourceType } from '@renderer/api/forum/models/comment';
import { addComment, deleteComment, likeToggle } from '@renderer/api/forum/comment';
import { useRouter } from 'vue-router';
import LikeUndoIcon from '@renderer/assets/like-undo.svga?url';
import LikeIcon from '@renderer/assets/like.svga?url';
import { useSVGA } from '@renderer/components/common/like-animated/useSVGA';
import EmojiSelector from '@/views/square/components/EmojiSelector.vue';
import { DATE_TIME_FORMAT, formatDateTime } from '@/utils/date';
import { errReasonMap } from '@/views/square/constant';
import Avatar from '@/components/kyy-avatar/index.vue';
import { useForumStore } from '../../store';
import UserSelect from '../UserSelect.vue';
import { AtDOM } from '@renderer/views/digital-platform/forum/components/mentionEditor/mention';
import MentionEditor from '../mentionEditor/editor.vue';
import { Flag } from '@renderer/api/forum/models/post';

const props = withDefaults(
  defineProps<{
    // openid: string;
    owner: any;
    commentTypes?: number;
    // 资源id（与资源类型匹配的id）
    resourceId: string;
    // 资源类型
    // resourceType: ResourceType;
    // 评论详情
    item: CommentDetail;
    // 是否存在置顶的评论
    hasStickOnTop?: boolean;
    // 是否是回复评论
    isReply?: boolean;
    // 是否聚焦输入框
    focus?: boolean;
    isScrolling?: boolean;
  }>(),
  {
    commentTypes: 0,
  },
);

const emit = defineEmits<{
  (e: 'success', payload: any): void;
  (e: 'toggle-top', commentId: string): void;
  (e: 'removed', commentId: string): void;
  (e: 'input-visible', visible: boolean): void;
}>();

const { t } = useI18n();
const router = useRouter();
const forumStore = useForumStore();
const emojiVisible = ref(false);

// 动态作者
const owner = computed(() => props.owner || {});
// 发布者
const isSelfPost = computed(() => owner.value?.id === forumStore.currCard.id);
// 回复的评论id
const replyCommentId = computed(() => props.item.comment.id);
const hasMultiCards = computed(() => forumStore.cardList.length > 1);

const mentionEditor = ref<InstanceType<typeof MentionEditor>>(null);

const disabledMention = ref(false);

// 当前评论的所有者（发布评论时使用）
const postOwner = ref({ ...forumStore.postOwner });
const userChange = (val) => {
  postOwner.value = val;
};

const liked = ref(props.item.liked);
const likes = ref(props.item.comment.likes || 0);

// 点赞过渡动画
const likeIconRef = ref(null);
const { isAnimating, startAnimation } = useSVGA(likeIconRef, {
  like: LikeIcon,
  unlike: LikeUndoIcon,
});

// 评论操作
const dropdownActions = computed(() => {
  const { item } = props;
  const result = [];
  // if (isSelfPost.value && !props.isReply) {
  //   result.push({
  //     content: item.stickOnTop ? t('square.action.cancelTop') : t('square.post.topComment'),
  //     value: 1,
  //   });
  // }

  // if (!item.isSelfComment && (store.isPersonal || isFromOuter)) {
  //   result.push({ content: '举报', value: 3 });
  // }

  // 发布者可删除所有评论；他人可删除自己的评论
  if (isSelfPost.value || item.isSelfComment) {
    result.push({ content: '删除', value: 2, theme: 'error' });
  }

  // 加分割线
  if (result.length > 1) {
    result[result.length - 2].divider = true;
  }

  return result;
});

const formData = reactive({
  content: '',
  show: false,
});

watch(
  [postOwner, mentionEditor],
  ([owner]) => {
    if (owner.flag === Flag.Anonymous) {
      disabledMention.value = true;
      mentionEditor?.value?.clearMentions();
    } else {
      disabledMention.value = false;
    }

    nextTick(() => {
      mentionEditor?.value?.focus();
    });
  },
  {
    immediate: true,
  },
);

// 回复评论
const toReply = async () => {
  formData.show = !formData.show;
  emit('input-visible', formData.show);

  await nextTick();
  mentionEditor.value.focus();
  const $input = mentionEditor.value.$el;
  console.log('===', $input, mentionEditor.value.$el);
  $input.scrollIntoView({ behavior: 'smooth', block: 'start' });
};

const onSelectReplyEmoji = (e) => {
  mentionEditor.value.insertNode(e.i);
};
const mentionPop = (event) => {
  // 获取鼠标位置
  let mouseY = event.clientY;
  // let mouseX = event.clientX;
  mouseY = mouseY > 360 ? 360 : mouseY;
  mentionEditor.value.showMentionModal(false, { x: 300, y: mouseY });
};
const hasTextContent = ref(false);
const disableChange = (data) => {
  hasTextContent.value = data;
};

// 回复评论
const replying = ref(false);
const doReply = async () => {
  if (replying.value) return;

  replying.value = true;
  const textData = mentionEditor.value?.getContents();
  const at = textData[0]?.atInfo;
  const content = textData[0]?.text;
  const [err, res] = await to<AxiosResponse, AxiosError<{ reason: string }>>(
    addComment({
      comment: {
        content,
        resourceId: props.resourceId,
        replyTo: replyCommentId.value,
        // ownerId: forumStore.postOwner?.id,
        ownerId: postOwner.value?.id,
        // ipRegion: store.ipInfo ? JSON.stringify(store.ipInfo) : '',
        at,
      },
    }),
  );
  replying.value = false;

  if (err) {
    const errReason = err.response.data.reason;
    if (
      [
        ErrorResponseReason.PostDeleted,
        ErrorResponseReason.PostInvisibility,
        ErrorResponseReason.PostCommentClosed,
      ].includes(errReason as ErrorResponseReason)
    ) {
      await MessagePlugin.warning(`${errReasonMap[errReason]}, 评论未发送`);
      return;
    }

    if (ErrorResponseReason.CommentDeleted === errReason) {
      await MessagePlugin.warning(`${errReasonMap[errReason]}, 回复未发送`);
      return;
    }

    await MessagePlugin.warning(errReasonMap[errReason]);
    return;
  }

  await MessagePlugin.success(t('square.post.commentSuccess'));
  formData.show = false;
  mentionEditor.value.clear();
  addLocalReply(replyCommentId.value, res.data.data.commentId, content, res.data.data.comment?.comment?.at);
};

// 添加回复时，本地添加
const addLocalReply = (parentId: string, id: string, content: string, at = []) => {
  const replayItem: CommentDetail = {
    // owner: forumStore.postOwner,
    owner: postOwner.value,
    comment: {
      id,
      content,
      // status: CommentStatus.Normal,
      // commentType: CommentType.Reply,
      // parentComment: parentId,
      likes: 0,
      replyTo: parentId,
      createdAt: formatDateTime(new Date(), DATE_TIME_FORMAT),
      // ipRegion: ipInfo.value?.province ? shortProvince(ipInfo.value?.province) : '',
      at,
    },
    liked: false,
    // isSelfPost: owner.value?.id === forumStore.postOwner.id,
    isSelfPost: owner.value?.id === postOwner.value.id,
    isSelfComment: true,
  };
  // liked.value = false;
  // likes.value = 0;

  // 显示回复哪条评论回复
  if (props.item.comment.commentType === CommentType.Reply) {
    replayItem.replyComment = {
      comment: props.item.comment,
      owner: props.item.owner,
    };
  }

  formData.content = '';
  formData.show = false;
  emit('success', replayItem);
};

// 评论相关操作
const doAction = (action) => {
  if (action.value === 2) {
    toDelComment();
  }
};

// 删除评论
const toDelComment = () => {
  const { item } = props;
  const confirmDia = DialogPlugin.confirm({
    header: t('square.post.commentTip'),
    body: item.children?.length ? t('square.post.commentTip1') : '',
    theme: 'warning',
    confirmBtn: {
      content: t('square.action.remove'),
      theme: 'danger',
    },
    cancelBtn: t('square.action.cancel'),
    onConfirm: async () => {
      confirmDia.destroy();
      const [err] = await to(deleteComment(item.comment.id));
      if (err) return;

      await MessagePlugin.success(t('square.delSuccessTip'));
      emit('removed', item.comment.id);
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

// 点赞
const setLike = async (item: CommentDetail) => {
  startAnimation(liked.value ? 'unlike' : 'like');

  const [err, res] = await to(
    likeToggle({
      resourceId: item.comment.id,
      resourceType: ResourceType.Comment,
      // ownerId: forumStore.postOwner.id,
      ownerId: postOwner.value.id,
    }),
  );
  if (err) return;

  liked.value = res.data.data.like;
  likes.value = res.data.data.likes;
};

const clickHead = (item) => {
  router.push({
    name: 'forumPostSub',
    query: {
      from: '3',
      target_owner_id: item.owner.id,
      avatar: item.owner.avatar,
      name: item.owner.name,
    },
  });
};

const commentRef = ref(null);
defineExpose({
  scrollIntoView: async () => {
    formData.show = true;
    await nextTick();

    commentRef.value.scrollIntoView({ behavior: 'smooth', block: 'start' });

    setTimeout(() => {
      mentionEditor.value.focus();
    }, 400);
  },
});

watch(
  () => props.isScrolling,
  (val) => {
    if (val) {
      emojiVisible.value = false;
    }
  },
);
</script>

<template>
  <div v-if="item" ref="commentRef" class="comment-item" :class="{ focus }">
    <Avatar
      avatar-size="32px"
      :image-url="item.owner?.avatar ?? ''"
      :user-name="item.owner?.remark || item.owner?.name"
      round-radius
      class="flex-self-start mr-8 cursor"
      @click="clickHead(item)"
    />

    <div class="right-content gap-4">
      <div class="flex items-center w-full">
        <div class="name" @click="clickHead(item)">{{ item.owner?.remark || item.owner?.name }}</div>
        <span v-if="item.isSelfPost" class="author-tag">
          {{ $t('square.post.author') }}
        </span>
        <span v-if="commentTypes === 1 && item.comment.ipRegion" class="from-address">
          {{ $t('square.post.from') }}{{ item.comment.ipRegion }}
        </span>
      </div>

      <div class="content">
        <AtDOM :text="item.comment.content" :atInfos="item.comment.at"></AtDOM>
      </div>

      <div v-if="item.replyComment" class="reply">
        <div class="symbol" />
        <template v-if="item.replyComment.deleted">{{ $t('square.post.commentDeleted') }}</template>
        <template v-else>{{ item.replyComment.owner?.name }}:{{ item.replyComment.comment?.content }}</template>
      </div>

      <div class="footer">
        <div class="time">
          <span>{{ item.comment.createdAt }}</span>
          <span v-if="commentTypes === 0 && item.comment.ipRegion">
            {{ $t('square.post.from') }}{{ item.comment.ipRegion }}
          </span>
          <!-- <t-link theme="primary" hover="color" @click="toReply">{{ $t('square.post.reply') }}</t-link> -->
        </div>

        <div class="toolbar">
          <div :class="['like-wrap', { active: liked }]" @click="setLike(item)">
            <div v-show="isAnimating" ref="likeIconRef" class="w-20 h-20" />
            <template v-if="!isAnimating">
              <iconpark-icon v-if="liked" name="iconlikefill2" class="icon icon-liked" />
              <iconpark-icon v-else name="iconlike" class="icon" />
            </template>
            <template v-if="likes">{{ likes }}</template>
          </div>
          <div class="like-wrap" @click="toReply">
            <iconpark-icon name="reply" class="icon" />
          </div>
          <t-dropdown
            v-if="dropdownActions.length"
            class="action-wrap"
            :min-column-width="136"
            :options="dropdownActions"
            @click="doAction"
          >
            <iconpark-icon name="iconmore" class="icon" />
          </t-dropdown>
        </div>
      </div>

      <t-tag v-if="item.stickOnTop" theme="primary" shape="round" variant="light" class="font-bold cursor-pointer">
        {{ $t('square.post.topComment') }}
      </t-tag>

      <div v-show="formData.show" class="input-container">
        <UserSelect
          isolation
          placement="bottom-left"
          :disabled="!hasMultiCards"
          :default-value="postOwner"
          :class="['inline-flex!', { cursor: hasMultiCards }]"
          @change="userChange"
        >
          <div class="avatar-wrap">
            <Avatar
              avatar-size="32px"
              :image-url="(postOwner || item.owner)?.avatar || ''"
              :user-name="(postOwner || item.owner)?.name"
              round-radius
            />
            <iconpark-icon v-if="hasMultiCards" name="iconstanding" class="icon" />
          </div>
        </UserSelect>

        <div class="input-wrap">
          <MentionEditor
            ref="mentionEditor"
            @disable-change="disableChange"
            :placeholder="`回复 ${item.owner?.name}`"
            :offset="{ left: 80, top: 0 }"
            :maxLength="140"
            :disabled-mention="disabledMention"
          />

          <div class="input-action">
            <template v-if="!disabledMention">
              <t-tooltip content="@" class="item">
                <span @click="mentionPop">
                  <img src="@renderer/assets/digital/svg/icon24_@.svg" class="w-24 h-24" />
                </span>
              </t-tooltip>
            </template>
            <EmojiSelector
              v-model="emojiVisible"
              placement="right-bottom"
              class="emoji"
              :popper-props="{
                modifiers: [
                  {
                    name: 'flip',
                    options: {
                      flipVariations: false,
                    },
                  },
                ],
              }"
              @select="onSelectReplyEmoji"
            />
            <t-button
              theme="primary"
              :disabled="!hasTextContent"
              :loading="replying"
              class="h-28 w-88"
              @click="doReply"
            >
              {{ $t('square.post.publish') }}
            </t-button>
          </div>
        </div>
      </div>

      <slot />
    </div>
  </div>
</template>

<style scoped lang="less">
.comment-item {
  display: flex;
  align-items: flex-start;
  // gap: 4px;
  // margin-top: 4px !important;
  margin-left: -8px;
  margin-right: -8px;
  padding: 8px;
  // width: 100%;
  overflow: hidden;
  transition: background 2s linear;

  &.focus {
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_deepest, #eceff5);
  }

  .right-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    flex: 1;
    // position: relative;
    width: 100%;
    min-width: 0;
    // TODO 临时方式
    > div:nth-child(5) {
      margin-top: 8px;
    }
    .comment-item {
      width: calc(100% + 16px);
      &:last-child {
        padding-bottom: 0;
      }
    }
    .from-address {
      position: absolute;
      right: 0;
      font-size: 14px;
      color: #828da5;
    }

    .input-container {
      width: 100%;
      display: flex;
      gap: 12px;
      margin-top: 12px;
      margin-bottom: 8px;
      :deep(.mention-editor) {
        height: 90px;
      }
    }
  }

  .name {
    color: var(--text-kyy-color-text-3, #828da5);
    min-width: 0;
    cursor: pointer;
    .ellipsis();
  }
  .content {
    color: var(--text-kyy-color-text-1, #1a2139);
  }
  .reply {
    display: flex;
    color: var(--text-kyy-color-text-3, #828da5);
    .symbol {
      flex-shrink: 0;
      display: inline-block;
      margin-right: 8px;
      margin-top: 4px;
      width: var(--checkbox-kyy-radius-checkbox, 2px);
      height: 14px;
      background: var(--text-kyy-color-text-3, #828da5);
    }
  }

  .footer {
    display: flex;
    align-items: center;
    gap: 16px;
    width: 100%;
    margin-top: 4px;
  }
  .time {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    flex: 1 0 0;
    color: var(--text-kyy-color-text-3, #828da5) !important;
    font-size: 14px;
    line-height: 22px;
  }
  .toolbar {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    // color: var(--brand-kyy-color-brand-default, #4d5eff);
    color: var(--icon-kyy-color-icon-default, #828da5);
    font-size: 14px;
    line-height: 22px;
    .like-wrap {
      display: flex;
      gap: 4px;
      cursor: pointer;
    }
    .action-wrap {
      color: var(--icon-kyy-color-icon-default, #828da5);
      display: flex;
      width: 20px;
      height: 20px;
      justify-content: center;
      align-items: center;
      border-radius: var(--radius-kyy-border-radius-xs, 4px);
      cursor: pointer;
      &:hover {
        color: var(--icon-kyy-color-brand-default, #4d5eff);
        background: var(--bg-kyy-color-bg-list-foucs, #e1eaff);
      }
    }
    .icon {
      font-size: 20px;
    }
    .icon-liked {
      color: #d54941 !important;
    }
  }

  .author-tag {
    display: flex;
    flex-shrink: 0;
    height: 16px;
    padding: 0 4px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    margin-left: 8px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--tag-bg-kyy-color-tag-bg-success, #e0f2e5);
    color: var(--kyy_color_tag_text_success, #499d60);
    font-size: 12px;
  }

  .input-wrap {
    display: flex;
    flex-direction: column;
    width: 100%;
    // position: relative;
    margin-top: -4px;

    border-radius: 4px;
    font-size: 14px;
    padding: 4px 8px;
    border: 1px solid #e3e6eb;
    background-color: #ffffff;
    color: var(--text-kyy-color-text-1, #1a2139);

    :deep(.emoji .icon) {
      font-size: 24px;
    }
    :deep(.t-textarea) {
      border-radius: 4px;
    }
    :deep(.t-textarea__info_wrapper) {
      display: none;
    }
    :deep(.t-textarea__inner),
    :deep(.t-textarea__inner:focus) {
      border: none;
      box-shadow: none;
    }
    .input-action {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 8px;
      padding: 0 12px 8px 0;
      background-color: #fff;
      border-bottom-right-radius: 4px;
      border-bottom-left-radius: 4px;
      .item {
        height: 24px;
      }
    }
  }
}

.avatar-wrap {
  position: relative;
  align-self: flex-start;
  cursor: pointer;
  user-select: unset;
  .icon {
    position: absolute;
    right: -6px;
    bottom: -6px;
    border-radius: 66px;
    border: 1px solid var(--border-kyy_color_border_white, #fff);
    background: var(--bg-kyy_color_bg_deepest, #eceff5);
    z-index: 1;
  }
}
</style>
