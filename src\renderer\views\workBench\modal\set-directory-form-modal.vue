<template>
  <t-drawer v-model:visible="visible" class="drawerSetForm drawerSetBodyNoBottomPadding" attach="body" :z-index="2500"
    :close-btn="true" :size="'472px'">
    <template #header>
      <div class="header">
        <span class="title">设置名录资料</span>
        <!-- <span v-show="!props.isMember" class="tip cursor" @click="onShowMemberFlow">
          <iconpark-icon name="iconhelp" class="iconhelp"></iconpark-icon>{{ $t('member.winter_column.know_add_flow_1') }} </span> -->
      </div>
    </template>
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px; color: #516082" />
    </template>
    <div class="toBody" v-if="visible">
      <div class="form">
        <form-runtime v-show="currentTab === 'unit'" ref="runtimeUnitRef" :widgets="controls_unit"
          @release="releaseRun_unit" @controls-event="controlsEventRun_unit" />
        <!-- <form-runtime  ref="runtimeUnitDefineRef" :widgets="controls_unit_define"
          @release="releaseRun_unit_define" @controls-event="controlsEventRun_unit_define" /> -->
        <!-- <form-runtime v-show="currentTab === 'person'" ref="runtimePersonRef" :widgets="controls_person"
          @release="releaseRun_person" @controls-event="controlsEventRun_unit" /> -->
        <t-form
          id="formOrg"
          ref="formOrg"
          label-align="top"
          :data="formDataDefine"
        >
          <draggable
            :list="controls_unit_define" item-key="id"
            handle=".move"
            :move="checkMove"
            drag-class="drag"
            chosen-class="chosen"
            ghost-class="ghost"

            @end="onSortChange"
            >
            <template
                #item="{ element: attr, index }"
              >
              <div class="definePeriod">
                <div class="move">
                  <iconpark-icon name="icondrag" class="move definePeriod-icondrag"></iconpark-icon>
                </div>
                <div class="definePeriod-content">
                  <t-form-item
                    class="definePeriod-item"
                    style="margin-bottom: 0 !important;"
                    :rules="
                      [
                        {
                          message: `${attr.placeholder}`,
                          validator: () => validatorName(attr),
                          required: attr.required,
                          trigger: 'blur'
                        }
                      ]
                    "
                    :name="attr.id+ 'name'"
                  >
                    <template #label>
                      <!-- <span style="flex: 1">{{ attr.name }}</span> -->
                      <!-- <t-input
                        v-model="attr.name"
                        :placeholder="attr.typeName"
                        :disabled="attr.disabled"
                        :maxlength="10"
                      /> -->
                    </template>
                    <t-input
                      v-model="attr.name"
                      :placeholder="attr.typeName"
                      :disabled="attr.disabled"
                      :maxlength="10"
                    />
                  </t-form-item>
                  <t-form-item
                    class="definePeriod-item"
                    style="margin-bottom: 0 !important;"
                    :rules="
                      [
                        {
                          message: `${attr.placeholder}`,
                          validator: () => validatorValue(attr),
                          required: attr.required,
                          trigger: 'blur'
                        }
                      ]
                    "
                    :name="attr.id + 'value'"
                  >
                    <template #label>
                      <!-- <span style="flex: 1">{{ attr.name }}</span> -->
                      <!-- <t-input
                        v-model="attr.name"
                        :placeholder="attr.typeName"
                        :disabled="attr.disabled"
                        :maxlength="10"
                      /> -->
                    </template>
                    <t-input
                      v-model="attr.value"
                      :placeholder="attr.placeholder"
                      :disabled="attr.disabled"
                      :maxlength="200"
                    />
                  </t-form-item>
                </div>
                <iconpark-icon name="icondelete" @click="onRemove(attr)" class="cursor definePeriod-icondelete"></iconpark-icon>
              </div>
            </template>
          </draggable>
        </t-form>
        <t-button theme="default" class="addBtn" @click="onAddDefinePeriod">
          <iconpark-icon name="iconadd" class="addBtn-icon"></iconpark-icon>
          添加自定义字段
        </t-button>
      </div>
    </div>
    <template #footer>
      <div class="footer" style="display: flex; justify-content: flex-end">
        <t-button theme="default" variant="outline" @click="onClose">
          {{ $t('member.impm.select_11') }}
        </t-button>
        <t-button theme="primary" @click="onSubmit">保存</t-button>
      </div>
    </template>
  </t-drawer>
</template>

<script lang="ts" setup>
/**
 * @description 批量调整角色弹层
 * <AUTHOR>
 */
import lodash, { debounce } from 'lodash';

import { ref, reactive, Ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import formRuntime from '@renderer/components/free-from/runtime/index.vue';
// import formDefineRuntime from '@renderer/components/free-from/runtime/runtime.vue';
import { useI18n } from 'vue-i18n';
import { v4 as uuidv4 } from "uuid";
import { originType, TagColors, TagType } from '@renderer/views/digital-platform/utils/constant';
// import {
//   onGetLabelSettingAxios as onGetLabelSettingPoliticsAxios,
//   getIndustryListAxios as getIndustryListAxiosPolitics,
//   getTeamsSizeListAxios as getTeamsSizeListAxiosPolitics,
//   onPostDirectorySettingAxios as onPostDirectorySettingAxiosPolitics,
// } from '@renderer/api/politics/api/businessApi';

// import {
//   onGetLabelSettingAxios as onGetLabelSettingMemberAxios,
//   getIndustryListAxios as getIndustryListAxiosMember,
//   getTeamsSizeListAxios as getTeamsSizeListAxiosMember,
//   onPostDirectorySettingAxios as onPostDirectorySettingAxiosMember,
// } from '@renderer/api/member/api/businessApi';


// import {
//   onGetLabelSettingAxios as onGetLabelSettingCBDAxios,
//   getIndustryListAxios as getIndustryListAxiosCBD,
//   getTeamsSizeListAxios as getTeamsSizeListAxiosCBD,
//   onPostDirectorySettingAxios as onPostDirectorySettingAxiosCBD,
// } from '@renderer/api/cbd/api/businessApi';


// import {
//   onGetLabelSettingAxios as onGetLabelSettingAssociationAxios,
//   getIndustryListAxios as getIndustryListAxiosAssociation,
//   getTeamsSizeListAxios as getTeamsSizeListAxiosAssociation,
//   onPostDirectorySettingAxios as onPostDirectorySettingAxiosAssociation,
// } from '@renderer/api/association/api/businessApi';

import {
  getTeamLabelSettingAxios,
  teamDirectorySaveAxios,
} from '@renderer/api/digital-platform/api/businessApi';
import draggable from "vuedraggable";
import { to } from 'await-to-js';
import { getChidlren, getResponseResult } from '@/utils/myUtils';
const currentTab = ref('');
const { t } = useI18n();
const type = ref(0); // 0创建 1编辑
const props = defineProps({
  teamId: {
    type: String,
    default: '',
  },
  origin: {
    type: String,
    default: originType.Member,
  },
  // orData: {
  //   type: Object,
  //   default: ()=> null
  // }
});
const formDataDefine = ref({});
const emits = defineEmits(['onReload']);

const runtimeUnitRef: Ref<any> = ref(null);
const runtimePersonRef: Ref<any> = ref(null);

const handleObj = {
  getLabelFunc: getTeamLabelSettingAxios,
  getIndustryListAxios: null,
  getTeamsSizeListAxios: null,
  // saveTitleFunc: null,
  onPostDirectorySettingAxios: teamDirectorySaveAxios,
};
/**

const handleFunc = () => {
  switch (props.origin) {
    case originType.Member:
      // handleObj.saveTitleFunc =
      handleObj.getLabelFunc = onGetLabelSettingMemberAxios;
      handleObj.getIndustryListAxios = getIndustryListAxiosMember;
      handleObj.getTeamsSizeListAxios = getTeamsSizeListAxiosMember;
      // handleObj.saveTitleFunc = saveLabelRelationPoliticsAxios;
      // 用来保存名录设置
      handleObj.onPostDirectorySettingAxios = onPostDirectorySettingAxiosMember;
      break;
    case originType.Politics:
      handleObj.getLabelFunc = onGetLabelSettingPoliticsAxios;
      handleObj.getIndustryListAxios = getIndustryListAxiosPolitics;
      handleObj.getTeamsSizeListAxios = getTeamsSizeListAxiosPolitics;
      // handleObj.saveTitleFunc = saveLabelRelationPoliticsAxios;
      // 用来保存名录设置
      handleObj.onPostDirectorySettingAxios = onPostDirectorySettingAxiosPolitics;
      break;
    case originType.CBD:
      handleObj.getLabelFunc = onGetLabelSettingCBDAxios;
      handleObj.getIndustryListAxios = getIndustryListAxiosCBD;
      handleObj.getTeamsSizeListAxios = getTeamsSizeListAxiosCBD;
      // handleObj.saveTitleFunc = saveLabelRelationPoliticsAxios;
      // 用来保存名录设置
      handleObj.onPostDirectorySettingAxios = onPostDirectorySettingAxiosCBD;
      break;
    case originType.Association:
      handleObj.getLabelFunc = onGetLabelSettingAssociationAxios;
      handleObj.getIndustryListAxios = getIndustryListAxiosAssociation;
      handleObj.getTeamsSizeListAxios = getTeamsSizeListAxiosAssociation;
      // handleObj.saveTitleFunc = saveLabelRelationPoliticsAxios;
      // 用来保存名录设置
      handleObj.onPostDirectorySettingAxios = onPostDirectorySettingAxiosAssociation;
      break;
    default:

      break;
  }
};

handleFunc();
 *
 */

const optionsOrganizeType = [
  // { label: "企业", value: 1 },
  // { label: "商协会", value: 2 },
  // { label: "个体户", value: 3 },
  // { label: "其他", value: 0 }
  { label: t('member.second.g'), value: 1 },
  { label: t('member.second.h'), value: 2 },
  { label: '政企单位', value: 4 },
  { label: t('member.second.i'), value: 3 },
  { label: t('member.second.j'), value: 0 },
];

const controls_person: any = ref([
  {
    type: 'BaseInfoMember', // 所使用的套件
    vModel: 'baseInfoMember', // baseInfoMember基础信息 organizeInfoMember组织详情信息
    passiveRelevance: false,
    name: '基础信息',
    typeName: '基础信息',
    // addButtonText: "新增明细",
    value: [],
    fromType: 'person', // 单位unit、个人person
    origin: [
      {
        type: 'Divider',
        vModel: 'divider',
        value: '',
        name: '基础资料',
        typeName: '基础资料',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'efba44c-680b-4gfdc8b943c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Logo',
        vModel: 'nameLogo',
        value: [],
        name: '名录照片',
        typeName: '名录照片',
        placeholder: '请上传',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        aspectRatio: 0.7142,
        id: 'namfe-638b-4608-a71b-iiib233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Input',
        vModel: 'name',
        value: '',
        name: '姓名',
        typeName: '姓名',
        placeholder: '请输入',
        required: true, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'efbwwwwb-fbaa2c8b233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'TextArea',
        vModel: 'interest',
        value: '',
        name: '兴趣爱好',
        typeName: '兴趣爱好',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'ukklwet-we9y3c-wq',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Tag',
        vModel: 'tagPlatform',
        value: null,
        name: '平台标签',
        typeName: '平台标签',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: 'u672hjhkc-34kjkrree09y3c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
        options: [],
      },
      {
        type: 'Tag',
        vModel: 'tagPerson',
        value: null,
        name: '个人标签',
        typeName: '个人标签',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: 'udvv-aaa-111-lll-adsvcxvxcvc',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Title',
        vModel: 'titleT',
        value: null,
        name: '头衔',
        typeName: '头衔',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'u67222-hhhh-aaa-eccec',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Divider',
        vModel: 'divider',
        value: '',
        name: '数字名片',
        typeName: '数字名片',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5qqytyiuiu-yrtujhjgh-21c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },
      {
        type: 'ElectronicCard',
        vModel: 'electronicCard',
        value: false,
        name: '显示数字名片',
        typeName: '显示数字名片',
        tip: '开启后，会将个人数字名片显示出来。其他平台成员可收藏你的数字名片。',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5b1eggg-asc-ewewr-gfdg1c',
        parentInfo: {
          id: 'e21906b7-c5yy-4a73-aa76-c8cuuu895e22',
          name: '基础信息',
        },
      },
    ],
  },
]);

const controls_unit_define: any = ref([]);

const controls_unit: any = ref([
  {
    type: 'BaseInfoMember', // 所使用的套件
    vModel: 'baseInfoMember', // baseInfoMember基础信息 organizeInfoMember组织详情信息
    passiveRelevance: false,
    name: '基础信息',
    typeName: '基础信息',
    // addButtonText: "新增明细",
    value: [],
    fromType: 'unit', // 单位unit、个人person
    origin: [
      {
        type: 'Divider',
        vModel: 'divider',
        value: '',
        name: '基础信息',
        typeName: '基础信息',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'efba144c-680b-4608-a71b-fbaa2c8b943c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },

      },
      {
        type: 'Logo',
        vModel: 'nameLogo',
        value: [],
        name: '名录照片',
        typeName: '名录照片',
        placeholder: '请上传',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        aspectRatio: 0.7142,
        id: 'namfe-638b-4608-a71b-f644432b233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Input',
        vModel: 'name',
        value: '',
        name: '姓名',
        typeName: '姓名',
        placeholder: '请输入',
        required: true, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'efba144c-680b-4608-a71b-fbaa2c8b233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Input',
        vModel: 'departs',
        value: '',
        name: '部门/岗位',
        typeName: '部门/岗位',
        placeholder: '请输入',
        required: true, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'efba33c-6811b-4608-a71b-f33233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Input',
        vModel: 'memberNum',
        value: '',
        name: '工号',
        typeName: '工号',
        placeholder: '请输入',
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'ef3344c-6d0b-4998-a71b-fb55233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Input',
        vModel: 'multipPhone',
        value: [],
        name: '手机号码',
        typeName: '手机号码',
        placeholder: '请输入',
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'ef223322898-a71b-fb55233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Input',
        vModel: 'email',
        value: [],
        name: '邮箱地址',
        typeName: '邮箱地址',
        placeholder: '请输入',
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'ef22gggee898-a71b-fb55233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Input',
        vModel: 'sex',
        value: [],
        name: '性别',
        typeName: '性别',
        placeholder: '请输入',
        required: false, // 是否必填
        options: [
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'ef244444-342-hh-233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'TextArea',
        vModel: 'interest',
        value: '',
        name: '兴趣爱好',
        typeName: '兴趣爱好',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'u6722th4c-635630b-467808-a71b-fb909y3c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Tag',
        vModel: 'tagPlatform',
        value: null,
        name: '',
        typeName: '组织标签',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'u6722th4c-34kjkrree09y3c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
        options: [],
      },
      {
        type: 'Tag',
        vModel: 'tagPerson',
        value: null,
        name: '',
        typeName: '个人标签',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: 'u6722th4c-635630b-467808-a71b-fb909y3c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      // {
      //   type: 'Title',
      //   vModel: 'titleT',
      //   value: null,
      //   name: '头衔',
      //   typeName: '头衔',
      //   placeholder: '请输入',
      //   width: '100%',
      //   required: false, // 是否必填
      //   disabled: false, // 是否禁用、是否可编辑
      //   isShow: true, // 是否显示
      //   id: 'u67qq4c-6356tt8-ppy3c',
      //   parentInfo: {
      //     id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
      //     name: '基础信息',
      //   },
      // },
      {
        type: 'Divider',
        vModel: 'divider',
        value: '',
        name: '自定义字段',
        typeName: '自定义字段',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5b1334e02-8cd9-445yy1c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
    ],
  },
]);
const formOrg = ref(null);
const validatorName = (attr: any) => {
  console.log("4444");
  console.log(attr);
  if (!attr.name || attr.name?.length === 0) {
    return {
      message: '信息名称不能为空',
      required: true,
      trigger: "blur",
      result: false,
      type: "error",
    };
  }

  // if (!attr.value || attr.value?.length === 0) {
  //   return {
  //     message: '详细信息不能为空',
  //     required: true,
  //     trigger: "blur"
  //   };
  // }

  return { result: true, message: "", type: "success" };
};
const validatorValue = (attr: any) => {
  console.log("4444");
  console.log(attr);
  // if (!attr.name || attr.name?.length === 0) {
  //   return {
  //     message: '信息名称不能为空',
  //     required: true,
  //     trigger: "blur"
  //   };
  // }

  if (!attr.value || attr.value?.length === 0) {
    return {
      message: '详细信息不能为空',
      required: true,
      trigger: "blur",
      result: false,
      type: "error",
    };
  }

  return { result: true, message: "", type: "success" };
};

const rules = {

};
const datas = ref(null);
const visible = ref(false);

let formData = reactive({
  name: '', // 名录照片

});


const onAddDefinePeriod = () => {



  console.log('onAddDefinePeriod');
  // 新增自定义时间段
  const obj = {
    type: 'DefinePeriod',
    vModel: 'definePeriod',
    value: '',
    name: '',
    typeName: '请输入信息名称',
    placeholder: '请输入内容',
    width: '100%',
    required: false, // 是否必填
    disabled: false, // 是否禁用、是否可编辑
    isShow: true, // 是否显示
    id: uuidv4(),
  };
  // baseList.push(obj);
  controls_unit_define.value.push(obj);
  console.log(controls_unit_define);
};

const onRemove = (attr) => {
  console.log(attr);
  const index = controls_unit_define.value.findIndex((item) => item.id === attr.id);
  if (index !== -1) {
    controls_unit_define.value.splice(index, 1);
  }
};


const switchColor = (intColor) => TagColors.find((item) => item.intColor == intColor);
const onGetLabelSettingInfo = async () => new Promise(async (resolve, reject) => {
  const [err, res] = await to(handleObj.getLabelFunc({}, props.teamId));
  if (err) return reject();
  if (res) {
    const { data } = res;
    console.log(data);
    resolve(data?.data);
  } else {
    reject();
  }
});

// 获取行业列表
const onGetIndustryList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      // console.log(activeAccount.value.teamRegion, "memberStorememberStore");

      result = await handleObj.getIndustryListAxios('', props.teamId);
      console.log(result, '行业11');
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};

// 获取组织规模列表
const onGetTeamsSizeList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await handleObj.getTeamsSizeListAxios({}, props.teamId);
      console.log(result);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};

const onSubmit = debounce(() => {
  // if (currentTab.value === 'person') {
  //   runtimePersonRef.value.submitRun();
  // } else if (currentTab.value === 'unit') {
  //   runtimeUnitRef.value.submitRun();
  // }
  runtimeUnitRef.value.submitRun();

}, 200);

const releaseRun_unit = async (data: any) => {
  console.log(data);
  if (data && data.free_form && data.free_form.length > 0) {

    // formOrg.value?.validate().then((validateResult) => {
    //   console.log(validateResult, 'validateResult');
    //   if (validateResult && Object.keys(validateResult).length) {
    //     const firstError = Object.values(validateResult)[0]?.[0]?.message;
    //     MessagePlugin.warning(firstError);
    //     return;
    //   }
    // });
    const validateResult = await formOrg.value?.validate();
    console.log(validateResult, 'validateResult');
    if (validateResult && Object.keys(validateResult).length) {
      const firstError = Object.values(validateResult)[0]?.[0]?.message;
      // MessagePlugin.warning(firstError);
      console.log('到这里吗firstError')
      return;
    }
    console.log('到这里吗')


    // return;

    const params = onConstructorParams(lodash.cloneDeep(data.free_form));
    console.log(params);
    // let result = null;
    // return;
    const [err, res]: any = await to(handleObj.onPostDirectorySettingAxios(params, props.teamId));
    if (err) {
      const errMsg: any = err instanceof Error ? err?.message : err;
      MessagePlugin.error(errMsg);
      return;
    }
    // const {data} = res;
    MessagePlugin.success('保存成功');
    onClose();
    emits('onReload');
  }
};

const releaseRun_person = async (data: any) => {
  console.log(data);
  if (data && data.free_form && data.free_form.length > 0) {
    const params = onConstructorParams(lodash.cloneDeep(data.free_form));
    console.log(params);
    // let result = null;
    const [err, res]: any = await to(handleObj.onPostDirectorySettingAxios(params));
    if (err) {
      const errMsg: any = err instanceof Error ? err?.message : err;
      MessagePlugin.error(errMsg);
      return;
    }
    // const {data} = res;
    MessagePlugin.success('保存成功');
    onClose();
    emits('onReload');
  }
};

// 构造单位申请的参数
const onConstructorParams = (freeForm: Array<any>) => {
  const params = {

    img: '',
    sex: '',
    email: '',
    hobby: '',
    customize_info: [],
    // id: datas.value?.id,
    telephone_info: [],
    personal_value_ids: [],
    team_value_ids: [],

  };
  if(controls_unit_define.value && controls_unit_define.value.length > 0) {
    params.customize_info = controls_unit_define.value.map((item) => {
      return {
        name: item.name,
        value: item.value,
      };
    })
  }


  const baseList = freeForm.filter((v: any) => v.type === 'BaseInfoMember');
  baseList.map((v: any) => {
    let origin: any = null;

    origin = v.origin.find((or: any) => or.vModel === 'tagPerson');
    if (origin) {
      params.personal_value_ids = origin.value ? [origin.value?.value_id] : [];
    }
    origin = v.origin.find((or: any) => or.vModel === 'tagPlatform');
    if (origin) {
      params.team_value_ids = origin.value ? [origin.value] : [];
    }


    // 名录照片
    origin = v.origin.find((or: any) => or.vModel === 'nameLogo');
    if (origin && origin.value && origin.value.length > 0) {
      params.img = origin.value[0].file_name;
      // params.directory_image_values = origin.value;
    }
    // // 代表人姓名
    // origin = v.origin.find((or: any) => or.vModel === 'name');
    // if (origin) {
    //   params.name = origin.value;
    // }
    // 兴趣爱好
    origin = v.origin.find((or: any) => or.vModel === 'interest');
    if (origin) {
      params.hobby = origin.value;
    }
    // 性别
    origin = v.origin.find((or: any) => or.vModel === 'sex');
    if (origin) {
      params.sex = origin.value;
    }
    // 手机号码
    origin = v.origin.find((or: any) => or.vModel === 'multipPhone');
    if (origin) {
      params.telephone_info = origin.value?.map((item) => {
        return {
          telephone: item.phone,
          phone_code: item.code,
        };
      });
    }
    // 邮箱地址
    origin = v.origin.find((or: any) => or.vModel ==='email');
    if (origin) {
      params.email = origin.value;
    }
    // 自定义字段
    // origin = v.origin.find((or: any) => or.vModel ==='customizeInfo');
    // if (origin) {
    //   params.customize_info = origin.value?.map((item) => {
    //     return {
    //       name: item.name,
    //       value: item.value,
    //     };
    //   })
    //     ;
    // }


    // 组织logo
    // origin = v.origin.find((or: any) => or.vModel === 'organizeLogo');
    // if (origin && origin.value && origin.value.length > 0) {
    //   params.team_logo = origin.value[0].file_name;
    // }



    return v;

  });
  return params;
};

const onGetSetting = async () => {
  const result = await Promise.all([
    onGetLabelSettingInfo(), // 获取标签设置信息
    // onGetIndustryList(), // 组织行业列表
    // onGetTeamsSizeList(), // 组织规模
  ]);
  // if (currentTab.value === 'unit') {
  //   onSetUnitForm(result);
  // } else {
  //   onSetPersonForm(result);
  // }
  onSetUnitForm(result);
};

const onSetPersonForm = (result) => {
  const baseList = controls_person.value?.filter(
    (v: any) => v.type === 'BaseInfoMember',
  );
  baseList.map((v: any) => {
    let origin: any = null;
    origin = v.origin.find((or: any) => or.vModel === 'tagPlatform');
    if (origin) { // 平台标签
      // const item = origin.options.find((v: any) => v.value === origin.value);

      let options = result[0]?.platform?.value;
      options = options?.map((v) => {
        if (switchColor(v.colour)) {
          return {
            ...v,
            ...switchColor(v.colour),
          };
        }
        return v;

      });
      console.log(options);
      origin.options = options;
      origin.isShow = !!result[0]?.platform?.enable;
      origin.name = result[0]?.platform?.name;
      // origin.tip =  result[0]?.platform?.name;
      if (datas.value?.label_relation?.platform?.value?.length) {
        origin.value = datas.value?.label_relation?.platform?.value[0]?.value_id;
      } else {
        origin.value = '';
      }
    }

    // 个人标签
    origin = v.origin.find((or: any) => or.vModel === 'tagPerson');
    if (origin) {
      origin.isShow = !!result[0]?.personal?.enable;
      origin.tip = result[0]?.personal?.explain;
      origin.name = result[0]?.personal?.name;
      // console.log('f', datas.value?.label_relation?.personal)
      if (datas.value?.label_relation?.personal?.value?.length) {
        const cos = origin.value = datas.value?.label_relation?.personal?.value[0];
        origin.value = {
          ...cos,
          ...switchColor(cos?.colour),
        };
      } else {
        origin.value = '';
      }
    }

    // 头衔
    origin = v.origin.find((or: any) => or.vModel === 'titleT');
    if (origin) {
      console.log('kaka', datas.value?.label_relation?.title?.value);
      // origin.isShow = result[0]?.personal?.enable ? true: false;
      origin.value = datas.value?.label_relation?.title?.value || [];
      origin.tip = result[0]?.title?.name;
    }

    origin = v.origin.find((or: any) => or.vModel === "nameLogo");
    if (origin) {
      if (datas.value?.directory_image_values?.length > 0) {
        origin.value = datas.value?.directory_image_values
      } else if (datas.value?.directory_image) {
        const nameLogoSrc = datas.value?.directory_image;
        const namelogoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(nameLogoSrc);
        const origin_name = nameLogoSrc ? nameLogoSrc.substring(nameLogoSrc.lastIndexOf('/') + 1) : '';
        const ck = [
          {
            file_name: nameLogoSrc,
            file_name_short: origin_name,
            original_name: origin_name,
            size: 0,
            type: namelogoRes?.length > 1 ? namelogoRes[1] : '',
          },
        ];
        console.log(ck);
        origin.value = ck;
      } else {
        origin.value = [];
      }
    }


    // 姓名
    origin = v.origin.find((or: any) => or.vModel === 'name');
    if (origin) {
      origin.value = datas.value?.name || '';
    }

    // 兴趣爱好
    origin = v.origin.find((or: any) => or.vModel === 'interest');
    if (origin) {
      origin.value = datas.value?.hobby || '';
    }

    return v;
  });
};

const onSetUnitForm = (result) => {
  console.log(result);

  const baseList = controls_unit.value?.filter(
    (v: any) => v.type === 'BaseInfoMember',
  );

  // 回显自定义字段
  if (datas.value?.customize_info?.length > 0) {
    const customizeInfo = datas.value?.customize_info;
    console.log(customizeInfo);
    controls_unit_define.value = customizeInfo.map((item) => {
      return {
        type: 'DefinePeriod',
        vModel: 'definePeriod',
        value: item.value,
        name: item.name,
        typeName: '请输入信息名称',
        placeholder: '请输入内容',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: uuidv4(),
      }
    });

  }


  baseList.map((v: any) => {
    let origin: any = null;
    origin = v.origin.find((or: any) => or.vModel === 'tagPlatform');
    console.log(origin);
    if (origin) { // 平台标签
      // const item = origin.options.find((v: any) => v.value === origin.value);

      let options = result[0]?.team?.value;
      options = options?.map((v) => {
        if (switchColor(v.colour)) {
          return {
            ...v,
            ...switchColor(v.colour),
          };
        }
        return v;

      });
      console.log(options);
      origin.options = options;
      origin.isShow = !!result[0]?.team?.enable;
      origin.name = result[0]?.team?.name;
      if (datas.value?.label_relation?.team?.value?.length) {
        origin.value = datas.value?.label_relation?.team?.value[0]?.value_id;
      } else {
        origin.value = '';
      }

    }

    // 个人标签
    origin = v.origin.find((or: any) => or.vModel === 'tagPerson');
    if (origin) {
      origin.isShow = !!result[0]?.personal?.enable;
      origin.tip = result[0]?.personal?.explain;
      origin.name = result[0]?.personal?.name;
      // console.log('f', datas.value?.label_relation?.personal)
      if (datas.value?.label_relation?.personal?.value?.length) {
        const cos = origin.value = datas.value?.label_relation?.personal?.value[0];
        origin.value = {
          ...cos,
          ...switchColor(cos?.colour),
        };
      } else {
        origin.value = '';
      }
    }

    origin = v.origin.find((or: any) => or.vModel === 'nameLogo');
    if (origin) {
      if (datas.value?.img) {
        const nameLogoSrc = datas.value?.img;
        const namelogoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(nameLogoSrc);
        const origin_name = nameLogoSrc ? nameLogoSrc.substring(nameLogoSrc.lastIndexOf('/') + 1) : '';
        const ck = [
          {
            file_name: nameLogoSrc,
            file_name_short: origin_name,
            original_name: origin_name,
            size: 0,
            type: namelogoRes?.length > 1 ? namelogoRes[1] : '',
          },
        ];
        origin.value = ck;
        console.log(ck)
      } else {
        origin.value = [];
      }
    }


    // 是否显示联系人
    origin = v.origin.find((or: any) => or.vModel === 'contactInfo');
    if (origin) {
      origin.value = !!datas.value?.contact_switch;
    }

    // 是否显示联系人
    origin = v.origin.find((or: any) => or.vModel === 'electronicCard');
    if (origin) {
      origin.value = !!datas.value?.business_card_switch;
    }

    origin = v.origin.find((or: any) => or.vModel === 'memberNum');
    if (origin) {
      origin.value = datas.value?.work_num || '';
    }
    origin = v.origin.find((or: any) => or.vModel === 'email');
    if (origin) {
      origin.value = datas.value?.email || '';
    }

    origin = v.origin.find((or: any) => or.vModel === 'sex');
    if (origin) {
      origin.value = datas.value?.sex || '';
    }

    origin = v.origin.find((or: any) => or.vModel === 'multipPhone');
    if (origin) {
      origin.value = datas.value?.telephone_info?.length > 0  ? datas.value?.telephone_info?.map((v)=>{
        return {
          code: v?.phone_code || '',
          phone: v?.telephone || '',
        }
      }) : [ { code: '86', phone: '' },] ;
    }


    origin = v.origin.find((or: any) => or.vModel === 'departs');
    if (origin) {
      // origin.value = [
      //   {
      //     dep: '',
      //     job: '',
      //   },
      // ];

      origin.value = datas.value?.department_position?.map(v=>({dep: v?.department_name|| '--', job: v?.job_name || '--'})) || [];
    }


    // 代表人姓名
    origin = v.origin.find((or: any) => or.vModel === 'name');
    if (origin) {
      origin.value = datas.value?.name || '';

      if (props.origin === originType.CBD) {
        origin.name = '负责人'
      }
    }

    // 兴趣爱好
    origin = v.origin.find((or: any) => or.vModel === 'interest');
    if (origin) {
      origin.value = datas.value?.hobby || '';
    }

    // 行业选择
    origin = v.origin.find((or: any) => or.vModel === 'industryType');
    if (origin) {
      // origin.code = 86;
      origin.options = result[1];
      // industryListData.value = result[1];
      origin.value = datas.value?.industry;
    }

    // 组织规模
    origin = v.origin.find((or: any) => or.vModel === 'organizeScale');
    if (origin) {
      origin.options = result[2];
      // sizeListData.value = result[2];
      origin.value = datas.value?.size;
    }

    // 组织类型选择
    origin = v.origin.find((or: any) => or.vModel === 'organizeType');
    if (origin) {
      origin.options = optionsOrganizeType;
      origin.value = datas.value?.team_type || '';
    }

    // 组织岗位
    origin = v.origin.find((or: any) => or.vModel === 'unitJob');
    if (origin) {
      origin.value = datas.value?.job;
    }

    // 业务范围
    origin = v.origin.find((or: any) => or.vModel === 'business');
    if (origin) {
      origin.value = datas.value?.business;
    }

    // 组织地址
    origin = v.origin.find((or: any) => or.vModel === 'organizeAddress');
    if (origin) {
      origin.value = datas.value?.address_code || [];
      origin.address_value = datas.value?.address_detail;
    }

    // 个人标签
    origin = v.origin.find((or: any) => or.vModel === 'tagPerson');
    if (origin) {
      origin.isShow = !!result[0]?.personal?.enable;
      // console.log('f', datas.value?.label_relation?.personal)
      if (datas.value?.label_relation?.personal?.value?.length) {
        const cos = origin.value = datas.value?.label_relation?.personal?.value[0];
        origin.value = {
          ...cos,
          ...switchColor(cos?.colour),
        };
      } else {
        origin.value = '';
      }
    }

    // 头衔
    origin = v.origin.find((or: any) => or.vModel === 'titleT');
    if (origin) {
      console.log('kaka', datas.value?.label_relation?.title?.value);
      // origin.isShow = result[0]?.personal?.enable ? true: false;
      origin.value = datas.value?.label_relation?.title?.value || [];
    }

    origin = v.origin.find((or: any) => or.vModel === 'organizeName');
    if (origin) {
      // origin.value = datas.value?.team_logo;
      origin.value = datas.value?.team_name || '';
    }

    origin = v.origin.find((or: any) => or.vModel === 'organizeAbbrName');
    if (origin) {
      // origin.value = datas.value?.team_logo;
      origin.value = datas.value?.team_short_name || '';
    }

    origin = v.origin.find((or: any) => or.vModel === 'organizeLogo');
    if (origin) {
      // origin.value = datas.value?.team_logo;
      if (datas.value?.team_logo) {
        const logoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(datas.value?.team_logo);
        const origin_name = datas.value?.team_logo ? datas.value?.team_logo.substring(datas.value?.team_logo.lastIndexOf('/') + 1) : '';
        const ck = [
          {
            file_name: datas.value?.team_logo,
            file_name_short: origin_name,
            original_name: origin_name,
            size: 0,
            type: logoRes?.length > 1 ? logoRes[1] : '',
          },
        ];
        console.log(ck);
        origin.value = ck;
      } else {
        origin.value = [];
      }
    }

    return v;
  });

};

const onSave = debounce(() => { });

const onOpen = (data?: any) => {
  datas.value = data;
  console.log(datas.value?.directory_image_values)
  currentTab.value = 'unit';
  // if (data?.type === 1) {
  //   currentTab.value = 'unit';
  // } else {
  //   currentTab.value = 'person';
  // }

  onGetSetting();

  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose,
});
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";
:deep(.t-form__label) {
  padding-right: 0 !important;
}

.move:hover .definePeriod-icondrag {
  transform: scale(1.2);
  cursor: grab;
}

.addBtn {
  height: 28px;
  color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  gap: 4px;
  &-icon {
    font-size: 20px;
    color: #828DA5;
    transition: all 0.3s;

  }
  &:hover {
    .addBtn-icon {
      color: #4d5eff;
      transition: all 0.3s;
    }
  }
}
// :deep(.t-form__item) {
//   margin-right: 0;
//   margin-bottom: 0 !important;
// }
:deep(.detail-control) {
  margin-bottom: 16px !important;
  margin-top: 0 !important;
}

:deep(.t-form__label) {
  white-space: wrap !important;
}

:deep(.t-popup) {
  z-index: 2501;
}

.searchForm {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  // gap: 24px;
  &-item {
    min-width: 324px;
    width: 100%;
  }
}

.t-alert--info {
  padding: 8px 16px;
}

.form {
  // margin-top: 10px;
}

// :deep(.t-dialog__body) {
//   overflow: hidden !important;
//   padding-bottom: 0;
// }

.inputFlex {
  display: flex;
  flex-direction: column;
  width: 100%;

  .tips {
    font-size: 14px;

    font-weight: 400;
    color: #717376;
  }
}

.toBody {
  //   max-height: 70vh;
  //   overflow: auto;
  // padding: 0 24px;
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}

// :deep(.t-dialog--default) {
//   padding: 0 !important;
// }</style>
<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";
.definePeriod {
  margin-bottom: 16px;
  display: flex;
  // align-items: center;
  // gap: 16px;
  padding: 12px;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  gap: 8px;
  align-items: flex-start;
  // :deep(.t-form:not(.t-form-inline) .t-form__item:last-of-type) {
  //   margin-bottom: 0 !important;
  // }
  &-item {
    flex: 1;
    width: 100%;
    // display: flex;
    // flex-direction: column;
    // align-items: flex-start;
    // gap: 4px;
    // margin-bottom: 0!important;
  }
  &-icondrag {
    color: #828DA5;
    font-size: 20px;
    margin-top: 3px;
    // margin-right: 12px;
  }
  &-icondelete {
    color: #828DA5;
    font-size: 20px;
    margin-top: 3px;
  }
  &-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 8px;
    :deep(.t-form__label) {
      display: none;
    }
  }
}
.createUpdate {
  .toBody {
    // height: 68vh;
    // overflow: auto;
    // padding-bottom: 120px;
  }

  .t-dialog__header {
    padding: 0 24px;
  }

  .t-dialog__footer {
    padding: 0 24px;
  }

  .t-dialog--default {
    padding-left: 0;
    padding-right: 0;
  }
}

.detail-control {
  margin-top: 16px;
  margin-bottom: 4px;

  .lable {
    display: flex;
    align-items: center;

    // &::before {
    //   content: " ";
    //   width: 2px;
    //   height: 14px;
    //   background: #2069e3;
    //   border-radius: 2px;
    //   // position: absolute;
    //   left: 0;
    //   top: 2px;
    // }
    .line {
      width: 2px;
      height: 14px;
      background: var(--brand-kyy-color-brand-default, #4d5eff);

      border-radius: 2px;
      margin-right: 10px;
    }

    .text {
      font-size: 14px;

      font-weight: 700;
      text-align: left;
      color: #13161b;
      height: 22px;
      line-height: 24px;
    }
  }

  .value {
    width: 100%;
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: #13161b;
    margin-top: 4px;
  }
}
</style>
