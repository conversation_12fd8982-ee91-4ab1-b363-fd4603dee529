import { defineStore } from "pinia";
import { setDigitalPlatformTeamID } from "@renderer/views/digital-platform/utils/auth";
import { loadUnreadStats } from "@renderer/api/forum/notice";
import to from "await-to-js";
import { RouteItem } from "./types";
import { getPlatformType } from "../forum/utils/business";
import { merge } from "lodash";
import { destroyNode, getShowNode } from "@renderer/_jssdk/components/iframe/iframePool";

export const useDigitalPlatformStore = defineStore("digitalPlatform", {
  state: () => ({
    tabs: [],
    activeIndex: 0, // 用这个记录的是路由名字
    activeAccount: null,
    goTeamId: "", // 需要跳转的teamId
    isBrushTeam: false, // 是否刷新组织列表
    openAssociationPackage: false, // 是否开启数字社群
    openUniPackage: false, // 是否开启数字高校
    openCbdPackage: false, // 是否自动开启数字CBD
    openMemberPackage: false, // 是否自动开启数字商协
    openPoliticsPackage: false, // 是否自动开启数字城市

    inputeMemberCenter: false, // 是否从主页平台进入会员中心

    recordTeamsSelectCardIds: [], // 记录各组织在会员中心选择的
    recordTeamsSelectMyPanelCardIds: [], // 记录各组织在我的会员中心选择的

    // 数智工场，组织介绍编辑页是否刷新
    isRefreshIntroduceEdit: false,
    // 解决数字平台，短时间内多次执行的问题
    isLoadHomeInfo: false,

    // isLoadHomePanel: false,


    supplyPagekey: 1,
    desiredPagekey: 10,
    richPagekey: 20,
    unreadData: null,
    platformType: null,
  }),

  getters: {},

  actions: {
    // 解决数字平台，短时间内多次执行的问题
    // setIsLoadHomePanelInfo(val: boolean) {
    //   this.isLoadHomePanel = val;
    // },

    // 解决数字平台，短时间内多次执行的问题
    setIsLoadHomeInfo(val: boolean) {
      this.isLoadHomeInfo = val;
    },
    
    // 数智工场，组织介绍编辑页是否刷新
    setIsRefreshIntroduceEdit(val: boolean) {
      this.isRefreshIntroduceEdit = val
    },

    // 获取平台类型
    async getPlatformType(team_id) {
      const [err, platformType] = await to(getPlatformType(team_id));
      if (err || !platformType) {
        console.error('获取 platformType 失败');
        return null;
      }

      this.platformType = platformType;
      return platformType;
    },
    // 获取消息红点
    async getUnreadStats(team_id) {
      await this.getPlatformType(team_id);
      if (!this.platformType) return;

      const [err, data] = await to(loadUnreadStats({
        platform_type: this.platformType,
        team_id
      }));
      if (err || !data) {
        return null;
      }
      this.unreadData = data?.data?.data;
    },
    setRecordTeamsSelectCardId(val: { teamId: string; cardId: number }) {
      const result = this.recordTeamsSelectCardIds.find((v) => v.teamId === val.teamId);
      if (result) {
        result.cardId = val.cardId;
      } else {
        this.recordTeamsSelectCardIds.push(val);
      }
    },
    setRecordTeamsSelectMyPanelCardId(val: { teamId: string; cardId: number }) {
      const result = this.recordTeamsSelectMyPanelCardIds.find((v) => v.teamId === val.teamId);
      if (result) {
        result.cardId = val.cardId;
      } else {
        this.recordTeamsSelectMyPanelCardIds.push(val);
      }
    },

    setInputeMemberCenter(val) {
      this.inputeMemberCenter = val;
    },

    setOpenAssociationPackage(val) {
      this.openAssociationPackage = val;
    },
     setOpenUniPackage(val) {
      this.openUniPackage = val;
    },
    
    setOpenCbdPackage(val) {
      this.openCbdPackage = val;
    },
    setOpenPoliticsPackage(val) {
      this.openPoliticsPackage = val;
    },

    setOpenMemberPackage(val) {
      this.openMemberPackage = val;
    },

    setGoTeam(val) {
      this.isBrushTeam = !!val;
      this.goTeamId = val;
    },
    setActiveAccount(item) {
      if (item) {
        setDigitalPlatformTeamID(item.teamId);
      }
      this.activeAccount = item;
      this.getPlatformType(item.teamId);
      this.getUnreadStats(item.teamId);
      // localStorage.setItem('project_teamid', item.teamId);
    },

    addTab(item: RouteItem, addNew = false): void {
      console.log("addTabaddNewaddNewaddNew", item);
      console.log("addTabaddNewaddNewaddNew1111", addNew);
      if (item.path_uuid) {
        const index = this.tabs.findIndex((v) => v.path_uuid === item.path_uuid);
        if (index > -1) {
          this.tabs[index] = { ...item };
          this.activeIndex = index;
          return;
        }
        this.tabs.push(item);
        this.activeIndex = this.tabs.length - 1;
        return;
      }
      if (addNew) {
        const index = this.tabs.findIndex(
          (v: RouteItem) => v?.path === item.path && JSON.stringify(v?.query) === JSON.stringify(item.query),
        );
        console.log("addTabaddNewaddNewaddNew1111indexindex", index);

        if (index > -1) {
          this.activeIndex = index;
          return;
        }
      } else {
        const index = this.tabs.findIndex((v: RouteItem) => v.path === item.path);
        // 已存在，更新
        console.log("this.tabs", this.tabs);
        if (index > -1) {
          this.activeIndex = index;
          // 更新标题及参数
          this.tabs[index] = { ...item };
          return;
        }
      }


      // 添加
      this.tabs.push(item);
      this.activeIndex = this.tabs.length - 1;

      // const itemsToAdd = Array.isArray(items) ? items : [items];
      // itemsToAdd.forEach((item) => {
      //   const index = this.tabs.find(
      //     (v: RouteItem) => v.fullPath === item.fullPath
      //   );
      //   if (index) {
      //     this.activeIndex = index.name;
      //     return;
      //   }

      //   this.tabs.push(item);
      //   this.activeIndex = this.tabs[this.tabs.length - 1]?.name;
      // });
    },
    /**
     *
     * @param item
     * @param toItem 移除后，指定跳到
     */
    removeTab(item, toItem?: any) {
      // const item = this.tabs.find((v: RouteItem) => v.fullPath === fullPath);
      // const index = this.tabs.findIndex((v: RouteItem) => v.fullPath === fullPath);
      // console.log(index, unchanged)
      // if (item) {
      //   this.tabs = this.tabs.filter((v) => v.name !== item.name);
      //   if (!unchanged) {
      //     this.activeIndex = this.tabs[Math.max(0, index - 1)]?.name;
      //     console.log('removeAfter:', this.activeIndex)
      //   }
      // }
      // const whiteList = ['/square/search']; && (whiteList.includes(v?.path)
      console.log(this.tabs);
      console.log(item, "itemitemitem");

      const index = this.tabs.findIndex((v) => {
        const queryStr = JSON.stringify(v?.query) === JSON.stringify(item.query);
        console.log(v.path, item.path);
        return v?.path === item.path && queryStr;
      });
      console.log(index);
      if (index > -1) {
        // this.isRemoveTag = true;
        const tabInfo = this.tabs[index];
        destroyNode(tabInfo.path_uuid || '');
        this.tabs.splice(index, 1);



        // 只处理 path 为 string 的 tab
        if (typeof item.path === 'string' && item.path.includes('/digital_platform_webview')) {
          const iframeOnlyId = item.path.split('/').pop();
          if (iframeOnlyId) {
            console.log('准备销毁节点:', iframeOnlyId);
            setTimeout(() => {
              destroyNode(iframeOnlyId);
            }, 0);
          }
        }

        // 处理特殊情况跳指定tab
        if (toItem) {
          const toIndex = this.tabs.findIndex((v) => {
            const queryStr = JSON.stringify(v?.query) === JSON.stringify(toItem.query);
            console.log(v.path, toItem.path);
            return v?.path === toItem.path && queryStr;
          });
          console.log(toIndex);
          this.activeIndex = toIndex;
          return;
        }

        // 处理焦点在左侧的时候的逻辑
        if (index < this.activeIndex || index === this.activeIndex) {
          this.activeIndex = Math.max(0, this.activeIndex - 1);
        }
      }
    },
    removeTabItem(item) {
      console.log(item,'特码实打实大');
      console.log(this.tabs,'特码实打实大this.tabs.');

      this.tabs = this.tabs.filter((v) => v.name !== item);
    },
    removeAllTab() {
      // this.tabs = this.tabs.filter((v) => v.affix);
      // this.tabs = [];
      this.tabs = this.tabs.filter((v) => v.affix);
    },

    switchTab(index: number) {
      if (index === this.activeIndex) return;
      this.activeIndex = index;
    },

    /**
     * 更新 tab 信息
     * @param newItem 要更新的字段（通常是 title 或详情页 id）
     * @param item 要更新的 tab 项，默认是当前 tab
     */
    updateTab(newItem, item = this.getActiveTab()) {
      const index = this.tabs.findIndex((v) => {
        const queryStr = JSON.stringify(v?.query) === JSON.stringify(item.query);
        return v?.path === item.path && queryStr;
      });
      if (index === -1) return;

      this.tabs[index] = merge(this.tabs[index], newItem);
    },

    getActiveTab() {
      return this.tabs[this.activeIndex];
      // return this.tabs.find((v) => v.name === this.activeIndex);
    },

    updateTitle(title: string) {
      this.getActiveTab().title = title;
    },
    logout() {
      this.tabs = [];
      // this.squareInfo = null;
    },
  },
  // persist: {
  //   key: "digitalPlatform",
  //   storage: localStorage
  // }
});
