<template>
  <div id="mentionEditor" ref="mentionEditorRef" class="mention-editor">
    <Editor
      class="chat-rich-editor"
      v-model="editorHtml"
      :defaultConfig="editorConfig"
      @onMaxLength="onMaxLength"
      mode="simple"
      @onCreated="handleCreated"
      @customPaste="pasteHandle"
      @keydown="keyup"
    />
    <mention-popup
      v-if="!disabledMention && isShowMentionPopup"
      :offset="offset"
      :mouseXY="mouseXY"
      :fromInput="fromInput"
      :members="members"
      @hideMentionModal="hideMentionModal"
      @insertMention="insertMention"
    />
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch, toRaw, watchEffect } from 'vue';
import MentionPopup from './mentionPopup.vue';
import { Editor } from '@wangeditor/editor-for-vue';
import { IDomEditor, DomEditor, SlateElement, SlateText, SlateEditor, SlateNode, SlateTransforms } from '@wangeditor/editor';

import { useI18n } from 'vue-i18n';
import { onClickOutside } from '@vueuse/core';
import _ from 'lodash';
import { useForumStore } from '@renderer/views/digital-platform/forum/store';

const { t } = useI18n();
const store = useForumStore();

const props = defineProps({
  placeholder: {
    type: String,
    default: '期待你的分享...',
  },
  maxLength: {
    type: Number,
    default: 1000,
  },
  offset: {
    type: Object,
    default: {
      left: 0,
      top: 0,
    },
  },
  defaultValue: {
    type: String,
    default: '',
  },
  // 禁用 mention 功能
  disabledMention: {
    type: Boolean,
    default: false,
  },
});
const mentionEditorRef = ref(null);
const editorRef = ref(null);
const editorHtml = ref(props.defaultValue);
const isShowMentionPopup = ref(false);
const fromInput = ref(true);
const mouseXY = ref({ x: 0, y: 0 });
const showMentionModal = (isInput = true, xy = { x: 0, y: 0 }) => {
  if (!isInput && isShowMentionPopup.value) {
    isShowMentionPopup.value = false;
    return;
  }
  fromInput.value = isInput === false ? false : true;
  mouseXY.value = xy;
  isShowMentionPopup.value = true;
};
// 点击输入栏之外收起操作
onClickOutside(mentionEditorRef, () => {
  isShowMentionPopup.value && (isShowMentionPopup.value = false);
});

const insertMention = (member, name) => {
  const mentionNode = {
    type: 'mention', // 必须是 'mention'
    value: name,
    info: {
      name,
      cardId: member.card_id || member.openid || '',
      openid: member.openid || '',
      remark: member.remark || '',
      teamId: store.teamId,
      appUuid: store.platformType,
    },
    children: [{ text: '' }], // 必须有一个空 text 作为 children
  };
  const editor = editorRef.value;
  if (editor) {
    editor.restoreSelection(); // 恢复选区
    console.log('12313', fromInput.value);
    if (fromInput.value) editor.deleteBackward('character'); // 删除 '@'
    editor.insertNode(mentionNode); // 插入 mention
    editor.move(1); // 移动光标
    editorRef.value.insertText(' ');
  }
};
const hideMentionModal = () => {
  isShowMentionPopup.value = false;
};

const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
  editor.clear(); // 助手切换会话时，携带了上个会话的草稿，需重置富文本数据
  const toolbar = DomEditor.getToolbar(editor);
  console.log(toolbar);
  if (toolbar && toolbar.getConfig()) {
    toolbar.getConfig().toolbarKeys = [];
    console.log(toolbar.getConfig());
  }
  // 设置默认值
  if (props.defaultValue) {
    editor.setHtml(props.defaultValue);
  }
  setTimeout(() => {
    focus();
  }, 10);
};
const keyup = (e) => {
  // if(e.key !== 'Enter') {
  //   return;
  // }
  if (e.key === ' ' || e.code === 'Escape') {
    hideMentionModal();
  }
};
const onMaxLength = (e) => {};
// 粘贴只保留文本
const pasteHandle = (editor: IDomEditor, event: ClipboardEvent) => {
  const data = event.clipboardData?.getData('text/plain');
  // 阻止默认的粘贴行为
  event.preventDefault();
  editor.insertText(data);
};
const editorConfig = {
  placeholder: props.placeholder,
  maxLength: props.maxLength,
  EXTEND_CONF: {
    mentionConfig: {
      showModal: showMentionModal,
      hideModal: hideMentionModal,
    },
  },
};
const insertNode = (emojiText) => {
  focus();
  editorRef.value?.restoreSelection();
  editorRef.value.insertText(`${emojiText}`);
};
const clear = () => {
  editorRef.value.clear();
};
const focus = () => {
  editorRef.value?.focus(true);
};
const getContents = () => {
  const contents = toRaw(editorRef.value.children);
  console.log('===', contents);
  let allChildrenData = [];
  contents?.forEach((item, index) => {
    if (index === contents?.length - 1) {
      allChildrenData = allChildrenData.concat(item?.children);
    } else {
      allChildrenData = allChildrenData.concat(item?.children, [{ type: 'paragraph' }]);
    }
  });
  const textList = getContets(allChildrenData);
  console.log(contents, textList);
  return textList;
};

const getContets = (arr: (Element | Text)[]) => {
  let textList = [];
  let textLabel = '';
  let source = ''; // 消息来源，生日送祝福的消息来源 birthday_wish，用于判断消息下蛋糕雨
  let atInfo = [];
  const getList = (iArr: (Element | Text)[]) => {
    for (const item of iArr) {
      if (SlateElement.isElementList(item)) {
        getList(item as unknown as (Element | Text)[]);
      } else if (SlateElement.isElement(item)) {
        // 处理@人和截图base64的数据类型
        switch ((item as any).type) {
          case 'mention':
            // textList.push({...item})
            textLabel += `@${(item as any).value}~`;
            source = (item as any).source;
            atInfo.push((item as any).info);
            break;
          case 'link':
            textLabel += (item as any).url;
            break;
          case 'paragraph':
            textLabel += '\n';
            break;
          default:
            textLabel += (item as any).text;
            break;
        }
      } else {
        textLabel += (item as any)?.type === 'paragraph' ? '\n' : (item as any).text;
      }
    }
  };
  getList(arr);
  if (textLabel) {
    textList.push(
      atInfo?.length
        ? {
            text: textLabel,
            atInfo: [...atInfo],
            source,
          }
        : {
            text: textLabel,
            source,
          },
    );
  }
  return textList;
};

const emits = defineEmits(['disableChange']);

const debounceResetDisable = _.debounce(() => {
  const contents = toRaw(editorRef.value.children);
  let allChildrenData = [];
  contents?.forEach((item, index) => {
    if (index === contents?.length - 1) {
      allChildrenData = allChildrenData.concat(item?.children);
    } else {
      allChildrenData = allChildrenData.concat(item?.children, [{ type: 'paragraph' }]);
    }
  });
  const parsedInput = getContets(allChildrenData);
  const isDisable = !!parsedInput.some((i) => i.type || i.text?.trim()?.replace(/[\r]/g, '')?.length);

  emits('disableChange', isDisable);
}, 100);

const members = ref({});

onMounted(() => {
  members.value = store.allMember;
  if (props.defaultValue && editorRef.value) {
    editorRef.value.setHtml(props.defaultValue);
  }
});
watch(() => editorHtml.value, debounceResetDisable, { immediate: true });

watchEffect(() => {
  if (editorRef.value && props.defaultValue) {
    editorRef.value.setHtml(props.defaultValue);
  }
});

// 清空所有mention节点
const clearMentions = () => {
  if (!editorRef.value) return;

  const editor = editorRef.value;

  // 使用Slate的Transform API来安全地移除mention节点
  // 查找所有mention节点的路径
  const mentionPaths = [];

  for (const [node, path] of SlateNode.nodes(editor, { reverse: true })) {
    if (SlateElement.isElement(node) && (node as any).type === 'mention') {
      mentionPaths.push(path);
    }
  }

  // 从后往前删除mention节点，避免路径变化影响
  SlateEditor.withoutNormalizing(editor, () => {
    mentionPaths.forEach(path => {
      SlateTransforms.removeNodes(editor, { at: path });
    });
  });
};

defineExpose({
  showMentionModal,
  insertNode,
  clear,
  focus,
  getContents,
  clearMentions,
});
</script>

<style lang="less">
@import './ChatEditorStyle.css';

::-webkit-scrollbar {
  width: 3px;
  height: 8px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}
.mention-editor {
  height: 100%;
}
.chat-editor {
  display: flex;
  flex-direction: column;
  padding: 0 8px 8px;
  border-top: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  .w-e-text-container {
    background: transparent;
  }
}
</style>
