<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue';
import to from 'await-to-js';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import merge from 'lodash/merge';
import { AxiosError, AxiosResponse } from 'axios';
import Draggable from 'vuedraggable';
import { useI18n } from 'vue-i18n';
import { useNetwork } from '@vueuse/core';
import { Flag, PostAdd, PostType } from '@renderer/api/forum/models/post';
import Avatar from '@renderer/components/kyy-avatar/index.vue';
import { Topic } from '@renderer/api/forum/models/topic';
import EmojiSelector from '@/views/square/components/EmojiSelector.vue';
import { useImageUpload, useVideoUpload } from '@/views/square/hooks/upload';
import { addDraft, postPublish } from '@/api/forum/post';
import ImageUpload from '@/views/square/components/post/ImageUpload.vue';
import VideoUpload from '@/views/square/components/post/VideoUpload.vue';
import VideoDisplay from '@/views/square/components/VideoDisplay.vue';
import UserSelect from '../UserSelect.vue';
import TopicSelect from '../TopicSelect.vue';
import TopicSelectItem from '../TopicSelectItem.vue';
import { checkTopicValid } from '../../utils/business';
import { useForumStore } from '../../store';
import MentionEditor from '../mentionEditor/editor.vue';

const props = withDefaults(
  defineProps<{
    modelValue: boolean;
    draft?: boolean;
    zIndex?: number;

    defaultValue?: Record<string, any>;
    // 不显示其它类型的发布按钮
    onlyText?: boolean;
    source?: string;
    // 指定上传类型
    type?: PostType;
    // 是否可上传图片
    showImg?: boolean;
    // 是否可上传视频
    showVideo?: boolean;

    topic?: Topic;
  }>(),
  {
    type: PostType.Text,
    showImg: true,
    showVideo: true,
  },
);

const emit = defineEmits(['update:modelValue', 'submit', 'submit-draft', 'error', 'close-dialog']);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit('update:modelValue', value);
  },
});

const { t } = useI18n();
const forumStore = useForumStore();
const { images, removeImage } = useImageUpload();
const { videos, removeVideo } = useVideoUpload();
const video = computed(() => (videos.value?.length ? videos.value[0] : ''));
const isRemoveVideo = ref(false);

const disabledMention = ref(false);

const hasError = ref(false);
const loading = ref(false);
let formDataBak = '';

// 表单
const initPostForm: PostAdd = {
  text: '',
  postType: props.type || PostType.Text,
  content: {
    objects: [],
  },
  topics: [],
  pin: false,
  ownerId: '',
  at: [],
};
const formData = reactive({ post: { ...initPostForm } });

// 重新选择用户
const currCard = ref(forumStore.currCard);

const mentionEditor = ref<InstanceType<typeof MentionEditor>>(null);

const userChange = (val) => {
  currCard.value = val;
};

// 话题
const selectedTopic = ref(props.topic);
const emptyTopic = ref(true);
const topicLoaded = (list) => {
  emptyTopic.value = list.length === 0;
};

// 开放加好友
const friendFlag = ref(false);
const checkedFriend = ref(false);
const onChange = (val) => {
  checkedFriend.value = val;
};

// 请求参数
const apiParams = () => {
  // 话题列表返回的是 topicID，草稿列表返回的是 id
  const selectTopicId = selectedTopic.value?.topicID || selectedTopic.value?.id;
  const topics = selectTopicId ? [selectTopicId] : [];
  const textData = mentionEditor.value.getContents();
  const at = textData[0]?.atInfo;
  const text = textData[0]?.text;

  return {
    post: {
      ...formData.post,
      friendFlag: friendFlag.value,
      text,
      // ipRegion: ipInfo.value ? JSON.stringify(ipInfo.value) : '',
      ownerId: currCard.value?.id,
      topics,
      at,
    },
  };
};

// 关闭弹窗
const dialogClose = () => {
  if (submitting.value) {
    MessagePlugin.error('发布失败，请检查网络后重试');
    return;
  }

  // 先获取当前表单数据，确保包含视频内容
  const data = apiParams();

  const _shutdown = () => {
    rmVideo();
    nextTick(() => {
      visible.value = false;
    });
  };

  if (
    !canPublish.value ||
    hasError.value ||
    // 编辑草稿，无变更不保存
    (props.draft && formDataBak === JSON.stringify(formData))
  ) {
    _shutdown();
    return;
  }

  _shutdown();

  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: '将此次编辑，保存到草稿箱吗？',
    theme: 'info',
    confirmBtn: t('square.action.save'),
    cancelBtn: '不保存',
    onConfirm: async () => {
      confirmDia.destroy();
      await saveDraft(data);
    },
    onClose: () => {
      confirmDia.hide();
      resetForm();
    },
  });
};

// 保存草稿
const saveDraft = async (data) => {
  if (loading.value) return;

  loading.value = true;
  const [err] = await to(addDraft(data));
  loading.value = false;
  if (err) return;

  await MessagePlugin.success(t('square.saveSuccessTip'));
  resetForm();
  forumStore.getUnreadStats();

  emit('submit-draft');
};

const uploadSuccess = (ctx, type) => {
  if (!isRemoveVideo.value && type === PostType.Video) {
    videos.value = ctx.fileList;
  }
};

let imgTimer = null;
const onImageUploadError = (file) => {
  // 这里不知道为什么有延时，过一定时间才能拿到值
  clearTimeout(imgTimer);
  imgTimer = setTimeout(() => {
    const index = images.value.findIndex((item) => item.flag === file.flag);
    if (index > -1 && images.value[index]) {
      images.value.splice(index, 1);
    }
  }, 200);
};

const canAddPicture = computed(() => images.value.length > 0 && images.value.length < 9);

const onUploadImageFile = (e) => {
  const index = images.value.findIndex((item) => item.flag === e.flag);
  if (index > -1 && images.value[index]) {
    images.value[index] = e;
  }
};

const onUploadVideoFile = (e, client) => {
  if (isRemoveVideo.value) {
    videos.value = [];
    client.abortMultipartUpload();
  } else {
    videos.value = [e];
  }
};

const onChangedVideo = () => {
  isRemoveVideo.value = false;
  formData.post.postType = PostType.Video;
};

const rmVideo = () => {
  isRemoveVideo.value = true;
  removeVideo();
  // formData.post.video = "";
  formData.post.content = { objects: [] };
};

watch(
  [currCard, mentionEditor],
  ([card]) => {
    if (card.flag === Flag.Anonymous) {
      mentionEditor.value?.clearMentions();
      disabledMention.value = true;
    } else {
      disabledMention.value = false;
    }

    nextTick(() => {
      mentionEditor.value?.focus();
    });
  },
  { immediate: true },
);

// 删除图片或视频，切换回文本模式
watch(
  () => images.value,
  (val) => {
    if (val.length === 0) {
      formData.post.postType = PostType.Text;
      formData.post.content = { objects: [] };
    } else if (val.length > 0) formData.post.postType = PostType.Picture;
  },
  { deep: true },
);

// 根据上传视频的状态来动态更新表单中的帖子类型
watch(
  () => videos.value,
  (val) => {
    if (val && val.filter((item) => item.url || item.loading).length === 0) formData.post.postType = PostType.Text;
    else if (val.length > 0) formData.post.postType = PostType.Video;
  },
  { deep: true },
);

// 聚焦输入框
watch(
  () => visible.value,
  async (isVisible: boolean) => {
    await nextTick();
    // isVisible && inputRef.value.focus();

    setTimeout(() => {
      formDataBak = JSON.stringify(formData);
    }, 400);
  },
  { immediate: true },
);

// 根据传入的默认值来初始化或更新表单数据
watch(
  () => props.defaultValue,
  async (val) => {
    merge(formData, val || {});

    // 处理图片或视频数据
    const objs = formData.post.content.objects;
    const mapper = (v) => ({ url: v.url, width: v.metadata.width, height: v.metadata.height });
    if (formData.post.postType === PostType.Picture) {
      images.value = objs.map((v) => ({ ...mapper(v), status: 'success' }));
    }
    if (formData.post.postType === PostType.Video) {
      videos.value = objs.map(mapper);
    }

    // 回显话题
    const topic = (val?.topics || [])[0];
    if (topic) {
      if (await checkTopicValid(topic.id, false)) {
        selectedTopic.value = topic;
      }
    }
  },
  { deep: true, immediate: true },
);

const mentionPop = (event) => {
  // 获取鼠标位置
  // let mouseY = event.clientY;
  // // let mouseX = event.clientX;
  // mouseY = mouseY > 360 ? 360 : mouseY

  mentionEditor.value.showMentionModal(false, { x: 208, y: 62 });
};
// 选表情
// const { onSelectEmoji } = useEmoji(inputRef, formData.post);
const onSelectEmoji = (e) => {
  mentionEditor.value.insertNode(e.i);
};
const emojiVisible = ref(false);
const hasTextContent = ref(false);
const disableChange = (data) => {
  hasTextContent.value = data;
};
// 是否可发布动态
const canPublish = computed(() => {
  const allImagesUploaded = images.value.length > 0 ? images.value.every((item) => item.status !== 'loading') : true;
  const allVideosUploaded = videos.value.length > 0 ? videos.value[0].status !== 'loading' : true;

  // 只要有文本、图片或视频中的任意一项，并且所有上传项都成功
  return (
    (hasTextContent.value || images.value.length > 0 || videos.value.length > 0) &&
    allImagesUploaded &&
    allVideosUploaded
  );
});

// 根据上传的图片和视频的状态来实时更新表单中的图片和视频数据
function updateContentObjects(type: PostType) {
  const objects = type === PostType.Picture ? images.value : videos.value;
  if (!objects || objects.length === 0) {
    formData.post.content.objects = [];
    return;
  }
  formData.post.content.objects = objects.map((v) => ({
    url: v.url,
    metadata: { width: v.width, height: v.height },
  }));
}

watch(
  () => [images, videos],
  () => {
    const type = formData.post.postType;
    if ([PostType.Picture, PostType.Video].includes(type)) {
      updateContentObjects(type);
    }
  },
  { deep: true },
);

const { isOnline } = useNetwork();

const errorTip = (msg: string) => {
  const confirmDia = DialogPlugin.confirm({
    header: t('square.tip'),
    body: msg,
    confirmBtn: t('square.know'),
    cancelBtn: null,
    closeBtn: null,
    closeOnOverlayClick: false,
    theme: 'info',
    onConfirm: async () => {
      confirmDia.destroy();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

// 发布
const submitting = ref(false);
const submit = async () => {
  if (!isOnline.value) {
    await MessagePlugin.error('网络连接失败，请检查网络后重试');
    return;
  }

  if (submitting.value) return;
  submitting.value = true;
  const data = apiParams();
  const [err, res] = await to<AxiosResponse, AxiosError<{ code: number; message: string }>>(postPublish(data));
  submitting.value = false;
  if (err) {
    console.log(err);
    const { code, message } = err.response?.data;

    if ([2001, 2002].includes(code)) {
      errorTip(message);
      return;
    }

    if (code === 2007) {
      await MessagePlugin.error('该话题已失效，请修改后重试');
      return;
    }

    await MessagePlugin.error(message);
    return;
  }

  await MessagePlugin.success('发布成功');
  emit('submit', { data: res.data });
  resetForm();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, { post: { ...initPostForm } });
  images.value = [];
  videos.value = [];
  mentionEditor.value?.clear();
};

const onlyOneCard = computed(() => forumStore.cardList.length === 1);

const topicIconHover = ref(false);
</script>

<template>
  <t-dialog
    v-model:visible="visible"
    :footer="false"
    :close-on-overlay-click="false"
    width="560"
    attach="body"
    placement="center"
    :header="$t('forum.post')"
    prevent-scroll-through
    class="d-forum-publish"
    v-bind="$attrs"
    :close-btn="false"
  >
    <iconpark-icon name="iconerror-a961a3n0" class="closeicon" @click="dialogClose" />
    <div v-if="!onlyOneCard" class="user-wrap">
      <Avatar
        avatar-size="32px"
        :image-url="currCard?.avatar ?? ''"
        :user-name="currCard?.name"
        round-radius
        class="avatar"
      />
      <UserSelect :default-value="formData.post.ownerId" isolation class="inline-flex!" @change="userChange" />
    </div>

    <div v-if="!onlyOneCard" class="px-16"><t-divider class="my-0!" /></div>

    <div class="main-content">
      <div class="content-inner">
        <div class="input-wrap">
          <MentionEditor
            ref="mentionEditor"
            :offset="{ left: 10, top: !onlyOneCard ? 135 : 82 }"
            @disable-change="disableChange"
            placeholder="期待你的分享..."
            :default-value="formData.post.text"
            :disabled-mention="disabledMention"
          />
          <!-- <t-textarea
            ref="inputRef"
            v-model="formData.post.text"
            class="editor-text"
            placeholder="期待你的分享..."
            :maxlength="1000"
          /> -->
        </div>

        <!--上传图片-->
        <Draggable
          v-if="formData.post.postType === PostType.Picture"
          item-key="index"
          :list="images"
          :animation="400"
          group="imagesGroup"
          class="uploader"
        >
          <template #item="{ element, index }">
            <div :key="element.flag" class="img-wrap sortable">
              <t-image v-if="!element.loading" :src="element.url" fit="cover" class="img">
                <template #loading>
                  <div class="img-wrap sortable flex-col loading">
                    <iconpark-icon name="iconimg" class="icon" />
                    <p class="color-text-2">加载中</p>
                    <iconpark-icon name="iconcleantransparent1" class="btn-close" @click="removeImage(index)" />
                  </div>
                </template>
              </t-image>
              <div v-else class="img-wrap sortable flex-col loading">
                <t-loading />
                <p class="color-text-2 progress-loading">{{ element.progress || '0' }}%</p>
                <iconpark-icon name="iconcleantransparent1" class="btn-close" @click="removeImage(index)" />
              </div>

              <iconpark-icon name="iconcleantransparent1" class="btn-close" @click="removeImage(index)" />
            </div>
          </template>
          <template #footer>
            <ImageUpload
              v-show="canAddPicture"
              v-model="images"
              class="flex"
              :add="true"
              @success="uploadSuccess($event, PostType.Picture)"
              @upload-file="onUploadImageFile"
              @error="onImageUploadError"
            >
              <div class="img-wrap btn-upload flex-col">
                <iconpark-icon name="iconimg" class="icon" />
                <p class="color-text-2">点击上传</p>
              </div>
            </ImageUpload>
          </template>
        </Draggable>

        <!--上传视频-->
        <div v-if="formData.post.postType === PostType.Video" class="uploader">
          <VideoDisplay v-if="video.url" :video="video.url" :large="false" can-remove forum @remove="rmVideo" />
          <div v-else-if="video.status === 'loading'" class="video-wrap sortable flex-col loading">
            <t-loading />
            <p class="color-text-2 progress-loading">{{ video.progress || '0' }}%</p>
            <iconpark-icon name="iconclean" fill="rgba(26, 33, 57, 0.36)" class="btn-close" @click="rmVideo" />
          </div>
        </div>

        <TopicSelectItem :data="selectedTopic" class="ml-24 mb-12" @close="selectedTopic = null" />
      </div>

      <!--工具栏-->
      <div class="toolbar">
        <div class="attachment">
          <EmojiSelector
            v-model="emojiVisible"
            placement="right-bottom"
            :disabled="formData.post.text.length >= 500"
            @select="onSelectEmoji"
          />

          <template v-if="!props.onlyText">
            <template v-if="showImg">
              <div v-if="formData.post.postType === PostType.Video" class="disabled-btn">
                <span :class="['item', { disabled: formData.post.postType === PostType.Video }]">
                  <iconpark-icon name="icon24img" class="icon" />
                </span>
              </div>
              <t-tooltip v-else content="图片">
                <div>
                  <ImageUpload
                    v-model="images"
                    class="flex"
                    :add="true"
                    :disabled="formData.post.postType === PostType.Video"
                    @success="uploadSuccess($event, PostType.Picture)"
                    @upload-file="onUploadImageFile"
                    @error="onImageUploadError"
                  >
                    <span :class="['item', { disabled: formData.post.postType === PostType.Video }]">
                      <iconpark-icon name="icon24img" class="icon" />
                    </span>
                  </ImageUpload>
                </div>
              </t-tooltip>
            </template>

            <template v-if="showVideo">
              <t-tooltip v-if="formData.post.postType !== PostType.Picture && !videos[0]?.url" content="视频">
                <div>
                  <VideoUpload
                    ref="videoUpload"
                    v-model="videos"
                    type="video"
                    class="flex"
                    :limit="300"
                    @success="uploadSuccess($event, PostType.Video)"
                    @error="formData.post.postType = PostType.Text"
                    @changed="onChangedVideo"
                    @upload-file="onUploadVideoFile"
                  >
                    <span :class="['item', { disabled: !!formData.post.content.objects?.length }]">
                      <iconpark-icon name="icon24video" class="icon" />
                    </span>
                  </VideoUpload>
                </div>
              </t-tooltip>
              <div v-else class="disabled-btn">
                <span
                  :class="[
                    'item',
                    {
                      disabled: formData.post.postType === PostType.Picture || !!formData.post.content.objects?.length,
                    },
                  ]"
                >
                  <iconpark-icon name="icon24video" class="icon" />
                </span>
              </div>
            </template>
          </template>

          <TopicSelect :default-value="selectedTopic" @change="selectedTopic = $event" @load="topicLoaded">
            <t-tooltip content="话题" class="item">
              <span @mouseover="topicIconHover = true" @mouseleave="topicIconHover = false">
                <iconpark-icon
                  v-if="topicIconHover && !emptyTopic"
                  name="icon24topicselect"
                  :class="['icon topic-selected', { disabled: emptyTopic }]"
                />
                <iconpark-icon v-else name="icon24topic2" :class="['icon', { disabled: emptyTopic }]" />
              </span>
            </t-tooltip>
          </TopicSelect>

          <template v-if="!disabledMention">
            <t-tooltip content="@" class="item">
              <span @click="mentionPop">
                <img src="@renderer/assets/digital/svg/icon24_@.svg" class="w-24 h-24" />
              </span>
            </t-tooltip>
          </template>
        </div>

        <div class="right">
          <div class="mr-16" v-if="!disabledMention">
            开放加好友
            <t-switch v-model="friendFlag" />
          </div>

          <t-button :disabled="!canPublish" :loading="submitting" class="publish-btn" size="small" @click="submit">
            <!-- <template #icon><iconpark-icon name="iconrelease" class="text-20!" /></template> -->
            {{ $t('square.post.publish') }}
          </t-button>
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<style lang="less" scoped>
.closeicon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: rgba(81, 96, 130, 1);
  position: absolute;
  right: 16px;
  top: 16px;
  cursor: pointer;
  &:hover {
    background-color: rgb(243, 243, 243);
  }
}
.user-wrap {
  display: flex;
  padding: 0 24px 12px;
  align-items: center;
  .avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--kyy_avatar_radius_full, 999px);
    border: 1.4px solid var(--kyy_avatar_border_default, #fff);
  }
}

.main-content {
  background: var(--bg-kyy-color-bg-light, #fff);
}

.content-inner {
  max-height: 492px;
  overflow: hidden;
}

.input-wrap {
  height: 120px;
  margin: 8px 2px 8px 24px;
  .t-textarea {
    width: 100%;
    overflow-y: auto;
  }
  :deep(.t-textarea__limit) {
    display: none;
  }
  :deep(.t-textarea__inner) {
    border-color: transparent !important;
    padding: 0;
    resize: none;
    &:hover,
    &:focus {
      box-shadow: none;
    }
    &::placeholder {
      color: var(--text-kyy_color_text_5, #acb3c0);
      font-size: 16px;
      font-weight: 400;
      line-height: 24px; /* 150% */
    }
  }
}

.toolbar {
  height: 52px;
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-top: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  margin: 0 16px;
  .attachment {
    display: flex;
    flex: 1;
    align-items: center;
    gap: 4px;
    .item,
    :deep(.item) {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 6px;
      border-radius: 4px;
      &:hover {
        background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
      }
      &.disabled {
        color: var(--icon-kyy_color_icon_disabled, #d5dbe4);
        opacity: 0.5;
        pointer-events: none;
      }
    }
    :deep(.icon),
    .icon {
      font-size: 24px;
    }
    .topic-selected {
      color: #4d5eff;
    }
  }

  .right {
    display: flex;
    align-items: center;
    .tool-item {
      display: flex;
      align-items: center;
      margin-right: 8px;
      cursor: pointer;
      padding: 3px 6px;
      border-radius: 4px;
      &:hover {
        background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
      }
      &.disabled {
        color: var(--text-kyy-color-text-3, #828da5);
        cursor: not-allowed;
      }
    }
  }
}

.uploader {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  width: 386px;
  margin: 0 16px;
  padding: 0 24px;
  .video-wrap.loading {
    width: 100px !important;
    height: 100px !important;
    margin-right: 8px !important;
    margin-bottom: 8px !important;
  }
  .video-wrap,
  .img-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    margin-right: 8px;
    margin-bottom: 8px;
    border: 1px solid #e3e6eb;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    &.loading {
      display: flex;
      flex-direction: column;
      border: none;
      width: 100%;
      height: 100%;
      background: var(--bg-kyy-color-bg-deep, #f5f8fe);
      margin: 0;
    }
    &.sortable {
      cursor: move;
    }
    .img {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }
    .play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      z-index: 1;
    }
    .btn-close {
      position: absolute;
      top: 2px;
      right: 2px;
      font-size: 20px;
      cursor: pointer;
      z-index: 1;
    }
  }
  .icon {
    font-size: 48px;
    color: #97989a;
  }
  .btn-upload {
    cursor: pointer;
  }
}

:deep(.t-button__suffix) {
  margin-left: 0 !important;
}

:global(.t-tooltip .t-popup__content) {
  transform: translateX(-5px);
}

.btn-selector {
  cursor: pointer;
  padding: 3px 6px;
  border-radius: 4px;
  &:hover {
    background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
  }
}

.icon-more {
  cursor: pointer;
  border-radius: 4px;
  &:hover {
    background-color: var(--bg-kyy-color-bg-list-foucs, #e1eaff);
    color: var(--kyy_color_dropdown_text_active, #4d5eff);
  }
}

.play-icon {
  font-size: 32px;
  color: #828da5;
}

.file-block {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 0;
  &--item {
    width: 64px;
    height: 64px;
    background-color: #eee;
  }
}
.progress-loading {
  color: #5570fa;
  margin-top: 8px;
  font-weight: bold;
}
.disabled-btn {
  cursor: not-allowed;
}

.file-block {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 0;
  &--item {
    width: 64px;
    height: 64px;
    background-color: #eee;
  }
}
.progress-loading {
  color: #5570fa;
  margin-top: 8px;
  font-weight: bold;
}

.editor-text {
  height: 120px;
  .scrollbar(6px, var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36)));
  &:deep(.t-textarea__inner) {
    height: 100%;
    padding-right: 16px;
    border-radius: 0;
  }
}

.publish-btn {
  width: 72px;
  height: 28px;
  padding: 0px 12px 0px 8px;
  font-size: 14px;
  color: var(--color-button_primary-kyy_color_button_primary_text, #fff);
}
</style>

<style lang="less">
.d-forum-publish {
  position: relative;
  .t-dialog {
    padding: 0;
  }
  .t-dialog__header {
    padding: 16px 16px 16px 24px;
  }
  .t-dialog__body {
    padding: 0;
    border-radius: 0 0 16px 16px;
  }

  .uploader {
    margin: 0;
  }

  .footer {
    display: flex;
    .item {
      display: flex;
      align-items: center;
      margin-right: 24px;
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #717376;
      cursor: pointer;
    }
    .icon {
      font-size: 20px;
      margin-right: 4px;
    }
  }
}
</style>
