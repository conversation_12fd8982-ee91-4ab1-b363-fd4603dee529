<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <link rel="icon" href="/favicon.ico">
  <!-- <meta name="apple-mobile-web-app-capable" content="yes"> -->
  <!-- <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"> -->
  <!-- <meta name="mobile-web-app-capable" content="yes"> -->
  <!-- <meta name="viewport"
    content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"> -->
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <!-- <meta name="viewport" content="width=device-width, initial-scale=1.0"> -->
  <!-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"> -->

  <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js" type="text/javascript"></script>
  <!-- <script src="https://lf1-cdn-tos.bytegoofy.com/obj/iconpark/icons_27918_335.156e1e5ca666c49318f07c4b355c8243.js"></script> -->
  <script
    src="https://lf1-cdn-tos.bytegoofy.com/obj/iconpark/icons_27918_511.5659f0b5fddbd22793e8a9a8622971a4.js"></script>
  <script
    src="https://lf1-cdn-tos.bytegoofy.com/obj/iconpark/svg_18531_250.ec2fd38cbfb075db83336b13d9580891.js"></script>
  <script src="https://castatic.fengkongcloud.cn/pr/v1.0.4/smcp.min.js"></script>
  <title>另可</title>
  <!-- <style>
    body {
      padding-top: env(safe-area-inset-top);
      padding-right: env(safe-area-inset-right);
      padding-bottom: 50px;  /* 兼容不支持 env( ) 的设备  */
      padding-bottom: calc(env(safe-area-inset-bottom) + 50px); /* 在 iphone x + 中本句才会生效 */
      padding-left: env(safe-area-inset-left);
    }
  </style> -->
</head>

<body>
  <script src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script>
  <script>
    document.addEventListener('WeixinOpenTagsError', function (e) {
      console.error(e.detail.errMsg); // 无法使用开放标签的错误原因，需回退兼容。仅无法使用开放标签，JS-SDK其他功能不受影响
    });
    // window.addEventListener('DOMContentLoaded', function() {
    //   var fileInput = document.querySelector('input[type="file"]');
    //   fileInput.capture = "";
    // });
  </script>
  <div id="app"></div>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>
