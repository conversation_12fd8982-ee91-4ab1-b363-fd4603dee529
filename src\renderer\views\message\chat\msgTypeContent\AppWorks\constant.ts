import { i18nt } from '@/i18n';

export const getBtnTxt = (status) => {
  const titleMap = {
    3: i18nt('ebook.vrefuse'),
    2: i18nt('ebook.vtype3'),
    4: i18nt('ebook.vtype6'),
    5: i18nt('ebook.vtype6'),
  };
  return titleMap[status] || null;
};

export const getTitle = (channel_type) => {
  if (channel_type === 'cbd') {
    return i18nt('application.digital_cbd');
  }
  if (channel_type === 'government') {
    return i18nt('application.government');
  }
  if (channel_type === 'member') {
    return i18nt('application.member');
  }
  if (channel_type === 'association') {
    return i18nt('niche.szsq');
  }
  return i18nt('application.member');
};
