<script setup lang="ts">
import { computed, ref } from 'vue';
import { onBeforeRouteLeave, useRoute } from 'vue-router';
import { Button, MessagePlugin } from 'tdesign-vue-next';
import { debounce } from 'lodash';
import { getTopicInfo } from '@renderer/api/forum/topic';
import { getTopicPostList } from '@renderer/api/forum/post';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { vElementVisibility } from '@vueuse/components';
import Empty from '@/components/common/Empty.vue';
import TopicList from '../components/TopicList.vue';
import PostPublishDialog from '../components/post/PostPublishDialog.vue';
import PostList from '@/views/digital-platform/forum/components/post/PostList.vue';
import FloatBtnBox from '@/views/digital-platform/forum/components/FloatBtnBox.vue';
import { refreshBus } from '../../utils/eventBus';
import { checkTopicValid } from '../utils/business';
import { useDigitalPlatformStore } from '../../store/digital-platform-store';
import useTopicPage from '../hooks/useTopicPage';

const route = useRoute();
const digitalStore = useDigitalPlatformStore();
const topicId = route.query.topicID as string;

// 用于刷新列表
const refreshKey = ref(0);
const refreshTopicKey = ref(0);
// 列表加载更多
const pageContent = ref(null);
const scroll = ref(0);
onBeforeRouteLeave((to, from, next) => {
  scroll.value = pageContent.value?.scrollTop;
  next();
});

// 帖子列表
const postListRef = ref(null);
const scrollDisabled = computed(() => postListRef.value?.status === 'finished');
const onLoading = () => {
  postListRef.value?.loading();
};

// 发帖
const postPublishVisible = ref(false);
const postSubmitKey = ref(1);
// const postDefaultVal = ref({ post: { topics: [topicId] } });
const submitPost = () => {
  postPublishVisible.value = false;
  postSubmitKey.value++;
  refreshKey.value++;

  const tab = digitalStore.getActiveTab();
  loadTopicInfo(tab.query.topicID);
};

// 列表排序
const sort = ref(1);
const sortMethod = ref([
  { label: '热门', value: 1 },
  { label: '最新', value: 2 },
]);
const postListParams = ref({ topic: topicId, sort: sort.value });
const noMoreTopic = ref(false);
const topicInfo = ref<any>({});

const { reload, list } = useTopicPage({}, () => ({}));

// 重新加载话题信息
const loadTopicInfo = async (id) => {
  // 话题全被屏蔽
  if (!id) {
    noMoreTopic.value = true;
    return;
  }

  const topic = String(id);
  postListParams.value.topic = topic;
  refreshKey.value++;

  const { data } = await getTopicInfo(topic);
  if (data.code === 0) {
    if (data?.data?.status === 'SHIELD') {
      await MessagePlugin.error('该话题已失效');
      refreshTopicKey.value++;
    }
    topicInfo.value = data.data;
  }
};

// 发布话题
const publishTopic = debounce(async () => {
  const valid = checkTopicValid(topicInfo.value?.topicID);
  if (!valid) return;

  postPublishVisible.value = true;
});

const debounceFn = debounce((value) => {
  sort.value = value;
  postListParams.value.sort = value;
  refreshKey.value++;
});

// 悬浮按钮显示状态
const floatBtnBoxVisible = ref(false);
const onElementVisibility = (state: boolean) => {
  floatBtnBoxVisible.value = !state;
};

// 监听返回顶部
const onBackTop = () => {
  pageContent.value.querySelector('main').scrollTo({
    top: 0,
    behavior: 'smooth',
  });
};

// 检测有无话题，无话题直接显示空页面
const checkHasData = async () => {
  await reload();
  noMoreTopic.value = list.value.length === 0;
  return !noMoreTopic.value;
};

onMountedOrActivated(async () => {
  if (!await checkHasData()) return;

  await loadTopicInfo(topicId);
  refreshTopicKey.value++;
});

refreshBus.on(async (event: string) => {
  if (event === 'refresh' && route.name === 'digital_platform_forum_topic') {
    refreshKey.value++;
    refreshTopicKey.value++;

    if (!await checkHasData()) return;

    const tab = digitalStore.getActiveTab();
    await loadTopicInfo(tab.query.topicID);
  }
});
</script>

<template>
  <div
    ref="pageContent"
    v-infinite-scroll="onLoading"
    :infinite-scroll-immediate-check="false"
    :infinite-scroll-distance="1800"
    :infinite-scroll-disabled="scrollDisabled"
    infinite-scroll-watch-disabled="scrollDisabled"
    class="topic-container"
  >
    <template v-if="noMoreTopic">
      <Empty name="no-friend-list" tip="暂无话题" />
    </template>

    <template v-else>
      <div class="topic-wrap">
        <div class="header">{{ $t('forum.topic') }}</div>

        <TopicList :key="refreshTopicKey" self @load-topic-info="loadTopicInfo" />
      </div>

      <FloatBtnBox
        v-if="floatBtnBoxVisible"
        :position="{right: 'calc((100vw - 838px) / 2 + 20px)', bottom: '40px'}"
        @back-top="onBackTop"
        @publish="publishTopic"
      />

      <main class="pr-2">
        <div class="topic-bg">
          <img class="logo" src="@/assets/digital/svg/forum-msg.svg" alt="">
          <div class="flex-center flex-col py-12 px-24 min-h-94 z-1">
            <div class="flex-center mb-8 title">
              <iconpark-icon name="icontopicfill" class="icon" />
              <div>{{ topicInfo.name }}</div>
            </div>
            <div class="desc">{{ topicInfo.description }}</div>
          </div>
          <div class="flex flex-1 items-center mx-16 mt-4 mb-8 num-wrap">
            <div class="tag items-center">
              <div class="num">帖子 {{ topicInfo.postCount }}</div>
              <div class="line" />
              <div class="look">浏览量 {{ topicInfo.viewCount }}</div>
            </div>
          </div>
        </div>
        <div class="filter-wrap flex justify-between pt-12 pb-4 my-8">
          <Button
            v-element-visibility="onElementVisibility"
            theme="primary"
            class="no-drag"
            @click="publishTopic"
          >
            参与话题
          </Button>

          <PostPublishDialog
            v-if="postPublishVisible"
            v-model="postPublishVisible"
            :topic="topicInfo"
            @submit="submitPost"
          />

          <div class="flex">
            <div
              v-for="item in sortMethod"
              :key="item.value"
              :class="['tag', sort === item.value ? 'active' : '']"
              @click="debounceFn(item.value)"
            >
              {{ item.label }}
            </div>
          </div>
        </div>

        <div class="list-wrap">
          <PostList
            ref="postListRef"
            :key="refreshKey"
            :api="getTopicPostList"
            :params="postListParams"
            go-detail
            class="trends-list"
            from="friend-circle"
            @removed="refreshKey++"
          />
        </div>
      </main>
    </template>
  </div>
</template>

<style lang="less" scoped>
.scrollbar(0);

.topic-container {
  display: flex;
  justify-content: center;
  padding: 4px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  overflow: hidden;
  nav {
    width: 240px;
    height: 624px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--kyy_radius_dropdown_m, 8px);
    align-self: stretch;
    padding: var(--kyy_radius_dropdown_m, 8px);
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
    background: var(--bg-kyy_color_bg_light, #FFF);
  }

  .topic-wrap {
    width: 240px;
    height: 624px;
    display: flex;
    padding: var(--kyy_radius_dropdown_m, 8px);
    flex-direction: column;
    gap: var(--kyy_radius_dropdown_m, 8px);
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    background: var(--bg-kyy_color_bg_light, #fff);
    overflow: hidden;
    .header {
      padding: 8px;
      border-radius: 4px;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);
      color: var(--text-kyy_color_text_1, #1a2139);
      font-size: 16px;
      font-weight: 400;
      line-height: 24px; /* 150% */
    }
  }

  main {
    width: 592px;
    margin-left: 8px;
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    .topic-bg {
      position: relative;
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      width: 100%;
      min-height: 130px;
      max-height: 250px;
      background: url('https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/null/173200043898185919844.png') no-repeat;
      background-size: cover;
      border-radius: 8px;
      border: 1px solid #eceff5;
      overflow: hidden;
      .icon {
        color: #fff;
        align-self: flex-start;
        font-size: 24px;
        margin-right: 8px;
      }
      .logo {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 95.268px;
        height: 108px;
      }
      .title {
        display: flex;
        align-items: center;
        color: var(--text-kyy_color_text_white, #FFF);
        font-family: "PingFang SC";
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px; /* 144.444% */
      }
      .desc {
        flex: 1;
        flex-shrink: 0;
        color: var(--text-kyy_color_text_white, #FFF);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        overflow: hidden;
      }
      .num-wrap {
        color: #FFF;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        .tag {
          display: flex;
          padding: 0px 12px;
          border-radius: 4px;
          background: var(--cyan-kyy_color_cyan_default, #11BDB2);
        }
        .line {
          display: flex;
          align-items: flex-start;
          height: 16px;
          width: 1px;
          background: var(--divider-kyy_color_divider_light, #ECEFF5);
          margin: 0 8px;
        }
      }
    }
    .filter-wrap {
      .tag {
        position: relative;
        display: flex;
        align-items: center;
        padding-right: 17px;
        cursor: pointer;
        color: #516082;
        &:hover {
          color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF);
        }
        &::after {
          content: '';
          position: absolute;
          right: 8px;
          width: 1px;
          height: 16px;
          background: var(--divider-kyy_color_divider_light, #ECEFF5);
        }
        &:last-child {
          padding-right: 0;
          &::after {
            display: none;
          }
        }
      }
      .tag.active {
        color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF);
      }
    }
    .list-wrap {
      flex: 1;
    }
  }
}
</style>
