<template>
  <div>
    <t-dialog
      :visible="corporatePayment"
      :close-btn="false"
      :header="true"
      :cancel-btn="null"
      :confirm-btn="null"
      attach="body"
      :z-index="*********"
      width="728"
      :close-on-esc-keydown="false"
      :close-on-overlay-click="false"
      v-bind="$attrs"
      class="qrcodeDialog"
    >
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>{{ t('order.zf') }}</div>
          <img
            style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="editcorporatePayment"
          />
        </div>
      </template>
      <div v-if="rowData && rowData.amount" class="qrcodes">
        <div class="headM">
          <span class="payment">{{ t('payment.payableAmount') }}:</span>
          <span class="num">
            <span class="fh">{{ rowData.region === 'CN' ? '￥' : 'MOP' }}</span>
            {{ addCommasToNumber(parseFloat(rowData.amount).toFixed(2)) }}
          </span>
        </div>
        <div class="lins">
          <div class="lins-cneter" />
        </div>

        <div class="body-box">
          <div class="payWayStrclass">
            {{ t('order.dgzf') }}
          </div>

          <div v-if="bankItem" class="content-box">
            <div class="content-box-flex">
              <div class="content-box-lable">{{ t('order.zzyhxx') }}</div>
              <div class="content-box-btn" @click="onCopyLink('all')">{{ t('order.qbcopy') }}</div>
            </div>
            <div class="content-info">
              <t-select v-model="bankItem" v-replace-svg placeholder="">
                <t-option v-for="item in options" :key="item" :value="item" :label="item.title"></t-option>
              </t-select>

              <div v-if="bankItem" class="item-box">
                <div v-for="item in rowData.region === 'CN' ? CNArr : MOArr" :key="item" class="info-item">
                  <div class="info-lable">{{ item.lable }}</div>
                  <div class="info-value">{{ bankItem[item.key] ? bankItem[item.key] : '--' }}</div>
                  <img class="info-icon" src="@/assets/img/icon_copy.png" @click="onCopyLink(bankItem[item.key])" />
                </div>
              </div>
            </div>
          </div>
          <div class="content-box" style="padding-top: 0">
            <div class="content-box-flex" :class="bankItem ? '' : 'mt24'">
              <div class="content-box-lable">
                {{ t('order.zysx') }}
                <span class="yellow-text">{{ t('order.qzxyd') }}</span>
              </div>
            </div>
            <div class="info-item-send">
              <div class="info-title">{{ t('order.zzjt') }}</div>
              <div class="info-text">
                {{ t('order.zzjt1') }}
                <span @click="cookiesFlag = true">{{ t('order.zzjt2') }}</span>
                {{ t('order.zzjt3') }}
              </div>
              <div class="info-title">{{ t('order.jeyz') }}</div>
              <div class="info-text">
                {{ t('order.jeye1') }}
              </div>
              <div class="info-title">{{ t('order.jjdf') }}</div>
              <div class="info-text">
                {{ t('order.jjdf1') }}
              </div>
              <div class="info-title">{{ t('order.bzqx') }}</div>
              <div class="info-text">
                {{ t('order.bzqx1') }}
              </div>
              <div class="info-title">{{ t('order.ddyq') }}</div>
              <div class="info-text">
                {{ t('order.ddyq1') }}
              </div>
              <div class="info-title">{{ t('order.ddyq2') }}</div>
              <div class="info-text">
                {{ t('order.ddyq3') }}
              </div>
              <div class="info-title">{{ t('order.ddyq4') }}</div>
              <div class="info-text">
                {{ t('order.ddyq5') }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex-a-js">
          <div class="foot-text">{{ t('order.zzfkhqscdydpz') }}</div>
          <t-button @click="updVoucher">{{ t('order.scpz') }}</t-button>
        </div>
      </template>
    </t-dialog>

    <!-- 转账付款后请上传对应的凭证 -->
    <t-dialog
      :visible="cookiesFlag"
      :close-btn="false"
      :header="true"
      :cancel-btn="null"
      :confirm-btn="null"
      :z-index="*********99"
      attach="body"
      width="728"
      :close-on-esc-keydown="false"
      :close-on-overlay-click="false"
      v-bind="$attrs"
      class="qrcodeDialog"
    >
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>{{ t('order.tips') }}</div>
          <img
            style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="cookiesFlag = false"
          />
        </div>
      </template>
      <div class="voucher">
        <t-tabs :default-value="1">
          <t-tab-panel :value="1" :label="t('order.chinese')" class="tab-lable1">
            <div class="voucher-tab-box">
              <div class="voucher-tab">
                {{ t('order.chinese1') }}
              </div>
              <div class="voucher-img-box">
                <img :src="image2" @click="viewimgFn(image2)" />
                <img :src="image1" @click="viewimgFn(image1)" />
              </div>
            </div>
          </t-tab-panel>
          <t-tab-panel :value="2" :label="t('order.chinesemo')" class="tab-lable2">
            <div class="voucher-tab-box">
              <div class="voucher-tab">
                {{ t('order.chinesemo1') }}
              </div>
              <div class="voucher-img-box">
                <img :src="image3" @click="viewimgFn(image3)" />
                <img :src="image4" @click="viewimgFn(image4)" />
              </div>
            </div>
          </t-tab-panel>
        </t-tabs>
      </div>
    </t-dialog>
    <!-- 上传凭证 -->
    <t-dialog
      :visible="cookiesDataFlag"
      :close-btn="false"
      :header="true"
      :cancel-btn="null"
      :confirm-btn="null"
      :z-index="*********"
      attach="body"
      width="728"
      :close-on-esc-keydown="false"
      :close-on-overlay-click="false"
      v-bind="$attrs"
      class="qrcodeDialog"
    >
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>{{ t('order.scpz') }}</div>
          <img
            style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="cookiesDataFlagClose"
          />
        </div>
      </template>
      <div class="upd-code">
        <div class="upd-code-head-box">
          <span>
            {{ t('order.qsczzhd') }}
            <span style="color: #4d5eff; cursor: pointer" @click="cookiesFlag = true">{{ t('order.pzhzdzhd') }}</span>
            {{ t('order.pzhzdzhd1') }}
          </span>
        </div>
        <div>
          <t-form ref="formRef" label-align="top" :data="formData" :rules="formRules">
            <t-form-item :label="t('order.updtip')" name="voucher">
              <div class="uploadRef-box">
                <t-upload
                  ref="uploadRef"
                  v-model="formData.voucher"
                  theme="image"
                  class="none-box"
                  multiple
                  accept="image/jpg,image/jpeg,image/png"
                  :max="5"
                  :action="null"
                  :on-select-change="onSelectChange"
                />
                <div v-if="optState === 100 && formData.voucher.length < 5" class="upd-box" @click="updFileFn">
                  <!-- <img src="@/assets/img/image6.png" /> -->
                  <iconpark-icon class="updimg" name="iconimg" style="font-size: 32px; color: #828da5"></iconpark-icon>
                  <span style="color: #516082; cursor: pointer">{{ t('order.clickpd') }}</span>
                </div>
                <div>
                  <div class="el-box">
                    <div v-for="(item, index) in formData.voucher" :key="index" class="a-item">
                      <img class="updend-img" :src="item" @click="viewimgFn(item)" />
                      <img
                        v-if="optState === 100"
                        class="close-img"
                        src="@/assets/img/image7.png"
                        @click="delImg(index)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </t-form-item>
            <t-form-item name="phone" :required-mark="false">
              <template #label>
                <div class="flex-a">
                  <span style="color: #d54941; padding-right: 6px">*</span>
                  <span style="margin-right: 4px">{{ t('order.lxdh') }}</span>
                  <t-tooltip
                    :show-arrow="false"
                    :z-index="*********9999"
                    class="icon_help-tooltip"
                    :content="t('order.ptshyywfbynlx')"
                  >
                    <!-- <img src="@/assets/img/icon_help.png" /> -->
                    <iconpark-icon name="iconhelp" style="font-size: 20px; color: #828da5"></iconpark-icon>
                  </t-tooltip>
                </div>
              </template>
              <t-input
                v-if="optState === 100"
                v-model="formData.phone"
                :maxlength="11"
                :placeholder="t('order.qsrlxdh')"
              />
              <div v-else class="value-text">{{ formData.phone }}</div>
            </t-form-item>
            <t-form-item :label="t('order.bz')" name="remark">
              <t-textarea
                v-if="optState === 100"
                v-model="formData.remark"
                :maxlength="100"
                :placeholder="t('order.qsrbz')"
              />
              <div v-else class="value-text">{{ formData.remark ? formData.remark : '--' }}</div>
            </t-form-item>
            <t-form-item v-if="reviewRemark">
              <template #label>
                <div class="flex-a">
                  <span style="margin-right: 4px">{{ t('order.jjyy') }}</span>
                  <t-tooltip :show-arrow="false" class="icon_help-tooltip">
                    <template #content>
                      <p>{{ t('order.qxdd1') }}</p>
                      <p>
                        {{ t('order.qxdd2') }}
                      </p>
                    </template>

                    <img src="@/assets/img/icon_help.png" />
                  </t-tooltip>
                </div>
              </template>
              <div class="value-text">{{ reviewRemark }}</div>
            </t-form-item>
          </t-form>
        </div>
      </div>
      <template #footer>
        <div class="flex-end">
          <t-button v-if="optState === 100" @click="subCookie">{{ t('order.scpz') }}</t-button>
          <div v-if="optState === 300">
            <t-button variant="outline" @click="dilatationFlag = true">{{ t('order.qxdd') }}</t-button>
            <t-button @click="resubmit">{{ t('order.cxtj') }}</t-button>
          </div>
        </div>
      </template>
    </t-dialog>
    <t-dialog
      v-model:visible="dilatationFlag"
      :z-index="*********999"
      :close-btn="false"
      :header="true"
      attach="body"
      :footer="true"
      width="384"
      v-bind="$attrs"
    >
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>{{ $t('order.cancel_order') }}</div>
          <img
            style="width: 24px; cursor: pointer; height: 24px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="dilatationFlag = false"
          />
        </div>
      </template>
      <div class="send-kr-box">
        <t-textarea
          v-model="reason"
          :maxlength="200"
          :autosize="{ minRows: 3, maxRows: 5 }"
          :placeholder="$t('order.please_input_order')"
        />
      </div>
      <template #footer>
        <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
          <t-button theme="default" variant="outline" @click="dilatationFlag = false">
            {{ $t('order.cancel') }}
          </t-button>
          <t-button :disabled="reason ? false : true" @click="sendKR">
            {{ $t('order.sure') }}
          </t-button>
        </div>
      </template>
    </t-dialog>
  </div>
</template>
<script setup lang="ts" name="paymentDialog">
import sdk from '@lynker-desktop/web';
import { ref, watch } from 'vue';
import useClipboard from 'vue-clipboard3';
import { getStsToken } from '@renderer/api/cloud';
import image2 from '@/assets/img/image2.svg';
import image1 from '@/assets/img/image1.svg';
import image3 from '@/assets/img/image3.png';
import image4 from '@/assets/img/image4.svg';
import { orderCancel } from '@renderer/api/myOrder/api/index';

import { payCreate, publicAayApply, publicdetail, banksList } from '@/api/myOrder/api/index';

import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

let CNArr = [
  {
    key: 'receiver',
    value: null,
    lable: t('order.receiver'),
  },
  {
    key: 'account',
    value: null,
    lable: t('order.account'),
  },
  {
    key: 'bank',
    value: null,
    lable: t('order.bank'),
  },
  {
    key: 'remark',
    lable: t('order.remark'),
    value: null,
  },
];
let MOArr = [
  {
    key: 'receiver',
    value: null,
    lable: t('order.receiver'),
  },
  {
    key: 'receiver_en',
    lable: t('order.receiver_en'),
    value: null,
  },
  {
    key: 'bank',
    value: null,
    lable: t('order.bank'),
  },
  {
    key: 'bank_address',
    value: null,
    lable: t('order.bank_address'),
  },
  {
    key: 'swift_code',
    value: null,
    lable: 'Swift Code',
  },
  {
    key: 'account',
    lable: t('order.accountMo'),
    value: null,
  },
  {
    key: 'remark',
    lable: t('order.remark'),
    value: null,
  },
];

const uploadRef = ref(null);

const { toClipboard } = useClipboard();
const formRef = ref(null);
const cookiesFlag = ref(false);
let options = ref([]);
const cookiesDataFlag = ref(false);
//100 未上传 200 已上传 300 已拒绝
const optState = ref(300);
const formData = ref({
  remark: '',
  voucher: [],
  phone: '',
});
const formRules = ref({
  voucher: [{ required: true, message: t('order.qscpz'), type: 'error' }],
  phone: [
    { required: true, message: t('order.qsrlxdh'), type: 'error' },
    {
      pattern: /^[0-9]+$/,
      message: t('order.pleaseenteranumber'),
    },
  ],
});
const props = defineProps({
  corporatePaymentFlag: {
    type: Boolean,
    default: true,
  },
  invoiceFlag: {
    type: Number,
    default: 1,
  },
  defaultInvoice: {
    type: Object,
    default: () => {},
  },
  rowData: {
    type: Object,
    default: () => {},
  },
});
const reviewRemark = ref(null);
const reason = ref('');
const corporatePayment = ref(props.corporatePaymentFlag);
const dilatationFlag = ref(false);
const emits = defineEmits(['update:corporatePaymentFlag', 'subCookieSuccess', 'paymentCallback']);
const client = ref(null);
const paddingUdpList = ref([]);
let payData = ref({});
let detValueData = ref(null);
const bankItem = ref(null);

const banksListFn = () => {
  banksList({
    order_sn: props.rowData.sn,
  }).then((res) => {
    options.value = res.data.list;
    for (let index = 0; index < res.data.list.length; index++) {
      res.data.list[index].remark = `${res.data.orderSn};${res.data.teamName}`;
      if (res.data.list[index].default) {
        bankItem.value = res.data.list[index];
      }
    }
    if (options.value.length > 0 && !bankItem.value) {
      bankItem.value = options.value[0];
    }
  });
};
const getLink = (sn) => {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    console.log(props.defaultInvoice, 'props.defaultInvoiceprops.defaultInvoice');
    try {
      const res = await payCreate({
        order_sn: sn,
        client_type: 0,
        payment: 4,
        invoice_header: props.defaultInvoice && props.invoiceFlag !== 1 ? JSON.stringify(props.defaultInvoice) : '',
      });
      if (res.status !== 200) {
        reject(res);
      } else {
        payData.value = res.data;
        if (Array.isArray(res.data.params)) {
          console.log(res, '11啊实打实大大撒');
          optState.value = 100;
        }
        resolve(res);
      }
    } catch (err) {
      MessagePlugin.error({
        content: err.data.message,
        zIndex: *********,
      });
      reject(err);
    }
  });
};
const getPublicdetail = (val) => {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      const det = await publicdetail(val);
      detValueData.value = det.data;
      formData.value.voucher = JSON.parse(det.data.voucher);
      formData.value.remark = det.data.applyRemark;
      formData.value.phone = det.data.phone;
      if (det.data.status === 2) {
        optState.value = 300;
      } else {
        optState.value = 200;
      }
      reviewRemark.value = det.data.reviewRemark;
      resolve(det);
    } catch (err) {
      reject(err);
    }
  });
};
const updVoucher = () => {
  if (payData.value && Array.isArray(payData.value.params)) {
    optState.value = 100;
    cookiesDataFlag.value = true;
    return;
  }
  getPublicdetail({ id: payData.value.params.id }).then(() => {
    cookiesDataFlag.value = true;
  });
};
const editcorporatePayment = () => {
  if (payData.value && !Array.isArray(payData.value.params)) {
    corporatePayment.value = false;
    emits('update:corporatePaymentFlag', false);
    emits('paymentCallback');

    return;
  }
  const confirmDia = DialogPlugin({
    header: t('order.tips'),
    theme: 'info',
    class: 'delmode',
    body: t('order.yscdsgddtip'),
    closeBtn: null,
    zIndex: *********9999,
    confirmBtn: t('address.sure'),
    cancelBtn: t('address.cancel'),
    onClose: () => {
      confirmDia.hide();
      // corporatePayment.value = false;
      // emits('update:corporatePaymentFlag', false);
    },
    onConfirm: () => {
      confirmDia.hide();
      // cookiesDataFlag.value = true;
      corporatePayment.value = false;

      emits('paymentCallback');

      emits('update:corporatePaymentFlag', false);
    },
  });
};
const subCookie = async () => {
  const res = await formRef.value.validate();

  if (res === true) {
    try {
      let objs = {
        pay_sn: payData.value.sn,
        remark: formData.value.remark,
        phone: formData.value.phone,
        voucher: formData.value.voucher,
      };
      const e = await publicAayApply(objs);
      if (e.status === 200) {
        corporatePayment.value = false;
        cookiesDataFlagClose();
        emits('subCookieSuccess');
        emits('paymentCallback');
        emits('update:corporatePaymentFlag', false);
        MessagePlugin.success({
          content: '操作成功',
          zIndex: *********,
        });
      } else {
        MessagePlugin.error({
          zIndex: *********,
          content: e.data.message,
        });
      }
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      return MessagePlugin.error({
        content: errMsg,
        zIndex: *********99,
      });
    }
  }
};
const formatDate = (timeStamp) => {
  const date = new Date(timeStamp); // 创建Date对象
  const year = date.getFullYear(); // 获取年份
  const month = date.getMonth() + 1; // 获取月份，记得+1
  const day = date.getDate(); // 获取日期
  return `${year}/${month}/${day}`; // 返回格式化后的日期字符串
};
const delImg = (index) => {
  formData.value.voucher.splice(index, 1);
};
const viewimgFn = (item) => {
  sdk.ipcRenderer.invoke(
    'preview-file',
    JSON.stringify({
      url: item,
      type: 'jpg',
    }),
  );
};
const sendKR = () => {
  if (props.rowData && props.rowData.sn) {
    orderCancel({
      sn: props.rowData.sn,
      reason: reason.value,
    }).then((res) => {
      if (res.data.code === 0) {
        dilatationFlag.value = false;
      } else {
        MessagePlugin.error(t('order.Theorderstatusoperation'));
      }
    });
  }
};
const onSelectChange = async (file) => {
  const arr = ['image/webp', 'image/gif', 'image/bmp', 'image/png', 'image/jpg', 'image/jpeg', 'image/svg'];
  const filteredBrr = file.filter((item) => !arr.some((type) => item.type === type));
  if (filteredBrr.length > 0) {
    MessagePlugin.error({
      zIndex: *********,
      content: t('order.wjgscw'),
    });
    return;
  }
  const maxlength = 5;
  if (file.length + formData.value.voucher.length > maxlength) {
    MessagePlugin.error({
      zIndex: *********,
      content: t('order.cgzdscsl'),
    });
    return;
  }
  const max = file.some((item) => item.size > 100 * 1024 * 1024);
  if (max) {
    MessagePlugin.error({
      zIndex: *********,
      content: '请上传小于100M文件',
    });
    return;
  }
  paddingUdpList.value = file;
  getStsToken().then((res) => {
    client.value = new OSS({
      region: 'oss-cn-shenzhen',
      accessKeyId: res.data.AccessKeyId,
      accessKeySecret: res.data.AccessKeySecret,
      stsToken: res.data.SecurityToken,
      refreshSTSTokenInterval: 300000,
      bucket: 'kuaiyouyi',
    });
    paddingUdpList.value.map((e) => {
      const fileName = e.name;
      const types = fileName.substring(fileName.lastIndexOf('.') + 1);
      const newName = `disk/${formatDate(new Date().getTime())}/${fileName + new Date().getTime()}.${types}`;
      client.value
        .multipartUpload(newName, e)
        .then((res) => {
          const url = res.res.requestUrls[0].replace(/\?.*/, '');
          formData.value.voucher.push(url);
        })
        .catch((err) => {
          console.log(err, 'errerrerr');
        });
    });
  });
};

const updFileFn = () => {
  uploadRef.value.triggerUpload();
};
const addCommasToNumber = (str) => {
  let [integerPart, decimalPart] = str.split('.');
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  if (decimalPart) {
    decimalPart = decimalPart.length === 1 ? `${decimalPart}0` : decimalPart.slice(0, 2);
  } else {
    decimalPart = '00';
  }
  return `${integerPart}.${decimalPart}`;
};

const onCopyLink = async (val) => {
  try {
    if (val === 'all') {
      const area = props.rowData.region === 'CN' ? CNArr : MOArr;
      const areaValue = area.map((e) => `${e.lable}:${bankItem.value[e.key]}`);
      await toClipboard(areaValue.join('\n'));
    } else {
      await toClipboard(val);
    }
    MessagePlugin.success({
      content: t('order.czcg'),
      zIndex: *********,
    });
  } catch (e) {
    MessagePlugin.error({
      content: t('order.czsb'),
      zIndex: *********,
    });
  }
};

const cookiesDataFlagClose = () => {
  cookiesDataFlag.value = false;
  formData.value.remark = '';
  formData.value.voucher = [];
  formData.value.phone = '';
  formRef.value.clearValidate();
  formRef.value.reset();
};
const resubmit = () => {
  optState.value = 100;
  payData.value.sn = detValueData.value.paySn;
};

// 打开上传凭证弹窗
const openCookiesFn = (val) => {
  console.log(val, 'aaaaaaaaa');
  if (val.id) {
    getPublicdetail(val).then(() => {
      cookiesDataFlag.value = true;
    });
  } else {
    payData.value.sn = val.sn;
    optState.value = 100;
    cookiesDataFlag.value = true;
    return;
  }
};
watch(
  () => props.corporatePaymentFlag,
  (newvalue) => {
    corporatePayment.value = newvalue;
    if (newvalue) {
      if (props.rowData && props.rowData.sn) {
        banksListFn();
        getLink(props.rowData.sn, props.rowData.region);
      }
    }
  },
);
defineExpose({
  openCookiesFn,
});
</script>
<style scoped lang="less">
.payWayStrclass {
  height: 32px;
  background: #4d5eff;
  border-radius: 0px 0px 4px 0px;
  display: inline-block;
  font-size: 12px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: center;
  color: #ffffff;
  line-height: 32px;
  padding: 0 6px;
}

.qrcodes {
  margin-top: 8px;
  max-height: 400px;
  overflow: auto;
  .foot-boxs {
    width: 300px;
    cursor: pointer;
    height: 72px;
    border: 1px solid #eceff5;
    border-radius: 4px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
    margin-left: 16px;
    img {
      width: 32px;
      height: 32px;
      margin-right: 8px;
    }
    div {
      height: 22px;
      font-size: 14px;
      font-family:
        Microsoft YaHei,
        Microsoft YaHei-Regular;
      font-weight: 400;
      color: #1a2139;
      line-height: 22px;
    }
  }
}

.lins {
  width: 644px;
  height: 12px;
  background: #eceff5;
  border-radius: 5px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  .lins-cneter {
    width: 636px;
    height: 6px;
    background: #5f7292;
    border-radius: 3px;
  }
}

.foot-text {
  color: var(--text-kyy_color_text_3, #828da5);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.flex-end {
  display: flex;
  justify-content: end;
}
.upd-box {
  display: flex;
  width: 78px;
  height: 78px;
  padding: 14px;
  cursor: pointer;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 8px;
  border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
  background: var(--kyy_color_upload_bg, #fff);
  span {
    color: var(--kyy_color_upload_text_default, #516082);
    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}
.none-box {
  display: none;
}
.el-box {
  display: flex;
  align-items: center;
  gap: 8px;
}
.uploadRef-box {
  display: flex;
  align-items: center;
  gap: 8px;
}
.a-item:hover {
  .close-img {
    display: block !important;
  }
}
.value-text {
  color: #1a2139;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.a-item {
  position: relative;
  .updend-img {
    display: flex;
    width: 78px;
    height: 78px;
    justify-content: flex-end;
    align-items: center;
    border-radius: 8px;
  }
  .close-img {
    position: absolute;
    top: 1px;
    right: 1px;
    display: none;
    cursor: pointer;
  }
}
.flex-a-js {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-a {
  display: flex;
  align-items: center;
}
.upd-code {
  .upd-code-head-box {
    display: flex;
    padding: 8px 24px;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: var(--kyy_color_alert_bg_bule, #eaecff);
    color: var(--kyy_color_alert_text, #1a2139);
    font-size: 14px;
    margin-bottom: 24px;
  }
}
.mt46 {
  margin-top: 24px;
}
:deep(.t-tabs__nav-item) {
  color: #1a2139;
}
:deep(.t-tabs__bar.t-is-top) {
  transform: scaleX(0.3);
  bottom: 1px;
}
:deep(.t-tabs__nav-container.t-is-top::after) {
  background-color: var(--divider-kyy_color_divider_light, #eceff5);
}
.voucher {
  max-height: 440px;
  overflow: auto;
  .voucher-img-box {
    display: flex;
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    align-self: stretch;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
    img {
      width: 100%;
    }
  }
  .voucher-tab-box {
    .voucher-tab {
      display: flex;
      padding: 8px 24px;
      align-items: center;
      gap: 8px;
      align-self: stretch;
      border-radius: 8px;
      background: var(--kyy_color_alert_bg_bule, #eaecff);
      color: var(--kyy_color_alert_text, #1a2139);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      margin-bottom: 16px;
      margin-top: 16px;
    }
  }
}
.body-box {
  width: 632px;
  margin: -5px auto 24px;
  background: #ffffff;
  position: relative;
  border-radius: 0px 0px 8px 8px;
  border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);
}

.headM {
  margin-bottom: 16px;
  text-align: center;
  .fh {
    color: var(--error-kyy_color_error_default, #d54941);

    /* kyy_fontSize_2/bold */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .num {
    color: var(--error-kyy_color_error_default, #d54941);

    /* kyy_fontSize_4/bold */
    font-family: 'PingFang SC';
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px; /* 144.444% */
  }
  .payment {
    color: var(--text-kyy_color_text_1, #1a2139);
    /* kyy_fontSize_2/regular */
    font-size: 14px;
    display: inline-block;
    width: 70px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

.content-box-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .content-box-lable {
    color: var(--text-kyy_color_text_1, #1a2139);
    font-size: 16px;
    font-style: normal;
    margin-left: 8px;
    position: relative;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
  .content-box-btn {
    cursor: pointer;
    color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
    text-align: center;
    font-size: 14px;
    font-style: normal;
    position: relative;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .content-box-lable::after {
    content: '';
    width: 3px;
    height: 16px;
    position: absolute;
    top: 5px;
    left: -8px;
    border-radius: 8px;
    background: var(--brand-kyy_color_brand_default, #4d5eff);
  }
}
.content-info {
  /* padding:16px; */
  display: flex;
  width: 584px;
  /* margin-bottom: 22px; */
  padding: 16px;
  margin-top: 16px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  gap: 16px;
}
.item-box {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}
.info-item {
  display: flex;
  align-items: center;
  .info-lable {
    color: var(--text-kyy_color_text_3, #828da5);
    width: 120px;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    margin-right: 16px;
    line-height: 22px; /* 157.143% */
  }
  .info-value {
    width: 372px;
    color: var(--text-kyy_color_text_1, #1a2139);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    margin-right: 24px;
  }
}
.info-icon {
  width: 20px;
  height: 20px;
  cursor: pointer;
}
.content-box {
  padding: 16px 24px 24px 24px;
}
.yellow-text {
  color: var(--warning-kyy_color_warning_default, #fc7c14);
  font-size: 14px;
  width: 98px;
  display: inline-block;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.info-item-send {
  margin-top: 16px;
}
.info-title {
  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
}
.info-text {
  color: var(--text-kyy_color_text_1, #1a2139);

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  margin-bottom: 8px;
  line-height: 22px; /* 157.143% */
  span {
    color: var(--brand-kyy_color_brand_default, #4d5eff);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    cursor: pointer;
  }
}
</style>
