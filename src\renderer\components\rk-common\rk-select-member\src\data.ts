import chartGraph from '@renderer/assets/svg/icon_organize.svg';
import { getPlatformUser,getPlatformAdmin } from '@renderer/api/contacts/api/organize';
import { getAllDepartmentList } from '@renderer/api/contacts/api/organize';
import { searchExternalCardList } from "@/api/contacts/api/friend";
import { recentContactList } from '@renderer/api/contacts/api/recent';
import { getOpenid, getCards } from '@renderer/utils/auth';
import { innerCardsDetails } from '@renderer/api/contacts/api/follow';
import { getNoteList } from '@renderer/api/contacts/api/common';
import { pairsList } from '@renderer/api/contacts/api/organize';
import { getGroupList } from '@renderer/api/contacts/api/group';
import { handelCardIds } from '@renderer/views/contacts/utils';
import { getTagList, tagDetail } from '@renderer/api/contacts/api/tag';
import { cardIdType, cardRelation } from '@renderer/views/identitycard/data';
import { getGovernmentStaffsPlatform, getGroupMemberApi } from '@renderer/api/im/api';
import { getContacts, getRepresentatives } from '@renderer/api/contacts/api/other';
// attachment.member 第一个人永远是自己
// 操作人的组织个人信息,用于匹配建立同组织的关系创建
let _orgMyInfo = [];
let _platformList = []
const _transAttachMemberCardModel = (v:any) => {
  if (!v) return {};
  return {
    "avatar": v?.avatar || '',
    "cardId": v.cardId,
    "openId": v.openId,
    "staffName": v.name,
    "stayOn": 0,
    "nickname": '',
    "comment": '',
    "teamName": v.team || '',
    "teamId": v.teamId || '',
    "departmentId": v?.position[0]?.departmentId || '',
    "departmentName": v?.position[0]?.departmentName || '',
    "jobName": v?.position[0]?.jobName || '',
    "jobId": '',
  };
}
const _transAttachInnerMemberCardModel = (v:any) => {
  if (!v) return {};
  return {
    "avatar": v?.avatar || '',
    "cardId": v.cardId,
    "openId": v.openId,
    "staffName": v.name,
    "stayOn": 0,
    "nickname": '',
    "comment": '',
    "teamName": v.team || '',
    "teamId": v.teamId || '',
    "departmentId": v?.idDepartment || '',
    "departmentName": v?.departmentName || '',
    "jobName": v?.jobName || '',
    "jobId": '',
  };
}
//平台人员数据结构转换
const _transAttachPlatMemberCardModel = (v:any) => {
  if (!v) return {};
  return {
    "avatar": v?.staffAvatar || '',
    "cardId": v.cardId,
    "openId": v.openid,
    "staffName": v.staffName,
    "stayOn": 0,
    "nickname": '',
    "comment": '',
    "teamName": v.teamName || '',
    "teamId": v.teamId || '',
    "departmentId": '',
    "departmentName":'',
    "jobName":v.jobName || '',
    "jobId": '',
  };
}

//conversationType 群聊3，单聊1
const _groupsList = (list, card_ids) => {
  return list.reduce((acc, cur) => {
    const group = {
      targetId: cur.group,
      cardId: card_ids[0],
      avatar: cur.attachment?.avatar || cur.name,
      team: '',
      teamId: '',
      openId: '',
      name: cur.name,
      from: 'groups',
      conversationType: 3,
      select: false,
    }
    acc.push(group);
    return acc;
  }, []);
};

// 应用标识（商协：member，政企：government，cbd：cbd）
const orgTypeMap = {
  2: 'member',
  4: 'government',
  1: 'cbd'
};
// 组合已经配对的数据
const _polyPairsList = async (data, from) => {
  const rela = {
    'friend': ['FRIEND'],
    'orgcontacts': ['BUSINESS', 'CONSULT'],
    'recent': [],
  }[from];
  let ids = [];
  let listData = await data.reduce((acc, cur) => {
    if (!rela?.length || rela.includes(cur.origin)) {
      // const mainInfo = cur.attachment.member.find(v => v.cardId === cur.main);
      const peerInfo = cur.card
      const renderData = {
        targetId: peerInfo.cardId,
        cardId: peerInfo.cardId,
        avatar: peerInfo.avatar || '',
        team: peerInfo.teamName || '',
        teamId: peerInfo.teamId || '',
        openId: peerInfo.openId || '',
        name: cur.comment || peerInfo.cardName,
        from,
        conversationType: 1,
        select: false,
        attachment: cur.attachment,
      }
      !~ids.findIndex(id => id === cur.peer) && ids.push(cur.peer);
      acc.push(renderData);
    }
    return acc;
  }, [])

  // listData已经是详情了，为啥还要去handelCardIds再获取一遍详情？导致平台身份格式错误先注释

  // const transData = await handelCardIds(ids,from);
  // listData = listData.map(v => {
  //   const peerInfo = transData.find(t => t.cardId === v.targetId);
  //   v.avatar = peerInfo?.avatar || '';
  //   v.name = peerInfo?.comment || peerInfo?.staffName || '';
  //   v.team = peerInfo?.teamName || '';
  //   v.teamId = peerInfo?.teamId || '';
  //   return v;
  // })
  return listData;
}
const _getPairList = async (type) => {
   let list = [];
    // if (card_ids.length === 0) return list;
    const res = await pairsList();
    if (res.status === 200 && res.data.data.relations?.length) {
     const filterList = _filterDel(res.data.data.relations);
     const newList = filterList.map(item => {
        return {
          card: item.friendCard,
          myCard: item.selfCard,
          comment: item.comment,
          peer: item.cardIdFriend,
          origin: item.pairType,
          attachment: {
            member: [item.friendCard, item.selfCard ],
            creatorCardId: item.cardIdFriend,
            relation: item.pairType,
          }
        }
      })
      list = await _polyPairsList(newList, type);
    }
    console.log(list,'list--------------------------');
    return list;
}
const _replaceNote = async (list, listData) => {
  // 获取备注信息
  const {data} = await getNoteList({cardIds: list.map(v => v.cardId)});
  if (data.code === 0) {
    data.data?.cardRemarks.forEach(item => {
      const itemData = listData.find(v => v.cardId === item.cardId);
      itemData.name = item.remarks;
      itemData.attachment.member[1].comment = item.remarks;
    });
  }
  return listData;
};

// 过滤掉被单向删除的好友，商务关系之类的+注销、退出组织
const _filterDel = (list) => {
  return list.filter(v => !v.hasDel && (!v.deleted || v.deleted == 0) && !v.unregistered);
}


const selectData = {
  groupMemberList: async (groupID: string) => {
    const members = await selectData.groupMemberList(groupID);
    const groupMember = [];
    members.forEach(val => {
      groupMember.push({
        avatar: val.attachments.avatar,
        cardId: val.attachments.cardId,
        conversationType: 1,
        from: 'groupMember',
        name: val.attachments.staffName,
        openId: val.attachments.openId,
        select: false,
        targetId: val.attachments.cardId,
        });
    });
    return groupMember;
  },
  externalList: async (cardId: Array<string>) => {
    // if (!teamId.length) return [];
    let list = getCards().reduce((acc, cur) => {
      if (~cardId.findIndex(v => v === cur.uuid)) {
        const org = {
          id: cur.internal_team_id,
          name: cur.team,
          teamId: cur.teamId,
          cardId: cur.uuid,
          type: `${cur.internal_team_id}`,
          icon: chartGraph,
        }
        acc.push(org);
      }
      return acc;
    }, [])
    return list;
  },
  // team 外部组织的信息
  externalCardList: async (team) => {
    const {data} = await searchExternalCardList({ teamId: team.teamId }, team.teamId);
    let ids = data.data.map(v => v.cardId);
    // 把我在当前组织下的身份卡加进去一起查询身份卡详情
    ids.push(team.cardId);
    const list = await handelCardIds(ids);
    // 我的身份卡信息，用于构造聊天数据
    const myInfo = list.find(v => v.cardId === team.cardId);
    // 同一个外部组织是同事关系
    const staffData = data.data.reduce((acc, cur) => {
      const peerInfo = list.find(v => v.cardId === cur.cardId);
      const orgData = {
        targetId: cur.cardId,
        cardId: cur.cardId,
        avatar: cur.avatar || '',
        team: team.name,
        teamId: team.id,
        openId: cur.staffOpenId,
        name: peerInfo.comment || cur.name,
        from: team.name,
        conversationType: 1,
        select: false,
        attachment: {
          member: [myInfo,peerInfo],
          creatorCardId: team.cardId,
          relation: 'CO_WORKER',
        },
      }
      acc.push(orgData);
      return acc;
    }, [])
    return staffData;
  },
  // team 客服外部组织的信息
  externalkefuCardList: async (params) => {
    const { data } = await searchExternalCardList(params, params.teamId);
    const ids = data.data.map((v) => v.cardId);
    // 把我在当前组织下的身份卡加进去一起查询身份卡详情
    const list = await handelCardIds(ids);
    // 我的身份卡信息，用于构造聊天数据
    // 同一个外部组织是同事关系
    const staffData = data.data.reduce((acc, cur) => {
      const peerInfo = list.find((v) => v.cardId === cur.cardId);
      const orgData = {
        targetId: cur.cardId,
        cardId: cur.cardId,
        avatar: cur.avatar || '',
        team: cur.teamName,
        teamId: cur.teamId,
        type: cur.teamId,
        openId: cur.staffOpenId,
        name: peerInfo.comment || cur.name,
        from: cur.name,
        conversationType: 1,
        select: false,
        attachment: {
          member: list,
          creatorCardId: null,
          relation: 'CO_WORKER',
        },
      };
      acc.push(orgData);
      return acc;
    }, []);
    return staffData;
  },
  // 数字城市代表人查询
  getGovernmentStaffsPlatform: async (myCard, groupId) => {

    const { data } = await getGroupMemberApi(groupId, myCard);
    if (data) {
      const teamId = data?.attachments?.teamId;
      const { data: data2 } = await getGovernmentStaffsPlatform(teamId);
      if (data2.code === 0 && data2?.data?.list?.length) {
        const staffData = data2.data?.list.reduce((acc, cur) => {
          const orgData = {
            targetId: cur.card_id,
            cardId: cur.card_id,
            avatar: cur.contacts_logo || '',
            team: '',
            teamId: cur.team_id,
            openId: cur.openid,
            name: cur.contacts_name,
            from: cur.contacts_name,
            conversationType: 1,
            select: false,
          };
          acc.push(orgData);
          return acc;
        }, []);
        return staffData;
      }
    }
  },
  organizeList: async (orgList) => {
    if (!orgList.length) return [];
    let list = [];
      const staffIds = []
      orgList.map(v => { parseInt(v.cardId.slice(1)) ? staffIds.push(parseInt(v.cardId.slice(1))) :''});
      if(!staffIds.length) return []
      const res =  await innerCardsDetails({ids: staffIds});
      const { code, data: staffData } = res.data;
      if (code === 0) {
         const list = orgList.map(cur => {
          const org = {
            name: cur.name,
            teamId: cur.teamId,
            id: cur.teamId,
            cardId: cur.cardId,
            type: 'organize',
            platType: cur.platform?.[0]?.type,
            icon: chartGraph,
          }
          _orgMyInfo.push({
            teamId: cur.teamId,
            cardInfo: staffData.find(v => v.cardId === cur.cardId),
          })
          return  org;
        })
        return list.reverse();
      }
    return list;
  },
  departmentList: async (teamId:string, name?:string) => {
    const {data} = await getAllDepartmentList({teamId, defaultParent:1});
    // 递归处理部门信息
    const processDepartment = (department: any) => {
      // 处理当前部门的 staffs
      if (department.staffs?.length) {
        department.staffs = selectData.combOrgData(department.staffs, {teamId, name})
      }

      // 递归处理子部门
      if (department.children?.length) {
        department.children = department.children.map(processDepartment);
      }

      return department;
    };

    // 处理根部门的 children
    const departmentInfoList = data.data?.children?.length
      ? data.data.children.map(processDepartment)
      : [];
    // const departmentInfoList = data.data?.children || [];
    const staffList = data.data?.staffs.length ? selectData.combOrgData(data.data?.staffs, {teamId, name}) : [];
    return {teamId, name, departmentInfoList, staffList};
  },
  recentList: async (card_ids: Array<string>) => {
    const params = {
      index: 0,
      size: 100,
      cardIds: card_ids,
    }
    const res = await recentContactList(params);
    if (res.status === 200 && res.data.data.pairs?.length) {
      const list = _filterDel(res.data.data.pairs);
      return _polyPairsList(list, 'recent');
    }
    return [];
  },
  friendList: async () => {
    return await _getPairList('friend');
  },
  getPairsList: async (ids: Array<string>) => {
    const card_ids = ids.filter(v => v[0] !== '$');
    if (!card_ids.length) {
      return [];
    }
    return await _getPairList('orgcontacts')
  },
  groupsList: async (card_ids: Array<string>, groupOption: { family?: boolean } = {}) => {
    const params = {
      index: 0,
      size: 1000,
      group: {
        card_ids,
        addr: true,
        ...groupOption
      },
    }
    const res = await getGroupList(params);
    console.log(res.data.array.groups?.arr,'res.data.array.groups?.arr--------------------------');

    let list = [];
    if (res.status === 200) {
      res.data.array.groups?.arr.length && (list = _groupsList(res.data.array.groups.arr, card_ids));
    }
    return list;
  },
  tagList: async (cardId?) => {
    const {data} = await getTagList(cardId ? {cardId} : null);
    let list = [];
    if (data?.tags && data.tags.length) {
      list = data.tags.map(v => {
        v['select'] = false;
        return v;
      })
    }
    return list;
  },
  tagDetailList: async (id, myCardId, showMenuGroups?) => {
    const {data} = await tagDetail(id);
    console.log(data);
    let list = [];
    if (data?.list && data.list.length) {
      list = await data.list.reduce(async (acc, cur) => {
        const emo = await acc;
        // 选人组件的标签根据是否显示群组菜单过滤群组选择
        if (showMenuGroups && cur.type === 2) {
          const group = {
            targetId: cur.group.group,
            cardId: cur.group.group,
            avatar: cur.group.avatar || '',
            team: '',
            teamId: '',
            openId: '',
            name: cur.group.name,
            from: 'tags',
            conversationType: 3,
            select: false,
            attachment: cur.group.attachment
          }
          emo.push(group);
        }

        if (cur.type === 1) {
          const { rela } = await cardRelation(cur.card.card_id, myCardId);
          const msgData = {
            targetId: cur.card.card_id,
            cardId: cur.card.card_id,
            avatar: cur.card?.avatar || '',
            team: cur.card?.team_name || '',
            teamId: cur.card?.team_id || '',
            openId: cur.card.openid,
            name: cur.card.card_name,
            from: 'tags',
            conversationType: 1,
            select: false,
            attachment: {
              // 现在只用到里面的cardId，其他参数不需要就不构造了
              member: [{cardId:myCardId},{cardId:cur.card.card_id}],
              creatorCardId: myCardId,
              relation: rela,
            },
          }
          emo.push(msgData);
        }
        return emo;
      }, [])
    }
    return list;
  },
  combOrgData: (data:Array<any>, obj:{name:string,teamId:string,departmentId?:string,departmentName?:string}) => {
    if (!data.length) {
      return [];
    }
    const myCardInfo = _orgMyInfo.find(v => v.teamId === obj.teamId)?.cardInfo;
    // const staffIds = data.map(item => item.idStaff);
    // const res = await innerCardsDetails({ids: staffIds});

    // const { code, data: staffData } = res.data;
    // if (code === 0) {
      const list = data.map(v => {
        // const peerInfo = staffData.find(item => item.cardId === v.cardId);
        const orgData = {
          targetId: v.cardId,
          cardId: v.cardId,
          avatar: v.avatar || '',
          team: obj.name,
          teamId: obj.teamId,
          openId: v.openId,
          name: v.name,
          from: obj.name,
          conversationType: 1,
          select: false,
          attachment: {
            member: [_transAttachMemberCardModel(myCardInfo),_transAttachInnerMemberCardModel({
              ...v,
              team: obj.name,
              teamId: obj.teamId,
            })],
            creatorCardId: myCardInfo?.cardId,
            relation: 'CO_WORKER',
          },
        }
        return orgData;
      })
      // const showList = await _replaceNote(list, list);
      return list || [];
    // }
  },

  getRepresentatives: async (parma) => {
    const { teamId, myCardId: cardId, type } = parma;
    if (teamId) {
      const { data } = await getRepresentatives(teamId, type || 'government', cardId);
      if (data.code === 0) {
        const { group_list, user_list, team } = data.data;
        const group_list_item = group_list.reduce((acc, cur) => { return [...cur.user_list, ...acc] }, []);
        _platformList = [...group_list_item, ...user_list]?.map(v => ({
          ...v,
          teamId: team.team_id,
          teamName: team.team_name,
          cardId: v.card_id,
          staffName: v.name,
          staffAvatar: v.avatar,
        }));

        let group_list_p = [];
        let user_list_p = [];
        if (group_list.length > 0) {
          const group_list_user_list = selectData.combPlatPRData('group', group_list || [], team, cardId);

          group_list_p = group_list_user_list;
        }
        if (user_list.length > 0) {
          user_list_p = selectData.combPlatPRData('staff', user_list || [], team, cardId);
        }
        return [group_list_p, user_list_p];
      }
    }
    return [[], []];
  },
  getContacts: async (parma) => {
    const { teamId, type, cardId } = parma;
    if (teamId) {
      const { data } = await getContacts(teamId, type || 'government');
      if (data.code === 0) {
        const { group_list, user_list, team } = data.data;
        const group_list_item = group_list.reduce((acc, cur) => { return [...cur.user_list, ...acc] }, []);
        _platformList = [...group_list_item, ...user_list]?.map(v => ({
          ...v,
          teamId: team.team_id,
          teamName: team.team_name,
          cardId: v.card_id,
          staffName: v.name,
          staffAvatar: v.avatar,
        }));

        let group_list_p = [];
        let user_list_p = [];
        if (group_list.length > 0) {
          const group_list_user_list = selectData.combPlatPRData('group', group_list || [], team, cardId);

          group_list_p = group_list_user_list;
        }
        if (user_list.length > 0) {
          user_list_p = selectData.combPlatPRData('staff', user_list || [], team, cardId);
        }
        _platformList = data.data || [];
        return [group_list_p, user_list_p];
      }
    }
    return [[], []];
  },
  // 获取平台成员列表数据并转换结构
  getPlatformUserList:async (parma) => {
    const type = typeof (parma?.type) === "number" ? orgTypeMap[parma?.type] : parma?.type;

    if (['association', 'government'].includes(type) || type == 4) {
      const data = await selectData.getRepresentatives(parma);
      return data;
    }
    const {data} = await getPlatformUser({team_id: parma.teamId,type})
    if(data.code === 0) {
      _platformList = data.data || []
      const staffList = data.data?.length ? await selectData.combPlatData(data.data, {teamId:parma.teamId,myCardId:parma.myCardId}) : [];
      return [[], staffList]
    }
    return [[], []]
  },
  // 获取平台成员列表数据并转换结构
  getPlatformAdmin: async (parma) => {
    const type = typeof (parma?.type) === "number" ? orgTypeMap[parma?.type] : parma?.type;
    if (['association', 'government'].includes(type) || type == 4) {
      const data = await selectData.getContacts(parma);
      return data;
    }
    const { data } = await getPlatformAdmin({ team_id: parma.teamId, type });
    if (data.code === 0) {
      _platformList = data.data || [];
      const staffList = data.data?.length ? await selectData.combPlatData(data.data, { teamId: parma.teamId, myCardId: parma.myCardId }) : [];
      return [[], staffList]
    }
    return [[], []]
  },
  combPlatPRData: (type, data: Array<any>, team, myCardId) => {
    if (!data.length) {
      return [];
    }
    const myCardIsPlatform = cardIdType(myCardId) === 'platform'

    let myCardInfo = myCardIsPlatform ? _platformList.find(v => v.card_id === myCardId): _orgMyInfo.find(v => v.teamId === team.team_id).cardInfo;

    if (type === 'group') {
      return data.map((vv) => {
        const staffs = vv?.user_list.map((v) => {
          const item = {
            targetId: v.card_id,
            cardId: v.card_id,
            avatar: v.avatar || '',
            team: team.team_name,
            teamId: team.team_id,
            openId: v.openid,
            name: v.name,
            from: team.team_name,
            conversationType: 1,
            select: false,
          }
          const transPlat = _transAttachPlatMemberCardModel({
            ...item,
            openid: item.openId,
            staffName: item.name,
            teamName: item.team,
          });
          return {
            ...item,
            attachment: {
              member: [myCardIsPlatform ? _transAttachPlatMemberCardModel(myCardInfo):_transAttachMemberCardModel(myCardInfo), transPlat],
              creatorCardId: myCardInfo?.cardId,
            },
          }
        });
        return {
          staffs,
          name: vv?.group_name,
          departmentId: vv?.group_id,
          id: vv?.group_id,
          staffsCount: vv?.user_total,
          select: false
        };
      });
    }
    return data.map((v) => {
      const item = {
        targetId: v.card_id,
        cardId: v.card_id,
        avatar: v.avatar || '',
        team: team.team_name,
        teamId: team.team_id,
        openId: v.openid,
        name: v.name,
        from: team.team_name,
        conversationType: 1,
        select: false,
      }
      const transPlat = _transAttachPlatMemberCardModel({
        ...item,
        openid: item.openId,
        staffName: item.name,
        teamName: item.team,
      });
      return {
        ...item,
        attachment: {
          member: [myCardIsPlatform ? _transAttachPlatMemberCardModel(myCardInfo):_transAttachMemberCardModel(myCardInfo), transPlat],
          creatorCardId: myCardInfo?.cardId,
        },
      }
    });
  },
  combPlatData: async (data:Array<any>, obj:{name?:string,myCardId?:string,teamId:string,departmentId?:string,departmentName?:string}) => {
    if (!data.length) {
      return [];
    }
    console.log('=====>',data,obj,_platformList);
    const myCardIsPlatform = cardIdType(obj.myCardId) === 'platform'
    let myCardInfo = myCardIsPlatform ? _platformList.find(v => v.cardId === obj.myCardId): _orgMyInfo.find(v => v.teamId === obj.teamId).cardInfo;
    const list = data.map(v => {
      const orgData = {
        targetId: v.cardId,
        cardId: v.cardId,
        avatar: v.staffAvatar || '',
        team: v.teamName,
        teamId: obj.teamId,
        openId: v.openid,
        name: v.staffName,
        from: v.teamName,
        conversationType: 1,
        select: false,
        attachment: {
          member: [myCardIsPlatform ?_transAttachPlatMemberCardModel(myCardInfo):_transAttachMemberCardModel(myCardInfo),_transAttachPlatMemberCardModel(v)],
          creatorCardId: myCardInfo?.cardId,
          relation: 'PLATFORM_FRIEND',
        },
      }
      return orgData;
    })
    return list || [];
  }
}

export default selectData;
