<template>
  <div>
    <!-- style="margin-top: 40px;"  -->
    <!-- :footer="shouldShowFooter()" -->
    <t-drawer
      v-model:visible="visibleFlag"
      class="aaaaaaaaaaaaaaaaa"
      :show-in-attached-element="true"
      class-name="bbbbbbbbbbbbbbbbbbbbbbbb"
      :show-overlay="false"
      size="472"
      v-bind="$attrs"
      :footer="
        (orderDetails.invoiceStatus !== 1 &&
          orderDetails.payAmount !== 0 &&
          orderDetails.status !== 3 &&
          (!orderDetails.refundInfo ||
            orderDetails.refundInfo?.refund_status === 0 ||
            orderDetails.refundInfo?.refund_status === 2 ||
            orderDetails.refundInfo?.refund_status === 4) &&
          orderDetails.payAmount > 0) ||
        (orderDetails.invoiceStatus === 3 && orderDetails.status === 4) ||
        (orderDetails.status === 3 && orderDetails.invoiceStatus === 0)
      "
    >
      <template #header>
        <div class="order-drawer-header">
          <div>{{ t('order.invoiceD') }}</div>
          <img
            style="width: 24px; cursor: pointer; height: 24px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="closeDrawer"
          />
        </div>
      </template>

      <template #body>
        <div class="head-tip">
          <div>1、发票抬头与购买人不一致，请在“开票资料”上传“付款回执”。</div>
          <div>2、订单支付金额为0元情况下，不能申请开票。</div>
        </div>
        <div class="invoice-statas">
          <div
            v-if="
              (orderDetails.invoiceStatus === 0 && orderDetails.payAmount !== 0 && orderDetails.status !== 3) ||
              (orderDetails.invoiceStatus === 2 && orderDetails.payAmount !== 0 && orderDetails.status !== 3)
            "
          >
            <img src="@assets/svg/Group1312317299.svg" />
            {{ t('order.notInvoiced') }}
          </div>
          <div v-if="orderDetails.invoiceStatus === 1 && orderDetails.payAmount !== 0 && orderDetails.status !== 3">
            <img src="@assets/svg/icon_time.svg" />
            {{ t('order.kpz') }}
          </div>
          <div v-if="orderDetails.invoiceStatus === 3 && orderDetails.payAmount !== 0 && orderDetails.status !== 3">
            <img src="@assets/svg/icon_check-one.svg" />
            {{ t('order.Invoiced') }}
          </div>
          <div v-if="orderDetails.payAmount === 0 || orderDetails.status === 3">
            <img src="@assets/svg/Group1312317299.svg" />
            不能开票
          </div>
          <span style="font-weight: 400">{{ InvoiceAmountNum(orderDetails) }}）</span>
        </div>
        <div style="background: #eaecff">
          <div
            v-if="orderDetails.invoiceStatus === 3 || orderDetails.invoiceStatus === 1"
            style="padding: 24px; background: #fff; margin-bottom: 12px"
          >
            <div class="form-box">
              <div v-if="invoicedetailvalue.region === 'CN'" class="form-flex">
                <div class="laber-item">发票类型:</div>
                <div class="value-item">
                  {{ invoicedetailvalue.header.invoice_type === 0 ? '普通发票' : '增值税专用发票' }}
                </div>
              </div>

              <div class="form-flex">
                <div class="laber-item">开票金额:</div>
                <div class="value-item">
                  {{ InvoiceAmountNum(orderDetails, true) }}
                </div>
              </div>

              <div
                v-if="orderDetails.currency === 'CNY' && invoicedetailvalue.header.invoice_type === 0"
                class="form-flex"
              >
                <div class="laber-item">{{ $t('order.Headingtype') }}:</div>

                <div class="value-item">
                  {{
                    invoicedetailvalue.header.header_type === 0
                      ? '--'
                      : invoicedetailvalue.header.header_type === 1
                        ? t('order.geren')
                        : t('order.danwei')
                  }}
                </div>
              </div>

              <div class="form-flex">
                <div class="laber-item">{{ $t('order.invoiceHeader') }}:</div>
                <div class="value-item">
                  {{ invoicedetailvalue.header.title ? invoicedetailvalue.header.title : '--' }}
                </div>
              </div>
              <div class="form-flex">
                <div class="laber-item">{{ $t('order.Emile') }}:</div>
                <div class="value-item">
                  {{ invoicedetailvalue.header.mail ? invoicedetailvalue.header.mail : '--' }}
                </div>
              </div>
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">{{ t('order.Invoicinginformationzl') }}:</div>
                <!-- {{ invoicedetailvalue.applyAttachment }}11 -->
                <div
                  v-if="invoicedetailvalue.applyAttachment && invoicedetailvalue.applyAttachment.length > 0"
                  style="width: 100%; display: flex; flex-wrap: wrap; padding-left: 8px"
                >
                  <t-image
                    v-for="(item, index) in invoicedetailvalue.applyAttachment"
                    :key="index"
                    :src="item"
                    fit="cover"
                    style="width: 60px; height: 60px; border-radius: 6px; margin-right: 8px"
                    @click="((_visible = true), (itemImgUrl = item))"
                  />
                </div>
                <div v-else class="value-item">--</div>
              </div>
              <div v-if="orderDetails.status === 4" class="form-flex">
                <div class="laber-item">申请时间:</div>
                <div class="value-item">
                  {{
                    orderDetails.status === 4
                      ? invoicedetailvalue.createdAt
                        ? invoicedetailvalue.createdAt
                        : '--'
                      : '--'
                  }}
                </div>
              </div>
              <div v-if="orderDetails.status === 4 && orderDetails.invoiceStatus !== 1" class="form-flex">
                <div class="laber-item">{{ t('order.kptime') }}:</div>
                <div class="value-item">
                  {{ orderDetails.status === 4 ? invoicedetailvalue.invoiceAt : '--' }}
                </div>
              </div>
              <div
                v-if="orderDetails.status === 4 && orderDetails.invoiceStatus !== 1"
                class="form-flex"
                style="width: 100%"
              >
                <div class="laber-item">{{ t('order.kpfile') }}:</div>
                <div
                  v-if="invoicedetailvalue.invoiceAttachment && invoicedetailvalue.invoiceAttachment.length > 0"
                  style="display: flex; flex-wrap: wrap"
                >
                  <div v-for="(item, index) in invoicedetailvalue.invoiceAttachment" :key="index" class="img-wrap">
                    <t-image
                      v-if="item.type !== 'pdf'"
                      :src="item.file_name"
                      fit="cover"
                      @click="((_visible = true), (itemImgUrl = item.file_name))"
                    />
                    <img v-else style="width: 100%; height: 100%" src="@assets/svg/pdf.svg" @click="viewImg(item)" />
                  </div>
                </div>
                <div v-else class="value-item">--</div>
              </div>
              <!-- {{ orderDetails.invoiceStatus }} -->
              <div
                v-if="orderDetails.invoiceStatus === 3 || orderDetails.invoiceStatus === 1"
                class="kpinfo"
                @click="applyInvoice(orderDetails, '查看发票')"
              >
                {{ t('order.fpinfo') }}
              </div>
            </div>
            <!-- 商品信息 -->
          </div>
          <!-- <order-table class="order-table-box" :table-data-goods="tableDataGoods" :status="orderDetails.status" /> -->
          <ad-shop-info v-if="orderDetails?.goodsInfo?.productType === 'ad_lk'" :order-details="orderDetails" />

          <orderTable
            v-else-if="(teamId === 1 || teamId === 0) && orderDetails?.goodsInfo?.productType !== 'ad_lk'"
            :table-data-goods="tableDataGoods"
            :status="orderDetails.status"
          />
          <orderTeamTable v-else :table-data-goods="tableDataGoods" :status="orderDetails.status" />
          <div class="foot-box" :class="orderDetails.invoiceStatus === 1 ? 'mgb40' : ''">
            <div class="flex-box">
              <div class="foot-lable">{{ t('order.orderNo') }}：</div>
              <div class="foot-value">{{ orderDetails.sn }}</div>
            </div>
            <div class="flex-box" style="margin-top: 8px">
              <div class="foot-lable">{{ t('order.outeOrderTime') }}：</div>
              <div class="foot-value">
                {{ orderDetails.status === 4 ? (orderDetails.createdAt ? orderDetails.createdAt : '--') : '--' }}
              </div>
            </div>
          </div>
          <div v-if="orderDetails.invoiceStatus === 2" class="reasonrejection-box">
            <div class="laber-item">{{ t('order.Reasonrejection') }}:</div>
            <div class="value-item">
              {{ invoicedetailvalue.reviewRemark ? invoicedetailvalue.reviewRemark : '--' }}
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="foot-btn-box">
          <div>
            <div>
              <t-button
                v-if="orderDetails.status === 1"
                class="min-80"
                theme="default"
                style="margin-right: 8px"
                variant="outline"
                @click="closeOrder(orderDetails)"
              >
                {{ t('order.cancellationoforder') }}
              </t-button>
              <t-button v-if="orderDetails.status === 1" class="min-80" @click="prompts(orderDetails)">付款</t-button>
            </div>
            <t-button
              v-if="orderDetails.status === 0"
              class="min-80"
              @click="shutdownReason(orderDetails.cancelReason)"
            >
              {{ t('order.closejet') }}
            </t-button>
            <t-button
              v-if="orderDetails.invoiceStatus === 1 && orderDetails.status === 4"
              class="min-80"
              @click="applyInvoice(orderDetails, '开票信息')"
            >
              {{ t('order.Invoicinginformation') }}
            </t-button>
            <t-button
              v-if="
                ((orderDetails.invoiceStatus === 2 || orderDetails.invoiceStatus === 0) &&
                  orderDetails.status === 4 &&
                  orderDetails.payAmount !== 0 &&
                  orderDetails.status !== 3) ||
                (orderDetails.status === 3 && orderDetails.invoiceStatus === 0)
              "
              class="min-80"
              theme="default"
              variant="outline"
              @click="applyInvoice(orderDetails, '申请开票')"
            >
              {{ t('order.Applyforinvoicing') }}
            </t-button>
            <t-button
              v-if="
                orderDetails.invoiceStatus === 3 &&
                orderDetails.payAmount > 0 &&
                orderDetails.invoiceStatus === 3 &&
                orderDetails.payAmount > 0 &&
                (!orderDetails.refundInfo ||
                  orderDetails.refundInfo?.refund_status === 0 ||
                  orderDetails.refundInfo?.refund_status === 2)
              "
              class="min-80"
              theme="default"
              variant="outline"
              @click="applyInvoice(orderDetails, '申请开票', '重新开票')"
            >
              {{ t('order.Reinvoicing') }}
              <!-- 重新開票 -->
            </t-button>
            <!-- <t-button
              v-if="orderDetails.invoiceStatus === 3"
              theme="default"
              variant="outline"
              @click="sendEmile(orderDetails)"
            >
              {{ t('order.sendmile') }}
            </t-button> -->
            <!-- <t-button v-if="orderDetails.invoiceStatus === 3&&orderDetails.status===4" @click="applyInvoice(orderDetails,'查看发票')">{{ t('order.Viewinvoice') }}</t-button> -->
          </div>
        </div>
      </template>
    </t-drawer>
    <applyInvoiceDialog
      ref="applyInvoiceDialogRef"
      :team-id="teamId"
      @apply-invoice-callback="orderCallback"
      @close-drawer="emits('closeDrawer')"
    />
    <paymentDialog ref="paymentDialogRef" @payment-callback="orderCallback" @get-data-list="orderCallback" />
    <t-dialog v-model:visible="closeFlag" :close-btn="false" :header="true" width="384">
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>{{ t('order.closejet') }}</div>
          <img
            style="width: 24px; cursor: pointer; height: 24px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="closeFlag = false"
          />
        </div>
      </template>
      <div class="send-kr-box">
        {{ closeText }}
      </div>
      <template #footer>
        <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
          <t-button theme="default" class="min-80" variant="outline" @click="closeFlag = false">关闭</t-button>
        </div>
      </template>
    </t-dialog>
    <t-dialog v-model:visible="dilatationFlag" :close-btn="false" :header="true" width="384">
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>取消原因</div>
          <img
            style="width: 24px; cursor: pointer; height: 24px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="((dilatationFlag = false), (reason = ''))"
          />
        </div>
      </template>
      <div class="send-kr-box">
        <t-textarea
          v-model="reason"
          :maxlength="200"
          :autosize="{ minRows: 3, maxRows: 5 }"
          placeholder="请输入取消原因"
        />
      </div>
      <template #footer>
        <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
          <t-button class="min-80" theme="default" variant="outline" @click="((dilatationFlag = false), (reason = ''))">
            取消
          </t-button>
          <t-button :disabled="reason ? false : true" class="min-80" @click="sendKR">确定</t-button>
        </div>
      </template>
    </t-dialog>
    <t-dialog v-model:visible="tipsDioalog" :close-btn="false" :header="true" width="520">
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>提示</div>
          <img
            style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="tipsDioalog = false"
          />
        </div>
      </template>
      <div class="tips-dioalog-box">
        <!-- <successsvg></successsvg> -->
        <REmpty name="success" tip="" />
        <div v-if="tipStatus === 100">
          红字确认单已确认，正在开具红字发票，请耐心等待。 若等待时间过长，请联系平台客服处理。
        </div>
        <div v-else>发票为已确认或已抵扣，请前往“电子税局平台”进行红字确认单确认，并在平台重新开票</div>
      </div>
      <template #footer>
        <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
          <t-button class="minw83" theme="default" variant="outline" @click="tipsDioalog = false">关闭</t-button>
        </div>
      </template>
    </t-dialog>
    <t-image-viewer v-model:visible="_visible" :images="[itemImgUrl]"></t-image-viewer>
  </div>
</template>
<script setup lang="ts">
import sdk from '@lynker-desktop/web';
import applyInvoiceDialog from './applyInvoiceDialog.vue';
import paymentDialog from './paymentDialog/index.vue';
import adShopInfo from './adShopInfo.vue';

import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import { orderDetail, orderCancel, payProduct } from '../../api';
import { getTime } from '../../utils';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { invoiceDetailData, invoiceList, redConfirm } from '../../api';
import orderTable from './orderTable.vue';
import orderTeamTable from './orderTeamTable.vue';
import { REmpty } from '@rk/unitPark';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  teamId: {
    type: String || Number,
    default: 0,
  },
});
const applyInvoiceDialogRef = ref(null);
const paymentDialogRef = ref(null);
const reason = ref('');
const _visible = ref(false);
const itemImgUrl = ref('');
const addCommasToNumber = (str) => {
  let [integerPart, decimalPart] = str.split('.');
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  if (decimalPart) {
    decimalPart = decimalPart.length === 1 ? `${decimalPart}0` : decimalPart.slice(0, 2);
  } else {
    decimalPart = '00';
  }
  console.log(`${integerPart}.${decimalPart}`, '222222222222');

  return `${integerPart}.${decimalPart}`;
};
const { t } = useI18n();

let visibleFlag = ref(false);
// 申请发票
let applyInvoiceFlag = false;
const dilatationFlag = ref(false);
const openpaymentDialog = (val) => {
  paymentDialogRef.value.openWin(val);
};
const closeText = ref('');
const closeFlag = ref(false);
const shutdownReason = (val) => {
  closeText.value = val;
  closeFlag.value = true;
};
const InvoiceAmountNum = (orderDetails, flag) => {
  let str =
    orderDetails.invoiceStatus === 1 ? '待开票金额' : orderDetails.invoiceStatus === 3 ? '已开票金额' : '可开票金额';
  const safeNumber = orderDetails?.invoiceAmount || 0;
  if (flag) {
    return `${orderDetails.currency === 'CNY' ? '￥' : 'MOP'} ${addCommasToNumber(safeNumber.toFixed(2))}`;
  }
  return `（${str}：${orderDetails.currency === 'CNY' ? '￥' : 'MOP'} ${addCommasToNumber(safeNumber.toFixed(2))}`;
};
// const shouldDisplayInvoice = () => {
//   console.log(orderDetails.value, 'orderDetails.valueorderDetails.value');
//   const { invoiceStatus, payAmount, refundInfo, status } = orderDetails.value;
//   if (refundInfo && refundInfo?.refund_status !== 4) {
//     return false;
//   }
//   // 确保invoiceStatus为3且支付金额大于0
//   if (invoiceStatus !== 3 || payAmount <= 0) {
//     return false;
//   }

//   // 特殊情况处理：如果invoiceStatus为3且订单状态为4，则也显示
//   if (status === 4) {
//     return true;
//   }

//   return false;
// };
// 创建订单
const prompts = (row) => {
  if (row === '') {
    MessagePlugin.error(t('order.Theorderstatusoperation'));
    orderCallback();
  } else if (row === '没有支付渠道') {
    console.log(row, 'rowwwwwwwwwwwwwwwwww');
    const confirmDia = DialogPlugin.alert({
      header: '提示',
      theme: 'info',
      body: t('order.closeTip'),
      closeBtn: null,
      confirmBtn: '知道了',
      className: 'delmode',
      onConfirm: async () => {
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  } else {
    // 第一个参数当前行row里面必须有个sn订单号和region订单区域
    let rowDatas = JSON.parse(JSON.stringify(row));
    rowDatas.amount = rowDatas.payAmount;
    paymentDialogRef.value.openWin(rowDatas);
  }
};
const closeOrder = (row) => {
  // 取消订单
  console.log(row.sn, 'rowwwwwwwwwwww');
  rowData.value = row;
  dilatationFlag.value = true;
};
const sendKR = () => {
  // 取消付款
  orderCancel(
    {
      sn: rowData.value.sn,
      reason: reason.value,
    },
    props.teamId,
  )
    .then((res) => {
      if (res.data.code === 0) {
        reason.value = '';
        dilatationFlag.value = false;
        orderCallback();

        visibleFlag.value = false;
      } else {
        MessagePlugin.error(t('order.Theorderstatusoperation'));
        if (visibleFlag.value) {
          orderDetailsFn(rowData.value);
        }
        reason.value = '';
        dilatationFlag.value = false;
      }
      orderCallback();

      console.log(res, 'resssssssssss');
    })
    .catch((err) => {
      MessagePlugin.error(err);

      console.log(err, 'errerrerrerr');
    });
};
let tipStatus = ref(100);
const tipsDioalog = ref(false);
const applyInvoice = async (row, flag, val) => {
  if (applyInvoiceFlag) {
    return;
  }
  console.log(props.teamId, '薩達撒打算');

  if (val) {
    const redconfirmData = await redConfirm(
      {
        invoiceId: row.invoiceId,
      },
      props.teamId,
    );
    // alert(JSON.stringify(redconfirmData))
    applyInvoiceFlag = false;
    if (redconfirmData.data.confirm.status === 0) {
      //待确定
      tipStatus.value = 200;
      tipsDioalog.value = true;

      return;
    } else if (redconfirmData.data.confirm.status === 2) {
      //失败
      tipStatus.value = 100;
      tipsDioalog.value = true;

      return;
    }
    console.log(redconfirmData.data.confirm, 'resssssssss');
  }
  applyInvoiceFlag = true;
  if (flag === '开票信息' || flag === '查看发票' || flag === '申请开票' || flag === '拒绝开票原因') {
    // InvoiceDrawerRef.value.openWin(row);
    invoiceList(
      {
        pageSize: 9999,
        page: 1,
      },
      props.teamId,
    )
      .then((resq) => {
        console.log(resq, 'resqresqresqresq');

        if (flag !== '申请开票') {
          invoiceDetailData(row.invoiceId, props.teamId)
            .then((res) => {
              console.log(res, '去瞧瞧翁群翁翁');
              applyInvoiceDialogRef.value.openWin({
                ...row,
                flag,
                invoiceListData: resq.data.list,
                invoiceDetailDatas: res.data.detail,
              });
            })
            .catch((err) => {
              MessagePlugin.error(err.response.data.message);
            })
            .finally(() => {
              applyInvoiceFlag = false;
            });
        } else {
          applyInvoiceDialogRef.value.openWin({
            ...row,
            flag,
            invoiceListData: resq.data.list,
          });
        }
      })
      .finally(() => {
        applyInvoiceFlag = false;
      });
  }
};
let clickFlag = false;
const openApplyInvoiceDialog = (val) => {
  if (clickFlag) {
    return;
  }
  clickFlag = true;

  applyInvoiceDialogRef.value
    .openWin({
      ...val,
    })
    .then(() => {
      clickFlag = false;
    })
    .finally(() => {
      clickFlag = false;
    });
  clickFlag = false;

  console.log('打开这个', clickFlag);
};
const orderDetails = ref({
  createdAt: '',
  status: 0,
  tradeAt: '',
  expiredAt: 0,
  payAmount: 0,
  sn: '',
  currency: '',
  finishAt: '',
  updatedAt: '',
  discount: 0,
  amount: 0,
  cancelReason: '',
  invoiceStatus: 0,
  pay_mode: '',
  staff: {
    name: '',
  },
  payee: '',
});
const tableDataGoods = ref([{ productType: '' }]);
let pdfFlag = false;
const viewImg = async (val) => {
  if (val.type !== 'pdf') {
    _visible.value = true;
    itemImgUrl.value = val.file_name;
  } else if (!pdfFlag) {
    pdfFlag = true;
    try {
      const dres = await sdk.ipcRenderer.invoke('download-file', {
        title: val.file_name_short,
        url: val.file_name,
      });
      if (dres) {
        MessagePlugin.success(`${val.file_name_short}${t('partner.dover')}`);
      }
      pdfFlag = false;
    } catch (error) {
      pdfFlag = false;
    }
  }
};
const isTeamId = ref(0);
const openWin = (row, teamId) => {
  isTeamId.value = teamId === 0 || teamId === 1 ? 0 : teamId;
  orderDetailsFn(row, teamId);
};
defineExpose({
  openWin,
  openApplyInvoiceDialog,
  openpaymentDialog,
});
const rowData = ref({
  sn: '',
});
const invoicedetailvalue = ref({
  header: {
    invoice_type: '',
  },
});
const orderDetailsFn = async (row, teamId) => {
  try {
    const res = await orderDetail(row.sn, teamId || props.teamId);
    console.log(res, 'ressssssss');
    if (res.data.info.invoiceId) {
      console.log(999999999);

      const invoiceDetailDataobj = await invoiceDetailData(res.data.info.invoiceId, teamId || props.teamId);
      console.log(invoiceDetailDataobj, 'invoiceDetailDataobjinvoiceDetailDataobj');
      invoicedetailvalue.value = invoiceDetailDataobj.data.detail;
      if (invoicedetailvalue.value.createdAt) {
        invoicedetailvalue.value.createdAt = getTime(new Date(invoicedetailvalue.value.createdAt * 1000));
      }
      if (invoicedetailvalue.value.applyAttachment) {
        invoicedetailvalue.value.applyAttachment = invoicedetailvalue.value.applyAttachment.split(',');
      }
      try {
        if (invoicedetailvalue.value.invoiceAt) {
          invoicedetailvalue.value.invoiceAt = getTime(new Date(invoicedetailvalue.value.invoiceAt * 1000));
        }

        if (invoicedetailvalue.value.invoiceAttachment) {
          invoicedetailvalue.value.invoiceAttachment = JSON.parse(invoicedetailvalue.value.invoiceAttachment);
        }
      } catch (error) {
        invoicedetailvalue.value.invoiceAttachment = [];
      }
    }

    orderDetails.value = {
      ...res.data.info,
      createdAt: getTime(new Date(res.data.info.createdAt * 1000)),
      tradeAt: res.data.info.tradeAt === 0 ? '' : getTime(new Date(res.data.info.tradeAt * 1000)),
      finishAt: getTime(new Date(res.data.info.finishAt * 1000)),
      updatedAt: getTime(new Date(res.data.info.updatedAt * 1000)),
      cancelReason: row.cancelReason ? row.cancelReason : '',
    };
    if (res.data.info.goodsInfo.productType === 'album') {
      tableDataGoods.value[0].productType = 'album';
      tableDataGoods.value[0] = { ...res.data.info.goodsInfo, ...res.data.info.snapshot };
      tableDataGoods.value[0].currency = res.data.info.currency;
      visibleFlag.value = true;
      const timestamp = new Date().getTime();
      const endTime = orderDetails.value.expiredAt * 1000;

      const leftSeconds = (endTime - new Date(timestamp)) / 1000; // 剩余秒数
      startCountdown(leftSeconds); // 开始倒计时
      return;
    }

    if (res.data.info.goodsInfo.productType === 'TEMPLATE') {
      tableDataGoods.value[0] = res.data.info.goodsInfo;
      tableDataGoods.value[0].productType = 'TEMPLATE';
      tableDataGoods.value[0].currency = res.data.info.currency;

      visibleFlag.value = true;
      const timestamp = new Date().getTime();
      const endTime = orderDetails.value.expiredAt * 1000;

      const leftSeconds = (endTime - new Date(timestamp)) / 1000; // 剩余秒数
      startCountdown(leftSeconds); // 开始倒计时
      return;
    }

    rowData.value = row;
    if (res.data.info.goodsInfo.productType !== 'ad_lk') {
      const e = await payProduct(row.sn, props.teamId);

      if (!e?.orderType) {
        MessagePlugin.error(t('order.Productdoesnotexist'));
        return;
      }
      tableDataGoods.value[0] = e.product;
    }

    visibleFlag.value = true;

    const timestamp = new Date().getTime();
    const endTime = orderDetails.value.expiredAt * 1000;
    const leftSeconds = (endTime - timestamp) / 1000;

    startCountdown(leftSeconds);
  } catch (error) {
    console.log(error.response.data.message, 'errorerror11');
    MessagePlugin.error(error.response.data.message);
    console.error(error);
    // 处理错误情况
  }
};

watch(
  () => props.visible,
  (newValue) => {
    visibleFlag.value = newValue;
  },
);
const countdown = ref('');

const startCountdown = (leftSeconds) => {
  const timer = setInterval(() => {
    if (leftSeconds > 0) {
      const hour = Math.floor(leftSeconds / 3600);
      const minute = Math.floor((leftSeconds - hour * 3600) / 60);
      const second = Math.floor(leftSeconds % 60);
      countdown.value = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second
        .toString()
        .padStart(2, '0')}`;
      leftSeconds--;
    } else {
      if (visibleFlag.value && orderDetails.value.expiredAt && orderDetails.value.status === 1) {
        visibleFlag.value = false;
        orderCallback();
      }
      console.log(visibleFlag.value, '走这里啊啊啊');
      clearInterval(timer);
      countdown.value = '00:00:00';
    }
  }, 1000);
};
// paymentCallback
const emits = defineEmits(['orderCallback', 'closeDrawer']);

const orderCallback = () => {
  console.log('orderCallbackorderCallback');
  emits('orderCallback');
  emits('closeDrawer');
  visibleFlag.value = false;
};
const closeDrawer = () => {
  emits('closeDrawer');
  visibleFlag.value = false;
};
</script>
<style lang="less" scoped>
:deep(.adShopInfobox) {
  background: #fff;
  padding: 18px;
}
.head-item-lab {
  height: 22px;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Bold;
  font-weight: 700;
  color: #13161b;
  padding-left: 8px;
  margin-bottom: 12px;
}
.foot-lab {
  width: 70px;
  height: 22px;
  font-size: 14px;
  font-weight: 400;
  color: var(--text-kyy-color-text-3, #828da5);
  font-family: PingFang SC;
  line-height: 22px;
  text-align: right;
}
.foot-val {
  min-width: 120px;
  height: 22px;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: right;
  color: #13161b;
  line-height: 22px;
}
.w120pl {
  width: 120px;
  padding-left: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.my-order-box {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
}
.flex-a-js-w140 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 140px;
}
.w70text-r {
  width: 190px;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.table-foot-box {
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: left;
  color: #717376;
  line-height: 44px;
  .foot-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.foot-btn-box {
  display: flex;
  align-items: center;
  justify-content: end;
  text-align: end;
}
.mgb40 {
  margin-bottom: 40px !important;
}
.form-box {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  .form-flex {
    display: flex;
    margin-bottom: 12px;
    width: 100%;
    align-items: center;
  }
  .value-item {
    height: 22px;
    font-size: 14px;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei-Regular;
    font-weight: 400;
    color: #1a2139;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .laber-item {
    width: 70px;
    font-size: 14px;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei-Regular;
    font-weight: 400;
    text-align: left;
    color: #828da5;
  }
}
.reasonrejection-box {
  display: flex;
  padding: 24px;
  background: #fff;
  margin-top: 24px;
  .value-item {
    color: var(--text-kyy-color-text-1, #1a2139);
    font-family: PingFang SC;
    font-size: 14px;
    width: 100%;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  .laber-item {
    width: 70px;
    color: var(--text-kyy-color-text-3, #828da5);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
}
.flex-box {
  display: flex;
  align-items: center;
  .foot-lable {
    color: var(--text-kyy-color-text-3, #828da5);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .foot-value {
    color: var(--text-kyy-color-text-1, #1a2139);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.foot-box {
  padding: 0 24px 24px;
  margin-bottom: -16px;
  background: #fff;
}

.tabledian150 {
  width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.invoice-statas {
  display: flex;
  padding: 24px;
  align-items: center;
  height: 72px;
  align-self: stretch;
  background: var(--brand-kyy-color-brand-default, #4d5eff);
  color: var(--text-kyy-color-text-white, #fff);
  /* kyy_fontSize_3/bold */
  font-family: PingFang SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
  div {
    display: flex;
    align-items: center;

    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
}
.table-thing-value {
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: var(--text-kyy-color-text-3, #828da5);
}
.flex-a-end {
  display: flex;
  align-items: center;
  justify-content: end;
}

.table-thing-laber {
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: var(--kyy-color-table-text, #1a2139);
}
.table-thing {
  display: flex;
  img {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }
}
.kpinfo {
  width: 70px;
  position: absolute;
  bottom: 10px;
  right: 0;
  cursor: pointer;
  color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff);
  text-align: center;
  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.content-box {
  // height: 122px;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  padding: 16px;
  margin-bottom: 24px;
}
.order-table-box {
  padding: 24px;
}
:deep(.aaaaaaaaaaaaaaaaa) {
  :deep(.t-drawer__content-wrapper) {
    :deep(.t-drawer__body) {
      background: var(--kyy_color_alert_bg_bule, #eceff5);
    }
  }
}
.img-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  margin-right: 8px;
  border: 1px solid #e3e6eb;
  border-radius: 4px;
  position: relative;
  &:hover .btn-close {
    opacity: 1;
  }
  .img {
    width: 100%;
    height: 100%;
    border-radius: 4px;
  }
  .btn-close {
    opacity: 0;
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 16px;
    cursor: pointer;
    color: #97989a;
    z-index: 1;
  }
  .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    z-index: 1;
  }
}
.head-tip {
  display: flex;
  padding: 8px 0 8px 24px;
  align-items: center;
  background: var(--kyy_color_alert_bg_bule, #eaecff);
  align-self: stretch;
  color: var(--kyy_color_alert_text, #1a2139);
  font-size: 14px;
  font-style: normal;
  flex-wrap: wrap;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.lin {
  position: relative;
}
.lin::after {
  content: '';
  width: 2px;
  height: 14px;
  border-radius: 1px;
  background: var(--brand-kyy-color-brand-default, #4d5eff);
  position: absolute;
  top: 4px;
  border-radius: 2px;
  left: 0;
}
:deep(.t-drawer__header) {
  border: none !important;
  border-bottom: none !important;
  background: #fff;
}
.order-drawer-header {
  display: flex;
  -webkit-app-region: no-drag;
  align-items: center;
  padding: 0 8px;
  width: 100%;
  justify-content: space-between;
}
.head-search-box {
  padding-top: 72px;
  height: 185px;
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 24px;
  padding-left: 24px;
}

:deep(.t-drawer__body) {
  padding: 0 !important;
}
.form-items {
  display: flex;
  align-items: center;
  padding-right: 32px;
  .labels {
    height: 22px;
    padding-right: 8px;
    font-size: 14px;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei-Regular;
    font-weight: 400;
    text-align: left;
    color: #13161b;
    line-height: 22px;
  }
}
.btn-box {
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: #2069e3;
  cursor: pointer;
  line-height: 22px;
}
.transaction-status {
  width: 58px;
  height: 24px;
  border-radius: 4px;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: center;
  line-height: 24px;
}
.flex-a {
  display: flex;
  align-items: center;
}
.tips-dioalog-box {
  text-align: center;
  img {
    width: 200px;
    height: 200px;
  }
}
</style>
