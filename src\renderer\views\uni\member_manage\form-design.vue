<template>
  <div class="box">
    <!-- {{ controls }} -->
    <formDesign
      ref="formDesignRef"
      :header-text="headerText"
      :confirm-text="$t('member.save')"
      :widgets="controls"
      icon-name="member"
      @release="releaseRun"
      @design-goback="goback"
    />
  </div>
</template>

<script setup lang="ts" name="bench_uni_manage">
import { ref, onMounted, computed, watch, nextTick } from "vue";
import formDesign from "@renderer/components/free-from/design/index.vue";
import memberConst from "@renderer/components/free-from/design/constants/uniConst";
import { useRoute, useRouter } from "vue-router";
import { ClientSide } from "@renderer/types/enumer";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { useI18n } from "vue-i18n";
import { useMemberFormDesignStore } from "@renderer/views/uni/store/formDesign";
import { MessagePlugin } from "tdesign-vue-next";
import { memberSettingApplyAxios } from "@renderer/api/uni/api/businessApi";
import { getResponseResult } from "@renderer/utils/myUtils";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import lodash from "lodash";
import {
  formDiff,
  formDiffBaseInfo,
  localNewItemTemplateReplace,
} from "@renderer/components/free-from/utils";
import { filds, filds_hk } from "./fields";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";

const { t, locale } = useI18n();

const controls = ref([]);
const router = useRouter();
const route = useRoute();
const store: any = useUniStore();
const formDesignStore: any = useMemberFormDesignStore();
const digitalPlatformStore = useDigitalPlatformStore();

const formDesignRef = ref(null);
const headerText = computed(() => {
  let msg = "";
  switch (route.query.type) {
    case "person":
      msg = '个人表单设计';
      break;
    case "unit":
      msg = '组织表单设计';
      break;
    default:
      msg = "自由表单";
      break;
  }
  return msg;
});
const list = ref(locale.value === "zh-cn" ? filds : filds_hk);


// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getUniTeamID()
  }
})


watch(
  () => route.query.type,
  (val) => {
    console.log(controls.value, val);
    if (val) {
      let menuList = memberConst.filter((v) => v.fromType === val);
      menuList = lodash.cloneDeep(menuList);
      nextTick(() => {
        if (val === "person") {
          if (formDesignStore.personal_form.length > 0) {
            controls.value = formDiffBaseInfo(
              menuList,
              localNewItemTemplateReplace(menuList, lodash.cloneDeep(formDesignStore.personal_form))
            );
          } else {
            controls.value = menuList;
          }
        } else if (formDesignStore.team_form.length > 0) {
          controls.value = formDiffBaseInfo(
            menuList,
            localNewItemTemplateReplace(menuList,lodash.cloneDeep(formDesignStore.team_form))
          );
        } else {
          controls.value = menuList;
        }

        formDesignRef.value.setFocusFieldIdSet(controls.value[0].id);
        // setFocusFieldIdSet
      });
    }
  },
  {
    immediate: true,
  }
);

const goback = () => {
  // console.log(route.name, route.query)
  // const home = store.tabs.find(v=>v.path === 'association_number')
  // const manage = store.tabs.find(v=>v.path === 'association_manage')
  // store.removeTab(manage, home );
  // console.log(home)
  // router.replace(home.fullPath);

  // store.setLoadingShow(true);
  const index = props.tabList.findIndex((e:any) => e.path === '/workBenchIndex/uni_manage' && JSON.stringify(route.query)=== JSON.stringify(e.query));
  // console.log(index, props.activeIndex)

  emits("deltabItem", index, true)

};
onMounted(() => {
  console.log(list);
  formDesignRef.value.changeFieldList(list.value);
  // controls.value.push(...memberConst);
  // const menuList = memberConst.filter((v) => v.fromType === route.query.type);
  // controls.value  = menuList
  // settabItem();
  /*
    const type = route.query.type;
    if (controls.value.length < 1) {
      const menuList = memberConst.filter((v) => v.fromType === type);
      if (type === "person") {
        if (formDesignStore.personal_form.length > 0) {
          controls.value = formDesignStore.personal_form;
        } else {
          controls.value = menuList;
        }
      } else if (formDesignStore.team_form.length > 0) {
        controls.value = formDesignStore.team_form;
      } else {
        controls.value = menuList;
      }
    }
    console.log("abcd", controls.value);
  */
});

const emits = defineEmits([
  "settabItem",
  "setActiveIndexAndName",
  "setActiveGroup",
  "getGroupListabi",
  "deltabItem",
  "setActiveIndex",
]);
const props = defineProps({
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
  platform: {
    type: String,
    default: '',
  },
});
const settabItem = () => {
  if (!props.tabList.find((e: any) => e.tag === "design")) {
    emits("settabItem", {
      path: "/approvalIndex/free-form/form-design",
      name: "design",
      title: "表单设计",
      tag: "freeForm",
      selectedType: "表单设计",
      type: ClientSide.APPROVAL,
    });
    emits("setActiveIndex", props.tabList.length - 1);
  } else {
    emits("setActiveIndexAndName", {
      name: "design",
      path: "/approvalIndex/free-form/form-design",
      title: "表单设计",
    });
  }
};

const releaseRun = (data) => {
  console.log(data);

  if (route.query.type === "person") {
    onSaveFreeFormAxios({ personal_form: data }).then(() => {
      formDesignStore.setPersonForm(data);
      setTimeout(() => {
        goback();
      }, 1000);
    });
  } else {
    onSaveFreeFormAxios({ team_form: data }).then(() => {
      formDesignStore.setTeamForm(data);
      setTimeout(() => {
        goback();
      }, 1000);
    });
  }

  // MessagePlugin.success(t("member.save") + t("member.success"));
};

// 保存表单
const onSaveFreeFormAxios = async (params) => {
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await memberSettingApplyAxios(params, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      MessagePlugin.success(t("member.save") + t("member.success"));
      resolve(result);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";
.box {
  width: 100%;
}
</style>
