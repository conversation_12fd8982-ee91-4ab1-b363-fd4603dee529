<script setup lang="ts">
import { ref, computed, watch, provide } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';
import PublishBtn from '@/views/digital-platform/forum/components/PublishBtn.vue';
import PostList from '@/views/digital-platform/forum/components/post/PostList.vue';
import TopPostList from '@/views/digital-platform/forum/components/post/TopPostList.vue';
import FloatActionBtn from '@/views/digital-platform/forum/components/FloatActionBtn.vue';
import AnonButton from '@/views/digital-platform/forum/components/AnonButton.vue';

import { useForumStore } from '../store';
import { PlatformType } from '@renderer/api/forum/models/forum';
import { PageTypeMap } from '../constant';

provide(PageTypeMap.Forum, PageTypeMap.Forum);

const forumStore = useForumStore();

const refreshKey = ref(0);
const refreshTopPost = ref(0);

const pageContent = ref(null);
const scroll = ref(0);
onBeforeRouteLeave((to, from, next) => {
  scroll.value = pageContent.value?.scrollTop;
  next();
});

const publishBtnRef = ref(null);
const postListRef = ref(null);
const scrollDisabled = computed(() => postListRef.value?.status === 'finished');
const onLoading = () => {
  postListRef.value?.loading();
};

const postPublishVisible = ref(false);
const postSubmitKey = ref(1);
const submitPost = () => {
  postPublishVisible.value = false;
  postSubmitKey.value++;
  refreshKey.value++;
};

watch(
  () => forumStore.ownerId,
  (val) => {
    if (!val) return;
    refreshKey.value++;
  },
);

const trickUuid = computed(() => {
  const map: Record<PlatformType, string> = {
    association: '数字社群-论坛',
    cbd: '数字CBD-论坛',
    government: '数字城市-论坛',
    member: '数字商协-论坛',
    uni: '数字高校-论坛',
  };
  return map[forumStore.platformType];
});

const backTopVisible = ref(false);
const backVisible = (val: boolean) => {
  backTopVisible.value = val;
};
</script>

<template>
  <div
    ref="pageContent"
    v-infinite-scroll="onLoading"
    class="page-content"
    :infinite-scroll-immediate-check="false"
    :infinite-scroll-distance="1800"
    :infinite-scroll-disabled="scrollDisabled"
    infinite-scroll-watch-disabled="scrollDisabled"
  >
    <div class="banner">
      <img src="https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/172973969826666786277.png" alt="" class="img" />
      <span class="text">{{ $t('forum.forum') }}</span>
    </div>

    <PublishBtn ref="publishBtnRef" v-model="postPublishVisible" class="my-8" @submit="submitPost" />

    <TopPostList :key="refreshTopPost" :show-title="false" from="1" class="my-8" />

    <div class="list-wrap">
      <PostList
        ref="postListRef"
        :key="refreshKey"
        go-detail
        hide-setting
        class="w-full"
        from="friend-circle"
        :toggle-top-refresh="false"
        :ownerId="forumStore.ownerId"
        @removed="refreshKey++"
        @load="forumStore.getUnreadStats()"
      />
    </div>

    <AnonButton :offset="['390px', backTopVisible ? '145px' : '90px']" />

    <FloatActionBtn
      container=".page-content"
      :offset="['390px', '40px']"
      can-publish
      @publish="publishBtnRef?.open"
      @change="backVisible"
    />

    <Tricks
      v-if="trickUuid"
      :key="backTopVisible"
      :offset="{ x: '-390', y: backTopVisible ? '-152' : '-96' }"
      :uuid="trickUuid"
    />
  </div>
</template>

<style scoped lang="less">
.page-content {
  .banner {
    width: 100%;
    height: 130px;
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    position: relative;
    .img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }
    .text {
      color: var(--text-kyy_color_text_white, #fff);
      font-family: Alexandria;
      font-size: 36px;
      font-style: normal;
      font-weight: 900;
      line-height: normal;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .list-wrap {
    flex: 1;
    overflow-y: auto;
    // height: calc(100% - 204px);
  }
}

:deep(.trends-list.empty) {
  min-height: calc(100vh - 251px);
}
</style>
