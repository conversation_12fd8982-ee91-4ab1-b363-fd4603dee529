<template>
  <div class="flex items-center gap-8">
    <div
      v-if="bondStatus !== BondStatusEnum.Refunded"
      class="cursor-pointer text-[#4D5EFF] hover:bg-[#EAECFF] rounded-4 p-4 inline-block"
      @click="onEdit"
    >
      编辑
    </div>

    <div
      v-if="bondStatus === BondStatusEnum.Paid"
      class="cursor-pointer text-[#4D5EFF] hover:bg-[#EAECFF] rounded-4 p-4 inline-block"
      @click="onPayRecord"
    >
      支付记录
    </div>

    <div
      v-if="bondStatus === BondStatusEnum.PendingPay"
      class="cursor-pointer text-[#4D5EFF] hover:bg-[#EAECFF] rounded-4 p-4 inline-block transition-all duration-300 opacity-100"
      :class="{ 'cursor-not-allowed pointer-events-none opacity-80': payLoading }"
      @click="onPay"
    >
      支付保证金
    </div>

    <div
      v-if="bondStatus === BondStatusEnum.Refunded"
      class="cursor-pointer text-[#4D5EFF] hover:bg-[#EAECFF] rounded-4 p-4 inline-block"
      @click="onRefundRecord"
    >
      退款记录
    </div>

    <div
      v-if="bondStatus !== BondStatusEnum.Paid"
      class="cursor-pointer text-[#4D5EFF] hover:bg-[#EAECFF] rounded-4 p-4 inline-block"
      @click="handleDelete"
    >
      删除
    </div>
  </div>
</template>

<script setup lang="ts">
import { DialogPlugin } from 'tdesign-vue-next';
import { BondStatusEnum } from '../../../constants/enum';

const props = defineProps<{
  bondStatus: BondStatusEnum;
  onEdit: () => void;
  onPayRecord: () => void;
  onPay: () => void;
  onRefundRecord: () => void;
  onDelete: () => void;
  payLoading?: boolean;
}>();

const onEdit = () => {
  props.onEdit();
};

const handleDelete = () => {
  const dialog = DialogPlugin.confirm({
    theme: 'info',
    header: '删除',
    body: '删除后展示该应用的渠道会全部移除，是否继续删除？',
    onConfirm: () => {
      props.onDelete();
      dialog.destroy();
    },
  });
};
</script>

<style lang="less" scoped></style>
