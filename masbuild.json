{"asar": true, "extraFiles": [], "publish": [{"provider": "generic", "url": "https://dl.ringkol.com/"}], "afterPack": ".electron-vite/afterPack.js", "beforePack": ".electron-vite/beforePack.js", "afterSign": ".electron-vite/afterSign.js", "productName": "另可", "appId": "com.xht.macringkol", "directories": {"output": "build", "buildResources": "icons"}, "files": ["dist/electron/**/*"], "extraResources": [{"from": "./src/renderer/assets/account/<EMAIL>", "to": "./<EMAIL>"}, {"from": "./src/renderer/assets/account/docker.png", "to": "./docker.png"}], "dmg": {"sign": false, "contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "mac": {"hardenedRuntime": true, "gatekeeperAssess": false, "artifactName": "ringkol_mac_${arch}.${ext}", "icon": "icons/icon.icns", "target": [{"target": "mas", "arch": ["universal"]}], "extendInfo": {"CFBundleVersion": "114", "CFBundleURLSchemes": ["ringkol"], "NSMicrophoneUsageDescription": "如果不允许，你将无法在另可进行音视频通话", "NSCameraUsageDescription": "如果不允许，你将无法在另可进行视频通话", "Bundle name": "另可", "LSHasLocalizedDisplayName": true, "LSMinimumSystemVersion": "10.12.0", "ITSAppUsesNonExemptEncryption": "NO", "ElectronTeamID": "AQQ23CJPMD", "CFBundleLocalizations": ["en", "zh_TW", "zh_CN"], "com.apple.security.app-sandbox": true, "com.apple.security.application-groups": ["AQQ23CJPMD.com.xht.macringkol"], "com.apple.application-identifier": "AQQ23CJPMD.com.xht.macringkol", "com.apple.security.cs.allow-jit": true, "com.apple.security.cs.allow-dyld-environment-variables": true, "com.apple.security.cs.disable-library-validation": true, "com.apple.security.network.client": true, "com.apple.security.network.server": true, "com.apple.security.files.user-selected.read-write": true, "com.apple.security.files.user-selected.read-only": true, "com.apple.security.files.bookmarks.document-scope": true, "com.apple.security.files.bookmarks.app-scope": true, "com.apple.security.assets.music.read-only": true, "com.apple.security.assets.movies.read-only": true, "com.apple.security.assets.pictures.read-only": true, "com.apple.security.device.usb": true, "NSLocalNetworkUsageDescription": "此应用需要访问局域网来上传或者下载文件。", "NSDesktopFolderUsageDescription": "此应用需要访问您的桌面文件夹，以便您可以浏览或上传或下载文件。", "NSDocumentsFolderUsageDescription": "此应用需要访问您的文档文件夹，以便您可以浏览或上传或下载文件。", "NSDownloadsFolderUsageDescription": "此应用需要访问您的下载文件夹，以便您可以浏览或上传或下载文件。", "NSMusicFolderUsageDescription": "此应用需要访问您的音乐文件夹，以便您可以浏览或上传或下载文件。", "NSPicturesFolderUsageDescription": "此应用需要访问您的图片文件夹，以便您可以浏览或上传或下载文件。。", "NSMoviesFolderUsageDescription": "此应用需要访问您的电影文件夹，以便您可以浏览或上传或下载文件。"}, "provisioningProfile": "AQQ23CJPMD.provisionprofile"}, "mas": {"type": "distribution", "hardenedRuntime": false, "gatekeeperAssess": false, "entitlements": "mas.plist", "entitlementsInherit": "mas.child.plist", "entitlementsLoginHelper": "mas.helper.plist", "provisioningProfile": "AQQ23CJPMD.provisionprofile"}, "win": {"artifactName": "ringkol_win.${ext}", "icon": "icons/icon.png", "target": "nsis"}, "linux": {"target": "deb", "icon": "build/icons"}, "nsis": {"oneClick": false, "perMachine": true, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": true, "include": ".electron-vite/urlProtocol.nsh"}, "protocols": [{"name": "ringkol", "schemes": ["ringkol"]}]}