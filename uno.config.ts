import { defineConfig, presetIcons } from 'unocss';
import presetUno from '@unocss/preset-uno';
import transformerDirectives from '@unocss/transformer-directives';
import presetRemToPx from '@unocss/preset-rem-to-px';
import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders';
import fs from 'fs';

// 本地SVG图标目录
const iconsDir = './src/renderer/assets/svg';

// 读取本地 SVG 目录，自动生成 safelist
const generateSafeList = () => {
  try {
    const files = fs
      .readdirSync(iconsDir)
      .filter((file) => file.endsWith('.svg'));
    return [
      ...files.map((file) => `i-svg:${file.replace('.svg', '')}`),
      ...files.map((file) => `i-svg-color:${file.replace('.svg', '')}`),
    ];
  } catch (error) {
    console.error('无法读取图标目录:', error);
    return [];
  }
};

// 智能处理 SVG 颜色，避免错误填充透明区域和描边图标
const svgTransform = (svg: string) => {
  let transformedSvg = svg;

  // 优化 fill 和 stroke 属性处理，提升可读性与性能
  const skipColors = ['none', 'transparent', '#fff', '#ffffff', 'white'];

  // 1. 处理 fill 属性：仅替换非透明、非白色的填充为 currentColor
  transformedSvg = transformedSvg.replace(/fill="([^"]*?)"/g, (match, color) => (skipColors.includes(color.trim().toLowerCase()) ? match : 'fill="currentColor"'));

  // 2. 处理 stroke 属性：仅替换非透明描边为 currentColor
  transformedSvg = transformedSvg.replace(/stroke="([^"]*?)"/g, (match, color) => (['none', 'transparent'].includes(color.trim().toLowerCase()) ? match : 'stroke="currentColor"'));

  // 3. 如果 SVG 标签没有 fill 属性且不是纯描边图标，则添加 fill="currentColor"
  const hasFillElements = /<[^>]*fill="[^"]*"[^>]*>/g.test(transformedSvg);
  if (hasFillElements && !transformedSvg.includes('fill="currentColor"')) {
    transformedSvg = transformedSvg.replace(/<svg([^>]*?)>/g, (match, attributes) => {
      if (!attributes.includes('fill=')) return `<svg${attributes} fill="currentColor">`;
      return match;
    });
  }

  return transformedSvg;
};

// 保留原始颜色的 SVG 处理函数
const svgColorPreserve = (svg: string) => {
  // 只处理透明区域，保留原始颜色
  let transformedSvg = svg;

  // 只处理 none 和 transparent 的情况，保留其他所有颜色
  transformedSvg = transformedSvg.replace(/fill="(none|transparent)"/g, 'fill="none"');
  transformedSvg = transformedSvg.replace(/stroke="(none|transparent)"/g, 'stroke="none"');

  return transformedSvg;
};

// https://unocss.dev/
export default defineConfig({
  exclude: [
    'node_modules',
    'dist',
    '.git',
    '.electron-vite',
    'config',
    'customTypes',
    'rootLib',
    'src/main',
  ],
  presets: [
    presetUno({ dark: 'class' }),
    // 将所有工具类单位由rem转为px，例如：w-4 代表 width: 4px（默认为 width: 1rem）
    presetRemToPx({ baseFontSize: 4 }) as any,
    presetIcons({
      // 额外属性
      extraProperties: {
        display: 'inline-block',
        width: '1em',
        height: '1em',
        'vertical-align': 'middle',
        // color: 'currentColor',
      },
      // 图表集合
      collections: {
        // svg 是可变色图标集合，使用 `i-svg:图标名` 调用，图标颜色会变为 currentColor
        svg: FileSystemIconLoader(iconsDir, svgTransform),
        // svg-color 是保留原始颜色的图标集合，使用 `i-svg-color:图标名` 调用，保留图标原始颜色
        'svg-color': FileSystemIconLoader(iconsDir, svgColorPreserve),
      },
    }),
  ],
  safelist: generateSafeList(),
  transformers: [transformerDirectives()],
  shortcuts: {
    'wh-full': 'w-full h-full',
    'flex-center': 'flex justify-center items-center',
    'flex-col-center': 'flex-center flex-col',
    'flex-x-center': 'flex justify-center',
    'flex-y-center': 'flex items-center',
    'i-flex-center': 'inline-flex justify-center items-center',
    'i-flex-x-center': 'inline-flex justify-center',
    'i-flex-y-center': 'inline-flex items-center',
    'flex-col': 'flex flex-col',
    'flex-col-stretch': 'flex-col items-stretch',
    'i-flex-col': 'inline-flex flex-col',
    'i-flex-col-stretch': 'i-flex-col items-stretch',
    'flex-1-hidden': 'flex-1 overflow-hidden',
    'nowrap-hidden': 'whitespace-nowrap overflow-hidden',
    'ellipsis-text': 'nowrap-hidden text-ellipsis',
    'transition-base': 'transition-all duration-300 ease-in-out',
    cursor: 'cursor-pointer select-unset',
  },
});
