import { im_syncRequest, client_orgRequest, ringkolRequestApi } from "@/utils/apiRequest";
import { pairs, cardIds, cardId, createmMsg } from "../model/common";

export function searchPairs(data: pairs) {
  return ringkolRequestApi({
    method: "post",
    url: "/im/v1/pair/checkCreatedPairs",
    data,
  });
}

export function getNoteList(params: cardIds) {
  return ringkolRequestApi({
    method: "get",
    url: "/im/v1/cardRemark/getBatchCardRemarks",
    params,
  });
}

export function getInnerCardInfo(staffId: number, teamId?) {
  return client_orgRequest({
    method: "get",
    url: `/staffs/${staffId}`,
    headers: {
      teamId,
    },
  });
}

export function createMessage(data) {
  // 后端需要string类型，这里对数据进行处理
  if (data?.attachment?.member?.length) {
    data.attachment.member = data.attachment.member.map((item) => {
      item.jobId = `${item.jobId}`;
      item.teamId = `${item.teamId}`;
      item.departmentId = `${item.departmentId}`;
      return item;
    });
  }
  return ringkolRequestApi({
    method: "post",
    url: `/im/v1/pair/createPair`,
    data
  });
}

export function ignoreFriendApply(applyId: number) {
  return ringkolRequestApi({
    method: "post",
    url: `/im/v1/friend/apply/ignoreFriend`,
    data: {
      id: applyId,
    }
  });
}

export function notifyCount() {
  return ringkolRequestApi({
    method: "get",
    url: "/im/v1/friend/apply/getUnreadApplyCount",
  });
}

export function notifyRead() {
  // post body 空字符串
  return ringkolRequestApi({
    method: "post",
    url: "/im/v1/friend/apply/markAllAppliesAsRead",
    data:{applyId:''},
  });
}
