import { ringkolRequest as request } from '@renderer/utils/apiRequest';
import { PostAdd, PostDetail, PostListRequest, PostListResponse, TopicPostListRequest } from "./models/post";
import { AxiosResponseData, BasePageRequest } from "../model";
import { FriendStatus } from '@renderer/views/digital-platform/forum/constant';

 const Api = {
  publish: 'bbs/v1/post/publishPost',
  updatePostFriendFlag: '/bbs/v1/post/updatePostFriendFlag',
  addDraft: 'bbs/v1/post/saveDraft',
  post: (postId: string) => `bbs/v1/post/getPost/${postId}`,
  delPost: `bbs/v1/post/deletePost`,
  togglePostPin: '/bbs/v1/post/togglePostPin',
  posts: '/bbs/v1/post/listPost',
  drafts: '/bbs/v1/post/listDraft',
  listTopicPost: '/bbs/v1/post/listTopicPost',
  getPostStats: (postId: string) => `/bbs/v1/post/getPostStats/${postId}`,
};

// 发布帖子（发布草稿）
export const postPublish = (data: { post: PostAdd }): AxiosResponseData<{ postId: string }> => request.post(Api.publish, data, { hideMessage: true });
export const updatePostFriendFlag = (data: { postId: string; ownerId: string; friendFlag: boolean }): AxiosResponseData<{ postId: string }> => request.post(Api.updatePostFriendFlag, data, { hideMessage: true });

// 保存草稿
export const addDraft = (data: { post: PostAdd }): AxiosResponseData<{ postId: string }> => request.post(Api.addDraft, data);

// 草稿列表
export const getDraftList = (params: BasePageRequest): AxiosResponseData<PostListResponse> => request.get(Api.drafts, { params });

// 获取帖子（或草稿）详情
export const getPostInfo = (postId: string, params: { ownerId: string, teamId? }): AxiosResponseData<{ post: PostDetail }> => request.get(Api.post(postId), { params, ...params });

// 删除帖子（或草稿）
export const delPost = (postId: string) => request.post(Api.delPost, { postId });

// 置顶帖子（已置顶时取消置顶）
export const togglePin = (postId: string): AxiosResponseData<{ pined: boolean }> => request.post(Api.togglePostPin, { postId });

// 帖子列表
export const getPostList = (params: PostListRequest): AxiosResponseData<PostListResponse> => request.get(Api.posts, { params });

// 话题帖子列表
export const getTopicPostList = (params: TopicPostListRequest): AxiosResponseData<PostListResponse> => request.get(Api.listTopicPost, { params });

// 获取帖子统计信息
export const getPostStats = (postId: string): AxiosResponseData<{ comments?: number; likes?: number; }> => request.get(Api.getPostStats(postId));
