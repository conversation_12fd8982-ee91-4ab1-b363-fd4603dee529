<script setup lang="ts">
import { ref, computed, provide } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';
import PostList from '@/views/digital-platform/forum/components/post/PostList.vue';
import TopPostList from '@/views/digital-platform/forum/components/post/TopPostList.vue';
import FloatActionBtn from '@/views/digital-platform/forum/components/FloatActionBtn.vue';
import PostPublishDialog from '@/views/digital-platform/forum/components/post/PostPublishDialog.vue';
import HomeHeader from './components/HomeHeader.vue';
import { useForumStore } from '../store';
import { PageTypeMap } from '../constant';

provide(PageTypeMap.Home, PageTypeMap.Home);

const forumStore = useForumStore();
const params = ref({
  // 1: 论坛，2: 我的主页，3: 特定所有者
  from: '2',
});

const refreshKey = ref(0);
const refreshTopPost = ref(0);
const pageContent = ref(null);
const scroll = ref(0);
onBeforeRouteLeave((to, from, next) => {
  scroll.value = pageContent.value?.scrollTop;
  next();
});

const selfPost = computed(() => params.value.from === '2');

const postListRef = ref(null);
const scrollDisabled = computed(() => {
  if (selfPost.value) {
    return false;
  }
  return postListRef.value?.status === 'finished';
});
const onLoading = () => {
  postListRef.value?.loading();
};

const isEmpty = ref(true);
const loaded = (list) => {
  isEmpty.value = list.posts.length === 0;
};

const postPublishVisible = ref(false);
const postSubmitKey = ref(1);
const submitPost = () => {
  postPublishVisible.value = false;
  postSubmitKey.value++;
  refreshKey.value++;
};
</script>

<template>
  <!-- 查看自已所有身份的帖子 -->
  <div
    ref="pageContent"
    v-infinite-scroll="onLoading"
    class="page-content"
    :infinite-scroll-immediate-check="false"
    :infinite-scroll-distance="1800"
    :infinite-scroll-disabled="scrollDisabled"
    infinite-scroll-watch-disabled="scrollDisabled"
  >
    <HomeHeader self />
    <TopPostList
      :key="refreshTopPost"
      from="2"
      class="mb-8"
    />

    <div class="list-wrap" :class="{ empty : isEmpty }">
      <PostList
        ref="postListRef"
        :key="refreshKey"
        :params="params"
        :self="selfPost"
        :empty-publish="selfPost"
        :ownerId="forumStore.ownerId"
        show-flag
        go-detail
        show-total
        class="w-full"
        from="friend-circle"
        @removed="refreshKey++; refreshTopPost++"
        @toggle-top="refreshTopPost++"
        @load="loaded"
        @publish="postPublishVisible = true"
      />
    </div>

    <FloatActionBtn
      v-if="!isEmpty"
      container=".page-content"
      :visible-height="50"
      :offset="['388px', '40px']"
      can-publish
      @publish="postPublishVisible = true"
    />

    <PostPublishDialog
      v-if="postPublishVisible"
      v-model="postPublishVisible"
      @submit="submitPost"
    />
  </div>
</template>

<style scoped lang="less">
.page-content {
  gap: var(--kyy_radius_dropdown_m, 8px);

  .header {
    display: flex;
    padding: 12px 16px;
    align-items: center;
    gap: 4px;
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
    background: var(--bg-kyy_color_bg_light, #FFF);
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 16px;
    font-weight: 400;
    line-height: 24px; /* 150% */
    margin-bottom: 12px;
    .back {
      display: flex;
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      cursor: pointer;
      .icon {
        font-size: 20px;
      }
    }
    .title {
      flex: 1;
      padding-right: 52px;
      text-align: center;
    }
  }

  .list-wrap {
    flex: 1;
    overflow-y: auto;
  }
}
</style>
