<template>
  <t-dialog v-model:visible="visible" :header="null" :close-btn="false" width="600" :footer="null" z-index="1000"
    attach="body" class="dialogSet dialogSet-noBodyPadding dialogSetNP" :close-on-overlay-click="false"
    :close-on-esc-keydown="false">
    <template #body>
      <div class="nameDetail">
        <img class="bannerBot" src="@renderer/assets/member/svg/top_img.svg" />

        <!-- <t-dropdown
          v-if="dropdownOpts.length > 1"
          :options="dropdownOpts"
          placement="left"
          overlay-class-name="activity"
          @click="onCSClick"
        >
          <div class="kefu">
            <iconpark-icon name="sidebaricon" class="icon"></iconpark-icon>
          </div>
        </t-dropdown>
        <div v-else-if="dropdownOpts.length > 0" class="kefu">
          <iconpark-icon name="sidebaricon" class="icon" @click="onCSClick(dropdownOpts[0])"></iconpark-icon>
        </div> -->
        <!-- <specialist v-if="visible" ref="specialistRef" channel="directories" :team-id="csTeamId">
          <div class="kefu">
            <iconpark-icon name="sidebaricon" class="icon"></iconpark-icon>
          </div>
        </specialist> -->
        <div v-show="datas?.openid !== seifOpenId" class="kefu" @click="msg">
          <!-- <iconpark-icon name="sidebaricon" class="icon"></iconpark-icon> -->
          <img src="@renderer/assets/chat.svg" />
        </div>

        <div v-show="datas?.openid === seifOpenId" class="setbtn cursor" @click="onSetDirectory">
          设置
        </div>
        <div class="close cursor" @click="onClose">
          <iconpark-icon name="iconerror" class="iconerror"></iconpark-icon>
        </div>
        <div class="first">
          <t-image class="img" :style="styleProp" fit="cover" :src="datas?.img" @click="preview([datas.img])">
            <template #loading>
              <img src="@renderer/assets/member/svg/name_detail_default.svg" />
            </template>
            <template #error>
              <img src="@renderer/assets/member/svg/name_detail_default.svg" />
            </template>
          </t-image>
          <div v-show="styleProp.opacity === 1 && datas?.openid === seifOpenId">
            <calling :sustyle="true" @confirm="confirmLogo" />
          </div>

          <!-- <t-image class="img"  v-else :style="styleProp" src="@renderer/assets/member/svg/name_detail_default.svg"/> -->

          <div class="two" ref="containerFlets">
            <div class="scroll">
              <div class="nameBox">
                <div class="rlo">
                  <div class="typec" v-if="props.origin === originType.Member">
                    <span class="per" v-show="datas?.type === 2">个人会员</span>
                    <span class="uni" v-show="datas?.type === 1">单位会员</span>
                  </div>
                  <div class="typec" v-else>
                    <span class="per" v-show="datas?.type === 2">个人</span>
                    <span class="uni" v-show="datas?.type === 1">组织</span>
                  </div>
                  <div class="card">
                    <div class="tagMain">
                      <div class="tagMain-left">
                        <div class="card-name">
                          <span class="text">{{ datas?.name }}</span>
                          <!-- <t-tooltip :content="$t('member.winter_column.activeOptions_1')" :show-arrow="false">
                            <span class="boxname">
                              <iconpark-icon v-show="datas?.activate === 1" name="activeicon" class="certify"></iconpark-icon>
                            </span>
                          </t-tooltip> -->
                        </div>
                        <!-- <div class="card-unit mt-4px" v-if="props.origin === originType.Member">
                          <span class="text" v-show="datas?.level_name">{{ datas?.level_name }}</span>
                          <span class="line" v-show="datas?.member_job && datas?.level_name"></span>
                          <span class="text" v-show="datas?.member_job">{{ datas?.member_job }}</span>
                          <span class="line" v-show="datas?.member_job || datas?.level_name"></span>
                        </div> -->
                        <div class="card-unit mt-4px">
                          <template v-if="props.origin === originType.Member">
                            <span class="level" v-show="datas?.level_name">{{ datas?.level_name }}</span>
                            <span class="tagColor" :style="{ background: '#E4F5FE', color: '#21ACFA' }"
                              v-show="datas?.member_job">{{ datas?.member_job }}</span>
                          </template>


                          <!-- <span class="text">{{ datas?.team_name }}</span> -->
                          <span class="tagColor" v-for="(tagColorItem, index) in datas?.label_relation?.team?.value"
                            :key="index" :style="{ background: tagColorItem.bgColor, color: tagColorItem.color }">
                            {{ tagColorItem?.name }}
                          </span>
                          <span class="tagColor" v-for="(tagColorItem, index) in datas?.label_relation?.personal?.value"
                            :key="index" :style="{ background: tagColorItem.bgColor, color: tagColorItem.color }">
                            {{ tagColorItem?.name }}
                          </span>
                        </div>
                      </div>
                      <div class="tagMain-right">
                        <t-tooltip :content="$t('member.bing.i')" :show-arrow="false">
                          <span class="idcardbox" @click="onCard">
                            <img src="@renderer/assets/member/svg/idCard.svg" />
                          </span>
                        </t-tooltip>
                      </div>
                    </div>
                    <div class="touxian" v-if="datas?.department_position?.length">
                      <div class="card-title mt-8px">
                        <span class="text"
                          v-for="(itemTitle, itemTitleIndex) in datas?.department_position?.slice(0, datas.isShowTitleMore ? datas?.department_position?.length : 1)"
                          :key="itemTitleIndex">
                          {{ itemTitle?.department_name || '--' }}/{{itemTitle.job_name || '--' }}
                        </span>
                      </div>
                      <div class="expand" v-show="datas?.department_position?.length > 1">
                        <iconpark-icon v-show="datas.isShowTitleMore" @click="onShowTitleMore" name="iconarrowup"
                          class="iconarrowup cursor"></iconpark-icon>
                        <iconpark-icon v-show="!datas.isShowTitleMore" @click="onShowTitleMore" name="iconarrowdwon"
                          class="iconarrowdwon cursor"></iconpark-icon>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="clo" v-show="datas?.hobby">
                  <div class="desc">
                    <div class="desc-row">
                      <div class="value"><span class="bold">兴趣爱好：</span>{{ datas?.hobby }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <!-- <div class="title w-56px">
                  <span class="text">组织信息</span>
                  <span class="bottom"></span>
                </div> -->
                <div class="boxsell">
                  <!-- <div class="boxsell-item" v-show="datas?.is_connect">
                    <div class="boxsell-item-title">组织logo</div>
                    <div class="boxsell-item-content">
                      <t-image :src="getSrcLogo(datas?.team_logo)" fit="cover" class="image">
                        <template #error>
                          <img class="image" :src="defaultCompany" alt="default-avatar" />
                        </template>
                      </t-image>
                    </div>
                  </div> -->
                  <div class="boxsell-item"  v-show="datas?.work_num">
                    <div class="boxsell-item-title">工号</div>
                    <div class="boxsell-item-content">
                      {{ datas?.work_num }}

                      <!-- <span class="certified ml-4px" v-show="datas?.team_auth === 1">已认证</span> -->
                    </div>
                  </div>
                  <div class="boxsell-item" v-for="(tel, telIndex) in datas?.telephone_info" :key="telIndex">
                    <div class="boxsell-item-title">手机号码</div>
                    <div class="boxsell-item-content">
                      {{tel?.phone_code ? '+'+tel.phone_code : '--'}} {{tel?.telephone}}

                    </div>
                  </div>
                  <div class="boxsell-item" v-show="datas?.email"> 
                    <div class="boxsell-item-title">邮箱地址</div>
                    <div class="boxsell-item-content">
                      {{ datas?.email }}
                    </div>
                  </div>
                  <div class="boxsell-item" v-for="(customize, customizeIndex) in datas?.customize_info" :key="telIndex">
                    <div class="boxsell-item-title">{{customize?.name}}</div>
                    <div class="boxsell-item-content">
                      {{customize?.value}}
                    </div>
                  </div>

                  <template v-if="datas?.is_connect">
                    <div class="boxsell-item" v-show="datas?.square_id">
                      <div class="boxsell-item-title">广场号</div>
                      <div class="boxsell-item-content">
                        <t-link theme="primary" hover="color" @click="goSquare(datas?.square_id)">{{
                          datas?.square_name
                        }}</t-link>
                      </div>
                    </div>
                    <div class="boxsell-item" v-show="datas?.job">
                      <div class="boxsell-item-title">组织岗位</div>
                      <div class="boxsell-item-content">
                        {{ datas?.job }}
                      </div>
                    </div>
                    <div class="boxsell-item" v-show="datas?.team_short_name">
                      <div class="boxsell-item-title">组织简称</div>
                      <div class="boxsell-item-content">
                        {{ datas?.team_short_name }}
                      </div>
                    </div>
                    <div class="boxsell-item" v-show="datas?.team_type_text">
                      <div class="boxsell-item-title">组织类型</div>
                      <div class="boxsell-item-content">
                        {{ datas?.team_type_text }}
                      </div>
                    </div>
                    <div class="boxsell-item" v-show="datas?.industry_text">
                      <div class="boxsell-item-title">行业类型</div>
                      <div class="boxsell-item-content">
                        {{ datas?.industry_text }}
                      </div>
                    </div>
                    <div class="boxsell-item" v-show="datas?.size_text">
                      <div class="boxsell-item-title">组织规模</div>
                      <div class="boxsell-item-content">
                        {{ datas?.size_text }}
                      </div>
                    </div>
                    <div class="boxsell-item" v-show="datas?.business">
                      <div class="boxsell-item-title">业务范围</div>
                      <div class="boxsell-item-content">
                        {{ datas?.business }}
                      </div>
                    </div>
                    <div class="boxsell-item" v-show="datas?.address">
                      <div class="boxsell-item-title">组织地址</div>
                      <div class="boxsell-item-content">
                        {{ datas?.address }}
                      </div>
                    </div>
                  </template>
                  <template v-else-if="datas?.self">
                    <div class="lock">
                      <img class="lock-img" src="@renderer/assets/member/svg/lock_icon.svg" />
                      <div class="lock-text">
                        <div class="lock-text-title">连接平台后会展示更多信息</div>
                        <t-link theme="primary" hover="color" @click="onConnectPlatform">连接平台</t-link>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              <!-- <template
                v-if="datas?.is_connect && datas?.contact_switch && datas?.contact_info && datas?.contact_info.length > 0">
                <div class="row">
                  <div class="title w-72px">
                    <span class="text">联系人信息</span>
                    <span class="bottom"></span>
                  </div>
                  <div class="columbox mt-12px">
                    <wc-waterfall gap="12" cols="2">
                      <div class="col cursor" v-for="(item, index) in datas?.contact_info"
                        @click="onOpenContactCard(item)" :key="index">
                        <div class="name mb-12px">
                          <div class="left">
                            <KyyAvatar class="avatar" avatar-size="44px" :image-url="item?.avatar"
                              :user-name="item?.name" shape="circle" />
                          </div>
                          <div class="right w-170px">
                            <span class="tname line-1">{{ item?.name }}</span>
                            <span class="tlevel line-1">{{ item?.job }}</span>
                          </div>
                        </div>
                        <div class="col-tem">
                          <iconpark-icon name="icontelephone" class="telicon"></iconpark-icon>
                          <span class="tel">+{{ item?.telcode }} {{ item?.telephone }}</span>
                        </div>
                        <div class="col-tem mt-4px">
                          <iconpark-icon name="iconemail" class="emailicon"></iconpark-icon>
                          <span class="tel">{{ item?.email || "--" }}</span>
                        </div>
                      </div>
                    </wc-waterfall>
                  </div>
                </div>
              </template>

              <div class="row" v-if="mainCardInfo && datas?.business_card_switch">
                <div class="title w-56px">
                  <span class="text">数字名片</span>
                  <span class="bottom"></span>
                </div>
                <div class="columbox">
                  <div class="vcard mt-12px cursor" @click="onOpenVcard">
                    <MyVCardType :infoData="mainCardInfo"></MyVCardType>
                  </div>
                </div>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </template>
    <!-- <template #footer>
      <div class="footer mt-16px">
        <t-button
          class="btn confirm"
          variant="base"
          theme="default"
          style="width: 80px"
          @click="onAfterSet"
        > 以后再说</t-button>

        <t-button
          class="btn confirm"
          theme="primary"
          variant="base"
          style="width: 80px"
          @click="toVerifyWeb"
        > 前往管理</t-button>
      </div>
    </template> -->
  </t-dialog>
  <SetDirectoryFormModal ref="setDirectoryFormModalRef" :origin="props.origin" @onReload="onReloadNameDetail"
    :teamId="currentTeamId" />
  <VcardDrawer ref="vcardDrawerRef" />
</template>

<script setup lang="ts">
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import KyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { ref, onMounted, onUnmounted, computed, toRaw } from "vue";
import { useRouter } from "vue-router";
import { getSrcLogo } from "@renderer/views/message/service/msgUtils";
import { useApi } from "@renderer/views/member/hooks/api";
import { createSession, getKeFuGroupList } from "@renderer/api/customerService";
import to from "await-to-js";
import { AxiosError, AxiosResponse } from "axios";
import { getOpenid, getProfilesInfo } from "@renderer/utils/auth";
import { MessagePlugin } from "tdesign-vue-next";
import calling from "@renderer/views/digital-platform/components/calling.vue";
import {
  // editSpecifiedFields,
  // getMemberNameDetailAxios as getMemberNameDetailAxiosMember,
  onGetLabelSettingAxios as onGetLabelSettingMemberAxios,
} from "@renderer/api/member/api/businessApi";
import {
  // editSpecifiedFieldsGVR,
  // getMemberNameDetailAxios as getMemberNameDetailAxiosPolitics,
  onGetLabelSettingAxios as onGetLabelSettingPoliticsAxios,
} from "@renderer/api/politics/api/businessApi";
import {
  // editSpecifiedFieldsCBR,
  // getMemberNameDetailAxios as getMemberNameDetailAxiosCBD,
  onGetLabelSettingAxios as onGetLabelSettingCBDAxios,
} from "@renderer/api/cbd/api/businessApi";
import {
  // editSpecifiedFieldsASS,
  // getMemberNameDetailAxios as getMemberNameDetailAxiosAssociation,
  onGetLabelSettingAxios as onGetLabelSettingAssociationAxios,
} from "@renderer/api/association/api/businessApi";

import {
  teamDirectorySaveImgAxios,
  getTeamDirectoryDetailAxios,
} from "@renderer/api/digital-platform/api/businessApi";

import { TagColors, originType } from "@renderer/views/digital-platform/utils/constant";

// import SetDirectoryFormModal from "@renderer/views/digital-platform/modal/set-directory-form-modal.vue"
import SetDirectoryFormModal from '@renderer/views/workBench/modal/set-directory-form-modal.vue';

import specialist from "@renderer/views/customerService/specialist.vue";
import lodash from 'lodash';
import { openChat } from '@/utils/share';
import { mainCard } from '@renderer/api/vcard/index.ts';

import MyVCardType from '@renderer/windows/vcard/components/MyVCardType.vue';
import VcardDrawer from '@renderer/views/digital-platform/modal/vcard-drawer.vue'
const { onGoSquarePage } = useApi();
const defaultCompany = ref("https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/1720590812494123343067.png");
const store = useDigitalPlatformStore();
const props = defineProps({
  origin: {
    type: String,
    default: originType.Member,
  },
  activationGroupItem: {
    type: Object,
    default: () => { },
  },
});


const handleObj = {
  getNameDetailAxiosFunc: getTeamDirectoryDetailAxios,
  getLabelFunc: null,
}
// const handleFunc = () => {
//   switch (props.origin) {
//     case originType.Member:
//       // handleObj.saveTitleFunc =
//       handleObj.getNameDetailAxiosFunc = getMemberNameDetailAxiosMember;
//       handleObj.getLabelFunc = onGetLabelSettingMemberAxios;
//       break;
//     case originType.Politics:
//       handleObj.getNameDetailAxiosFunc = getMemberNameDetailAxiosPolitics;
//       handleObj.getLabelFunc = onGetLabelSettingPoliticsAxios;
//       break;
//     case originType.CBD:
//       handleObj.getNameDetailAxiosFunc = getMemberNameDetailAxiosCBD;
//       handleObj.getLabelFunc = onGetLabelSettingCBDAxios;
//       break;
//     case originType.Association:
//       handleObj.getNameDetailAxiosFunc = getMemberNameDetailAxiosAssociation;
//       handleObj.getLabelFunc = onGetLabelSettingAssociationAxios;
//       break;
//     default:

//       break;
//   }
// }
// handleFunc();


const router = useRouter();
const visible = ref(false);
const scrolledDistance = ref(0); // 滚动距离
const containerFlets = ref(null);
const datas = ref(null);
const emits = defineEmits(["change-succ", "onConnectPlatform", "onClose", "onCard"]);
const styleProp = computed(() => {
  const top = scrolledDistance.value < 141 ? `${-scrolledDistance.value}px` : "-140px";
  let opacity = 1;
  const height = 140;
  const round = 10;
  if (scrolledDistance.value === 0) {
    opacity = 1;
  } else if (scrolledDistance.value < height) {
    opacity = 1 / scrolledDistance.value;
  } else if (scrolledDistance.value > height || scrolledDistance.value === height) {
    opacity = 0;
  }
  return {
    top,
    opacity,
    transition: "all 0.25s linear",
  };
});

// const labelSetting = ref(null);
// const onGetLabelSettingInfo = async ()=> {
//   return new Promise(async (resolve, reject) => {
//     const [err, res] = await to(handleObj.getLabelFunc({}, currentTeamId.value))
//     if(err) return reject();
//     if(res) {
//       const { data }: any = res;
//       console.log(data)
//       labelSetting.value = data?.data;
//       resolve(data?.data)
//     } else {
//       reject();
//     }
//   })
// }

const mainCardInfo = ref(null);
const onGetMainCardInfo = async () => {
  return new Promise(async (resolve, reject) => {
    const [err, res] = await to(mainCard({ openid: datas.value?.openid }, currentTeamId.value))
    if (err) return reject();
    if (res) {
      const { data }: any = res;
      console.log(data)
      mainCardInfo.value = data?.data;
      resolve(data?.data)
    } else {
      reject();
    }
  })
}


const vcardDrawerRef = ref(null);
const onOpenVcard = () => {
  console.log(mainCardInfo.value)
  vcardDrawerRef.value?.onOpen(mainCardInfo.value);
}

const msg = () => {
  // console.log(datas.value)
  // emits("onCard", datas.value);
  console.log({main: props?.activationGroupItem?.cardId, peer: datas.value?.card_id })
  openChat({main: props?.activationGroupItem?.cardId, peer: datas.value?.card_id });
}


const setDirectoryFormModalRef = ref(null);
// 优化思路：简化 Promise 包装，直接返回异步操作结果，错误处理更清晰
const onGetMemberNameDetail = async (box) => {
  const [err, res] = await to(handleObj.getNameDetailAxiosFunc({ id_staff: box.id_staff }, currentTeamId.value));
  if (err) {
    throw err;
  }
  return res.data;
};

const onReloadNameDetail = () => {
  onGetMemberNameDetail(datas.value).then((res: any) => {
    console.log(res);
    onOpen(res?.data, currentTeamId.value);
  });
  emits('change-succ');
}

const handleScroll = (event) => {
  console.log(event.target.scrollTop);
  scrolledDistance.value = event.target.scrollTop;
  // scrolledDistance.value = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
};
const onConnectPlatform = () => {
  console.log('onConnectPlatform')
  emits("onConnectPlatform", toRaw(datas.value));
};

const goSquare = (squareId) => {
  onGoSquarePage(squareId);
};

const dropdownOpts = ref([]);
const getCSGroupList = async (teamId) => {
  const [err, res] = await to(getKeFuGroupList({ channelType: "directories" }, teamId));
  if (!err) {
    const { data } = res.data;
    dropdownOpts.value = data.list.map((v) => ({
      content: v.kefu_group_name,
      value: v.id,
    }));
  }
};

const onCard = () => {
  emits("onCard", datas.value);
};

const onOpenContactCard = (item) => {
  const params = {
    card: "PT" + item.id_staff,
    activate: 1,
  };
  emits("onCard", params);
};

const onCSClick = async (action) => {
  const teamId = datas.value?.relation_team_id;
  const openId = getOpenid();

  // 创建会话，根据 groupId 获取相关数据
  const [err, res] = await to<AxiosResponse, AxiosError<{ message: string }>>(
    createSession({ main: openId, groupId: action.value }, teamId),
  );
  if (err) {
    await MessagePlugin.error(err.response.data.message);
    return;
  }
  const { main, peer } = res.data.data;

  const { ipcRenderer } = require("electron");
  await ipcRenderer.invoke("im.chat.open", { main, peer });
  ipcRenderer.send("update-nume-index", 0);
};

onMounted(() => {
  containerFlets.value?.addEventListener("scroll", handleScroll); // 监听滚动事件
});
onUnmounted(() => {
  containerFlets.value?.removeEventListener("scroll", handleScroll); // 取消监听滚动事件
});

const { ipcRenderer } = require("electron");
const preview = (imgs) => {
  if (!(imgs && imgs.length > 0 && imgs[0])) return;
  const temp = imgs.map((item) => {
    return { url: item };
  });
  ipcRenderer.invoke("view-img", JSON.stringify(temp));
};


const onSetDirectory = () => {
  console.log('onSetDirectory')

  setDirectoryFormModalRef.value?.onOpen(datas.value);
}



const onClose = () => {
  visible.value = false;
  containerFlets.value.scrollTop = 0;
  scrolledDistance.value = 0;
  emits("onClose");
};
const currentTeamId = ref(0);
const csTeamId = ref(null);
const onOpen = (data: any, teamId) => {
  currentTeamId.value = teamId;
  console.log("哈哈", data);
  visible.value = true;

  if (data?.label_relation?.personal.value?.length > 0) {
    data.label_relation.personal.value = data.label_relation.personal.value.map(v => {
      return {
        ...switchColor(v.colour),
        ...v
      };
    })
  }
  if (data?.label_relation?.team?.value?.length > 0) {
    data.label_relation.team.value = data.label_relation.team.value.map(v => {
      return {
        ...switchColor(v.colour),
        ...v
      };
    })
  }
  data.isShowTitleMore = false;
  console.log(data)
  datas.value = data;
  if(data?.relation_team_id) {
    // getCSGroupList(data?.relation_team_id);
    csTeamId.value = data?.relation_team_id;
  }

  if (data?.openid) {
    onGetMainCardInfo();

  }
  // onGetLabelSettingInfo();
};

const onShowTitleMore = () => {
  datas.value.isShowTitleMore = !datas.value.isShowTitleMore
}

const changeok = (file_name) => {
  MessagePlugin.success("修改成功");
  // datas.value.avatar = file_name;
  // emits("change-succ");
  onReloadNameDetail();

};

// const editSpecifiedFieldsMb = (params, file_name) => {
//   editSpecifiedFields(params, currentTeamId.value)
//     .then((res) => {
//       if (res) {
//         changeok(file_name);
//       }
//     })
//     .catch((err) => {
//       console.log(err);
//       MessagePlugin.error(err?.message);
//     });
// };

// const editSpecifiedFieldsCBD = (params, file_name) => {
//   editSpecifiedFieldsCBR(params, currentTeamId.value)
//     .then((res) => {
//       if (res) {
//         changeok(file_name);
//       }
//     })
//     .catch((err) => {
//       console.log(err);
//       MessagePlugin.error(err?.message);
//     });
// };
// const editSpecifiedFieldsGV = (params, file_name) => {
//   editSpecifiedFieldsGVR(params, currentTeamId.value)
//     .then((res) => {
//       if (res) {
//         changeok(file_name);
//       }
//     })
//     .catch((err) => {
//       console.log(err);
//       MessagePlugin.error(err?.message);
//     });
// };

// const editSpecifiedFieldsASSReq = (params, file_name) => {
//   editSpecifiedFieldsASS(params, currentTeamId.value)
//     .then((res) => {
//       if (res) {
//         changeok(file_name);
//       }
//     })
//     .catch((err) => {
//       console.log(err);
//       MessagePlugin.error(err?.message);
//     });
// };


const onSaveImg = async (params, file_name) => {
  teamDirectorySaveImgAxios(params, currentTeamId.value).then((res) => {
    if (res) {
      changeok(file_name);
    }
  })
  .catch((err) => {
    console.log(err);
    MessagePlugin.error(err?.message);
  });
}

const switchColor = (intColor) => {
  return TagColors.find((item) => item.intColor == intColor)
}

const confirmLogo = (data) => {
  const params = {
    // id: datas.value?.id,
    // directory_image_values: [data],
    // is_contact: datas.value?.is_contact,
    img: data.file_name,
  };

  onSaveImg(params, data.file_name);

  // switch (props.origin) {
  //   case originType.Member:
  //     editSpecifiedFieldsMb(params, data.file_name);
  //     break;
  //   case originType.Politics:
  //     editSpecifiedFieldsGV(params, data.file_name);
  //     break;
  //   case originType.CBD:
  //     editSpecifiedFieldsCBD(params, data.file_name);
  //     break;
  //   case originType.Association:
  //     editSpecifiedFieldsASSReq(params, data.file_name);
  //     break;
  //   default:
  //     editSpecifiedFieldsMb(params, data.file_name);
  //     break;
  // }

};

const showCalling = ref(false);
const callingRef = ref(null);

const seifOpenId = ref(getProfilesInfo().openid);

defineExpose({
  onOpen,
  onClose,
});
</script>

<style lang="less" scoped>
// src\renderer\assets\member\svg\name_detail_default.svg
// :deep(.activity) {
//   padding: 4px !important;
// }

:deep(.head-card) {
  margin-left: 0 !important;
}


.tagMain {
  display: flex;
  justify-content: space-between;

  &-left {}

  &-right {
    .idcardbox {
      display: flex;
      align-items: center;
      justify-content: center;
      // width: 24px;
      // height: 24px;
      padding: 9px 6px;

      img {
        width: 36px;
      }

      flex: none;

      &:hover {
        border-radius: 4px;

        background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
      }
    }
  }
}

.setbtn {
  color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
  display: flex;
  justify-content: center;
  align-items: center;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
  width: 60px;
  height: 28px;

  position: absolute;
  right: 60px;
  top: 30px;
  z-index: 30;

  border-radius: var(--radius-kyy_radius_button_s, 4px);
  border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
  background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);


}

.touxian {
  display: flex;
  width: 100%;
  gap: 4px;
  justify-content: space-between;

  .expand {
    .iconarrowup {
      font-size: 20px;
    }

    .iconarrowdwon {
      font-size: 20px;
    }
  }
}

.nameDetail {
  // src\renderer\assets\member\svg\name_detail.svg
  height: 560px;
  border-radius: 16px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  position: relative;

  .kefu {
    position: absolute;
    right: 32px;
    bottom: 42px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    z-index: 40;
    background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4d5eff);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    user-select: none;

    .icon {
      font-size: 32px;
      color: #fff;
    }

    &:hover {
      background: var(--color-button_primary-kyy_color_button_primary_bg_hover, #707eff);
    }
  }

  // padding: 32px 6px;
  // padding-bottom: 0;
  // url(assets/member/svg/name_detail.svg) no-repeat; /* 这里的图片可能会被渐变覆盖 */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 240px;
    /* 设置为50%以创建上半部分 */
    background-image: url("@/assets/member/svg/name_detail.svg");
    /* 上半部分的图片背景 */
    background-size: cover;
    /* 确保图片覆盖整个区域 */
    background-position: center;
    /* 调整图片位置 */
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
  }

  .bannerBot {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 22;
    width: 100%;
    height: 32px;
  }

  .close {
    position: absolute;
    z-index: 30;
    right: 24px;
    top: 32px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    .iconerror {
      color: #fff;
      font-size: 24px;
    }
  }

  .first {
    position: absolute;
    z-index: 3;

    // background-color: red;
    bottom: 0px;
    top: 32px;
    left: 0;
    right: 0;
    padding: 0 6px;
    // padding-bottom: 6px;
    padding-top: 44px;
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;

    .img {
      position: absolute;
      top: 0;
      left: calc(24px + 24px);
      width: 100px;
      height: 140px;
      border-radius: 8px;
      transition: all 0.25s linear;
    }

    .two {
      padding: 0 18px;
      padding-bottom: 24px;
      // background-color:  blue;
      height: 100%;
      overflow-y: overlay;

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        // background-color: red;
      }

      /*定义滚动条轨道 内阴影+圆角*/
      &::-webkit-scrollbar-track {
        // box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        // border-radius: 10px;
        // background-color: #fff;
      }

      /*定义滑块 内阴影+圆角*/
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
        background-color: #d5dbe4;
      }

      .scroll {
        // background-color: yellow;
        width: 100%;
        height: fit-content;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .nameBox {
          width: inherit;
          border-radius: 8px;
          background: #fff;
          min-height: 1 12px;
          padding: 16px 24px;
          display: flex;
          flex-direction: column;
          gap: 12px;

          .rlo {
            display: flex;

          }

          .typec {
            padding-top: 88px;
            width: 100px;
            display: flex;
            justify-content: center;
            align-items: flex-start;

            .per {
              min-width: 64px;
              width: fit-content;
              color: var(--kyy_color_tag_text_success, #499D60);
              font-size: 12px;
              font-weight: 400;
              line-height: 20px;
              /* 166.667% */
              border-radius: var(--kyy_radius_tag_full, 999px);
              background: var(--kyy_color_tag_bg_success, #E0F2E5);
              padding: 0px 8px;
              text-align: center;
            }

            .uni {
              width: fit-content;
              min-width: 64px;
              color: var(--kyy_color_tag_text_purple, #CA48EB);
              font-size: 12px;
              font-weight: 400;
              line-height: 20px;
              /* 166.667% */
              padding: 0px 8px;
              border-radius: var(--kyy_radius_tag_full, 999px);
              background: var(--kyy_color_tag_bg_purple, #FAEDFD);
              text-align: center;
            }
          }

          .card {
            flex: 1;
            // height: 80px;
            padding-left: 16px;

            &-name {
              color: var(--text-kyy_color_text_2, #516082);
              /* kyy_fontSize_2/regular */
              font-family: "PingFang SC";
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
              /* 157.143% */

              display: flex;
              gap: 4px;

              .text {
                color: var(--text-kyy_color_text_1, #1a2139);
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px;
                /* 150% */
              }

              .boxname {
                width: 24px;
                height: 24px;

                &:hover {
                  border-radius: 4px;
                  background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
                }

                display: flex;
                align-items: center;
                justify-content: center;
              }

              .certify {
                font-size: 20px;
                // width: 24px;
              }
            }

            &-unit {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              gap: 4px;

              .tagColor {
                flex: none;
                padding: 0 4px;
                border-radius: var(--kyy_radius_tag_s, 4px);
                font-family: "PingFang SC";
                font-size: 12px;
                font-weight: 400;
                line-height: 20px;
                /* 166.667% */
                max-width: 380px;
              }

              .level {
                color: var(--text-kyy_color_text_3, #828DA5);
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                /* 157.143% */
              }

              .text {
                color: var(--text-kyy_color_text_2, #516082);
                /* kyy_fontSize_2/regular */
                font-family: "PingFang SC";
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                /* 157.143% */
              }

              .line {
                height: 16px;
                min-width: 1px;
                max-width: 1px;
                background: var(--divider-kyy_color_divider_light, #eceff5);
              }

              .tag {
                padding: 0 4px;
                color: var(--kyy_color_tag_text_purple, #ca48eb);
                text-align: center;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
                /* 166.667% */
                border-radius: var(--kyy_radius_tag_s, 4px);
                background: var(--kyy_color_tag_bg_purple, #faedfd);
              }
            }

            &-title {
              display: flex;
              flex-direction: column;
              position: relative;
              gap: 2px;

              // &::after {
              //   content: "";
              //   position: absolute;
              //   background: var(--kyy_blue-kyy_color_kyyBlue_hover, #49BBFB);
              //   top: 4px;
              //   bottom: 4px;
              //   width: 2px;
              // }

              .text {
                // padding-left: 6px;
                // color: var(--text-kyy_color_text_3, #828DA5);
                // font-size: 12px;
                // font-style: normal;
                // font-weight: 400;
                // line-height: 20px;

                color: var(--text-kyy_color_text_2, #516082);

                /* kyy_fontSize_2/regular */
                font-family: "PingFang SC";
                font-size: 14px;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
                /* 166.667% */
              }
            }
          }

          .desc {
            &-row {
              display: flex;

              .label {
                color: var(--text-kyy_color_text_1, #1a2139);
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: 22px;
                /* 157.143% */
              }

              .value {
                color: var(--text-kyy_color_text_1, #1a2139);
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;

                .bold {
                  font-weight: 600;
                }
              }
            }
          }
        }

        .row {
          display: flex;
          flex-direction: column;
          background-color: #fff;
          padding: 16px 24px;
          border-radius: 8px;

          .title {
            position: relative;
            // width: auto;
            height: 22px;
            display: flex;
            align-items: flex-end;

            .text {
              color: var(--text-kyy_color_text_1, #1a2139);
              font-size: 14px;
              font-style: normal;
              font-weight: 600;
              line-height: 22px;
              /* 157.143% */
              z-index: 2;
              position: absolute;
            }

            .bottom {
              position: absolute;
              height: 10px;
              background: #82f8ff;
              left: 0;
              right: 0;
              bottom: 0;
              z-index: 1;
            }
          }

          .boxsell {
            margin-top: 16px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;

            &-item {
              display: flex;
              gap: 8px;
              align-items: flex-start;

              &-title {
                color: var(--text-kyy_color_text_3, #828da5);

                /* kyy_fontSize_2/regular */
                font-family: "PingFang SC";
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                /* 157.143% */
                width: 96px;
              }

              &-content {
                flex: 1;
                color: var(--text-kyy_color_text_1, #1a2139);

                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;

                /* 157.143% */
                .image {
                  border-radius: 8px;
                  width: 78px;
                  height: 78px;
                }
              }
            }

            .lock {
              width: 100%;
              height: 126px;

              display: flex;
              align-items: center;
              flex-direction: column;
              justify-content: center;

              &-img {
                width: 32px;
                height: 32px;
              }

              &-text {
                margin-top: 8px;
                font-size: 14px;
                color: #333333;
                display: flex;
                align-items: center;
                gap: 4px;

                &-title {
                  color: var(--text-kyy_color_text_2, #516082);
                  text-align: center;
                  font-size: 14px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 22px;
                  /* 157.143% */
                }
              }
            }
          }

          .columbox {

            .vcard {
              display: inline-block;

            }

            .col {
              border-radius: 8px;
              background: #eef6ff;
              padding: 12px;
              transition: all 0.15s linear;

              &:hover {
                transition: all 0.15s linear;
                background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
              }

              .name {
                display: flex;
                gap: 8px;

                .left {}

                .right {
                  display: flex;
                  flex-direction: column;

                  .tname {
                    color: var(--text-kyy_color_text_1, #1a2139);
                    /* kyy_fontSize_2/bold */
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 22px;
                    /* 157.143% */
                  }

                  .tlevel {
                    color: var(--text-kyy_color_text_3, #828da5);

                    /* kyy_fontSize_2/regular */
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px;
                    /* 157.143% */
                  }
                }
              }

              &-tem {
                display: flex;
                align-items: center;
                gap: 12px;

                .telicon {
                  color: #11bdb2;
                  font-size: 20px;
                }

                .emailicon {
                  color: #707eff;
                  font-size: 20px;
                }

                .tel {
                  color: var(--text-kyy_color_text_2, #516082);
                  font-size: 14px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 22px;
                  /* 157.143% */
                }
              }
            }
          }
        }
      }
    }
  }
}

.certified {
  color: var(--kyy_color_tag_text_success, #499d60);
  text-align: center;
  display: inline-block;

  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  /* 166.667% */
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_success, #e0f2e5);
  padding: 0 4px;
}

// .avatar {
//   width: 24px;
//   height: 24px;
//   border-radius: 5px;
//   margin-right: 8px;
// }</style>
