<template>
  <div class="w-full h-full overflow-hidden">
    <ApplyListWrapper v-if="isApplyWrapperVisible" @back="isApplyWrapperVisible = false" @change="loadData" />

    <div v-else class="pb-16 pl-16 pr-2">
      <RTable
        class="platform-activity-admin-list-table"
        :table="table"
        :filter="filter"
        @change="onTableChange"
      >
        <template #empty>
          <REmpty :tip="isSearchEmpty ? '暂无搜索结果' : '暂无数据'" :name="isSearchEmpty ? 'no-result' : 'no-data'" />
        </template>

        <template #toolbarContent>
          <div class="apply-list-button-box">
            <t-button
              class="w-88 h-32"
              theme="default"
              @click="isApplyWrapperVisible = true"
            >
              <span class="font-600">申请列表</span>
            </t-button>

            <div v-if="count > 0" class="count-num">{{ count }}</div>
          </div>
        </template>
      </RTable>

      <DeleteReasonDialog ref="deleteReasonDialogRef" @success="loadData" />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { computed, nextTick, onMounted, reactive, ref } from 'vue';
import { RTable, REllipsisTooltip, REmpty } from '@rk/unitPark';
import { debounce } from 'lodash';
import dayjs from 'dayjs';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import { getPlatform } from '@/utils/auth';
import DeleteReasonDialog from '@/views/activity/platform-activity-admin/components/DeleteReasonDialog.vue';
import { listDPActivities, stickDPActivity, unStickDPActivity } from '@/api/activity/platform';
import { useNavigate } from '@/views/activity/hooks/useNavigate';
import ApplyListWrapper from '@/views/activity/platform-activity-admin/components/ApplyListWrapper.vue';

defineProps({
  count: {
    type: Number,
    default: 0,
  },
});

const deleteReasonDialogRef = ref(null);

const isLoading = ref(true);

const isApplyWrapperVisible = ref(false);

const table = ref({
  attrs: {
    loading: isLoading,
    rowKey: 'id',
    onRowClick: ({ row }) => openActivityDetail(row),
  },
  columns: [{
    title: '活动',
    width: 456,
    cell: (h, { row }) => <div class="flex gap-12">
        <img class="w-72 h-72 rounded-8" src={row.assetUrl} alt=""/>
      <div class="flex flex-col gap-4">
        <REllipsisTooltip line-number={1} text={row.subject} />
        <div class="flex items-center gap-8 text-[#828DA5]">
          <iconpark-icon className="text-20" name="icondate"></iconpark-icon>
          <span>{dayjs(row.durationStart * 1000).format('MM-DD HH:mm')} ~ {dayjs(row.durationEnd * 1000).format('MM-DD HH:mm')}</span>
        </div>
        <div class="flex items-center gap-8 text-[#828DA5]">
          <iconpark-icon className="text-20" name="iconpositioning"></iconpark-icon>
          <REllipsisTooltip line-number={1} text={row.address} />
        </div>
      </div>
    </div>,
  }, {
    title: '推广人',
    width: 198,
    cell: (h, { row }) => <div class="flex items-center gap-8">
      <div class="px-4 rounded-4 bg-[#E0F2E5] text-[#499D60] text-12 leading-20">个人</div>
      <div>{row.promoterName}</div>
    </div>,
  }, {
    title: '推广时间',
    width: 162,
    cell: (h, { row }) => <div>{dayjs(row.passedAt * 1000).format('YYYY-MM-DD HH:mm')}</div>,
  }, {
    title: '操作',
    width: 128,
    cell: (h, { row }) => <div class="flex items-center gap-8">
                  <div class="cursor-pointer text-[#4D5EFF] hover:bg-[#EAECFF] rounded-4 p-4 inline-block" onClick={(e) => handleStick(e, row)}>
                    {row.stickedAt === '0' ? '置顶' : '取消置顶'}
                  </div>
                  <div class="cursor-pointer text-[#D54941] hover:bg-[#FDF5F6] rounded-4 p-4 inline-block" onClick={(e) => handleDelete(e, row)}>
                    删除
                  </div>
                </div>,
  }],
  list: [],
  pagination: {
    pageSize: 10,
    current: 1,
    total: 0,
  },
});

const filter = computed(() => ({
  attrs: {
    size: 'small',
    labelWidth: '80px',
    placeholder: '搜索活动主题',
  },
  advanced: {
    form: {
      list: [
        {
          name: 'promoterName',
          label: '推广人',
          type: 'input',
          value: '',
          defaultValue: '',
        },
        {
          name: 'promotionTime',
          label: '推广时间',
          type: 'dateRange',
          value: [],
          defaultValue: [],
          attrs: {
            clearable: true,
            placeholder: ['开始时间', '结束时间'],
            options: [],
          },
        },
      ],
    },
  },
}));

const filterParams = reactive({
  keyword: null,
  promoterName: null,
  promotionTimeStart: null,
  promotionTimeEnd: null,
});

const isSearchEmpty = computed(() => (
  table.value.list.length < 1
    && ((filterParams.keyword !== '' && filterParams.keyword !== null)
    || (filterParams.promoterName !== '' && filterParams.promoterName !== null)
      || filterParams.promotionTimeStart
      || filterParams.promotionTimeEnd)
));

const teamId = localStorage.getItem('honorteamid');

const onTableChange = debounce(async ({ filter, pageInfo }, name) => {
  filterParams.keyword = filter?.searchVal ?? null;

  filterParams.promoterName = filter?.promoterName;

  const startTime = filter?.promotionTime?.[0];
  const endTime = filter?.promotionTime?.[1];

  filterParams.promotionTimeStart = startTime ? dayjs(startTime).unix().toString() : null;
  filterParams.promotionTimeEnd = endTime ? dayjs(endTime).endOf('day').unix().toString() : null;

  if (name === 'table') {
    table.value.pagination.current = pageInfo.current;
    table.value.pagination.pageSize = pageInfo.pageSize;
    table.value.pagination.total = pageInfo.total;
  } else {
    table.value.pagination.current = 1;
    table.value.pagination.pageSize = 10;
  }

  await nextTick();

  // 动态计算表格高度
  const filterResultEl = document.querySelector('.filter-result');
  const tableEl = document.querySelector('.RK-Table');

  if (filterResultEl) {
    const filterResultHeight = filterResultEl.clientHeight;
    tableEl.style.height = `calc(100vh - 16px - ${filterResultHeight}px - 116px)`;
  } else {
    tableEl.style.height = 'calc(100vh - 116px)';
  }

  loadData();
}, 200);

const loadData = async () => {
  isLoading.value = true;

  const [error, res] = await to(listDPActivities({
    'pagination.size': table.value.pagination.pageSize,
    'pagination.number': table.value.pagination.current,
    teamId,
    fromPage: 1,
    status: 'PLATFORM_STATUS_AGREED',
    ...filterParams,
  }));

  if (error) {
    isLoading.value = false;
    return;
  }

  isLoading.value = false;

  table.value.list = res.data.data.activities;

  table.value.pagination.total = res.data.data.pagination.total;
};

// 置顶
const handleStick = async (event, row) => {
  event.stopPropagation();

  if (row.stickedAt !== '0') {
    const confirmDia = DialogPlugin.confirm({
      header: '提示',
      theme: 'info',
      body: '确定取消该活动置顶',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        await unStickDPActivity({
          activityId: row.id,
          teamId,
        });

        MessagePlugin.success('取消置顶成功');

        loadData();

        confirmDia.destroy();
      },
      onCancel: () => {
        confirmDia.destroy();
      },
      onCloseBtnClick: () => {
        confirmDia.destroy();
      },
    });
  } else {
    await stickDPActivity({
      activityId: row.id,
      teamId,
    });

    MessagePlugin.success('置顶成功');

    loadData();
  }
};

// 删除
const handleDelete = (event, row) => {
  event.stopPropagation();

  deleteReasonDialogRef.value?.open(row.id);
};

const { openParticipantDetailTab } = useNavigate();

// 打开活动详情tab
const openActivityDetail = (row) => {
  // 使用平台身份进入
  const platformCardId = getPlatform().find((item) => item.teamId === teamId)?.uuid;

  openParticipantDetailTab({
    activity: row,
    type: 'WORKBENCH',
    teamId,
    cardId: platformCardId,
  });
};

onMounted(() => {
  loadData();
});
</script>

<style lang="less" scoped>
:deep(.platform-activity-admin-list-table){
  .header {
    padding-right: 14px;
  }

  .RK-Table {
    height: calc(100vh - 116px);
    overflow: overlay;
    padding-right: 14px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }

  .t-table {
    .t-table__body tr:not(.t-table__empty-row) {
      &:hover {
        background-color: #f3f6fa !important;
        cursor: pointer;
      }
    }

    td:last-child {
      padding: 8px !important;
    }
  }

  .t-table__pagination{
    padding-bottom: 0 !important;
  }
}

.apply-list-button-box{
  position: relative;

  .count-num{
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(50%, -50%);
    width: 16px;
    height: 16px;
    line-height: 16px;
    color: #fff;
    font-weight: 500;
    font-size: 12px;
    text-align: center;
    background: #FF4AA1;
    border-radius: 50%;
  }
}
</style>
