<template>
  <div class="chat-message">
    <!-- 消息卡片 -->
    <div class="message-card">
      <!-- 消息头部 -->
      <AppCardHeader style="font-size: 16px">
        {{ getTitle(extendData?.digital_platform?.digital_uuid) }}
      </AppCardHeader>

      <!-- 消息内容 -->
      <div class="message-content">
        <!-- 用户信息 -->
        <UserInfo
          :avatar="extendData?.digital_platform?.avatar || ''"
          :company-name="extendData?.digital_platform?.name || ''"
          :apply-text="`个人会员【${extendData?.promoter?.name}】已取消活动推广`"
        />

        <!-- 活动信息 -->
        <ActivityCard
          section-title="推广"
          :thumbnail-url="extendData?.asset_url || ''"
          :title="extendData?.subject || ''"
          :time="time"
          :location="extendData?.address || ''"
        />

        <!-- 申请人信息 -->
        <SectionItem
          title="取消理由"
          :value="extendData?.cancel_reason || ''"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { AppCardHeader } from '@renderer/views/message/chat/MessageAppCard';
import { getTitle } from '@renderer/views/message/chat/msgTypeContent/AppWorks/constant';
import UserInfo from '../../../components/activity-promote/components/UserInfo.vue';
import ActivityCard from '../../../components/activity-promote/components/ActivityCard.vue';
import SectionItem from '../../../components/activity-promote/components/SectionItem.vue';
import { useSceneData } from '../../../components/activity-promote/composables/useSceneData';

const props = defineProps({
  msg: { type: Object as PropType<any>, default: null },
});

const { extendData, time } = useSceneData(props);
</script>

<style scoped>
.chat-message {
  width: 360px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-card {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.message-content {
  display: flex;
  flex-direction: column;
  padding: 16px 16px;
  gap: 12px;
}

.divider {
  height: 1px;
  background: #eceff5;
}
</style>
