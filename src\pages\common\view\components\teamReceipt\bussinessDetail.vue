<template>
  <t-drawer
    v-model:visible="visible"
    :show-overlay="true"
    destroy-on-close
    size="472px"
    class="bussiness-drawer-body"
    :footer="btnArr.length === 0 ? false : true"
    @close="orclose"
  >
    <template #header>
      <div class="order-drawer-header">
        <div>{{ $t('order.orderD') }}</div>
        <img style="width: 24px; cursor: pointer; height: 24px" src="@assets/<EMAIL>" @click="orclose" />
      </div>
    </template>

    <template #body>
      <div class="bc-grey" style="margin-top: -4px">
        <div class="bac-big-pic"></div>
        <orderStatusStep v-if="orderStatusStepFlag" :order-details="orderDetails" />
        <div style="margin-top: 12px">
          <!-- 退款信息 -->
          <div
            v-if="orderDetails?.pay_at && orderDetails?.refund_status && orderDetails?.refund_status !== 0"
            class="content-box"
          >
            <div class="lin head-item-lab">{{ $t('order.tkxx') }}</div>
            <div class="form-box">
              <div class="form-flex" style="width: 100%">
                <div class="laber-item" style="color: #1a2139; font-size: 14px; font-weight: 600">
                  {{ t('order.tkzt') }}
                </div>
                <div class="value-item" style="color: #1a2139; font-size: 14px; font-weight: 600">
                  {{
                    orderDetails.refund_status === 1
                      ? t('order.tkz')
                      : orderDetails.refund_status === 2
                        ? t('order.bftk')
                        : orderDetails.refund_status === 3
                          ? t('order.ytk')
                          : ''
                  }}
                </div>
                <div style="color: #4d5eff; display: flex; cursor: pointer" @click="goRefundDetail(orderDetails)">
                  {{ t('order.tkjl') }}
                  <iconPark-icon name="iconarrowright" style="color: #4d5eff; font-size: 20px" />
                </div>
              </div>
              <div class="tui-kuan-price">用户已申请退款，退款金额为¥{{ orderDetails.refund_amount }}</div>
            </div>
          </div>
          <!-- 订单信息 -->
          <div class="content-box">
            <div class="lin head-item-lab" style="display: flex; align-items: center; justify-content: space-between">
              <span>{{ t('order.orderlistinfo') }}</span>
              <div class="order-record" @click="openOrderRecord(row)">
                <span>订单记录</span>
                <!-- <iconpark-icon

                  name="iconright"
                ></iconpark-icon> -->
                <iconpark-icon
                  style="padding-left: 4px; font-size: 20px; color: #4d5eff"
                  name="iconarrowright"
                ></iconpark-icon>
              </div>
            </div>
            <div class="form-box">
              <!-- 订单编号 -->
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">{{ t('order.orderNo') }}</div>
                <div class="value-item">{{ orderDetails?.sn || '--' }}</div>
              </div>
              <!-- 订单状态 -->
              <div class="form-flex mt12" style="width: 100%">
                <div class="laber-item">{{ t('order.orderstats') }}</div>
                <div class="value-item">
                  <div
                    class="transaction-status"
                    :style="{
                      background: orderStatusMap.find((item) => item.value === orderDetails?.status)?.bgc,
                      color: orderStatusMap.find((item) => item.value === orderDetails?.status)?.color,
                    }"
                  >
                    {{ orderStatusMap.find((item) => item.value === orderDetails?.status)?.text }}
                  </div>
                </div>
              </div>
              <!-- 收款来源 -->
              <div class="form-flex mt12" style="width: 100%">
                <div class="laber-item">{{ t('order.skly') }}</div>
                <div class="value-item">
                  <span>{{ orderDetails?.origin === 'store_product' ? '店铺订单' : '活动收款' }}</span>
                </div>
              </div>
              <!-- 下单时间 -->
              <div class="form-flex mt12" style="width: 100%">
                <div class="laber-item">{{ t('order.orderTime') }}</div>
                <div class="value-item">
                  {{ orderDetails?.created_at ? getTime(new Date(orderDetails?.created_at * 1000)) : '--' }}
                </div>
              </div>
            </div>
          </div>
          <!-- 购买信息 -->
          <div class="content-box">
            <div class="lin head-item-lab">{{ t('order.by') }}信息</div>
            <div class="form-box">
              <!-- 购买者 -->
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">{{ t('order.byer') }}</div>
                <div v-if="orderDetails?.buyer_name" class="value-item">
                  {{ orderDetails?.buyer_name }}
                </div>
                <div v-else>--</div>
              </div>
              <!-- 购买主体 -->
              <div class="form-flex" style="width: 100%; margin-top: 12px">
                <div class="laber-item">{{ t('order.gmzt') }}</div>
                <div v-if="orderDetails?.buyer_team_name" class="value-item">
                  {{ orderDetails?.buyer_team_name }}
                </div>
                <div v-else>--</div>
              </div>
              <!-- 收款方 -->
              <div class="form-flex" style="width: 100%; margin-top: 12px">
                <div class="laber-item">收款方</div>
                <div v-if="orderDetails?.pay_info" class="value-item">
                  {{ orderDetails?.pay_info.payee ? orderDetails?.pay_info.payee : '--' }}
                </div>
                <div v-else>--</div>
              </div>
              <!-- 支付方式 -->
              <div v-if="orderDetails?.pay_at" class="form-flex" style="width: 100%; margin-top: 12px">
                <div class="laber-item">{{ t('order.pay_type') }}</div>
                <div v-if="orderDetails?.pay_info" class="value-item">
                  {{ orderDetails?.pay_info.mode ? orderDetails?.pay_info.mode : '--' }}({{
                    orderDetails?.pay_info.channel === 1 ? '微信支付' : '支付宝支付'
                  }})
                </div>
                <div v-else>--</div>
              </div>
              <!-- 发票状态 -->
              <div class="form-flex" style="width: 100%; margin-top: 12px">
                <div class="laber-item">{{ t('order.invoiceStatus') }}</div>
                <div class="value-item">
                  {{
                    orderDetails?.invoice_status === 0
                      ? t('order.notInvoiced')
                      : orderDetails?.invoice_status === 1
                        ? '开票中'
                        : orderDetails?.invoice_status === 2
                          ? '已拒绝'
                          : '已开票'
                  }}
                </div>
              </div>
            </div>
          </div>
          <!-- {{orderDetails?.order_mode}}orderDetails?.order_modeorderDetails?.order_mode -->
          <div v-if="orderDetails?.order_mode === 3 && orderDetails?.logistic_address" class="content-box">
            <!--  -->

            <div class="lin head-item-lab">收货信息</div>
            <div class="form-box" style="gap: 12px">
              <!-- 购买者 -->
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">收货地址</div>
                <div
                  v-if="orderDetails?.logistic_address?.address"
                  class="value-item remark-warp100"
                  style="display: flex; width: calc(100% - 70px)"
                >
                  <div
                    ref="addressText"
                    class="remark-warp-text"
                    :class="{ 'remark-expanded': addressExpanded }"
                    @click="toggleAddressExpand"
                  >
                    {{ orderDetails?.logistic_address?.address?.location?.address }}
                    {{ orderDetails?.logistic_address?.address?.location?.name }}
                  </div>
                  <iconpark-icon
                    v-if="
                      orderDetails?.logistic_address?.address?.location?.address &&
                      orderDetails?.logistic_address?.address?.location?.address.length > 15
                    "
                    style="width: 20px; height: 20px; font-size: 20px; color: #828da5; cursor: pointer"
                    :name="addressExpanded ? 'iconarrowup-a960jjb9' : 'iconarrowdown'"
                    @click="toggleAddressExpand"
                  ></iconpark-icon>
                </div>
                <div v-else>--</div>
              </div>
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">收货人</div>
                <div v-if="orderDetails?.logistic_address?.userName" class="value-item">
                  {{ orderDetails?.logistic_address?.userName }}
                </div>
                <div v-else>--</div>
              </div>
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">手机号</div>
                <div v-if="orderDetails?.logistic_address?.phone" class="value-item">
                  + {{ orderDetails?.logistic_address?.phone?.countryCode }}
                  {{ orderDetails?.logistic_address?.phone?.number }}
                </div>
                <div v-else>--</div>
              </div>
              <div class="form-flex" style="width: 100%">
                <div class="laber-item" style="flex: none">备注</div>
                <div
                  v-if="orderDetails?.remark"
                  class="value-item"
                  style="display: flex; align-items: center; height: auto"
                  :class="{
                    'remark-warp100': orderDetails?.status === 1,
                    'remark-warp': remarkExpanded && orderDetails?.status === 1,
                  }"
                >
                  <div
                    v-if="orderDetails?.status !== 1"
                    ref="remarkTextSecond"
                    class="remark-warp-text"
                    :class="{ 'remark-expanded': remarkExpanded }"
                    @click="toggleRemarkExpand"
                  >
                    {{ orderDetails?.remark }}
                  </div>
                  <div v-else style="white-space: break-spaces">
                    {{ orderDetails?.remark }}
                  </div>

                  <div v-if="orderDetails?.status === 1" class="add-btn" @click="openRemark()">添加</div>
                  <iconpark-icon
                    v-if="orderDetails?.remark.length > 15 && orderDetails?.status !== 1"
                    style="width: 20px; height: 20px; font-size: 20px; color: #828da5; cursor: pointer"
                    :name="remarkExpanded ? 'iconarrowup-a960jjb9' : 'iconarrowdown'"
                    @click="toggleRemarkExpand"
                  ></iconpark-icon>
                </div>
                <div v-else class="value-item">无</div>
              </div>
            </div>
          </div>
          <div v-if="orderDetails?.order_mode === 1 && orderDetails?.delivery_address" class="content-box">
            <!-- thxx去掉地图背景 -->
            <div v-if="orderDetails?.status === 1" class="add-btn" @click="openRemark()">添加</div>
            <div class="lin head-item-lab">配送信息</div>
            <div class="form-box" style="gap: 12px">
              <!-- 购买者 -->
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">配送地址</div>
                <div
                  v-if="orderDetails?.delivery_address?.address"
                  class="value-item"
                  style="display: flex; height: auto"
                  :class="{
                    'remark-warp100': orderDetails?.status === 1,
                    'remark-warp': deliveryAddressExpanded && orderDetails?.status === 1,
                  }"
                >
                  <div
                    ref="deliveryAddressText"
                    class="remark-warp-text"
                    :class="{ 'remark-expanded': deliveryAddressExpanded }"
                    @click="toggleDeliveryAddressExpand"
                  >
                    {{
                      orderDetails?.delivery_address?.address?.location?.address +
                      orderDetails?.delivery_address?.address?.houseNumber
                    }}
                  </div>
                  <iconpark-icon
                    v-if="
                      orderDetails?.delivery_address?.address?.location?.address &&
                      orderDetails?.delivery_address?.address?.location?.address.length > 15
                    "
                    style="width: 20px; height: 20px; font-size: 20px; color: #828da5; cursor: pointer"
                    :name="deliveryAddressExpanded ? 'iconarrowup-a960jjb9' : 'iconarrowdown'"
                    @click="toggleDeliveryAddressExpand"
                  ></iconpark-icon>
                </div>
                <div v-else>--</div>
              </div>
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">收货人</div>
                <div v-if="orderDetails?.delivery_address?.userName" class="value-item">
                  {{ orderDetails?.delivery_address?.userName }}
                </div>
                <div v-else>--</div>
              </div>
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">手机号</div>
                <div v-if="orderDetails?.delivery_address?.phone" class="value-item">
                  +{{ orderDetails?.delivery_address?.phone?.countryCode }}
                  {{ orderDetails?.delivery_address?.phone?.number }}
                </div>
                <div v-else>--</div>
              </div>
              <div class="form-flex" style="width: 100%">
                <div class="laber-item" style="flex: none">备注</div>
                <div
                  v-if="orderDetails?.remark"
                  class="value-item"
                  style="display: flex; align-items: center; height: auto"
                  :class="{
                    'remark-warp100': orderDetails?.status === 1,
                    'remark-warp': remarkExpanded && orderDetails?.status === 1,
                  }"
                >
                  <div
                    v-if="orderDetails?.status !== 1"
                    ref="remarkTextSecond"
                    class="remark-warp-text"
                    :class="{ 'remark-expanded': remarkExpanded }"
                    @click="toggleRemarkExpand"
                  >
                    {{ orderDetails?.remark }}
                  </div>
                  <div v-else style="white-space: break-spaces">
                    {{ orderDetails?.remark }}
                  </div>
                  <div v-if="orderDetails?.status === 1" class="add-btn" @click="openRemark()">添加</div>
                  <iconpark-icon
                    v-if="orderDetails?.remark.length > 15 && orderDetails?.status !== 1"
                    style="width: 20px; height: 20px; font-size: 20px; color: #828da5; cursor: pointer"
                    :name="remarkExpanded ? 'iconarrowup-a960jjb9' : 'iconarrowdown'"
                    @click="toggleRemarkExpand"
                  ></iconpark-icon>
                </div>
                <div v-else class="value-item">无</div>
              </div>
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">期望送达</div>
                <div class="value-item">立即配送</div>
              </div>
            </div>
          </div>
          <!--  -->
          <div v-if="orderDetails?.order_mode === 2 && orderDetails?.pickup_address" class="content-box thxx">
            <div class="shop-logo" @click="openAddress()">
              <img :src="orderDetails?.pickup_address?.store_logo" />
            </div>

            <div v-if="orderDetails?.status === 1" class="add-btn" @click="openRemark()">添加</div>
            <div class="lin head-item-lab">提货信息</div>
            <div class="form-box" style="gap: 12px">
              <!-- 购买者 -->
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">自提地址</div>
                <div
                  v-if="orderDetails?.pickup_address?.address"
                  class="value-item"
                  style="display: flex"
                  :class="{
                    'remark-warp100': orderDetails?.status === 1,
                    'remark-warp': pickupAddressExpanded,
                  }"
                >
                  <div
                    ref="pickupAddressText"
                    class="remark-warp-text"
                    :class="{ 'remark-expanded': pickupAddressExpanded }"
                    @click="togglePickupAddressExpand"
                  >
                    {{ orderDetails?.pickup_address?.address }}
                  </div>
                  <iconpark-icon
                    v-if="orderDetails?.pickup_address?.address && orderDetails?.pickup_address?.address.length > 15"
                    style="width: 20px; height: 20px; font-size: 20px; color: #828da5; cursor: pointer"
                    :name="pickupAddressExpanded ? 'iconarrowup-a960jjb9' : 'iconarrowdown'"
                    @click="togglePickupAddressExpand"
                  ></iconpark-icon>
                </div>
                <div v-else>--</div>
              </div>
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">自提时间</div>
                <div v-if="orderDetails?.pickup_address?.pickup_at" class="value-item">
                  {{ dayjs(orderDetails?.pickup_address?.pickup_at * 1000).format('YYYY-MM-DD HH:mm:ss') }}
                </div>
                <div v-else>--</div>
              </div>
              <div class="form-flex" style="width: 100%">
                <div class="laber-item">手机号</div>
                <div v-if="orderDetails?.pickup_address?.phone" class="value-item">
                  {{ orderDetails?.pickup_address?.phone }}
                </div>
                <div v-else>--</div>
              </div>
              <div class="form-flex" style="width: 100%">
                <div class="laber-item" style="flex: none">备注</div>
                <div
                  v-if="orderDetails?.remark"
                  class="value-item"
                  style="display: flex; align-items: center; height: auto"
                  :class="{
                    'remark-warp100': orderDetails?.status === 1,
                    'remark-warp': remarkExpanded && orderDetails?.status === 1,
                  }"
                >
                  <div
                    v-if="orderDetails?.status !== 1"
                    ref="remarkTextSecond"
                    class="remark-warp-text"
                    :class="{ 'remark-expanded': remarkExpanded }"
                    @click="toggleRemarkExpand"
                  >
                    {{ orderDetails?.remark }}
                  </div>
                  <div v-else style="white-space: break-spaces">
                    {{ orderDetails?.remark }}
                  </div>

                  <div v-if="orderDetails?.status === 1" class="add-btn" @click="openRemark()">添加</div>
                  <iconpark-icon
                    v-if="orderDetails?.remark.length > 15 && orderDetails?.status !== 1"
                    style="width: 20px; height: 20px; font-size: 20px; color: #828da5; cursor: pointer"
                    :name="remarkExpanded ? 'iconarrowup-a960jjb9' : 'iconarrowdown'"
                    @click="toggleRemarkExpand"
                  ></iconpark-icon>
                </div>
                <div v-else class="value-item">无</div>
              </div>
            </div>
          </div>
          <!-- 商品信息 -->
          <div class="commodity-mes">
            <div class="lin head-item-lab">
              <t-tooltip :content="orderDetails?.snapshot?.subtitle">
                {{ orderDetails?.snapshot?.subtitle }}
              </t-tooltip>
            </div>
            <div :style="{ height: 'auto' }">
              <div
                v-for="(item, key) in showAll ? orderDetails?.goods : orderDetails?.goods?.slice(0, 1)"
                :key="key"
                class="commodity-list"
                :style="{
                  paddingBottom:
                    (orderDetails?.goods.length > 1 && key === 0) || (key === orderDetails?.goods.length - 1 && showAll)
                      ? '12px'
                      : '32px',
                }"
              >
                <img :src="item.goods_logo" alt="" class="commodity-img" />
                <div style="width: 100%">
                  <div class="commodity-name">
                    <span ref="withVal" :class="item.showGnAll ? 'commodity-name-span2' : 'commodity-name-span'">
                      {{ item.goods_name }}
                    </span>
                    <iconPark-icon
                      v-if="item.goods_name?.length > 15"
                      :name="item.showGnAll ? 'iconarrowup-a960jjb9' : 'iconarrowdown'"
                      style="color: #828da5; cursor: pointer; font-size: 20px"
                      @click="item.showGnAll = !item.showGnAll"
                    />
                  </div>

                  <div class="commodity-name">
                    <span :class="item.showSeAll ? 'commodity-name-span2 time' : 'commodity-name-span time'">
                      {{ item.sku_name }}
                    </span>
                    <iconPark-icon
                      v-if="item.sku_name?.length > 15"
                      :name="item.showSeAll ? 'iconarrowup-a960jjb9' : 'iconarrowdown'"
                      style="color: #828da5; cursor: pointer; font-size: 20px"
                      @click="item.showSeAll = !item.showSeAll"
                    />
                    <span v-if="item.refund_status === 1 || item.refund_status === 3" class="tui-icon">
                      {{ item.refund_status === 1 ? '退款中' : '退款成功' }}
                    </span>
                  </div>
                  <div class="commodity-price">
                    <span>
                      <span class="preferential-price">¥{{ item.price }}</span>
                      <span class="original-price">¥{{ item.origin_price }}</span>
                    </span>
                    <span style="color: #828da5">×{{ item.num }}</span>
                  </div>
                </div>
                <!-- <div v-if="item.goods_detail" class="commodity-right" @click="goGoodsDetail(item)"> -->
              </div>
              <!-- {{orderDetails?.goods.length}} -->
              <div
                v-if="orderDetails?.goods?.length > 1 && !showAll"
                style="
                  width: 100%;
                  text-align: center;
                  display: flex;
                  justify-content: center;
                  margin-left: 0;
                  margin-top: 12px;
                "
                class="commodity-see-detail"
                @click="showAll = true"
              >
                查看明细
                <iconPark-icon name="iconarrowdwon" style="color: #828da5; font-size: 20px" />
              </div>

              <div
                v-if="orderDetails?.goods?.length > 1 && showAll"
                class="commodity-see-detail"
                style="
                  width: 100%;
                  text-align: center;
                  display: flex;
                  justify-content: center;
                  margin-left: 0;
                  margin-top: 12px;
                "
                @click="showAll = false"
              >
                点击收起
                <iconPark-icon name="iconarrowup" style="color: #828da5; font-size: 20px" />
              </div>
            </div>
            <div class="foot-box">
              <div class="flex-a-end" style="margin-bottom: 8px">
                <div class="foot-lab">{{ t('order.Totalpriceofgoods') }}:</div>
                <div class="foot-val">
                  {{ orderDetails?.currency === 'CNY' ? '￥' : 'MOP' }}
                  {{ addCommasToNumber(orderDetails?.amount ? orderDetails?.amount + '' : '0') }}
                </div>
              </div>

              <!-- <div v-if="orderDetails?.goods[0]?.origin_price" class="flex-a-end" style="margin-bottom: 8px">
                <div class="foot-lab">原价:</div>
                <div class="foot-val">
                  {{ orderDetails.currency === 'CNY' ? '￥' : 'MOP' }}
                  {{ addCommasToNumber(orderDetails?.goods[0]?.origin_price) }}
                </div>
              </div> -->
              <div v-if="orderDetails?.order_mode === 1" class="flex-a-end" style="margin-bottom: 8px">
                <div class="foot-lab">用户配送费:</div>
                <div v-if="orderDetails?.delivery_fee" class="foot-val">
                  {{ orderDetails.currency === 'CNY' ? '￥' : 'MOP' }}
                  {{ addCommasToNumber(orderDetails?.delivery_fee + '') }}
                </div>
                <div v-else class="foot-val">免配送费</div>
              </div>
              <div v-if="orderDetails?.order_mode === 3" class="flex-a-end" style="margin-bottom: 8px">
                <div class="foot-lab">快递费:</div>
                <div v-if="orderDetails?.logistic_fee" class="foot-val">
                  {{ orderDetails.currency === 'CNY' ? '￥' : 'MOP' }}
                  {{ addCommasToNumber(orderDetails?.logistic_fee + '') }}
                </div>
                <div v-else class="foot-val">免快递费</div>
              </div>
              <div v-if="orderDetails?.discount" class="flex-a-end" style="margin-bottom: 8px">
                <div class="foot-lab">{{ t('order.Preferentialreduction') }}:</div>
                <div class="foot-val">
                  {{ orderDetails.currency === 'CNY' ? '￥' : 'MOP' }}
                  {{ addCommasToNumber(orderDetails?.discount ? orderDetails?.discount + '' : '0') }}
                </div>
              </div>
              <div class="flex-a-end">
                <div class="foot-lab">{{ orderDetails?.pay_at ? t('order.Disbursements') : '需付款' }}:</div>
                <div class="foot-val" style="color: #d54941; font-size: 16px; font-weight: 600">
                  {{ orderDetails?.currency === 'CNY' ? '￥' : 'MOP' }}
                  {{ addCommasToNumber(orderDetails?.pay_amount ? orderDetails?.pay_amount.toFixed(2) : '0') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="foot-btn-box">
        <div v-if="orderDetails?.status === 1 && orderDetails?.expiredAt !== 0 && countdown !== '00:00:00'">
          <span style="color: #828da5">{{ t('order.residue') }}付款</span>
          <span class="red-time">
            {{ countdown }}
          </span>
        </div>
        <div v-else class="zhan-wei-zhi"></div>
        <div style="display: flex; gap: 8px">
          <!-- 取消订单 -->
          <t-button
            v-if="orderDetails?.status === 1"
            v-permission="'value_added_order'"
            theme="default"
            variant="outline"
            @click="closeOrderWay(orderDetails)"
          >
            {{ t('order.cancellationoforder') }}
          </t-button>
          <!-- 支付 -->
          <t-button v-if="orderDetails?.status === 1" class="minw83" @click="prompts(orderDetails)">立即支付</t-button>
          <!-- 申请退款 -->
          <t-button
            v-if="applyRefund(orderDetails)"
            theme="default"
            variant="outline"
            class="minw83"
            @click="sqtk(orderDetails)"
          >
            {{ t('order.sqtk') }}
          </t-button>
          <t-button
            v-if="viewps(orderDetails)"
            theme="default"
            variant="outline"
            class="minw83"
            @click="viewpsFn(orderDetails)"
          >
            查看配送
          </t-button>
          <t-button
            v-if="viewLogisticsBtn(orderDetails)"
            theme="default"
            variant="outline"
            class="minw83"
            @click="viewLogistics(orderDetails)"
          >
            查看物流
          </t-button>
          <t-button v-if="confirmReceiptBtn(orderDetails)" class="minw83" @click="confirmReceipt(orderDetails)">
            确认收货
          </t-button>
          <t-button v-if="evaluate(orderDetails)" class="minw83" @click="openEvaluate(orderDetails)">评价</t-button>
          <t-button v-if="getPickupCode(orderDetails)" class="minw83" @click="pickUp(orderDetails)">取货码</t-button>

          <!-- 关闭原因 -->
          <t-button
            v-if="orderDetails?.status === 0"
            v-permission="'value_added_order'"
            theme="default"
            variant="outline"
            class="minw83"
            @click="shutdownReason(orderDetails?.cancel_reason)"
          >
            {{ t('order.close_reason') }}
          </t-button>
          <!-- <t-button
						v-if="
							payInfo && payInfo?.publicPayInfo && payInfo?.publicPayInfo.status && payInfo?.publicPayInfo.status !== 1
						"
						class="minw83"
						@click="updCode()"
						>{{ t('order.jjyy') }}</t-button
					> -->
        </div>
      </div>
    </template>
  </t-drawer>
  <close-order ref="closeOrderModal" @update="paymentCallback" />
  <orderRecord ref="orderRecordVisible"></orderRecord>
  <!-- <detail-modal ref="detailModalRef" @detail-modal-callback="paymentCallback" /> -->
  <public-payment ref="paymentDialogRef" @payment-callback="paymentCallback" />
  <refundList ref="refundListRef" :team-id="props.teamId" @refund-list-callback="paymentCallback" />
  <rebackModal
    ref="rebackModalRef"
    :team-id="props.teamId"
    @get-bussiness-list="orclose"
    @reback-modal-callback="paymentCallback"
  />
  <RouteMap ref="RouteMapRef" :hasfooter="false" :is-customer="true" :type="deliveryOrmerchantFlag"></RouteMap>

  <addRemark ref="remarkWin" :order-id="orderDetails.id" @success="paymentCallback" />
  <EvaluationMg ref="evaluationMgRef" :is-show-fotter="true" @get-list="orclose"></EvaluationMg>
  <!-- 物流信息 -->
  <logisticsLog ref="LogisticsInfoRef" :show-tel-flag="true" />
</template>
<script setup lang="ts">
import { applyRefund } from '@pages/shop/view/workbench/orders/orderStatusHelpers.ts';
import { ref, reactive, watch, onMounted, onBeforeMount, onBeforeUnmount, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import RouteMap from '@pages/shop/view/workbench/orders/components/deliveryRouteDetails/index.vue';
import sdk from '@lynker-desktop/web';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import orderStatusStep from '../orderStatusStep.vue';
import { PickupCode } from '@components/pickup-code';
import publicPayment from '../publicPayment/index.vue';
import addRemark from './addRemark.vue';
import { bussinessOrderDetail, orderrefundDetail } from '../../../api';
import { getTime } from '../../../utils';
import dayjs from 'dayjs';
import closeOrder from './closeOrder.vue';
// import detailModal from './detailModal.vue';
import refundList from './refundList.vue';
import rebackModal from './rebackModal.vue';
import orderRecord from '@pages/common/view/organize_payments/components/modals/OrderRecord.vue';
import {
  orderStatusMap,
  evaluate,
  confirmReceiptBtn,
  viewLogisticsBtn,
  getPickupCode,
  viewps,
} from '@pages/shop/view/workbench/orders/orderStatusHelpers.ts';
import { finishOrder } from '@pages/shop/view/workbench/orders/apis';

import EvaluationMg from '@pages/shop/view/comment-management/components/EvaluationMg.vue';
import logisticsLog from '@pages/shop/view/workbench/orders/components/logistics/logisticsLog.vue';
const props = defineProps({
  teamId: {
    type: String,
    default: '',
  },
});
const { t } = useI18n();
const visible = ref(false);
const remarkWin = ref(false);
const orderDetails = ref({});
const refundInfo = reactive({});
const countdown = ref('00:00:00');
const paymentDialogRef = ref(null);
// 查看物流
const LogisticsInfoRef = ref(null);
// 取货码
const pickUp = (row) => {
  console.log(row, 'rowwwwwwwwwwww');

  PickupCode(row.pickup_sn);
};
const RouteMapRef = ref(null);
const deliveryOrmerchantFlag = ref('delivery');
const viewpsFn = (row) => {
  console.log(row, 'rowwwwwww');

  deliveryOrmerchantFlag.value = row.delivery_type === 0 ? 'merchant' : 'delivery';
  nextTick(() => {
    if (deliveryOrmerchantFlag.value === 'delivery') {
      RouteMapRef.value?.openWin({
        serialNumber: row.delivery_info.delivery_order_sn,
        orderId: row.sn,
      });
    } else {
      RouteMapRef.value?.openWin({
        orderId: row.sn,
      });
    }
  });
};
// 确认收货
const confirmReceipt = (row) => {
  const confirmDia = DialogPlugin({
    header: '提示',
    theme: 'info',
    body: `订单还在"${orderStatusMap.find((item) => item.value === row.status)?.text}订单状态"中，请确认商品收到后再点击，是否确认完成订单？`,
    confirmBtn: '确定',
    cancelBtn: '取消',
    onConfirm: () => {
      finishOrder({ id: row.id }, props.teamId)
        .then((res) => {
          console.log(res, '啊啊啊大大是的');

          if (res.code === 0) {
            emits('getBussinessList');
            getOrderDetails(valData.value);
            MessagePlugin.success('订单已确认完成');
          } else {
            MessagePlugin.error(res.message);
          }
        })
        .catch((err) => {
          const alertDia = DialogPlugin.alert({
            header: '提示',
            theme: 'info',
            body: err.response.data.message,
            confirmBtn: '我知道了',
            cancelBtn: null,
            onConfirm: ({ e }) => {
              console.log('confirm e: ', e);
              alertDia.hide();
              getOrderDetails(valData.value);

              emits('getBussinessList');
            },
          });
        });
      confirmDia.hide();
      // business-order/finish
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
  console.log(row, 'confirmReceiptconfirmReceiptconfirmReceipt');
};
const viewLogistics = (row) => {
  console.log(row, 'viewLogisticsviewLogisticsviewLogistics');
  // if (props.teamId === 0) {
  //   LogisticsInfoRef.value.show(row.sn, true);
  // } else {
  LogisticsInfoRef.value.show(row.sn, true, props.teamId);
  // }
};
const rowData = ref({
  sn: '',
});
const orderRecordVisible = ref(false);
const openRemark = () => {
  remarkWin.value.openWin();
};
const openOrderRecord = (row) => {
  console.log(row, 'rowrowrowrow');
  orderRecordVisible.value.show(orderDetails.value);
};
const withVal = ref(null);
// const elementWidthVal =  computed(() => {
// 	const elementWidth = withVal.value.offsetWidth || 0;
// 	return elementWidth;
// });

// const withVal = ref(null);
// const elementWidth = ref(0);
// onMounted(() => {
// 	console.log(withVal.value, 'withVal.value.offsetWidth');
// 	nextTick(() => {
// 		console.log(withVal.value.offsetWidth, 'withVal.value.offsetWidth');
// 		elementWidth.value = withVal.value.offsetWidth || 0;
// 	});
// });

// mine
const showAll = ref(false); // 控制是否显示所有商品信息

// const detailModalRef = ref(null);
// const goGoodsDetail = (val: { goods_name: any }) => {
//   detailModalRef.value.open(val);
// };

// const closeReason = () => {
// 	const confirmDia = DialogPlugin({
// 		header: t('order.close_reason'),
// 		className: 'modeIconPadding',
// 		closeBtn: null,
// 		confirmBtn: null,
// 		body: t('lss.please_remove_all_job'),
// 		cancelBtn: t('payment.close'),
// 		onClose: () => {
// 			confirmDia.hide();
// 		},
// 	});
// };

// 评价
const evaluationMgRef = ref(null);
const openEvaluate = (row) => {
  evaluationMgRef.value.openWin(row);
  console.log(row, 'openEvaluateopenEvaluateopenEvaluate');
};
const shutdownReason = (val) => {
  const confirmDia = DialogPlugin.alert({
    header: '关闭原因',
    body: val,
    confirmBtn: {
      content: '关闭',
      variant: 'outline',
      theme: 'default',
    },
    onConfirm: () => {
      confirmDia.hide();
    },
    className: 'shutdownReasonmode',
    onClose: () => {
      confirmDia.hide();
    },
  });
};

const emits = defineEmits(['indexGetList', 'getBussinessList']);
const paymentCallback = () => {
  visible.value = false;
  emits('indexGetList', snValue.value);
};

const prompts = (row) => {
  if (row === '') {
    MessagePlugin.error('订单状态已变更,请重新操作');

    getOrderDetails(row.sn);
  } else if (row === '没有支付渠道') {
    const confirmDia = DialogPlugin.alert({
      header: '提示',
      theme: 'info',
      body: t('order.closeTip'),
      closeBtn: false,
      confirmBtn: '知道了',
      className: 'delmode',
      onConfirm: async () => {
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  } else {
    rowData.value = JSON.parse(JSON.stringify(row));
    rowData.value.amount = row.payAmount;
    paymentDialogRef.value.openWin(row);
  }
};
const orderStatusStepFlag = ref(false);
const getOrderDetails = (row) => {
  return bussinessOrderDetail(row.sn, props.teamId).then((res) => {
    console.log(res, 'eeeeeeeeeeeeeeeeeeeeeeeeeee');
    orderDetails.value = res.data;
    orderStatusStepFlag.value = true;
    if (orderDetails.value.status === 1 && orderDetails.value.timeout_seconds) {
      startCountdown(orderDetails.value.timeout_seconds);
    }
    if (orderDetails.value?.refundInfo && orderDetails.value?.refundInfo.refund_id) {
      return orderrefundDetail(orderDetails.value?.refundInfo.refund_id).then((refund) => {
        refundInfo.value = refund.data.data;
        refundInfo.value.refundAt =
          refund.data.data.refundAt === 0 ? '' : getTime(new Date(refund.data.data.refundAt * 1000), true);
        console.log(refundInfo.value, 'refundInfo.valuerefundInfo.value');
        orderDetails.value.goods.forEach((item) => {
          item.showGnAll = false;
          item.showSeAll = false;
        });
        return res;
      });
    } else {
      refundInfo.value = null;
      return res;
    }
  });
};
const openAddress = () => {
  if (orderDetails.value.pickup_address) {
    sdk.openBrowser(
      `https://api.map.baidu.com/marker?location=${orderDetails.value.pickup_address.latitude},${orderDetails.value.pickup_address.longitude}&title=${orderDetails.value.pickup_address.address}&output=html`,
    );
  }
};
const orclose = () => {
  visible.value = false;
  clearInterval(timer);

  emits('getBussinessList');
  countdown.value = '';
};
const refundListRef = ref(null);
// 退款详情
const goRefundDetail = (val) => {
  refundListRef.value.open(val.id);
};
let timer = null;
const startCountdown = (leftSeconds) => {
  console.log(leftSeconds, '11leftSecondsleftSeconds');
  timer = setInterval(() => {
    if (leftSeconds > 0) {
      // 处理天数（超过24小时）
      const days = Math.floor(leftSeconds / 86400);
      leftSeconds = leftSeconds % 86400;

      // 处理小时（超过60分钟）
      const hours = Math.floor(leftSeconds / 3600);
      leftSeconds = leftSeconds % 3600;

      const minutes = Math.floor(leftSeconds / 60);
      const seconds = leftSeconds % 60;
      console.log(days, hours, minutes, seconds, 'days, hours, minutes seconds');
      // 构建显示字符串
      let displayTime = '';
      if (days > 0) {
        displayTime = `${days}天 ${hours.toString().padStart(2, '0')}小时`;
      } else if (hours >= 1) {
        displayTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}小时`;
      } else if (minutes > 0 && seconds > 0) {
        displayTime = `${minutes + 1}分钟`;
      } else if (minutes === 0 && seconds >= 0) {
        displayTime = '不足1分钟';
      }
      countdown.value = displayTime;
      leftSeconds--;
    } else {
      if (visible.value) {
        visible.value = false;
        emits('indexGetList', snValue.value);
      }
      clearInterval(timer);
      countdown.value = '';
    }
  }, 1000);
};

const addCommasToNumber = (str) => {
  let [integerPart, decimalPart] = str.split('.');
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  if (decimalPart) {
    decimalPart = decimalPart.length === 1 ? `${decimalPart}0` : decimalPart.slice(0, 2);
  } else {
    decimalPart = '00';
  }
  console.log(`${integerPart}.${decimalPart}`, '222222222222');

  return `${integerPart}.${decimalPart}`;
};

const closeOrderModal = ref(null);
const closeOrderWay = (row) => {
  // 取消订单
  console.log(row.sn, 'rowwwwwwwwwwww');
  // rowData.value = row;
  // dilatationFlag.value = true;
  closeOrderModal.value.open(row.id, 0);
};

const rebackModalRef = ref(null);
const sqtk = () => {
  // 申请退款
  console.log(orderDetails.value, 'orderDetails.value');
  rebackModalRef.value.open(orderDetails.value);
};
const btnArr = ref([]);
const snValue = ref('');
const valData = ref(null);
const open = async (val, flag) => {
  clearInterval(timer);

  console.log(flag, val, 'flagggggggggggg');
  btnArr.value = flag?.length > 0 ? flag : [];
  orderStatusStepFlag.value = false;
  await nextTick();
  valData.value = val;
  await getOrderDetails(val).then(() => {
    visible.value = true;
  });
  snValue.value = val.sn;
};
const getDt = (val) => {
  getOrderDetails(val);
};
defineExpose({ open, getDt });

const addressExpanded = ref(false);
const addressText = ref(null);
const isAddressOverflow = ref(false);

const deliveryAddressExpanded = ref(false);
const deliveryAddressText = ref(null);
const isDeliveryAddressOverflow = ref(false);

const remarkExpanded = ref(false);
const remarkText = ref(null);
const isRemarkOverflow = ref(false);

const remarkTextSecond = ref(null);
const isRemarkSecondOverflow = ref(false);

const pickupAddressExpanded = ref(false);
const pickupAddressText = ref(null);
const isPickupAddressOverflow = ref(false);

const toggleAddressExpand = () => {
  addressExpanded.value = !addressExpanded.value;
};

const toggleDeliveryAddressExpand = () => {
  deliveryAddressExpanded.value = !deliveryAddressExpanded.value;
};

const toggleRemarkExpand = () => {
  remarkExpanded.value = !remarkExpanded.value;
};

const togglePickupAddressExpand = () => {
  pickupAddressExpanded.value = !pickupAddressExpanded.value;
};

// 检测收货地址文本是否溢出
const checkAddressOverflow = () => {
  if (addressText.value) {
    const element = addressText.value;
    isAddressOverflow.value = element.scrollWidth > element.clientWidth;
  }
};

// 检测配送地址文本是否溢出
const checkDeliveryAddressOverflow = () => {
  if (deliveryAddressText.value) {
    const element = deliveryAddressText.value;
    isDeliveryAddressOverflow.value = element.scrollWidth > element.clientWidth;
  }
};

// 检测备注文本是否溢出
const checkRemarkOverflow = () => {
  if (remarkText.value) {
    const element = remarkText.value;
    isRemarkOverflow.value = element.scrollWidth > element.clientWidth;
  }

  if (remarkTextSecond.value) {
    const element = remarkTextSecond.value;
    isRemarkSecondOverflow.value = element.scrollWidth > element.clientWidth;
  }
};

// 检测自提地址文本是否溢出
const checkPickupAddressOverflow = () => {
  if (pickupAddressText.value) {
    const element = pickupAddressText.value;
    isPickupAddressOverflow.value = element.scrollWidth > element.clientWidth;
  }
};

// 检查所有文本是否溢出的函数
const checkAllOverflow = () => {
  checkDeliveryAddressOverflow();
  checkRemarkOverflow();
  checkAddressOverflow();
  checkPickupAddressOverflow();
};

// 监听收货地址内容变化，检查文本是否溢出
watch(
  () => [
    orderDetails.value?.logistic_address?.address?.location?.address,
    orderDetails.value?.logistic_address?.address?.location?.name,
  ],
  () => {
    nextTick(() => {
      checkAddressOverflow();
    });
  },
);

// 监听备注内容变化，检查文本是否溢出
watch(
  () => orderDetails.value?.remark,
  () => {
    nextTick(() => {
      checkRemarkOverflow();
    });
  },
);

// 监听配送地址内容变化，检查文本是否溢出
watch(
  () => orderDetails.value?.delivery_address?.address?.location?.address,
  () => {
    nextTick(() => {
      checkDeliveryAddressOverflow();
    });
  },
);

// 监听自提地址内容变化，检查文本是否溢出
watch(
  () => orderDetails.value?.pickup_address?.address,
  () => {
    nextTick(() => {
      checkPickupAddressOverflow();
    });
  },
);

// 在组件挂载后添加检查文本是否溢出
onMounted(() => {
  nextTick(() => {
    checkAllOverflow();
  });
});

// 监听窗口大小变化，重新检查所有文本是否溢出
onBeforeMount(() => {
  window.addEventListener('resize', checkAllOverflow);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', checkAllOverflow);
});
</script>
<style lang="less" scoped>
.remark-expanded {
  white-space: normal !important;
  text-overflow: initial !important;
  overflow: visible !important;
  word-break: break-all !important;
}

.remark-warp-text {
  width: calc(100% - 20px);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  cursor: pointer;
}

.remark-warp100 {
  display: flex;
  flex: inherit !important;
  /* justify-content: end; */
  width: 100%;
  height: auto !important;
}

.remark-warp {
  display: flex;
  flex: inherit !important;
  justify-content: end;
  height: auto !important;
  width: calc(100% - 125px);
}

.bac-big-pic {
  width: 100%;
  height: 100%;
  background: url('@assets/bg_s.png') no-repeat #f5f8fe;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

:deep(.t-table__header) {
  z-index: 1;
}

.head-item-lab {
  /* 单行 */
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 隐藏超出的内容 */
  text-overflow: ellipsis; /* 用省略号表示被隐藏的部分 */
  height: 22px;
  font-size: 14px;

  font-weight: 700;
  color: #1a2139;
  padding-left: 0px;
  margin-bottom: 12px;
}

.foot-lab {
  width: 75px;
  height: 22px;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: #828da5;
  line-height: 22px;
  text-align: right;
}

.foot-val {
  min-width: 120px;
  height: 22px;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: right;
  color: #1a2139;
  line-height: 22px;
}

.form-box {
  display: flex;
  flex-wrap: wrap;

  .form-flex {
    display: flex;
    align-items: center;
  }

  .value-item {
    height: 22px;
    font-size: 14px;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei-Regular;
    font-weight: 400;
    color: #1a2139;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .laber-item {
    width: 74px;
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: #828da5;
  }
}

.foot-box {
  padding: 12px 0;
  margin-bottom: -16px;
}

:deep(.t-drawer__footer) {
  /* display: none; */
}

.table-thing-value {
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: #1a2139;
}

.flex-a-end {
  display: flex;
  align-items: center;
  justify-content: end;
}

.table-thing-laber {
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: #717376;
}

.table-thing {
  display: flex;

  img {
    width: 48px;
    height: 48px;
    margin-right: 4px;
  }
}

.content-box {
  border-radius: 8px;
  background: #fff;
  padding: 12px 16px;
  margin: 0 12px 12px;
  position: relative;
}

.lin {
  position: relative;
}

/* .lin::after {
  content: '';
  width: 2px;
  height: 14px;
  background: #4d5eff;
  position: absolute;
  top: 4px;
  border-radius: 2px;
  left: 0;
} */

.head-search-box {
  padding-top: 16px;
  display: block;
  flex-wrap: wrap;
  padding-bottom: 24px;
  border-radius: 8px 8px 0 0;
  padding-left: 16px;
  background: #fff;
}

.form-items {
  display: flex;
  align-items: center;
  padding-bottom: 12px;

  padding-right: 32px;

  .labels {
    height: 22px;
    padding-right: 8px;
    font-size: 14px;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei-Regular;
    font-weight: 400;
    text-align: left;
    width: 64px;
    color: #1a2139;
    line-height: 22px;
  }
}

.my-order-box {
  background-color: #fff;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  border-radius: 8px;
}

.btn-box {
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: #4d5eff;
  cursor: pointer;
  line-height: 22px;
}

.transaction-status {
  width: 52px;
  height: 20px;
  border-radius: 20px;
  font-size: 12px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: center;
  line-height: 20px;
}

.tabledian150 {
  width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.add-btn {
  display: flex;
  width: fit-content;
  height: 22px;
  padding: 0px 12px;
  justify-content: center;
  align-items: center;
  border-radius: 999px;
  border: 1px solid #d5dbe4;
  color: #516082;
  text-align: center;
  font-size: 12px;
  font-weight: 400;
  line-height: 22px;
  cursor: pointer;
}

.foot-btn-box {
  /* margin-top: 36px; */
  text-align: end;
}

.flex-a {
  display: flex;
  align-items: center;
}

.head-item-lab {
  height: 22px;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Bold;
  font-weight: 700;
  color: #1a2139;
  padding-left: 0px;
  margin-bottom: 12px;
}

.foot-val {
  min-width: 120px;
  height: 22px;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: right;
  color: #1a2139;
  line-height: 22px;
}

.foot-btn-box {
  display: flex;
  align-items: center;
  justify-content: space-between !important;
  text-align: end;
  /* margin-bottom: 48px; */
}

.table-foot-box {
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: left;
  color: #717376;
  line-height: 44px;

  .foot-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.w120pl {
  width: 120px;
  padding-left: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.flex-a-js-w140 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.w70text-r {
  width: 190px;
  text-align: right;
}

.w40 {
  width: 50px;
}
:global(.bussiness-drawer-body .t-dialog) {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}
.icon_help-tooltip {
  width: 20px;
  height: 20px;
  margin-left: 4px;
  font-size: 20px;
  line-height: 20px;
  cursor: pointer;
}

.red-time {
  height: 22px;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: left;
  color: #da2d19;
  line-height: 22px;
}

.table-thing-value {
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: #1a2139;
}

.flex-a-end {
  display: flex;
  align-items: center;
  justify-content: end;
}

:deep(.t-dialog__close) {
  width: 24px;
  height: 24px;
}

.table-thing-laber {
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: #717376;
}

.table-thing {
  display: flex;

  img {
    width: 48px;
    height: 48px;
    margin-right: 4px;
  }
}

.order-record {
  display: flex;
  align-items: center;
  color: #4d5eff;
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  cursor: pointer;
}

.filter-res {
  display: flex;
  margin-top: 16px;

  .tit {
    color: var(--text-kyy-color-text-2, #516082);
    margin-right: 8px;
    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    /* 157.143% */
  }

  .ov-time {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }

  .close2 {
    margin-left: 8px;
    width: 20px;
    height: 20px;
    font-size: 20px;
  }

  .te {
    color: var(--kyy-color-tag-text-black, #1a2139);
    cursor: pointer;
    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    border-radius: 4px;
    background: var(--kyy-color-tag-bg-gray, #eceff5);
    margin-right: 8px;
  }

  .stat {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }

  .kword {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }

  .icon {
    display: flex;
    align-items: center;
    cursor: pointer;

    svg {
      width: 14px;
      height: 15px;
      margin-right: 4px;
    }
  }
}

.lin {
  position: relative;
}

/* .lin::after {
  content: '';
  width: 2px;
  height: 14px;
  background: #4d5eff;
  position: absolute;
  top: 4px;
  border-radius: 2px;
  left: 0;
} */
.minw83 {
  font-weight: 600;
}

.head-search-box {
  padding-bottom: 12px;
  padding-left: 16px;
}

.form-items {
  display: flex;
  align-items: center;
  padding-right: 32px;
  /* padding-top: ; */
  padding-bottom: 12px;

  .labels {
    height: 22px;
    padding-right: 8px;
    font-size: 14px;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei-Regular;
    font-weight: 400;
    text-align: left;
    color: #1a2139;
    line-height: 22px;
  }
}

.form-boxxx {
  .fitem {
    margin-bottom: 24px;

    .title {
      color: var(--text-kyy-color-text-3, #828da5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .ctl {
      margin-top: 8px;
    }
  }
}

.shop-logo {
  width: 56px;
  height: 56px;
  background-image: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/shoplogo.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  bottom: 32px;
  right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    margin-bottom: 8px;
  }
}

.thxx {
  background-image: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/bg-zt.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
  /* padding: 12px 16px;
  border-radius: 8px;
  margin: 0 12px; */
}

.order-drawer-header {
  display: flex;
  align-items: center;
  width: 100%;
  border-bottom: none !important;
  justify-content: space-between;
}

.btn-box {
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: #4d5eff;
  cursor: pointer;
  line-height: 22px;
}

.tui::after {
  content: '';
  /* color: #FFFFFF; */
  position: absolute;
  top: 0px;
  left: 0px;
  width: 32px;
  height: 32px;
  background: url('@assets/img/icon_subscript.png') 100% center no-repeat;
  background-position: center;
  background-size: 100%;
  /* text-align: center;
	font-size: 11px;
	width: 0;
	height: 0;
	border-top: 30px solid #FC7C14;
	border-right: 30px solid transparent;
	display: flex;
	align-items: flex-end;
	line-height: 40px;
	text-indent: 3px; */
}

.flex-al-space {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.af-icon {
  margin-left: 8px;
  height: 32px;
}

.f-icon {
  display: flex;
  width: 32px;
  height: 32px;
  cursor: pointer;
  min-height: 32px;
  max-height: 32px;
  padding: 6px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-left: 8px;
  border-radius: var(--radius-kyy-radius-button-s, 4px);
  border: 1px solid var(--color-button-border-kyy-color-button-border-dedault, #d5dbe4);
  background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
}

.flex-al-sb {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 8px;
}

.tkje {
  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.tktext {
  color: var(--text-kyy_color_text_3, #828da5);
  text-align: right;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
  cursor: pointer;
}

.tktips {
  color: var(--text-kyy_color_text_2, #516082);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.mt12 {
  margin-top: 12px;
}

.commodity-mes {
  background: #fff;
  border-radius: 8px;
  margin: 0 12px;
  padding: 12px 16px;
}
.commodity-list:first-child {
  margin-top: 0;
}
.commodity-list {
  position: relative;
  display: flex;
  margin-top: 12px;
  align-items: center;
  justify-content: flex-start;
  padding-bottom: 12px;
  border-bottom: 1px solid #eceff5;

  .commodity-img {
    width: 72px;
    height: 72px;
    border-radius: 12px;
    margin-right: 12px;
  }

  .commodity-name {
    display: flex;
    margin-bottom: 4px;
    justify-content: space-between;
  }

  .commodity-name-span {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    width: 0;
    color: #1a2139;
  }

  .commodity-name-span2 {
    display: inline-block;
    flex: 1;

    color: #1a2139;
  }

  .commodity-name-span.time {
    color: #828da5;
  }

  .preferential-price {
    color: #1a2139;
    margin-right: 8px;
  }

  .original-price {
    color: #acb3c0;
    font-size: 12px;
    text-decoration: line-through;
  }

  .commodity-right {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-between;
    min-height: 70px;
    cursor: pointer;
  }
}

.commodity-see-detail {
  color: #828da5;
  font-size: 12px;
  display: flex;
  border-bottom: 1px solid #eceff5;
  padding-bottom: 4px;
  cursor: pointer;
}

.tui-kuan-price {
  color: #828da5;
  margin-top: 4px;
}

.zhan-wei-zhi {
  width: 50px;
}

.tui-icon {
  color: #fc7c14;
  width: 76px;
  text-align: right;
}

.commodity-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
