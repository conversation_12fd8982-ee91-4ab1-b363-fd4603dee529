import { requests } from '@axios/services';
import { AxiosResponseHeaders } from 'axios';
import { client_orgRequest, iam_srvRequest, ringkolRequest, im_syncRequest } from '@axios/index';

/**
 * 单位分类与设置
 * @param params 请求参数
 * @returns 单位分类与设置
 */
export const unitClassify = (params: any) => requests.clientOrg.get('/unit-classify', params);

export enum AgreementType {
  // 另可广场平台规则
  ContentSpecification = 'lkgcCN',
  // 另可组织广场号服务购买协议
  OrganizationSquare = 'Package',
  // 广场展位销售协议
  circleRingkolBooth = 'Boothsales',
  // 官网模板购买协议
  portalTemplate = 'WebsiteTemplate',
  // 数字社群开启协议
  Association = 'LKSQ001',
  // 生成专属名称购买协议
  ExclusiveName = 'ExclusiveName',
  // 另可店铺入驻协议
  RingkolShop = 'DPFW',
  // 保证金服务协议
  Margin = 'WBYYBZJ',
}

// 获取协议
// export const getAgreement = (name: AgreementType) => `${getBaseUrl('square')}/v1/agreement/${name}`;
export const getAgreement = (uuid: AgreementType) => {
  const lang = navigator.language.split('-');
  return client_orgRequest({
    url: `/agreement/detail?uuid=${uuid}&lang=${lang[0]}-hans-${lang[1]}`,
    method: 'get',
  });
};

// 根据父级id查询树状结构的行政区划列表
export const getRegionsTree = (code?: string) => {
  return ringkolRequest({
    url: `/iam/v2/regions/listRegionsTree?code=${code || ''}`,
    method: 'get',
  });
};

const basePrex = '/v1';
const Api = {
  GetRegionApi: `${basePrex}/regions/search`, // 行政区划查询 POST
  GetAreaCodesApi: `${basePrex}/area_codes/search`, // 国际区号 POST
  getBufferByImgUrl: `${basePrex}/rtransit`, // 获取图片buffer
  makeFilePreview: `/v1/files`, // 获取图片buffer
};
export function getRegion(data: any) {
  return iam_srvRequest({
    method: 'post',

    url: Api.GetRegionApi,
    data: {
      ...data,
    },
  });
}

export function getAreaCodesAxios(data: any) {
  return iam_srvRequest({
    method: 'post',

    url: Api.GetAreaCodesApi,
    data: {
      ...data,
    },
  });
}

export function getFilePreview(data: any) {
  return im_syncRequest({
    method: 'post',

    url: Api.makeFilePreview,
    data,
  });
}

export const getBufferByImgUrl = (r: string): Promise<AxiosResponseHeaders> =>
  requests.square.get(Api.getBufferByImgUrl, { responseType: 'arraybuffer', params: { r } });
