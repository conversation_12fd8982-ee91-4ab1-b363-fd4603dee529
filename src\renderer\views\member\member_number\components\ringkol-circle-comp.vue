<template>
  <div class="ringkol">
    <Preview :platform="platformCpt"/>
    <Tricks :offset="{ x: '-32', y: '-40' }" :uuid="uuidCpt"  :scene="2"/>
  </div>
</template>

<script setup lang="ts">
import Preview from '@renderer/views/square/components/ringkol-circle/Preview.vue';
import { Ref, computed, onActivated, onBeforeMount, reactive, ref, toRaw, watch } from "vue";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
import { useMemberStore } from "@renderer/views/member/store/member";
import { useRoute } from 'vue-router';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();
const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
});


// 平台类型 目前只有digital-platform
const platformCpt = computed(() => props.platform || route.query?.platform);

const uuidCpt = computed(() => {
  let msg = '';
  switch(digitalPlatformStore.platformType) {
    case 'member':
      msg = '数字商协-另可圈-查看者';
      break;
    case 'government':
      msg = '数字城市-另可圈-查看者';
      break;
    case 'cbd':
      msg = '数字CBD-另可圈-查看者';
      break;
    case 'association':
      msg = '数字社群-另可圈-查看者';
      break;
     case 'uni':
      msg = '数字高校-另可圈-查看者';
      break;
  }
  return msg;
});

const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  }
  return getMemberTeamID();
});

const store: any = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore;
  }
  return useMemberStore();
});

const activeAccount = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    let user_ids = {};
    if (route.query.user_ids) {
      user_ids = JSON.parse(route.query.user_ids);
    }
    return { ...route.query, user_ids };
  } else {
    return store.activeAccount;
  }
});
onMountedOrActivated(()=> {
  console.log('kakaxi', route.query)
})
</script>

<style lang="less" scoped>
:deep(.top) {
  padding: 0;
  .t-input__wrap {
    margin-bottom: 20px;
    padding-top: 12px;
    .t-input__prefix {
      margin-right: 4px;
    }
  }
  .color-text-3 {
    font-size: 20px;
  }
}

:deep(.wrap) {
  height: 60vh;
}

:deep(.desktop-wrap) {
  width: 872px;
  background: rgba(0,0,0, 0) !important;
  .right-content {
    padding-right: 16px;
    overflow-y: overlay;
  }
}
:deep(.t-is-active) {
  background: var(--bg-kyy_color_bgBrand_foucs, #DBDFFF) !important;
  a {
    color: var(--kyy_color_anchor_text_foucs, #4D5EFF) !important;
  }

}
:deep(.booth-wrap) {
  gap: 12px;
}
:deep(.t-input__wrap) {
  width: 304px;
}
:deep(.t-anchor__item) {
  transition: all 0.15s linear;
  margin-bottom: 2px;
  &:hover {
    background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
    transition: all 0.15s linear;
    a {
      color: var(--brand-kyy_color_brand_hover, #707EFF) !important;
    }
  }
}
:deep(.allies-square) {
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #FFF);

  /* kyy_shadow_s */
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
  .card-item {
    width: 94px !important;
  }
}
.ringkol {
  display: flex;
  justify-content: center;
  background: url('@renderer/assets/member/icon/bgalbum.png');
  // object-fit:cover;
  background-size: cover;
  padding-bottom: 16px;
}
:deep(.nav) {
  display: none !important;
}
:deep(.top) {
  background: rgba(0,0,0, 0);
  border-bottom: 0;
}
:deep(.preview-content) {
  border-radius: 8px;
  background: #fff;
  padding: 16px 24px;
  padding-right: 2px;
    /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar {
    width: 6px;
    // height: 2px;
    background-color: rgba(0,0,0,0);
  }
  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    background-color: rgba(0,0,0,0);
  }
  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #c8c8c8;
  }

  .right-content {
    gap: 24px !important;
  }

  .card {
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #FFF);

    /* kyy_shadow_s */
    box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
  }

  .left-anchor {
    border-radius: 4px;
    background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  }
}
</style>
