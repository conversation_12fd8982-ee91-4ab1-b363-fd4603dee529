# Ignore generated files
.DS_Store
.idea
.editorconfig
.eslintcache
pnpm-lock.yaml
yarn-lock.yaml
.npmrc
.yarnrc
.husky
.cache

# Ignored suffix
*.log
*.md
*.svg
*.png
*.ico
*ignore

## Local
/dist
/node_modules
/rootLib
/customTypes
/config
/build

# ==========================================
# 👇 src 下业务代码渐进检验（若需校验，注释相应目录）

#! 指定的 allowlist 和 denylist 规则会优先于隐含的忽略规则。
# 因为默认忽略了所有点文件夹及其子文件，目标目录必须首先要处于允许列表中，这样 eslint 才会知道它的子文件。
# 详见文档：https://zh-hans.eslint.org/docs/latest/use/configure/ignore#eslintignore-%E6%96%87%E4%BB%B6
# ==========================================

src/main
src/preload
src/renderer/_jssdk

# 公共
src/renderer/assets
src/renderer/cert
src/renderer/hooks
src/renderer/layouts
src/renderer/plugins
src/renderer/politics
src/renderer/store
src/renderer/style
src/renderer/types
src/renderer/utils
src/renderer/windows

# api
!src/renderer/api
src/renderer/api/*
# 明确校验的👇
!src/renderer/api/square

# 公共组件
!src/renderer/components
src/renderer/components/*
!src/renderer/components/common
src/renderer/components/common/*
src/renderer/components/RK/*

# 明确校验的👇
!src/renderer/components/common/map
!src/renderer/components/common/region
!src/renderer/components/common/EllipsisTooltip.vue
!src/renderer/components/common/Empty.vue
!src/renderer/components/common/UploadAvatar.vue
!src/renderer/components/common/VideoPlayer.vue
!src/renderer/components/page-header
!src/renderer/components/InfiniteLoading
!src/renderer/components/SvgIcon.vue
!src/renderer/components/TextEllipse.vue

# Views
!src/renderer/views
src/renderer/views/*

# 广场
!src/renderer/views/square
# 广场目录下暂时不校验的（排除），需相关责任人注释掉并修复错误
src/renderer/views/square/activity
src/renderer/views/square/fengcai
src/renderer/views/square/friend-ablum
src/renderer/views/square/niche
src/renderer/views/square/notice
src/renderer/views/square/pb
src/renderer/views/square/phone-album
src/renderer/views/square/service
src/renderer/views/square/components/SquareHall.vue
src/renderer/views/square/components/page-designer/components/albumContentDetail.vue
src/renderer/views/square/components/annual-fee/AnnualFeeDialog.vue
src/renderer/views/square/components/FindAddressHistory.vue
src/renderer/views/square/components/post/comment/CommentItem.vue
src/renderer/api/square/comment.ts

# IM
!src/renderer/views/message/
src/renderer/views/message/*
!src/renderer/views/message/chat/
src/renderer/views/message/chat/*
!src/renderer/views/message/chat/msgTypeContent
src/renderer/views/message/chat/msgTypeContent/*
# 明确校验的👇
!src/renderer/views/message/chat/msgTypeContent/AppWorks
!src/renderer/views/message/chat/msgTypeContent/AppSecretary
!src/renderer/views/message/chat/msgTypeContent/components

# 数智工厂
!src/renderer/views/workBench
src/renderer/views/workBench/*
!src/renderer/views/workBench/enterprise
!src/renderer/views/workBench/enterprise/**
!src/renderer/views/workBench/receipt

# 数字平台
!src/renderer/views/digital-platform/
src/renderer/views/digital-platform/*
src/renderer/views/digital-platform/modal/exclusiveSetting.vue
!src/renderer/views/digital-platform/components
src/renderer/views/digital-platform/components/*
# 明确校验的👇
!src/renderer/views/digital-platform/forum
!src/renderer/views/digital-platform/post
!src/renderer/views/digital-platform/components/ProductThumbnail.vue

# 活动
!src/renderer/views/activity/
src/renderer/views/activity/*
!src/renderer/views/activity/components
src/renderer/views/activity/components/*
# 明确校验的👇
!src/renderer/views/activity/activityListInvolved.vue
!src/renderer/views/activity/activityListCreated.vue
!src/renderer/views/activity/activityParticipantDetail.vue
!src/renderer/views/activity/manage
!src/renderer/views/activity/create
!src/renderer/views/activity/platform-activity-admin
!src/renderer/views/activity/components/ActivityList.vue
!src/renderer/views/activity/components/RegistrationFormDialog.vue
!src/renderer/views/activity/hooks

# 政策列车
!src/renderer/views/policy-express
!src/renderer/views/policy-express/**

