import {
  BrowserWindow,
  BrowserView,
  Rectangle,
  Tray,
  nativeImage,
  Menu,
  app,
  screen,
  ipcMain,
  session,
} from 'electron';
import { showDevTool } from '@main/env';
import { getSDK, enable, getCustomSession, getRandomUUID } from '@lynker-desktop/electron-sdk/main';
import { getRendererWindowEntry, lib } from '../config/StaticPath';
import { handleBvRenderProcessGone } from './window/utils';

const path = require('path');

let timer = null;
const getPath = (url) => url.split('#')[1] || '';
let tray: Tray = null;
let trayTimer = null;
const icon = nativeImage.createFromPath(path.join(lib, '<EMAIL>'));
export default class WindowsManager {
  loadWindow: BrowserWindow = null;

  loginWindow: BrowserWindow = null;

  mainWindow: BrowserWindow = null;

  iframeWindow: BrowserWindow = null;

  settingWindow: BrowserWindow = null;

  previewWindow: BrowserWindow = null;

  // 独立窗口
  aloneWindow: BrowserWindow = null;
  // 合并消息窗口
  mergedMessageWindow: BrowserWindow = null;
  // 系统监控窗口
  monitorWindow: BrowserWindow = null;

  identWin: BrowserWindow = null;

  // 作为im桥梁及音视频通话承载视图
  messageView: BrowserView = null;

  // 音视频通话独立窗口
  messageWindow: BrowserWindow = null;

  // 弹窗 BrowserView
  popView: BrowserView = null;

  // 独立弹窗
  dialog: BrowserWindow = null;

  // 提醒右下角独立弹窗
  remindDialog: BrowserWindow = null;

  destroyTray = () => {
    tray?.destroy();
    tray = null;
  };

  preventClose = (window: BrowserWindow) => {
    window.on('close', (e) => {
      e.preventDefault();
      if (window.isFullScreen()) {
        window.setFullScreen(false);
        window.once('leave-full-screen', () => window.hide());
      } else {
        window.hide();
      }
    });

    // 新建托盘
    // if (!app.isPackaged) {
    if (!tray) {
      tray = new Tray(icon);
    }
    //   // 开发模式下，直接从原始路径加载
    //   tray = new Tray(nativeImage.createFromPath(
    //     path.resolve(__dirname, '../renderer/assets/account/<EMAIL>')
    //   ));
    // } else {
    //   // 打包后模式，考虑到资源可能被 extraResources 复制到了特定位置
    //   // 假设 extraResources 配置已正确处理资源到预期位置
    //   const trayImagePath = path.join(process.resourcesPath, 'assets/account/tray.png');
    //   tray = new Tray(nativeImage.createFromPath(trayImagePath));
    // }
    // 消息通知
    // 托盘名称
    tray.setToolTip(process.env.userConfig?.VITE_APP_TITLE);
    window.setSkipTaskbar(false);
    // 托盘菜单
    const contextMenu = Menu.buildFromTemplate([
      {
        label: '打开',
        click: () => {
          this.showWindow();
        },
      },
      {
        label: '退出',
        click: () => {
          app.exit();
        },
      },
    ]);
    // 载入托盘菜单
    tray.setContextMenu(contextMenu);
    tray.on('click', () => {
      this.showWindow();
    });
  };

  createMessageView = async (onMessageLoaded: () => unknown) => {
    this.messageView = await getSDK().windowManager.create({
      name: `message-view`,
      type: 'BV',
      url: '',
    });
    // this.messageView = new BrowserView({
    //   webPreferences: {
    //     // session: getCustomSession(),
    //     nodeIntegration: true,
    //     contextIsolation: false,
    //     preload: getSDK()?.getPreload?.(),
    //   },
    // });
    handleBvRenderProcessGone(this.messageView.webContents, 'messageView');
    enable(this.messageView.webContents);
    const url = getRendererWindowEntry("windows/RKIM/index.html");
    this.messageView.webContents.loadURL(url);
    if (showDevTool) {
      // this.messageView.webContents.toggleDevTools();
    }
    const that = this;
    this.messageView.webContents.on('did-finish-load', async () => {
      await onMessageLoaded();
      that.createPopView();
    });
  };

  hideMessageView = () => {
    this.messageWindow?.hide();
    this.messageWindow?.setBrowserView(null);
    this.messageWindow?.destroy();
    this.messageWindow = null;
  };

  showMessageView = async (bounds: Rectangle) => {
    if (!this.messageWindow) {
      this.messageWindow = await getSDK().windowManager.create({
        name: `messageWindow-${getRandomUUID()}`,
        url: '',
        browserWindow: {
          ...bounds,
          frame: false,
          resizable: false,
          movable: true,
          maximizable: false,
          fullscreenable: false,
          webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
          },
        },
      });
      this.messageWindow.setBrowserView(this.messageView);
      this.messageView.setBounds({
        x: 0,
        y: 0,
        width: bounds.width,
        height: bounds.height,
      });
      this.messageWindow.on('close', (e) => {
        try {
          this.messageView.webContents.send('im.real.av', {
            action: 'QUIT_CALL_VIDEO',
            data: {},
          });
          e.preventDefault();
          this.messageWindow.hide();
        } catch (error) {
          console.error('messageWindow close error: ', error);
        }
      });
    } else {
      this.messageWindow.setBounds(bounds);
      this.messageView.setBounds({
        x: 0,
        y: 0,
        width: bounds.width,
        height: bounds.height,
      });
      this.messageWindow.show();
      this.messageWindow.focus();
    }
  };

  createPopView = async () => {
    if (!this.popView) {
      this.popView = await getSDK().windowManager.create({
        name: `pop-bv`,
        type: 'BV',
        url: '',
      });
      // this.popView = new BrowserView({
      //   // 创建新的BrowserView
      //   webPreferences: {
      //     // session: getCustomSession(),
      //     nodeIntegration: true,
      //     contextIsolation: false,
      //     preload: getSDK()?.getPreload?.(),
      //   },
      // });
      // enable(this.popView.webContents);
      handleBvRenderProcessGone(this.popView.webContents, 'popView');
      this.popView.webContents.loadURL(getRendererWindowEntry('windows/popBv/index.html')).catch((res) => {
        console.log('popCrashReason', res);
      });
      // pop弹窗
      // if (showDevTool) {
      //   this.popView.webContents.openDevTools({
      //     mode: "undocked",
      //     activate: true,
      //   });
      // }
    }
  };

  disconnectMessageView = async () => {
    if (this.messageView?.webContents?.isDestroyed() === false) {
      await new Promise((resolve) => {
        try {
          ipcMain.once('im.logout.finish', () => {
            resolve(null);
          });
          this.messageView.webContents.send('im.logout');
        } catch (error) {
          console.error('disconnectMessageView error: ', error);
        }
      });
    }
  };

  destoryMessageView = async () => {
    await this.disconnectMessageView();
    if (this.messageView?.webContents?.isDestroyed() === false) {
      (this.messageView.webContents as any).close();
    }
    this.messageView = null;

    if (this.messageWindow?.isDestroyed() === false) {
      this.messageWindow.destroy();
    }
    this.messageWindow = null;
  };

  refreshMessageWindow = () => {
    console.log('====>refreshMessageWindow');
    this.messageView.webContents.reload();
  };

  showMessageWindow = () => { };

  hideMessageWindow = () => { };

  showIdentWin = () => {
    this.identWin.show();
  };

  hideIdentWin = () => {
    this.identWin.hide();
  };

  /**
   * 清除托盘闪烁提醒。
   */
  clearTrayFlash = () => {
    if (trayTimer) {
      clearTimeout(trayTimer);
      trayTimer = null;
      tray?.setImage(icon);
    }
  };

  showWindow = () => {
    const window = this.mainWindow || this.loginWindow;
    window.show();
  };

  showhideWindow = () => {
    const window = this.mainWindow || this.loginWindow;
    const plat = process.platform;
    if (plat === 'darwin') {
      window.isMinimized() ? window.maximize() : window.minimize();
    } else {
      window.isVisible() ? window.hide() : window.show();
    }
  };

  setWindowSize = (window: BrowserWindow, width: number, height: number, smooth = true) => {
    window.setContentSize(width, height, smooth);
  };

  setBvSize = (window: BrowserView, xNew?: number, yNew?: number, widthNew?: number, heightNew?: number) => {
    const { x, y, width, height } = window.getBounds();
    window.setBounds({ x: xNew || x, y: yNew || y, width: widthNew || width, height: heightNew || height });
  };

  closeLoginWindow = () => {
    this.loginWindow?.isDestroyed() === false && this.loginWindow?.destroy();
    this.loginWindow = null;
  };

  closeMainWindow = () => {
    console.log('初始化closeMainWindow');

    this.popView && this.mainWindow.removeBrowserView(this.popView);
    if (this.popView?.webContents?.isDestroyed() === false) {
      (this.popView.webContents as any).close();
    }

    this.mainWindow?.isDestroyed() === false && this.mainWindow?.destroy();
    this.identWin?.isDestroyed() === false && this.identWin?.destroy();
    this.popView?.webContents?.isDestroyed() === false && this.popView?.webContents?.isDestroyed();
    this.popView = null;
    this.mainWindow = null;
    this.identWin = null;
  };

  closeIdentWindow = () => {
    this.identWin?.isDestroyed() === false && this.identWin?.destroy();
    this.identWin = null;
  };

  closeLoadWindow = () => {
    this.loadWindow?.isDestroyed() === false && this.loadWindow?.destroy();
    this.loadWindow = null;
  };

  closeIframeWindow = () => {
    this.iframeWindow?.isDestroyed() === false && this.iframeWindow?.destroy();
    this.iframeWindow = null;
  };

  closePreviewWindow = () => {
    this.previewWindow?.isDestroyed() === false && this.previewWindow?.destroy();
    this.previewWindow = null;
  };

  closeDialog = () => {
    this.dialog?.isDestroyed() === false && this.dialog?.destroy();
    this.dialog = null;
  };

  closeAloneWindow = () => {
    this.aloneWindow?.isDestroyed() === false && this.aloneWindow?.destroy();
    this.aloneWindow = null;
  };

  closeMergedMessageWindow = () => {
    this.mergedMessageWindow?.isDestroyed() === false && this.mergedMessageWindow?.destroy();
    this.mergedMessageWindow = null;
  };
  closeRemindDialog = () => {
    this.remindDialog?.isDestroyed() === false && this.remindDialog?.destroy();
    this.remindDialog = null;
  };

  closeSettingWindow = () => {
    this.settingWindow?.isDestroyed() === false && this.settingWindow?.destroy();
    this.settingWindow = null;
  };

  setRemindDialogSize = (window: BrowserWindow, widthSize: number, heightSize: number, smooth = true) => {
    window?.setContentSize(widthSize, heightSize, smooth);
    const sizeObj = screen.getPrimaryDisplay().workAreaSize;
    const { width, height } = sizeObj;
    const [cwidth, cheight] = window.getContentSize();
    const left = parseInt(width - (cwidth || 0) - 5);
    const top = process.platform === 'darwin' ? 5 : parseInt(height - (cheight || 0) - 5);
    window.setPosition(left, top);
  };

  closeAll() {
    // Get all windows and close them
    BrowserWindow.getAllWindows().forEach((window) => {
      if (!window.isDestroyed()) {
        window.close();
      }
    });
  }

  preHideIdentWin = () => {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
    return new Promise((resolve) => {
      // this.identWin.focus();
      try {
        this.identWin.webContents.send('load-card');
        // windowManager.showIdentWin();
        this.identWin.setOpacity(0);
        timer = setTimeout(() => {
          clearTimeout(timer);
          timer = null;
          this.hideIdentWin();
          resolve('');
        }, 200);
      } catch (error) {
        console.error('preHideIdentWin error: ', error);
      }
    });
  };

  // 管理 mainwindow bv
  setBv = async (bv: BrowserView, options?: any, reload = false, bvName = '') => {
    if (!this.mainWindow) {
      return;
    }
    if (bv) {
      if (!bv.webContents) {
        bv = null;
      }
      if (bv?.webContents?.isDestroyed()) {
        bv = null;
      }
      if (!bv?.webContents?.id) {
        bv = null;
      }
    }
    if (bv) {
      try {

        const currentUrl = bv.webContents.getURL();
        // 需要用addBrowserView设置然后才能设置bounds
        getSDK().windowManager.rename(bv.webContents.id, bvName);

        // 如果不需要reload，且url不一致，则发送跳转指令
        if (!options?.reload && options?.jumpPath) {
          console.log('====>setBv jumpPath', options?.jumpPath, currentUrl, options?.url, !currentUrl.includes(options?.jumpPath));
          const jumpPath = 'index.html#' + options?.jumpPath;
          if (currentUrl !== options.url && !currentUrl.includes(jumpPath)) {
            bv.webContents.send('main-router-jump', { url: options.url });
          }
        }
        if (options?.bounds.width !== 0) {
          if (options.showCrruentBv) {
            this.mainWindow.addBrowserView(bv);
            setTimeout(() => {
              this.hideBvs(bv)
            }, 150);
          } else if (options.addBv) {
            this.mainWindow.addBrowserView(bv);
          } else {
            this.mainWindow.setBrowserView(bv);
          }
        }
        if (options?.reload) {
          bv.webContents.once('did-finish-load', () => {
            bv.webContents.reload();
          });
          const url = new URL(`${options?.url}`);
          url.searchParams.append('__RINGKO_DESKTIOP_TIME__', `${Date.now()}`);
          // console.log('reload', options?.url, url)

          bv.webContents.loadURL(url.href);
        }
        // if (options?.reload) {
        //   bv.webContents.loadURL(options?.url);
        //   setTimeout(() => {
        //     bv.webContents.reload();
        //   }, 100);
        // }
        options.bounds && bv.setBounds(options.bounds);
        bv.setAutoResize({ width: true, height: true });
        return bv;
      } catch (error) {
        console.error('====>setBv', error);
      }
    }
    const newBv = await getSDK().windowManager.create({
      type: 'BV',
      name: bvName || `new-bv-${getRandomUUID()}`,
      url: options?.url,
      browserWindow: {
        ...options?.browserWindow,
        webPreferences: {
          ...options?.browserWindow?.webPreferences,
          preload: getSDK()?.getPreload?.(),
        },
      }
    })
    // const newBv = new BrowserView({
    //   // 创建新的BrowserView
    //   webPreferences: {
    //     // session: getCustomSession(),
    //     nodeIntegration: true,
    //     contextIsolation: false,
    //     preload: getSDK()?.getPreload?.(),
    //     ...options?.webPreferences,
    //   },
    // });
    handleBvRenderProcessGone(newBv.webContents, 'newBv');
    enable(newBv.webContents);
    if (options.addBv) {
      this.mainWindow.addBrowserView(newBv);
    } else {
      this.mainWindow.setBrowserView(newBv);
      newBv.setBackgroundColor('#fff');
    }
    options.bounds && newBv.setBounds(options.bounds);
    newBv.setAutoResize({ width: true, height: true });
    await newBv.webContents.loadURL(options?.url);
    console.log('====>newBv.webContents.loadURL', options?.url);
    if (showDevTool) {
      // options?.showDevtool && newBv.webContents.openDevTools({ mode: "undocked", activate: true });
    }
    return newBv;
  };

  /**
   * 隐藏mainwindow下的 bv
   *  */
  hideBvs = (notBv?: any) => {
    const bvs = this.mainWindow.getBrowserViews();
    bvs.forEach((bv) => {
      if (bv?._name !== notBv?._name) {
        this.mainWindow.removeBrowserView(bv);
      }
    });
  };

  /**
   * 更新 popView 的位置和大小
   * @param type 弹窗类型2
   * @param data 额外数据
   */
  updatePopViewBounds = (type: string, data?: any) => {
    if (!this.mainWindow || !this.popView) return;

    const size = this.mainWindow.getSize();
    const plat = process.platform;

    const boundsObj = {
      personal: { x: 18, y: 44, width: 226, height: 520 },
      add: {
        x: plat === 'darwin' ? size[0] - 16 - 194 : size[0] - 16 - 124 - 184,
        y: 48,
        width: 154,
        height: 300,
      },
      dialog: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      dialogCreateGroup: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      dialogScene: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      dialogContacts: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      dialogSearch: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      dialogOrg: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      // dialogActCode: { x: 0, y: 48, width: size[0], height: size[1] - 48 },

      accountAuthTip: { x: 0, y: 0, width: size[0], height: size[1] },
      packUpdate: { x: 0, y: 0, width: size[0], height: size[1] },
      appAuthUpdateTip: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogModifyPw: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogLogout: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogKickOut: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogViolationOut: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogRemind: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogActivityRemind: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogShare: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogVcard: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogReport: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogIdentity: { x: 0, y: 0, width: size[0], height: size[1] },
      moreMenu: {
        x: data?.isCollapse ? 152 : 88,
        y: 48 + (data?.isCollapse ? 50 * data?.leftMenuNum : 70 * data?.leftMenuNum),
        width: 164,
        height: 91,
      },
      moreMenuIcon: {
        x: (data?.isCollapse ? 135 : 70) - 16,
        y: size[1] * 0.5 - 31,
        width: 32,
        height: 62,
      },
    };

    const bounds = boundsObj[type];
    if (bounds) {
      this.popView.setBounds(bounds);
    }
  }
}

/**
 * 是否是有效的窗口
 * @param bv 窗口
 * @returns
 */
export const isValidWindow = (bv?: BrowserWindow) => bv && !bv.isDestroyed();

export const windowManager = new WindowsManager();

/**
 * 设置未读数
 * @param count
 * @param flashFrame 是否闪烁
 */
export const setUnread = ({ count, flashFrame }) => {
  if (process.platform === 'darwin') {
    app.setBadgeCount(count);
    tray?.setTitle(`${count || ''}`);
  } else if (process.platform === 'win32') {
    console.log('====>count, flashFrame ', count, flashFrame);
    if (flashFrame) {
      if (trayTimer) {
        clearTimeout(trayTimer);
        trayTimer = null;
      }
      windowManager.mainWindow.flashFrame(true);
      // windowManager.mainWindow.flashFrame(!windowManager.mainWindow.isFocused());
      let flag = false;
      trayTimer = setInterval(() => {
        flag = !flag;
        if (flag) {
          tray?.setImage(nativeImage.createEmpty());
        } else {
          tray?.setImage(icon);
        }
      }, 500);
      // } else {
      //   tray?.setImage(icon);
    }
    if (count === 0) {
      if (trayTimer) {
        clearTimeout(trayTimer);
        trayTimer = null;
      }
      tray?.setImage(icon);
    }
  }
};
