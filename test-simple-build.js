const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始简单构建测试...');

// 先清理构建目录
console.log('🧹 清理构建目录...');
const cleanProcess = spawn('npm', ['run', 'build:clean'], {
  stdio: 'inherit',
  shell: true
});

cleanProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ 清理完成');
    startBuild();
  } else {
    console.error(`❌ 清理失败，退出码: ${code}`);
  }
});

function startBuild() {
  console.log('📦 开始构建渲染进程...');
  
  const buildProcess = spawn('npm', ['run', 'build:renderer:dev'], {
    stdio: 'pipe',
    shell: true
  });
  
  let output = '';
  
  buildProcess.stdout.on('data', (data) => {
    const text = data.toString();
    output += text;
    process.stdout.write(text);
    
    // 检查是否卡住
    if (text.includes('building renderer process')) {
      console.log('📊 检测到渲染进程构建开始...');
    }
  });
  
  buildProcess.stderr.on('data', (data) => {
    const text = data.toString();
    output += text;
    process.stderr.write(text);
  });
  
  // 设置超时
  const timeout = setTimeout(() => {
    console.log('⏰ 构建超时，终止进程...');
    buildProcess.kill('SIGTERM');
  }, 180000); // 3分钟超时
  
  buildProcess.on('close', (code) => {
    clearTimeout(timeout);
    if (code === 0) {
      console.log('✅ 构建成功完成');
      checkBuildResults();
    } else {
      console.error(`❌ 构建失败，退出码: ${code}`);
      console.log('最后的输出:', output.slice(-1000)); // 显示最后1000字符
    }
  });
  
  buildProcess.on('error', (error) => {
    clearTimeout(timeout);
    console.error('❌ 构建过程出错:', error.message);
  });
}

function checkBuildResults() {
  try {
    const distPath = path.join(__dirname, 'dist/electron/renderer');
    const assetsPath = path.join(distPath, 'assets');
    
    if (fs.existsSync(assetsPath)) {
      const files = fs.readdirSync(assetsPath);
      const jsFiles = files.filter(f => f.endsWith('.js'));
      const cssFiles = files.filter(f => f.endsWith('.css'));
      const assetFiles = files.filter(f => !f.endsWith('.js') && !f.endsWith('.css'));
      
      console.log('\n📊 构建结果统计:');
      console.log(`JS 文件: ${jsFiles.length} 个`);
      console.log(`CSS 文件: ${cssFiles.length} 个`);
      console.log(`其他资源文件: ${assetFiles.length} 个`);
      
      // 检查文件大小
      let totalSize = 0;
      jsFiles.forEach(file => {
        const filePath = path.join(assetsPath, file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
        totalSize += stats.size;
        console.log(`  ${file}: ${sizeInMB}MB`);
      });
      
      console.log(`\n📈 总JS大小: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
      
      if (assetFiles.length === 0) {
        console.log('✅ 成功！所有静态资源已内联到JS文件中');
      } else {
        console.log('⚠️  仍有部分资源文件未内联:');
        assetFiles.forEach(file => {
          const filePath = path.join(assetsPath, file);
          const stats = fs.statSync(filePath);
          const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
          console.log(`  ${file}: ${sizeInMB}MB`);
        });
      }
    } else {
      console.log('❌ 构建输出目录不存在');
    }
  } catch (error) {
    console.error('❌ 检查构建结果时出错:', error.message);
  }
}
