<template>
  <div
    class="activity"
    @scroll="detailScroll"
    ref="activityRef"
    :style="{ height: Route.name == 'activityDetail' ? '100%' : 'calc(100% - 40px)' }"
  >
    <div v-if="!initFinished" class="skeleton-container">
      <t-skeleton theme="article" animation="gradient"></t-skeleton>
    </div>

    <div v-show="initFinished">
      <!-- 场景活动显示来源信息(独立弹窗) -->
      <div class="chatBox" v-if="activityDetail?.chatId && identityType === 'created'">
        <div>
          {{
            `${t("activity.activity.from")}${
              activityDetail?.chatId?.groupId ? t("activity.activity.groupChat") : t("activity.activity.singleChat")
            }:`
          }}
        </div>
        <div class="overHiddenName">{{ activityDetail?.chat?.name }}</div>
        <t-button class="clickViewButton" @click.stop="openIM(activityDetail)" theme="default" variant="text">
          {{ t("activity.activity.clickView") }}
        </t-button>
      </div>
      <!-- 场景活动显示来源信息（tab） -->
      <div class="chatBox" v-else-if="Route.query.targetId && identityType === 'created'">
        <div>
          {{
            `${t("activity.activity.from")}${
              Route.query.conversationType == 3 ? t("activity.activity.groupChat") : t("activity.activity.singleChat")
            }:`
          }}
        </div>
        <div class="overHiddenName">{{ Route.query.targetName }}</div>
        <t-button class="clickViewButton" @click.stop="openIM(Route.query, 'query')" theme="default" variant="text">
          {{ t("activity.activity.clickView") }}
        </t-button>
      </div>
      <!-- 取消原因 -->
      <div
        class="chatBox-reason"
        v-if="activityDetail?.changed !== 'Update'"
        :style="{ marginTop: activityDetail?.chatId || Route.query.targetId ? '8px' : '' }"
      >
        <div v-if="activityDetail?.changed === 'Cancel'">
          {{t("activity.activity.reason")}}
        </div>
        <div v-else-if="activityDetail?.changed === 'Reschedule'">
          {{t("activity.activity.Reschedule")}}
        </div>
        <span>：</span>
        <div class="overHiddenName">{{ activityDetail?.reason !== "" ? activityDetail?.reason : "--" }}</div>
      </div>
      <!-- 上方图片内容部分 -->
      <div class="header-box">
        <!-- 图片 -->
        <div>
          <t-image class="assetUrl" fit="cover" position="center" :src="activityDetail.assetUrl" />
        </div>
        <!-- 右侧详情文案 -->
        <div style="flex: 1">
          <!-- 活动标题 -->
          <div class="title">
            <span>{{ activityDetail.subject }}</span>
          </div>
          <div class="flex">
            <div style="flex: 1">
              <!-- 活动类型+状态tag -->
              <div class="duration">
                <img src="@renderer/assets/activity/icon_type.svg" alt="" />
                <!-- 活动类型 -->
                <t-tag style="margin: 0; background-color: #eaecff; color: #4d5eff">{{
                    activityDetail.categoryTitle
                  }}</t-tag>
                <!-- 状态tag -->
                <div class="status-box">
                  <div :class="[formatStatus(activityDetail, 'activityTypeClass'), 'tag']">
                    {{ formatStatus(activityDetail, "activityTypeText") }}
                  </div>
                </div>
              </div>
              <!-- 活动时间 -->
              <div class="duration">
                <img src="@renderer/assets/activity/icon_date.svg" alt="" />
                <div class="line-1 text-color">
                  {{
                    activityDetail.duration?.startTime
                      ? `${moment.unix(activityDetail.duration?.startTime).format("YYYY-MM-DD HH:mm")} ~ ${moment
                        .unix(activityDetail.duration.endTime)
                        .format("YYYY-MM-DD HH:mm")}`
                      : "--"
                  }}
                </div>
                <!-- <div class="status-box">
                  <div :class="[formatStatus(activityDetail.details, 'activityTypeClass'), 'tag']">{{ formatStatus(activityDetail.details, 'activityTypeText') }}</div>
                </div> -->
                <!--              <t-tag>{{ formatStatus(activityDetail.details, 'activityTypeText') }}</t-tag>-->
              </div>
              <!-- 活动地点 -->
              <div class="location">
                <img src="@renderer/assets/activity/icon_positioning.svg" alt="" />
                <div class="line-3 text-color">
                  {{ activityDetail.location?.title || activityDetail.location?.address || "--" }}
                </div>
              </div>

              <!-- 创建者的身份显示(被邀请的) -->
              <div>
                <div class="contact-box">
                  <div class="svg">
                    <kyy-avatar
                      avatar-size="44px"
                      :image-url="identityItem.avatar"
                      :user-name="identityItem.staffName"
                      roundRadius
                    />
                    <div class="content">
                      <div class="name">{{ identityItem.staffName }}</div>
                      <div v-if="identityItem.teamId" :class="identityItem.cardId.split('')[0] == '$' ? 'blue' : 'org'">
                        {{ identityItem.teamName }}
                      </div>
                    </div>
                  </div>
                  <div style="margin-top: 8px" class="button" v-show="identityType === 'involved'">
                    <t-button @click="openIM(identityItem)" class="button borderButton" theme="default" variant="text">
                      {{ openIdentityType.text }}
                    </t-button>
                  </div>
                </div>
              </div>
              <!-- 头部操作按钮 -->
              <div class="headerBtn">
                <!-- 渲染前3个按钮（select类型只会出现在前三个） -->
                <template v-for="(button, index) in allButtonList.slice(0, 3)" :key="index">
                  <t-select
                    v-if="button.component === 'select'"
                    v-replace-svg
                    v-model="reply"
                    @change="button.onChange"
                    style="max-width: 80px; margin-right: 8px; font-weight: 600"
                  >
                    <t-option
                      v-for="option in button.options"
                      :key="option.key"
                      :value="option.value"
                      :label="option.text"
                      :style="{ display: option.value === reply ? 'none' : 'flex' }"
                    >{{ option.text }}</t-option
                    >
                  </t-select>

                  <t-button
                    v-else
                    :theme="button.theme"
                    :class="button.class"
                    :disabled="button.disabled"
                    style="font-weight: 600"
                    @click.stop="button.click"
                  >
                    {{ button.text }}</t-button
                  >
                </template>

                <t-dropdown
                  v-if="allButtonList.length > 3"
                  overlayClassName="activity"
                  :options="
                  allButtonList
                    .slice(3)
                    .map((button, index) => ({ content: button.text, value: index, onClick: button.click }))
                "
                  placement="bottom-left"
                  destroy-on-close
                  :overlay-style="{ width: '140px' }"
                >
                  <t-button theme="default" style="font-weight: 600">{{ t("activity.activity.more") }}</t-button>
                </t-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 发布至、活动提醒、通知参与人 -->
      <div class="middle-box" v-if="identityType === 'created' && nowMoudle && showMiddleBox">
        <div class="people" v-if="showSendTo">
          <div><img src="@renderer/assets/activity/sendto.svg" alt="" />{{ t("activity.activity.sendTo") }}</div>
          <div class="line-1 text-color">{{ sendToText() }}</div>
        </div>
        <div class="remind">
          <div>
            <img src="@renderer/assets/activity/icon_alarm_clock.svg" alt="" />{{
              t("activity.activity.reminders_label")
            }}
          </div>
          <div class="reminders-flex" v-if="activityDetail.reminders?.length">
            <t-tag v-for="(item, index) in formatDetail(activityDetail, 'reminders')" :key="index">{{ item }}</t-tag>
          </div>
          <div v-else class="none">{{ t("activity.activity.noremindtime") }}</div>
        </div>
        <div class="people" v-if="activityDetail.channels?.length">
          <div>
            <img src="@renderer/assets/activity/icon_people.svg" alt="" />{{ t("activity.activity.channels_label") }}
          </div>
          <div class="line-1 text-color">{{ formatDetail(activityDetail, "channels")?.join("、") }}</div>
        </div>
      </div>
      <!-- tab操作按钮 -->
      <div class="tab-box" ref="boxHeight">
        <div class="tab-list" :class="{ 'fixed-nav': isFixed }">
          <div
            :class="['default-tab-item', tabValue === 'content' ? 'active-tab-item' : '']"
            v-if="activityDetail?.content"
            href="“toQuillEditor”"
          >
            <t-button theme="default" variant="text" @click="scrollToSection('content')">{{
                t("activity.activity.activityDetail")
              }}</t-button>
            <div class="tab-item-border"></div>
          </div>
          <div
            :class="['default-tab-item', tabValue === 'quota' ? 'active-tab-item' : '']"
            v-if="activityDetail?.actors?.length || activityDetail?.open"
          >
            <t-button theme="default" variant="text" @click="scrollToSection('quota')">
              {{
                activityDetail?.open
                  ? `${t("activity.activity.quota_person")}（${activityDetail?.actors?.length || 0}）`
                  : `${t("activity.activity.actors_label")}（${activityDetail?.actors?.length || 0}）`
              }}
            </t-button>
            <div class="tab-item-border"></div>
          </div>
          <div
            :class="['default-tab-item', tabValue === 'files' ? 'active-tab-item' : '']"
            v-if="activityDetail?.files?.files.length != 0"
          >
            <t-button theme="default" variant="text" @click="scrollToSection('files')">{{
                t("activity.activity.files_label")
              }}</t-button>
            <div class="tab-item-border"></div>
          </div>
          <div
            :class="['default-tab-item', tabValue === 'reports' ? 'active-tab-item' : '']"
            v-if="identityType === 'created'"
          >
            <t-button theme="default" variant="text" @click="scrollToSection('reports')">{{
                t("activity.activity.reports")
              }}</t-button>
            <div class="tab-item-border"></div>
          </div>
          <div
            :class="['default-tab-item', tabValue === 'albums' ? 'active-tab-item' : '']"
            v-if="identityType === 'created'"
          >
            <t-button theme="default" variant="text" @click="scrollToSection('albums')">{{
                t("activity.activity.albums")
              }}</t-button>
            <div class="tab-item-border"></div>
          </div>
        </div>
        <div class="tab-detail" :class="{ 'tab-details': isFixed }" ref="detailRef">
          <div
            :class="['default-tab-item']"
            v-if="activityDetail?.content"
            style="margin-bottom: 20px; border-bottom: 1px solid #eceff5; padding-bottom: 20px"
            id="content"
          >
            <div class="tabdetail-title" v-if="firstTab != 'content'">
              <span></span><span>{{ t("activity.activity.activityDetail") }}</span>
            </div>
            <lk-editor
              v-if="activityDetail?.content"
              class="QuillEditor"
              ref="quill"
              :options="{ readOnly: true, showTool: false, placeholder: '' }"
            ></lk-editor>
          </div>
          <div :class="['default-tab-item']" id="quota">
            <div class="tabdetail-title" v-if="firstTab != 'quota'">
            <span></span
            ><span>{{
                activityDetail.open ? `${t("activity.activity.quota_person")}` : `${t("activity.activity.actors_label")}`
              }}</span>
            </div>
            <Personnel
              :actors="activityDetail.actors"
              :details="activityDetail"
              :canEdit="false"
              :super="superType"
              @refresh="refresh"
              :open="activityDetail?.open"
              :tabValue="tabValue"
              :isFixed="isFixed"
              :firstTab="firstTab"
              :scrollFlag="scrollFlag"
              :personalCount="personalCount"
            />
          </div>
          <div :class="['default-tab-item']" id="files" v-if="activityDetail?.files?.files.length != 0">
            <div
              class="tabdetail-title"
              v-if="firstTab != 'files'"
              :style="{ marginBottom: activityDetail?.files?.files.length != 0 ? '8px' : '16px' }"
            >
              <span></span><span>{{ t("activity.activity.files_label") }}</span>
            </div>
            <files v-if="activityDetail?.id" :details="activityDetail" :canEdit="false" :super="superType" type="files" />
          </div>
          <div :class="['default-tab-item']" id="reports" v-if="activityDetail?.files?.reports.length > 0 || identityType === 'created'">
            <div
              class="tabdetail-title"
              v-if="firstTab != 'reports'"
              :style="{ marginBottom: activityDetail?.files?.reports.length != 0 ? '8px' : '16px' }"
            >
              <span></span><span>{{ t("activity.activity.reports") }}</span>
            </div>
            <reports :details="activityDetail" :canEdit="canEdit" :super="superType" type="reports" />
          </div>
          <div :class="['default-tab-item']" id="albums" v-if="activityDetail?.files?.albums.length > 0 || identityType === 'created'">
            <div
              class="tabdetail-title"
              v-if="firstTab != 'albums'"
              :style="{ marginBottom: activityDetail?.files?.albums.length != 0 ? '8px' : '16px' }"
            >
              <span></span><span>{{ t("activity.activity.albums") }}</span>
            </div>
            <albums :details="activityDetail" :canEdit="canEdit" :super="superType" type="albums" />
          </div>
        </div>
      </div>
    </div>

    <t-dialog
      v-model:visible="delDialog"
      theme="info"
      header="提示"
      class="dialog-classp24"
      :body="'活动已删除'"
      :closeBtn="null"
      :on-close="() => (delDialog = false)"
      :cancel-btn="null"
      :closeOnOverlayClick="false"
      @confirm="delConfirm"
    />

    <!-- <audio-add-members v-model:visible="shareVisible" :disabledCard="true" :activeCardId="activeCardId" :changeMenus="true" @confirm='confirmShare'/> -->
    <audio-add-members
      v-model:visible="shareVisible"
      :disabledCard="true"
      :showDropdownMenu="false"
      :activeCardId="activeCardId"
      :disable-list="[...activeCardId, ...(platformUuid ? [platformUuid] : [])]"
      :changeMenus="true"
      :showMyGroupMenu="true"
      @confirm="confirmShare"
    />
    <RegisterDialog v-model:visible="registerVisible" :registerRes="registerRes" @confirm="getDetailData" />
    <SignDialog
      v-model:visible="signVisible"
      :myId="myId"
      :teamId="Route.query.teamId"
      :activityId="Route.params.id"
      :involvedIdentityType="involvedIdentityType"
      :open="activityDetail?.open"
      :quota="activityDetail?.quota"
    />

    <cancelDialog v-model:visible="cancelVisible" @confirm="confirmCancel" />
    <AdvanceExtensionDialog
      v-model:visible="AdvanceVisible"
      :type="showAdvanceType"
      :data="activityItem"
      @confirm="confirmReSchedule"
    />

    <div v-if="activityDetail?.advanced?.enableComment" class="comment-container">
      <CommentList
        self-tag="主办方"
        :resource-id="id"
        :focus="!!forceTopCommentId"
        :user="commentUser"
        :force-top-comment-id="forceTopCommentId"
        :global-user="globalUser"
        :is-scrolling="scrollFlag"
      />
    </div>
  </div>
</template>

<script setup lang="ts" name="bench_activityDetail">
import { computed, nextTick, onActivated, onDeactivated, watch, onMounted, onUnmounted, reactive, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { ChevronRightIcon } from "tdesign-icons-vue-next";
import { useI18n } from "vue-i18n";
import { useActivityStore } from "@/views/activity/store";
import CommentList from '@/components/comment/CommentList.vue';
import {
  activityRes,
  addRegisters,
  delRegisters,
  extendsActivity,
  getActivityDetail,
  getActors,
  deleteActivity,
} from "@/api/activity";
import moment from "moment";
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { formatStatus, formatDetail, involvedStatus, ErrorResponseReason } from "@/views/activity/utils";
// import { QuillEditor, Quill, Delta } from "@vueup/vue-quill";
// import "@vueup/vue-quill/dist/vue-quill.snow.css";
// import { reg } from "@renderer/views/zhixing/note/quillEmbe";
import Personnel from "./components/Personnel.vue";
import files from "./components/Files.vue";
import reports from "./components/Files.vue";
import albums from "./components/Files.vue";
import { handelCardIds } from "@/views/contacts/utils";
import { cardIdType } from "@/views/identitycard/data";
import { openChat } from "@/utils/share";
import audioAddMembers from "@renderer/components/selectMember/audio-add-members.vue";
import AdvanceExtensionDialog from "@renderer/views/activity/components/AdvanceExtensionDialog.vue";
import cancelDialog from "@renderer/views/activity/components/cancelDialog.vue";
import RegisterDialog from "@renderer/views/activity/components/RegisterDialog.vue";
import SignDialog from "@renderer/views/activity/components/SignDialog.vue";
import { MsgShareType, sendApplicationMsg } from "@/utils/share";
import to from "await-to-js";
import { getOpenid, getPlatform, getProfilesInfo} from "@/utils/auth";

// reg();
const { t } = useI18n();

import LynkerSDK from '@renderer/_jssdk';
const { ipcRenderer } = LynkerSDK;
const Route = useRoute();
const router = useRouter();
const activityStore = useActivityStore();

const emits = defineEmits(["setActiveIndexAndName", "settabItem", "setActiveIndex", "deltabItem"]);
const profile = getProfilesInfo();

// 评论列表入参
const id = ref(Route.params.id as string);
const forceTopCommentId = Route.query.forceTopCommentId as string;
const commentUser = computed(() => {
  if (!identityItem.value) return {};
  const { openId, staffName, avatar } = identityItem.value;
  return { name: staffName, avatar, openId };
});
const globalUser = ref({ name: profile.title, avatar: profile.avatar, openId: profile.openid });

// 初始化完成标识
const initFinished = ref(false);

//是否显示发布至
const showSendTo = ref(false);
//发布至、活动提醒、通知参与人是否在活动应用、场景活动、数智工场模块
const showMiddleBox = ref(false);
watch(
  () => Route?.params,
  (newValue) => {
    console.log(newValue, "详情中的路由");
    if (Route.path.includes("/workBenchIndex/activityDetail") && newValue?.id) {
      getDetailData();
    }
  },
);
onActivated(() => {
  console.log("触发这里x", Route);
  let index = activityStore.tabList?.findIndex((e: any) => e.tag === Route.path);
  index !== -1 && emits("setActiveIndex", index);
  // if (Route.query?.isRefresh) {
  //   delete Route.query.isRefresh;
  // }
  if (startregisterTime.value) {
    initstartregisterTime();
  }
  initFinished.value = false;
  getDetailData();

  //动态初始化tab高亮
  let children = [...detailRef?.value?.children];
  tabValue.value = children[0].getAttribute("id");
  firstTab.value = children[0].getAttribute("id");
});
//活动已删除弹框的flag
const delDialog = ref(false);
// 监听来自发送进程的消息（删除活动变更）
ipcRenderer.on("delete-activity-item", () => {
  console.log(
    delDialog.value,
    Route.query,
    "详情中---------------接收到了删除活动的消息变更：delete-activity-item详情中---------------详情中---------------详情中---------------详情中---------------详情中---------------",
  );
  if (!delDialog.value) {
    //tab页签
    delDialog.value = true;
  }
});
//点击确认关闭tab页签或者独立窗口
const delConfirm = () => {
  delDialog.value = false;
  emits("deltabItem", activityStore.tabList.length - 1, true);
  ipcRenderer.invoke("close-dialog");
};

const props = defineProps({
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
  activeIndex: {
    type: Number,
    default: 0,
  },
});
//活动应用内--添加详情tab
const settabItem = () => {
  if (!activityStore.tabList?.find((e: any) => e.tag === Route.path)) {
    emits("settabItem", {
      path: Route.path ?? "/activity/activityDetail",
      name: Route.path,
      title: Route.query.title ?? t("activity.activity.activityDetail"),
      tag: Route.path ?? "activityDetail",
      type: 13,
      query: routeRuery.value,
    });
    emits("setActiveIndex", activityStore.tabList.length - 1);
  }
};
const delTabItem = () => {
  let index = activityStore.tabList.findIndex((e) => e.path === Route.path);
  emits("deltabItem", index, true);
}
//获取路由鞋带参数
const routeRuery = computed(() => {
  // let query = Route.fullPath?.split('?')[1];
  // let res = {}
  // query?.split('&')?.forEach(item => {
  //   let info = item?.split('=');
  //   res[info[0]] = info[1]
  // })
  // return {
  //   ...Route.query,
  //   ...res
  // }
  return Route.query;
});
//当前角色是什么权限（普通成员、管理员、超管、自定义角色）
const superType = ref("Normal");
const canEdit = computed(() => {
  return superType.value !== "Normal" || identityType.value === "created";
});
//当前详情是自己创建的还是被邀请的
const identityType = computed(() => {
  if (routeRuery.value?.identityType) {
    return routeRuery.value?.identityType;
  } else if (cardId.value == myId.value || activityDetail.value?.creator == localStorage.getItem("openid")) {
    return "created"; //自己创建
  } else {
    return "involved"; //被邀请
  }
});
//创建者的cardid
const cardId = computed(() => activityDetail.value?.cardId || activityDetail.value?.creator);
//自己的cardid
const myId = computed(() => routeRuery.value?.myCardId);
//判断自己cardid是什么身份（内部/外部/个人/平台）
const involvedIdentityType = computed(() => cardIdType(myId.value));
// 是否是平台身份
const isPlatForm = computed(() => involvedIdentityType.value === "platform");
// 在活动归属下的平台身份uuid
const platformUuid = getPlatform().find((platform) => platform.teamId === (routeRuery.value?.teamId || Route.query.teamId))?.uuid;

const openIdentityType = reactive({
  type: "card",
  text: t("activity.activity.contact"),
});

// 是否指定参与者
const isAppointed = computed(() => {
  return activityDetailAll.value?.actors?.some(
    (item) => item.role === "Appointed" && (item.cardId || item.openid) === currCardId.value,
  );
});

// 是否已报名
const isRegistered = computed(() => {
  return activityDetailAll.value?.actors?.some(
    (item) => item.role === "Registered" && (item.cardId || item.openid) === currCardId.value,
  );
});
//发布至bug
const sendToText = () => {
  const stages = activityDetail.value?.stages;
  const texts = [];
  if (stages.includes("WORKSHOP")) {
    texts.push(t("activity.activity.Factory"));
  }
  if (stages.includes("PLATFORM")) {
    texts.push(t("activity.activity.Number"));
  }
  if (stages.includes("SQUARE")) {
    texts.push(t("activity.activity.Square"));
  }
  return texts.join("、");
};
//三个响应按钮
const showResponse = computed(() => {
  let status = formatStatus(activityDetailAll.value, "activityTypeClass");
  return isAppointed.value && status === "noStart";
});
const showEntryText = computed(() => {
  let status = formatStatus(activityDetail.value, "activityTypeClass");
  let registerStatus = formatStatus(activityDetail.value, "activityTypeClass", "register");
  if (status === "canceled") {
    return t("activity.activity.canceled");
  }
  if (status === "ended") {
    return t("activity.activity.ended");
  }
  if (status === "ongoing") {
    return t("activity.activity.ongoing");
  }
  if (status === "noStart") {
    if (registerStatus === "noStart") {
      if (activityDetail.value?.register?.startTime) {
        initstartregisterTime();
        return startregisterTime.value > 0 ? t("activity.activity.startregister") : t("activity.activity.register");
      } else {
        return isRegistered.value ? t("activity.activity.cancalregister") : t("activity.activity.register");
      }
    } else if (registerStatus === "ended") {
      return t("activity.activity.registered");
    } else {
      return isRegistered.value ? t("activity.activity.cancalregister") : t("activity.activity.register");
    }
  }
  return isRegistered.value ? t("activity.activity.cancalregister") : t("activity.activity.register");
});
const disabledEntry = computed(() => {
  let status = formatStatus(activityDetail.value, "activityTypeClass");
  let registerStatus = formatStatus(activityDetail.value, "activityTypeClass", "register");
  if (status === "canceled") {
    return true;
  }
  if (status === "ended") {
    return true;
  }
  if (status === "ongoing") {
    return true;
  }
  if (status === "noStart") {
    if (registerStatus === "noStart") {
      if (activityDetail.value?.register?.startTime) {
        return startregisterTime.value > 0;
      } else {
        return false;
      }
    } else if (registerStatus === "ended") {
      return true;
    } else {
      return false;
    }
  }
  return false;
});

let timer: number | null = null;
const startregisterTime = ref(0);
//立即报名视图显示的时间
const formatstartregisterTime = computed(() => {
  const durationTime = moment.duration(startregisterTime.value, "seconds");
  const days = durationTime.days();
  const hours = durationTime.hours();
  const minutes = durationTime.minutes();
  const seconds = durationTime.seconds();
  return days > 0
    ? `${days}天${hours}时${minutes}分`
    : `${hours < 10 ? "0" + hours : hours}:${minutes < 10 ? "0" + minutes : minutes}:${
        seconds < 10 ? "0" + seconds : seconds
      }`;
});
const initstartregisterTime = () => {
  startregisterTime.value = moment.unix(activityDetail.value?.register?.startTime).diff(moment(), "seconds");
  timer = window.setInterval(() => {
    // startregisterTime.value--;
    startregisterTime.value = moment.unix(activityDetail.value?.register?.startTime).diff(moment(), "seconds");
    if (startregisterTime.value <= 0) {
      window.clearInterval(timer);
      // getDetailData();
    }
  }, 1000);
};
//卸载
onUnmounted(() => {
  if (timer) {
    window.clearInterval(timer);
    timer = null;
  }
  if (time) {
    clearTimeout(time);
  }
  // 记得在组件销毁时移除事件监听器，避免内存泄漏
  window.removeEventListener("scroll", detailScroll);
});
//未激活
onDeactivated(() => {
  if (timer) {
    window.clearInterval(timer);
    timer = null;
  }
  isFixed.value = false;
});
//按钮集成新数据被调用（对应按钮对应方法）
const formatDetailStatus = computed(() => {
  if (activityDetail.value) {
    return formatStatus({ ...activityDetail.value, open: false }, "showDetailButton");
  }
  return [];
});
// 整理全部按钮列表
const allButtonList = computed(() => {
  // 初始化按钮列表
  const buttons = [
    // 自己创建的按钮逻辑
    ...(!isPlatForm.value && identityType.value === "created"
      ? formatDetailStatus.value.map((item) => ({
          text: item.text,
          click: () => formatStatusClickIcon[item.click](activityDetail.value),
          theme: item.click === "rowEdit" ? "primary" : "default",
        }))
      : []),

    // 被邀请的或平台身份的创建人逻辑
    ...((isPlatForm.value && identityType.value === "created") ||
    (identityType.value === "involved" && showResponse.value)
      ? (reply.value === 0
          ? [
              {
                text: t("activity.activity.Confirmed"),
                click: () => activityResponse(1),
                theme: reply.value === 1 ? "primary" : "primary",
              },
              {
                text: t("activity.activity.Received"),
                click: () => activityResponse(2),
                theme: reply.value === 2 ? "primary" : "default",
              },
              {
                text: t("activity.activity.Denied"),
                click: () => activityResponse(3),
                theme: reply.value === 3 ? "primary" : "default",
              },
            ]
          : [
              {
                text: t("activity.activity.Denied"),
                onChange: activityResponse,
                theme: reply.value === 3 ? "primary" : "default",
                component: "select",
                options: [
                  {
                    key: "1",
                    value: 1,
                    text: t("activity.activity.Confirmed"),
                    display: reply.value === 1 ? "none" : "flex",
                  },
                  {
                    key: "2",
                    value: 2,
                    text: t("activity.activity.Received"),
                    display: reply.value === 2 ? "none" : "flex",
                  },
                  {
                    key: "3",
                    value: 3,
                    text: t("activity.activity.Denied"),
                    display: reply.value === 3 ? "none" : "flex",
                  },
                ],
              },
            ]
        )
      : []),

    // 入口按钮
    ...(!isAppointed.value || (showEntryText.value === t('activity.activity.canceled') || showEntryText.value === t('activity.activity.ended') || showEntryText.value === t('activity.activity.ongoing'))
      ? [
        {
          text:
            showEntryText.value +
            (showEntryText.value === t("activity.activity.startregister") && startregisterTime.value > 0
              ? formatstartregisterTime.value
              : ""),
          click: activityEntry,
          theme: "primary",
          class: showEntryText.value === t("activity.activity.startregister") ? "startregisterButton" : "",
          disabled: disabledEntry.value,
        },
      ]
      : []),

    // 分享按钮
    ...(activityDetail.value.open
      ? [
          {
            key: "share",
            text: t("activity.activity.share"),
            click: () => rowShare(activityDetail.value),
            theme: "default",
          },
        ]
      : []),

    // 签到签退按钮
    ...(involvedIdentityType.value !== "platform" && (identityType.value === "created" || superType.value !== "Normal")
      ? [
          {
            key: "checkInOut",
            text: t("activity.activity.checkInOut"),
            click: () => (signVisible.value = true),
            theme: "default",
          },
        ]
      : []),
  ];

  // 检查按钮优先级并调整位置
  const shareButtonIndex = buttons.findIndex((button) => button.key === "share");
  const checkInOutButtonIndex = buttons.findIndex((button) => button.key === "checkInOut");

  if (shareButtonIndex > -1) {
    // 如果有分享按钮，确保它在第二个位置
    const [shareButton] = buttons.splice(shareButtonIndex, 1);
    buttons.splice(1, 0, shareButton);
    if (checkInOutButtonIndex > -1) {
      // 如果有签到签退按钮，将其放在第三位
      const [checkInOutButton] = buttons.splice(checkInOutButtonIndex, 1);
      buttons.splice(2, 0, checkInOutButton);
    }
  } else if (checkInOutButtonIndex > -1) {
    // 如果没有分享按钮，但有签到签退按钮，将签到签退放在第二位
    const [checkInOutButton] = buttons.splice(checkInOutButtonIndex, 1);
    buttons.splice(1, 0, checkInOutButton);
  }

  return buttons;
});
const activityItem = ref({});
const showAdvanceType = ref("Advance");
const shareVisible = ref(false);
const cancelVisible = ref(false);
const AdvanceVisible = ref(false);
const registerVisible = ref(false);
const signVisible = ref(false);
const registerRes = ref("success");
const inSquare = computed(() => Route.path.includes("/square-activity/activityDetail"));
const currCardId = computed(() => (inSquare.value ? myOpenid.value : (myId.value as string)));
//跳转聊天框
const openIM = async (row) => {
  if(!activityDetail?.value.open){
    await activityStore.checkIsInGroup(activityDetail.value.teamId, true, delTabItem);
    await checkISInActivity(delTabItem);
  }

  if (!activityDetail.value?.chatId?.groupId && !row?.cardId) return MessagePlugin.info(t("activity.activity.noChat"));
  openChat({ main: currCardId.value, peer: row.cardId, rela: "TEMPORARY", group: activityDetail.value?.chatId?.groupId });
};

//分享的确认事件
const confirmShare = async (val) => {
  let data = JSON.parse(JSON.stringify(activityItem.value));
  sendApplicationMsg(MsgShareType.activity_card, data, val);
};
//取消
const confirmCancel = async (val) => {
  await activityStore.checkIsInGroup(activityDetail.value.teamId, true, delTabItem);

  extendsActivity(
    {
      ...val,
      id: activityItem.value.id,
    },
    activityItem.value.id,
  ).then((res) => {
    if (res?.status === 200) {
      MessagePlugin.success(t("activity.activity.success_tip"));
      getDetailData();
      cancelVisible.value = false;
    }
  });
};
//改期
const confirmReSchedule = async (val) => {
  await activityStore.checkIsInGroup(activityDetail.value.teamId, true, delTabItem);

  extendsActivity(
    {
      ...val,
      id: activityItem.value.id,
    },
    activityItem.value.id,
  ).then((res) => {
    if (res?.status === 200) {
      MessagePlugin.success(t("activity.activity.success_tip"));
      getDetailData();
      AdvanceVisible.value = false;
    }
  });
};
//编辑
const rowEdit = async (row) => {
  await activityStore.checkIsInGroup(activityDetail.value.teamId, true, delTabItem);

  if (Route?.path.includes("/workBenchIndex")) {
    router.push({
      path: "/workBenchIndex/activityEdit/" + row.id,
      query: {
        ...routeRuery.value,
        from: "activityDetail",
      },
    });
    ipcRenderer.invoke("set-work-bench-tab-item", {
      activeIcon: "workshop",
      icon: "workshop",
      path: "/workBenchIndex/activityEdit/" + row.id,
      path_uuid: "workBench",
      name: "bench_activityEdit",
      title: t("activity.activity.editActivity"),
      type: 13,
      query: {
        ...routeRuery.value,
        from: "activityDetail",
        team_id: window.localStorage.getItem("workBenchTeamid"),
      },
    });
    return;
  }
  console.log(row, Route.path);
  if (Route.path.indexOf("layoutActivity") === -1) {
    router.push({
      path: "/activity/activityEdit/" + row.id,
      query: {
        ...routeRuery.value,
        from: "activityDetail",
      },
    });
  } else {
    router.push({
      path: "/layoutActivity/activityEditLayout/" + row.id,
      query: {
        ...routeRuery.value,
        from: "activityDetailLayout",
      },
    });
  }
};
const myOpenid = computed(() => getOpenid());
const activeCardId = ref([]);
//点击分享
const rowShare = async (row) => {
  if (!row.open) {
    return MessagePlugin.warning(t("activity.activity.shareTip"));
  }
  activeCardId.value = [currCardId.value];
  activityItem.value = row;
  shareVisible.value = true;
};
const rowCancel = async (row) => {
  await activityStore.checkIsInGroup(activityDetail.value.teamId, true, delTabItem);

  activityItem.value = row;
  cancelVisible.value = true;
};
const rowReSchedule = async (row) => {
  await activityStore.checkIsInGroup(activityDetail.value.teamId, true, delTabItem);

  const execute = () => {
    activityItem.value = row;
    showAdvanceType.value = "reSchedule";
    AdvanceVisible.value = true;
  }

  // 如果有人已经报名，或者有指定参加人员选择了【参与、暂定、拒绝】，显示确认弹窗
  const hasRegister = row.actors.some(
    (actor) =>
      actor.role === "Registered" ||
      (actor.role === "Appointed" && actor.status !== "Unknown" && actor.openid !== row.creator),
  );
  if (hasRegister) {
    const confirmDia = DialogPlugin.confirm({
      header: t("activity.activity.tip"),
      theme: "info",
      body: t("activity.activity.reScheduleConfirm"),
      onConfirm: async () => {
        confirmDia.hide();
        execute();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  } else {
    execute();
  }

};
//点击列表删除
const rowDel = async (row) => {
  await activityStore.checkIsInGroup(activityDetail.value.teamId, true, delTabItem);

  const confirmDia = DialogPlugin.confirm({
    header: t("activity.activity.delete"),
    theme: "danger",
    body: t("activity.activity.delete_activity_tip"),
    closeBtn: null,
    confirmBtn: t("activity.activity.confirm"),
    onConfirm: async () => {
      confirmDia.hide();
      let res = await deleteActivity(row.id);
      if (res?.status === 200) {
        MessagePlugin.success(t("activity.activity.success_tip"));
      }
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};
//底部显示按钮对应的事件
const formatStatusClickIcon = {
  rowEdit,
  rowShare,
  rowReSchedule,
  rowCancel,
  rowDel,
};

const reply = ref(0);
//活动响应事件
const activityResponse = async (val) => {
  await activityStore.checkIsInGroup(activityDetail.value.teamId, true, delTabItem);
  await checkISInActivity(delTabItem);

  let activityId = Route.params?.id;
  let data = {
    activityId: activityId,
    reply: val,
  };
  if (involvedIdentityType.value !== "personal") {
    data.cardId = myId.value;
    data.teamId = routeRuery.value?.teamId || Route.query.teamId;
  }
  activityRes(activityId, data).then((res) => {
    if (res?.status === 200) {
      console.log(res);
      reply.value = val;
      getDetailData();
    }
  });
};

const activityEntry = async () => {

  let activityId = Route.params?.id;
  let data = {
    id: activityId,
  };
  if (involvedIdentityType.value === "personal") {
    data.openid = myId.value;
  } else {
    if (inSquare.value) {
      data.openid = currCardId.value;
    } else {
      data.cardId = myId.value;
    }
    data.teamId = inSquare.value ? '' : routeRuery.value?.teamId || Route.query.teamId; // 来自广场的活动以个人身份报名
  }
  if(isRegistered.value){
    const confirmDia = DialogPlugin.confirm({
      header: t("activity.activity.tip"),
      theme: "info",
      body: "确定取消报名？",
      onConfirm: async () => {
        let [err, res] = await to(delRegisters(activityId, data));
        if (err) {
          const errReason = err.response?.data?.metadata?.reason;
          if (errReason === ErrorResponseReason.Full) {
            if (!isRegistered.value) {
              registerVisible.value = true;
              registerRes.value = "Full";
            }
          }
        }
        if (res?.status === 200) {
          getDetailData();
          if (!isRegistered.value) {
            registerVisible.value = true;
            registerRes.value = "success";
          }
        }
        confirmDia.hide();
      },
      onCancel: () => {
        confirmDia.hide();
      },
      onCloseBtnClick: () => {
        confirmDia.hide();
      },
    });
  } else {
    let [err, res] = await to(addRegisters(data));
    console.log(err, res);
    if (err) {
      const errReason = err.response?.data?.metadata?.reason;
      if (errReason === ErrorResponseReason.Full) {
        if (!isRegistered.value) {
          registerVisible.value = true;
          registerRes.value = "Full";
        }
      }
    }
    if (res?.status === 200) {
      getDetailData();
      if (!isRegistered.value) {
        registerVisible.value = true;
        registerRes.value = "success";
      }
    }
  }
};

const identityItemButton = async () => {
  openIdentityType.type = "card";
  openIdentityType.text = t("activity.activity.contact");
};

const identityItem = ref({} as any);
const activityDetail = ref({});
const activityDetailAll = ref({});
const personalCount = reactive({
  invited: 0,
  confirmed: 0,
  received: 0,
  denied: 0,
  unknown: 0,
});
const nowMoudle = ref();
//获取详情数据（所有的）
const getDetailData = () => {
  console.log(Route, "Route----------Route----------Route----------Route----------Route----------");
  let moudle =
    localStorage.getItem("LeftBarpath_uuid") == "workBench" ||
    localStorage.getItem("LeftBarpath_uuid") == "activities" ||
    localStorage.getItem("LeftBarpath_uuid") == "message";
  nowMoudle.value = moudle;
  const activityId = Route.params?.id != ":id" ? Route.params?.id : Route.query?.id;
  getActivityDetail(activityId, "All")
    .then(async (res) => {
      if (res.status === 200) {
        activityDetail.value = res.data;
        activityDetailAll.value = res.data;
        console.log("活动详情的额数据--------------------------", res.data);
        nextTick(() => {
          activityDetail.value?.content && getQuill(activityDetail.value);
        });
        const ids = inSquare.value ? activityDetail.value?.contact : activityDetail.value?.cardId || activityDetail.value?.creator;
        let resCardIds = await handelCardIds([ids]);
        identityItem.value = resCardIds?.[0] ?? {};
        console.log(
          res?.data?.count,
          personalCount,
          res?.data?.count,
          identityItem.value,
          "identityItem.value ------------identityItem.value ",
        );
        identityItemButton();
        reply.value = involvedStatus.get(
          activityDetail.value.actors?.find((item) => (item.cardId || item.openid) === myId.value)?.status,
        )?.code;
        // }
        console.log(
          activityDetail.value.actors?.find((item) => (item.cardId || item.openid) === myId.value),
          "statttttt",
        );

        console.log(myId.value, "myIdmyIdmyId");

        console.log(reply.value, "reply.valuereply.value");

        //活动参与人个状态数据
        personalCount.invited = res?.data?.count?.invited;
        personalCount.confirmed = res?.data?.count?.confirmed;
        personalCount.received = res?.data?.count?.received;
        personalCount.denied = res?.data?.count?.denied;
        personalCount.unknown = res?.data?.count?.unknown;
        //如果是详情公开的对参与人员进行筛选展示
        if (res.data?.open) {
          opengetDetailData(activityId);
        } else {
          // 非公开活动无需再请求详情接口则初始化完成
          initFinished.value = true;
        }
        //是否显示发布至
        res.data.stages.indexOf("UNDEFINED") == -1 ? (showSendTo.value = true) : (showSendTo.value = false);
        // showSendTo.value = res.data.stages.includes('UNDEFINED');
        //发布至、活动提醒、通知参与人是否在活动应用、场景活动、数智工场模块
        localStorage.getItem("LeftBarpath_uuid") == "activities" ||
        localStorage.getItem("LeftBarpath_uuid") == "workBench" ||
        localStorage.getItem("LeftBarpath_uuid") == "message"
          ? (showMiddleBox.value = true)
          : (showMiddleBox.value = false);
      }
    })
    .catch((e) => {
      delDialog.value = true;
      console.log(e, "错误信息-------------------");
    });
};
//再拉取一次详情接口
const opengetDetailData = (activityId) => {
  getActivityDetail(activityId, "Confirmed")
    .then(async (res) => {
      if (res.status === 200) {
        activityDetail.value = res.data;
        console.log("活动详情的额数据--------------------------", res.data);
        nextTick(() => {
          activityDetail.value?.content && getQuill(activityDetail.value);
        });
        // if (identityType.value === 'involved') {
        const ids = inSquare.value
          ? activityDetail.value?.contact
          : activityDetail.value?.cardId || activityDetail.value?.creator;
        let resCardIds = await handelCardIds([ids]);
        identityItem.value = resCardIds?.[0] ?? {};
        console.log(res?.data?.count, identityItem.value, "identityItem.value ------------identityItem.value ");
        identityItemButton();
        //活动参与人个状态数据
        personalCount.invited = res?.data?.count?.invited;
        personalCount.confirmed = res?.data?.count?.confirmed;
        personalCount.received = res?.data?.count?.received;
        personalCount.denied = res?.data?.count?.denied;
        personalCount.unknown = res?.data?.count?.unknown;

        initFinished.value = true;
      }
    })
    .catch((e) => {
      delDialog.value = true;
      console.log(e, "错误信息-------------------");
    });
};

const refresh = () => {
  getDetailData();
};

const tabValue = ref("content");
const quill = ref(null);
const qillContent = ref();
const editorOptions = reactive({
  modules: {
    toolbar: false,
  },
});
const getQuill = (data) => {
  console.log(quill.value, data);
  // 解析delta
  const delta = data.content ? JSON.parse(data.content) : [];
  quill.value.renderContent({ ops: delta }, false);
};

//滚动事件
const isFixed = ref(false); //是否吸顶
const activityRef = ref(null); //整个详情页面
const boxHeight = ref(null); //tab大盒子
const detailRef = ref(null); //tab中详情盒子
const scrollFlag = ref(false);
const detailScroll = () => {
  // console.log('gundongla-----------gundongla------gundongla')
  scrollFlag.value = true;
  // 当页面滚动超过某个阈值时，将 isFixed 设置为 true，否则设置为 false
  if (activityRef.value.scrollTop > 490) {
    isFixed.value = true;
  } else {
    isFixed.value = false;
  }
  //滚动时切换tab高亮
  let children = [...detailRef.value.children];
  let tabs = [];
  children.forEach((item, index) => {
    if (activityRef.value.scrollTop >= item.offsetTop - 120) {
      tabValue.value = item.getAttribute("id");
    }
    // console.log(activityRef.value.scrollTop,item.offsetTop-120)
  });
};
//点击tab栏锚点跳转
const scrollToSection = (section) => {
  if (activityRef.value.scrollTop <= 491) {
    activityRef.value.scrollTo({
      top: 491,
      behavior: "smooth",
    });
    if (boxHeight.value.offsetTop >= 632) {
      isFixed.value = true;
    } else {
      tabValue.value = section;
    }
  }
  const element = document.getElementById(section);
  console.log(boxHeight.value.offsetTop, element.offsetHeight, element.offsetTop, "element.offsetTop");
  if (element) {
    activityRef.value.scrollTo({
      top: element.offsetTop - 107,
      behavior: "smooth",
    });
  }
  let timer;
  clearTimeout(timer);
  timer = setTimeout(() => {
    scrollFlag.value = false;
  }, 1000);

  // tabValue.value=section
};
let time;
const firstTab = ref("");

// 重新查一次活动详情，判断该用户是否还在活动邀请人列表中
const checkISInActivity = async (callback) => {
  if(identityType.value === 'created'){
    // 创建人不用处理（创建归属的组织已经在调用此方法之前用checkIsInGroup校验）
    return Promise.resolve();
  }
  const activityId = Route.params?.id != ":id" ? Route.params?.id : Route.query?.id;
  const res = await getActivityDetail(activityId, "All");
  if(res.data.actors.some(actor => (actor.cardId || actor.openid) === myId.value)){
    return Promise.resolve();
  }

  const alertDia = DialogPlugin.alert({
    header: t("activity.activity.tip"),
    theme: "warning",
    body: t("activity.activity.outActivity"),
    closeBtn: null,
    confirmBtn: t("activity.activity.confirm"),
    onConfirm: () => {
      alertDia.hide();
      callback && callback();
    },
    onClose: () => {
      alertDia.hide();
      callback && callback();
    }
  });
  return Promise.reject();
}

onMounted(() => {
  console.log(localStorage.getItem("LeftBarpath_uuid"), Route, "onMounted=======");
  if (localStorage.getItem("LeftBarpath_uuid") == "activities") {
    settabItem();
  }
  getDetailData();
  // 在 Electron 渲染进程中，可以使用 window 对象来监听窗口滚动事件
  window.addEventListener("scroll", detailScroll);
  //动态初始化tab高亮
  time = setTimeout(() => {
    let children = [...detailRef?.value?.children];
    tabValue.value = children[0].getAttribute("id");
    firstTab.value = children[0].getAttribute("id");
  }, 300);
});
</script>

<style lang="less" scoped>
.fixed-nav {
  position: fixed;
  top: 40px;
  z-index: 999;
  background-color: #fff;
  width: 872px;
  /* 添加其他样式以适应你的需求 */
}
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}

.noDataBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 201px;
    height: 201px;
  }
}
.activity {
  width: 100%;
  height: calc(100% - 40px);
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  padding: 12px 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: overlay;
}
.header-box {
  width: 872px;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #fff);
  display: flex;
  padding: 16px 24px;
  align-items: flex-start;
  // height: 332px;
  margin-top: 12px;
  gap: 24px;
  .assetUrl {
    width: 400px;
    height: 240px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    :deep(.t-image) {
      //max-height: 100%;
      //max-width: 100%;
      //width: auto;
      //height: auto;
    }
  }
  .title {
    margin-bottom: 16px;
    color: var(--text-kyy-color-text-1, #1a2139);

    /* kyy_fontSize_4/bold */
    font-family: PingFang SC;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px; /* 144.444% */

    .t-tag {
      margin-right: 8px;
      display: inline-block;
      height: 26px;
      padding: 0px 6px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_brand, #eaecff);
      color: var(--kyy_color_tag_text_brand, #4d5eff);
      text-align: center;

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px; /* 157.143% */
    }
  }
  .duration {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    img {
      width: 20px;
      height: 20px;
      margin-right: 12px;
    }
    .t-tag {
      margin-left: 8px;
      height: 20px;
      min-height: 20px;
      max-height: 20px;
      padding: 0 4px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_warning, #ffe5d1);
      color: var(--kyy_color_tag_text_warning, #fc7c14);
      text-align: center;

      /* kyy_fontSize_1/regular */
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
    }

    .status-box {
      .noStart {
        display: flex;
        width: 58px;
        height: 24px;
        min-height: 24px;
        max-height: 24px;
        padding: 1px 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: var(--kyy_radius_tag_full, 999px);
        background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
        color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
        text-align: right;

        /* kyy_fontSize_2/bold */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
      .ongoing {
        display: flex;
        width: 58px;
        height: 24px;
        min-height: 24px;
        max-height: 24px;
        padding: 1px 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: var(--kyy_radius_tag_full, 999px);
        background: var(--kyy_color_tag_bg_warning, #ffe5d1);
        color: var(--kyy_color_tag_text_warning, #fc7c14);
        text-align: right;

        /* kyy_fontSize_2/bold */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
      .ended {
        display: flex;
        width: 58px;
        height: 24px;
        min-height: 24px;
        max-height: 24px;
        padding: 1px 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: var(--kyy_radius_tag_full, 999px);
        background: var(--kyy_color_tag_bg_success, #e0f2e5);
        color: var(--kyy_color_tag_text_success, #499d60);
        text-align: right;

        /* kyy_fontSize_2/bold */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
      .canceled {
        display: flex;
        width: 58px;
        height: 24px;
        min-height: 24px;
        max-height: 24px;
        padding: 1px 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: var(--kyy_radius_tag_full, 999px);
        background: var(--kyy_color_tag_bg_gray, #eceff5);
        color: var(--kyy_color_tag_text_gray, #516082);
        text-align: right;

        /* kyy_fontSize_2/bold */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
      .tag {
        margin-left: 8px;
        width: auto;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: 0 4px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }
  .location {
    margin-bottom: 12px;
    display: flex;
    img {
      width: 20px;
      height: 20px;
      margin-right: 12px;
    }
  }
  .reminders {
    margin-bottom: 12px;
    display: flex;
    img {
      width: 20px;
      height: 20px;
      margin-right: 12px;
    }
    .reminders-flex {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      .t-tag {
        display: flex;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: 0px 4px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_gray, #eceff5);
        color: var(--kyy_color_tag_text_gray, #516082);
        text-align: center;
        margin: 4px;
        /* kyy_fontSize_1/regular */
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
    }
  }
  .channels {
    margin-bottom: 12px;
    display: flex;
    img {
      width: 20px;
      height: 20px;
      margin-right: 12px;
    }
  }
  .chatId {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    img {
      width: 20px;
      height: 20px;
      margin-right: 12px;
    }
    .line-1 {
      width: 0;
      flex: 1;
      display: flex;
      align-items: center;
      .overHiddenName {
        max-width: calc(100% - 120px);
        padding-left: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .clickViewButton {
        margin-left: 12px;
        height: 22px;
        padding: 0;
        color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff) !important;
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        :deep(.t-button__suffix) {
          margin-left: 0;
        }
      }
    }
  }
  .headerBtn {
    margin-top: 16px;
    display: flex;
    gap: 12px;

    .t-button {
      padding: 0;
      width: 80px;
      height: 32px;

      :deep(.startregisterButton) {
        padding: 0 16px;
        .t-button__text {
          color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
          text-align: center;
          font-size: 10px;
          font-style: normal;
          font-weight: 400;
          line-height: 12px; /* 120% */
          flex-direction: column;
        }
      }
    }
  }

  .text-color {
    color: var(--text-kyy-color-text-1, #1a2139);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

.contact-box {
  width: 400px;
  display: flex;
  padding: 16px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  .svg {
    display: flex;
  }
  .content {
    margin-left: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .name {
    color: #000;
    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .org {
    display: flex;
    height: 20px;
    min-height: 20px;
    max-height: 20px;
    padding: 2px 4px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_warning, #ffe5d1);
    color: var(--kyy_color_tag_text_warning, #fc7c14);
    text-align: center;
    /* kyy_fontSize_1/regular */
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
  .button {
    width: 77px;
    color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4d5eff) !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.middle-box {
  width: 872px;
  margin-top: 12px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  display: flex;
  align-items: flex-start;
  gap: 12px;
  border-radius: 8px;
  > div {
    flex-shrink: 0;
    width: 430px;
    height: auto;
    display: flex;
    padding: 16px 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    flex: 1 0 0;
    align-self: stretch;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_default, #fff);
  }
  .remind {
    > div:nth-child(1) {
      display: flex;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-align: center;

      /* kyy_fontSize_2/regular */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      img {
        vertical-align: middle;
        margin-right: 8px;
      }
    }

    > div:nth-child(2) {
      color: var(--text-kyy_color_text_2, #516082);
      text-align: center;

      /* kyy_fontSize_2/regular */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .reminders-flex {
      display: flex;
      flex-wrap: wrap;
      .t-tag {
        display: flex;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: 0 4px !important;
        justify-content: center;
        align-items: center;
        margin: 0 8px 8px 0;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_gray, #eceff5);
        color: var(--kyy_color_tag_text_gray, #516082);
        text-align: center;
        /* kyy_fontSize_1/regular */
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
      .t-tag:last-child {
        margin: 0;
      }
    }
  }
  .people {
    > div:nth-child(1) {
      display: flex;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-align: center;

      /* kyy_fontSize_2/regular */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      img {
        vertical-align: middle;
        margin-right: 8px;
      }
    }
    > div:nth-child(2) {
      color: var(--text-kyy_color_text_2, #516082);
      text-align: center;

      /* kyy_fontSize_2/regular */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
  }
}
.borderButton {
  border: 1px solid var(--color-button-secondary-brand-kyy-color-button-secondary-brand-border-dedault, #4d5eff) !important;
  background: var(--color-button-secondary-brand-kyy-color-button-secondray-border-bg-default, #eaecff) !important;
}

.checkInOutButton {
  color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4d5eff) !important;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}

.tab-box {
  width: 872px;
  margin-top: 12px;
  // min-height: calc(100% - 256px);
  height: auto;
  border-radius: 12px;
  background: var(--bg-kyy_color_bg_light, #fff);
  padding: 0px 24px 12px 24px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  // padding-bottom:360px;
}
.notshow {
  display: none !important;
}
.tab-list {
  width: 828px;
  border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  display: flex;
  align-items: flex-start;
  gap: 44px;
  height: 56px;
  align-self: stretch;
  .default-tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    .t-button--variant-text.t-button--theme-default:hover {
      background-color: transparent !important;
      color: var(--brand-kyy-color-brand-default, #4d5eff) !important;
    }
    .t-button {
      height: 54px;
      padding: 0;
      color: var(--kyy_color_tabbar_item_text, #1a2139);
      text-align: center;

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px; /* 157.143% */
      border: none !important;
    }
    .tab-item-border {
      width: 28px;
      height: 1.5px;
      border-radius: 1.5px;
      background: transparent;
    }
  }
  .active-tab-item {
    .t-button {
      font-weight: 600;
      color: var(--brand-kyy-color-brand-default, #4d5eff) !important;
    }
    .tab-item-border {
      width: 16px;
      height: 1.5px;
      flex-shrink: 0;
      border-radius: 1.5px;
      background: var(--brand-kyy-color-brand-default, #4d5eff);
    }
  }
}

.tab-detail {
  overflow: auto;
  margin-top: 20px;
  padding-bottom: 20px;
  :deep(.QuillEditor) {
    width: 816px;
    //max-width: calc(100% - 96px);
    max-height: calc(100% - 400px);
    margin: auto;
    border: none;
    color: var(--text-kyy-color-text-1, #1a2139);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    .ql-clipboard {
      display: none;
    }
    .ql-editor {
      padding: 0;
    }
  }
  :deep(.ql-container) {
    position: static !important;
    height: auto;
  }
}
.tab-details {
  margin-top: 86px;
  overflow: overlay;
}
.chatBox {
  width: 872px;
  text-align: center;
  display: flex;
  padding: 8px 24px;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  background: var(--kyy_color_alert_bg_bule, #eaecff);
  color: var(--kyy_color_alert_text, #1a2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
  .overHiddenName {
    max-width: calc(100% - 140px);
    padding-left: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .clickViewButton {
    height: 22px;
    padding: 0;
    color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff) !important;
    background-color: transparent !important;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;

    /* 157.143% */
    :deep(.t-button__suffix) {
      margin-left: 0;
    }
  }
}
.chatBox-reason {
  width: 872px;
  text-align: center;
  display: flex;
  padding: 8px 24px;
  justify-content: flex-start;
  border-radius: 8px;
  background: var(--kyy_color_alert_bg_bule, #eaecff);
  color: var(--kyy_color_alert_text, #1a2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
  .overHiddenName {
    max-width: calc(100% - 70px);
    padding-left: 8px;
    text-align: left;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
  }

  .clickViewButton {
    height: 22px;
    padding: 0;
    color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff) !important;
    background-color: transparent !important;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;

    /* 157.143% */
    :deep(.t-button__suffix) {
      margin-left: 0;
    }
  }
}
.blue {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 2px 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  text-align: center;
  /* kyy_fontSize_1/regular */
  font-family: PingFang SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  background: rgb(228, 245, 254);
  color: rgb(33, 172, 250);
}
.tabdetail-title {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  > span:nth-child(2) {
    color: var(--text-kyy_color_text_1, #1a2139);
    /* kyy_fontSize_3/bold */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
  > span:nth-child(1) {
    display: inline-block;
    margin-right: 8px;
    width: 3px;
    height: 16px;
    border-radius: 8px;
    background: var(--brand-kyy_color_brand_default, #4d5eff);
  }
}

.skeleton-container{
  margin-top: 12px;
  width: 872px;
  height: calc(100vh - 68px);
  background: #fff;
  border-radius: 8px;
  padding: 24px;
}

.comment-container {
  width: 872px;
  background-color: #fff;
  margin-top: 12px;
  padding-bottom: 12px;
}
</style>
