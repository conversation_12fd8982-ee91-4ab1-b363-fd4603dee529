<template>
  <t-drawer
    v-model:visible="visible"
    class="addChannel2"
    header="推广渠道"
    size="472px"
    :close-btn="true"
    :footer="null"
  >
    <template #closeBtn>
      <iconpark-icon name="iconerror" class="iconorientation icon20" />
    </template>
    <div class="sr-box">
      <t-table
        :data="tableData"
        :columns="columns"
        row-key="orderNumber"
        :pagination="pagination.total > 10 ? pagination : null"
        @page-change="onPageChange"
      >
        <template #name="{ row }">
          <div class="goods-name">
            <div v-if="row.isSelf" class="name-tag">自</div>
            {{ row.teamName }}
          </div>
        </template>
        <template #channel="{ row }">
          <div class="chbox">
            <img :src="channelIcon[row.channelType]" alt="渠道图片" class="channel-image" />
            <div :class="`text${row.channelType}`">{{ channelTypeText[row.channelType] }}</div>
          </div>
        </template>
        <template #empty>
          <div class="empty-box">
            <REmpty :name="'no-data'" :tip="'暂无数据'" />
          </div>
        </template>
      </t-table>
    </div>
  </t-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { listPromotionChannels } from '../apis';
import { REmpty } from '@rk/unitPark';

const visible = ref(false);
const tableData = ref([]);
const pagination = ref({
  pageSize: 10,
  current: 1,
  total: 0,
});
const openDrwer = (id) => {
  spuId.value = id;
  getData();
  visible.value = true;
};
const channelIcon: any = {
  member: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/vip.png',
  cbd: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/cbd.png',
  association: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/community.png',
  government: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/type1.png',
  uni: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/community.png',
};
const channelTypeText = {
  member: '数字商协',
  cbd: '数字CBD',
  association: '数字社群',
  government: '数字政企',
  uni: '数字高校',
};
const columns = [
  { title: '渠道信息', colKey: 'name', width: 304 },
  { title: '渠道类型', colKey: 'channel', width: 120 },
];
const spuId = ref('');
const getData = async () => {
  const params = {
    spuId: spuId.value,
    'page.size': pagination.value.pageSize,
    'page.number': pagination.value.current,
  };
  try {
    const res = await listPromotionChannels(params);
    console.log(res, 'res');
    if (res.code === 0) {
      tableData.value = res.data.channels;
      pagination.value.total = res.data.total;
    }
  } catch (error) {
    console.log(error);
  }
};
const onPageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  getData();
};
defineExpose({
  openDrwer,
});
</script>

<style scoped lang="less">
:global(.addChannel2 .t-drawer__content-wrapper) {
  background-image: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bg_img_code.png') !important;
  background-repeat: no-repeat !important;
  background-size: 100% 100% !important;
}
:global(.addChannel2 .t-drawer__body) {
  padding: 0 12px 12px 12px;
}
:global(.addChannel2 .t-drawer__close-btn) {
  background: none !important;
  right: 24px;
}
:global(.addChannel2 .t-drawer__close-btn:hover) {
  background: none !important;
}
.sr-box {
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #fff);
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  min-height: 563px;
}

// .sr-box {
//   border-radius: 8px;
//   background: var(--bg-kyy_color_bg_light, #fff);
// }
.chbox {
  display: flex;
  height: 24px;
  padding: 0px 4px;
  align-items: center;
  gap: 2px;
  border-radius: 4px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  width: fit-content;
  img {
    display: flex;
    width: 20px;
    height: 20px;
    padding: 2px;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
  }
  .textmember {
    color: var(--warning-kyy_color_warning_default, #fc7c14);

    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .textcbd {
    color: var(--brand-kyy_color_brand_default, #4d5eff);

    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .textassociation {
    color: var(--error-kyy_color_error_default, #d54941);

    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .government {
    color: var(--error-kyy_color_error_default, #d54941);

    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.name-tag {
  display: flex;
  height: 20px;
  width: 20px;
  padding: 0px 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_blue, #e8f0fb);
  color: var(--kyy_color_tag_text_blue, #4093e0);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  float: left;
  margin-right: 4px;
}
.icon20 {
  font-size: 24px;
  color: #1a2139;
  font-weight: 600;
}
</style>
