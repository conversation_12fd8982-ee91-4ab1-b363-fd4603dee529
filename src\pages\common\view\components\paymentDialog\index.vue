<template>
  <StyledGradientDialog
    :visible="qrcodeDialogFlag"
    :close-btn="false"
    :header="true"
    :cancel-btn="null"
    :confirm-btn="null"
    :footer="false"
    :z-index="9999999"
    width="464"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
    class="qrcodeDialog"
    placement="center"
    :title="t('payment.smzf')"
    :description="extend?.subTitle ? `类目名称：${extend?.subTitle}` : undefined"
    :background-image="{
      src: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/common_pay_bg.png',
    }"
    @close="qrcodeDialogClose"
  >
    <div class="qrcodes">
      <div class="headM">
        <!-- rowData.pay_amount -->
        <div class="money">
          <span class="money-unit">{{ rowData.pay === 'CN' ? '￥' : 'MOP' }}</span>
          <span class="money-num">
            {{ addCommasToNumber(parseFloat(rowData?.amount.toString() || '0').toFixed(2)) }}
          </span>
        </div>

        <div class="money-tips">{{ t('payment.payableAmount') }}</div>
      </div>

      <div class="body-box">
        <div class="qrcode-img-box" :class="times === 0 ? 'expires' : ''" @click="refsqrCode">
          <qrcode-vue style="width: 128px; height: 128px; border-radius: 8px" :value="link" :size="128" level="H" />
          <div v-if="times === 0" class="fail-tips">获取失败，点击重新获取二维码</div>
        </div>

        <div v-if="times === 0" class="tips">
          <span>{{ t('payment.qrCodeHasExpired') }}</span>
          <span class="cxhq" @click="refsqrCode">{{ t('payment.cxhq') }}</span>
        </div>

        <div v-else class="tips">
          <i18n-t keypath="payment.qrCodeExTime" tag="span">
            <template #num>
              <span class="time-num">{{ times }}</span>
            </template>
          </i18n-t>
        </div>

        <div class="line"></div>

        <div v-if="payWayStr === '云闪付'" class="ysf-box">
          <div>
            <div>{{ t('order.plaisit') }}{{ payWayStr }}{{ t('order.saocode') }}</div>
            <div>{{ t('payment.scanQRCodePaymentForPayment') }}</div>
          </div>
        </div>

        <div v-else>
          <div class="zf-box">
            <div v-if="resPayRegionDataFlag.includes(3)" class="zfb-box">
              <img src="@assets/img/mypay.svg" />
            </div>
            <div v-if="resPayRegionDataFlag.includes(2)" class="zfb-box">
              <img src="@assets/img/zfb.svg" />
            </div>
            <div v-if="resPayRegionDataFlag.includes(1)" class="zfb-box">
              <img src="@assets/img/wx.svg" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </StyledGradientDialog>
  <t-dialog
    v-model:visible="tipDialogPayOkFlag"
    :close-btn="false"
    :header="true"
    :cancel-btn="null"
    :confirm-btn="null"
    :footer="true"
    width="384"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
    class="tipQrcodeDialog"
  >
    <div style="text-align: center">
      <img
        v-if="paySteta === '支付成功'"
        style="width: 48px; height: 48px; margin-bottom: 12px"
        src="@assets/svg/clouddisk/icon_success.svg"
      />
      <div class="paysess">
        {{ paySteta }}
      </div>
      <img
        v-if="paySteta === t('payment.paymentErr')"
        style="width: 48px; height: 48px; margin-bottom: 12px"
        src="@assets/img/icon_error.svg"
      />
      <img
        v-if="paySteta === t('payment.paymentClose')"
        style="width: 48px; height: 48px; margin-bottom: 12px"
        src="@assets/img/icon_warning.svg"
      />
      <div v-if="paySteta === '支付成功'" class="paynum">
        {{ rowData.pay === 'CN' ? '￥' : 'MOP' }}
        {{ addCommasToNumber(rowData.amount.toFixed(2)) }}
      </div>
      <div v-if="paySteta === t('payment.paymentClose')" class="play-customer-service">
        {{ t('payment.orderStatusHasChanged') }}
      </div>

      <div v-if="paySteta === t('payment.paymentErr')" class="play-customer-service">
        {{ payStetaTip }}
      </div>

      <t-button style="width: 88px" @click="isOk">
        {{ paySteta === t('payment.paymentClose') ? t('payment.close') : '知道了' }}
      </t-button>
    </div>
  </t-dialog>
  <t-dialog
    v-model:visible="PayPaddingOkFlag"
    :close-btn="false"
    :header="true"
    :cancel-btn="null"
    :confirm-btn="null"
    :footer="true"
    width="384"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
    class="tipQrcodeDialog"
    attach="body"
  >
    <div style="text-align: center; height: 170px">
      <img class="rotate" src="@assets/loading.png" />
      <div class="pay-text">{{ t('order.paycximg') }}</div>
      <t-button style="margin-top: 20px; width: 88px" @click="((PayPaddingOkFlag = false), (qrcodeDialogFlag = false))">
        {{ t('payment.close') }}
      </t-button>
    </div>
  </t-dialog>
  <TipsDialog
    v-model:show-dialog="showTipsDialog"
    :info="tipsDialogInfo"
    @close-dialog="closeTipsDialog"
    @on-agin-buy="onAginBuy"
  />
  <t-loading v-if="isShowLoading" size="small" :loading="loading" show-overlay attach="body" z-index="8000" />
</template>
<script setup lang="ts" name="paymentDialog">
import { Utils } from '@utils';
import { ref, watch, onBeforeUnmount, defineProps } from 'vue';
import QrcodeVue from 'qrcode.vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import TipsDialog from './tipsDialog.vue';
import { payCreate, payStatus, payRegion } from '../../../api';
import sdk from '@lynker-desktop/web';
import StyledGradientDialog from '@components/common/StyledGradientDialog.vue';
// const { ipcRenderer } = require('electron');
const props = defineProps({
  defaultInvoice: {
    type: Object,
    default: () => {},
  },
  invoiceFlag: {
    type: Number,
    default: 1,
  },
  customSuccessDialog: {
    type: Boolean,
    default: false,
  },
  extend: {
    type: Object,
    default: () => {},
  },
});
const link = ref('');
const { t } = useI18n();
const emits = defineEmits(['paymentCallback', 'getDataList']);
let timeData = null;
let downTimer = null;
const showTipsDialog = ref(false);
const tipsDialogInfo = ref({});
const resPayRegionDataFlag = ref([]);
const qrcodeDialogFlag = ref(false);
const PayPaddingOkFlag = ref(false);
const tipDialogPayOkFlag = ref(false);
const payWayStr = ref(t('payment.scanCodePayment'));
const times = ref(180);
const time180or300 = ref(180);
const isShowLoading = ref(false);
const paySteta = ref('支付成功');
// const isMas = ref(__APP_ENV__.VITE_APP_MAS);
const isMas = ref(Utils?.config?.viteConfig?.VITE_APP_MAS);
const InAppPurchaseInfo = ref({
  data: {},
  times: 0,
});
const payResponseInfo = ref({});
const isApplePay = ref(false);
const addCommasToNumber = (str) => {
  let [integerPart, decimalPart] = str.split('.');
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  if (decimalPart) {
    decimalPart = decimalPart.length === 1 ? `${decimalPart}0` : decimalPart.slice(0, 2);
  } else {
    decimalPart = '00';
  }
  return `${integerPart}.${decimalPart}`;
};
const qrcodeDialogClose = () => {
  const confirmDia = DialogPlugin({
    header: '提示',
    theme: 'info',
    class: 'delmode',
    body: t('payment.orderTip'),
    closeBtn: null,
    zIndex: 9999999999999,

    confirmBtn: t('payment.continuePayment'),
    cancelBtn: t('payment.confirmDeparture'),
    onClose: () => {
      confirmDia.hide();
      qrcodeDialogFlag.value = false;
    },
    onConfirm: () => {
      confirmDia.hide();
    },
  });
};
const rowData = ref({
  sn: '',
  region: 0,
  pay: '',
  amount: 0,
  orderType: '',
  // 下单渠道, 0:其他,1:ios,2:mac,3:mac非应用市场,4:windows,5:安卓,6:web,7:客服
  // orderChannel: 0
});
const isOk = () => {
  emits('paymentCallback', paymentStatus.value);
  tipDialogPayOkFlag.value = false;
};
const outOrderNo = ref('');
const handleCountDown = () => {
  times.value--;
  downTimer = setTimeout(() => {
    if (times.value > 0) {
      handleCountDown();
    } else {
      clearTimeout(downTimer);
    }
  }, 1000);
};
const getLink = (sn, pay, order_type?) =>
  new Promise((resolve, reject) => {
    console.log(props.defaultInvoice, 'defaultpropspropsInvoicedefaultInvoicedefaultInvoice');
    payResponseInfo.value = {};
    isApplePay.value = false;
    // 是否是待支付订单
    const isHasOrder = typeof rowData.value.channel !== 'undefined';
    // 是不是mac应用商店的订单 订单为2 苹果应用内的订单
    const isMacOrder = isHasOrder && rowData.value.channel === 2;
    // 默认是0
    let payment = 0;
    // 如果不是待支付订单 或者 是待支付订单在mac商店类型包下的单,且当前设备是mac商店类型
    if ((!isHasOrder || isMacOrder) && isMas.value) {
      payment = 5;
    }
    const params = {
      order_sn: sn,
      client_type: 0,
      payment: payment,
      invoice_header: props.defaultInvoice && props.invoiceFlag !== 1 ? JSON.stringify(props.defaultInvoice) : '',
    };
    if (order_type) {
      params.order_type = order_type;
    }
    payCreate(params)
      .then(async (res) => {
        console.log('payCreate:', res, sn, pay);
        if (res && !res.data.sn) {
          reject(res);
          emits('getDataList', sn);
          qrcodeDialogFlag.value = false;
        } else {
          outOrderNo.value = res.data.sn;
          // 苹果设备上处理为苹果支付的订单 或者 不是待支付订单在苹果设备上处理
          if ((isMacOrder || !isHasOrder) && isMas.value) {
            isApplePay.value = true;
            const _data = res?.data || {};
            _data.price = rowData.value.amount;
            _data.PRODUCT_IDS = _data.params?.product_ids;
            await handleInAppPurchase(_data);
          } else {
            isApplePay.value = false;
            if (res.data.channel === 4) {
              // 拉卡拉
              link.value = res.data.params.qr_code;
            } else if (res.data.channel === 3) {
              // mypay
              link.value = res.data.params.counter_url;
            }
          }
          resolve(res);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });

let clickFlag = false;
const openWin = async (row) => {
  if (clickFlag) {
    return;
  }
  clickFlag = true;
  rowData.value = row;
  rowData.value.pay = row.region;
  clearTimeout(downTimer);
  clearTimeout(timeData);
  try {
    const res = await payRegion(row.region);

    resPayRegionDataFlag.value = res.data[0].payments;
    if (res.data[0].channel === 4) {
      time180or300.value = 300;
    } else {
      time180or300.value = 180;
    }
    times.value = time180or300.value;

    handleCountDown();
    await getLink(row.sn, row.region, row.orderType);
    if (!isApplePay.value) {
      qrcodeDialogFlag.value = true;
    }
    clickFlag = false;
  } catch (error) {
    if (error.response) {
      MessagePlugin.error(error.response.data.message);
    } else {
      MessagePlugin.error(error.data.message);
    }
    emits('getDataList', row.sn);

    clickFlag = false;
  }
};
// 检查支付信息
// 目前只有mac应用内购买 下单前先查询商品是否存在
const checkPayInfo = async (row, isAginBuy?) => {
  if (isMas.value && row.product_ids) {
    InAppPurchaseInfo.value.data = row;
    // 如果不是重新购买 次数就清空
    if (!isAginBuy) {
      InAppPurchaseInfo.value.times = 0;
    }

    console.log('InAppPurchaseInfo12222', InAppPurchaseInfo.value);
    if (InAppPurchaseInfo.value.times >= 2) {
      showTipsDialog.value = false;
      setTimeout(() => {
        showTipsDialog.value = true;
        tipsDialogInfo.value = {
          code: 101,
        };
      }, 0);
      return false;
    }

    const _data = {
      PRODUCT_IDS: row.product_ids,
      onlyCheckProducts: true,
    };
    isShowLoading.value = true;
    const inAppPurchase = await sdk.ipcRenderer.invoke('applePay-inAppPurchase', JSON.stringify(_data));
    isShowLoading.value = false;
    console.log('checkProducts 查询 inAppPurchase:', inAppPurchase);
    // 没有查到商品
    if (inAppPurchase.status === 'noProducts') {
      if (inAppPurchase.data.code) {
        showTipsDialog.value = true;
        tipsDialogInfo.value = {
          code: inAppPurchase.data.code,
        };
      }
      return false;
    }
    return inAppPurchase;
  } else {
    return true;
  }
};

defineExpose({
  openWin,
  checkPayInfo,
});
onBeforeUnmount(() => {
  clearTimeout(timeData);
  clearTimeout(downTimer);
});
const refsqrCode = async () => {
  if (times.value === 0) {
    await getLink(rowData.value.sn, rowData.value.pay, rowData.value.orderType);
    times.value = time180or300.value;

    clearTimeout(downTimer);
    clearTimeout(timeData);

    getLinkStart();
    handleCountDown();
    console.log(
      timeEnd.value - new Date().getTime(),
      'timeEnd.value-new Date().getTime()timeEnd.value-new Date().getTime()',
    );
  }
};
const payStetaTip = ref('');
const paymentStatus = ref(0); // 0待支付 1支付成功 2支付渠道已关闭 3转入退款 4扫码支付已取消 5支付中 6支付失败
const timeEnd = ref(null);
//最大次数
const maxTimes = 20;
// 计算的次数
let countTimes = 0;
const getLinkStart = (cb?) => {
  console.log('getLinkStart', isApplePay.value);
  if (times.value < 1 || (!isApplePay.value && !qrcodeDialogFlag.value)) {
    clearTimeout(timeData);
    return;
  }

  //mac商店类型 超过最大尝试次数
  if (isApplePay.value && countTimes >= maxTimes) {
    cb && cb({ error: 'timeout' });
    return;
  }
  const data: any = {};
  if (isApplePay.value) {
    data.receipt = payResponseInfo.value.receipt;
  }
  console.log('payStatus data:', data);
  countTimes++;
  payStatus(outOrderNo.value, data)
    .then((res) => {
      console.log('payStatus res:', res);
      const { status } = res.data;
      paymentStatus.value = status;
      if (status === 0) {
        timeData = setTimeout(() => {
          getLinkStart(cb);
        }, 3000);
      } else if ([1, 2, 4].includes(status)) {
        cb && cb({ status });
        // 非苹果支付的
        if (!isApplePay.value) {
          if (status === 1) {
            paySteta.value = '支付成功';
            qrcodeDialogFlag.value = false;

            emits('paymentCallback', paymentStatus.value);
          } else if (status === 4) {
            paySteta.value = t('payment.paymentErr');
            qrcodeDialogFlag.value = false;
            tipDialogPayOkFlag.value = true;
            emits('paymentCallback', paymentStatus.value);
          } else {
            paySteta.value = t('payment.paymentErr');
            times.value = 0;
          }
        }

        PayPaddingOkFlag.value = false;

        clearTimeout(timeData);
      } else if (status === 5) {
        timeData = setTimeout(() => {
          times.value = time180or300.value;
          getLinkStart(cb);
        }, 3000);
        // 非苹果支付的
        if (!isApplePay.value) {
          if (qrcodeDialogFlag.value) {
            PayPaddingOkFlag.value = true; // 状态5
          }
        }
      } else {
        timeData = setTimeout(() => {
          getLinkStart(cb);
        }, 3000);
      }
    })
    .catch(() => {
      timeData = setTimeout(() => {
        getLinkStart(cb);
      }, 3000);
    });
};
const closeTipsDialog = () => {
  // todothing...
};
const onAginBuy = async () => {
  InAppPurchaseInfo.value.times++;
  await checkPayInfo(InAppPurchaseInfo.value.data, true);
};
// 处理苹果应用内支付
const handleInAppPurchase = async (data) => {
  console.log('苹果支付');
  countTimes = 0;
  isShowLoading.value = true;

  // emits("update:allPayDialog", false);
  const inAppInfo = await sdk.ipcRenderer.invoke('applePay-inAppPurchase', JSON.stringify(data));
  console.log('inAppInfo', inAppInfo);
  if (inAppInfo) {
    if (inAppInfo.status === 'purchased') {
      payResponseInfo.value = {
        receipt: inAppInfo.data.receipt,
      };
      // 交易完成
      getPayStatus()
        .then((res: any) => {
          countTimes = 0;
          isShowLoading.value = false;
          if (res) {
            if (res.status === 1) {
              showTipsDialog.value = true;
              tipsDialogInfo.value = {
                code: 1,
                pay: rowData.value.pay,
                amount: rowData.value.amount,
              };
            } else {
              showTipsDialog.value = true;
              tipsDialogInfo.value = {
                code: 6,
              };
            }
          }
        })
        .catch(() => {
          isShowLoading.value = false;
        });
    } else {
      isShowLoading.value = false;
      if (inAppInfo.data.code) {
        showTipsDialog.value = true;
        tipsDialogInfo.value = {
          code: inAppInfo.data.code,
        };
      }
    }
  }
};

const getPayStatus = () => {
  return new Promise((reslove, reject) => {
    getLinkStart(({ status, error }) => {
      if (error) {
        reject(error);
      } else {
        reslove({
          status,
        });
      }
    });
  });
};

watch(
  () => qrcodeDialogFlag.value,
  (newvalue) => {
    if (newvalue) {
      clearTimeout(timeData);
      getLinkStart();
    } else {
      clearTimeout(timeData);
      clearTimeout(downTimer);
      timeData = null;
      downTimer = null;
      times.value = time180or300.value;
    }
  },
);
</script>
<style scoped lang="less">
.ysf-box {
  margin: 16px auto 0;
  width: 200px;
  height: 58px;
  background: #eeffe8;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    width: 40px;
    height: 40px;
    margin-right: 8px;
  }
  div {
    font-size: 14px;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei-Regular;
    font-weight: 400;
    color: #1c8710;
    line-height: 22px;
  }
}
.pay-text {
  text-align: center;
  width: 100%;
  height: 24px;
  font-size: 16px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Bold;
  font-weight: 700;
  text-align: center;
  color: #1a2139;
  line-height: 24px;
}
.rotate {
  margin-top: 28px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  animation: rotate 2s infinite linear;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.play-customer-service {
  height: 22px;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: center;
  color: #717376;
  line-height: 22px;
  margin-bottom: 24px;
}
.play-customer-service-two {
  color: #da2d19;
  font-size: 20px;
  font-weight: 800;
  margin-bottom: 8px;
}
.back-box {
  padding-left: 16px;
  display: flex;
  height: 22px;
  margin-top: 50px;
  cursor: pointer;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: #4d5eff;
  line-height: 22px;
  align-items: center;
  img {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}

.paynum {
  height: 24px;
  font-size: 16px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Bold;
  font-weight: 700;
  text-align: center;
  color: #da2d19;
  line-height: 24px;
  margin-bottom: 24px;
}
.paysess {
  height: 24px;
  font-size: 16px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Bold;
  font-weight: 700;
  text-align: center;
  color: #1a2139;
  line-height: 24px;
  margin-bottom: 8px;
}
.qrcodes {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #fff;
  overflow: hidden;

  .foot-boxs {
    width: 300px;
    cursor: pointer;
    height: 72px;
    border: 1px solid #eceff5;
    border-radius: 4px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
    margin-left: 16px;
    img {
      width: 32px;
      height: 32px;
      margin-right: 8px;
    }
    div {
      height: 22px;
      font-size: 14px;
      font-family:
        Microsoft YaHei,
        Microsoft YaHei-Regular;
      font-weight: 400;
      color: #1a2139;
      line-height: 22px;
    }
  }
  .zf-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .zfb-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 112px;
      height: 24px;

      .labzfb {
        height: 12px;
        font-size: 8px;
        font-family:
          Microsoft YaHei,
          Microsoft YaHei-Regular;
        font-weight: 400;
        text-align: left;
        color: #1a2139;
        line-height: 12px;
      }
    }
  }
  .tips {
    text-align: center;
    height: 22px;
    font-size: 14px;

    font-family: PingFang SC;
    font-weight: 400;
    color: #516082;
    line-height: 22px;
  }

  .time-num {
    margin: 0 4px;
    color: #d54941;
  }
  .expires {
    background: rgba(#141a42, 0.8);
    position: relative;
  }
  .expires::after {
    content: '';
    width: 144px;
    height: 144px;
    background: rgba(#141a42, 0.8);

    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 22px;
    border-radius: 8px;
    transform: translate(-50%, -50%);
  }
  .fail-tips {
    width: 98px;
    position: absolute;
    top: 50%;
    color: #ffff;
    z-index: 9;
    left: 50%;
    margin-left: -49px;
    margin-top: -22px;
  }
  .qrcode-img-box {
    margin: 0 auto;
    width: 144px;
    height: 144px;
    padding: 8px;
    border-radius: 8px;
    position: relative;
    margin-bottom: 8px;
    img {
      width: 200px;
      height: 200px;
    }
  }
}
.zfbs {
  width: 112px;
  height: 32px;
}

.foot-boxs:last-child {
  margin-left: 0;
}
.foot-boxs:first-child {
  margin-left: 0;
}
.foots {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0 16px;
}

.body-box {
  padding: 16px 32px;
  background: #ffffff;
  border-radius: 0 0 4px 4px;
  position: relative;
}

.headM {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 88px;
  width: 100%;
  text-align: center;
  overflow: hidden;
  background: url(http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/common_money_bg.png);
  padding: 16px 24px;

  .money {
    text-align: center;
    font-family: PingFang SC;
    color: #d54941;
    font-weight: 600;

    .money-unit {
      margin-right: 4px;
      font-size: 16px;
      line-height: 24px;
    }

    .money-num {
      font-size: 28px;
      line-height: 32px;
    }
  }

  .money-tips {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #d54941;
    line-height: 22px;
  }
}

.line {
  width: 100%;
  height: 1px;
  background: #eceff5;
  margin-top: 16px;
  margin-bottom: 16px;
}

.cxhq {
  margin-left: 4px;
  font-size: 14px;
  line-height: 22px;
  color: #4d5eff;
  cursor: pointer;
  opacity: 1;
  transition: opacity 0.3s;

  &:hover {
    opacity: 0.8;
  }
}
</style>
<style lang="less">
.qrcodeDialog {
  .sub-title {
    margin-top: 12px;
  }
}
</style>
