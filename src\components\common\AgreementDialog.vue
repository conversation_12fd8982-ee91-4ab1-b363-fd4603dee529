<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { REmpty } from '@rk/unitPark';
import { AgreementType, getAgreement } from '@api/common';
import type { TdButtonProps, TNode } from 'tdesign-vue-next';

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    /** 协议类型 */
    uuid?: AgreementType;
    confirmBtn?: TdButtonProps | string | TNode | null;
    cancelBtn?: TdButtonProps | string | TNode | null;
  }>(),
  {
    confirmBtn: '同意',
    cancelBtn: '取消',
  },
);

const emit = defineEmits<{
  (e: 'confirm'): void;
}>();

const visible = defineModel<boolean>('modelValue');
const loading = ref(false);
const data = ref({
  content: '',
  name: '',
});
const hasError = computed(() => !loading.value && !data.value.content && !data.value.name);

// 获取协议内容
const fetchAgreement = async (uuid: AgreementType) => {
  if (!uuid) return;

  loading.value = true;
  data.value = { content: '', name: '' };

  try {
    const res = await getAgreement(uuid);
    data.value = (res as any).data;
  } catch (error) {
    console.error('获取协议失败:', error);
    data.value = { content: '', name: '' };
  } finally {
    loading.value = false;
  }
};

// 监听 props 变化，支持通过 props 传参的方式
watch(
  () => [props.modelValue, props.uuid] as const,
  ([newVisible, newUuid]) => {
    if (newVisible && newUuid) {
      fetchAgreement(newUuid);
    }
  },
  { immediate: true },
);

// 通过实例方法打开弹窗
const open = async (uuid: AgreementType) => {
  if (!uuid) return;

  visible.value = true;
  await fetchAgreement(uuid);
};

const handleConfirm = () => {
  visible.value = false;
  emit('confirm');
};

defineExpose({
  open,
  close: handleConfirm,
});
</script>

<template>
  <t-dialog
    v-model:visible="visible"
    width="600"
    attach="body"
    placement="center"
    :cancel-btn="cancelBtn"
    prevent-scroll-through
    :close-btn="null"
    :z-index="9999"
    :confirm-btn="confirmBtn"
    class="agreement-dialog"
    destroy-on-close
    v-bind="$attrs"
    @confirm="handleConfirm"
  >
    <div v-if="!hasError" v-loading="{ loading, size: 'small', text: '加载中' }" class="frame" v-html="data?.content" />
    <REmpty v-if="hasError" name="404" center tip="协议未找到" />
  </t-dialog>
</template>

<style lang="less">
.agreement-dialog {
  .t-dialog {
    padding: 24px 0 0 0 !important;
  }

  .t-dialog__header {
    display: none;
  }

  .t-dialog__body {
    height: 454px;
    padding: 0 16px 0 24px !important;
    overflow-y: auto;
    .scrollbar2();
  }

  .t-dialog__footer {
    display: flex;
    padding: 24px;
    justify-content: flex-end;
    align-items: center;
    align-self: stretch;
  }

  .frame {
    color: initial !important;
    figure,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
      margin: revert !important;
    }
  }
}
</style>
