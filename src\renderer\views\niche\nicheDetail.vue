<template>
  <div class="detail-page">
    <div  class="detail-content">
      <div class="src-box niche-src-box">
        <div v-if="nicheDetail?.id" class="create-info">
          <div class="create-item">
            <div class="label">{{ t("niche.cjz") }}</div>
            <div class="text">{{ nicheDetail?.staff_name }}</div>
          </div>
          <!-- :style="{opacity: nicheDetail?.is_external_link ? 0 : 1}" -->

          <template>

          </template>
          <div class="create-item" v-if="!nicheDetail?.is_external_link">
            <div class="label">{{ t("niche.gch") }}</div>
            <div class="text">
              <div
                v-if="nicheDetail?.channel_data[0]?.id"
                :class="`st-tag${nicheDetail?.channel_data[0]?.release_state}`"
              >
                {{ releaseStateText[nicheDetail?.channel_data[0]?.release_state] }}
              </div>
              <div v-else class="st-tag00">{{ t("niche.wfb") }}</div>
            </div>
          </div>
          <div class="create-item" v-else>
            <div class="label">{{ t("niche.szpt") }}</div>
            <div class="text">
              <div
                v-if="nicheDetail?.channel_data[1]?.id"
                :class="`st-tag${nicheDetail?.channel_data[1]?.release_state}`"
              >
                {{ releaseStateText[nicheDetail?.channel_data[1]?.release_state] }}
              </div>
              <div v-else class="st-tag00">{{ t("niche.wfb") }}</div>
            </div>
          </div>
          <div class="create-item">
            <div class="label">{{ t("niche.cr_time") }}</div>
            <div class="text">{{ nicheDetail?.created_at }}</div>
          </div>
          <div class="create-item" :style="{opacity: nicheDetail?.is_external_link ? 0 : 1}">
            <div class="label">{{ t("niche.szpt") }}</div>
            <div class="text">
              <div
                v-if="nicheDetail?.channel_data[1]?.id"
                :class="`st-tag${nicheDetail?.channel_data[1]?.release_state}`"
              >
                {{ releaseStateText[nicheDetail?.channel_data[1]?.release_state] }}
              </div>
              <div v-else class="st-tag00">{{ t("niche.wfb") }}</div>
            </div>
          </div>
        </div>

        <div v-if="nicheDetail?.id" class="detail-box">
          <nicheDateil :review="false" :show-effective="true" :niche-detail="nicheDetail" />
        </div>
      </div>
      <div class="detail-footer family">
        <template v-for="(item, index) in func" :key="item.id">
          <t-button
            v-show="index === 0 || index === 1"
            theme="default"
            style="min-width: 80px"
            @click="featureRun(item.id)"
          >
            {{ item.title }}
          </t-button>
        </template>
        <template v-if="func?.length === 3">
          <div v-if="func[2].id === 'delete'" class="danger-btn" ghost @click="featureRun(func[2].id)">
            {{ func[2].title }}
          </div>
          <t-button v-else style="margin-left: 8px; min-width: 80px" theme="primary" @click="featureRun(func[2].id)">
            {{ func[2].title }}
          </t-button>
        </template>

        <t-popup
          v-if="func?.length > 3"
          overlay-class-name="niche-popup"
          placement="top-left"
          :z-index="1000"
          destroy-on-close
        >
          <template #content>
            <div class="actions-boxs">
              <div v-for="(item, index) in func" v-show="index > 1" :key="item.id">
                <div class="item" @click.stop="featureRun(item.id)">
                  <div class="icon">
                    <iconpark-icon :name="item.icon" class="name-icon" />
                  </div>
                  <div class="text">{{ item.title }}</div>
                </div>
              </div>
            </div>
          </template>

          <t-button style="width: 80px"> {{ t("niche.gd") }} </t-button>

          <!-- <a style="margin-left: 8px">更多</a> -->
        </t-popup>
      </div>
    </div>
  </div>
  <delist ref="delistRef" @delist-succ="getDetailRun" />
  <RenewalDialog
    v-if="renewalDialogVisible"
    v-model="renewalDialogVisible"
    :square-id="squareData?.square?.squareId"
    :upgrade="upgrade"
    :team-id="teamId"
    @success="renewalSuccess"
  />
  <OpenSquare v-if="openSquareVisible" v-model="openSquareVisible" @success="renewalSuccess" />
</template>

<script setup lang="ts" name="nicheDetail">
import { ClientSide } from "@renderer/types/enumer";
import { onActivated, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import nicheDateil from "@renderer/views/niche/components/nicheHome/nicheDateil.vue";
import detailExtend from "@renderer/views/niche/components/nicheHome/detailExtend.vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import delist from "@renderer/views/niche/components/nicheHome/delist.vue";
import { getTeamAnnualFee } from "@renderer/api/service";
import to from "await-to-js";
import { getAppsState } from "./apis/create";
import { businessReleaseCopy, businessReleaseDel, getDetail, hotSet, releaseRelease } from "./apis";
import OpenSquare from "@/views/square/components/OpenSquare.vue";
import RenewalDialog from "@/views/square/components/annual-fee/AnnualFeeDialog.vue";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;
const emits = defineEmits(["uptWorkBenchTabItem", "setActiveIndexAndName", "deltabItem"]);

const closePage = () => {
  const index = props.tabList.findIndex(
    (e) => e.path === "/workBenchIndex/nicheEdit" || e.path === "/workBenchIndex/nicheCreate",
  );
  emits("deltabItem", index, true, true);
};
const { t } = useI18n();

const route = useRoute();
const showType = ref(1);
const teamId = ref(localStorage.getItem("businessTeamId") || "");
const releaseStateText = ref([
  t("niche.status_not_active"),
  t("niche.status_in_progress"),
  t("niche.status_off_shelf"),
  t("niche.status_expired"),
  t("niche.status_deleted"),
]);

const props = defineProps({
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
  activationGroupItem: {
    type: Object,
    default: null,
  },
  cloudDiskType: {
    type: Object,
    default: null,
  },
});

const settabItem = () => {
  console.log(route.query?.title, " route.query?.title");
  const res = props.tabList.some((root) => root.updateKey === "nicheDetail");
  console.log(res, "res");
  if (res) {
    emits("uptWorkBenchTabItem", {
      path: `/workBenchIndex/nicheDetail`,
      path_uuid: "niche",
      name: "nicheDetail",
      title: route.query?.title || "商机详情",
      query: route.query,
      updateKey: "nicheDetail",
      type: ClientSide.NICHE,
    });
  } else {
    ipcRenderer.invoke("set-work-bench-tab-item", {
      path: `/workBenchIndex/nicheDetail`,
      path_uuid: "niche",
      name: "nicheDetail",
      title: route.query?.title || "商机详情",
      query: route.query,
      updateKey: "nicheDetail",
      type: ClientSide.NICHE,
    });
  }
};
const extendShow = () => {
  showType.value = 2;
};
const nicheDetail: any = ref({});
const func: any = ref([]);

const editHotReq = (hot, row, myDialog) => {
  hotSet({ is_top: hot }, row.id).then((res: any) => {
    console.log(res);
    if (res.data?.code === 0) {
      MessagePlugin.success(t("niche.opsucc"));
      myDialog.hide();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};
const editHotRun = (rowData) => {
  const val = rowData.is_top ? 0 : 1;
  const tipText = [t("niche.topcl"), t("niche.topop")];
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: tipText[val],
    className: "dialog-classp32",
    onConfirm: () => {
      editHotReq(val, rowData, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const copyBusinessReq = (cid, myDialog) => {
  businessReleaseCopy({ id: cid }).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      MessagePlugin.success(t("niche.opsucc"));
      myDialog.hide();
    }
  });
};

const squareData = ref(null);
const pageShow = ref(true);
const tipsData = ref({
  con: "",
  btn: "",
  tipHeader: null,
});
const renewalDialogVisible = ref(false);
const upgrade = ref(false);
const openSquareVisible = ref(false);
const squareShow = ref(false);
const checkExpiration = (expiredAt) => {
  const now = new Date();
  const expirationDate = new Date(expiredAt);
  return now > expirationDate;
};
const getTeamAnnualFeeRun = async () => {
  const res = await getTeamAnnualFee(teamId.value);
  if (res.data) {
    squareData.value = res.data;
    tipsData.value.tipHeader = null;
    if (squareData.value.opened) {
      pageShow.value = false;
      squareShow.value = false;
      if (checkExpiration(squareData.value.annualFeeExpiredAt)) {
        if (squareData.value?.annualFeeDetail?.trial) {
          pageShow.value = true;
          tipsData.value.tipHeader = t("niche.vip_open_tiph");
          tipsData.value.con = t("niche.vip_open_tip5");
          tipsData.value.btn = t("niche.vip_open_btn4");
        } else {
          pageShow.value = true;
          tipsData.value.con = t("niche.vip_open_tip1");
          tipsData.value.btn = t("niche.vip_open_btn1");
        }
      } else {
        pageShow.value = false;
        if (squareData.value?.annualFeeDetail?.package) {
          const item = squareData.value?.annualFeeDetail?.package?.items.find((item) => item.itemType === "NICHE");
          if (!item) {
            if (squareData.value?.annualFeeDetail?.trial) {
              tipsData.value.con = t("niche.vip_open_tip4");
              tipsData.value.btn = t("niche.vip_open_btn4");
              tipsData.value.tipHeader = t("niche.vip_open_tiph");
              upgrade.value = false;
            } else {
              tipsData.value.con = t("niche.vip_open_tip2");
              tipsData.value.btn = t("niche.vip_open_btn2");
              upgrade.value = true;
            }
            pageShow.value = true;
          } else {
            pageShow.value = false;
          }
        } else {
          tipsData.value.con = t("niche.vip_open_tip2");
          tipsData.value.btn = t("niche.vip_open_btn2");
          upgrade.value = true;
          pageShow.value = true;
        }
      }
    } else {
      tipsData.value.con = t("niche.vip_open_tip3");
      tipsData.value.btn = t("niche.vip_open_btn3");
      pageShow.value = true;
      squareShow.value = true;
    }
  }
};

const renewalSuccess = () => {
  renewalDialogVisible.value = false;
  openSquareVisible.value = false;
  getTeamAnnualFeeRun();
};

const copyBusRun = (data) => {
  console.log(t("niche.copy"));
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: `${t("niche.askcopy")} “${data.title}” ${t("niche.cgxz")}`,
    className: "dialog-classp32",
    onConfirm: () => {
      copyBusinessReq(data.id, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const businessDelRun = (row) => {
  console.log(t("niche.del"));
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("niche.re_del_tip"),
    className: "dialog-classp32",
    onConfirm: () => {
      businessDelReq(row.id, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const businessDelReq = (id, myDialog) => {
  businessReleaseDel(id).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc();
      myDialog.hide();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};
const delSucc = () => {
  closePage();
};

const delistRef = ref(null);
const delistRun = (rowData, origin) => {
  console.log(t("niche.xj"));
  delistRef.value.deOpen(rowData, origin);
};

const router = useRouter();
const delistEdit = ref(false);
const editBusRun = (rowData) => {
  console.log(t("niche.edit"));
  console.log(rowData);
  if (rowData.channel_data[0].release_state === 1 || rowData.channel_data[1]?.release_state === 1) {
    const myDialog = DialogPlugin({
      header: t("niche.tip"),
      theme: "info",
      body: t("niche.xuxiajia"),
      className: "dialog-classp32",
      confirmBtn: t("niche.xj"),
      onConfirm: () => {
        myDialog.hide();
        delistEdit.value = true;
        delistRun(rowData, 0);
      },
      onCancel: () => {
        myDialog.hide();
      },
    });
  } else {
    router.push(`/workBenchIndex/nicheEdit?form=list&id=${rowData.id}`);
  }
};
const isMac = ref(process.platform === "darwin");
const mactip = () => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("niche.vip_open_tip2"),
    className: "dialog-classp32",
    cancelBtn: null,
    confirmBtn: "知道了",
    onConfirm: () => {
      myDialog.hide();
    },
  });
};
const plazaRenew = () => {
  if (upgrade.value && isMac.value) {
    mactip();
    return;
  }
  const myDialog = DialogPlugin({
    header: tipsData.value.tipHeader || t("niche.tip"),
    theme: "info",
    body: tipsData.value.con,
    className: "dialog-classp32",
    confirmBtn: tipsData.value.btn,
    onConfirm: () => {
      if (squareShow.value) {
        openSquareVisible.value = true;
      } else {
        renewalDialogVisible.value = true;
      }
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const figureAuth = ref(false);
const getAppAuth = async () => {
  const [err, res] = await to(getAppsState({ teamId: teamId.value }));
  if (err) {
    return;
  }
  const { data } = res;
  console.log(data.data, "figureAuth");
  if (data.data?.government || data.data?.member || data.data?.cbd|| data.data?.association|| data.data?.uni) {
    figureAuth.value = true;
  } else {
    figureAuth.value = false;
  }
};
// const figureAuthTip = () => {
//   const myDialog = DialogPlugin({
//     header: t("niche.tip"),
//     theme: "info",
//     body: t('niche.szpttip'),
//     className: "dialog-classp32",
//     onConfirm: () => {
//       myDialog.hide();
//       jumpFigureRun();
//     },
//     onCancel: () => {
//       myDialog.hide();
//     },
//   });
// };
const listingRunReq = (id, myDialog) => {
  releaseRelease(id).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc();
      myDialog.hide();
    }
  });
};
const listingMain = (rowData) => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("niche.sxjss"),
    className: "dialog-classp32",
    onConfirm: () => {
      listingRunReq(rowData.id, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const listingRun = async (rowData) => {
  if (pageShow.value) {
    plazaRenew();
  } else {
    listingMain(rowData);
  }
};

const featureRun = (eventId) => {
  const rowData = nicheDetail.value;
  console.log(eventId, rowData);
  switch (eventId) {
    case "listing":
      listingRun(rowData);
      break;
    case "delist":
      delistRun(rowData, 0);
      break;
    case "edit":
      editBusRun(rowData);
      break;
    case "pinned":
      editHotRun(rowData);
      break;
    case "topCancel":
      editHotRun(rowData);
      break;
    case "copy":
      copyBusRun(rowData);
      break;
    case "delete":
      businessDelRun(rowData);
      break;
    case "extend":
      extendRun(rowData);
      break;
    default:
      console.log("推广");
      break;
  }
};
const getFeature = (rowData) => {
  // 发布状态（0：未生效，1：{{t('niche.szpt')}}，2：已下架，3：已失效，4：已删除）
  if (rowData.effective_state === 3) {
    return [
    {
        title: t("niche.tg"),
        id: "extend",
        icon: "icontotopcancel",
        show: true,
        keys: [0, 1, 2, 3, 4],
      },
      {
        title: t("niche.copy"),
        id: "copy",
        icon: "iconcopy",
        show: true,
        keys: [0, 1, 2, 3, 4],
      },
    ];
  }
  const funcs = [
    {
      title: t('niche.tg'),
      id: "extend",
      icon: "icontotopcancel",
      show: true,
      keys: [],
    },
    {
      title: t("niche.sj"),
      id: "listing",
      icon: "iconListing",
      show: false,
      keys: [2],
    },
    {
      title: t("niche.xj"),
      id: "delist",
      icon: "iconcommodity-reduce",
      show: false,
      keys: [1],
    },
    {
      title: t("niche.edit"),
      id: "edit",
      icon: "iconedit",
      show: false,
      // keys: [0, 2, 3],
      keys: [],
    },
    {
      title: t("niche.top"),
      id: "pinned",
      icon: "icontotop",
      show: false,
      keys: [0, 1, 2],
    },
    {
      title: t("niche.topcal"),
      id: "topCancel",
      icon: "icontotopcancel",
      show: false,
      keys: [0, 1, 2],
    },
    {
      title: t("niche.copy"),
      id: "copy",
      icon: "iconcopy",
      show: true,
      keys: [0, 1, 2, 3, 4],
    },
    {
      title: t("niche.del"),
      id: "delete",
      icon: "icondelete",
      show: false,
      keys: [0, 1, 2, 3],
    },
  ];
  const topKey = rowData.is_top ? "pinned" : "topCancel";
  for (const item of rowData.channel_data) {
    for (const func of funcs) {
      if (func.keys.includes(item.release_state)) {
        func.show = true;
      }
    }
  }
    if(rowData.is_external_link){
    funcs[4].show = false;
  }
  if (rowData.operation_rules?.can_edit) {
    funcs[3].show = true;
  }
  return funcs.filter((item) => item.show).filter((item) => item.id !== topKey);
};
const getDetailRun = () => {
  getDetail(nicheId.value).then((res) => {
    if (res.data) {
      nicheDetail.value = res.data.data;
      func.value = getFeature(res.data.data);
    }
  });
};
const nicheId: any = ref(0);

onActivated(() => {
  settabItem();
  init();
});

const init = () => {
  getTeamAnnualFeeRun();
  getAppAuth();
  nicheId.value = route.query?.id || 0;
  const type: any = Number(route.query?.to || "1");
  console.log(nicheId.value, type);
  showType.value = type;
  getDetailRun();
};

onMounted(() => {
  settabItem();
  init();
});

const extendRun = (row) => {
  console.log("推广");
  router.push(`/workBenchIndex/detailExtendPage?to=2&id=${row.id}&title=${row.title}`);
};

const showDetailRun = () => {
  showType.value = 1;
  getDetailRun();
};
</script>

<style lang="less" scoped>
@import "./styles/common.less";
.detail-page {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  border-radius: 0px 0px 16px 0px;
  // height: 100vh;
  background-image: url(@/assets/niche/bg_small.png);
  overflow-y: auto;
  background-position: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .detail-content {
    width: 1168px;
    //min-width: 1104px;
    // height: calc(100vh - 64px);
    // overflow-y: auto;
    .tab {
      height: 56px;
      display: flex;
      align-items: center;
      align-self: stretch;
      border-bottom: 1px solid #fff;
      gap: 44px;

      .item {
        color: var(--brand-kyy_color_brand_default, #4d5eff);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
        position: relative;
        cursor: pointer;
        .tag {
          width: 16px;
          height: 3px;
          flex-shrink: 0;
          border-radius: 1.5px;
          background: var(--brand-kyy_color_brand_default, #4d5eff);
          position: absolute;
          top: 37px;
          left: 22px;
        }
      }
      .item2 {
        color: var(--text-kyy_color_text_1, #1a2139);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
        cursor: pointer;
      }
    }
    .src-box::-webkit-scrollbar {
      width: 0px;
    }
    .src-box {
      width: 100%;
      height: calc(100vh - 142px);
      overflow-y: auto;
      border-radius: 8px;
      margin-top: 12px;
    }
    .baaak {
      width: 100%;
      height: 12px;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);
    }
    .create-info {
      display: flex;
      padding: 12px 16px;
      justify-content: center;
      align-items: flex-start;
      gap: 12px;
      align-self: stretch;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_light, #fff);
      width: 100%;
      flex-wrap: wrap;
      // margin: 12px 0;
      .create-item {
        width: 552px;
        display: flex;
        align-items: center;
        gap: 16px;
        .label {
          width: 64px;
          color: var(--text-kyy_color_text_3, #828da5);
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
        .text {
          color: var(--text-kyy_color_text_1, #1a2139);
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }
    }
    .detail-box {
      width: 100%;
      background: #fff;
      padding: 16px 24px;
      border-radius: 8px;
      margin-top: 12px;
      min-height: calc(100% - 92px);
    }
    .placeholder {
      width: 100%;
      height: 6px;
      opacity: 0;
    }
  }

  .detail-extend {
    width: 1168px;
    height: calc(100vh - 58px);
  }
  .detail-footer {
    margin-top: 12px;
    display: flex;
    height: 64px;
    padding: 16px 24px;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #fff);
    margin-bottom: 16px;
    justify-content: center;
  }
}
.danger-btn:hover {
  border-radius: var(--radius-kyy_radius_button_s, 4px);
  border: 1px solid var(--color-button_secondaryError-kyy_color_button_secondaryError_border_hover, #e15971);
  background: var(--color-button_secondaryError-kyy_color_button_secondrayError_bg_hover, #f7d5db);
}
.danger-btn {
  display: flex;
  width: 80px;
  height: 32px;
  min-height: 32px;
  max-height: 32px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  color: var(--color-button_secondaryError-kyy_color_button_secondrayError_text_default, #d54941);
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  gap: 4px;
  cursor: pointer;
  border-radius: var(--radius-kyy_radius_button_s, 4px);
  border: 1px solid var(--color-button_secondaryError-kyy_color_button_secondaryError_border_dedault, #d54941);
  background: var(--color-button_secondaryError-kyy_color_button_secondrayError_bg_default, #fdf5f6);
}
</style>
