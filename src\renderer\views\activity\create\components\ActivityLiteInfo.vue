<template>
  <div>
    <t-form
      ref="formRef"
      class="activity-lite-create-form"
      :data="activityFormData"
      :rules="rules"
      label-align="top"
      scroll-to-first-error="smooth"
    >
      <div class="activity-lite-create-form-group w-872 px-24 py-16 mx-auto rounded-8 bg-[#fff]">
        <div class="text-[#1A2139] text-16 leading-24 font-600 mb-12">基本信息</div>

        <!--活动归属-->
        <t-form-item :label="t('activity.activity.teamId_label')" name="basic.teamId">
          <activity-team-select
            :value="activityFormData.basic.teamId"
            :disabled="isScene || published"
            @change="onTeamChange"
          />
        </t-form-item>

        <!--活动主题-->
        <t-form-item class="t-form-item__subject" :label="t('activity.activity.subject_label')" name="basic.subject">
          <t-textarea
            v-model="activityFormData.basic.subject"
            :placeholder="t('activity.activity.subject')"
            :autosize="{ minRows: 1 }"
            maxlength="50"
            :show-limit-number="true"
            clearable
            style="padding-right: 45px"
          />
        </t-form-item>

        <!--活动类型-->
        <t-form-item :label="t('activity.activity.categories')" name="basic.categoryId">
          <activity-category-select v-model:value="activityFormData.basic.categoryId" @change="onCategoryChange" />
        </t-form-item>

        <!--主题图片-->
        <t-form-item :label="t('activity.activity.assetUrl')" name="basic.assetUrl">
          <activity-theme-image-upload
            v-model:value="activityFormData.basic.assetUrl"
            @change="assetUrlUploaded = true"
          />
        </t-form-item>

        <!--活动时间-->
        <t-form-item
          class="activity-duration-form-item"
          :label="t('activity.activity.durationObj_label')"
          name="basic.duration"
        >
          <t-form-item class="!mr-0" name="basic.duration.startTime" :required-mark="false">
            <t-date-picker
              v-model="startTime"
              class="w-408"
              enable-time-picker
              clearable
              format="YYYY-MM-DD HH:mm"
              :placeholder="t('activity.activity.startTime')"
              :popup-props="{
                placement: 'left',
              }"
              :default-time="moment.unix(currentTimestamp).format('HH:mm')"
              @change="onChangeStartTime"
            />
          </t-form-item>

          <t-form-item class="activity-duration-end-time-form-item" name="basic.duration.endTime" :required-mark="false">
            <t-tooltip v-if="!startTime" content="请先选择开始时间">
              <t-date-picker
                v-model="endTime"
                class="w-408 ml-8"
                enable-time-picker
                clearable
                format="YYYY-MM-DD HH:mm"
                disabled
                :placeholder="t('activity.activity.endTime')"
                :disable-date="{
                  before: moment(startTime).add(-1, 'day').format('YYYY-MM-DD HH:mm:ss'),
                }"
                :popup-props="{
                  placement: 'left',
                }"
              />
            </t-tooltip>

            <t-date-picker
              v-else
              v-model="endTime"
              class="w-300 ml-8"
              enable-time-picker
              clearable
              format="YYYY-MM-DD HH:mm"
              :placeholder="t('activity.activity.endTime')"
              :disable-date="{
                before: moment(startTime).add(-1, 'day').format('YYYY-MM-DD HH:mm:ss'),
              }"
              :popup-props="{
                placement: 'left',
              }"
            />
          </t-form-item>
        </t-form-item>

        <!--活动地点-->
        <t-form-item :label="t('activity.activity.location_label')" name="basic.location.title">
          <activity-location-input v-model:location="activityFormData.basic.location" />
        </t-form-item>

        <!--活动详情-->
        <t-form-item class="editorWrap" :label="t('activity.activity.content_label')" name="particulars.content">
          <div class="min-h-280 w-full editor-wrap">
            <Editor
              ref="editorRef"
              editor-type="A"
              root-dir="activity"
              :options="{ toolbar: ['annex', 'link', 'code'], height: 280 }"
              :maxlength="false"
              @img-insert="imgInsert"
              @update="handleContentChange"
            />
          </div>
        </t-form-item>
      </div>

      <div class="activity-lite-create-form-group mt-12 w-872 px-24 py-16 mx-auto rounded-8 bg-[#fff]">
        <div class="text-[#1A2139] text-16 leading-24 font-600 mb-12">活动设置</div>

        <!--参与人员范围-->
        <t-form-item
          :class="{ '!mb-8': activityFormData.basic.actorScope === 'Internal' }"
          :label="t('activity.activity.range')"
          name="basic.actorScope"
        >
          <t-checkbox-group :value="actorScope" :disabled="published" @change="onActorScopeChange">
            <t-checkbox key="Publish" value="Publish">{{ t('activity.activity.openRegister') }}</t-checkbox>
            <t-checkbox key="Internal" value="Internal">
              {{ isPersonal ? '我的好友' : '组织架构' }}
            </t-checkbox>
          </t-checkbox-group>
        </t-form-item>

        <t-form-item v-if="activityFormData.basic.actorScope === 'Internal'" label="" name="member.actors">
          <activity-actor-selector v-model:actors="activityFormData.members.actors" />
        </t-form-item>

        <!--发布到数字平台（活动归属=个人且有【个人入会】加入并且是正式成员的数字平台）-->
        <t-form-item v-if="activityFormData.advanced.platforms.length || (isPersonal && personalPlatforms.length)" label="发布到数字平台" name="platforms">
          <Activity-platform-selector v-model:platforms="activityFormData.advanced.platforms" :options="personalPlatforms" />
        </t-form-item>

        <!--报名费设置-->
        <template v-if="!isPersonal && !activityFormData.advanced.registFeeEnable">
          <t-form-item
            class="regist-fee-form-item !mb-8"
            name="advanced.registFeeEnable"
            label-align="left"
          >
            <template #label>
              <div class="flex items-center">
                <span>报名费金额</span>
              </div>
            </template>
            <t-switch
              :value="activityFormData.advanced.registFeeEnable"
              @click="handleRegistFeeClick"
            />
          </t-form-item>

          <div class="text-[#ACB3C0] mb-24">开启后可设置需要收取的报名费金额</div>
        </template>

        <t-form-item
          v-if="activityFormData.advanced.registFeeEnable"
          name="advanced.registFee.value"
        >
          <template #label>
            <div class="inline-flex items-center">
              <div class="w-120">报名费金额</div>
              <t-switch
                :value="activityFormData.advanced.registFeeEnable"
                @click="handleRegistFeeClick"
              />
            </div>
          </template>

          <div class="flex items-center gap-8">
            <t-input-number
              v-model="activityFormData.advanced.registFee.value"
              theme="normal"
              class="w-294"
              placeholder="请填写参与人报名时需缴纳费用"
              :decimal-places="2"
              :min="0"
              :max="9999999.99"
              @blur="onRegistFeeInputBlur"
            />
            <span>元</span>
          </div>
        </t-form-item>

        <!--上传附件-->
        <t-form-item name="particulars.files">
          <template #label>
            <div class="flex items-center gap-4">
              <span>{{ t('activity.activity.files_label') }}</span>
              <t-tooltip :content="t('activity.activity.files_tip')">
                <img class="flex-shrink-0" src="@renderer/assets/activity/icon_help.svg" alt="">
              </t-tooltip>
            </div>
          </template>

          <activity-file-upload v-model:files="activityFormData.particulars.files.files" />
        </t-form-item>

        <!--活动提醒（最多9条）-->
        <t-form-item label="活动提醒" name="advanced.reminders">
          <activity-reminder-selector v-model:reminders="activityFormData.advanced.reminders" />
        </t-form-item>
      </div>
    </t-form>
  </div>
</template>
<script setup lang="ts">
import { computed, inject, ref, watch } from 'vue';
import moment from 'moment';
import { useI18n } from 'vue-i18n';
import _ from 'lodash';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import ActivityCategorySelect from '@/views/activity/create/components/ActivityCategorySelect.vue';
import Editor from '@/components/editor/index.vue';
import ActivityReminderSelector from '@/views/activity/create/components/ActivityReminderSelector.vue';
import ActivityFileUpload from '@/views/activity/create/components/ActivityFileUpload.vue';
import ActivityTeamSelect from '@/views/activity/create/components/ActivityTeamSelect.vue';
import ActivityThemeImageUpload from '@/views/activity/create/components/ActivityThemeImageUpload.vue';
import ActivityActorSelector from '@/views/activity/create/components/ActivityActorSelector.vue';
import ActivityLocationInput from '@/views/activity/create/components/ActivityLocationInput.vue';
import { activityRemoveAllActor, getActivityListActors, getAssetsList } from '@/api/activity';
import { useCurrentTimestamp } from '@/hooks/useCurrentTimestamp';
import { getOpenid } from '@/utils/auth';
import { useActivityStore } from '@/views/activity/store';
import { cardIdType } from '@/views/identitycard/data';
import { checkMerchantStatus } from '@/api/activity/product';
import { useCheckAuth } from '../../hooks/useCheckAuth';
import ActivityPlatformSelector from '@/views/activity/create/components/ActivityPlatformSelector.vue';
import { usePlatforms } from '@/views/activity/hooks/usePlatforms';

const { t } = useI18n();

const { published } = defineProps({
  // 活动是否已经发布
  published: {
    type: Boolean,
    default: false,
  },
});

const formRef = ref(null);

const editorRef = ref(null);

const activityStore = useActivityStore();

// 活动主题图片是否已主动上传标志
const assetUrlUploaded = ref(false);

// 活动表单数据
const activityFormData = inject('activityFormData');
const selectedTeam = inject('selectedTeam');
const isPersonal = inject('isPersonal');
const isScene = inject('isScene');

const isCreate = computed(() => !activityFormData.id);

// 活动开始时间（特殊处理字段）
const startTime = computed({
  get() {
    return activityFormData.basic.duration.startTime
      ? moment.unix(activityFormData.basic.duration.startTime).format('YYYY-MM-DD HH:mm')
      : null;
  },
  set(val) {
    activityFormData.basic.duration.startTime = moment(val).unix();
  },
});

// 活动结束时间（特殊处理字段）
const endTime = computed({
  get() {
    return activityFormData.basic.duration.endTime
      ? moment.unix(activityFormData.basic.duration.endTime).format('YYYY-MM-DD HH:mm')
      : null;
  },
  set(val) {
    activityFormData.basic.duration.endTime = moment(val).unix();
  },
});

// 获取当前时间戳，用于默认时间
const { currentTimestamp } = useCurrentTimestamp();

// 处理参与人员范围值，将checkbox应用为单选的数值
const actorScope = computed({
  get() {
    return [activityFormData.basic.actorScope];
  },
  set() {
    return [activityFormData.basic.actorScope];
  },
});

const noteData = ref({
  content: {
    images: [],
  },
  annex_size: 0,
  user_id: '',
  openId: '',
});

// 表单验证规则
const rules = {
  'basic.teamId': [{ required: true, message: t('activity.activity.teamId_placeholder') }],
  'basic.subject': [{ required: true, message: t('activity.activity.subject') }],
  'basic.categoryId': [{ required: true, message: t('activity.activity.activityType_placeholder') }],
  'basic.assetUrl': [{ required: true, message: t('activity.activity.assetUrl') }],
  'basic.duration': [
    {
      required: true,
    },
  ],
  'basic.duration.startTime': [
    {
      validator: () => {
        if (!startTime.value) {
          return Promise.resolve({
            result: false,
            message: t('activity.activity.startTimePlaceholder'),
            type: 'error',
          });
        }
        return Promise.resolve({ result: true });
      },
    },
  ],
  'basic.duration.endTime': [
    {
      validator: () => {
        if (startTime.value && !endTime.value) {
          return Promise.resolve({ result: false, message: t('activity.activity.endTimePlaceholder'), type: 'error' });
        }
        if (startTime.value && !moment(endTime.value).isAfter(moment(startTime.value))) {
          return Promise.resolve({ result: false, message: t('activity.activity.endTimeBefore'), type: 'error' });
        }
        return Promise.resolve({ result: true });
      },
    },
  ],
  'basic.location.title': [{ required: true, message: t('activity.activity.location_placeholder') }],
  'advanced.registFee.value': [{ required: true, message: '请填写不小于 0 且精确到小数点后两位的数' }],
};

const { personalPlatforms, getPersonalPlatforms } = usePlatforms();

// 当前组织是否已经绑定组织收款
const isSelectedTeamMerchantBind = ref(false);

// 活动归属切换
const onTeamChange = async (value) => {
  if (activityFormData.members.actors.length) {
    // 如果添加了参与人员，切换活动归属时会清空所有参与人员，需要用户确认是否继续操作
    await actorClearConfirm();
  }

  // 如果切换到组织，则判断对应组织是否已退出
  if (value !== getOpenid()) {
    try {
      await activityStore.checkIsInGroup(value, false, null, false, false);
    } catch {
      MessagePlugin.warning('已退出当前组织');
      activityFormData.basic.teamId = getOpenid();
      return;
    }
  } else {
    // 如果是个人创建活动，则清空报名费设置相关内容
    activityFormData.advanced.registFeeEnable = false;
    activityFormData.advanced.registFee.value = null;
  }

  activityFormData.basic.teamId = value;
};

// 活动类型切换
const onCategoryChange = async (categoryId) => {
  if (!categoryId) {
    return;
  }
  // 如果没有手动上传过主题图片，则获取对应类型的主题图片
  if (!assetUrlUploaded.value) {
    const res = await getAssetsList({ id: categoryId });
    activityFormData.basic.assetUrl = res?.data?.data.items?.[0]?.url;
  }
};

// 活动开始时间切换
const onChangeStartTime = () => {
  // 活动开始时间改变时如果设置了结束时间则清空活动结束时间
  if (activityFormData.basic.duration.endTime) {
    activityFormData.basic.duration.endTime = null;
  } else if (activityFormData.basic.duration.startTime) {
    // 如果没有设置活动结束时间，设置了活动开始时间，,则结束时间默认开始后2小时
    activityFormData.basic.duration.endTime = moment(activityFormData.basic.duration.startTime * 1000)
      .add(2, 'hours')
      .unix();
  }
};

// 切换参与人员范围
const onActorScopeChange = async (value, context) => {
  if (activityFormData.members.actors.length) {
    // 如果添加了参与人员，切换活动归属时会清空所有参与人员，需要用户确认是否继续操作
    await actorClearConfirm();
  }

  // 使用单选模式
  if (context.type === 'uncheck') {
    activityFormData.basic.actorScope = context.option.value;
    return;
  }
  activityFormData.basic.actorScope = context.option.value;
};

// 参与人员清空确认
const actorClearConfirm = () => new Promise((resolve, reject) => {
  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: '修改后将清空【人员管理-参与人员/协作人】中已添加的成员，确定修改？',
    theme: 'info',
    confirmBtn: '确认修改',
    cancelBtn: '取消',
    onConfirm: async () => {
      // 执行清空
      activityFormData.members.actors = [];

      // 如果不是新增数据，则需要调用接口清空所有参与人
      if (!isCreate.value) {
        activityRemoveAllActor({
          activityId: activityFormData.id,
        });
      }

      confirmDia.destroy();
      resolve();
    },
    onCancel: () => {
      confirmDia.destroy();
      reject();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
      reject();
    },
  });
});

const editorRenderContent = () => {
  // 活动详情富文本内容回显
  const delta = activityFormData.particulars.content ? JSON.parse(activityFormData.particulars.content) : [];
  editorRef.value?.renderContent({ ops: delta }, false);
};

// 富文本上传图片
const imgInsert = (val) => {
  val.forEach((img) => {
    // 更新content images
    const imgObj = {
      name: img.name,
      size: img.size,
      type: img.type.split('/')[1],
      url: img.url,
    };
    noteData.value.content.images.push(imgObj);
  });
};

// 富文本内容更新
const handleContentChange = (contents) => {
  const content = {
    imageUrl: '',
    description: '',
    images: [],
    attachments: [],
    delta: '',
  };
  /**
   * 处理content里的数据
   * description： 所有文本信息加起来
   * imageUrl：第一张图片
   * images：所有的图片集合
   * attachments：所有的文件集合
   * delta：富文本内容
   */
  const delta = [];
  // 附件大小
  let size = 0;
  contents.forEach((v) => {
    let deltaItem = _.cloneDeepWith(v);
    if (typeof v.insert === 'string') {
      content.description += v.insert.trim();
    }
    if (v.insert?.image) {
      !content.imageUrl && (content.imageUrl = v.insert.image);
      const imgItem = noteData.value.content.images.find((img) => img.url === v.insert.image);
      content.images.push(imgItem);
      imgItem?.size && (size += imgItem.size);
    }
    if (v.insert?.custom) {
      size += v.insert.custom.size;
      content.attachments.push(v.insert.custom);
      const atta = { attachment: JSON.stringify(v.insert.custom) };
      deltaItem.insert = { custom: JSON.stringify(atta) };
    }
    delta.push(deltaItem);
  });
  content.delta = JSON.stringify(delta);
  activityFormData.particulars.content = content.delta;
  if (content.delta === '[{"insert":"\\n"}]') {
    activityFormData.particulars.content = null;
  }
};

// 加载活动参与人员
const loadActors = async () => {
  const res = await getActivityListActors({
    activityId: activityFormData.id,
    replyStatus: -1, // 固定查询全部报名状态
    role: -2, // 固定查询普通参与者与报名人员
    approveStatus: -1, // 固定查询全部审核状态
  });

  activityFormData.members.actors = res.data.data.actors.map((actor) => ({
    ...actor,
    targetId: actor.cardId || actor.openId,
  }));
};

// 获取当前选择的组织是否开通组织收款
const getSelectedTeamMerchantStatus = async () => {
  const res = await checkMerchantStatus({
    teamId: selectedTeam.value.teamId,
  });
  isSelectedTeamMerchantBind.value = res.data.data.result;

  if (!isSelectedTeamMerchantBind.value) {
    // 如果没有开通组织收款，则清空报名费设置相关内容
    activityFormData.advanced.registFeeEnable = false;
    activityFormData.advanced.registFee.value = null;
  }
};

// 报名费输入框失焦事件
const onRegistFeeInputBlur = () => {
  // 自动修正最大最小值
  if (activityFormData.advanced.registFee.value <= 0) {
    activityFormData.advanced.registFee.value = null;
  } else if (activityFormData.advanced.registFee.value >= 10000000) {
    activityFormData.advanced.registFee.value = 9999999.99;
  }
};

// 切换非个人活动归属时查询组织商户状态
watch(
  () => activityFormData.basic.cardId,
  () => {
    if (cardIdType(activityFormData.basic.cardId) !== 'personal') {
      getSelectedTeamMerchantStatus();

      activityFormData.advanced.platforms = [];
    } else {
      getPersonalPlatforms();
    }
  },
  {
    immediate: true,
  },
);

// 检查用户权限
const { handleRegistFeeClick } = useCheckAuth({
  teamId: computed(() => selectedTeam.value.teamId),
  onRegistFeeEnable: () => {
    activityFormData.advanced.registFeeEnable = !activityFormData.advanced.registFeeEnable;
  },
});

defineExpose({
  editorRenderContent,
  loadActors,
  validate: (params) => formRef.value.validate(params),
});
</script>

<style lang="less" scoped>
:deep(.activity-lite-create-form) {
  .t-form__label {
    line-height: 22px;
    min-height: 22px;
    padding: 0;

    &.t-form__label--top {
      margin-bottom: 8px;
    }
  }

  .t-form__controls {
    min-height: 22px;
    .t-form__controls-content {
      min-height: 22px;
    }
  }

  .t-form__controls-content {
    .t-input {
      padding-left: 12px;

      &.t-is-disabled {
        border-color: #eceff5 !important;
        background: #fff !important;
        .t-input__inner {
          color: #acb3c0 !important;
        }
      }

      .t-input__limit-number {
        color: #acb3c0;
        font-size: 12px;
      }
    }

    .t-checkbox__input {
      border-radius: 50%;
    }
  }

  .t-form-item__subject {
    .t-textarea__inner {
      height: 34px;
      min-height: 34px;
    }
    .t-textarea__limit {
      display: inline !important;
      line-height: 20px;
      position: absolute;
      right: 7px;
      bottom: 7px;
      color: var(--text-kyy_color_text_5, #acb3c0);
      font-size: 12px;
    }
  }

  .activity-duration-form-item {
    .t-is-error .t-input__extra {
      position: absolute !important;
      color: var(--td-error-color) !important;
    }

    .activity-duration-end-time-form-item {
      .t-is-error .t-input__extra {
        left: 8px;
      }
    }
  }

  .regist-fee-form-item{
     .t-form__label{
       width: 130px !important;
     }

    .t-form__controls{
      margin-left: 0 !important;
    }
  }

  .upload-file-card-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .upload-file-card {
      margin-top: 0;
      width: 408px;
    }
  }

  .lk-editor {
    border-radius: 4px;
    border: 1px solid #d5dbe4;
  }

  .editorWrap {
    display: flex;
    flex-direction: column;
    .t-form__controls {
      flex-grow: 2;
    }
    .t-form__controls-content {
      height: 100%;
    }
  }

  .editor-wrap {
    :deep(.lk-editor) {
      overflow: hidden;
    }
    .lk-toolbar {
      border-radius: 4px 4px 0 0;
    }
    :deep(.lk-editor-body) {
      height: calc(100% - 48px) !important;
    }
  }
}
</style>
