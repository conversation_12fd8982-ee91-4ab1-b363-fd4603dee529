<template>
  <!-- show-overlay -->
  <!-- <t-loading size="medium" class="memberLoading" :loading="!isAShow"   text="加载中..." > -->
  <!-- v-lkloading="{ show:!isAShow, height:false, opacity:true }"  -->
  <div class="pageTop">
    <!-- <div class="fixTop" :class="{isOpacity: scrolledDistance > 450}">
      <div  class="header">
        <img src="@/assets/member/icon/linker.png">
        <span class="bot">·</span>
        <span class="title">数字平台</span>

      </div>
      <t-button theme="primary" @click="onCreateMember">{{ $t('member.long.org_1') }}</t-button>
    </div> -->

    <div class="backTop cursor" v-show="scrolledDistance > 30" :class="{isOpacity: scrolledDistance > 60}"
      @click="scrollToTop">
      <!-- <iconpark-icon name="iconarrowup" class="iconarrowup"></iconpark-icon> -->
      <iconpark-icon class="iconarrowup" name="icontopinside"></iconpark-icon>

    </div>
    <div style="background-color: #fff; height: inherit;">
      <div ref="containerFlets" class="leaflets">
        <div class="content">
          <!-- <span class="global"></span> -->
          <div class="box">
            <div class="header">
              <img src="@/assets/member/icon/linker.png">
              <span class="bot">·</span>
              <span class="title">{{$t('member.squarek.m')}}</span>
              <span class="tip">{{ $t('member.regular.selected_products') }}</span>
            </div>
            <div class="desc">
              {{$t('member.squarek.m')}}&nbsp;&nbsp;{{$t('member.digital.l')}}
            </div>
            <div class="tips">
              {{$t('association.aso.g')}}
            </div>
            <!-- <div class="create cursor" @click="onCreateMember">
              {{ $t('member.regular.create_member') }}

            </div> -->

            <div class="cards">
              <div v-for="card in cards" :key="card.icon" :class="onGearateClass(card.icon, card)"
                @click="card.isOpen ? onOpenNewLeaft(card) : ''">
                <div class="card-name">

                  <i :class="[`i-svg-color:${card.icon}`, 'svg']"></i>
                  <span class="text">     {{ card.label }}</span>


                </div>
                <div class="card-desc">
                  {{ card.value }}
                </div>
                <t-button style="font-weight: 600;" theme="outline" class="tButton mt-8" :class="{isDisable: !card.isOpen, cursor: card.isOpen}">
                  {{card.isOpen ? t('member.digital.h'): t('member.digital.i')}}
                </t-button>
              </div>
            </div>


          </div>
          <div class="exampleT">
            <div class="example min-h-100%">
              <div class="example-desc">
                <div class="toLeft">
                  <div class="top"> {{ $t('member.regular.imported_member') }}</div>
                  <div class="tip"> {{$t('member.digital.g')}}</div>
                </div>
                <div class="toRight">
                  <span class="btn" @click="onJoinDigital">
                    <iconpark-icon name="iconpeopleadd" class="add"></iconpark-icon>
                    加入数字平台
                  </span>
                </div>
              </div>
              <div class="example-row mt-28px">

                <div v-for="(menu, menuIndex) in memberList" :key="menuIndex" class="bofox">

                  <div class="public">
                    <img v-lazy="menu.avatar || ORG_DEFAULT_AVATAR" class="logo">
                    <span class="public-name line-1 mt-12px">{{ menu.name }}</span>
                    <span class="public-tip line-2 mt-4px">{{ menu.intro }}</span>
                  </div>
                  <!-- <span class="bofox-tip">{{ menu.tab_one_desc }}</span> -->

                  <div class="description">
                    <div class="relate">
                      <img v-lazy="menu.avatar || ORG_DEFAULT_AVATAR" class="logo">
                      <div class="title line-1"> {{ menu.name }}</div>
                      <span class="square cursor" @click="onGoSquarePage(menu)">{{ $t('member.long.org_2') }}</span>
                    </div>
                  </div>
                </div>

              </div>
              <div class="example-more mt-24px">
                <span v-if="(!page) || (page && page.nextPageToken )" class="more cursor" @click="onMore"> {{ isLoading
                  ? '加载中...': '加载更多' }} </span>
                <span v-else class="noempty">
                  <span class="line"></span><span class="toText">{{ $t('member.long.org_3') }}</span> <span
                    class="line"></span>
                </span>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- </t-loading> -->
  <!-- <t-back-top
    container=".head-menu-wrap-box"
    shape="circle"
    size="small"
    :visible-height="240"
    :offset="['16px', '30px']"
    style="position: fixed"
  >
    <div class="back-top">
      <t-icon name="arrow-up" class="icon" />
    </div>
  </t-back-top> -->
  <!-- <CreateOrganizeModel ref="createOrganizeModelRef" /> -->
  <joinDrawer ref="joinDrawerRef" />
  <Tricks :offset="{ x: '-16', y: '-94' }" :styles="{width: '48px', height: '48px'}" :uuid="'数字平台-宣传页'" />
</template>

<script setup lang="ts">
import { ref, toRaw, watch, onMounted, onUnmounted } from "vue";
// import anhui from '@renderer/assets/member/icon/left-anhui.png';
// import aomen from '@renderer/assets/member/icon/left-aomen.png';
// import company from '@renderer/assets/member/icon/left-company.png';
// import jiangsu from '@renderer/assets/member/icon/left-jiangsu.png';
// import CreateOrganizeModel from '@renderer/views/member/member_number/modal/create-organize-modal.vue';
import { getSquaresAxios } from "@renderer/api/member/api/businessApi";
import { getResponseResult } from "@renderer/utils/myUtils";
import { useI18n } from "vue-i18n";
import { toSquareHome } from "@renderer/views/square/utils/ipcHelper";
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
import { useApi } from '@renderer/views/member/hooks/api';
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import { useRouter } from "vue-router";
import { platform } from '@renderer/views/digital-platform/utils/constant';
import { SquareType } from "@renderer/api/square/enums";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import joinDrawer from "@renderer/views/digital-platform/components/join-drawer.vue";


const { menuList, routeList, roleFilter } = useRouterHelper("digitalPlatformIndex");
const router = useRouter();
const store = useDigitalPlatformStore();
const { t } = useI18n();
const isAShow = ref(false);
const { onActionSquare } = useApi();
setTimeout(()=> {isAShow.value = true}, 500 )
const cards = ref([
    {
      icon: 'card_bg_one',
      // label: '数字城市',
      // value: '全国统一大市场的数字化解决方案',
      label: t('member.squarek.r'),
      value: t('member.digital.a'),
      key: 'digital_politics',
      isOpen: true,
    },
    {
      icon: 'card_bg_two',
      label: t('member.regular.number_member'),
      // value: '激活商协资源，提升会员协作效能',
      value: t('member.digital.b'),
      key: 'digital_member',
      isOpen: true,

    },
    {
      icon: 'card_bg_three',
      // label: '数字CBD',
      // value: '打造智慧商业中心，引领城市新风尚',
      label: t('member.digital.c'),
      value: t('member.digital.d'),
      key: 'digital_cbd',
      isOpen: true,

    },
    {
      icon: 'card_bg_four',
      // label: '数字园区',
      // value: '创新驱动产业升级，构建智能生态圈',
      label: t('association.workbench.a'),
      value: t('association.aso.h'),
      key: 'digital_association',
      isOpen: true,
    },
    {
      icon: 'card_bg_five',
      // label: '数字园区',
      // value: '创新驱动产业升级，构建智能生态圈',
      label: t('member.digital.e1'),
      value: t('member.digital.z'),
      key: 'digital_uni',
      isOpen: true,
    },
  ]);


  // const menuList = ref([
  //    {
  //        icon: jiangsu,
  //        name: '江苏省山东商会',
  //        tab_one_tip: '江苏省民政厅4A级资信\n会员单位和团体会员六千余家',
  //        tab_two: '获评:江苏省民政厅4A级资信至2021年底，商会拥有会员单位和团体会员6100余家。会员企业产业分布广泛，主要有建筑工程、化工、新能源、房地产、金融投资、IT业、商贸、文化、农业、物流等。'
  //    },
  //    {
  //        icon: anhui,
  //        name: '广东省安徽商会',
  //        tab_one_tip: '广东省5A级社会组织\n广东年度经济风云商会称号',
  //        tab_two: '获评：广东省5A级社会组织当选：广州市异地商会联合会执行会长单位荣晋:广东省社会组织总会执行会长单位荣获：广东年度经济风云商会称号(广东省唯一获此殊荣的异地商会)'
  //    },
  //    {
  //        icon: anhui,
  //        name: '澳门建造商会',
  //        tab_one_tip: '团结建造业从业员及商号\n建立与政府的沟通渠道',
  //        tab_two: '团结建造业从业员及商号,推动从业员培训及考核制度,建立与政府的沟通渠道 '
  //    },
  //    {
  //        icon: company,
  //        name: '新海通机械租赁有限公司',
  //        tab_one_tip: '澳门机械租赁行业领导者\n澳门泵送行业第一品牌',
  //        tab_two: '《新海通机械租赁有限公司》——澳门机械租赁行业领导者2005年，新海通成立，主要从事建筑机械设备的租赁及销售行业等。2013年，成为澳门泵送行业第一品牌。2015年，成为澳门机械安装业务主要服务商，多次获得各施工项目的安全奖项。2019年，开展鑽切服务，向建筑工程拆除领域进军。2020年至今，服务澳门新濠影汇2期、银河3A、银河3D酒店等多个大型工程项目。 '
  //    },
  // ]);
  const joinDrawerRef = ref(null);
  const onJoinDigital = () => {
    joinDrawerRef.value?.onOpen();
  }

  const isLoading = ref(false);
  const memberList = ref([]);
  const page = ref(null);
  const onGetSquaresAxios = async () => {
    let result = null;
    // eslint-disable-next-line no-async-promise-executor
    isLoading.value = true;
    try {
      result = await getSquaresAxios({ 'page.size': 25, square_type: SquareType.Organization, 'page.next_page_token': page.value ? page.value.nextPageToken : '' }, store?.activeAccount?.teamId);
      result = getResponseResult(result);
      isLoading.value = false;
      if (!result) {

        return;
      }
      memberList.value = memberList.value.concat(result.items);
      page.value = result.page;
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      //   MessagePlugin.error(errMsg);
      console.log(errMsg);
    }
    isLoading.value = false;


  };
  const onMore = () => {
    onGetSquaresAxios();
  };
  const onSearch = () => {
    memberList.value = [];
    onGetSquaresAxios();
  };
  onSearch();
  const onGearateClass = (type, card) => {
    const obj = {
      card: true,
      cursor: card.isOpen
    };
    obj[type] = true;
    return obj;
  };
  const onGoSquarePage = (square) => {
    console.log(square);
    // ipcRenderer.invoke("create-dialog", {
    //   url: `square-single/info?id=${square?.squareId}`,
    //   opts: {
    //     width: 656,
    //   },
    // });
    // onActionSquare(square?.squareId, store?.activeAccount?.teamId)
    onActionSquare({}, square?.squareId)

  };
  // const createOrganizeModelRef = ref(null);
  // const onCreateMember = () => {
  //     createOrganizeModelRef.value?.beginCreate();
  // };
  const scrolledDistance = ref(0); // 滚动距离
  const containerFlets = ref(null);
  const handleScroll = (event) => {
    console.log(event)
    console.log(event.target.scrollTop);
    scrolledDistance.value = event.target.scrollTop;
    // scrolledDistance.value = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
  };
  let animationId = null;
  const scrollToTop = () => {
    console.log(containerFlets.value.scrollTop);
    // containerFlets.value.scrollTop = 0; // 滚动容器到顶部
    cancelAnimationFrame(animationId); // 取消之前的动画

    const scrollTop = containerFlets.value.scrollTop;
    console.log(containerFlets.value.scrollTop);
    const step = Math.ceil(scrollTop / 6); // 每帧滚动的步长
    console.log(step);
    const animate = () => {
      if (containerFlets.value.scrollTop > 0) {
        containerFlets.value.scrollTop -= step;
        animationId = requestAnimationFrame(animate); // 请求下一帧动画
      } else {
        cancelAnimationFrame(animationId); // 动画结束，取消请求
      }
    };

    animationId = requestAnimationFrame(animate); // 开始动画
  };


  const onOpenNewLeaft = (row) => {
    let key = ''
    if (row.key === 'digital_member') {
      key = 'digital_platform_member_leaflets'
    } else if (row.key === 'digital_politics') {
      key = 'digital_platform_politics_leaflets'
    } else if (row.key === 'digital_cbd') {
      key = 'digital_platform_cbd_leaflets'
    } else if (row.key === 'digital_association') {
      key = 'digital_platform_association_leaflets'
    } else if (row.key === 'digital_uni') {
      key = 'digital_platform_uni_leaflets'
    }
    console.log(routeList, 'routeListrouteListrouteList');

    const searchMenu = routeList.find((v) => v.name === key);
    if (!searchMenu) {
      console.error('未找到路由:', key);
      console.log('可用的路由列表:', routeList.map(r => r.name));
      return;
    }
    searchMenu.query = { ...searchMenu.query, platform: platform.digitalPlatform };
    console.log(searchMenu, 'searchMenusearchMenu');

    // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
    router.push({ path: searchMenu.fullPath, query: searchMenu.query });
    store.addTab(toRaw(searchMenu), true);
  }

  onMounted(() => {
    console.log('监听', containerFlets.value)
    setTimeout(() => {
      console.log('添加监听', containerFlets.value?.scroll)
      containerFlets.value?.addEventListener('scroll', handleScroll); // 监听滚动事件

    }, 1000);
    // containerFlets.value?.addEventListener('scroll', handleScroll); // 监听滚动事件
  });
  onUnmounted(() => {
    containerFlets.value?.removeEventListener('scroll', handleScroll); // 取消监听滚动事件
  });

</script>

<style lang="less" scoped>
  @import "@renderer/views/engineer/less/common.less";

  .memberLoading {
    height: 100%;
  }

  .pageTop {
    height: calc(100% - 13px);

    // height: 2000px;
    // overflow-y:auto;
    // padding-bottom: 24px;
    background-color: #fff;
    overflow-y: auto;
  }

  .backTop {
    position: fixed;
    right: 16px;
    bottom: 32px;
    opacity: 0;
    transition: all 0.25s linear;

    z-index: 99;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    // border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
    background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.50));

    .iconarrowup {
      font-size: 20px;
      color: #1A2139;
    }

    .text {
      color: var(--text-kyy_color_text_1, #1A2139);
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      /* 166.667% */
    }
  }

  .isOpacity {
    opacity: 1 !important;
    transition: all 0.25s linear;
  }

  .fixTop {

    position: fixed;
    height: 56px;
    left: 0;
    right: 0;
    background: var(--bg-kyy_color_bg_default, #FFF);
    opacity: 0;
    z-index: 10;
    /* kyy_shadow_s */
    box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
    transition: all 0.25s linear;

    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;

    .header {
      display: flex;
      align-items: center;
      letter-spacing: 1px;

      img {
        // background-position:cover;
        // width: 136px;
        height: 32px;
      }

      .bot {
        margin: 0 12px;
      }

      .title,
      .bot {
        color: var(--brand-kyy-color-brand-default, #4D5EFF);
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px;
        /* 144.444% */
      }

      .tip {
        margin-left: 8px;
        border-radius: 12px 12px 12px 0px;
        background: var(--magenta-kyy-color-magenta-default, #FF4AA1);
        padding: 0 10px;
        color: #FFF;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 166.667% */
      }
    }
  }

  .leaflets {
    // background-image: url('@renderer/assets/member/icon/bg_img.png');
    width: 100%;
    // height: inherit;
    //
    overflow-x: hidden;
    overflow-y: overlay;
    // background-size: cover;
    // background-repeat: no-repeat;
    // background-position: 50% 30%;
    // background-position: center;
    display: flex;
    justify-content: center;
    background-color: #D5E6FC;
    ;
    height: inherit;



    .content {
      display: flex;
      // justify-content: center;
      flex-direction: column;
      width: 1216px;
      // min-width: 1152px;
      // max-width: 1216px;

      // position: relative;
      // width: 1216px;

      // .global {
      //     position: absolute;
      //     z-index: 1;
      //     background-image: url('@/assets/member/icon/bg_img.png');
      //     width: 1920px;
      //     overflow: hidden;
      //     height: 700px;
      //     background-position: center;
      //     background-size: contain;
      // }
      .box {
        // position: absolute;
        // z-index: 2;

        padding: 32px;
        background-image: url('@renderer/assets/member/icon/bg_img_digital.png');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: 100% 30%;

        .header {
          display: flex;
          align-items: center;
          letter-spacing: 1px;

          img {
            // background-position:cover;
            width: 136px;
          }

          .bot {
            margin: 0 12px;
          }

          .title,
          .bot {
            color: var(--brand-kyy-color-brand-default, #4D5EFF);
            font-size: 18px;
            font-style: normal;
            font-weight: 600;
            line-height: 26px;
            /* 144.444% */
          }

          .tip {
            margin-left: 8px;
            border-radius: 12px 12px 12px 0px;
            background: var(--magenta-kyy-color-magenta-default, #FF4AA1);
            padding: 0 10px;
            color: #FFF;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            /* 166.667% */
          }
        }

        .desc {
          color: var(--text-kyy-color-text-1, #1A2139);
          font-size: 32px;
          font-style: normal;
          font-weight: 600;
          line-height: 40px;
          /* 125% */
          margin-top: 24px;
          letter-spacing: 1px;
        }

        .tips {
          color: var(--text-kyy-color-text-2, #516082);
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          line-height: 26px;
          /* 144.444% */
          margin-top: 12px;
          letter-spacing: 1px;

        }

        .create {
          border-radius: 40px;
          background: linear-gradient(90deg, #4D5EFF 15.8%, #7E56FF 100%);
          color: #FFF;
          font-size: 18px;
          font-style: normal;
          font-weight: 600;
          line-height: 26px;
          /* 144.444% */
          padding: 8px 24px;
          margin-top: 24px;
          display: inline-block;
          letter-spacing: 1px;
        }

        .cards {
          margin-top: 36px;
          display: flex;
          gap: 12px;
          flex-wrap: wrap;

          .card {
            width: 376px;
            height: 136px;
            padding: 16px 24px;
            background-size: 100%;
            object-fit: fill;
            background-repeat: no-repeat;
            display: flex;
            flex-direction: column;
            gap: 8px;

            &-name {
              display: flex;
              align-items: center;
              gap: 8px;

              .svg {
                width: 24px;
                height: 24px;
                color: #fff;
              }

              .text {
                color: var(--text-kyy-color-text-2, #516082);

                font-size: 18px;
                font-style: normal;
                font-weight: 600;
                line-height: 26px;
                /* 144.444% */
              }
            }

            &-desc {
              position: relative;

              // padding-left: 8px;
              &::before {
                display: inline-block;
                position: absolute;
                left: 0;
                bottom: 4px;
                top: 0;
                content: ' ';
                width: 2px;
                // background-color: var(--brand-kyy-color-brand-hover, #707EFF);

              }
            }

          }

        }


      }

      .exampleT {
        // margin-top: 32px;
        background-color: #D5E6FC;
        min-height: 50%;
      }

      .example {

        background-image: url('@renderer/assets/member/icon/bg_square_img.png');
        background-repeat: no-repeat;
        object-fit: cover;
        display: flex;
        flex-direction: column;
        padding: 24px 32px 24px;

        // padding-bottom: 0;
        // gap: 48px;
        &-desc {

          display: flex;
          justify-content: space-between;


          .toLeft {
            display: flex;
            flex-direction: column;
            gap: 8px;
          }

          .toRight {
            display: flex;
            align-items: center;

            .btn {
              display: flex;
              gap: 4px;
              height: 32px;
              padding: 0px 12px 0px 8px;
              align-items: center;
              user-select: none;
              cursor: pointer;

              color: var(--text-kyy_color_text_2, #516082);
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
              /* 157.143% */


              border-radius: 16px;
              border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
              background: var(--bg-kyy_color_bg_light, #FFF);
              transition: all 0.15s linear;

              .add {
                transition: all 0.15s linear;
                color: #828DA5;
                font-size: 20px;
              }


              &:hover {
                transition: all 0.15s linear;
                color: var(--brand-kyy_color_brand_hover, #707EFF);

                .add {
                  transition: all 0.15s linear;
                  color: var(--brand-kyy_color_brand_hover, #707EFF);
                }
              }
            }
          }

          .top {
            color: var(--text-kyy-color-text-1, #1A2139);
            font-size: 18px;
            font-style: normal;
            font-weight: 600;
            line-height: 26px;
            /* 144.444% */
          }

          .tip {
            color: var(--text-kyy-color-text-3, #828DA5);

            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
          }
        }

        &-row {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
          width: 100%;

          .bofox {
            width: calc((100% - 48px) / 5);
            // width: 220px;
            // height: 162px;

            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;

            border-radius: 8px;
            // gap: 6px;
            background: var(--bg-kyy-color-bg-default, #FFF);

            .description {
              position: absolute;
              // width: 221px;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              z-index: 2;
              background-color: #fff;
              visibility: hidden;
              // transition: all 0.25s linear;
              // padding: 32px 16px 12px;

              border-radius: 8px;
              // background: var(--bg-kyy-color-bg-default, #FFF);

              background: var(--brand-kyy_color_brand_default, #4D5EFF);

              /* kyy_shadow_s */
              // box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);



              .relate {
                width: 100%;
                position: relative;
                background-image: url('@renderer/assets/member/icon/boxfox.png');
                background-size: cover;
                background-repeat: no-repeat;
                height: 100%;
                border-radius: 8px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 12px;
                padding: 16px;

                .logo {
                  left: 0;
                  right: 0;
                  margin: 0 auto;
                  top: -56px;

                }

                .title {

                  width: inherit;
                  color: var(--text-kyy_color_text_white, #FFF);
                  text-align: center;
                  font-size: 14px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 22px;
                  /* 157.143% */
                }

                .square {

                  border-radius: var(--radius-kyy_radius_button_s, 4px);
                  border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
                  background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
                  padding: 4px 16px;
                  color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
                  text-align: center;
                  font-size: 14px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 22px;
                  /* 157.143% */
                }

              }

            }


            &:hover {
              .description {
                visibility: visible;

                // transition: all 0.25s linear;
                // height: 400

              }

            }

            .logo {
              // position: absolute;
              // top: -24px;
              width: 48px;
              height: 48px;
              border-radius: 48px;
              object-fit: cover;
            }

            .public {
              width: 100%;
              display: flex;
              flex-direction: column;
              align-items: center;
              padding: 16px;
              // gap: 4px;



              &-name {
                flex: 1;
                width: inherit;
                color: var(--text-kyy-color-text-1, #1A2139);
                text-align: center;

                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: 22px;
                /* 157.143% */
              }

              &-tip {
                overflow: hidden;
                color: var(--text-kyy-color-text-3, #828DA5);
                text-align: center;
                text-overflow: ellipsis;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                /* 157.143% */
              }
            }

          }
        }

        &-more {
          display: flex;
          align-items: center;
          justify-content: center;

          .more {
            border-radius: var(--radius-kyy_radius_button_s, 4px);
            border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
            background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
            color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
            padding: 4px 16px;
          }

          .noempty {
            color: var(--text-kyy_color_text_2, #516082);
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
            display: flex;
            align-items: center;
            width: 100%;
            gap: 12px;

            .toText {
              flex: none;
            }

            .line {
              height: 1px;
              background: var(--divider-kyy_color_divider_deep, #D5DBE4);
              width: 100%;
            }
          }
        }
      }

    }

  }

  .tButton {
    display: flex;
    color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
    text-align: center;
    width: fit-content;
    min-height: 32px;
    max-height: 32px;
    justify-content: center;
    align-items: center;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */

    padding: 0 16px;
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
    background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
  }

  .isDisable {
    color: var(--color-button_border-kyy_color_buttonBorder_text_disabled, #ACB3C0);
    text-align: center;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    background-color: rgba(0, 0, 0, 0);
  }

  .card_bg_one {
    background-image: url('@renderer/assets/digital/svg/card_bg_one.svg');

    .card-name {
      .text {
        color: #0EA197 !important;
      }
    }

    .card-desc {
      color: #0EA197;
      //   border-left: 2px solid var(--brand-kyy-color-brand-hover, #707EFF);
      //  &::before {
      //     background-color: var(--brand-kyy-color-brand-hover, #707EFF);
      //  }
    }
  }

  .card_bg_two {
    background-image: url('@renderer/assets/digital/svg/card_bg_two.svg');

    .card-name {
      .text {
        color: #EA8330 !important;
      }
    }

    .card-desc {
      color: #EA8330;

      // &::before {

      // background-color: #46C7FF;

      // }
    }

  }

  .card_bg_three {
    background-image: url('@renderer/assets/digital/svg/card_bg_three.svg');

    .card-name {
      .text {
        color: #4C5EFF !important;
      }
    }

    .card-desc {
      color: #4C5EFF;
      //  &::before {

      //      background: #704DFF;

      //  }
    }
  }

  .card_bg_five {
    background-image: url('@renderer/assets/digital/svg/card_bg_five.svg');

    .card-name {
      .text {
        color: #21ACFA !important;
      }

    }

    .card-desc {
        color: #21ACFA !important;

    }
  }

  .card_bg_four {
    background-image: url('@renderer/assets/digital/svg/card_bg_four.svg');

    .card-name {
      .text {
        color: #ED565C !important;
      }
    }

    .card-desc {
      color: #ED565C;
      // &::before {
      //     background:#0EA197;

      // }
    }

  }
</style>
