<template>
  <home :title="'加入组织'" className="containerPc">
    <template #content>
      <div v-if="showPage" class="join-container">
      <!-- <div class="join-container"> -->
        <div v-if="!hideTop" class="logo" >
          <img v-if="linkInfo.logo" :src="linkInfo.logo" alt="" />
          <img v-else src="@/assets/svg/building-fill.svg" alt="" />
        </div>
        <div class="invite">
          {{
            fromSms
              ? ['member', 'government', 'cbd', 'association'].includes(route.query?.app)
                ? linkInfo.inviter
                : linkInfo.staffName
              : linkInfo.name
          }}
          {{ t('account.invite') }}
        </div>
        <div class="team">
          {{ linkInfo.team }}
        </div>
        <div v-if="!fromSms" class="department">{{ t('account.department') }}{{ linkInfo?.department_name }}</div>
        <div class="info">
          <!-- <t-input :readonly="fromSms" :disabled="fromSms" v-model="join.name" class="mb12" :placeholder="t('account.inputName')" /> -->
          <van-field
            v-model="join.name"
            :readonly="fromSms"
            :disabled="fromSms"
            :placeholder="t('account.inputName')"
            style="font-size: 18px; color: #1a2139; padding: 21px 0"
            @focus="() => inputEventFn('focus')"
            @blur="() => inputEventFn('blur')"
          />
          <div class="tel-code-style">
            <tel-code
              :readonly="fromSms"
              :disabled="fromSms"
              :tel-code="join.telCode"
              :tel-number="join.phone"
              @focus="() => inputEventFn('focus')"
              @blur="() => inputEventFn('blur')"
              @changeCode="(v) => (join.telCode = v)"
              @changeTel="(v) => (join.phone = v)"
              style="font-size: 18px; color: #1a2139"
            />
          </div>
          <!-- <div class="adornment">
            <area-code :readonly="fromSms" :disabled="fromSms" class="area" style="width:70px" v-model="join.telCode" />
            <t-input :readonly="fromSms" :disabled="fromSms" class="phone" v-model="join.phone" :placeholder="t('account.inputTel')" />
          </div> -->
          <!-- <field-code :maxlength="fromSms ? 6 : 4" @onClick="getCode" /> -->
          <van-field
            v-model="join.code"
            :maxlength="6"
            :placeholder="t('account.inputCode')"
            class="mb12 van-bottom kyy-cell"
            style="font-size: 18px; color: #1a2139; padding: 21px 0"
            @focus="() => inputEventFn('focus')"
            @blur="() => inputEventFn('blur')"
          >
            <template #right-icon>
              <div class="f-c">
                <div class="line"></div>
                <div v-if="countdown <= 0" class="verify" @click="checkSM">{{ t('account.sendCode') }}</div>
                <div v-else class="verify">{{ `${t('account.resend')}${countdown}` }}</div>
              </div>
            </template>
          </van-field>
          <van-button
            style="border-radius: 4px"
            type="primary"
            :loading="loginLoading"
            :disabled="loginBtnDisabled"
            block
            @click="joinTeam"
            >{{ t('account.joinNow') }}</van-button
          >
          <!-- <t-button
            v-loading="loginLoading"
            class="login-btn"
            :disabled="loginBtnDisabled"
            block
            theme="primary"
            variant="base"
            @click="joinTeam"
          >{{ t('account.joinNow') }}</t-button> -->
          <div v-if="fromSms" class="info-tips mt12">{{ t('account.joinTip2') }}</div>
          <div v-show="showBottom" class="info-tip">
            {{ t('account.joinTip')
            }}<a target="_blank" :href="`${getBaseUrl('website')}/privacy?uuid=PlatformServices`">{{
              t('account.agreement')
            }}</a
            ><a target="_blank" :href="`${getBaseUrl('website')}/privacy?uuid=PrivacyPolicy`">{{
              t('account.privacy')
            }}</a>
          </div>
        </div>
      </div>
      <div class="f-c-c" v-if="visible" style="height: 100%">
        <div>
          <img src="@/assets/svg/annulus_suress.svg" alt="" style="width: 64px; height: 64px" />
        </div>
        <div class="submit-suc">{{ joinType === 1 ? t('contacts.joinSuc') : t('account.submit') }}</div>
        <div class="wait">
          {{ joinType === 1 ? `${t('contacts.joinSucTip')}"${linkInfo?.team}"` : t('account.waitVerify') }}
        </div>
        <div>
          <van-button type="primary" v-if="!isInMiniApp" :loading="loginLoading" :disabled="loginBtnDisabled" block @click="openLink">{{
            t('account.openLink')
          }}</van-button>
          <!-- <t-button
            v-loading="loginLoading"
            class="open"
            :disabled="loginBtnDisabled"
            block
            theme="primary"
            variant="base"
            @click="openLink"
          >{{ t('account.openLink') }}</t-button> -->
        </div>
      </div>
    </template>
  </home>
  <!-- <t-dialog v-model:visible="visible"  :header="false" :closeBtn="false" :footer="false" width="300" :closeOnEscKeydown="false" :closeOnOverlayClick="false">
      <template #body>
        <div class="f-c-c">
          <div>
            <img src="@/assets/svg/annulus_suress.svg" alt="">
          </div>
          <div class="submit-suc">{{ joinType === 1 ? t('contacts.joinSuc') : t('account.submit') }}</div>
          <div class="wait">{{ joinType === 1 ? `${t('contacts.joinSucTip')}"${linkInfo?.team}"` : t('account.waitVerify') }}</div>
          <div>
            <t-button
              v-loading="loginLoading"
              class="open"
              :disabled="loginBtnDisabled"
              block
              theme="primary"
              variant="base"
              @click="openLink"
            >{{ t('account.openLink') }}</t-button>
          </div>
        </div>
      </template>
    </t-dialog> -->

  <tip
    v-model:visible="tipVisible"
    :tip="checkPhoneTip"
    :btn-confirm="t('zx.other.confirmChange')"
    :btn-cancel="t('account.cancel')"
    @onconfirm="changeRegion"
  />
</template>

<script setup lang="ts">
import { Field as VanField, Icon as VanIcon, Button as VanButton } from 'vant';
import { getBaseUrl } from '@/api/requestApi';
import home from '../homeIndex.vue';
import tip from '../account/tipCommon.vue';
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { err_reason } from './util';
import { useI18n } from 'vue-i18n';
import { setAccesstoken, setSMDeviceId } from '@/utils/auth';
import {
  joinOrg,
  getSms,
  smsAgree,
  checkAccount,
  loginAccount,
  getJwt,
  registerAccount,
  getIdentifyCode,
  getLinkDetail,
  uuidAgree,
} from '@/api/account/login';
// import areaCode from '@/components/keeppx/account/AreaCode.vue';
import telCode from '@/components/keeppx/account/TelCode.vue';
import fieldCode from '@/components/keeppx/account/FieldCode.vue';
import { useAccountStore } from '@/stores/account';
import { handleWeixinBridgeReady } from '@/views/square/utils.ts';

import { checkPhoneAndMatch } from './util';
import CallApp from 'callapp-lib';
import { initSM, dealSmDeviceId, getSMCaptcha, getSMCaptchaResult } from '@/utils/shumei';
import {deviceType} from "@/utils/device";
const isInMiniApp = ref(false);
  handleWeixinBridgeReady(() => { 
    isInMiniApp.value = window.__wxjs_environment === 'miniprogram';
  });
const accountStore = useAccountStore();
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const hideTop = ref(false);
const join = ref({
  name: '',
  telCode: '86',
  phone: '',
  code: '',
  from: '',
  teamId: '',
  link: '',
  from_openid: '',
});
const countdown = ref(0);
const timer: any = ref(null);
const inputStatus: any = ref(null);
const loginLoading = ref(false);
const loginBtnDisabled = ref(false);
const visible = ref(false);
const fromSms = ref(false);
const linkInfo: any = ref(accountStore.$state.linkInfo);
const joinType = ref();
const exists = ref(); // 账号是否存在
let smsid: any;
const tipVisible = ref(false);
const checkPhoneTip = ref('');
const showPage = ref(false);
let checkRegion: any = 0;
const codeTip = [10016, 10017, 10018];
let inviteFrom = 'link';

const checkSM = () => {
  if (!SMCaptcha.value) {
    return;
  }
  getSMCaptchaResult(SMCaptcha.value, getCode);
};
let InputTimer:any = null;


const inputEventFn = (type) => {
  // pc不做调整
  if (deviceType(navigator.userAgent).type === 'pc') {
    return;
  }
  if (type === 'focus') {
    hideTop.value = true;
    showBottom.value = false;
    inputStatus.value = 'focus';
  } else if (type === 'blur') {
    hideTop.value = false;
    inputStatus.value = 'blur';
    InputTimer && clearTimeout(InputTimer);
    InputTimer = setTimeout(() => {
      if (inputStatus.value === 'blur') {
        showBottom.value = true;
      }
    }, 100)
  }
}
const SMCaptcha = ref(null);
onMounted(async () => {
  if (['staff'].includes(route.query?.app) || route.query?.sms) {
    initSM();
    // 短信邀请进来的
    fromSms.value = true;
    linkInfo.value = accountStore.$state.smsInfo;
    join.value.name = linkInfo.value.name;
    join.value.phone = linkInfo.value.phone;
    join.value.telCode = linkInfo.value.telCode?.toString();
    smsid = route.query.uuid || route.query.sms;
    showPage.value = true;
  } else if (['member', 'government', 'cbd', 'association'].includes(route.query?.app)) {
    initSM();
    // 数字平台短信邀请进来的
    fromSms.value = true;
    linkInfo.value = accountStore.$state.smsInfo;
    join.value.name = linkInfo.value.name;
    join.value.phone = linkInfo.value.telephone;
    join.value.telCode = linkInfo.value.telcode?.toString();
    smsid = route.query.uuid;
    showPage.value = true;
  } else {
    const myInfo: any = accountStore.$state.userInfo;
    join.value.name = myInfo?.title;
    join.value.phone = myInfo?.account_mobile || '';
    join.value.telCode = myInfo?.account_mobile_region.toString() || '';
    inviteFrom = accountStore.qrcode === 1 ? 'qrcode' : 'link';
    linkDetail();
  }
  try {
    SMCaptcha.value = await getSMCaptcha({ width: 300 });
    console.error(SMCaptcha.value);
  } catch (error) {
    console.error(error);
  }
});
const linkDetail = () => {
  const params = {
    link: accountStore.linkInfo.link || '',
    openid: accountStore?.linkInfo?.openid || route?.query?.openid || '',
  };
  let routerName = '';
  let query = {};
  getLinkDetail(params)
    .then((res) => {
      showPage.value = true;
    })
    .catch((err) => {
      console.log(err, 'getLinkDetail err');
      // 待审核：10016，已加入：10017，已拒绝：10018
      const code = err.response.data.code;
      if (codeTip.includes(code)) {
        routerName = 'accountTip';
        query = { code, isSms: route.query.isSms };
      }
      if (code === 10015 || code === -1) {
        routerName = 'accountExpired';
      }
      routerName && router.push({ name: routerName, query });
    });
};
const changeRegion = () => {
  tipVisible.value = false;
  join.value.telCode = checkRegion.toString();
  console.log(join.value.telCode, 'telCode');
  join.value.code ? joinTeam() : getCode();
};
const checkPhone = () => {
  console.log(join.value, 'aaaaaa');
  checkRegion = checkPhoneAndMatch(+join.value.telCode, join.value.phone);
  if (!checkRegion) {
    MessagePlugin.error({
      content: t('zx.account.phoneIllegal'),
      duration: 3000,
    });
    return false;
  }
  if (checkRegion !== +join.value.telCode) {
    (checkPhoneTip.value = `${t('zx.account.checkPhoneRegion1')}“+${checkRegion}”，${t(
      'zx.account.checkPhoneRegion2',
    )}`),
      (tipVisible.value = true);
    return false;
  }
  return true;
};
const getCode = (data?) => {
  // console.log(fromSms.value, 'aaa')
  if (!checkPhone()) return;
  if (fromSms.value) {
    // 检查账号是否存在
    checkAccount({ acc: join.value.phone }).then((res: any) => {
      console.log(res.done, 'checkaccount');
      exists.value = res.done;
      loginOrRegisterCode(res.done ? 'LOGIN' : 'REGISTER', data);
    });
    // loginOrRegisterCode('LOGIN');
  } else {
    joinCode(data);
  }
};

const loginOrRegisterCode = (typ: string, data?) => {
  const params = {
    mobile: { mobile: join.value.phone, region: join.value.telCode },
    typ,
  };
  if (data) {
    params.captcha = {
      code: data?.rid,
      mode: 'slide',
    };
  }
  getIdentifyCode(params)
    .then((res: any) => {
      console.log(res, 'getIdentifyCode');
      interval();
    })
    .catch((err) => {
      const reason = err.response.data.reason;
      MessagePlugin.error({
        content: err_reason[reason] || '获取验证码失败',
        duration: 3000,
      });
    });
};

const joinCode = (data?) => {
  const params = {
    phone: join.value.phone,
    scene: 'joinTeam;6',
    telCode: join.value.telCode,
  };
  if (data) {
    params.rid = data?.rid || '';
  }
  getSms(params)
    .then((res: any) => {
      console.log(res, 'getJoinCode');
      interval();
    })
    .catch((err: any) => {
      MessagePlugin.error({
        content: err.response.data.message,
        duration: 3000,
      });
    });
};

const interval = () => {
  timer.value && (clearInterval(timer.value), (timer.value = null));
  countdown.value = 60;
  timer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer.value);
      timer.value = null;
    }
  }, 1000);
};

const login = () => {
  const ma = {
    mobile: join.value.phone,
    region: join.value.telCode,
    code: join.value.code,
  };
  loginAccount({ ma, app: 'RINGKOL', zone: 'CN', platform: 'H5' })
    .then((res: any) => {
      console.log(res, 'loginAccount');
      // saveJwt(res);
      setAccesstoken(res.jwt);
      agreeSms();
    })
    .catch((err) => {
      const reason = err.response.data.reason;
      MessagePlugin.error({
        content: err_reason[reason] || err.response.data?.message || '登录失败',
        duration: 3000,
      });
    });
};

// const saveJwt = (jwtParams:any) => {
//   getJwt(jwtParams).then((res: any) => {
//     console.log(res, 'getJwt');
//     setAccesstoken(res.jwt);
//     agreeSms();
//   })
// };
const showBottom = ref(true);
// var originalHeight = window.innerHeight; // 初始窗口高度

// window.addEventListener('resize', function () {
//   var currentHeight = window.innerHeight; // 当前窗口高度
//   if (originalHeight > currentHeight) {
//     // 窗口高度减小，键盘可能弹出
//     showBottom.value = false;
//     console.log('键盘已弹出');
//   } else if (originalHeight < currentHeight) {
//     // showBottom.value = true;
//     // 窗口高度增大，键盘可能收起
//     console.log('键盘已收起');
//   }
//   originalHeight = currentHeight; // 更新窗口高度
// });
const register = () => {
  const params = {
    title: join.value.name,
    mobile: {
      region: join.value.telCode,
      mobile: join.value.phone,
      code: join.value.code,
    },
    region: join.value.telCode === '86' ? 'CN' : 'MO',
    app: 'RINGKOL',
    from: 'H5',
    platform: 'H5',
  };
  registerAccount(params)
    .then((res: any) => {
      console.log(res, 'registerAccount');
      setAccesstoken(res.jwt);
      agreeSms();
    })
    .catch((err) => {
      const reason = err.response.data.reason;
      MessagePlugin.error({
        content: err_reason[reason] || err.response.data?.message || '注册失败',
        duration: 3000,
      });
    });
};

const agreeSms = () => {
  if (['member', 'government', 'cbd', 'association'].includes(route.query?.app)) {
    const params = {
      app: route.query.app,
      uuid: route.query.uuid,
    };
    uuidAgree(params)
      .then(() => {
        joinType.value = 1;
        showPage.value = false;
        visible.value = true;
      })
      .catch((err: any) => {
        MessagePlugin.error({
          content: err.response.data.message,
          duration: 3000,
        });
      });
    return;
  }
  smsAgree({ id: smsid })
    .then(() => {
      joinType.value = 1;
      showPage.value = false;
      visible.value = true;
    })
    .catch((err: any) => {
      MessagePlugin.error({
        content: err.response.data.message,
        duration: 3000,
      });
    });
};

const joinTeam = () => {
  if (!checkPhone()) return;
  if (!join.value.code)
    return MessagePlugin.error({
      content: '验证码必填',
      duration: 2000,
    });
  // 短信邀请和非短信邀请走不同的流程
  if (fromSms.value) {
    // 先登录或者注册获取token
    dealSmDeviceId(async (deviceId) => {
      console.log('回调执行成功，设备标识为：' + deviceId);
      setSMDeviceId(deviceId);
      checkAccount({ acc: join.value.phone }).then((res: any) => {
        console.log(res.done, 'checkaccount');
        exists.value = res.done;
        exists.value ? login() : register();
      });
    }).catch((e) => {
      console.error(e);
      MessagePlugin.error('网络繁忙，请稍后刷新重试');
    });
  } else {
    join.value.from = fromSms.value ? 'sms' : inviteFrom;
    join.value.teamId = linkInfo.value.teamId;
    join.value.link = linkInfo.value.link;
    join.value.from_openid = accountStore?.linkInfo.openid;
    joinOrg(join.value)
      .then((res: any) => {
        joinType.value = res.data.type;
        showPage.value = false;
        visible.value = true;
      })
      .catch((err) => {
        MessagePlugin.error({
          content: err.response.data.message,
          duration: 3000,
        });
      });
  }
};

const openLink = () => {
  // visible.value = false;
  // window.open('ringkol://ringkol.com/index/');
  // window.location.href = 'ringkol://ringkol.com/index/';
  const options = {
    scheme: {
      protocol: 'ringkol',
      host: 'ringkol.com',
    },
    appstore: 'ringkol://ringkol.com/index/',
    fallback: '',
  };
  const lib = new CallApp(options);
  try {
    lib.open({
      path: 'webview',
      callback: () => {
        console.log('打开失败');
        // router.push( {name: 'appDownload'} );
        if (navigator.userAgent.indexOf('RingkolApp') > -1) {
          // 通知app关闭h5
          console.log('app内-第一种方式');
          window.RingkolAppChannel.postMessage(JSON.stringify({ method: 'appRoutePop' }));
        } else {
          window.location.href = `${getBaseUrl('website')}/downloadCenter`;
        }
      },
    });
  } catch (error) {
    /**
     * 展示先做这个处理，后续等此需求规划好了以后在进行处理
     * 后续逻辑
     * 1. 如果是另可app就直接关掉
     * 2. 如果不是另可app就跳转到官网, 由官网进行打开等操作
     */
    if (navigator.userAgent.indexOf('RingkolApp') > -1) {
      // 通知app关闭h5
      console.log('app内-第一种方式');
      window.RingkolAppChannel.postMessage(JSON.stringify({ method: 'appRoutePop' }));
    } else {
      window.location.href = `${getBaseUrl('website')}/downloadCenter`;
    }
  }
};

// const test = () => {
//   console.log('aaaa')
// }
</script>

<style lang="less" scoped>
@import url('../css/base.less');

:deep(.tel-code-style) {
  & > div {
    width: 100%;
  }
  .kyy-cell {
    width: 108px !important;
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 8px;
    margin-bottom: 0;
    font-size: 18px;
    .van-field__control {
      height: 66px;
    }
  }
  .van-bottom {
    width: calc(100% - 108px) !important;
    padding-top: 0;
    padding-bottom: 0;
    margin-bottom: 0;
    .van-field__control {
      height: 66px;
    }
  }
  .line {
    height: 24px;
  }
}
:deep(.f-c-c .van-button--primary) {
  min-width: 100px;
  height: 36px;
}
.f-c-c {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 35%;
}
.mb12 {
  margin-bottom: 12px;
}
.mt12 {
  margin-top: 12px;
}
.mt34 {
  margin-top: 34px;
}
.mt60 {
  margin-top: 60px;
}
// @media screen and (max-width:600px) {
.join-container {
  // max-width: 600px;
  // min-height: 100vh;
  // margin: auto;
  // // position: absolute;
  // // top: 0;
  // // bottom: 0;
  // // left: 50%;
  // // transform: translateX(-50%);
  // background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  .logo {
    width: 64px;
    height: 64px;
    font-size: 0;
    margin-top: 24px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .invite {
    margin-top: 28px;
    font-size: 17px;
    // font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    color: #1a2139;
    line-height: 22px;
  }
  .team {
    font-size: 18px;
    // font-family: Microsoft YaHei, Microsoft YaHei-Bold;
    font-weight: 700;
    color: #1a2139;
    line-height: 24px;
    margin-top: 4px;
    margin-bottom: 4px;
  }
  .department {
    font-size: 17px;
    // font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    color: #828da5;
    line-height: 22px;
  }
  .suffix--line {
    width: 1px;
    height: 24px;
    background-color: #f6f6f6;
    margin-right: 16px;
  }
  .verify {
    font-size: 14px;
    // font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
    color: #2069e3;
    line-height: 22px;
  }
  .info {
    width: 320px;
    margin: 32px auto 0;
    .adornment {
      display: flex;
      .area {
        // min-width: 78px;
        width: 80px !important;
        margin-right: -1px;
        display: flex;
        :deep(.t-input) {
          border-bottom-right-radius: 0;
          border-top-right-radius: 0;
        }
      }
      .phone {
        width: calc(100% - 80px) !important;
        :deep(.t-input) {
          border-bottom-left-radius: 0;
          border-top-left-radius: 0;
        }
      }
    }
    :deep(.t-input) {
      height: 40px;
    }
    :deep(.t-input-adornment__prepend) {
      background: #fff;
    }
  }
  .login-btn {
    margin-top: 16px;
    height: 40px;
    font-size: 16px;
    // font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
    color: #ffffff;
    line-height: 24px;
  }
  .info-tip {
    // margin-top: 60px;
    font-size: 14px;
    // font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;
    text-align: left;
    color: #1a2139;
    line-height: 20px;
    position: fixed;
    left: 19px;
    bottom: 24px;
  }
  .info-tips {
    margin-top: 12px;
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: #1a2139;
    line-height: 20px;
  }
}
.submit-suc {
  font-size: 17px;
  // font-family: Microsoft YaHei, Microsoft YaHei-Bold;
  font-weight: 700;
  text-align: center;
  color: #1a2139;
  line-height: 24px;
  margin: 8px 0 4px;
}
.wait {
  font-size: 14px;
  // font-family: Microsoft YaHei, Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: center;
  color: #828da5;
  line-height: 22px;
  margin-bottom: 24px;
}
.open {
  height: 40px;
  width: 112px;
  font-size: 16px;
  // font-family: Microsoft YaHei, Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: center;
  color: #ffffff;
  line-height: 24px;
}
// }
.van-cell {
  padding-left: 0;
  padding-right: 0;
  &:after {
    right: 0;
    left: 0;
  }
}
.f-c {
  display: flex;
  align-items: center;
}
.line {
  width: 1px;
  height: 16px;
  background: #eceff5;
  margin-right: 8px;
}
</style>
<style lang="less">
.verify {
  color: #4d5eff !important;
}
.van-cell__value {
  font-size: 17px;
  color: #1a2139;
}
.van-button__text {
  font-size: 17px !important;
}
</style>
