import { im_syncRequest, client_orgRequest, iam_srvRequest, squareRequest, ringkolRequest, ringkolRequestApi } from "@renderer/utils/apiRequest";
import { im_syncRequest as simple_im_syncRequest } from "@renderer/utils/apiSimpleRequest";
import {
  IGroupMember,
  IGroupUpdate,
  IGroupMemberRoleUpdate,
  IGroupMemberAdd,
  IGroupMemberRemove,
  IRelationPrivate,
  IGroupInvite,
  IGroupInviteApprove,
} from "./model/relation";
import type { IGroupBirthdayPutItem, IGroupToolsPutItem, IGroupWeatherPutItem } from "./model/scene";
import { getCurrentArea } from "@renderer/utils/auth";

/**
 *  
 * @param data cardIds array[string] 身份卡 可选
    showReserveSession boolean 可选 是否获取保留的历史消息
    sortByJoinTime boolean  是否按加入时间排序 可选
    sortByJoinTimeDesc boolean 是否按加入时间降序
 * @returns 
 */
export const getAllChatListApi = (data) =>
  ringkolRequest({
    url: `/im/v1/pair/getAllPairs`,
    method: "post",
    data
  });

export const getPrivateChatApi = (main: string, peer: string) =>
 ringkolRequest({
    url: `/im/v1/pair/getPairByOpenimIds`,
    method: "post",
    data: {
      main,
      peer
    },
    noError: true,
  });

export const getAssistantInfoApi = (main: string, peer: string) =>
  ringkolRequest({
    url: `/im/v1/assistant/getMyAssistant`,
    method: "POST",
    data: {
      main: main,
      peer: peer
    }
  });
export const createPrivateChatApi = (params: Omit<IRelationPrivate, "updated" | "removed">) =>
  ringkolRequestApi({
    url: `/im/v1/pair/createPair`,
    method: "post",
    data: params,
  });
  // 同意添加好友
export function acceptFriend(data:{id:string}) {
  return ringkolRequestApi({
    method: "post",
    url: `/im/v1/friend/apply/acceptFriend`,
    data
  });
}


export function updatePrivateChatApi(data:Omit<IRelationPrivate, "updated" | "removed">) {
  return ringkolRequestApi({
    method: "post",
    url: `/im/v1/pair/updatePair`,
    data
  });
}

export const refreshPrivateChatApi = (main: string, peer: string) =>
  im_syncRequest({
    url: `/im/v1/pair/updatePairActive`,
    method: "post",
    data: {
      main,
      peer
    }
  });

export const removePrivateChatApi = (id, reservedHistory) =>
  client_orgRequest({
    url: `/addressbook/business-relation/agree-apply`,
    method: "post",
    data: { apply_id: id, reservedHistory },
  });

/**
 * 批量获取助手会话
 * @returns ringkolRequest
 */
export const getHelperChatListApi = () =>
  ringkolRequest.get(`/im/v1/assistant/listMyAssistants`, {});
/**
 * 更新助手会话
 * @returns ringkolRequest
 * data:{assistantId:助手ID，stayOn:是否置顶：0-否 1-是, noDisturb:是否免打扰：0-否 1-是 }
 */
export const updateHelperChatListApi = (data) =>
  ringkolRequest.post(`/im/v1/assistant/updateMyAssistant`, data);

/**
 * 批量获取会话列表，加了showReserveSession参数删除关系保留历史
 * @param ids
 * @param index
 * @param size
 * @param showReserveSession true 保留历史聊天记录、
 * @returns
 */
export const getGroupChatListApi = (ids: string[], index = 0, size = 50, showReserveSession = false, types?) =>
  im_syncRequest({
    url: `/v1/groups/search`,
    method: "post",
    data: {
      index,
      size,
      group: { sortByJoinTime: true, sortByJoinTimeDesc: false, card_ids: ids, showReserveSession, types },
    },
  });

// 获取群聊详情
export const getGroupChatApi = (id: string, card_ids: string[] = []) =>
  im_syncRequest({
    url: `/v1/groups/cards`,
    method: "post",
    data: {
      group: id,
      card_ids,
    },
  });

// 本接口，无论是否被移除群聊，数据均返回
export const getGroupCardChatApi = (id: string, card: string) =>
  im_syncRequest.get(`/v1/groups/detail/${encodeURIComponent(id)}/${encodeURIComponent(card)}`);

export const getGroupMemberListApi = (
  data: { member: { group: string; get_platform?: Boolean } },
  index = 0,
  size = 100,
) =>
  im_syncRequest({
    url: `/v1/members/search`,
    method: "post",
    data: {
      index,
      size,
      member: {
        group: data.member.group,
        get_platform: data.member.get_platform,
      },
    },
  });

export const createGroupApi = (params: any) =>
  im_syncRequest({
    url: `/v1/groups`,
    method: "post",
    data: params,
  });

export const refreshGroupChatApi = (id: string) =>
  im_syncRequest({
    url: `/v1/groups/active/${id}`,
    method: "get",
  });

export const updateGroupChatApi = (params: IGroupUpdate) =>
  im_syncRequest({
    url: `/v1/groups`,
    method: "put",
    data: params,
  });

export const updateGroupMemberApi = (params: IGroupMember) =>
  im_syncRequest({
    url: `/v1/members`,
    method: "put",
    data: params,
  });

export const addGroupMemberApi = (params: IGroupMemberAdd) =>
  im_syncRequest({
    url: `/v1/members/batch/add`,
    method: "post",
    data: params,
  });
// 根据群组id和openid查在次群中的信息
export const getGroupMemberApi = (group, openid) =>
  im_syncRequest({
    url: `/v1/members/${group}/${openid}`,
    method: "get",
  });

export const inviteGroupMemberApplyApi = (params: IGroupInvite) => im_syncRequest.post(`/v1/applies`, params);

export const getGroupMemberInviteListApi = (gid: string, adminCards: string[]) =>
  im_syncRequest.post(`/v1/applies/search`, {
    index: 0,
    size: 100,
    apply: { card_ids: adminCards, gid, typ: 0, status: "SEND" },
  });

export const getGroupMemberInviteDetailApi = (id: string) => im_syncRequest.get(`/v1/applies/${id}`);

export const approveGroupInviteApi = (params: IGroupInviteApprove) => im_syncRequest.put(`/v1/applies/status`, params);

export const removeGroupMemberApi = (params: IGroupMemberRemove) =>
  im_syncRequest({
    url: `/v1/members/batch/del`,
    method: "post",
    data: params,
  });

export const updateGroupMemberRoleApi = (params: IGroupMemberRoleUpdate) =>
  im_syncRequest({
    url: `/v1/members/roles`,
    method: "put",
    data: params,
  });

export const updateGroupOwnerRoleApi = (data) =>
  im_syncRequest({
    url: `/v1/groups/transfer`,
    method: "post",
    data,
  });

export const destoryGroupApi = (id: string) =>
  im_syncRequest({
    url: `/v1/groups/${encodeURIComponent(id)}`,
    method: "delete",
  });

export const quitGroupApi = (group: string, card: string, rid?: string, history = true) =>
  im_syncRequest({
    url: `/v1/members/${group}/${encodeURIComponent(card)}/${encodeURIComponent(rid || '#')}/${history}`,
    method: "delete",
  });

export const updateGroupToolsApi = (params: IGroupToolsPutItem) => im_syncRequest.put(`/v1/members/tool`, params);

export const updateGroupWeatherApi = (params: IGroupWeatherPutItem) =>
  im_syncRequest.put(`/v1/members/weather`, params);

export const getGroupWeatherApi = (group: string, openid: string) =>
  im_syncRequest({ url: `v1/group_weather/${encodeURIComponent(group)}/${encodeURIComponent(openid)}`, method: "get" });

export const updateGroupBirthdayApi = (params: IGroupBirthdayPutItem) =>
  im_syncRequest.put(`/v1/members/birthday`, params);

// data.creator 创建人
// data.user 受邀用户必传
// data.quit 退出用户必传
export const makeFilePreviewApi = (data: any) =>
  im_syncRequest({
    url: `/v1/files`,
    method: "post",
    data,
  });

// 获取用户配置(极速模式)
export const getUserConfig = (typ: string) =>
  iam_srvRequest({
    url: `/v1/cfg/${typ}`,
    method: "get",
  });

// 用户配置更新(极速模式)
export const putUserConfig = (data) =>
  iam_srvRequest({
    url: `/v1/cfg`,
    method: "put",
    data,
  });

// 聚合搜索
export const imSearchApi = (params: {
  title: string;
  teams: string[];
  innerCardIds: string[];
  outerCardIds: string[];
  platformCardIds: string[];
}) =>
  ringkolRequest({
    url: "/im/v1/search/globalSearch",
    method: "post",
    data: params,
  });

// v1消息审核 现在用的v2 imReviewApiV2
// code= 0:图片 urls=array[string] 文件列表
export const imReviewApi = (params: { code: number; urls: string[]; from: string; to: string; text: string }) =>
  im_syncRequest({
    url: "/v1/message_review",
    method: "post",
    data: params,
  });
// -------------------------------------------------
//                  音视频接口
// -------------------------------------------------

export const createMeetingApi = (data: MeetingStartInfo) =>
  im_syncRequest({
    url: `/v1/meeting`,
    method: "post",
    data,
  });

export const getMeetingApi = (id: string) =>
  im_syncRequest({
    url: `/v1/meeting/${encodeURIComponent(id)}`,
    method: "get",
  });

export const meetingRecordApi = (room: string, op: "STOP" | "START") =>
  im_syncRequest({
    url: `/v1/meeting/record`,
    method: "post",
    data: {
      room_id: room,
      op,
    },
  });

export const getMeetingRecordResultApi = (recordId: string) =>
  im_syncRequest({
    url: `/v1/meeting/record/${encodeURIComponent(recordId)}`,
    method: "get",
  });

export const endMeetingApi = (id: string) => im_syncRequest.delete(`/v1/meeting/${encodeURIComponent(id)}`);

export const inviteToMeetingApi = (invites: MeetingInvite[]) => im_syncRequest.post(`/v1/meeting_invites`, { invites });

export const getAllMeetingUserApi = (id: string) => im_syncRequest.get(`/v1/meeting_invites/${encodeURIComponent(id)}`);

export const delMeetingInviteApi = (id: string) =>
  im_syncRequest.delete(`/v1/meeting_invites/${encodeURIComponent(id)}`);

// -------------------------------------------------
//                  短语音接口
// -------------------------------------------------
export const getVoiceMsgTextApi = (id: string) => {
  return im_syncRequest({
    url: `/v1/voice_text/${encodeURIComponent(id)}`,
    method: "get",
  });
};

// -------------------------------------------------
//                  审批相关接口
// -------------------------------------------------
type ApproveMsgExtend = {
  approval_id?: number;
  log_id?: number;
  team_id?: string;
  project_id?: number;
};

/**
 * 获取审批消息审批状态
 * @param data 请求参数
 * @returns
 */
export const getApproveStatusApi = (extend: ApproveMsgExtend) =>
  client_orgRequest({
    url: `/approve/notice-status`,
    method: "get",
    params: extend,
    headers: {
      teamId: extend.team_id,
      project_id: extend.project_id,
    },
  });

export const getApproveDetailApi = (extend: ApproveMsgExtend) =>
  client_orgRequest({
    url: `/approve/detail`,
    method: "post",
    data: { approve_id: extend.approval_id },
    headers: {
      teamId: extend.team_id,
      project_id: extend.project_id,
    },
  });

export const agreeApproveApi = (
  extend: ApproveMsgExtend,
  data: {
    handle_value: any;
    free_form: any;
    approve_id: number;
    form_content_modify: boolean;
  },
) =>
  client_orgRequest({
    url: `/approve/agree`,
    method: "post",
    data,
    headers: {
      teamId: extend.team_id,
      project_id: extend.project_id,
    },
  });

export const rejectApproveApi = (
  extend: ApproveMsgExtend,
  data: {
    handle_value: any;
    free_form: any;
    approve_id: number;
    form_content_modify: boolean;
  },
) =>
  client_orgRequest({
    url: `/approve/reject`,
    method: "post",
    data,
    headers: {
      teamId: extend.team_id,
      project_id: extend.project_id,
    },
  });

export const getStaffAppplyApi = (id: string) =>
  client_orgRequest({
    url: `/staffs-apply/${encodeURIComponent(id)}`,
    method: "get",
  });

export const reviewApplyApi = (data: { ids: number | number[]; agree: number }) =>
  client_orgRequest({
    url: `/staffs-apply/review`,
    method: "post",
    data,
  });

// 群文件同步
export const groupFileSync = (data) =>
  im_syncRequest({
    url: `/v1/file_sync`,
    method: "post",
    data,
  });

// 群文件同步撤回
export const groupFileSyncRevoke = (id) =>
  im_syncRequest({
    url: `/v1/file_sync/${encodeURIComponent(id)}`,
    method: "delete",
  });

export const getPeerApplyInfoApi = (id: string) =>
  client_orgRequest({
    url: `/addressbook/contacts/getStatusCollection?applyIds${encodeURIComponent("[]")}=${encodeURIComponent(id)}`,
    method: "get",
  });
export const getFriendAppliesApi = (data) =>
  ringkolRequest({
    url: `/im/v1/friend/apply/batchGetFriendApplies`,
    method: "post",
    data,
  });

export const getBussinessPeerRemoveInfoApi = (id: number) =>
  client_orgRequest({
    url: `/addressbook/business-relation/get-apply-status?applyIds=${encodeURIComponent(id)}`,
    method: "get",
  });

export const rejectRemoveBussinessPeerApi = (id: number) =>
  client_orgRequest({
    url: `/addressbook/business-relation/refuse-apply`,
    method: "post",
    data: { apply_id: id },
  });

// ----------- 广场、另可圈 ----------------
export const getSquareStatusApi = (id: string) => squareRequest.get(`/v1/violation/${encodeURIComponent(id)}/appealed`);

export const applyBoothStatusApi = (boothId: string, status: "WAIT" | "PASS" | "REJECT") =>
  squareRequest.put(`/v1/booth/apply_status`, { status, boothId });

export const getBoothStatusApi = (id: string) => {
  const reqConfig = {
    url: `/v1/booth/apply_status/${encodeURIComponent(id)}`,
    method: "get",
    hideMessage: true,
  } as any;
  return squareRequest.request(reqConfig);
};

// ----------- 商机状态接口 ----------------
export const getNicheStatusApi = (extend: { uuid: string; team_id: string }) =>
  client_orgRequest.get(`/niche/business/notice/${encodeURIComponent(extend?.uuid)}`, {
    headers: {
      teamId: extend?.team_id,
    },
  });

// ----------- 引导视频状态获取 ----------------
export const getGuideStatusApi = (typ: "SceneSocial" | "FamilyGroup") => im_syncRequest.get(`/v1/guid/${typ}`);

export const finishGuideStatusApi = (id = "") => im_syncRequest.get(`/v1/guid/finish/${id}`);

// ----------- 群公告 ----------------
export const getGroupNoticeListApi = (data: { group_id: string; pin?: boolean; card_id: string }) =>
  im_syncRequest({
    url: `/v1/bulletin/search`,
    method: "post",
    data,
  });
export const addGroupNoticeApi = (data: any) =>
  im_syncRequest({
    url: `/v1/bulletin`,
    method: "post",
    data,
  });
export const deleteGroupNoticeApi = (id: string) =>
  im_syncRequest({
    url: `/v1/bulletin/${id}`,
    method: "delete",
  });
export const closeGroupNoticeApi = (data: any) =>
  im_syncRequest({
    url: `/v1/bulletin`,
    method: "put",
    data,
  });

// ----------- 祝福语列表 ----------------
export const getBlessingWordsApi = () =>
  im_syncRequest.post(
    `v1/blessing_words/search`,
    { page: 0, size: 100 },
    {
      headers: {
        zone: getCurrentArea(),
      },
    },
  );

// -----------平台群身份-------------
export const createGroupLabel = (params: any) =>
  im_syncRequest({
    url: `/v1/group_label`,
    method: "post",
    data: params,
  });

export const putGroupLabel = (params: any) =>
  im_syncRequest({
    url: `/v1/group_label`,
    method: "put",
    data: params,
  });

export const deleteGroupLabel = (id: string) =>
  im_syncRequest({
    url: `/v1/group_label/${id}`,
    method: "delete",
  });

export const listGroupLabel = (params: { group_id: string }) =>
  im_syncRequest({
    url: `/v1/group_label/search`,
    method: "post",
    data: params,
  });

export const getGovernmentStaffsPlatform = (teamId) =>
  client_orgRequest({
    url: `/government-group/get-government-staffs-platform`,
    method: "get",
    headers: {
      teamId,
    },
  });

// export const getRemoteMessage = (params) =>
//   im_syncRequest({
//     url: `/v1/history_message`,
//     method: "post",
//     data: params,
//   });
export const syncMessage = (params) =>
  simple_im_syncRequest({
    url: `v1/sync_message`,
    method: "post",
    data: params,
  });
export const syncMessageStatus = (params) =>
  simple_im_syncRequest({
    url: `v1/sync_message_status`,
    method: "post",
    data: params,
  });
export const imGroupExit = (params) =>
  client_orgRequest({
    url: `/im-group-exit`,
    method: "post",
    data: params,
    headers: {
      teamId: params?.team_id,
    },
  });
// 部门群的解散在客户端也直接调用业务方提供的解散接口
export const disbandGroup = (params:{im_group:string,team_id?:string}) =>
  client_orgRequest({
    url: `/department/group/dissolve`,
    method: "post",
    data: params,
    headers: {
      teamId: params.team_id,
    },
  });
