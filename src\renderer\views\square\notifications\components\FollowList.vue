<template>
  <PersonList :data="dataList" :show-empty="false">
    <template #before-item="{ index }">
      <CategoryTitle :count="count" :index="index" />
    </template>
    <template #name="{ item }">
      <div class="name-wrap">
        <div class="name line-1" v-html="item._name" />
        <div class="time">{{ item.followedTime }}</div>
      </div>
    </template>
    <template #desc>
      <div class="desc">{{ $t('square.square.followedYou') }}</div>
    </template>
  </PersonList>

  <InfiniteLoading :class="{ 'empty-wrap': !dataList.length }" @infinite="loadMore">
    <template #complete>
      <div v-if="!dataList.length" class="is-empty">
        <Empty :tip="$t('square.square.noFollowedTip')" />
      </div>
    </template>
  </InfiniteLoading>
</template>

<script setup lang="ts">
import Empty from '@/components/common/Empty.vue';
import InfiniteLoading from '@/components/InfiniteLoading/index.vue';
import { getNewsFollowsList } from '@/api/square/news';
import PersonList from '@/views/square/components/PersonList.vue';
import useInfiniteLoad from '@/hooks/infiniteLoad';
import { timeAgo } from '@/views/square/utils/time';
import CategoryTitle from '@/views/square/notifications/components/CategoryTitle.vue';
import { useSquareStore } from '@/views/square/store/square';

const store = useSquareStore();
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
});

const count = store.newsStats.follows;

// 创建包装函数来传递teamId
const getListWithTeamId = (params: any) => getNewsFollowsList(params, store.teamId || '');

const { dataList, loadMore } = useInfiniteLoad(getListWithTeamId, {
  dataList: props.data,
  dataHandler: (v) => ({
    ...v.square,
    followedTime: timeAgo(v.square.followedAt),
  }),
});
</script>

<style lang="less" scoped>
@import "list";

.name-wrap {
  display: flex;
}
.name {
  color: var(--text-kyy-color-text-1, #1A2139);
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  margin-right: 20px;
  max-width: 168px;
}
.time, .desc {
  font-size: 14px;
  color: var(--text-kyy-color-text-3, #828DA5);
}
</style>
