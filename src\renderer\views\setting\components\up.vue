<template>
  <div class="container">
    <div class="f-c">
      <img class="logo" src="@renderer/assets/svg/uplogo2.svg" draggable="false" alt="" :style="{ width: '88px', height: '88px',marginRight:'4px'}">
      <!-- <t-image :src="logoImage" fit="contain" :style="{ width: '88px', height: '88px', background:'#fff' }" /> -->
      <div class="f-c-align" style="height: 80px;margin: 0 12px;">
        <div class="f-c">
          <span style="margin-right: 12px;flex:none;" @click="handleVersionClick">{{ `${t('zx.setting.curVersion')}：${showVersion}` }}</span>
          <span v-if="checkApi" class="f-c" style="color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4D5EFF);"><img class="icon" src="@renderer/assets/svg/icon_loading.svg" alt=""> {{ t('zx.setting.checkingUp') }}</span>
          <span v-else-if="!checkApi && showNewVersion && appStore.updatePack && appStore.updatePack.show && appStore.updatePack.status === 'ing'" class="f-c" style="color: var(--success-kyy-color-success-active, #499D60);">
            {{ `${t('zx.setting.installIng')}` }}
          </span>
          <span v-else-if="!checkApi && showNewVersion" class="f-c" style="flex:none;color: var(--success-kyy-color-success-active, #499D60);"><img class="icon" src="@renderer/assets/svg/icon_selt.svg" alt=""> {{ `${t('zx.setting.hasNewVersion')}${newVersion}${newPackConfig.buildNumber ? `-${newPackConfig.buildNumber}` : ''}` }}</span>
          <span v-else-if="!checkApi && !showNewVersion" class="f-c" style="color: var(--success-kyy-color-success-active, #499D60);"> {{ `${t('zx.setting.latestVersion')}` }}</span>
        </div>
        <div>sdk版本 ：{{ getVersion()?.sdkVersion }}</div>
        <div class="f-c" style="margin-top: 12px;">
          <t-select
            v-if="showEnvSelect"
            v-model="currentEnv"
            :options="envOptions"
            style="margin-right: 12px; width: 120px;"
            @change="handleEnvChange"
          />
          <t-button v-if="showEnvSelect" @click="openMonitorWindow" style="margin-right: 12px;" >打开应用监控</t-button>
          <t-button v-if="showNewVersion && appStore.updatePack && appStore.updatePack.show && appStore.updatePack.status === 'ing'" @click="handelUpdate('ing')">
            {{t('zx.setting.backInstall')}} {{appStore.updatePack.percent}}%
          </t-button>
          <t-button v-else-if="showNewVersion" @click="checkUp">{{ t('zx.setting.updateNow') }}</t-button>
          <t-button v-else @click="checkUp">{{ t('zx.setting.checkUpdate') }}</t-button>
          <t-button v-if="showEnvSelect" @click="cleanCache" style="margin-left: 12px;" >清除数据</t-button>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { setAutoUp, getAutoUP, checkNewVersion, getUpVersion, configInfo } from '../util';
import { MessagePlugin } from 'tdesign-vue-next';
import {compareVersionAndNumber, getVersion} from "@/utils/auth";
import { useAppStore } from '@renderer/store/modules/appStore';
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, shell } = LynkerSDK;
const { t } = useI18n();
const appStore = useAppStore();
let _configInfo:any = '';
try {
  _configInfo = `${configInfo.version}-${configInfo.buildNumber}`;
} catch (e) {
  console.log('configInfo error', e);
}
const showVersion = ref(_configInfo);
const version = ref(configInfo.version);
let newPackConfig:any = {};
// const _env = ref(configInfo.env);
const newVersion = ref(getUpVersion());
const autoUp = ref(getAutoUP());
const checkApi = ref(false);
import logoImage from '@renderer/assets/svg/uplogo.svg';
import { LynkerSDKConfig } from '@renderer/_jssdk/packages/jssdk/src/common';
// import { backgroundClip } from 'html2canvas/dist/types/css/property-descriptors/background-clip';
let upLink = '';
// const showNewVersion = computed(() => compareVersion(newVersion.value, version.value));
const showNewVersion = ref(false);
const emits = defineEmits(['closeUp']);
const currentEnv = ref(LynkerSDK.config.env || 'PROD');
const envOptions = [
  { label: '生产环境', value: 'PROD' },
  { label: '预发布环境', value: 'PRE' },
  { label: '测试环境', value: 'TEST' },
  { label: '开发环境', value: 'DEV' },
  { label: '恢复默认', value: 'RESET' }
];

const showEnvSelect = ref(false);
const clickCount = ref(0);
let clickTimer: NodeJS.Timeout | null = null;

const openMonitorWindow = () => {
  ipcRenderer.invoke('open-monitor-window');
};
const handleVersionClick = () => {
  if (clickTimer) {
    clearTimeout(clickTimer);
  }

  clickCount.value++;

  if (clickCount.value >= 6) {
    showEnvSelect.value = true;
    clickCount.value = 0;
  }

  clickTimer = setTimeout(() => {
    clickCount.value = 0;
  }, 2000); // 2秒内没有继续点击则重置计数
};

const handleEnvChange = (value: string) => {
  // 这里可以添加切换环境后的处理逻辑
  console.log('环境已切换至:', value);
  // 如果需要保存环境设置，可以在这里调用相关API
  LynkerSDK.setEnv(value as LynkerSDKConfig['env']);
};

const checkUp = () => {
  checkApi.value = true;
  checkNewVersion().then(res => {
    console.log(res, 'checkNewVersion');
    const info = res?.data?.data;
    checkApi.value = false;
    if (res.status == 200) {
      newVersion.value = info.external_version || '';
      upLink = res.data?.data?.download || '';

      if (info?.package_info_two) {
        const config = info.package_info_two.find(v => v && v.type === 'config');
        if (config) {
          newPackConfig = config
        };
      }
      const newVersionInfo = {
        version: newVersion.value,
        number: newPackConfig.buildNumber
      }
      const versionInfo = {
        version: configInfo.version,
        number: configInfo.buildNumber,
      }
      showNewVersion.value = compareVersionAndNumber(newVersionInfo, versionInfo);

      if (showNewVersion.value) {
        // 有更新就弹窗提示并关闭窗口
        emits('closeUp');
        ipcRenderer.invoke('check-update', { isHandCheck: true, winType: 'main', data: res.data?.data});
      } else {
        MessagePlugin.success({
          content: t('zx.setting.latestVersion'),
          duration: 3000,
        })
      }
    }
  }).catch(e => {
    checkApi.value = false;
  })
};
const handelUpdate = (status) => {
  ipcRenderer.invoke('pack-back-install', { isBack: false });
}
const updateIm = () => {
  checkNewVersion().then(res => {
    if (res.status == 200) {
      shell.openExternal(res.data?.data?.download);
    }
  })
  // ipcRenderer.invoke('check-update', 'setting');
};
const changeAutoUp = (v) => {
  setAutoUp(v);
};
const checkVersionInfo = () => {
  console.log('====>configInfo', configInfo);
  checkApi.value = true;
  checkNewVersion().then(res => {
    console.log('res', res)
    if (res.status == 200) {
      const info = res?.data?.data;
      console.log('info', info)
      newVersion.value = info.external_version || '';
      if (info?.package_info_two) {
        const config = info.package_info_two.find(v => v && v.type === 'config');
        if (config) {
          newPackConfig = config
        };
      }
      const newVersionInfo = {
        version: newVersion.value,
        number: newPackConfig.buildNumber,
      }
      const versionInfo = {
        version: configInfo.version,
        number: configInfo.buildNumber
      }
      console.log(newVersionInfo, versionInfo)
      showNewVersion.value = compareVersionAndNumber(newVersionInfo, versionInfo);
    }
    checkApi.value = false;
  }).catch(e => {
    checkApi.value = false;
  });
}
const cleanCache = async() => {
  // 1. 清除 Web 存储（localStorage/sessionStorage）
  localStorage.clear();
  window.sessionStorage.clear();
  // 2. 清除 IndexedDB
  const databases = await indexedDB.databases();
  databases.forEach(db => indexedDB.deleteDatabase(db.name));
  // 3. 清除本地db
  ipcRenderer.invoke('im.db.delete')
  ipcRenderer.invoke('quit-login');
}
onMounted(() => {
  checkVersionInfo();
});
</script>

<style lang="less" scoped>
.f {
  display: flex;
}
.f-c {
  display: flex;
  align-items: center;
}
.f-c-align {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.container {
  color: var(--checkbox-kyy-color-checkbox-text-default, #1A2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  .logo {
    width:88px;
    height: 88px;
  }
  .icon {
    width:16px;
    height: 16px;
    margin-right: 4px;
  }
}
</style>
