<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>另可</title>
  <!-- <script>
      window._smReadyFuncs = [];
      window.SMSdk = {
        onBoxDataReady(boxData) { // 非必填
          console.log('此时拿到的数据为boxData或者boxId', boxData);
        },
        ready(fn) {
          fn && _smReadyFuncs.push(fn);
        }
      };
      window.SMSdkError = false;

      // 1. 通用配置项
      window._smConf = {
        organization: 'OioUFNPPerhbBNzpgJoi', // 必填，组织标识，邮件中organization项
        appId: 'default', // 必填，应用标识，默认传值default，其他应用标识提前联系数美协助定义
        publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCbkXhIXYNLFtBl91+pKZIaiElM0dxi3LZWsal13AtvwLGD2HI3maECRIHQvk8ehaRgbnq3KRDTGKbU2Nuw8jsQDTsb/bHu16XEYJX0MVxQo0R+rLRlWAwKT6laC1K4XbznFlYGTHghBuU+SK2MOrvMQb7HoYsRfTIgsHej8C6grwIDAQAB', // 必填，私钥标识，邮件中publicKey项
        staticHost: 'static.portal101.cn', // 必填, 设置JS-SDK文件域名，建议填写static.portal101.cn
        protocol: 'https', // 如果使用https，则设置，如不使用，则不设置这个字段
      };
    </script>
    <script src="./outcdn/shumei.js"></script> -->
  <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-6.18.1.min.js"></script>
  <!-- Web Component 模式  -->
  <!-- <script src="https://lf1-cdn-tos.bytegoofy.com/obj/iconpark/icons_27918_329.e852e098ce97fb1a285529cbd09bf14e.js"></script> -->
  <!-- <script src="https://lf1-cdn-tos.bytegoofy.com/obj/iconpark/icons_27918_541.77d8f3a427db31b00c93e2de34952e29.js"></script> -->
  <script src="https://lf1-cdn-tos.bytegoofy.com/obj/iconpark/icons_27918_617.20e477e502f0b568c8c8603a28297eb7.js"></script>
  <script src="https://res.hc-cdn.com/web-sdk-cdn/1.0.24/websdk.min.js"></script>
  <script src="https://castatic.fengkongcloud.cn/pr/v1.0.4/smcp.min.js"></script>
  <!-- <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    /> -->
  <style>
    *, *::before, *::after {
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }
    html, body {
      /* Firefox */
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    html::-webkit-scrollbar,
    body::-webkit-scrollbar,
    *::-webkit-scrollbar {
      /* Chrome, Safari, Edge */
      display: none;
      width: 0 !important;
      height: 0 !important;
    }

    /* 确保所有元素都隐藏滚动条 */
    * {
      /* Firefox */
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    *::-webkit-scrollbar {
      /* Chrome, Safari, Edge */
      display: none;
      width: 0 !important;
      height: 0 !important;
    }
    #__loading__ {
      position: fixed;
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 8px;
      justify-content: center;
      align-items: center;
      /* background-color: rgba(255, 255, 255, .7); */
      background-color: #fff;
      overflow: hidden;
    }

    .loader {
      width: 28px;
      padding: 8px;
      aspect-ratio: 1;
      border-radius: 50%;
      background: #4C5EFF;
      --_m:
        conic-gradient(#0000 10%, #000),
        linear-gradient(#000 0 0) content-box;
      -webkit-mask: var(--_m);
      mask: var(--_m);
      -webkit-mask-composite: source-out;
      mask-composite: subtract;
      animation: l3 1s infinite linear;
    }

    @keyframes l3 {
      to {
        transform: rotate(1turn)
      }
    }

    .loader-text {
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      text-align: left;
      color: #333;
    }

    img {
      -webkit-user-drag: none;
    }
  </style>
  <script>
    const hideLoading = () => {
      const style = document.createElement('style');
      style.textContent = '#__loading__ { display: none !important; }';
      document.head.appendChild(style);
    }
    const hideLoadingPaths = ['/setting/setGroup'];
    if (window.__ELECTRON_WINDOW_MANAGER_TYPE__ === 'BW') {
      hideLoading()
    }
  </script>
</head>

<body>

  <!--禁止拖动图片-->
  <div id="app">
    <div id="__loading__">
      <span class="loader"></span>
      <div class="loader-text">加载中...</div>
    </div>
  </div>
  <script type="module" src="/main.ts"></script>
</body>

</html>
