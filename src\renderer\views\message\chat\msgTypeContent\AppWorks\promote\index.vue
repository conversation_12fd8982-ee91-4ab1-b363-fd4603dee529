<template>
  <!-- 使用 Apply 组件作为默认实现 -->
  <Apply
    v-if="sceneData?.scene === 20053"
    :msg="msg"
  />
  <Cancel
    v-if="sceneData?.scene === 20055"
    :msg="msg"
  />
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import Apply from './components/Apply.vue';
import Cancel from './components/Cancel.vue';
import { useSceneData } from '../../components/activity-promote/composables/useSceneData';

const props = defineProps({
  msg: { type: Object as PropType<any>, default: null },
});

const { sceneData } = useSceneData(props);

</script>

<style scoped>
/* 样式已移至子组件中 */
</style>
