import { Flag } from '@renderer/api/forum/models/post';

export const flagMap = {
  [Flag.Staff]: '员工',
  [Flag.StaffPlatform]: '平台',
  [Flag.Visitor]: '访客',
  [Flag.Anonymous]: '匿名',
};

export enum FriendStatus {
  FRIEND_STATUS_UNKNOWN = 'FRIEND_STATUS_UNKNOWN',
  FRIEND_STATUS_SELF = 'FRIEND_STATUS_SELF',
  FRIEND_STATUS_NO_ADDED = 'FRIEND_STATUS_NO_ADDED',
  FRIEND_STATUS_PENDING = 'FRIEND_STATUS_PENDING',
  FRIEND_STATUS_ADDED = 'FRIEND_STATUS_ADDED',
}

export enum PageType {
  Home = 'Home',
  Forum = 'Forum',
  Topic = 'Topic',
}
export const PageTypeMap: Record<PageType, string> = {
  [PageType.Home]: PageType.Home,
  [PageType.Forum]: PageType.Forum,
  [PageType.Topic]: PageType.Topic,
};

export const BtnStatusMap = {
  [FriendStatus.FRIEND_STATUS_NO_ADDED]: {
    show: true,
    text: '添加好友',
    disabled: false,
  },
  [FriendStatus.FRIEND_STATUS_PENDING]: {
    show: true,
    text: '待通过',
    disabled: true,
  },
}

export const DEFAULT_COVER = 'https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/1728454691170457412904.png';

// 跳过匿名设置
export const SKIP_ANON_SETUP = 'skip_anon_setup';
