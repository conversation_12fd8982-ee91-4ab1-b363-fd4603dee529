import { useRouter } from 'vue-router';
import { computed } from 'vue';
import { RouteItem } from '@/views/square/types';
import { useSquareStore } from '@/views/square/store/square';

export default function useRouterHelper(parentName) {
  const router = useRouter();
  const store = useSquareStore();

  // 获取一级路由下的子路由数据
  const getSubRoutes = (parentName: string): RouteItem[] => {
    const topRoute = router.options.routes.find((v) => v.name === parentName);
    return topRoute?.children.map((v) => ({
      name: v.name,
      path: v.path,
      icon: v.meta?.icon as string,
      title: v.meta?.title as string,
      fullPath: `${topRoute.path}/${v.path}`,
      parentPath: v.meta?.parentPath,
      affix: Boolean(v.meta?.affix),
      hidden: Boolean(v.meta?.hidden),
      keepAlive: Bo<PERSON>an(v.meta?.keepAlive),
      role: v.meta?.role as string,
      sort: (v.meta?.sort || 9999) as number,
      tabName: v.meta?.tabName ? v.meta?.tabName : '',
    }));
  };

  const routeList = Array.isArray(parentName) ? parentName.reduce((acc, curr) => {
    acc.push(...getSubRoutes(curr));
    return acc;
  }, []) : getSubRoutes(parentName);

  const roleFilter = (v) => {
    if (!v.role) return true;
    return store.isPersonal ? v.role === 'personal' : v.role === 'office';
  };

  // 左侧菜单栏
  const menuList = computed<RouteItem[]>(() => routeList
    .filter((v) => !v.hidden)
    .filter(roleFilter)
    .sort(((a, b) => a.sort - b.sort)));

  return {
    routeList,
    menuList,
    roleFilter,
  };
}
