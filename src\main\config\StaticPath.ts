// 这里定义了静态文件路径的位置
import { join } from "path";
import config from "@config/index";
import { app } from "electron";
import { URL } from "url";

const isDev = app.isPackaged === false;
function getAppTrayPath(path: string) {
  return isDev
    ? join(__dirname, "..", "..", "..", "src", "renderer", "assets", "account", path).replace(/\\/g, "\\\\")
    : join(__dirname, "..", "..", "..", "..", path).replace(/\\/g, "\\\\");
}

class StaticPath {
  constructor() {
    const basePath = isDev ? join(__dirname, "..", "..", "..") : join(app.getAppPath(), "..", "..");
    this.__updateFolder = join(basePath, `${config.HotUpdateFolder}`);
    this.__static = join(__dirname, "..", "renderer");
    this.__lib = basePath;
    this.__common = basePath;
  }

  /**
   * 静态文件路径 渲染进程目录下
   *
   * @type {string}
   * @memberof StaticPath
   */
  __static: string;

  /**
   * dll文件夹及其他os平台相关的文件路径
   *
   * @type {string}
   * @memberof StaticPath
   */
  __lib: string;

  /**
   * 与os无关的资源
   *
   * @type {string}
   * @memberof StaticPath
   */
  __common: string;

  /**
   * 增量更新文件夹
   *
   * @type {string}
   * @memberof StaticPath
   */
  __updateFolder: string;
}
const staticPath = new StaticPath();
/**
 * 获取真正的地址
 *
 * @param {string} devPath 开发环境路径
 * @param {string} proPath 生产环境路径
 * @param {string} [hash=""] hash值
 * @param {string} [search=""] search值
 * @return {*}  {string} 地址
 */
export function getUrl(devPath: string, proPath: string = "", hash = "", search = ""): string {
  console.log('devPath', proPath);
  // const url = isDev ? new URL(`http://localhost:${process.env.PORT}`) : new URL("file://");
  // url.pathname = isDev ? devPath : proPath;
  const url = isDev ? new URL(`http://localhost:${process.env.PORT}`) : new URL(`app://-${devPath}`);
  // const url = isDev ? new URL(`http://localhost:${process.env.PORT}`) : new URL(`http://ringkol-desktop.lynker.cn/`);
  url.pathname = !devPath ? "/index.html" : `${devPath}`;
  url.hash = hash;
  url.search = search;
  return url.href;
}
export const winURL = getUrl("", join(__dirname, "..", "renderer", "index.html"));
export const loadingURL = getUrl("/windows/loading/index.html", join(__dirname, "..", "renderer/windows/loading", "index.html"));
export const errorURL = getUrl("/windows/error/index.html", join(__dirname, "..", "renderer/windows/error", "index.html"));
export const preloadHtmlURL = getUrl("/__proload_assets__.html", join(__dirname, "..", "renderer/__proload_assets__.html"));
// export const loadingURL = getUrl(
//   "/loader.html",
//   // `${staticPath.__static}/loader.html`,
//   join(__dirname, "..", "renderer", "loader.html")
// );

/**
 * 获取以 /src/renderer/index.html 为入口的功能模块路径
 * @param filePath 入口文件路径，！！！不能以 / 开头
 * @param hashRoute 功能模块的路由
 * @param query  string query参数，需要自行拼接
 * @returns
 */
export const getRendererMainEntry = (filePath: string, hash = "", query = "") => {
  return getUrl(`/${filePath}`, join(__dirname, `../renderer/${filePath}`), hash, query);
};

/**
 * 获取 renderer/windows下的功能模块路径
 * @param filePath
 * @param hashRoute
 * @returns
 */
export const getRendererWindowEntry = (filePath: string, hash = "", query = "") => {
  return getUrl(`/${filePath}`, join(__dirname, `../renderer/${filePath}`), hash, query);
};

export const printURL = getUrl("", join(__dirname, "..", "renderer", "index.html"), "#/Print");
export const preloadPath = isDev
  ? join(app.getAppPath(), "..", "preload.js")
  : join(app.getAppPath(), "dist", "electron", "preload.js");
export const lib = getAppTrayPath("");

export const common = staticPath.__common;
export const updateFolder = staticPath.__updateFolder;
export const staticPaths = getUrl("", staticPath.__static);

// process.env 修改
for (const key in staticPath) {
  process.env[key] = staticPath[key];
}
