<script setup lang='ts'>
import { ref, computed } from 'vue';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { getTeamLabelSettingAxios, saveLableTeamSettingAxios } from '@renderer/api/digital-platform/api/businessApi';
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { to } from 'await-to-js';
import { useI18n } from 'vue-i18n';
import tagOrganizeDrawer from '@renderer/views/digital-platform/components/tagOrganizeDrawer.vue';
const tagOrganizeDrawerRef = ref(null);
let leabFormData = ref({
  personal: {
    enable: 0,
    setting_id: '',
  },
  team: {
    enable: 0,
    setting_id: '',
  },
});
const { t } = useI18n();
const props = defineProps({
  activationGroupItem: {
    type: Object,
    default: () => { },
  },
});

const currentTeamId = computed(() => {
  return props.activationGroupItem?.teamId||window.localStorage.getItem('workBenchTeamid')
});


const onGetSetting = async () => {
  return new Promise(async (resolve, reject) => {

    const [err, res] = await to(getTeamLabelSettingAxios({}, currentTeamId.value));
    if (err) {
      reject(err);
      return;
    }
    const {data} = res?.data;
    console.log(data);
    resolve(data);

  });
};

const onInitData = async () => {
  if(currentTeamId.value){
    const [err, res] = await to(onGetSetting());
    if (err) {
      MessagePlugin.error({
        content: err.message,
        duration: 1000,
      });
      return;
    }
    if (res) {
      leabFormData.value = res;
    }
  }
};
const openGrtag = (val) => {
  tagOrganizeDrawerRef.value.openWin(val, currentTeamId.value);
};

const changTagFlagFn = async(enable, settingId, flag?) => {
  // saveLableTeamSettingAxios(
  //   'association',
  //   {
  //     enable,
  //   },
  //   settingId,
  //   currentTeamId.value,
  // )
  //   .then((res) => {
  //     MessagePlugin.success(t('member.save') + t('member.success'));
  //     onGetMemberSetting(true);
  //   })
  //   .catch((error) => {
  //     const errMsg = error instanceof Error ? error.message : error;
  //     MessagePlugin.error(errMsg);
  //     onGetMemberSetting();
  //   });

  const [err, res] = await to(saveLableTeamSettingAxios({
    id: settingId,
    enable
  } , currentTeamId.value));
  if (err) {
    MessagePlugin.error(err.message);
    return;
  }
  if (res) {
    MessagePlugin.success( enable ? '开启成功' : '关闭成功');
    onInitData();
  }
};

const onChangeGeTag = (e) => {
  if (!e) {
    const myDialog = DialogPlugin({
      header: '确定关闭个人标签?',
      theme: 'info',
      body: '关闭后，组织成员已设置的个人标签将不再显示',
      className: 'dialog-classp32',
      confirmBtn: t('ebook.mset3'),
      cancelBtn: t('ebook.mset4'),
      closeOnOverlayClick: false,
      onConfirm: () => {
        myDialog.hide();
        changTagFlagFn(0, leabFormData.value.personal?.setting_id, '个人');
      },
      onCancel: () => {
        leabFormData.value.personal.enable = 1;
        myDialog.hide();
      },
    });
  } else {
    changTagFlagFn(1, leabFormData.value.personal?.setting_id, '个人');
  }
};


const onChangeOrganizeTag = (e) => {
  if (!e) {
    const myDialog = DialogPlugin({
      header: '确定关闭组织标签?',
      theme: 'info',
      body: '关闭后，组织成员已设置的组织标签将不再显示',
      className: 'dialog-classp32',
      confirmBtn: t('ebook.mset3'),
      cancelBtn: t('ebook.mset4'),
      closeOnOverlayClick: false,
      onConfirm: () => {
        myDialog.hide();
        changTagFlagFn(0, leabFormData.value.team?.setting_id);
      },
      onCancel: () => {
        leabFormData.value.team.enable = 1;
        myDialog.hide();
      },
    });
  } else {
    changTagFlagFn(1, leabFormData.value.team?.setting_id);
  }
}



onMountedOrActivated(() => {
  onInitData();
});



</script>

<template>
  <div class="outer">
    <div class="container">
      <div class="header">
        <h1 class="header-title">标签设置</h1>
      </div>
      <div class="setting">
        <div class="setting-form" style="gap: 0">
          <div class="setting-form-item !flex-items-start" style="margin-bottom: 16px">
            <!-- 标签设置 -->
            <span class="title pt-5"> 组织标签</span>
            <span class="value">
              <span class="team pt-5" style="color:#828DA5 ;">
                <t-switch :customValue="[1, 0]" :value="leabFormData.team.enable"
                  @change="onChangeOrganizeTag"></t-switch>开启后，组织成员可选择可选标签中设置的标签
              </span>
            </span>
          </div>
          <div class="setting-form-item !flex-items-start" style="margin-bottom: 24px">
            <img src="@/assets/digital/organize.png" style="width: 280px; height: 144px; margin-right: 12px" />
            <div style="display: flex; flex-direction: column; justify-content: space-between; height: 144px">
              <t-button theme="default" variant="outline" class="w600" style="font-weight: 600;width: fit-content"
                @click="openGrtag(2)">设置组织标签</t-button>
              <div style="color: #828da5; font-size: 14px; font-weight: 400">
                <div>1. 组织标签将在名录卡片和名录详情中显示</div>
                <div>2. 组织成员可从设置好的标签中选择符合自己的平台标签</div>
                <div>3. 例如组织标签为车型，则组织成员可选择：Model Y、Model X ..</div>
              </div>
            </div>
          </div>

          <div class="setting-form-item !flex-items-start" style="margin-bottom: 16px">
            <!-- 标签设置 -->
            <span class="title pt-5"> 个人标签</span>
            <span class="value">
              <span class="team pt-5" style="color:#828DA5 ;">
                <t-switch :customValue="[1, 0]" :value="leabFormData.personal.enable"
                  @change="onChangeGeTag"></t-switch>开启后，组织成员可自定义符合自己的个人标签
              </span>
            </span>
          </div>
          <div class="setting-form-item !flex-items-start">
            <img src="@/assets/digital/greg.png" style="width: 280px; height: 144px; margin-right: 12px" />
            <div style="display: flex; flex-direction: column; justify-content: space-between; height: 144px">
              <t-button theme="default" class="w600" variant="outline" style="width: fit-content;font-weight: 600;"
                @click="openGrtag(1)">设置个人标签</t-button>
              <div style="color:  #828da5; font-size: 14px; font-weight: 400">
                <div>1. 个人标签将在名录卡片和名录详情中显示</div>
                <div>2. 组织成员可自定义符合自己的个人标签</div>
                <div>3. 例如个人标签为车牌号，则组织成员可输入：粤C00001</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <tagOrganizeDrawer ref="tagOrganizeDrawerRef"></tagOrganizeDrawer>
</template>

<style lang='less' scoped>
.groupOwner {
  border-radius: 4px;
  background: var(--kyy_color_tag_bg_gray, #eceff5);
  padding: 2px 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.outer {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: #fff;
  width: 100%;
  border-radius: 8px;
  border: 1px solid #ECEFF5;
}

.vip {
  color: var(--text-kyy-color-text-1, #1a2139);

  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  /* 157.143% */
}

.container {
  border: 1px solid var(--divider-kyy-color-divider-deep, #d5dbe4);
}

.flexBetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header {
  padding: 12px;
  background: var(--kyy_color_table_hrading_bg, #e2e6f5);

  &-title {
    color: var(--kyy_color_table_hrading_text, #516082);
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    /* 157.143% */
  }

  .dissolution {
    color: var(--error-kyy_color_error_default, #d54941);

    /* kyy_fontSize_2/regular */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }
}

:deep(.t-button__text) {
  font-weight: 600 !important;
}

.tdesc {
  color: var(--text-kyy-color-text-3, #828da5);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.setting {
  background-color: @kyy_color_bg_light;
  padding: 16px;

  // height: inherit;
  &-form {
    display: flex;
    flex-direction: column;
    gap: 20px;

    &-item {
      display: flex;
      align-items: center;

      .title {
        color: var(--text-kyy-color-text-1, #1a2139);

        /* kyy_fontSize_2/bold */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        /* 157.143% */
      }

      .value {
        display: flex;
        flex-direction: column;
        margin-left: 24px;
        gap: 16px;
        color: rgb(130, 141, 165);

        .team {
          display: flex;
          align-items: center;
          gap: 16px;

          :deep(.t-checkbox__label) {
            color: var(--checkbox-kyy_color_checkbox_text_default, #1a2139);
          }
        }
      }

      .value1 {
        display: flex;
        flex-direction: row;
        gap: 1px;
        color: var(--text-kyy-color-text-1, #1a2139);

        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }
    }
  }
}

.head-btn {
  margin-bottom: 24px;
}

:deep(.t-switch) {
  width: 44px;
  height: 24px;
}

.white-page {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  right: 0;
  background: #fff;
}

.font {
  color: rgb(130, 141, 165);

  // color: var(--checkbox-kyy_color_checkbox_text_active, red);

  /* kyy_fontSize_2/regular */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */

  :deep(.t-radio__label) {
    // color: var(--checkbox-kyy_color_checkbox_text_active, red);
    color: var(--checkbox-kyy_color_checkbox_text_active, #1a2139);
  }
}

:deep(.t-switch__handle) {
  width: 20px !important;
  height: 20px !important;
  top: 1.5px !important;
}
</style>
