import { join } from "path";
import { defineConfig } from "vite";
import vuePlugin from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import VueSetupExtend from "vite-plugin-vue-setup-extend";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import UnoCSS from "unocss/vite";
import { getConfig } from "./env";

const path = require("path");

function resolve(dir: string) {
  return join(__dirname, "..", dir);
}

const config = getConfig();
const root = resolve("src/renderer");

// fix: Failed to resolve component: iconpark-icon
const isCustomElement = (tag: string) => ['iconpark-icon', 'wc-waterfall'].includes(tag);

export default defineConfig({
  optimizeDeps: {
    exclude: ['@rk/editor']
  },
  mode: process.env.NODE_ENV,
  root,
  publicDir: '../../public',
  define: {
    __APP_ENV__: config,
  },
  resolve: {
    alias: {
      "@": root,
      "@renderer": root,
      "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
      "customTypes": resolve('customTypes'),
    },
  },
  assetsInclude: [
    'svga'
  ],
  base: "./",
  build: {
    /** 将静态资源内联到JS中以提升Electron加载速度 */
    assetsInlineLimit: config && config.target ? 4096 : 1024 * 1024 * 5, // Electron环境下5MB以下资源内联，Web环境4KB
    outDir: config && config.target ? resolve("dist/web") : resolve("dist/electron/renderer"),
    emptyOutDir: true,
    target: "esnext",
    reportCompressedSize: false,
    minify: false, //取消代码压缩
    cssCodeSplit: config && config.target ? true : false, // Web环境启用CSS分割，Electron环境禁用
    rollupOptions: {
      input: {
        index: join(__dirname, "../src/renderer/index.html"),
        internal: join(__dirname, "../src/renderer/windows/internal/index.html"),
        injoin: join(__dirname, "../src/renderer/windows/injoin/index.html"),
        wps: join(__dirname, "../src/renderer/windows/wps/index.html"),
        mergedmsg: join(__dirname, "../src/renderer/windows/merged/index.html"),
        RKIM: join(__dirname, "../src/renderer/windows/RKIM/index.html"),
        popBv: join(__dirname, "../src/renderer/windows/popBv/index.html"),
        square: join(__dirname, "../src/renderer/windows/square/index.html"),
        loading: join(__dirname, "../src/renderer/windows/loading/index.html"),
        webmodal: join(__dirname, "../src/renderer/windows/webmodal/index.html"),
        vcard: join(__dirname, "../src/renderer/windows/vcard/index.html"),
        activationCode: join(__dirname, "../src/renderer/windows/activationCode/index.html"),
        error: join(__dirname, "../src/renderer/windows/error/index.html"),
        /** SDK 选人组件 */
        SelectMember: join(__dirname, "../src/renderer/_jssdk/components/SelectMember/index.html"),
        /** SDK 加入数字平台组件 */
        JoinDigitalPlatformDrawer: join(__dirname, "../src/renderer/_jssdk/components/join-digital-platform-drawer/index.html"),
        /** SDK 组织认证组件 */
        OrgAuthDrawer: join(__dirname, "../src/renderer/_jssdk/components/org-auth-drawer/index.html"),
        /** SDK 添加联系人组件 */
        AddContactsDialog: join(__dirname, "../src/renderer/_jssdk/components/add-contacts-dialog/index.html"),
        /** SDK 年费组件 */
        AnnualFeeDrawer: join(__dirname, "../src/renderer/_jssdk/components/annual-fee-drawer/index.html"),
        /** SDK 窗口组件 for tabs */
        windows_tabs: join(__dirname, "../src/renderer/_jssdk/components/windows-tabs/index.html"),
        debugtools: join(__dirname, "../src/renderer/_jssdk/components/debugtools/index.html"),
      },
      output: {
        manualChunks(id) {
          // 控制拆包逻辑
          // 所有依赖独立一个js
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        },
        dir: "dist/electron/renderer",
      },
    },
  },

  server: {
    port: 3000,
  },
  plugins: [
    vueJsx({
      isCustomElement,
    }),
    vuePlugin({
      template: {
        compilerOptions: {
          isCustomElement,
        },
      },
    }),
    createSvgIconsPlugin({
      // 指定要缓存的图标文件夹
      iconDirs: [path.resolve(process.cwd(), "src/renderer/assets/svg")],
      // 执行icon name的格式
      symbolId: "icon-[dir]-[name]",
      svgoOptions: false,
    }),
    VueSetupExtend(),
    UnoCSS(),
  ],
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          hack: `true; @import (reference) "${resolve("src/renderer/style/index.less")}";`,
        },
        javascriptEnabled: true,
      },
    },
  },
});
