<template>
    <Teleport to="body">
        <div v-if="guide" class="guide" @click.stop="">
            <div>
                <div class="tool-bar tool-bar-top">
                    <div class="close-btn" @click="finishGuide">
                        <t-icon name="close-circle" size="32px" style="color: #fff"></t-icon>
                    </div>
                </div>

                <div class="video" @mouseenter="showControls = true" @mouseleave="showControls = false" @click="togglePlay">
                    <video ref="videoEle" style="border-radius: 8px;overflow: hidden;" width="680" height="386" :muted="isMute" autoplay @ended="onPlayEnd" >
                        <source :src="guide.url">
                    </video>
                    <!-- <SvgIcon v-show="isPause" class="play" name="video_play" color="#fff" style="width:80px;height:80px;"></SvgIcon> -->
                    <div v-show="isPause" class="i-svg:video_play text-80 text-white" />
                </div>

                <div class="tool-bar tool-bar-bottom" @mouseenter="showControls = true" @mouseleave="showControls = false" >
                    <div v-if="isMute" class="voice" @click="toggleMute" v-show="showControls">
                        <!-- <SvgIcon name="video_mute" color="#fff" style="width:24px;height:24px;color:#fff;"></SvgIcon> -->
                        <i class="i-svg:video_mute text-24 text-white" />
                        {{ t('im.public.unmute1') }}
                    </div>
                    <div v-else class="voice" @click="toggleMute" v-show="showControls">
                        <!-- <SvgIcon  name="video_voice" color="#fff" style="width:24px;height:24px;color:#fff;"></SvgIcon> -->
                        <i class="i-svg:video_voice text-24 text-white" />
                        {{ t('im.public.mute1') }}
                    </div>
                </div>
            </div>
        </div>
    </Teleport>
</template>

<script lang="ts" setup>

import { ref, withDefaults } from "vue"
import { finishGuideStatusApi } from "@renderer/api/im/api";
import SvgIcon from "@/components/SvgIcon.vue";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

type IProps = {
    guide?: {
        // "FamilyGroup" | "SceneSocial"
        "typ": string,
        "openid": string,
        "status": boolean,
        "url": string,
        // "VIDEO" | "PICTURE"
        "url_type": string,
        "user_id": string,
        "zone"?: string,
        "created"?: number,
        "removed"?: number,
        "updated"?: number
    }
}

const props = withDefaults(defineProps<IProps>(), {
    guide: null
})

const emits = defineEmits(['finished']);

const isPause = ref(false);
const isMute = ref(true);

const videoEle = ref<HTMLVideoElement>();
const showControls = ref(false);

const onPlayEnd = () => {
    videoEle.value?.pause();
    isPause.value = true;
}

const finishGuide = async () => {
    try {
        const res = await finishGuideStatusApi(props.guide.openid)
        if (res.status === 200) {
            emits('finished');
        }
    } catch (error) {
        emits('finished');
    }

}

const togglePlay = () => {
    isPause.value = !isPause.value;
    if (isPause.value) {
        videoEle.value?.pause();
    } else {
        videoEle.value?.play();
    }
}

const toggleMute = () => {
    isMute.value = !isMute.value;
}

</script>

<style scoped lang="less">
.guide {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.50));
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.close-btn {
    cursor: pointer;
    padding: 8px;

    &:hover {
        opacity: 0.8;
    }
}

.tool-bar {
    height: 64px;

    &-top {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
    }

    &-bottom {
        position: relative;
        bottom: 64px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
    }
}

.voice {
    margin-left: 16px;
    border-radius: 999px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 8px;
    color: #fff;
    line-height: 24px;
    gap: 4px;
    font-size: 16px;
    font-weight: 400;
    cursor: pointer;
}

.video {
    position: relative;

    .play {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(26, 33, 57, 0.36);
        border-radius: 50%;
    }
}

</style>
