import type { AxiosResponse } from 'axios';
import type { PostResponse, SharedPost } from '@/api/square/models/post';
import { squareRequest } from '../requestApi';
import type { AppEntry, Entry, SquareData, SquareStatistics } from './models/square';

const Api = {
  post: '/v1/spost',
  square: '/v1/ssquare',
  trialAnnualFee: '/v1/organization/trial_annual_fee',
  squareStats: (squareId: string) => `/v1/square/${squareId}/stats`,
  postList: (squareId: string) => `/v1/ssquare/${squareId}/posts`,
  appEntries: (squareId: string) => `/v1/square/${squareId}/app_entries`,
}

// 获取被分享动态详情
export const getPostDetail = (params = {}): Promise<AxiosResponse<PostResponse>['data']> => squareRequest.get(Api.post, { params })

// 重定向到下载引导页
// export const redirectToDownload = (params = {}): Promise<AxiosResponse> => squareRequest.get('/v1/redirect_to_download', { params });

// 获取广场信息
export const getSquareInfo = (params = {}): Promise<AxiosResponse<{
  info: SquareData,
  stats?: SquareStatistics;
  entries?: AppEntry[];
}>['data']> => squareRequest.get(Api.square, { params });

// 获取动态列表
export const getPostList = (squareId: string): Promise<AxiosResponse<{ posts: SharedPost[] }>['data']> => squareRequest.get(Api.postList(squareId));

// 获取统计数据
export const getSquareStats = (squareId: string, params = {}): Promise<AxiosResponse<{ stats: SquareStatistics }>['data']> => squareRequest.get(Api.squareStats(squareId), { params });

// 获取应用入口列表
export const getAppEntries = (squareId: string): Promise<AxiosResponse<{ entries: Entry[] }>['data']> => squareRequest.get(Api.appEntries(squareId));

// 获取年费套餐信息
export const getSharedTeamAnnualFee = (params = {}): Promise<AxiosResponse> => squareRequest.get(`/v1/shared/team_annual_fee`, { params } );

// 获取可用体验年费套餐
export const getTrialAnnualFee = (team_id: string) => squareRequest.get(Api.trialAnnualFee, { params: { team_id } });
