<template>
  <t-dialog
    attach="body"
    class="activity-delete-reason-dialog"
    :header="false"
    :visible="visible"
    :close-btn="false"
    :footer="false"
    placement="center"
  >
    <div class="p-24 flex justify-between items-center">
      <div class="text-16 text-[#1A2139] leading-24 font-600">删除原因</div>
      <iconpark-icon name="iconerror" class="cursor-pointer text-24 text-[#1A2139]" @click="close" />
    </div>

    <t-form
      ref="formRef"
      label-align="top"
      :data="formData"
    >
      <div class="activity-delete-reason-form px-24">
        <t-form-item label="将从当前列表中删除，并通知对应推广人" name="reason">
          <div class="textarea-box">
            <t-textarea
              v-model="formData.reason"
              placeholder="请输入"
              :maxlength="200"
              show-limit-number
            />
          </div>
        </t-form-item>
      </div>
    </t-form>

    <div class="bg-[#fff] py-24 px-24 flex justify-end gap-8">
      <t-button class="w-80" theme="default" @click="close">取消</t-button>
      <t-button
        class="min-w-80"
        theme="primary"
        :disabled="formData.reason === '' || formData.reason === null"
        @click="submit"
      >
        确定
      </t-button>
    </div>
  </t-dialog>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { deleteDPActivity } from '@/api/activity/platform';

const emit = defineEmits<{(e: 'success'): void }>();

const formRef = ref(null);

const visible = ref(false);

const formData = reactive({
  activityId: null,
  // 删除原因
  reason: null,
});

const open = (activityId) => {
  formData.activityId = activityId;

  visible.value = true;
};

const close = () => {
  visible.value = false;

  setTimeout(() => {
    formData.reason = null;
    formRef.value?.reset();
  }, 300);
};

const submit = async () => {
  await deleteDPActivity({
    ...formData,
    teamId: localStorage.getItem('honorteamid'),
  });

  MessagePlugin.success('删除成功');

  emit('success');

  close();
};

defineExpose({
  open,
});
</script>

<style lang="less">
.activity-delete-reason-dialog {
  .t-dialog__position.t-dialog--center {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .t-dialog {
    padding: 0;
    width: 480px;
    border: none;

    .t-dialog__body {
      padding: 0;
      border-radius: 16px;

      .activity-delete-reason-form {
        .t-form__label {
          line-height: 22px;
          min-height: 22px;
          padding: 0;

          &.t-form__label--top {
            margin-bottom: 8px;
          }
        }

        .t-form__controls {
          min-height: 22px;
          .t-form__controls-content {
            min-height: 22px;
          }

          &.t-is-error {
            .textarea-box {
              border-color: #d54941;
            }
          }
        }

        .t-form__controls-content {
          .t-input {
            padding-left: 12px;
          }
        }

        .textarea-box {
          height: 146px;
          width: 100%;
          position: relative;
          padding: 4px;
          border-radius: 4px;
          border: 1px solid #d5dbe4;

          .t-textarea {
            height: 100%;
          }

          .t-textarea__inner {
            resize: none;
            padding: 8px 0 0 8px;
            border: none;
            height: 110px;

            &::-webkit-scrollbar {
              width: 6px;
              height: 6px;
              background: transparent;
            }

            &::-webkit-scrollbar-thumb {
              border-radius: 4px;
              //background: #d5dbe4 !important;
            }

            &::-webkit-scrollbar-track {
              background-color: transparent !important;
            }
          }

          .t-textarea__limit {
            display: inline !important;
            line-height: 20px;
            position: absolute;
            right: 4px;
            bottom: 4px;
            color: var(--text-kyy_color_text_5, #acb3c0);
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
