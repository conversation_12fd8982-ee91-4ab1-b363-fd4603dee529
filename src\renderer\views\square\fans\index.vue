<template>
  <div class="page-content">
    <div class="title">{{ $t('square.square.allFans') }}({{ stats.fans }})</div>

    <PersonList :data="dataList" :show-follow="false" :show-empty="false">
      <template #name="{ item }">
        <div class="name-wrap">
          <div class="name lin-1" v-html="item._name" />
          <div class="time">{{ item.followedTime }} {{ $t('square.square.follow') }}</div>
        </div>
      </template>
      <template #desc="{ item }">
        <div class="desc">{{ item.intro }}</div>
      </template>
    </PersonList>

    <InfiniteLoading :identifier="refreshId" @infinite="loadMore">
      <template #complete>
        <Empty v-if="!dataList.length" :tip="$t('square.square.noFansFollow')">
          <!-- <SvgIcon name="square-empty-fans" /> -->
          <i class="i-svg:empty-fans text-140" />
        </Empty>
      </template>
    </InfiniteLoading>
  </div>
</template>

<script setup lang="ts" name="SquareFans">
import to from 'await-to-js';
import { ref } from 'vue';
import PersonList from '@/views/square/components/PersonList.vue';
import InfiniteLoading from '@/components/InfiniteLoading/index.vue';
import useInfiniteLoad from '@/hooks/infiniteLoad';
import { getSquareFansList, getSquareStats } from '@/api/square/home';
import { useSquareStore } from '@/views/square/store/square';
import { onMountedOrActivated } from '@/hooks/onMountedOrActivated';
import { formatDate } from '@/utils/date';
import Empty from '@/components/common/Empty.vue';
// import SvgIcon from '@/components/SvgIcon.vue';

const store = useSquareStore();
const {
  dataList, loadMore, reLoad, refreshId,
} = useInfiniteLoad(getSquareFansList, {
  listName: 'squares',
  params: { square_id: store.squareId },
  dataHandler: (v) => ({
    ...v,
    followedTime: formatDate(v.followedAt),
  }),
});

const stats = ref({});
const getStats = async (square_id) => {
  const [err, res] = await to(getSquareStats({ square_id }));
  if (err) return;
  stats.value = res.data.stats;
};

onMountedOrActivated(() => {
  getStats(store.squareId);
  // refreshId.value++;
  reLoad();
});
</script>

<style lang="less" scoped>
.title {
  color: var(--text-kyy-color-text-2, #516082);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  padding: 16px;
}

.name-wrap {
  display: flex;
  margin-bottom: 2px;
}

.name {
  color: var(--text-kyy-color-text-1, #1A2139);
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  margin-right: 20px;
}
.time, .desc {
  font-size: 14px;
  color: var(--text-kyy-color-text-3, #828DA5);
}

.desc {
  width: 800px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
