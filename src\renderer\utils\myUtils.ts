import Qs from 'qs';
import { MessagePlugin } from 'tdesign-vue-next';
import { i18nt } from '@/i18n';

import pdf from '@renderer/assets/svg/pdf.svg';
import folder from '@renderer/assets/svg/icon_staffFiles (1).svg';
// import file from "@renderer/assets/svg/icon_staffFiles (1).svg";
import image from '@renderer/assets/svg/image.svg';
import ppt from '@renderer/assets/svg/ppt.svg';
import excel from '@renderer/assets/svg/excel.svg';
import other from '@renderer/assets/svg/other.svg';
import qita from '@renderer/assets/svg/icon_group.svg';
import world from '@renderer/assets/svg/world.svg';
import common from '@renderer/assets/svg/common.svg';
import video from '@renderer/assets/svg/icon_video.svg';
import { RouteItem, RouteItem } from '@renderer/views/engineer/store/types';
import { ClientSide } from '@renderer/types/enumer.js';
import CLOIDDISKIMG from '@renderer/assets/svg/cloud.svg'; // 云盘
import BENCH from '@renderer/assets/svg/workshop.svg';
import TAKEDOWNIMG from '@renderer/assets/icon_apply_deal@2x (1).png'; // 笔记
import INVENTORYIMG from '@renderer/assets/icon_apply_deal@2x (2).png'; // 清单
import PROGRAMMEIMG from '@renderer/assets/<EMAIL>'; // 日程
import NOTICE from '@/assets/niche/ggao2.svg';
import FCIMG from '@/assets/fengcai/elegance.svg';

import NEEDTOBEDEALTWITHIMG from '@renderer/assets/<EMAIL>'; // 待办
import APPROVALIMG from '@renderer/assets/approval.svg'; // 审批
import DEVICEIMG from '@renderer/assets/device.png'; // 设备
import CUSTOMERIMG from '@renderer/assets/customer/peoples.png'; // 客户
import SUPPLIERIMG from '@renderer/assets/supplier/business_partner.png'; // 供应商
import PARTNER from '@renderer/assets/partner/business_partner.png'; // 合作伙伴
import CLOIDDISKIMGADMINIMG from '@renderer/assets/svg/cloudbackgroundtap.svg'; // 云盘后台
import ACTIVITYIMG from '@renderer/assets/svg/activity.svg'; // 活动
import NICEHIMG from '@renderer/assets/svg/niche.svg'; // 活动
import MEMBER from '@renderer/assets/member/svg/member.svg';
import BIGMARKETIMG from '@renderer/assets/big-market/market.svg';
import POLITICS from '@renderer/assets/member/svg/government.svg';
import SERVICE from '@renderer/assets/service/service.svg';
import appicon14 from '@/assets/bench/appicon14.svg';
import appicon8 from '@/assets/bench/appicon8.svg';
import appicon9 from '@/assets/bench/appicon9.svg';
import appicon16 from '@/assets/bench/appicon16.svg';
import tabcbd from '@/assets/cbd/svg/tabcbd.svg';
import photo_album from '@/assets/photo_album.svg';
import directory from "@renderer/assets/member/svg/directory.svg";

import ztc from '@/assets/bench/ztc.svg';
import shop from '@/assets/svg/shop.svg';
import external_app from '@/assets/bench/external_app_mini.svg';


import association from '@renderer/assets/member/svg/association.svg';
import partner from '@/assets/partner/partner.svg';

// import appicon10 from "@/assets/pb/party.svg";
import appicon10 from '@/assets/pb/party-1.svg';
import CBD from '@/assets/partner/cbd.svg';
import { getWebsiteUrl } from '@renderer/constants/website';
import { inviteUrl } from './baseUrl';
import { OrderChannel } from '@renderer/api/square/models/square';
import { isMacOS, isWindows } from '@renderer/views/square/utils';
import ringkolAdvertisementIcon from '@/assets/workBench/lkad.svg';
import lkadhead from '@/assets/workBench/lkadhead.svg';
import school from '@/assets/member/svg/school.svg';
const Store = LynkerSDK.eStore;

const useStore = new Store({ accessPropertiesByDotNotation: true });
export function setgztdid(v) {
  // 工作台
  useStore.set('gztdqzzid', v);
}
export function getgztdid() {
  // 工作台
  useStore.get('gztdqzzid');
}

export function isUrlContainsTexts(urls) {
  const regex = /\?key=([^&]+).*提取碼：([^&]+)/;
  const matches = urls.match(regex);
  if (matches) {
    const keyVariable = matches[1];
    const signVariable = matches[2];
    const regexPattern = new RegExp(
      `^${escapeRegExp(getWebsiteUrl('cloudDisk'))}\\/#\\/link\\?key=${escapeRegExp(
        keyVariable,
      )}&sign=[^&]+提取碼：${escapeRegExp(signVariable)}$`,
    );

    return regexPattern.test(urls);
  }

  return false;
}

export function isCloudDiskUrl(url: string) {
  const escapedLink = `${escapeRegExp(getWebsiteUrl('cloudDisk'))}\\/#\\/link\\?key=`;
  const regexStr = `${escapedLink}[\\w\\-]+&sign=[\\w\\-]+`;
  return new RegExp(regexStr).test(url);
}

function escapeRegExp(string) {
  return string.replace(/[.*+\-?^${}()|[\]\\]/g, '\\$&');
}

export function tapImage(type) {
  console.log(type, 'typeeeeeee');

  let itemImg;
  switch (type) {
     case ClientSide.UNI:
      itemImg = school;
      break;
    case ClientSide.RINGKOLADVERTISEMENT:
      itemImg = lkadhead;
      break;
    case ClientSide.POLICYTHROUGHTRAIN:
      itemImg = ztc;
      break;
    case ClientSide.ALBUM:
      itemImg = photo_album;
      break;
    case ClientSide.CBD:
      itemImg = tabcbd;
      break;
    case ClientSide.CLOUDDISK:
      itemImg = CLOIDDISKIMG;
      break;
    case ClientSide.APPROVAL:
      itemImg = APPROVALIMG;
      break;
    case ClientSide.DEVICE:
      itemImg = DEVICEIMG;
      break;

    case ClientSide.KEFU:
      itemImg = appicon9;
      break;
    case ClientSide.EGINEER:
      itemImg = appicon14;
      break;
    case ClientSide.CUSTOMER:
      itemImg = CUSTOMERIMG;
      break;
    case ClientSide.SUPPLIER:
      itemImg = SUPPLIERIMG;
      break;
    case ClientSide.PARTNER:
      itemImg = PARTNER;
      break;
    case ClientSide.NEEDTOBEDEALTWITH:
      itemImg = NEEDTOBEDEALTWITHIMG;
      break;
    case ClientSide.PROGRAMME:
      itemImg = PROGRAMMEIMG;
      break;
    case ClientSide.TAKEDOWN:
      itemImg = TAKEDOWNIMG;
      break;
    case ClientSide.POLITICS:
      itemImg = appicon8;
      break;

    case ClientSide.INVENTORY:
      itemImg = INVENTORYIMG;
      break;
    case ClientSide.ACTIVITY:
      itemImg = ACTIVITYIMG;
      break;
    case ClientSide.NICHE:
      itemImg = NICEHIMG;
      break;
    case ClientSide.SERVICE:
      itemImg = SERVICE;
      break;

    case ClientSide.BENCH:
      itemImg = BENCH;
      break;
    case ClientSide.MEMBER:
      itemImg = MEMBER;
      break;
    case ClientSide.PB:
      itemImg = appicon10;
      break;
    case ClientSide.NOTICE:
      itemImg = NOTICE;
      break;
    case ClientSide.FC:
      itemImg = FCIMG;
      break;
    case ClientSide.BIGMARKET:
      itemImg = BIGMARKETIMG;
      break;
    case ClientSide.ASSOCIATION:
      itemImg = association;
      break;
    case ClientSide.PARTNERAPP:
      itemImg = partner;
      break;
    case ClientSide.NAMEDIRECTORY:
      itemImg = directory;
      break;
    case ClientSide.SHOP:
      itemImg = shop;
      break;
    case ClientSide.EXTERNAL_APP:
      itemImg = external_app;
      break;
    case ClientSide.FEATURED_SERVICES:
      itemImg = appicon16;
      break;
    default:
      itemImg = CLOIDDISKIMGADMINIMG;
      break;
  }
  return itemImg;
}
export function KBTOMB(bytes, places = 2) {
  if (bytes === 0 || typeof bytes !== 'number') {
    return '0.00';
  }
  const k = 1024;
  const sizes = ['KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  if (i < 0) {
    return `${bytes.toFixed(places)} ${sizes[0]}`;
  }

  const convertedValue = (bytes / k ** i).toFixed(places);
  return `${convertedValue} ${sizes[i]}`;
}
// export function KBTOMB(bytes) {
//   if (bytes === 0) return "0.00";

//   const k = 1024;
//   const sizes = ["KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
//   const i = Math.floor(Math.log(bytes) / Math.log(k));

//   if (i < 0) {
//     return `${bytes} ${sizes[0]}`;
//   }

//   const convertedValue = (bytes / k ** i).toFixed(2);
//   return `${
//     Math.floor(convertedValue) === Number(convertedValue)
//       ? Math.floor(convertedValue)
//       : convertedValue
//   } ${sizes[i]}`;
// }
// export function KBTOMB(bytes) {
//   const conversionFactors = {
//     KB: 1,
//     MB: 1024,
//     GB: 1024 * 1024,
//     TB: 1024 * 1024 * 1024,
//     PB: 1024 * 1024 * 1024 * 1024,
//     EB: 1024 * 1024 * 1024 * 1024 * 1024,
//     ZB: 1024 * 1024 * 1024 * 1024 * 1024 * 1024,
//     YB: 1024 * 1024 * 1024 * 1024 * 1024 * 1024 * 1024,
//   };

//   let unit = Object.keys(conversionFactors)[0];
//   for (let i = 0; i < Object.keys(conversionFactors).length; i++) {
//     if (bytes >= conversionFactors[Object.keys(conversionFactors)[i]]) {
//       unit = Object.keys(conversionFactors)[i];
//       break;
//     }
//   }

//   let value = bytes / conversionFactors[unit];
//   if (value >= 1) {
//     value = value.toFixed(1);
//   } else {
//     value = value.toFixed(0);
//   }
//   return `${value} ${unit}`;
// }

export function getTime(date, flag?) {
  const Y = date.getFullYear();
  const M = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1;
  const D = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
  const h = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours();
  const m = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes();
  const s = date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds();
  if (flag) {
    return `${Y}-${M}-${D}`;
  }
  return `${Y}-${M}-${D} ${h}:${m}:${s}`;
}
export function getTimeMH(date) {
  const Y = date.getFullYear();
  const M = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1;
  const D = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
  const h = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours();
  const m = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes();

  return `${Y}-${M}-${D} ${h}:${m}`;
}

export const imgUrl = 'https://img.kuaiyouyi.com/';

export function fileImage(typeInfo) {
  const type = typeInfo?.toLowerCase();
  let src = '';
  switch (type) {
    case 'common':
      src = common;
      break;
    case 'folder':
      src = folder;
      break;

    case 'word':
    case 'doc':
    case 'docx':
      src = world;
      break;
    case 'pdf':
      src = pdf;
      break;
    case 'ppt':
    case 'pptx':
      src = ppt;
      break;
    case 'xls':
    case 'xlsx':
      src = excel;
      break;
    case 'groupFile':
      src = qita;
      break;
    case 'image':
    case 'jpeg':
    case 'jpg':
    case 'png':
    case 'gif':
    case 'svg':
    case 'webp':
    case 'bmp':
    case 'exif':
    case 'apng':
    case 'heif':
    case 'tiff':
      src = image;
      break;
    case 'mp4':
    case 'mov':
    case 'wmv':
    case 'mpg':
    case 'flv':
    case 'avi':
    case 'm3u8':
    case 'wma':
    case 'rmvb':
      src = video;
      break;
    default:
      src = other; // 其他
      break;
  }
  return src;
}

/**
 * 通用js方法封装处理
 * Copyright (c) 2019 ruoyi
 */
// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time, 10);
    } else if (typeof time === 'string') {
      time = time
        // eslint-disable-next-line prefer-regex-literals
        .replace(new RegExp(/-/gm), '/')
        .replace('T', ' ')
        // eslint-disable-next-line prefer-regex-literals
        .replace(new RegExp(/\.[\d]{3}/gm), '');
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time *= 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  // eslint-disable-next-line camelcase
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value];
    }
    if (result.length > 0 && value < 10) {
      value = `0${value}`;
    }
    return value || 0;
  });
  // eslint-disable-next-line camelcase
  return time_str;
}
export function checkPhoneNumber(mobileZone = 86, mobile) {
  let reg = null;
  let passed = true;
  switch (Number(mobileZone)) {
    case 86:
      reg = /^[1][3-9]\d{9}$/;
      if (!reg.test(mobile)) {
        passed = false;
      }
      break;
    case 852:
      reg = /^([5|6|7|8|9])\d{7}$/;
      if (!reg.test(mobile)) {
        passed = false;
      }
      break;
    case 853:
      reg = /^[6]\d{7}$/;
      if (!reg.test(mobile)) {
        passed = false;
      }
      break;
    default:
      break;
  }
  return passed;
}
// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
  }
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  const search = params;
  search.params = typeof search.params === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
  dateRange = Array.isArray(dateRange) ? dateRange : [];
  if (typeof propName === 'undefined') {
    search.params.beginTime = dateRange[0];
    search.params.endTime = dateRange[1];
  } else {
    search.params[`begin${propName}`] = dateRange[0];
    search.params[`end${propName}`] = dateRange[1];
  }
  return search;
}

// 回显数据字典
export function selectDictLabel(datas, value) {
  if (value === undefined) {
    return '';
  }
  const actions = [];
  // eslint-disable-next-line array-callback-return
  Object.keys(datas).some((key) => {
    // eslint-disable-next-line eqeqeq
    if (datas[key].value == `${value}`) {
      actions.push(datas[key].label);
      return true;
    }
  });
  if (actions.length === 0) {
    actions.push(value);
  }
  return actions.join('');
}

// 回显数据字典（字符串数组）
export function selectDictLabels(datas, value, separator) {
  if (value === undefined || value.length === 0) {
    return '';
  }
  if (Array.isArray(value)) {
    value = value.join(',');
  }
  const actions = [];
  const currentSeparator = undefined === separator ? ',' : separator;
  const temp = value.split(currentSeparator);
  // eslint-disable-next-line array-callback-return
  Object.keys(value.split(currentSeparator)).some((val) => {
    let match = false;
    // eslint-disable-next-line array-callback-return
    Object.keys(datas).some((key) => {
      // eslint-disable-next-line eqeqeq
      if (datas[key].value == `${temp[val]}`) {
        actions.push(datas[key].label + currentSeparator);
        match = true;
      }
    });
    if (!match) {
      actions.push(temp[val] + currentSeparator);
    }
  });
  return actions.join('').substring(0, actions.join('').length - 1);
}

// 字符串格式化(%s )
export function sprintf(str) {
  // eslint-disable-next-line prefer-rest-params
  const args = arguments;
  let flag = true;
  let i = 1;
  // eslint-disable-next-line func-names
  str = str.replace(/%s/g, () => {
    const arg = args[i++];
    if (typeof arg === 'undefined') {
      flag = false;
      return '';
    }
    return arg;
  });
  return flag ? str : '';
}

// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str) {
  if (!str || str === 'undefined' || str === 'null') {
    return '';
  }
  return str;
}

// 数据合并
export function mergeRecursive(source, target) {
  for (const p in target) {
    try {
      if (target[p].constructor === Object) {
        source[p] = mergeRecursive(source[p], target[p]);
      } else {
        source[p] = target[p];
      }
    } catch (e) {
      source[p] = target[p];
    }
  }
  return source;
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  const config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children',
  };

  const childrenListMap = {};
  const nodeIds = {};
  const tree = [];

  for (const d of data) {
    const parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentId].push(d);
  }

  for (const d of data) {
    const parentId = d[config.parentId];
    if (nodeIds[parentId] == null) {
      tree.push(d);
    }
  }

  for (const t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]];
    }
    if (o[config.childrenList]) {
      for (const c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  }
  return tree;
}

/**
 * 参数处理
 * @param {*} params  参数
 */
export function tansParams(params) {
  let result = '';
  for (const propName of Object.keys(params)) {
    const value = params[propName];
    const part = `${encodeURIComponent(propName)}=`;
    if (value !== null && value !== '' && typeof value !== 'undefined') {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== '' && typeof value[key] !== 'undefined') {
            const params = `${propName}[${key}]`;
            const subPart = `${encodeURIComponent(params)}=`;
            result += `${subPart + encodeURIComponent(value[key])}&`;
          }
        }
      } else {
        result += `${part + encodeURIComponent(value)}&`;
      }
    }
  }
  return result;
}

// 返回项目路径
export function getNormalPath(p) {
  if (p.length === 0 || !p || p === 'undefined') {
    return p;
  }
  const res = p.replace('//', '/');
  if (res[res.length - 1] === '/') {
    return res.slice(0, res.length - 1);
  }
  return res;
}

// 验证是否为blob格式
export async function blobValidate(data) {
  try {
    const text = await data.text();
    JSON.parse(text);
    return false;
  } catch (error) {
    return true;
  }
}
/**
 * @description 根据区号校验电话格式
 * @param {*} mobileZone 区号
 * @param {string} mobile 电话号码
 * @param {Boolean} hideToast 隐藏提示
 * @return {Boolean}
 */
export const checkPhoneNumberOnly = (mobile) => {
  let passed = true;
  let msg = '';
  const reg1 = /^[1][3-9]\d{9}$/;
  const reg2 = /^([5|6|8|9])\d{7}$/;
  const reg3 = /^[6]\d{7}$/;
  if (!reg1.test(mobile) && !reg2.test(mobile) && !reg3.test(mobile)) {
    passed = false;
    msg = '請輸入正確的手機號';
  }
  return { status: passed, message: msg };
};

// 截取字符串，突出名字
export const getNameContent = (name) => {
  if (!name) {
    return '--';
  }
  const namestr = name.replace(/[`~_!@#$^&*()=|{}':;',?~！@#￥……&*（）——|{}【】'；：""'。，、？\s]/g, '');
  const isletter = /^[a-zA-Z]+$/.test(namestr);
  if (isletter) {
    return name.slice(0, 2);
  }
  return name.slice(name.length - 2, name.length);
};

// 用于判断状态码异常处理
export const getResponseResult = (res) => {
  if (!res) {
    return null;
  }

  if ([200, 201].includes(res.status)) {
    return res.data;
  }
  console.log(res);
  throw new Error(res ? res.data.message : '异常');

  return null;
};

// lss
// 获取一级路由下的子路由数据
export const getSubRoutes = (parentName: string, router): RouteItem[] => {
  const topRoute = router.options.routes.find((v) => v.name === parentName);
  return topRoute.children.map((v) => ({
    name: v.name,
    path: v.path,
    icon: v.meta?.icon as string,
    title: v.meta?.title as string,
    fullPath: `${topRoute.path}/${v.path}`,
    parentPath: v.meta?.parentPath,
    affix: Boolean(v.meta?.affix),
    hidden: Boolean(v.meta?.hidden),
    role: v.meta?.role as string,
    sort: (v.meta?.sort || 10) as number,
  }));
};

/**
 * @description 格式化价格分位展示 除divisor展示价格通用千分位
 */
export const priceDivisorShow = (val, divisor) => numberFormat(Number(val) / divisor, 2, '.', ',');

/**
 * @desc: 实现金额千分位显示
 * @param {*} number
 * @param {*} decimals
 * @param {*} decPoint
 * @param {*} thousandsSep
 * @param {*} roundtag
 * @example: console.log(numberFormat(100000.00, 2, '.', ',')) // '100,000.00'
 */

export const numberFormat = (number, decimals, decPoint, thousandsSep, roundtag?) => {
  /*
   * 参数说明：
   * number：要格式化的数字
   * decimals：保留几位小数
   * dec_point：小数点符号
   * thousands_sep：千分位符号
   * roundtag:舍入参数，默认 'ceil' 向上取,'floor'向下取,'round' 四舍五入
   */
  number = `${number}`.replace(/[^0-9+-Ee.]/g, '');
  roundtag = roundtag || 'ceil'; // 'ceil','floor','round'
  const n = !Number.isFinite(+number) ? 0 : +number;
  const prec = !Number.isFinite(+decimals) ? 0 : Math.abs(decimals);
  const sep = typeof thousandsSep === 'undefined' ? ',' : thousandsSep;
  const dec = typeof decPoint === 'undefined' ? '.' : decPoint;
  let s: any = '';
  // eslint-disable-next-line func-names
  const toFixedFix = function (n, prec) {
    const k = 10 ** prec;
    console.log();

    return `${parseFloat(Math[roundtag](parseFloat((n * k).toFixed(prec * 2))).toFixed(prec * 2)) / k}`;
  };
  s = (prec ? toFixedFix(n, prec) : `${Math.round(n)}`).split('.');
  const re = /(-?\d+)(\d{3})/;
  while (re.test(s[0])) {
    s[0] = s[0].replace(re, `$1${sep}$2`);
  }

  if ((s[1] || '').length < prec) {
    s[1] = s[1] || '';
    s[1] += new Array(prec - s[1].length + 1).join('0');
  }
  return s.join(dec);
};

// 回传乘以100
export const priceRecovery = (val) => parseInt(`${Number(val) * 100}`, 10);
// 下载blob文件
export function createDownload(blob, fileName, fileType) {
  if (!blob || !fileName || !fileType) return;
  const element = document.createElement('a');
  const url = window.URL.createObjectURL(blob);
  element.style.display = 'none';
  element.href = url;
  element.download = `${fileName}.${fileType}`;
  document.body.appendChild(element);
  element.click();
  if (window.URL) {
    window.URL.revokeObjectURL(url);
  } else {
    window.webkitURL.revokeObjectURL(url);
  }
  document.body.removeChild(element);
}

export function createVNDDownload(url, fileName) {
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 下载二进制文件

export const createDownloadFromBinary = (uploadFileRes) => {
  // const data = uploadFileRes.data; // 后台传回来的base64字符串
  console.log(uploadFileRes);
  console.log(typeof uploadFileRes);
  const isBase64 = /^data:[\w\/\+\-]+;(?:[\w\s\/\-]|=(?:\r\n?|\n))*$/i.test(uploadFileRes);
  console.log(isBase64);

  const uint8Array = new Uint8Array(uploadFileRes);
  console.log(uint8Array);
  createDownload(new Blob([uint8Array]));

  // const url = window.URL.createObjectURL(new Blob([arrayBuffer]))
  // const link = document.createElement('a')
  // link.style.display = 'none'
  // link.href = url
  // // link.setAttribute('download', 'response.pdf')
  // document.body.appendChild(link)
  // link.click()
  // document.body.removeChild(link)
};

// 二进制流转换为base64 格式。
export const binaryToBase64 = (data, opt) => new Promise((resolve, reject) => {
  const blob = new Blob([data], { type: 'image/jpeg' || opt.type }); // 类型一定要写！！！
  const reader = new FileReader();
  reader.readAsDataURL(blob);
  reader.onload = () => resolve(reader.result);
  reader.onerror = (error) => reject(error);
});

// 下载二进制文件
export const downloadBinary = async (data, opt) => {
  const _opt = { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', ...opt };
  const url = await binaryToBase64(data, _opt);
  return LynkerSDK.ipcRenderer.invoke('download-file', {
    title: opt.name || 'excel.xlsx',
    url,
    fileType: _opt.fileType || 'xlsx',
  });

  // 浏览器下载形式
  // const url = window.URL.createObjectURL(new Blob([data], _opt))
  // const link = document.createElement('a')
  // link.style.display = 'none'
  // link.href = url
  // link.setAttribute(opt.path || 'download', opt.name || 'excel.xlsx')
  // document.body.appendChild(link)
  // link.click()
  // document.body.removeChild(link)

};

/*
 * @desc:这种递归方式，是先从内深入去执行，先从第一个元素，不断解刨children去寻找，
 *       最后从最后一个开始向刚开始的方向去寻找
 */
export function getChidlren(id: Number, datas: any) {
  let hasFound = false; // 表示是否有找到id值
  let result = null;
  // 通过递归实现
  const fn = function (data: any) {
    if (Array.isArray(data) && !hasFound) {
      // 判断是否是数组并且没有的情况下，
      data.forEach((item) => {
        if (item.id === id) {
          // 数据循环每个子项，并且判断子项下边是否有id值
          result = item; // 返回的结果等于每一项
          hasFound = true; // 并且找到id值
        } else if (item.children) {
          fn(item.children); // 递归调用下边的子项
        }
      });
    }
  };
  fn(datas); // 调用一下
  return result;
}

/*
 * @输入参数 list: 需要扁平化的树形结构数组，默认按children字段扁平展开
 * @输出：返回别扁平化的数组
 */
export function platFn(list) {
  let res = [];
  res = list.concat(
    ...list
      .map((item) => {
        if (item.children instanceof Array && item.children.length > 0) {
          return platFn(item.children);
        }
        return null;
      })
      .filter((o) => o instanceof Array && o.length > 0),
  );
  return res;
}

// 跳转访问h5
export function previewAppPage({ url = '', path = '', queryParams = {} }) {
  let combineUrl = '';
  if (url) {
    combineUrl = url;
  } else {
    combineUrl = inviteUrl + path + Qs.stringify(queryParams);
  }

  console.log(combineUrl);

  LynkerSDK.ipcRenderer.invoke(
    'create-h5-preview',
    'http://h5-dev.ringkol.com/member/invite?link=d9844ff11d04752dbbd318c075dee4bf',
  );
}

// 密码aes加密规则
import CryptoJS from 'crypto-js';
import { ep } from '@fullcalendar/core/internal-common';
import LynkerSDK from '@renderer/_jssdk';

export function encrypt(word) {
  const keyStr = byteToString([48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 48, 49, 50, 51, 52, 53]);
  const key = CryptoJS.enc.Utf8.parse(keyStr);
  const srcs = CryptoJS.enc.Utf8.parse(word);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });

  const str = encrypted.toString();
  return str;
}

function byteToString(arr) {
  if (typeof arr === 'string') {
    return arr;
  }
  let str = '';
  const _arr = arr;
  for (let i = 0; i < _arr.length; i++) {
    const one = _arr[i].toString(2);
    const v = one.match(/^1+?(?=0)/);
    if (v && one.length == 8) {
      const bytesLength = v[0].length;
      let store = _arr[i].toString(2).slice(7 - bytesLength);
      for (let st = 1; st < bytesLength; st++) {
        store += _arr[st + i].toString(2).slice(2);
      }
      str += String.fromCharCode(parseInt(store, 2));
      i += bytesLength - 1;
    } else {
      str += String.fromCharCode(_arr[i]);
    }
  }
  return str;
}

export const checkExpiration = (expiredAt: any) => {
  const now = new Date();
  const expirationDate = new Date(expiredAt);
  return now > expirationDate;
};

/**
 * 富文本解析成[图片][文字]
 * @param richContent
 * @returns
 */
export const getTipText = (richContent) => {
  let text = '';
  try {
    if (richContent) {
      const content = JSON.parse(richContent);
      for (const item of content) {
        if (item.insert !== '\n' && typeof item.insert !== 'object') {
          text += item.insert;
        }
        if (typeof item.insert === 'object' && item.insert.image) {
          text += '[图片]';
        }
      }
    }
  } catch (err) {
    text = '';
  }
  return text;
};
/**
 * 富文本解析成[图片][文字]
 * @param richContent
 * @returns
 */
export const getTipTextImage = (richContent) => {
  let imag = [];
  let text = [];
  try {
    if (richContent) {
      const content = JSON.parse(richContent);
      for (const item of content) {
        if (item.insert !== '\n' && typeof item.insert !== 'object') {
          text.push(item.insert);
        }
        if (typeof item.insert === 'object' && item.insert.image) {
          imag.push(item.insert.image);
        }
      }
    }
  } catch (err) {
    imag = [];
    text = [];
  }
  return [imag, text];
};

interface GenericObject {
  [key: string]: any;
}
// 去重函数，接受一个数组和一个用于去重的键
export const removeDuplicates = <T extends GenericObject>(arr: T[], key: keyof T): T[] => {
  if (!key) {
    console.error('没有传入指定的key');
    return;
  }
  const uniqueArr: T[] = [];
  const seen = new Set<T[keyof T]>();

  arr.forEach((item) => {
    const keyValue = item[key]; // 获取指定 key 的值
    if (!seen.has(keyValue)) {
      seen.add(keyValue);
      uniqueArr.push(item);
    }
  });

  return uniqueArr;
};
// const uniqueArr = removeDuplicates(arr, 'id');

/**
 * 复制图片
 * @param url
 * @returns
 */
export const copyImage = async (url) => {
  try {
    // 确保文档获得焦点
    window.focus();
    // 使用 fetch 获取图片数据
    const response = await fetch(url);
    let blob = await response.blob();
    console.log('====>blob', blob, url);
    if (blob.size > 8 * 1024 * 1024) {
      MessagePlugin.error('图片过大，复制失败，建议下载到本地。');
      return;
    }
    if (blob.type !== 'image/png') {
      // 使用 canvas 转换图片格式为 PNG
      const img = new Image();
      img.crossOrigin = 'anonymous';

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = URL.createObjectURL(blob);
      });

      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;

      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0);

      // 转换为 PNG 格式的 blob
      blob = await new Promise((resolve) => {
        canvas.toBlob(resolve, 'image/png');
      });
    }
    // 使用 blob 的原始类型
    const clipboardItem = new ClipboardItem({
      'image/png': blob,
    });
    window.focus();
    if (navigator.clipboard) {
      await navigator.clipboard.write([clipboardItem]);
      MessagePlugin.success(i18nt('im.msg.copied'));
    }
  } catch (error) {
    console.error('Copy image error:', error);
    MessagePlugin.error('复制失败,建议下载到本地。');
  }
};

export const getOrderChannel = (): OrderChannel => {
  if (__APP_ENV__.VITE_APP_MAS) return OrderChannel.Mac;
  if (isMacOS) return OrderChannel.MacNonMarket;
  if (isWindows) return OrderChannel.Windows;
}

export const addCommasToNumber = (str) => {
  let [integerPart, decimalPart] = str.split(".");
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  if (decimalPart) {
    decimalPart = decimalPart.length === 1 ? `${decimalPart}0` : decimalPart.slice(0, 2);
  } else {
    decimalPart = "00";
  }
  console.log(`${integerPart}.${decimalPart}`, "222222222222");

  return `${integerPart}.${decimalPart}`;
};
