<template>
  <StyledGradientDialog
    class="app-margin-record-dialog"
    title="保证金记录"
    :visible="visible"
    :footer="renderFooter"
    padding="0px 12px 12px 12px"
    :background-image="{
      src: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/h5/extapp_dialog_bg.png',
    }"
    @update:visible="updateVisible"
  >
    <div class="bg-white rounded-8 pl-12 py-12 pr-6 max-h-[412px] overflow-hidden">
      <r-table :table="tableConfig" />
    </div>
  </StyledGradientDialog>
</template>

<script setup lang="tsx">
import StyledGradientDialog from '@components/common/StyledGradientDialog.vue';
import { RTable } from '@rk/unitPark';
import { h, ref, VNode } from 'vue';
import external_app from '@pages/external-app/assets/external_app.svg';
import { MarginRecordItem, MarginRecordResponse } from '@pages/external-app/api';
import { addCommasToNumber } from '@utils/pay';

const visible = ref(false);

const currency = ref<'CNY' | 'MOP'>('CNY');
const total = ref(0.0);

const renderFooter = () => {
  return (
    <div class="flex items-center justify-between w-full h-56">
      <span class="text-14 text-#828DA5">总金额</span>

      <div class="text-#D54941 space-x-4">
        <span class="text-12 font-500">{currency.value === 'CNY' ? '¥' : 'MOP'}</span>
        <span class="text-16 font-600">{addCommasToNumber(total.value.toFixed(2))}</span>
      </div>
    </div>
  );
};

const tableConfig = ref<{
  attrs: {
    rowKey: 'id';
    maxHeight: 412;
  };
  columns: {
    title: string;
    width: number;
    cell: (_h: typeof h, { row }: { row: MarginRecordItem }) => VNode;
  }[];
  list: MarginRecordItem[];
  pagination: {
    pageSize: number;
    current: number;
    total: number;
  };
}>({
  attrs: {
    rowKey: 'id',
    maxHeight: 412,
  },
  columns: [
    {
      title: '应用信息',
      width: 280,
      cell: (_, { row }) => (
        <div class="flex items-center gap-8">
          <img class="w-36 h-36 object-cover rounded-12" src={row.picture_linking || external_app} alt="" />
          <div>{row.name || '未命名应用'}</div>
        </div>
      ),
    },
    {
      title: '保证金金额',
      width: 154,
      cell: (_, { row }) => (
        <div class="text-14 text-#1A2139">
          <span>{row.currency === 'CNY' ? '¥' : 'MOP'}</span>
          <span>{addCommasToNumber(row.bond_amount?.toFixed(2) || '0')}</span>
        </div>
      ),
    },
  ],
  list: [],
  pagination: {
    pageSize: 10,
    current: 1,
    total: 0,
  },
});

const updateVisible = (val: boolean) => {
  visible.value = val;
  if (!val) {
    tableConfig.value.list = [];
    total.value = 0;
    currency.value = 'CNY';
  }
};

defineExpose({
  show(data: MarginRecordResponse) {
    currency.value = data.currency as 'CNY' | 'MOP';
    total.value = data.bond_amount;
    tableConfig.value.list = data.items;
    visible.value = true;
  },
  hide() {
    visible.value = false;
  },
});
</script>

<style lang="less">
.app-margin-record-dialog.style-gradient-dialog .t-dialog__footer {
  height: 56px;
}

.app-margin-record-dialog.style-gradient-dialog .title-box {
  padding: 16px 12px;
}
</style>
