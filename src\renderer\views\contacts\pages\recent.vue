<template>
  <div class="cantainer">
    <div class="title">{{ t('contacts.recent') }}</div>
    <div v-if="empty" class="recent-empty">
      <img src="@renderer/assets/emptydata.png" alt="">
      <div class="tip">暂无数据</div>
    </div>
    <div v-else :class="['t-menu-container', isNotMac ? 'scrollbar' : '']">
      <t-menu theme="light" defaultValue="dashboard" style="width: 100%">
        <t-menu-item v-for="item in options" :key="item.peer" :value="item.peer" @mouseover="showAct(item)" @mouseleave="hiddenAct" @click="showCard(item)">
          <template #icon>
            <avatar class="avatar-icon" roundRadius :imageUrl="item.peerInfo.avatar" :userName="item.peerName" avatarSize="44px" />
          </template>
          <div class="user-info">
            <div class="user-name">
              <div class="ellipsis-1">{{ item.peerName }}</div>
              <RelationTag :relation="item.relation"/>
            </div>
            <div v-if="item.peerInfo?.departments?.length" class="post">{{ `${item.peerInfo.departments[0].name}` }}/{{ item.peerInfo.departments[0].jobName }}</div>
            <MultiIdTag :myCard="item.mainInfo" :anotherCard="item.peerInfo"></MultiIdTag>
          </div>
          <div class="act-groups" v-if="item.main + item.peer === hoverValue">
            <!-- item.relation === 'TEMPORARY' ||  -->
            <div v-if="!item.relation || item.has_del">
              <t-button variant="base" theme="primary">
                {{ t('contacts.addStatusAdd') }}
              </t-button>
            </div>
            <div v-else>
              <!-- <t-button class="mr-12" shape="circle" theme="primary" @click.stop="vioce(item)">
                <template #icon>
                  <t-tooltip :content="t('zx.contacts.voiceCall')" :show-arrow="false" placement="bottom">
                    <img src="@renderer/assets/svg/voicefill_new.svg" alt="">
                  </t-tooltip>
                </template>
              </t-button>
              <t-button class="mr-12" shape="circle" theme="primary" @click.stop="video(item)">
                <template #icon>
                  <t-tooltip :content="t('zx.contacts.videoCall')" :show-arrow="false" placement="bottom">
                    <img src="@renderer/assets/svg/videofill_new.svg" alt="">
                  </t-tooltip>
                </template>
              </t-button> -->
              <t-button shape="circle" theme="primary" @click.stop="msg(item)">
                <template #icon>
                  <t-tooltip :content="t('zx.contacts.msgCall')" :show-arrow="false" placement="bottom">
                    <img src="@renderer/assets/svg/commentfill_new.svg" alt="">
                  </t-tooltip>
                </template>
<!--                {{ t('contacts.msg') }}-->
              </t-button>
            </div>
          </div>
        </t-menu-item>
      </t-menu>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { recentContactList } from '@renderer/api/contacts/api/recent';
import { getOpenid, getCards, getStaff } from '@renderer/utils/auth';
import avatar from '@renderer/components/kyy-avatar/index.vue';
import { useI18n } from 'vue-i18n';
import { videoMsg, voiceMsg } from '../utils';
import { isNotMac } from '@renderer/views/zhixing/util';
import { openChat } from '@/utils/share';
import MultiIdTag from '@/components/contacts/MultiIdTag.vue';
import RelationTag from '@renderer/components/contacts/relationTag.vue'

import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;
const { t } = useI18n();
const options = ref([]);
const hoverValue = ref('');
const uuids = ref([]);
const empty = ref(false);

const getCradsInfo = () => {
  getCards().forEach(card => {
    uuids.value.push(card.uuid);
  })
  getStaff(true).forEach(staff => {
    uuids.value.push(staff.uuid);
  })
  getRecentList();
};

// 获取最近联系人列表
const getRecentList = () => {
  const params = {
    index: 0,
    size: 30,
    cardIds: uuids.value,
  }
  recentContactList(params).then((res) => {
    console.log(res, 'getRecentList');
    if (res.status === 200) {
      res.data.data.pairs?.length ? polyRencentList(res.data.data.pairs) : empty.value = true;
    }
  })
};

// 格式化最近联系人数据
const polyRencentList = async (data) => {
  const listData = []
  // 取消循环调用 跟app同步 取消好友标签展示 一直false
  const isCoWorker = false;
  data.forEach(val => {
    const peerInfo = {
      ...val.card,
      teamName: val.card.teamName,
      teamId: val.card.teamId,
      cardId: val.card.cardId,
      cardName: val.card.cardName,
    }
    const mainInfo = {
      ...val.myCard,
      teamName: val.myCard.teamName,
      teamId: val.myCard.teamId,
      cardId: val.myCard.cardId,
      cardName: val.myCard.cardName,
    }
    const renderData = {
        peerInfo,
        peerName: val.comment || val.card.cardName,
        describe: val.describe,
        mainInfo,
        relation: val.origin,
        coWorker: isCoWorker,
        team: val.team,
        peer: val.peer,
        main: val.main,
        has_del: val.hasDel,
      }
    listData.push(renderData)
  });
  options.value = listData;
  console.log('=====>',options.value);
};
const msg = (item) => {
  openChat({main: item.main, peer: item.peer});
};
const video = (item) => {
  videoMsg(item.peer, item.main);
};
const vioce = (item) => {
  voiceMsg(item.peer, item.main);
};
const showAct = (item) => {
  hoverValue.value = item.main + item.peer;
};
const hiddenAct = () => {
  hoverValue.value = '';
};
const showCard = (item) => {
  ipcRenderer.invoke("identity-card", { cardId: item.peer, myId: item.main });
};

const updateContactListener = (event, arg) => {
  getCradsInfo();
};

onMounted(() => {
  // 个人身份id
  uuids.value.push(getOpenid());
  getCradsInfo();
  ipcRenderer.on("update-contact-list", updateContactListener);
});

onUnmounted(() => {
  ipcRenderer.off("update-contact-list", updateContactListener);
});
</script>

<style lang="less" scoped>
.mr-12 {
  margin-right: 12px;
}
.cantainer {
  width: 100%;
  position: relative;
  .title {
    color: var(--text-kyy-color-text-1, #1A2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    margin:20px 24px 20px 24px;
  }
  .recent-empty {
    width: 100%;
    position: absolute;
    top: 64px;
    left: 8px;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 200px;
      height: 200px;
    }
    .tip{
      color: var(--text-kyy_color_text_2, #516082);

    }
  }
  .t-menu-container {
    width: calc(100% - 48px);
    position: absolute;
    top: 64px;
    left: 24px;
    bottom: 0;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  :deep(.t-default-menu__inner .t-menu) {
    padding: 0;
  }
  :deep(.t-default-menu:not(.t-menu--dark) .t-menu__item.t-is-active:not(.t-is-opened)) {
    background: transparent !important;
    color: #13161b !important;
    &:hover {
      background: #f0f8ff !important;
      border-radius: 4px;
    }
  }
  :deep(.t-default-menu .t-menu__item) {
    position: relative;
    font-size: 14px;

    color: #13161b;
    line-height: 22px;
    height: 68px !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA) !important;
    }
  }
  .avatar-icon {
    margin-right: 12px;
  }
  .user-info {
    .user-name {
      display: flex;
      align-items: flex-start;
    }
    .post {
      font-size: 12px;
      margin-top: 2px;
      color: #717376;
      line-height: 16px;
    }
  }
  .act-groups {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    .t-button {
      min-width: auto;
    }
    .t-button--shape-circle{
      border: none;
      background-color: transparent;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      img {
        width: 36px;
        height: 36px;
      }
    }
  }
}
</style>
