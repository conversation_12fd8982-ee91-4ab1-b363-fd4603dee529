<template>
  <div>

    <div class="tops">
      <!-- <div class="back pointer" @click="goBack">{{ t('account.back') }}</div> -->
    </div>
    <div class="info">
      <div class="top">
        <img src="@/assets/img/logo_img_logo.png" class="img">
        <span class="toTip">需绑定手机号加入组织</span>
        <span class="bindEmail">绑定邮箱：{{accountStore?.userInfo?.account_email}}</span>
      </div>
      <t-input-adornment>
        <template #prepend>
          <div>
            <area-code v-model="mobile.region" :disabled="false" />
          </div>
        </template>
        <t-input v-model="mobile.mobile" :placeholder="t('account.inputTel')" />
      </t-input-adornment>

      <t-input :maxlength="6" v-model="mobile.code" class="mt12" :placeholder="t('account.inputCode')">
        <template #suffix>
          <div style="display: flex; align-items: center">
            <div class="suffix--line" />
            <div
              v-if="countdown <= 0"
              class="verify pointer"
              aria-role="button"
              @click="checkSM"
            >{{ t('account.sendCode') }}</div>
            <div v-else class="verify">{{ `${t('account.resend')}${countdown}` }}</div>
          </div>
        </template>
      </t-input>
      <t-button
          v-loading="loginLoading"
          class="login-btn"
          :disabled="loginBtnDisabled"
          block
          theme="primary"
          variant="base"
          @click="confirm"
        > 确认绑定</t-button>
    </div>
  </div>
  <tip v-model:visible="tipVisible" :tip="t('zx.account.alreadyBindPhone')" btn-confirm="知道了" @onconfirm="tipVisible = false" />
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import tip from '../tipCommon.vue';
import {MessagePlugin} from 'tdesign-vue-next';
import areaCode from '@/components/keeppx/account/AreaCode.vue';
import { err_reason, checkPhoneAndMatch } from '../util';
import {
  getIdentifyCode,
} from '@/api/account/login';
import { useI18n } from 'vue-i18n';

import { useAccountStore } from '@/stores/account';
import { getSMCaptcha, getSMCaptchaResult } from '@/utils/shumei';
const accountStore = useAccountStore();


const { t } = useI18n();
const mobile = ref({
  region: '86',
  mobile: '',
  code: '',
});
const countdown = ref(0);
const tipVisible = ref(false);
const loginLoading = ref(false);
const loginBtnDisabled = ref(true);
let timer:any = null;

const emits = defineEmits(['back', 'confirm']);
const goBack = () => {
  clear();
  emits('back');
};
const clear = () => {
  mobile.value = {
    region: '86',
    mobile: '',
    code: '',
  }
  loginBtnDisabled.value = true;
  countdown.value = 0;
};
const confirm = () => {
  checkPhone() && emits('confirm', mobile.value);
};
const clearCount = () => {
  countdown.value = 0;
};
const checkPhone = () => {
  const checkRegion = checkPhoneAndMatch(+mobile.value.region, mobile.value.mobile, false);
  if (!checkRegion) {
    MessagePlugin.error({
      content: t('zx.account.phoneIllegal'),
      duration: 3000,
    });
    return false;
  }
  return true;
};
const getCode = (data?) => {
  if (checkPhone()) {
    const params = {
      typ: 'ACCOUNT_CHANGE',
      mobile: {
        mobile: mobile.value.mobile,
        region: mobile.value.region,
      },
    };
    if (data) {
      params.captcha = {
        code: data?.rid,
        mode: 'slide',
      };
    }
    getIdentifyCode(params).then((res: any) => {
      console.log(res)
      if (res && res.retry_after) {
        countdown.value = res.retry_after;
        timer = setInterval(() => {
          countdown.value--;
          if (countdown.value <= 0) {
            clearInterval(timer);
            timer = null;
          }
        }, 1000);
      }
      console.log(res, 'getIdentifyCode');
    }).catch(err => {
      console.log(err)
      err = err.response;
      const reason = err.data.reason;
      // y用户已注册特殊处理
      if (reason === 'USER_EXISTED') {
        tipVisible.value = true;
        return;
      }
      MessagePlugin.error({
        content: err_reason[reason] || t('zx.other.requestFail'),
        duration: 3000,
      });
    });
  }
};
watch(mobile, (newValue) => {
  if (newValue.mobile && newValue.code) {
    loginBtnDisabled.value = false;
  } else {
    loginBtnDisabled.value = true;
  }
}, {
  deep: true,
})
defineExpose({
  clearCount,
});

const checkSM = () => {
  if (!SMCaptcha.value) {
    return;
  }
  getSMCaptchaResult(SMCaptcha.value, getCode);
};

const SMCaptcha = ref(null);
onMounted(async () => {
  try {
    SMCaptcha.value = await getSMCaptcha({ width: 300 });
    console.error(SMCaptcha.value);
  } catch (error) {
    console.error(error);
  }
});
</script>

<style lang="less" scoped>
.mt12 {
  margin-top: 12px;
}
.info {
  width: 320px;
  // margin: 80px auto 0;
  :deep(.t-input) {
    height: 40px;
  }
  :deep(.t-input-adornment__prepend) {
    background: #fff;
  }
  .title {
    font-size: 20px;
    font-family: Microsoft YaHei, Microsoft YaHei-Bold;
    font-weight: 700;
    text-align: left;
    color: #13161b;
    line-height: 28px;
    margin-bottom: 32px;
  }
  .login-btn {
    margin-top: 16px;
    height: 40px;
    font-size: 16px;
    font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
    color: #ffffff;
    line-height: 24px;
  }
}
.pointer {
  cursor: pointer;
}
.top {
  width: 320px;
 //  margin:41px auto 0;
  // padding-left: 15px;

}
.back {
  font-size: 14px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
  color: #717376;
  line-height: 22px;
  position: relative;
  &:before {
    content: '';
    width: 8px;
    height: 8px;
    border-top: 1px solid #1A2139;
    border-left: 1px solid #1A2139;
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%) rotate(-45deg);
  }
}
.suffix--line {
  width: 1px;
  height: 24px;
  background-color: #f6f6f6;
  margin-right: 16px;
}
.verify {
  font-size: 14px;
  font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
  color: #2069e3;
  line-height: 22px;
}
.tops {
  display: flex;
  padding: 8px 10px;
  .back {
    color: #1A2139;
  }
}
.top {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 0;
  .img {
    width: 80px;
    height: 80px;
    border-radius: 12px;
  }
  .toTip {
    margin-top: 16px;
    color: black;
    color: var(--text-kyy_color_text_1, #1A2139);
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px; /* 144.444% */
  }
  .bindEmail {
    margin-top: 4px;
    color: var(--text-kyy_color_text_3, #828DA5);
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px; /* 152.941% */
  }
}
</style>
