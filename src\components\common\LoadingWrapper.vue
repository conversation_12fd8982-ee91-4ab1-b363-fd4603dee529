<template>
  <div class="loading-container">
    <div class="loader"></div>
    <div class="loading-text">加载中...</div>
  </div>
</template>

<style lang="less" scoped>
.loading-container {
  position: fixed;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: center;
  align-items: center;
  /* background-color: rgba(255, 255, 255, .7); */
  background-color: #fff;
}

.loader {
  width: 28px;
  padding: 8px;
  aspect-ratio: 1;
  border-radius: 50%;
  background: #4c5eff;
  --_m: conic-gradient(#0000 10%, #000), linear-gradient(#000 0 0) content-box;
  -webkit-mask: var(--_m);
  mask: var(--_m);
  -webkit-mask-composite: source-out;
  mask-composite: subtract;
  animation: l3 1s infinite linear;
}

@keyframes l3 {
  to {
    transform: rotate(1turn);
  }
}

.loader-text {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  text-align: left;
}
</style>
