<template>
  <div class="F-BaseInfoMember">
    <!-- {{ attrs }} -->
    <!-- <div v-if="attrs.fromType ==='person'" class="person"> -->

    <div v-for="(attr, attrIndex) in attrs.origin" :key="attrIndex">
      <div
        v-if="attr.vModel === 'divider' && attr.isShow"
        class="detail-control"
        style="width: 100%"
      >
        <div :class="{ lable: true, border: attrs.lineType === 'border' }">
          <span class="line" />
          <span class="text">{{ attr.name }}</span>
        </div>
        <!-- <div v-for="(item, index) in attrs.value" :key="index" class="value">
            {{ item.name }}
          </div> -->
      </div>
      <!-- organizeName -->

      <t-form-item
        v-if="
          [

            'name',
            'reference',
            'referenceUnit',
            'unitJob',
            'memberNum',

            'typePhone',
            'typeCompany',
            'typePosition',
            'typeCustom'
          ].includes(attr.vModel) && attr.isShow
        "
        :rules="
          attr.required
            ? [
              {
                message: `${attr.name}${$t('member.bolit.c')}`,
                validator: () => validator(attr),
                required: attr.required,
                trigger: 'blur'
              }
            ]
            : null
        "
        :name="attr.id"
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <t-input
          v-model="attr.value"
          :placeholder="attr.placeholder"
          :disabled="attr.disabled"
          :maxlength="attr?.maxlength ? attr.maxlength: 50"
        />
      </t-form-item>

      <t-form-item
        v-if="['interest', 'business'].includes(attr.vModel) && attr.isShow"
        :rules="
          attr.required
            ? [
              {
                message: `${attr.name}${$t('member.bolit.c')}`,
                validator: () => validator(attr),
                required: attr.required,
                trigger: 'blur'
              }
            ]
            : null
        "
        :name="attr.id"
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <!-- <t-input
          v-model="attr.value"
          :placeholder="attr.placeholder"
          :disabled="attr.disabled"
          :maxlength="50"
        /> -->
        <t-textarea
          v-model="attr.value"
          :placeholder="attr.placeholder"
          :disabled="attr.disabled"
          :maxlength="attr?.maxLength || 200"
          autosize
        />
        <!-- @change="changeValue" -->
      </t-form-item>




      <t-form-item
        v-if="['email'].includes(attr.vModel) && attr.isShow"
        :rules="[
          {
            message: `${attr.name}${$t('member.bolit.c')}`,
            validator: () => validatorEmail(attr),
            required: attr.required,
            trigger: 'blur'
          }
        ]"
        :name="attr.id"
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <t-input
          v-model="attr.value"
          :placeholder="attr.placeholder"
          :disabled="attr.disabled"
          :maxlength="50"
        />
      </t-form-item>

      <t-form-item
        v-if="['organizeAbbrName'].includes(attr.vModel) && attr.isShow"
        :rules="
          attr.required
            ? [
              {
                message: `${attr.name}${$t('member.bolit.c')}`,
                validator: () => validator(attr),
                required: attr.required,
                trigger: 'blur'
              }
            ]
            : null
        "
        :name="attr.id"
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <t-input
          v-model="attr.value"
          :placeholder="attr.placeholder"
          :maxlength="6"
        />
      </t-form-item>
      <t-form-item
        v-else-if="['departs'].includes(attr.vModel) && attr.isShow"
        :name="attr.id"
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <!-- readonly
          @click="onSelectOrganize(attr)" -->
        <div class="departs">
          <t-input
            v-for="(item, index) in attr.value" :key="index"
            :value="item.dep+'/'+item.job"
            class="departs-input"
            :placeholder="attr.placeholder"
            :maxlength="100"
            :disabled="attr.disabled"
          />
        </div>
          <!-- <template #suffixIcon>
            <svg class="iconpark-icon icon16" style="color: #a1a2a4">
              <use href="#down" />
            </svg>
          </template> -->
      </t-form-item>
      <t-form-item
        v-else-if="['sex'].includes(attr.vModel) && attr.isShow"
        :rules="
          attr.required
            ? [
              {
                message: `${attr.name}${$t('member.bolit.c')}`,
                validator: () => validator(attr),
                required: attr.required,
                trigger: 'blur'
              }
            ]
            : null
        "
        :name="attr.id"
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <t-radio-group v-model="attr.value">
          <t-radio
            v-for="(item, index) in attr.options"
            :key="index"
            :value="item.value"
          >
            {{ item.label }}
          </t-radio>
        </t-radio-group>
      </t-form-item>
      <t-form-item
        v-else-if="['organizeName'].includes(attr.vModel) && attr.isShow"
        :rules="
          attr.required
            ? [
              {
                message: `${attr.name}${$t('member.bolit.c')}`,
                validator: () => validator(attr),
                required: attr.required,
                trigger: 'blur'
              }
            ]
            : null
        "
        :name="attr.id"
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <!-- readonly
          @click="onSelectOrganize(attr)" -->
        <t-input
          v-model="attr.value"
          :placeholder="attr.placeholder"
          :maxlength="50"
        >
          <!-- <template #suffixIcon>
            <svg class="iconpark-icon icon16" style="color: #a1a2a4">
              <use href="#down" />
            </svg>
          </template> -->
        </t-input>
      </t-form-item>

      <t-form-item
        v-else-if="attr.vModel === 'memberLevel' && attr.isShow"
        :name="attr.id"
        :rules="
          attr.required
            ? [
              {
                validator: () => validator(attr),
                required: attr.required,
                trigger: 'blur'
              }
            ]
            : null
        "
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <div class="selectFormItem">
          <t-select v-replace-svg

            v-model="attr.value"
            :keys="{ label: 'level_name', value: 'id' }"
            :options="attr.options"
            clearable
            @change="(e)=> onChangeMemberLevel(e, attr)"
          >
            <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" >
            </template>
          </t-select>
          <div
            v-if="attr.options && attr.options.length > 0"
            v-show="attr.options.find((v:any)=>v.id===attr.value)"
            class="fee"
          >
            <span class="fee-label">会费（{{
              attr.options.find((v: any) => v.id === attr.value)?.currency
            }}）：</span>
            <span class="fee-value">{{
              priceDivisorShow(
                attr.options.find((v: any) => v.id === attr.value)?.money,
                100
              )
            }}</span>
          </div>
        </div>
      </t-form-item>

      <t-form-item
        v-else-if="attr.vModel === 'phone' && attr.isShow"
        :rules="
          attr.required
            ? [
              {
                validator: () => validatorPhone(attr),
                required: attr.required,
                trigger: 'blur'
              }
            ]
            : null
        "
        :name="attr.id"
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <div class="selectPhoneItem">
          <t-input-adornment class="selectPhoneItem-phone">
            <template #prepend>
              <t-select v-replace-svg
                v-model="attr.code_value"
                :options="attr.options"
                :disabled="attr.disabled"
                style="width: 84px"
              />
            </template>
            <t-input
              v-model="attr.value"
              :placeholder="attr.placeholder"
              :maxlength="30"
              :disabled="attr.disabled"
              clearable
            />
          </t-input-adornment>
        </div>
      </t-form-item>
      <FPhone v-else-if="['multipPhone'].includes(attr.vModel)&& attr.isShow" :attrs="attr" />

      <CImageUploadComp
        v-else-if="['organizeLogo', 'logo', 'nameLogo'].includes(attr.vModel)&& attr.isShow && isShowPosition"
        :attrs="attr"
        :multiple="false"
      />

      <CImageUploadComp v-else-if="['typeLogo'].includes(attr.vModel)&& attr.isShow && isShowPosition" :noCropper="true"  :attrs="attr"
        :multiple="false"/>
      <!-- <CCrapperImageUploadComp
        v-else-if="['nameLogo'].includes(attr.vModel)&& isShowPosition"
        :attrs="attr"
        :multiple="false"
      /> -->
      <t-form-item
        v-else-if="attr.vModel === 'industryType' && attr.isShow"
        :name="attr.id"
        :rules="
          attr.required
            ? [
              {
                validator: () => validatorNumber(attr),
                required: attr.required,
                trigger: 'blur'
              }
            ]
            : null
        "
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <t-cascader
          v-replace-svg
          v-model="attr.value"
          :options="attr.options"
          clearable
          :keys="{ label: 'name', value: 'id' }"
        />
      </t-form-item>

      <t-form-item
        v-else-if="attr.vModel === 'organizeScale' && attr.isShow"
        :name="attr.id"
        :rules="
          attr.required
            ? [
              {
                validator: () => validatorNumber(attr),
                required: attr.required,
                trigger: 'blur'
              }
            ]
            : null
        "
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <t-select
          v-model="attr.value"
          :options="attr.options"
          clearable
          :keys="{ label: 'name', value: 'id' }"
        >

        <template #suffixIcon>
          <img src="@/assets/svg/icon_arrow_down.svg" >
        </template>
        </t-select>

      </t-form-item>
      <t-form-item
        v-else-if="attr.vModel === 'organizeType' && attr.isShow"
        :name="attr.id"
        :rules="
          attr.required
            ? [
              {
                validator: () => validatorNumber(attr),
                required: attr.required,
                trigger: 'blur'
              }
            ]
            : null
        "
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>
        <t-select v-replace-svg  v-model="attr.value" :options="attr.options" clearable >
          <template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" >
          </template>
        </t-select>
      </t-form-item>
      <CAddressComp
        v-else-if="attr.vModel === 'organizeAddress' && attr.isShow"
        :attrs="attr"
      />
      <CRadioComp
        v-else-if="attr.vModel === 'relateRespector' && attr.isShow"
        :attrs="attr"
        :origin="attrs.origin"
        @reset-valid="resetValid"
        @controls-event="onSelectOrganize"
      />
      <t-form-item
        v-else-if="attr.vModel === 'joinTime' && attr.isShow"
        :name="attr.id"
        :rules="[
          {
            validator: () => validatorDater(attr),
            required: attr.required,
            trigger: 'blur'
          }
        ]"
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>

        <t-date-picker
          v-model="attr.value"
          :placeholder="attr.placeholder"
          :maxlength="500"
          :format="'YYYY-MM-DD'"
          style="width: 100%"
          clearable
          @change="
            (value, context) =>
              changeDatePicker(value, context, attr, attrs.origin)
          "
          @blur="
            (value, context) =>
              changeDatePicker(value, context, attr, attrs.origin)
          "
        />
      </t-form-item>

      <t-form-item  v-else-if="['electronicCard', 'contactInfo'].includes(attr.vModel) && attr.isShow">
        <template #label>
          <span class="label">
            {{attr.name}}
            <!-- <template v-if="attr.name">({{ attr.name }})</template> -->
            <t-tooltip placement="top">
              <template #content>{{attr?.tip}}</template>
              <iconpark-icon v-if="attr?.tip" name="iconhelp" class="iconhelp"></iconpark-icon>
            </t-tooltip>
          </span>
        </template>
        <t-switch v-model="attr.value"/>
      </t-form-item>

      <t-form-item  v-else-if="['tagPerson'].includes(attr.vModel) && attr.isShow">
        <template #label>
          <span class="label">
            个人标签
            <template v-if="attr.name">({{ attr.name }})</template>
            <t-tooltip placement="top">
              <template #content>{{attr?.tip}}</template>
              <iconpark-icon v-if="attr?.tip" name="iconhelp" class="iconhelp"></iconpark-icon>
            </t-tooltip>
          </span>
        </template>
        <div class="tagList">
          <template v-if="!attr.value">
            <SetTagPopup :placement="'bottom-left'" :tagType="TagType.Search" @onSelect="(val)=>onSelectTag(val, attr)">
              <div class="add cursor">
                <iconpark-icon class="iconadd" name="iconadd"></iconpark-icon>
                <span class="addText">添加</span>
              </div>
            </SetTagPopup>
          </template>
          <span class="tag" v-if="attr.value" :style="{'background-color': attr.value?.bgColor, color: attr.value?.color}">
            {{ attr.value?.name }}
            <iconpark-icon name="iconerror" @click.stop="removePerTag(attr)" class="iconerror cursor" :style="{'color': attr.value?.color}"></iconpark-icon>
          </span>
        </div>
      </t-form-item>
      <t-form-item  v-else-if="['tagPlatform'].includes(attr.vModel) && attr.isShow">
        <template #label>
          <span class="label">
            {{attr?.typeName || '平台标签'}}
            <template v-if="attr.name">({{ attr.name }})</template>
            <!-- <t-tooltip placement="top">
              <template #content>{{attr?.tip}}</template>
              <iconpark-icon attr?.tip name="iconhelp" class="iconhelp"></iconpark-icon>
            </t-tooltip> -->
          </span>
        </template>

        <t-select
          v-model="attr.value"
          v-replace-svg
          placeholder="请选择"
          clearable
          @change="changeSelect"
          class="optTagSelect"
        >
          <template #valueDisplay="{value}">
            <div v-if="value" class="active"
              :style="{
                'color': attr?.options?.find(v=>v.value_id === value)?.color,
                'background-color': attr?.options?.find(v=>v.value_id === value)?.bgColor
              }"
            >
              {{attr?.options?.find(v=>v.value_id === value)?.name }}
            </div>
            <div v-else class="defaultValue">
              请选择
            </div>

          </template>

          <template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" >
          </template>

          <!-- <template #content>卡卡下地方</template> -->
          <t-option
            v-for="item in attr?.options"
            :key="item?.value_id"
            :value="item?.value_id"
            :label="item?.name"
            class="opt"
          >
            <template #content>
              <div class="con">
                <span class="con-icon" :style="{'background-color': switchColor(item?.colour)?.color}"></span>
                <span class="con-text">{{ item?.name }}</span>
              </div>
            </template>
          </t-option>
        </t-select>
      </t-form-item>
      <t-form-item  v-else-if="['titleT'].includes(attr.vModel) && attr.isShow">
        <template #label>
          <span class="label">
            {{ attr.name }}
            <!-- <t-tooltip placement="top">
              <template #content>{{attr?.tip}}</template>
              <iconpark-icon name="iconhelp" class="iconhelp"></iconpark-icon>
            </t-tooltip> -->
          </span>
        </template>
        <div class="tagList">
          <SetTitlePopup :origin="originType.Politics" placement="bottom-left" :isHandleSave="false" :arrV="attr.value" @onSaveTitle="(val)=>onSaveTitle(val, attr)">
            <div class="add cursor" v-show="attr.value?.length < 5">
              <iconpark-icon class="iconadd" name="iconadd"></iconpark-icon>
              <span class="addText">添加</span>
            </div>
          </SetTitlePopup>
          <template v-if="attr.value">
            <span class="tag"  v-for="(it, itIndex) in attr.value"  :style="{'background-color': '#ECEFF5', color: '#516082'}">
              {{ it?.name }}
              <iconpark-icon name="iconerror" @click.stop="removeTitle(it, attr)" class="iconerror cursor" :style="{'color': '#516082'}"></iconpark-icon>
            </span>
          </template>
        </div>
      </t-form-item>

      <t-form-item
        v-else-if="attr.vModel === 'expireTime' && attr.isShow"
        :name="attr.id"
        :rules="[
          {
            validator: () => validatorDaterExpire(attr),
            required: attr.required,
            trigger: 'blur'
          }
        ]"
      >
        <template #label>
          <span style="flex: 1">{{ attr.name }}</span>
        </template>

        <t-date-picker
          v-model="attr.value"
          :placeholder="attr.placeholder"
          :maxlength="500"
          :format="'YYYY-MM-DD'"
          style="width: 100%;margin-right: 16px;"
          clearable
          :disabled="attr.is_expire_value || attr.disabled"
          @change="
            (value, context) =>
              changeDatePicker(value, context, attr, attrs.origin)
          "
          @blur="
            (value, context) =>
              changeDatePicker(value, context, attr, attrs.origin)
          "
        />
        <t-checkbox
          v-model="attr.is_expire_value"
          class="w-120px"
          @change="changeDatePickerExpire(attr, attrs.origin)"
        >长期有效
        </t-checkbox>
      </t-form-item>

      <div class="definePeriod"  v-if="
          ['definePeriod'].includes(attr.vModel) && attr.isShow
        ">
        <iconpark-icon name="icondrag" class="move cursor definePeriod-icondrag"></iconpark-icon>
        <t-form-item
          class="definePeriod-item"
          style="margin-bottom: 0 !important;"
          :rules="
            attr.required
              ? [
                {
                  message: `${attr.placeholder}`,
                  validator: () => validator(attr),
                  required: attr.required,
                  trigger: 'blur'
                }
              ]
              : null
          "
          :name="attr.id"
        >
          <template #label>
            <!-- <span style="flex: 1">{{ attr.name }}</span> -->
            <t-input
              v-model="attr.name"
              :placeholder="attr.typeName"
              :disabled="attr.disabled"
              :maxlength="50"
            />
          </template>
          <t-input
            v-model="attr.value"
            :placeholder="attr.placeholder"
            :disabled="attr.disabled"
            :maxlength="attr?.maxlength ? attr.maxlength: 50"
          />
        </t-form-item>
        <iconpark-icon name="icondelete" class="cursor definePeriod-icondelete"></iconpark-icon>
      </div>
      

    </div>
    <!-- </div> -->
  </div>
</template>

<script setup lang="ts" name="F-BaseInfoMember">
import { ref, onMounted } from "vue";
import lodash from "lodash";
import { useI18n } from "vue-i18n";
import {
  getOrganizeDepartmentListAxios,
  getOrganizeDepartmentJobsListAxios
} from "@renderer/api/member/api/businessApi";
import { v4 as uuidv4 } from "uuid";
import { MessagePlugin } from "tdesign-vue-next";
import {
  priceDivisorShow,
  checkPhoneNumber,
  getResponseResult
} from "@/utils/myUtils";
// import CImageNocaijianUploadComp from "@/components/free-from/runtime/components/CImageUpload.vue";
// import CImageNocaijianUploadComp from "@/components/free-from/runtime/components/AvatarImageNocaijianUpload.vue";

import CImageUploadComp from "@/components/free-from/runtime/components/AvatarImageUpload.vue";
import CCrapperImageUploadComp from "@/components/free-from/runtime/components/AvatarImageUploadCrapper.vue";
import SetTagPopup from '@renderer/views/digital-platform/modal/set-tag-popup.vue'
import FPhone from "@renderer/components/free-from/runtime/controls/FPhoneMultip.vue";

import { TagColors, TagType } from '@renderer/views/digital-platform/utils/constant'
import SetTitlePopup from '@renderer/views/digital-platform/modal/set-title-popup.vue';
import { originType } from "@renderer/views/digital-platform/utils/constant";


// import CImageUploadComp from "@/components/free-from/runtime/controls/FImageUpload.vue";
import CAddressComp from "@/components/free-from/runtime/components/CAddress.vue";
import CRadioComp from "@/components/free-from/runtime/components/CRadio.vue";
const isShowPosition = ref(false);
const { t } = useI18n();
const props = defineProps({
  attrs: {
    type: Object,
    default: () => {}
  }
});
const treeProps = {
  keys: {
    label: "name",
    value: "id",
    children: "children"
  }
};
const emits = defineEmits(["controls-event", "resetValid"]);

const attrs = ref(props.attrs);
// const controlEvent = (e) => {
//   emits("controls-event", e);
// };

// 姓名的校验、会员级别的校验
const validator = (attr: any) => {
  console.log("ddd");
  console.log(attr);
  if (!attr.value || attr.value?.length === 0) {
    return {
      message: `${attr.name}${t('member.bolit.c')}`,
      required: true,
      trigger: "blur"
    };
  }
  if (attr.vModel === 'typePhone') {
    const reg = /^(86)?(1[3-9]\d)\d{8}$/;
    if (!reg.test(attr.value)) {
      return {
        message: `请输入正确的手机号码`,
        trigger: "blur"
      };
    }
  }
  return { result: true, message: "", type: "success" };
};

const validatorDepartments = (attr: any) => {
  console.log("ddd");
  if (attr.position.length === 0) {
    return {
      message: `${attr.name}${t('member.bolit.c')}`,
      required: true,
      trigger: "blur"
    };
  }
  if (!attr.position.some((v) => v.departmentId)) {
    return {
      message: "请至少选择一个部门",
      required: true,
      trigger: "blur"
    };
  }

  return { result: true, message: "", type: "success" };
};

// formData.value.position.some((v) => v.departmentId

const validatorNumber = (attr: any) => {
  console.log(attr.value, "ddd", attr.name);
  if (!lodash.isNumber(attr.value)) {
    return {
      message: `${attr.name}${t('member.bolit.c')}`,
      required: true,
      trigger: "blur"
    };
  }
  return { result: true, message: "", type: "success" };
};
const validatorDater = (attr: any) => {
  console.log(attr.value, "ddd", attr.name);
  if (!attr.value) {
    return {
      message: `${attr.name}${t('member.bolit.c')}`,
      required: true,
      trigger: "blur"
    };
  }
  return { result: true, message: "", type: "success" };
};

// 到期
const validatorDaterExpire = (attr: any) => {
  console.log(attr.value, "ddd", attr.name);
  if (!(attr.value || attr.is_expire_value)) {
    return {
      message: `${attr.name}${t('member.bolit.c')}`,
      required: true,
      trigger: "blur"
    };
  }
  return { result: true, message: "", type: "success" };
};

// const validatorNumberIncludeZero = (attr: any)=> {

//  console.log(attr.value, 'ddd', attr.name)
//  if ( !lodash.isNumber(attr.value) ) {
//    return {
//      message: `${attr.name}不能为空`,
//      required: true,
//      trigger: "blur"
//    };
//  }
//  return { result: true, message: "", type: "success" };
// }

// 手机号校验 checkPhoneNumber
const validatorPhone = (attr: any) => {
  if (!checkPhoneNumber(attr.code_value, attr.value)) {
    return {
      message: `请输入正确的手机号码`,
      required: true,
      trigger: "blur"
    };
  }
  return { result: true, message: "", type: "success" };
};

const validateEmailRegx = (email) => {
  console.log("email", email);

  // 正则表达式用来校验邮箱格式
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  // 使用正则表达式进行匹配
  return regex.test(email);
};

const validatorEmail = (attr: any) => {
  console.log("email", attr);
  // if (attr.value === 0) {
  //   return {
  //     message: t("free_form.emi1"),
  //     required: true,
  //     trigger: "blur"
  //   };
  // }

  // if (attr.value.length > 0) {
  //   return {
  //     message: t("free_form.emi1"),
  //     required: true,
  //     trigger: "blur"
  //   };
  // }

  const isValidEmail = validateEmailRegx(attr.value);
  if (!isValidEmail && attr.value) {
    return {
      message: t("free_form.emfm", { email: attr.value }),
      required: true,
      trigger: "blur"
    };
  }

  return { result: true, message: "", type: "success" };
};


onMounted(() => {
  setTimeout(() => {
    isShowPosition.value = true;

  }, 500);
});


const switchColor = (intColor)=> {
  return TagColors.find((item) => item.intColor == intColor)
}


const onSelectTag = (tag, attrV) => {
  console.log(tag)
  // formData.person_tag = tag?.value_id;

  const colorObj = switchColor(tag?.colour);
  // formData.person_tag_obj = {
  //   ...colorObj,
  //   ...tag
  // };
  attrV.value = {
    ...colorObj,
    ...tag
  };
}

const onSaveTitle = (tag, attrV) => {
  attrV.value = tag;
}



const removePerTag = (attr) => {
  attr.value = null;
}

const removeTitle = (val, attr) => {
  attr.value = attr.value.filter((v) => v.value_id!== val.value_id);
}




// 监听会员级别选择
const onChangeMemberLevel = (e, attr) => {
  attr.isShow = false;
  setTimeout(()=> {
    attr.isShow = true;
  })
};

// 点击选择组织
const onSelectOrganize = (item: any) => {
  console.log(item);
  // eslint-disable-next-line vue/custom-event-name-casing
  emits("controls-event", item);
};

const resetValid = (arr: Array<any>) => {
  emits("resetValid", arr);
};

const changeDatePicker = (value, context, item, origin: any) => {
  if (context?.trigger === "clear") {
  }
  emits("controls-event", [item, origin]);
};

const changeDatePickerExpire = (item, origin) => {
  emits("controls-event", [item, origin]);
};

const onAddPosition = (positions) => {
  // console.log(positions, ar);
  isShowPosition.value = false;
  positions.push({
    departmentId: undefined,
    jobId: undefined,
    uuid: uuidv4()
  });
  // ar.positions = positions;
  setTimeout(() => {
    isShowPosition.value = true;
  }, 0);
};

const onDelPosition = (position, index) => {
  // isShowPosition.value = false;
  position.splice(index, 1);
};

// 选择部门，联动岗位
const onSelectDepartment = (e, position, attr) => {
  console.log(e);
  console.log(attr);
  // 1.清空岗位
  position.jobId = undefined;
  position.jobDatas = []; // 新增

  // 1.4注释
  // if (e) {
  //   // 2.重新获取数据
  //   getJobListData(e, position);
  // } else {
  //   position.jobDatas = [];
  // }
  emits("controls-event", attr);
};

// 获取岗位列表
// const jobDatas = ref([]);
const getJobListData = (departmentID, position) => {
  const params = {
    departmentId: departmentID
  };
  let res = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      res = await getOrganizeDepartmentJobsListAxios(params);
      res = getResponseResult(res);
      if (!res) {
        reject();
        return;
      }
      console.log(res);
      // position.jobDatas = res.data;
      const { jobs } = res.data[0];

      position.jobDatas =
        jobs && jobs.length > 0 ? jobs.filter((v) => v.status !== 0) : [];
      resolve(res.data);
    } catch (error) {
      MessagePlugin.error(error.message);
      reject();
    }
  });
};
</script>

<style lang="less" scoped>
@import url("@renderer/components/free-from/style/public.less");

.definePeriod {
  margin-bottom: 16px;
  display: flex;
  // align-items: center;
  // gap: 16px;
  padding: 12px;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  gap: 8px;
  align-items: flex-start;
  // :deep(.t-form:not(.t-form-inline) .t-form__item:last-of-type) {
  //   margin-bottom: 0 !important;
  // }
  &-item {
    flex: 1;
    width: 100%;
    // margin-bottom: 0!important;
  }
  &-icondrag {
    color: #828DA5;
    font-size: 20px;
    margin-top: 3px;
    // margin-right: 12px;
  }
  &-icondelete {
    color: #828DA5;
    font-size: 20px;
    margin-top: 3px;
  }
}

.departs {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 8px;
  &-input {
    width: 100%;
    flex: 1;
  }
}
.defaultValue {
  color: var(--input-kyy_color_input_text_default, #ACB3C0);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.optTagSelect {
  height: 32px;
  .active {
    padding: 0 8px;
    // margin-left: 6px;
    border-radius: 4px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    // margin: 4px 0;
  }

}


.tagList {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  .add {
    color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    height: 24px;
    padding-left: 8px;
    padding-right: 12px;
    gap: 4px;

    border-radius: var(--radius-kyy_radius_button_s, 4px);
    border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);

    .iconadd {
      color: #828DA5;
      font-size: 20px;
    }

  }
  .tag {
    border-radius: var(--kyy_radius_tag_s, 4px);
    padding: 0 4px;
    height: 20px;
    line-height: 20px; /* 166.667% */
    display: flex;
    align-items: center;
    gap: 4px;
    .iconerror {
      font-size: 20px;
    }
  }
}

.iconhelp {
  font-size: 20px;
}

.label {
  display: flex;
  align-items: center;
  gap: 4px;
}

:deep(.t-input__extra) {
  display: relative;
}

:deep(.t-input-adornment) {
  width: 100%;
}

// :deep(.t-form__item) {
//   margin-bottom: 12px !important;
// }

.selectFormItem {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.selectPhoneItem {
  display: flex;
  width: 100%;
  &-code {
    width: 25% !important;
  }
  &-phone {
    width: 75% !important;
  }
}
.fee {
  display: flex;
  &-label {
    font-size: 12px;

    font-weight: 400;
    text-align: left;
    color: #717376;
  }
  &-value {
    font-size: 12px;

    font-weight: 400;
    color: #2069e3;
  }
}

.border {
  padding-bottom: 8px;
  border-bottom: 1px solid #e3e6eb;
}
.detail-control {
  margin-top: 16px;
  margin-bottom: 4px;
  .lable {
    display: flex;
    align-items: center;

    // &::before {
    //   content: " ";
    //   width: 2px;
    //   height: 14px;
    //   background: #2069e3;
    //   border-radius: 2px;
    //   // position: absolute;
    //   left: 0;
    //   top: 2px;
    // }
    .line {
      width: 2px;
      height: 14px;
      background: var(--brand-kyy-color-brand-default, #4d5eff);

      border-radius: 2px;
      margin-right: 10px;
    }
    .text {
      font-size: 14px;

      font-weight: 700;
      text-align: left;
      color: #13161b;
      height: 22px;
      line-height: 24px;
    }
  }
  .value {
    width: 100%;
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: #13161b;
    margin-top: 4px;
  }
}

.depart {
  width: 100%;
  .departItem {

    display: flex;
    align-items: center;
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }

    .select {
      flex: 1;

      margin-left: 8px;
      &:first-child {
        margin-left: 0;
      }
    }
    .iconAdd {
      color: #2069e3;
      height: 20px;
      width: 20px;
      margin-left: 16px;
      cursor: pointer;
    }
    .iconDel {
      color: #da2d19;
      height: 20px;
      width: 20px;
      margin-left: 16px;
      cursor: pointer;
    }
  }
}

:deep(.add) {
  width: 78px;
  height: 78px;;
}
:deep(.form-id-12) .img-list img {
  width: 78px;
  height: 78px;;
}

:deep(.tagList) {
  gap: 8px !important;
}
</style>
