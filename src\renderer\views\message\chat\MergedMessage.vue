<template>
    <div class="merged-message">
        <div class="merged-card">
            <div class="text select-text">
                <template v-for="item in textSplits">
                    <img v-if="item.type === MsgTextType.Emoji" class="emoji" :class="{'coco_emoji':message.msg?.contentExtra?.contentType === 'emoji_coco_image'}" :data-large="textSplits.length === 1"  :src="item.emojiSrc">
                    <span v-else :data-at="item.type === 3 ? getAtInfo(item) : ''" :data-text-type="item.type" @click="onClickItem(item)">
                        {{ item.str }}
                    </span>
                </template>
            </div>
            <div class="divider"></div>
            <div class="merged-senders" @click.stop="openDetails">
                <div class="merged-count">X<span class="merged-times">{{ props.message.merged?.length + 1 }}</span></div>
                <div class="merged-sender" v-for="item in getMergedUsers(props.message)">
                    <ChatAvatar :size="24" :src="item.avatar" :alt="item.name" :trim-length="1" />
                    <span class="name">{{ item.name }}</span>
                    <span v-if="item.count > 1" class="merged-times"> X{{ item.count }}</span>
                </div>
            </div>
            <div class="divider divider1"></div>
            <div v-if="showNotInGroup()" class="plus-one-row" @click="onSendMesage">
                <div class="plus-one">
                    <img src="@/assets/im/plus_one.png">{{t('im.msg.addOne')}}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { MessagePlugin } from 'tdesign-vue-next';
import ChatAvatar from '../components/ChatAvatar.vue';
import MsgWrapper from '../service/message'
import { useMessageStore } from '../service/store';
import useChatSendStore from '../service/sendStore';
import { MsgTextType, getParsedTextMessage } from '../service/msgUtils';
import { useChatExtendStore } from '../service/extend';
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer, shell } = LynkerSDK;

const { t } = useI18n();

const props = defineProps({
    message: {
        type: MsgWrapper,
        required: true
    }
})

// 回复的内容
const textSplits = computed(() => {
    const splits = getParsedTextMessage(props.message.msg);
    return splits;
})

const showNotInGroup = () => useMessageStore().chatingSession?.inSession;

const openDetails = () => {
    useChatExtendStore().showChatDialog('merged-msg-detail', props.message.msg)
}

const onClickItem = (item) => {
    if(item.type === MsgTextType.At) {
        openIdentityCard(item)

    } else if(item.type === MsgTextType.Url) {
        openUrlByBrowser(item.str);
    }
}

function openIdentityCard(item) {
  const cardId = props.message.msg.contentExtra?.data?.atInfo?.[item?.atIndex || 0]?.cardId;
  const myId = useMessageStore().chatingSession?.myCardId;
  openIdentityCardById(myId, cardId);
}

function getAtInfo(item) {
  const atInfo = props.message.msg.contentExtra?.data?.atInfo?.[item?.atIndex || 0]
  return JSON.stringify({"value":item?.str?.replace("@","") ,"cardId": atInfo?.cardId,"openId": atInfo?.openId,"sessionId": atInfo?.sessionId})
}

function openIdentityCardById(mycard: string, targetCard: string) {
  if (mycard && targetCard) {
    ipcRenderer.invoke("identity-card", { cardId: targetCard, myId: mycard });
  }
}

function openUrlByBrowser(url: string) {
  shell.openExternal(url);
}

const onSendMesage = () => {
    const text = props.message.msg.contentExtra?.data?.text;
    if (props.message.msg.contentExtra?.contentType === 'emoji_coco_image') {
        const message = {
            height: 120,
            width: 120,
            text,
        };
        useChatSendStore().sendData(message, 'emoji_coco_image');
        return;
    }

    const atInfo = props.message.msg.contentExtra?.data?.atInfo;
    useChatSendStore().sendText(text, Array.isArray(atInfo) ? JSON.parse(JSON.stringify(atInfo)) : null);
}

const getMergedUsers = (msg: MsgWrapper) => {

    const res = new Map<string, { name: string, avatar: string, count: number, card: string }>();
    const { allMembers } = useMessageStore();
    const list = [...msg.merged, msg];
    const len = list.length;
    for (let index = len - 1; index >= 0; index--) {
        let item = list[index];
        const user = res.get(item.msg.contentExtra?.senderId);
        if (user) {
            user.count++;

        } else {
            const members = allMembers.get(item.msg.targetId);
            const member = members?.get(item.msg.contentExtra?.senderId);
            const name = member?.nickname || member?.staffName || item.msg.senderNickname;
            res.set(item.msg.contentExtra?.senderId, {
                name,
                avatar: member?.avatar || item.msg.senderFaceUrl || '',
                card: member?.cardId,
                count: 1,
            });
        }
    }
    // 移动端和产品确定只显示10个
    return Array.from(res.values()).slice(0, 10);
};

const openIdCard = (card: string) => {
    const myCard = useMessageStore().chatingSession.myCardId;
    if (card && myCard) {
        ipcRenderer.invoke("identity-card", { cardId: card, myId: myCard });

    } else {
        MessagePlugin.error(t('im.msg.cardError'));
    }
}

</script>

<style lang="less" scoped>
.merged-message {
    flex: 1;
    display: flex;
    justify-content: center;
    align-content: center;
    width: 0;
}

.divider {
    display: block;
    height: 1px;
    background-color: #ECEFF5;
    margin: 9px 0;
}

.divider1 {
    display: none;
}

.merged-card {
    width: 440px;
    padding: 9px 12px;
    border-radius: 8px;
    border: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
    background: var(--bg-kyy-color-bg-light, #FFF);
    word-break: break-word;
}

.merged-senders {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 16px;
    overflow: hidden;
    cursor: pointer;
}

.merged-sender {
    display: flex;
    flex-direction: row;

    & img {
        width: 100%;
        height: 100%;
    }

    & img::before {
        content: attr(alt);
        line-height: 40px;
        font-size: 12px;
        line-height: inherit;

        display: flex;
        justify-content: center;
        align-items: center;
        color: @kyy_white;
        background-color: @kyy_brand_5;
        text-align: center;
    }
    .merged-times{
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: #FC7C14;
    }
}

.merged-count {
    color: #4D5EFF;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;

    & .merged-times {
        font-size: 18px;
        line-height: 26px;
    }
}
.name{
    margin: 0 8px;
    color: #516082;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
}

.plus-one-row {
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.plus-one {
    font-size: 14px;
    font-weight: 400;
    color: #1A2139;
    line-height: 22px; /* 157.143% */
    display: flex;
    align-items: center;

    & img {
        width: 28px;
        height: 28px;
        margin-right: 8px;
    }
}

.plus-one-row:hover .plus-one {
    opacity: 0.8;
}

.text {
    white-space: pre-line;
    vertical-align: middle;
}
.msg-row:nth-last-child(1) .plus-one-row {
    display: flex;
}
.msg-row:nth-last-child(1) .divider1 {
    display: block;
}

.text span[data-text-type='3'] {
    color: #4D5EFF;
    cursor: pointer;
    margin-right: 8px;
}

.text span[data-text-type='6'] {
    color: #4D5EFF;
    cursor: pointer;
}

.emoji {
    width: 32px;
    height: 32px;
    display: inline;
    margin: 0;
    padding: 0;
    border: 0;
    box-sizing: border-box;
    vertical-align: middle;
}

.emoji[data-large="true"] {
    width: 44px;
    height: 44px;
}
.emoji.coco_emoji{
    width: 80px;
    height: 80px;
}

</style>
