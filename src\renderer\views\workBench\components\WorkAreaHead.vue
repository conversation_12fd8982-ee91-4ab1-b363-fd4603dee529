<template>
  <div class="head-box">
    <icon v-if="!hidGroup" class="icon-specials" :url="iconUrl" name="iconSpecial-graphics" />
    <div class="tabs-list">
      <div v-for="(item, index) in tabList" :key="index" :class="isactiveTabClass(item)" @click.stop="goRoute(item)">
        <div class="flex-a">
          <img class="tab-icon" :src="(item.icon && /^http/.test(item.icon)) ? item.icon : tapImage(item.type)" />
          <span class="line-1 mw100">{{ item.title }} </span>
        </div>
        <div v-if="index !== 0" @click.stop="deltabItem(item)">
          <icon class="tap-close-item" name="iconerror" :url="iconUrl" />
        </div>
      </div>
    </div>
    <div v-show="isShowTabsScrollIcon" class="tabs-arrow" @click="onScrollTabs(-1)">
      <iconpark-icon name="iconarrowright" style="font-size: 20px"></iconpark-icon>
    </div>
    <t-popup v-if="!hidGroup" placement="bottom" trigger="click" @visible-change="onVisibleChangeFn">
      <div class="org-Infor">
        <img v-if="
            activationGroupItem?.teamLogo &&
            activationGroupItem?.teamFullName !== t('clouddisk.personaLisk') &&
            activationGroupItem?.teamFullName !== t('activity.activity.person')
          " class="org-img" :src="activationGroupItem?.teamLogo" />
        <img v-if="
            !activationGroupItem?.teamLogo &&
            activationGroupItem?.teamFullName !== t('clouddisk.personaLisk') &&
            activationGroupItem?.teamFullName !== t('activity.activity.person')
          " class="org-img" src="@/assets/svg/clouddisk/temaavatar.svg" />
        <div class="org-text">{{ activationGroupItem?.teamFullName }}</div>
        <img v-if="isRedDot" class="red-dot-tap" src="@/assets/svg/badge.svg" />
        <img class="org-img-arrow" src="@/assets/svg/tap-item-down.svg" />
      </div>
      <template #content>
        <div class="t-popup-operate-clouddiskhome operate-kr">
          <div v-for="item in groupList" :key="item.teamId" class="flex-align-sb operate-item-fliter-group-item"
            style="height: 32px" :class="item.teamId !== activationGroupItem?.teamId ? 'pl44' : ''"
            @click="changGroup(item)">
            <div class="flex-a" style="width: 100%">
              <img v-if="item.teamId === activationGroupItem?.teamId" class="m012"
                src="@/assets/svg/clouddisk/icon_other.svg" />
              <avatar v-if="
                  item?.teamFullName === t('clouddisk.personaLisk') ||
                  item?.teamFullName === t('activity.activity.person')
                " class="popup-item-avatar" avatar-size="24px" />
              <img :src="item?.teamLogo ? item?.teamLogo : temaavatar"
                style="width: 24px; height: 24px; border-radius: 50%" />
              <span style="flex: 1" class="operate-item-span flex-align ovfdian pl12">{{ item.teamFullName }} </span>
              <img style="width: 12px; height: 12px;margin-right: 4px;"
                v-if="item.isRedDot && item.teamId !== activationGroupItem?.teamId" src="@/assets/bench/badge.svg" />
            </div>
          </div>
        </div>
      </template>
    </t-popup>
    <div class="flex-a">
      <div class="refreshleft">
        <icon class="org-img-window" name="iconrefresh" :url="iconUrl" @click="openRefresh" />
      </div>
      <icon v-if="newWinFlag && !hidGroup" class="org-img-window" name="iconwindow" :url="iconUrl"
        @click="openStandAloneWin" />
    </div>
  </div>
  <!-- 点击存草稿的弹窗 -->
  <t-dialog class="appAuthTipDialog" v-model:visible="saveDraftFlag" theme="info" :cancelBtn="t('address.cancel')"
    :closeBtn="false" :confirmBtn="t('address.sure')" :header="t('banch.saveDraft')" :body="t('banch.saveDraftTip')"
    @confirm="saveDraftConfirm(true)" @cancel="saveDraftConfirm(false)">
  </t-dialog>
</template>

<script setup lang="ts">
  import { ClientSide } from "@renderer/types/enumer.js";
  import { useRoute, useRouter } from "vue-router";
  import { onMounted, onActivated, ref, watch, nextTick, computed, defineExpose } from "vue";
  import avatar from "@renderer/components/kyy-avatar/index.vue";
  import { useI18n } from "vue-i18n";
  import { iconUrl } from "@renderer/plugins/KyyComponents";
  import { Icon } from "tdesign-icons-vue-next";
  import LynkerSDK from '@renderer/_jssdk';
  import { businessCount } from "@renderer/api/business";
  import { approveForeignCount } from "@/api/approve";
  import { tapImage, setgztdid } from "@renderer/utils/myUtils.ts";
  import temaavatar from "@/assets/svg/clouddisk/temaavatar.svg";
  import { useHonorStore } from "@renderer/views/workBench/teamSting/honorStore";
  import { useTabStore } from "@renderer/views/workBench/teamSting/honorStore/tabStore";
  import { Tooltip as TTooltip } from "tdesign-vue-next";
  import { destroyNode, getShowNode, refreshNode } from "@renderer/_jssdk/components/iframe/iframePool";
  import { DialogPlugin } from "tdesign-vue-next";

  const { ipcRenderer, shell } = LynkerSDK;
  const { t } = useI18n();
  const indexsActive = ref(0);
  const tooltipMap = ref({});
  const titleRef = ref(null);
  const tooltipVisible = ref(false);
  const tooltipContent = ref('');
  const tooltipRef = ref(null);

  const isTextOverflow = (el) => {
    if (!el) return false;
    return el.scrollWidth > el.clientWidth;
  };

  // 状态管理：控制移动tabs的偏移距离
  const tabsScrollLeft = ref(0);
  //状态管理：获取tabs-list的dom使用
  const tabsListRef = ref(null);
  // 状态管理：是否现实移动的图标
  const isShowTabsScrollIcon = ref(false);
  //公共store
  const honorStore = useHonorStore();
  const tabStore = useTabStore();
  const newWinFlag = ref(true);
  const props = defineProps({
    currentAppPath: {
      type: String,
      default: "",
    },
    iconIconWindow: {
      type: Boolean,
      default: true,
    },
    notRed: {
      type: Boolean,
      default: false,
    },
    groupList: {
      type: Array,
      default: () => [],
    },
    tabList: {
      type: Array,
      default: () => [],
    },
    activationGroupItem: {
      type: Object,
      default: () => { },
    },
    activeIndex: {
      type: Number,
      default: 0,
    },
    tabIndex: {
      type: Number,
      default: 0,
    },
    redType: {
      type: Number,
      default: 1,
    },
    hidGroup: {
      type: Boolean,
      default: false,
    },
  });
  onActivated(() => { });
  onMounted(() => { });
  const router = useRouter();
  const route = useRoute();
  // 'openStandAloneWin',
  const emits = defineEmits([
    "openRefresh",
    "getGroupListApi",
    "deltabItem",
    "setCloudDiskType",
    "changGroupApproval",
    "changGroupCustomer",
    "changGroupSupplier",
    "changGroupDevice",
    "chang-group-business",
    "changGroupPartner",
    "setActiveIndex",
    "restUdpListRefresh",
    "setActiveIndexAndName",
    "changGroupCustomerService",
    "changGroupActivity",
  ]);
  const openRefresh = () => {
    honorStore.shershFlag = true;
    getShowNode().forEach((node) => {
      refreshNode(node);
    });
    emits("openRefresh");
  };
  const onVisibleChangeFn = (val) => {
    console.log(val, props.activationGroupItem);
    if (val) {
      emits("getGroupListApi", props.activationGroupItem.teamId);
    }
  };
  const openStandAloneWin = () => {
    console.log(props.tabList, "routerprops.tabListouteroute");
    newWinFlag.value = false;
    ipcRenderer.invoke("click-standalone-window", {
      url: props.currentAppPath,
      flag: route.fullPath.split("/")[1],
    });
  };
  // const goRoute = (item) => {
  //   emits("getGroupListApi", props.activationGroupItem.teamId);
  //   router.push({
  //     path: item.path,
  //     query: { ...route.query, ...item.query },
  //   });
  //   honorStore.setdetailHaved(false)
  // };

  const goRoute = (item, index?) => {
    emits("getGroupListApi", props.activationGroupItem.teamId);
    console.log("item.netCover~item.netCover44444", item);
    console.log("item.netCoverroute.query", route.query);
    const querys = { ...route.query, ...item.query };
    // if (route.query.restFlag) {
    //   router.push({
    //   path: item.path,
    //   query: route.query,
    // });
    //   return
    // }istab:true,

    router.push({
      path: item.path,
      query: {
        ...((item.netCover ? { ...item.query } : querys) || {}),
        __tabs_id__: item.path_uuid,
        __tabs_title__: item.label,
        __tabs_icon__: item.icon,
        __tabs_active_icon__: item.icon,
      },
    });
    if (item.path == "/workBenchIndex/aboutour") {
      // honorStore.shershFlag = false;//红点问题注释掉了不知道之前为啥变成false
    }

  };

  watch(
    () => indexsActive.value,
    async (newVal: number, oldVal: number) => {
      console.log(newVal, oldVal, 'newValoldVal1');
      await nextTick();
      console.log(newVal, oldVal, 'newValoldVal');
      onScrollTabs(newVal - oldVal > 0 ? -1 : 1, newVal);
    },
    {
      immediate: true,
    },
  );
  watch(
    () => props.tabList,
    async () => {
      await nextTick();
      console.log('哲理执行了吗')
      const scrollWidth = tabsListRef.value.scrollWidth;
      const offsetWidth = tabsListRef.value.offsetWidth;
      console.log(scrollWidth, offsetWidth, 'scrollWidthoffsetWidth');
      isShowTabsScrollIcon.value = scrollWidth > offsetWidth;
    },
    {
      immediate: true,
      deep: true,
    },
  );



  ipcRenderer.on("update-approve", (val, i) => {
    if (props.activationGroupItem?.teamId) {
      emits("getGroupListApi", props.activationGroupItem.teamId);
    }
  });
  ipcRenderer.on("update-niche-reddot", (val, i) => {
    if (props.activationGroupItem?.teamId) {
      emits("getGroupListApi", props.activationGroupItem.teamId);
    }
  });
  const changGroup = (item) => {
    console.log(item, "item-----444444----------");
    emits("setactivationGroupItem", item);
    honorStore.shershFlag = true;
  };

  const isactiveTabClass = (item, index) => {
    console.log(item, "item-----444444----------");
    if (item.name === 'bench_ablumDetail' && route.name === 'bench_ablumDetail') {
      indexsActive.value = index
      return "bgc-fff tabs-item"
    }
    if (item.name === 'rkAdDetails' && route.name === 'rkAdDetails'&&route.query.id === item.query.id) {
      return "bgc-fff tabs-item"
    }
    if (item?.addNew) {
      if (route.path === item.path && route.path.indexOf('fengcai') > -1) {

        if (route.query.id === item.query.id) {
          indexsActive.value = index
        }
        return route.query.id === item.query.id ? "bgc-fff tabs-item"
          : "tabs-item";
      }
      const routerQ = {...route?.query,activeIndex: +route?.query.activeIndex}
      let cloneRouteQuery=JSON.parse(JSON.stringify(route.query));
      let itemQuery=JSON.parse(JSON.stringify(item.query));
      delete cloneRouteQuery.__tabs_id__;
      delete  itemQuery.__tabs_id__;
      let routeNotUuidArr=['analysisExpressDetail','bench_ad_details','noticeDetail','noticeDetailRead','policyExpressDetail',"PolicyExpressInfo","PolicyAnalysisInfo"]
      return route.path === item.path
      && JSON.stringify(routeNotUuidArr.includes(route.name)?itemQuery:item.query) === JSON.stringify(routeNotUuidArr.includes(route.name)?cloneRouteQuery:routerQ)
        ? "bgc-fff tabs-item"
        : "tabs-item";
    } else {
      if (route.path === item.path) {
          indexsActive.value = index
        }
      return route.path === item.path ? "bgc-fff tabs-item" : "tabs-item";
    }
  };


  /**
   * 点击左右箭头图标移动tabs拦
   * @param normal 移动的方向
   */
  const onScrollTabs = async (normal: -1 | 1, index = 0) => {
    await nextTick();
    const tabItemWidth = 180;
    tabsScrollLeft.value += tabItemWidth * normal * (index + 1);
    const scrollWidth = tabsListRef.value.scrollWidth;
    const offsetWidth = tabsListRef.value.offsetWidth;
    if (tabsScrollLeft.value < -scrollWidth + offsetWidth) {
      tabsScrollLeft.value = -scrollWidth + offsetWidth;
    } else if (tabsScrollLeft.value > 0) {
      tabsScrollLeft.value = 0;
    }
  };


  const deldata = ref();

  const handleBeforeClose = (title: string, content: string): Promise<boolean> => new Promise((resolve) => { const confirmDia = DialogPlugin.confirm({ theme: 'info', header: title, body: content, confirmBtn: '确认关闭', cancelBtn: { content: '取消', theme: 'default', variant: 'outline' }, onConfirm: async () => { confirmDia.hide(); resolve(true); }, onClose: () => { confirmDia.destroy(); resolve(false); }, }); });

  const deltabItem = async (data) => {
    if (data.beforeCloseOptions && (data.beforeCloseOptions.title || data.beforeCloseOptions.content)) {
      const isClose = await handleBeforeClose(data.beforeCloseOptions.title, data.beforeCloseOptions.content);
      if (!isClose) {
        return;
      }
    }
    // 如果data.path包含work_bench_iframe，则手动处理iframe的关闭
    if (data.path?.includes('/work_bench_iframe') || data.path?.includes('/work_bench_webview')) {
      const iframeOnlyId = data.path.split('/').pop();
      console.log(iframeOnlyId, "iframeOnlyId--------------------");
      destroyNode(iframeOnlyId);
    }
    if (data.path?.includes('/workBenchEnterprise')) {
      destroyNode('receive-iframe');
      destroyNode('refund-iframe');
      destroyNode('settlement-order-iframe');
      destroyNode('settlement-fund-iframe');
      destroyNode('settlement-withdrawal-record-iframe');
    }
    if (data.name === "fengcai_admin_list") {
      localStorage.removeItem("leftTabValue");
    }
    const index = props.tabList.findIndex((e) => {
      /**
       * fix: https://www.tapd.cn/69781318/bugtrace/bugs/view/1169781318001040963
       */
      if ('query' in data) {

        return e.path === data?.path && JSON.stringify(e?.query) === JSON.stringify(data.query);
      }
      return e.path === data?.path;
    });
    //如果是发布荣誉页面做判断是否存草稿处理
    console.log(index, 'indexxxxxxxxxxxx', route.path, data.path);
    if (data.routeName == "addHonor" && honorStore.saveDraftFlag) {
      deldata.value = data;
      saveDraft();
    } else if (data.routeName === "addIntroduce" && tabStore.saveDraftWhenCloseTab) {
      deldata.value = data;
      saveDraft();
    } else if (data.routeName === "addGrowth" && tabStore.saveDraftWhenCloseTab) {
      deldata.value = data;
      saveDraft();
    } else {
      if (route.path === '/workBenchIndex/ablum-view/ablum-detail') {
        // 数字工厂的相册特殊处理下
        let indexs = props.tabList.findIndex(e => e.path === "/workBenchIndex/work_bench_album")
        console.log(indexs, 'indexsindexsindexs');
        if (indexs > 0) {
          emits("deltabItem", indexs, true);
        }

      }
      if (route.path === data.path) {
        emits("deltabItem", index, true);
        if (data.path == "/workBenchIndex/aboutour") {
          honorStore.shershFlag = true;
        }
      } else {
        emits("deltabItem", index);
      }
    }
  };
  //点击存草稿按钮
  const saveDraftFlag = ref(false);
  const saveDraft = () => {
    saveDraftFlag.value = true;
  };
  const saveDraftConfirm = (flag) => {
    console.log("deldata.valuedeldata.value", deldata.value);
    if (deldata.value.routeName === "addHonor") {
      honorStore.saveDraftConfirm = flag;
      console.log(honorStore.saveDraftConfirm, flag, "flag--------------------flag--------------flag");
      saveDraftFlag.value = false;
      deltabItem({ path: "/workBenchIndex/addHonor" });
      honorStore.setcloseAddHonor(false);
    } else if (deldata.value.routeName === "addIntroduce") {
      deltabItem({ path: deldata.value.path });
      tabStore.saveDraft("introduce", flag);
      saveDraftFlag.value = false;
    } else if (deldata.value.routeName === "addGrowth") {
      deltabItem({ path: deldata.value.path });
      tabStore.saveDraft("growth", flag);
      saveDraftFlag.value = false;
    }
  };
  watch(
    () => route.path,
    () => {
      if (route.query.closeTab) {
        deltabItem({ path: route.query.closeTab });
      }
    },
  );
  //是否关闭新建页签
  watch(
    () => honorStore.closeAddHonor,
    (val) => {
      if (val) {
        deltabItem({ path: "/workBenchIndex/addHonor" });
        honorStore.setcloseAddHonor(false);
      }
    },
  );
  const team_list = ref([]);
  const isRedDot = computed(() => {
    let flag = false;
    for (let index = 0; index < props.groupList.length; index++) {
      const element = props.groupList[index];
      if (element.teamId !== props.activationGroupItem.teamId && element.isRedDot) {
        flag = true;
      }
    }
    console.log(props.activationGroupItem, 'props.groupList[props.groupList[props.activationGroupItem');
    console.log(props.groupList, 'props.groupList[props.groupList[');
    console.log(flag, 'props.groupList[props.groupList[flagflag');
    return flag;
  });

  // 检查文本是否溢出并显示tooltip
  const showTooltip = (event, content) => {
    const target = event.target;
    if (target.scrollWidth > target.clientWidth) {
      tooltipContent.value = content;
      tooltipVisible.value = true;
    }
  };

  // 隐藏tooltip
  const hideTooltip = () => {
    tooltipVisible.value = false;
  };

  // 处理tooltip显示状态
  const handleTooltipVisible = (visible, index, content) => {
    // 只在元素实际发生截断时才显示提示
    if (visible) {
      const elements = document.querySelectorAll('.text-ellipsis');
      if (elements[index] && elements[index].scrollWidth > elements[index].clientWidth) {
        tooltipMap.value[index] = true;
      } else {
        tooltipMap.value[index] = false;
      }
    } else {
      tooltipMap.value[index] = false;
    }
  };
  defineExpose({ deltabItem });
</script>

<style lang="less" scoped>
  @import "@/style/workAreaHeadNew.less";

  .operate-kr {
    max-height: 320px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .operate-kr::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    // background-color: red;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  .operate-kr::-webkit-scrollbar-track {
    // box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    // border-radius: 10px;
    background-color: #fff;
  }

  /*定义滑块 内阴影+圆角*/
  .operate-kr::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #d5dbe4;
  }

  .mw100 {
    max-width: 100px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .text-ellipsis {
    display: block;
    max-width: 100px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .tooltip-wrapper {
    position: relative;
    display: inline-block;
    max-width: 100px;
  }
</style>
