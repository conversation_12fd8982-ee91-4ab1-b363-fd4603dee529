<template>
  <div class="cantainer">
    <div class="title">{{ t('contacts.new') }}</div>
    <div v-if="empty" class="recent-empty">
      <img src="@renderer/assets/emptydata.png" alt="">
      <div class="tip">暂无数据</div>
    </div>
    <div v-else :class="['t-menu-container', isNotMac ? 'scrollbar' : '']">
      <t-menu theme="light" defaultValue="dashboard" style="width: 100%">
        <t-menu-item v-for="item in renderList" :key="item.id" :value="item.id" @click="item.status === 'FRIEND_APPLY_STATUS_PENDING' ? agree(item) : showCard(item)">
          <template #icon>
            <avatar class="avatar-icon" roundRadius :imageUrl="item.cardApplyInfo.avatar" :userName="item.cardApplyInfo.name" avatarSize="44px" />
          </template>
          <div class="user-info">
            <div class="user-name">
              <div>{{ item.cardApplyInfo.name }}</div>
            </div>
            <div class="verify-msg">{{ item.remarks }}</div>
            <MultiIdTag :myCard="item.cardAcceptInfo" :anotherCard="item.cardApplyInfo"></MultiIdTag>
          </div>
          <div class="act-groups">
            <div v-if="item.status === 'FRIEND_APPLY_STATUS_PENDING'">
              <t-button
                theme="default"
                @click.stop="viewApply(item)"
                class="viewApplyBtn"
              >
                {{ t('contacts.view') }}
              </t-button>
              <t-button theme="primary" @click.stop="agreeApply(item)" class="agreeApplyBtn">
                {{ t('account.agree') }}
              </t-button>
            </div>
            <div v-else>
              <div v-if="item.status === 'FRIEND_APPLY_STATUS_IGNORED'" class="cur-status">{{ t('contacts.refuse') }}</div>
              <div v-if="item.status === 'FRIEND_APPLY_STATUS_ACCEPTED'" class="cur-status">{{ t('contacts.agree') }}</div>
              <div v-if="item.status === 'FRIEND_APPLY_STATUS_EXPIRED'" class="cur-status">{{ t('contacts.expired') }}</div>
            </div>
          </div>
        </t-menu-item>
      </t-menu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import avatar from '@renderer/components/kyy-avatar/index.vue';
import MultiIdTag from '@/components/contacts/MultiIdTag.vue';

import { outerCardsDetails, personalCardsDetails, platformCardsDetails} from '@renderer/api/contacts/api/follow';
import { acceptFriend } from '@renderer/api/im/api';
import { getAllApplyList } from '@renderer/api/contacts/api/friend';

import { MessagePlugin } from 'tdesign-vue-next';
import { filterCard } from '../utils';
import { isNotMac } from '@renderer/views/zhixing/util';
import { setApplyCards } from '@renderer/utils/auth';
import { useI18n } from 'vue-i18n';
import { useContactsStore } from '@renderer/store/modules/contacts';
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const contactsStore = useContactsStore();
const { t } = useI18n();
const empty = ref(false);
const list = ref([]);
// 申请人信息
const applyListData = ref([]);
// 接受人信息
const acceptCardList = ref([]);
const renderList = ref([]);
let agreeId = ''; // 记录同意操作的id

const viewApply = async (item) => {
  console.log('viewApply',item);
  ipcRenderer.invoke("identity-card", {
    cardId: item.cardIdApply,
    myId: item.cardIdSelf,
    applyId: item.id,
    route_path: 'contactValidate',
  });
};

const agreeApply = async (item) => {
  acceptFriend({id:item.id}).then(() => {
    agreeId = item.id;
    updateStatus();
  }).catch(err => {
    MessagePlugin.error(err.response.data?.message || t('zx.contacts.addFail'));
  });
}
const applyList = () => {
  getAllApplyList({onlySelfReceived:1}).then(({data}) => {
    console.log(data, 'getApplyList')
    if (data.code === 0) {
      list.value = data.data.applies;
      console.log('list.value',list.value)
      data.data.applies.length ? getApplyCardsDetail(list.value) : empty.value = true;
    }
  })
};
const getApplyCardsDetail = (list) => {
  // 此列表不会出现内部身份卡
  // 个人身份卡id 对外身份卡id
  const { personalCards, outerCards, platformCards } = filterCard(list, 'cardIdApply');
  let promiseAll = [];
  personalCards.length && promiseAll.push(personalCardsDetails({ids: personalCards}));
  outerCards.length && promiseAll.push(outerCardsDetails({ids: outerCards}));
  platformCards.length && promiseAll.push(platformCardsDetails({ ids: platformCards }));

  // 获取身份卡信息
  Promise.all(promiseAll).then((res) => {
    console.log(res, 'promise all cardsDetails');
    let dataList0 = [], dataList1 = [], dataList2 = []
    // 判断是否获取个人身份卡信息
    if (personalCards.length && res[0].status === 200) {
      dataList0 = res[0].data?.profiles.map(v => {
        const item = {
          avatar: v.avatar || '',
          team: '',
          teamName: '',
          cardId: v.openid,
          jobName: '',
          name: v.title,
        };
        return item;
      });
    } else if (res[0]?.data.code === 0) {
      dataList0 = handleDataList(res[0].data.data);
    }
    if (res[1]?.data.code === 0) {
      dataList1 = handleDataList(res[1].data.data);
    };
    if (res[2]?.data.code === 0) {
      dataList2 = handlePlatDataList(res[2].data.data);
    };
    applyListData.value = [...dataList0, ...dataList1, ...dataList2];
    getAcceptCardDetail();
  });
};
const getAcceptCardDetail = () => {
  // 对外身份卡id
  const { outerCards } = filterCard(list.value, 'cardIdSelf');
  if (outerCards.length) {
    outerCardsDetails({ids: outerCards}).then(({data}) => {
      if (data.code === 0) {
        acceptCardList.value = handleDataList(data.data);
        comList();
      }
    })
  } else {
    comList();
  }
};
const comList = () => {
  renderList.value = list.value.map(v => {
    v.cardApplyInfo = {};
    v.cardAcceptInfo = {};
    // 整合申请人信息
    const applyInfo = applyListData.value.find(apply => apply.cardId === v.cardIdApply);
    if (applyInfo) {
      v.cardApplyInfo = applyInfo;
    }
    // 整合接受人信息
    const acceptInfo = acceptCardList.value.find(accept => accept.cardId === v.cardIdSelf);
    if (acceptInfo) {
      v.cardAcceptInfo = acceptInfo;

    }
    return v;
  })
  console.log('=====>',renderList.value);
};
const handleDataList = (dataList) => {
  const showList = dataList.map(v => {
    const item = {
      avatar: v.avatar,
      team: v.team,
      teamName: v.team,
      cardId: v.cardId,
      jobName: v.position[0]?.jobName || '',
      name: v.name || v.title,
    };
    return item;
  });
  return showList;
};
// 平台身份
const handlePlatDataList = (dataList) => {
  const showList = dataList.map(v => {
    const item = {
      avatar: v.platform?.avatar,
      team: v.team?.teamId,
      teamName: v.team?.fullName,
      cardId: v.platform?.cardId,
      jobName: '',
      name: v.platform?.name,
    };
    return item;
  });
  return showList;
};

const agree = (item) => {
  setApplyCards({agree: true, remarks: item.remarks, source: item.source});
  agreeId = item.id;
  ipcRenderer.invoke("identity-card", { cardId: item.cardIdApply, myId: item.cardIdSelf });
};
const showCard = (item) => {
  ipcRenderer.invoke("identity-card", { cardId: item.cardIdApply, myId: item.cardIdSelf });
};
const updateStatus = () => {
  renderList.value = renderList.value.map(v => {
    if (v.id === Number(agreeId)) {
      v.status = 'FRIEND_APPLY_STATUS_ACCEPTED';
    }
    return v;
  })
}
const updateContactListener = (event, arg) => {
  agreeId = arg?.applyId;
  updateStatus();
};

onMounted(() => {
  applyList();
  contactsStore.clearNotice();
  ipcRenderer.on("update-contact-list", updateContactListener);
});

onUnmounted(() => {
  ipcRenderer.off("update-contact-list", updateContactListener);
});
</script>

<style lang="less" scoped>
.mr-8 {
  margin-right: 8px;
}
.cantainer {
  width: 100%;
  position: relative;
  .title {
    color: var(--text-kyy-color-text-1, #1A2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    margin:20px 24px 20px 24px;
  }
  .recent-empty {
    width: 100%;
    position: absolute;
    top: 64px;
    left: 8px;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 200px;
      height: 200px;
    }
    .tip{
      color: var(--text-kyy_color_text_2, #516082);

    }
  }
  .t-menu-container {
    width: calc(100% - 48px);
    position: absolute;
    top: 64px;
    left: 24px;
    bottom: 0;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  :deep(.t-default-menu__inner .t-menu) {
    padding: 0;
  }
  :deep(.t-default-menu:not(.t-menu--dark) .t-menu__item.t-is-active:not(.t-is-opened)) {
    background: transparent !important;
    color: #13161b !important;
    &:hover {
      background: #f0f8ff !important;
      border-radius: 4px;
    }
  }
  :deep(.t-default-menu .t-menu__item) {
    position: relative;
    font-size: 14px;

    color: #13161b;
    line-height: 22px;
    height: 68px !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA) !important;
    }
  }
  .avatar-icon {
    margin-right: 12px;
  }
  .user-info {
    width: 0;
    flex: 1;
    overflow: hidden;

    .user-name {
      display: flex;
      align-items: flex-start;
    }
    .post {
      font-size: 12px;

      color: #717376;
      line-height: 20px;
    }
    .company-mark {
      padding: 1px 5px;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_warning, #FFE5D1);
      color: var(--kyy_color_tag_text_warning, #FC7C14);
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin-left: 4px;
    }
    .verify-msg {
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;

      color: #717376;
      line-height: 20px;
    }
  }
  .act-groups {
    margin-right: 8px;
    display: flex;
    align-items: center;
    .t-button {
      min-width: auto;
    }
    .cur-status {
      width: 60px;
      font-size: 14px;

      color: #13161b;
      line-height: 22px;
      text-align: center;
    }
  }
}

:deep(.viewApplyBtn) {
  margin-right: 12px;
  border-radius: var(--radius-kyy_radius_button_s, 4px);
  border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4D5EFF) !important;
  background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #EAECFF) !important;

  .t-button__text{
    color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4D5EFF) !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
}
:deep(.agreeApplyBtn) {
  .t-button__text{
    color: var(--color-button_primary-kyy_color_button_primary_text, #FFF) !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
}

:deep(.t-menu__content) {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  overflow: hidden;
  gap: 12px;
}


</style>
