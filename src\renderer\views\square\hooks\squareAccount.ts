import { computed, onBeforeMount, ref, watch } from 'vue';
import { DialogPlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import { useSquareStore } from '@/views/square/store/square';
import { useStateStore } from '@/views/square/store/state';
import { AccountItem } from '@/components/page-header/type';
import { SquareType } from '@/api/square/enums';
import { toAccount } from '@/views/square/utils/business';
import { ORG_DEFAULT_AVATAR } from '../constant';

export const isSwitchAsking = ref(false);

export const useSquareAccount = (refresh?: () => void) => {
  const store = useSquareStore();
  const stateStore = useStateStore();
  const { t } = useI18n();

  const accountList = ref<AccountItem[]>([]);
  const accountNoticeCount = ref(0);

  // 其他账号的未读数
  const accountCount = computed(() => {
    const otherCount = accountList.value
      .filter((v) => v.id !== store.squareId)
      .map((v) => v.dotCount)
      .reduce((total, val) => total + val, 0);
    return otherCount;
  });

  const getAvatar = (avatar, squareType) => {
    if (squareType && squareType !== SquareType.Individual) return avatar || ORG_DEFAULT_AVATAR;
    return avatar;
  };

  const setAccountList = () => {
    accountList.value = store.squareList.map((item) => {
      const { squareId, squareType, name, avatar } = item.square;

      // 红点数
      let dotCount = 0;
      // 是否以红点样式显示红点(非数字)
      let dot = false;

      // 非当前选中广场号才显示红点
      if (squareId !== store.squareId) {
        dotCount = item.unreadStats.total;
        dot = dotCount === 0 && item.unreadStats.timelinePosts > 0;

        // 确保红点能显示
        if (dot) dotCount = dotCount || 1;
      } else {
        dotCount = 0;
      }

      return {
        id: squareId,
        name,
        avatar: getAvatar(avatar, squareType),
        dotCount,
        dot,
        extra: {
          ...item,
        },
      };
    });

    accountNoticeCount.value = store.noticesTotalCount;

    const item = store.squareList.find((v) => v.square.squareId === store.squareId);
    item && store.setSquareSelected(item);
  };

  onBeforeMount(async () => {
    await store.getSquaresList();
    await setAccountList();
  });

  watch(
    () => store.squareList,
    (val) => {
      if (isSwitchAsking.value) return;

      if (!val) return;
      setAccountList();
    },
    { deep: true },
  );

  watch(
    () => store.noticesTotalCount,
    (val) => {
      if (isSwitchAsking.value) return;

      accountNoticeCount.value = val;
      // accountList.value = store.squareList;
      setAccountList();
    },
  );
  const setAlbumTeamId = (square) => {
    if (square?.squareType !== SquareType.Individual) {
      localStorage.setItem('albumTeamId', square?.originId);
    }
  };

  // 切换账号
  const accountChange = async (item: AccountItem, isAsk = true, cb = (() => true)) => {
    console.log('square:accountChange', isAsk, isSwitchAsking.value, item);
    if (isSwitchAsking.value) return false;

    stateStore.isSwitchAccount = false;
    if (item.id === store.squareId || item?.extra?.square?.squareId === store.squareId) {
      return false;
    }

    const doSwitch = async () => {
      isSwitchAsking.value = false;
      const res = await toAccount(item.extra);
      if (res) {
        const square = store.squareList.find((v) => v.square.squareId === (item.id || item?.extra?.square?.squareId));
        setAlbumTeamId(square.square);
        await refresh?.();
        await setAccountList();
      }

      stateStore.isSwitchAccount = true;
      store.searchKeyword = '';
      cb?.();
    };

    if (isAsk) {
      isSwitchAsking.value = true;
      const confirmDia = DialogPlugin.confirm({
        header: t('square.switchAndRefresh'),
        body: t('square.switchAndRefreshContent'),
        confirmBtn: t('square.action.confirm'),
        cancelBtn: t('square.action.cancel'),
        placement: 'center',
        onConfirm: async () => {
          setTimeout(() => {
            isSwitchAsking.value = false;
            confirmDia.destroy();
            doSwitch();
          }, 200);
        },
        onClose: () => {
          setTimeout(() => {
            isSwitchAsking.value = false;
            stateStore.isSwitchAccount = false;
            confirmDia.destroy();
          }, 200);
        },
      });
    } else {
      doSwitch();
    }

    return true;
  };

  return {
    accountList,
    accountCount,
    accountChange,
  };
};
