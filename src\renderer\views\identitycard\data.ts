import { searchPairs } from "@renderer/api/contacts/api/common";
import logoIcon from "@renderer/assets/defaultOrgLogo.svg";
import {
  getPlatformCard,
  getInternalCard,
  getProfilesCard,
  projectList,
  relatedOrganize,
  getRelatedOrg,
  relatedBusiness,
  getAccountsDetail,
  getExternalCardNew,
  getCardRemark,
} from "../../api/identity/api/card";
import { getCards, setCards, setStaff, getStaff, setPlatform, checkCommonTeams } from "@renderer/utils/auth";
import { MessagePlugin } from "tdesign-vue-next";
import { staffOpenid,getPlatformWithTeams } from '@renderer/api/contacts/api/recent';

export const loadAcountIdentityCards =  async () => {
  const res = await staffOpenid();
  if (res.data?.code === 0) {
    const data = res.data;
    setCards(data.data.cards);
    setStaff(data.data.staff);
  }
  // 获取平台身份
  const plat = await getPlatformWithTeams()
  if(plat.data?.code === 0){
    const data = plat.data.data?.list
    const list = []
    data?.forEach(val => {
      list.push({
        uuid: val.platform?.cardId || val.platform?.card_id || ('PT' + val.platform?.id),
        avatar: val.platform?.avatar || '',
        name: val.platform?.name || '',
        team: val.team?.fullName || '',
        teamId: val.team?.teamId || '',
        type: val.team?.type || '',
        staffId: val.platform?.id,
        member_position: val.platform?.member_position || '',
        positions: [],
        logo: val.team?.logo || '',
        jobs: '',
      })
    });
    setPlatform(list);

  }
  await checkCommonTeams();
}

export const _transPlatformDetail = (val) => {
  return {
    cardId:val.platform?.cardId || val.platform?.card_id || ('PT' + val.platform?.id),
    uuid: val.platform?.cardId || val.platform?.card_id || ('PT' + val.platform?.id),
    openId: val.platform?.openid || '',
    staffId: val.platform?.id,
    avatar: val.platform?.avatar || '',
    name: val.platform?.name || '',
    team: val.team?.fullName || '',
    teamId: val.team?.teamId || val.platform?.team_id || '',
    telCode: val.platform?.telCode || '',
    telephone: val.platform?.telephone || '',
    positions: [{departmentId: '', departmentName: '', jobName: ''}],
  }
}

const _checkCardIdApi = async (cardId, viewCardId?) => {
  // 外部身份卡
  if (cardIdType(cardId) === "outer") {
    console.log(cardId, "outer cardid");
    // const id = +cardId.split("#")[1];
    // const { data } = await getExternalCard(id);
    const { data } = await getExternalCardNew({ cardId, viewCardId })
    return data.data;
  }
  // 内部身份卡
  if (cardIdType(cardId) === "inner") {
    const { data } = await getInternalCard({ cardId, viewCardId }).catch(err => {
      MessagePlugin.error(err?.response?.data?.message);
    });
    return data.data;
  }
   // 平台身份卡
   if (cardIdType(cardId) === "platform") {
    const { data } = await getPlatformCard({ cardId, viewCardId }).catch(err => {
      MessagePlugin.error(err?.response?.data?.message);
    });
    return data.data;
  }
  const { data } = await getProfilesCard(cardId);
  return data;
};

// 区分个人，内部，外部身份卡,平台身份卡
export const cardIdType = (cardId) => {

  if(!cardId){
    return "personal";
  }
  if (~cardId.indexOf("#")) {
    return "outer";
  }
  if (~cardId.indexOf("$")) {
    return "inner";
  }
  if (cardId && /^PT/.test(cardId)) {
    return "platform";
  }
  return "personal";
};
// 简单获取关系类型，添加好友用
export const cardPairType = (cardId, peerId, teamId?) => {
  if (cardIdType(cardId) === "personal" && cardIdType(peerId) === "personal") {
    return "FRIEND";
  }
  if (cardIdType(cardId) === "platform" || cardIdType(peerId) === "platform") {
    return "PLATFORM_FRIEND";
  }
  if (teamId) {
      const localCardsAndStaffs = [...getCards(), ...getStaff(true)]
      const localCard = localCardsAndStaffs.find(val => val.uuid === cardId)
      if(localCard?.teamId === teamId || localCard?.internal_team_id === teamId){
        return "CO_WORKER";
      }
  }
  return "BUSINESS";
}
// 会话分组，区分个人，内部，外部身份卡,平台身份卡
export const cardGroupType = (cardId) => {

  if(!cardId){
    return "friend";
  }
  if (~cardId.indexOf("#")) {
    return "external";
  }
  if (~cardId.indexOf("$")) {
    return "internal";
  }
  if (cardId && /^PT/.test(cardId)) {
    return "community";
  }
  return "friend";
};

// 转换数据模型通用
const _transData = (cardType, data) => {
  if (cardType === "personal") {
    return {
      avatar: data?.avatar || "",
      name: data?.title || "",
      linkId: data.link_id,
      cardId: data.openid,
      openid: data.openid,
      teamId: "",
      teamLogo: "",
      teamName: "",
      options: _transPersonal(data),
      removed: data?.removed || false,
      psn_auth: data.psn_auth
    };
  }
  if (cardType === "inner") {
    return {
      avatar: data.options.find((v) => v.type === "avatar")?.value || "",
      name: data.options.find((v) => v.type === "name")?.value || "",
      linkId: "",
      cardId: `$${data.staffId}`,
      openid: data.openid,
      teamId: data.teamId,
      teamLogo: data.teamLogo || logoIcon,
      teamName: data.teamName,
      isDeleted: data.isDeleted,
      options: _transInner(data),
      is_card: data.is_card,
      contact_auth: data.contact_auth
    };
  }
  if (cardType === "platform") {
    return {
      avatar: data.options.find((v) => v.type === "avatar")?.value || "",
      name: data.options.find((v) => v.type === "name")?.value || "",
      linkId: "",
      cardId: `PT${data.staffId}`,
      openid: data.openid,
      teamId: data.teamId,
      teamLogo: data.teamLogo || logoIcon,
      teamName: data.teamName,
      isDeleted: data.isDeleted,
      options: _transPlatform(data),
      is_card: data.is_card,
      contact_auth: data.contact_auth,
      teamType: data.teamType,
      isOneself: data.isOneself,
      is_disable: data.is_disable,
      member_position: data.member_position,
      official_type: data?.official_type,
    };
  }
  if (cardType === "outer") {
    return {
      avatar: data.options.find((v) => v.type === "avatar")?.value || "",
      name: data.options.find((v) => v.type === "name")?.value || "",
      linkId: "",
      cardId: data.cardId,
      openid: data.openId,
      teamId: data.internal_teamId || "",
      teamLogo: data.logo || logoIcon,
      teamName: data.team,
      options: _transOuter(data),
      is_card: data.is_card,
      contact_auth: data.contact_auth
    };
  }
};

// 处理个人数据附属数据
const _transPersonal = (data) => {
  if (data.attachments?.options) {
    return data.attachments.options?.map(item => {
      if (item.hasOwnProperty('visible_type')) {
        return item
      } else {
        return {
          ...item,
          visible: 1,
          visible_type: 2,
          visible_users: {}
        }
      }
    });
  }
  const options = [
    {
      type: "phone",
      name: "手机号",
      editable: 1,
      value: [
        {
          code: data?.region || "",
          phone: data?.cellphone || ""
        }
      ],
      display: 2,
      visible: 1,
      visible_type: 2,
      visible_users: {}
    },
    {
      type: "email",
      name: "邮箱地址",
      editable: 1,
      value: data?.email || "",
      display: 2,
      visible: 1,
      visible_type: 2,
      visible_users: {}
    },
    {
      type: "gender",
      name: "性别",
      editable: 1,
      value: data?.gender || 0, // 1 男 2 女
      display: 2,
      visible: 1,
      visible_type: 2,
      visible_users: {}
    },
    {
      type: "birthday",
      name: "生日",
      editable: 1,
      value: data?.birthday || "",
      display: 2,
      visible: 1,
      visible_type: 2,
      visible_users: {}
    },
    {
      type: "area",
      name: "地区",
      editable: 1,
      value: "",
      display: 2,
      visible: 1,
      visible_type: 2,
      visible_users: {}
    }
  ];
  return options;
};

// 处理平台身份卡附属数据 插入会员职务，非编辑页需排序到姓名后展示
const _transPlatform = (data, isEditPage = false) => {
  const options = isEditPage
    ? data.options
    : data.options.filter((v) => v.type !== "avatar");
  if (data.member_position) {
    const member_position_obj = {
      type: 'member_position',
      name: '会员职务',
      value: data.member_position || '',
      editable: 0,
      display: 1,
      visible: 0,
      visible_type: 0,
      visible_users: {},
    }
    const index = options?.findIndex(v => v.type === 'name');
    options.splice(index + 1, 0, member_position_obj);
  }
  return options;
};

// 处理内部身份卡附属数据
const _transInner = (data) => {
  const options = data.options.filter(
    (v) => v.type !== "avatar" && v.type !== "name"
  );
  return options;
};

// 处理外部身份卡附属数据
const _transOuter = (data) => {
  if (data.options) {
    return data.options?.map(item => {
      if (item.type === 'no') {
        return {
          ...item,
          type: "jobNumber",
        }
      } else if (item.type === 'positions') {
        return {
          ...item,
          type: "positions",
        }
      } else if (item.type === 'telephone') {
        const value = item.value?.map(v => {
          return {
            code: v?.telcode || "",
            phone: v?.telephone || ""
          }
        })
        return {
          ...item,
          type: "phone",
          value
        }
      } else if (item.type === 'email') {
        return {
          ...item,
          type: "email",
        }
      } else {
        return item
      }
    });
  }
  const options = [
    {
      type: "jobNumber",
      name: "工号",
      editable: 1,
      value: data?.no || "",
      display: 0
    },
    {
      type: "positions",
      name: "部门/岗位",
      editable: 1,
      value: [
        {
          departmentName: data?.positions[0]?.departmentName || "",
          jobName: data?.positions[0]?.jobName || ""
        }
      ],
      display: 0
    },
    {
      type: "phone",
      name: "手机号",
      editable: 1,
      value: [
        {
          code: data?.telCode || "",
          phone: data?.telephone || ""
        }
      ],
      display: 0
    },
    {
      type: "email",
      name: "邮箱地址",
      editable: 1,
      value: data?.email || "",
      display: 0
    }
  ];
  return options;
};

// 构造聊天数据 isGetExternalCardNew外部身份卡新接口数据结构
export const _transMsgData = (type, data, note, isGetExternalCardNew = false) => {
  if (type === "personal") {
    return {
      avatar: data.avatar || "",
      cardId: data.openid,
      openId: data.openid,
      staffName: data.title || "",
      stayOn: 0,
      nickname: "",
      comment: note.remarks || "",
      teamName: "",
      teamId: "",
      departmentId: "",
      departmentName: "",
      jobName: "",
      jobId: ""
    };
  }
  if (type === "inner") {
    if (data?.options) {
      const de = data.options.find((v) => v.type === "positions")?.value || {
        departmentId: "",
        departmentName: "",
        jobName: "",
        jobId: ""
      };
      return {
        avatar: data.options.find((v) => v.type === "avatar")?.value || "",
        cardId: `$${data.staffId}`,
        openId: data.openid,
        staffName: data.options.find((v) => v.type === "name")?.value || "",
        stayOn: 0,
        nickname: "",
        comment: note.remarks || "",
        teamName: data.teamName,
        teamId: data.teamId,
        departmentId: de?.departmentId || "",
        departmentName: de?.departmentName || "",
        jobName: de?.jobName || "",
        jobId: de?.jobId || ""
      };
    }
    return {
      avatar: data.avatar || "",
      cardId: data.cardId,
      openId: data.openId,
      staffName: data.name || "",
      stayOn: 0,
      nickname: "",
      comment: note.remarks || "",
      teamName: data.team,
      teamId: data.teamId,
      departmentId: data?.position[0]?.departmentId || "",
      departmentName: data?.position[0]?.departmentName || "",
      jobName: data?.position[0]?.jobName || "",
      jobId: ""
    };
  }
  if (type === "platform") {
    if (data?.options) {
      const de = data.options.find((v) => v.type === "positions")?.value || {
        departmentId: "",
        departmentName: "",
        jobName: "",
        jobId: ""
      };
      return {
        avatar: data.options.find((v) => v.type === "avatar")?.value || "",
        cardId: `PT${data.staffId}`,
        openId: data.openid,
        staffName: data.options.find((v) => v.type === "name")?.value || "",
        stayOn: 0,
        nickname: "",
        comment: note.remarks || "",
        teamName: data.teamName,
        teamId: data.teamId,
        departmentId: de?.departmentId || "",
        departmentName: de?.departmentName || "",
        jobName: de?.jobName || "",
        jobId: de?.jobId || ""
      };
    }
    return {
      avatar: data.avatar || "",
      cardId: data.cardId || data.card_id,
      openId: data.openId,
      staffName: data.name || "",
      stayOn: 0,
      nickname: "",
      comment: note.remarks || "",
      teamName: data.team,
      teamId: data.teamId,
      departmentId: data?.position?.[0]?.departmentId || "",
      departmentName: data?.position?.[0]?.departmentName || "",
      jobName: data?.position?.[0]?.jobName || "",
      jobId: ""
    };
  }
  if (type === "outer") {
    if (isGetExternalCardNew) {
      const position = data.options?.find((v) => v.type === "positions");
      return {
        avatar: data.options?.find((v) => v.type === "avatar")?.value || "",
        name: data.options?.find((v) => v.type === "name")?.value || "",
        cardId: data.cardId,
        openId: data.openId,
        staffName: data.options?.find((v) => v.type === "name")?.value || "",
        stayOn: 0,
        nickname: "",
        comment: note.remarks || "",
        teamName: data.team,
        teamId: data.internal_teamId || "",
        departmentId: position[0]?.departmentId || "",
        departmentName: position[0]?.departmentName || "",
        jobName: position[0]?.jobName || "",
        jobId: position[0]?.jobId || ""
      }
    } else {
      const position = data?.positions ? data.positions : data.position;
      return {
        avatar: data.avatar || "",
        cardId: data.cardId,
        openId: data.openId,
        staffName: data.name || "",
        stayOn: 0,
        nickname: "",
        comment: note.remarks || "",
        teamName: data.team,
        teamId: data.internal_teamId || "",
        departmentId: position[0]?.departmentId || "",
        departmentName: position[0]?.departmentName || "",
        jobName: position[0]?.jobName || "",
        jobId: position[0]?.jobId || ""
      };
    }
  }
};

// 转换数据模型通用
const _transDataForEdit = (cardType, data, isEditPage) => {
  if (cardType === "personal") {
    return {
      avatar: data?.avatar || "",
      name: data?.title || "",
      linkId: data.link_id,
      cardId: data.openid,
      openid: data.openid,
      teamId: "",
      teamLogo: "",
      teamName: "",
      options: _transPersonal(data)
    };
  }
  if (cardType === "inner") {
    return {
      avatar: data.options.find((v) => v.type === "avatar")?.value || "",
      name: data.options.find((v) => v.type === "name")?.value || "",
      linkId: "",
      cardId: `$${data.staffId}`,
      openid: data.openid,
      teamId: data.teamId,
      teamLogo: data.teamLogo || logoIcon,
      teamName: data.teamName,
      options: isEditPage ? data.options : _transInner(data),
      is_card: data.is_card,
      contact_auth: data.contact_auth
    };
  }
  if (cardType === "platform") {
    return {
      avatar: data.options.find((v) => v.type === "avatar")?.value || "",
      name: data.options.find((v) => v.type === "name")?.value || "",
      linkId: "",
      cardId: `PT${data.staffId}`,
      openid: data.openid,
      teamId: data.teamId,
      teamLogo: data.teamLogo || logoIcon,
      teamName: data.teamName,
      options: _transPlatform(data, isEditPage),
      is_card: data.is_card,
      contact_auth: data.contact_auth,
      teamType: data.teamType,
      isOneself: data.isOneself,
      isDeleted: data.isDeleted,
      is_disable: data.is_disable,
      member_position: data.member_position,
    };
  }
  if (cardType === "outer") {
    return {
      avatar: data.options.find((v) => v.type === "avatar")?.value || "",
      name: data.options.find((v) => v.type === "name")?.value || "",
      linkId: "",
      cardId: data.cardId,
      openid: data.openId,
      teamId: data.internal_teamId || "",
      teamLogo: data.logo || logoIcon,
      teamName: data.team,
      options: _transOuter(data),
      is_card: data.is_card,
      contact_auth: data.contact_auth
    };
  }
};

export const cardDataForEdit = async (cardId, isEditPage = false, myId?) => {
  const type = cardIdType(cardId);
  const data = await _checkCardIdApi(cardId, myId);
  const res = _transDataForEdit(type, data, isEditPage);
  return res;
};

export const cardData = async (cardId, myId?) => {
  const type = cardIdType(cardId);
  const data = await _checkCardIdApi(cardId, myId);
  const res = _transData(type, data);
  return res;
};

export const cardRemark = async (cardId) => {
  const { data } = await getCardRemark({ cardId });
  return data.data;
};

export const cardRelation = async (cardId, myId) => {
  const result = {checkPair:false, rela:'' }
  if (cardId === myId) {
    return result;
  }
  const { data } = await searchPairs({ mains: [myId], peers: [cardId] });
  if (data.data?.rela) {
    const kys = Object.keys(data.data.rela);
    return {checkPair:true, rela:kys[0] };
  }
  // 在同个外部组织下的两个人定义为同事关系
  if (cardIdType(cardId) === "outer" && cardIdType(myId) === "outer") {
    const cardInfo = await _checkCardIdApi(cardId, myId);
    if (getCards() && getCards()?.find((v) => v.uuid === myId)?.internal_team_id === cardInfo.internal_teamId) {
      return {checkPair:false, rela: "CO_WORKER" };
    }
    return result;
  }
  // 在同一组织的内部身份为同事关系
  if (cardIdType(cardId) === "inner" && cardIdType(myId) === "inner") {
    const cardInfo = await _checkCardIdApi(cardId, myId);
    if (getStaff() && getStaff()?.find((v) => v.uuid === myId)?.teamId === cardInfo.teamId) {
      return {checkPair:false, rela: "CO_WORKER" };
    }
    return result;
  }
  // 同一组织的内部身份或者平台身份为平台关系
  if ((cardIdType(cardId) === "inner" || cardIdType(cardId) === "platform") && (cardIdType(myId) === "inner" || cardIdType(myId) === "platform")) {
    const cardInfo = await _checkCardIdApi(cardId, myId);
    if (getStaff(true) && getStaff(true)?.find((v) => v.uuid === myId)?.teamId === cardInfo.teamId) {
      return {checkPair:false, rela: "PLATFORM_FRIEND" };
    }
    return result;
  }
  // 临时关系.商务关系
  if ((cardIdType(myId) === "personal" && cardIdType(cardId) === "outer") || (cardIdType(cardId) === "personal" && cardIdType(myId) === "outer")) {
    return {checkPair:false, rela: "BUSINESS" };
  }
  return result;
};

// 构造聊天信息
export const combMsgInfo = async (cardId, myId, rela?) => {
  console.log('====>combMsgInfo', cardId, myId, rela);
  const cardData = await _checkCardIdApi(cardId, myId);
  const myData = await _checkCardIdApi(myId, cardId);
  if (!rela) {
    rela = (await cardRelation(cardId, myId)).rela;
  }
  const noteInfo = await cardRemark(cardId);
  console.log('>noteInfo',noteInfo)
  const transCradData = _transMsgData(cardIdType(cardId), cardData, noteInfo, true);
  // 后端需要 departmentId 转为 string 类型
  transCradData.departmentId = transCradData.departmentId == null ? `${transCradData.departmentId}` : '';

  const transMyData = _transMsgData(cardIdType(myId), myData, {
    remarks: "",
    describe: ""
  }, true);
  // 后端需要 departmentId 转为 string 类型
  transMyData.departmentId = transMyData.departmentId == null ? `${transMyData.departmentId}` : '';

  const params = {
    main: myId,
    peer: cardId,
    origin: rela,
    team: transMyData.teamId,
    attachment: {
      member: [transMyData, transCradData],
      creatorCardId: myId,
      relation: rela
    }
  };
  return params;
};

export const getProjectList = async (id, teamId) => {
  const { data } = await projectList({ idStaff: id }, teamId);
  return data.data;
};

export const getRelatedOrganize = async (params) => {
  const { data } = await relatedOrganize(params);
  return data.data;
};

export const getRelatedBusiness = async (params) => {
  const { data } = await relatedBusiness(params);
  return data.data;
};

export const relatedOrgInfo = async (id) => {
  const { data } = await getRelatedOrg(id);
  return data.data;
};

export const getAccountsDetailData = async (id) => {
  const { data } = await getAccountsDetail(id);
  return data;
};

export const tranStaffs = (arr, key) => {
  let res = [];
  if (!arr?.length) return res;
  if (key === 'id') {
    res = arr?.map(item => {
      if (cardIdType(item.cardId) === 'inner') {
        // return item.cardId?.substring(1);
        return item.cardId;
      } else if (cardIdType(item.cardId) === 'outer') {
        return item.cardId;
      } else {
        return item.cardId;
      }
    })
  } else if (key === 'name') {
    res = arr?.map(item => item.name)
  } else if (key === 'inner') {
    res = arr?.map(item => {
      if (/^[0-9]+$/.test(item)) {
        return '$' + item;
      } else if (cardIdType(item) === 'outer') {
        return item;
      } else {
        return item;
      }
    })
  } else if (key === 'platform') {
    res = arr?.map(item => {
      if (/^[0-9]+$/.test(item)) {
        return 'PT' + item;
      } else if (cardIdType(item) === 'outer') {
        return item;
      } else {
        return item;
      }
    })
  } else if (key === 'cardId') {
    res = arr?.map(item => item.cardId);
  }
  console.log(res);

  return res;
}
