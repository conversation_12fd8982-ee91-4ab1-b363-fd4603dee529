<template>
  <div id="website-index" :class="['page-container', { pc: !isMobile }]">
    <KyyHeader ref="header" :title="pageInfo?.title" :nav-data="navData" />

    <div v-if="loading" class="flex-center mt-25">
      <van-loading type="spinner" />
    </div>
    <div v-if="componentList.length" class="components-list">
      <component
        :is="item.config.tag"
        v-for="item in componentList"
        :key="item.config.renderKey"
        :data="item.formModel"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {getPage, getPortalHome, getTemplate} from "@/api/square/website";
import { onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import { computed } from "vue";
import { navComponent } from "@/views/square/website/config";
import type {Page} from "@/api/square/models/website";
import { Loading as VanLoading } from "vant";
import type { WidgetItem } from "@/views/square/website/types";
import { isMobile } from "@/views/square/utils";

const route = useRoute();
// let { id, teamId } = route.query;
const isApp = ref(false);

const init = async () => {
  isApp.value = window.navigator.userAgent.includes("RingkolApp");

  console.log(route.query)
  getInfo();
  getHomeInfo();
  await getTplInfo();
};

onMounted(() => {
  init();
});

// 组件列表
const pageInfo = ref<Page>();
// 导航组件
const navData = ref();
const componentList = computed(() => {
  if (!pageInfo.value?.content) return [];

  try {
    const list = JSON.parse(pageInfo.value.content);
    return list.filter((v: WidgetItem) => v.config.show);
  } catch (error) {
    console.log(error);
    return [];
  }
});

// 页面信息
const loading = ref(false);
let count = 0;
const getInfo = async () => {
  const { id } = route.query;
  if (!id) return;

  count++;
  loading.value = true;
  const data = await getPage(id as string);

  count--;
  if (count <= 0) loading.value = false;
  if (!route.query.teamId) {
    document.title = data.page.name as string;
  }
  pageInfo.value = data.page;

  try {
    navComponent.formModel.items = JSON.parse(data.menu || '[]');
    if (data.menu) navData.value = navComponent;
  } catch (e) {
    console.log(e);
  }
};

const getHomeInfo = async () => {
  const { teamId } = route.query;
  if (!teamId) return;

  count++;
  loading.value = true;
  const data = await getPortalHome(teamId as string);

  count--;
  if (count <= 0) loading.value = false;
  document.title = data.name as string;
  pageInfo.value = data;

  try {
    navComponent.formModel.items = JSON.parse(data.menu || '[]');
    if (data.menu) navData.value = navComponent;
  } catch (e) {
    console.log(e);
  }
}

// 获取模板信息
const getTplInfo = async () => {
  const { tplId } = route.query;
  if (!tplId) return;

  count++;
  loading.value = true;
  const data = await getTemplate(tplId as string);

  count--;
  if (count <= 0) loading.value = false;
  document.title = data.title as string;
  pageInfo.value = data;

  try {
    navComponent.formModel.items = JSON.parse(data.menu || '[]');
    if (data.menu) navData.value = navComponent;
  } catch (e) {
    console.log(e);
  }
}

watch(
  () => route.query,
  (query) => {
    // id = query.id;
    // teamId = query.teamId;
    init();
  }
);
</script>

<script lang="ts">
import KyyHeader from "./widgets/Header.vue";
import KyyText from "./widgets/KyyText.vue";
import KyyImage from "./widgets/Image.vue";
import KyyMap from "./widgets/Map.vue";
import KyyRichText from "./widgets/RichText.vue";
import KyyImageMulti from "./widgets/ImageMulti.vue";
import KyySwiper from "./widgets/Swiper.vue";
import KyyArticle from "./widgets/Article.vue";
import KyyImageText from "@/views/square/website/widgets/ImageText.vue";
import KyyBtn from "@/views/square/website/widgets/Btn.vue";

export default {
  components: {
    KyyHeader,
    KyyText,
    KyyImage,
    KyyMap,
    KyyRichText,
    KyyImageMulti,
    KyySwiper,
    KyyArticle,
    KyyImageText,
    KyyBtn,
  },
};
</script>

<style lang="less" scoped>
.page-container {
  background-color: #f1f2f5;
  //max-width: 375px;
  margin: 0 auto;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;

  // :deep(.van-popup) {
  //   width: 50%;
  // }

  &.pc {
    max-width: 375px;
    box-shadow: rgba(33, 37, 41, 0.075) 0px 2px 4px 0px;
  }
}

.widget-wrap {
  //margin-top: 12px;
  padding: 16px;
  background-color: #fff;
  font-size: 14px;
  font-weight: 400;
  color: #13161b;
  line-height: 22px;
  cursor: pointer;

  .empty-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    .empty-img {
      width: 64px;
      height: 64px;
      background-color: lightblue;
    }
    .tips {
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      color: #717376;
      line-height: 22px;
      margin-top: 4px;
    }
  }
}
</style>
