<template>
  <div>
    <div class="text-[#1A2139] text-16 leading-24 font-600">
      <span>1.</span>
      {{ t('activity.activity.basic') }}
    </div>
    <t-form
      ref="formRef"
      class="activity-basic-info-form !mt-16 w-608"
      :data="activityFormData.basic"
      :rules="rules"
      label-align="top"
      scroll-to-first-error="smooth"
    >
      <!--活动归属-->
      <t-form-item :label="t('activity.activity.teamId_label')" name="teamId">
        <activity-team-select
          :value="activityFormData.basic.teamId"
          :disabled="isScene || published"
          @change="onTeamChange"
        />
      </t-form-item>

      <!--活动主题-->
      <t-form-item :label="t('activity.activity.subject_label')" name="subject">
        <t-textarea
          v-model="activityFormData.basic.subject"
          :placeholder="t('activity.activity.subject')"
          :autosize="{ minRows: 1 }"
          maxlength="50"
          :show-limit-number="true"
          clearable
          style="padding-right: 45px"
        />
      </t-form-item>

      <!--活动类型-->
      <t-form-item :label="t('activity.activity.categories')" name="categoryId">
        <activity-category-select v-model:value="activityFormData.basic.categoryId" @change="onCategoryChange" />
      </t-form-item>

      <!--主题图片-->
      <t-form-item :label="t('activity.activity.assetUrl')" name="assetUrl">
        <activity-theme-image-upload
          v-model:value="activityFormData.basic.assetUrl"
          @change="assetUrlUploaded = true"
        />
      </t-form-item>

      <!--活动时间-->
      <t-form-item
        class="activity-duration-form-item"
        :label="t('activity.activity.durationObj_label')"
        name="duration"
      >
        <t-form-item class="!mr-0" name="duration.startTime" :required-mark="false">
          <t-date-picker
            v-model="startTime"
            class="w-300"
            enable-time-picker
            clearable
            format="YYYY-MM-DD HH:mm"
            :placeholder="t('activity.activity.startTime')"
            :popup-props="{
              placement: 'left',
            }"
            :default-time="moment.unix(currentTimestamp).format('HH:mm')"
            @change="onChangeStartTime"
          />
        </t-form-item>

        <t-form-item class="activity-duration-end-time-form-item" name="duration.endTime" :required-mark="false">
          <t-tooltip v-if="!startTime" content="请先选择开始时间">
            <t-date-picker
              v-model="endTime"
              class="w-300 !ml-8"
              enable-time-picker
              clearable
              format="YYYY-MM-DD HH:mm"
              disabled
              :placeholder="t('activity.activity.endTime')"
              :disable-date="{
                before: moment(startTime).add(-1, 'day').format('YYYY-MM-DD HH:mm:ss'),
              }"
              :popup-props="{
                placement: 'left',
              }"
            />
          </t-tooltip>

          <t-date-picker
            v-else
            v-model="endTime"
            class="w-300 ml-8"
            enable-time-picker
            clearable
            format="YYYY-MM-DD HH:mm"
            :placeholder="t('activity.activity.endTime')"
            :disable-date="{
              before: moment(startTime).add(-1, 'day').format('YYYY-MM-DD HH:mm:ss'),
            }"
            :popup-props="{
              placement: 'left',
            }"
          />
        </t-form-item>
      </t-form-item>

      <!--活动地点-->
      <t-form-item :label="t('activity.activity.location_label')" name="location.title">
        <activity-location-input v-model:location="activityFormData.basic.location" />
      </t-form-item>

      <!--活动主办方-->
      <t-form-item
        class="activity-sponsor-form-item"
        label="活动组织单位"
        name="sponsor"
        label-align="left"
      >
        <activity-sponsor-selector ref="activitySponsorSelectorRef" @submit="onSponsorSubmit" />
      </t-form-item>

      <t-form-item v-for="(sponsorItemArr, index) in allSponsors" :key="index" :label="sponsorItemArr[0].title">
        <div class="flex items-center gap-8">
          <div class="w-580 px-12 py-4 border-1 border-solid border-[#D5DBE4] rounded-4">
            <div class="flex items-center gap-8 flex-wrap">
              <div
                v-for="sponsorItem in sponsorItemArr.filter((item) => !_.isEmpty(item.name))"
                :key="sponsorItem.title"
                class="px-8 py-2 rounded-4 bg-[#ECEFF5] flex items-center"
              >
                <div class="ml-4 leading-20 max-w-512 truncate">{{ sponsorItem.name }}</div>
                <img
                  class="ml-8 cursor-pointer"
                  src="@renderer/assets/activity/icon_error.svg"
                  alt=""
                  @click="clearSponsorItem(sponsorItem)"
                >
              </div>

              <t-button
                class="bg-[#EAECFF] h-24 pl-8 pr-12"
                variant="outline"
                theme="primary"
                @click="activitySponsorSelectorRef.open(sponsorItemArr[0].title)"
              >
                <img src="@renderer/assets/activity/icon_add_blue.svg" alt="">
                <span class="ml-2">添加</span>
              </t-button>
            </div>
          </div>
          <img
            class="cursor-pointer"
            src="@renderer/assets/activity/icon_clean_danger.svg"
            alt=""
            @click="clearSponsorItemArr(sponsorItemArr)"
          >
        </div>
      </t-form-item>

      <!--参与人员范围-->
      <t-form-item :label="t('activity.activity.range')" name="actorScope">
        <t-checkbox-group :value="actorScope" :disabled="published" @change="onActorScopeChange">
          <t-checkbox key="Publish" value="Publish">{{ t('activity.activity.openRegister') }}</t-checkbox>
          <t-checkbox key="Internal" value="Internal">
            {{ isPersonal ? '我的好友' : '组织架构' }}
          </t-checkbox>
        </t-checkbox-group>
      </t-form-item>
    </t-form>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, watchEffect } from 'vue';
import { useI18n } from 'vue-i18n';
import moment from 'moment';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import _ from 'lodash';
import ActivityTeamSelect from './ActivityTeamSelect.vue';
import ActivityCategorySelect from '@/views/activity/create/components/ActivityCategorySelect.vue';
import ActivityThemeImageUpload from '@/views/activity/create/components/ActivityThemeImageUpload.vue';
import ActivityLocationInput from '@/views/activity/create/components/ActivityLocationInput.vue';
import { activityRemoveAllActor, getAssetsList } from '@/api/activity';
import ActivitySponsorSelector from '@/views/activity/create/components/ActivitySponsorSelector.vue';
import { computedActivitySponsors, initialRegisterForms } from '@/views/activity/utils';
import { getOpenid } from '@/utils/auth';
import emitter from '@/utils/MittBus';
import { useCurrentTimestamp } from '@/hooks/useCurrentTimestamp';
import { useActivityStore } from '@/views/activity/store';

const { t } = useI18n();

const { published } = defineProps({
  // 活动是否已经发布
  published: {
    type: Boolean,
    default: false,
  },
});

const formRef = ref(null);
const activitySponsorSelectorRef = ref(null);

const activityStore = useActivityStore();

// 活动主题图片是否已主动上传标志
const assetUrlUploaded = ref(false);

// 活动表单数据
const activityFormData = inject('activityFormData');
const selectedTeam = inject('selectedTeam');
const isPersonal = inject('isPersonal');
const isScene = inject('isScene');
const isProfessional = inject('isProfessional');

const isCreate = computed(() => !activityFormData.id);

// 在成员管理中是否添加过成员（工作人员中只有当前创建者时，不算有添加成员）
const hasMembers = computed(() => {
  if (activityFormData.members.actors.length) {
    return true;
  }
  if (activityFormData.members.staffs.length) {
    return activityFormData.members.staffs.length > 1 || activityFormData.members.staffs[0].openId !== getOpenid();
  }
  if (isProfessional) {
    if (activityFormData.members.proposers.length || activityFormData.members.collaborators.length) {
      return true;
    }
  }
  return false;
});

// 活动开始时间（特殊处理字段）
const startTime = computed({
  get() {
    return activityFormData.basic.duration.startTime
      ? moment.unix(activityFormData.basic.duration.startTime).format('YYYY-MM-DD HH:mm')
      : null;
  },
  set(val) {
    activityFormData.basic.duration.startTime = moment(val).unix();
  },
});

// 活动结束时间（特殊处理字段）
const endTime = computed({
  get() {
    return activityFormData.basic.duration.endTime
      ? moment.unix(activityFormData.basic.duration.endTime).format('YYYY-MM-DD HH:mm')
      : null;
  },
  set(val) {
    activityFormData.basic.duration.endTime = moment(val).unix();
  },
});

// 获取当前时间戳，用于默认时间
const { currentTimestamp } = useCurrentTimestamp();

// 处理活动主办方展示列表数据
const allSponsors = computed(() => computedActivitySponsors(activityFormData.basic.sponsor));

// 处理参与人员范围值，将checkbox应用为单选的数值
const actorScope = computed({
  get() {
    return [activityFormData.basic.actorScope];
  },
  set() {
    return [activityFormData.basic.actorScope];
  },
});

// 表单验证规则
const rules = {
  teamId: [{ required: true, message: t('activity.activity.teamId_placeholder') }],
  subject: [{ required: true, message: t('activity.activity.subject') }],
  categoryId: [{ required: true, message: t('activity.activity.activityType_placeholder') }],
  assetUrl: [{ required: true, message: t('activity.activity.assetUrl') }],
  duration: [
    {
      required: true,
    },
  ],
  'duration.startTime': [
    {
      validator: () => {
        if (!startTime.value) {
          return Promise.resolve({
            result: false,
            message: t('activity.activity.startTimePlaceholder'),
            type: 'error',
          });
        }
        return Promise.resolve({ result: true });
      },
    },
  ],
  'duration.endTime': [
    {
      validator: () => {
        if (startTime.value && !endTime.value) {
          return Promise.resolve({ result: false, message: t('activity.activity.endTimePlaceholder'), type: 'error' });
        }
        if (startTime.value && !moment(endTime.value).isAfter(moment(startTime.value))) {
          return Promise.resolve({ result: false, message: t('activity.activity.endTimeBefore'), type: 'error' });
        }
        return Promise.resolve({ result: true });
      },
    },
  ],
  'location.title': [{ required: true, message: t('activity.activity.location_placeholder') }],
};

// 活动归属切换
const onTeamChange = async (value) => {
  if (hasMembers.value || activityFormData.advanced.publishChannels.length) {
    // 如果切换归属时，在成员管理中添加过成员或选择了发布渠道，则触发清空确认

    let type;
    if (activityFormData.advanced.publishChannels.length < 1) {
      type = 1;
    } else if (!hasMembers.value) {
      type = 2;
    } else {
      type = 3;
    }
    await dataClearConfirm(type);
  } else if (activityFormData.members.staffs.length) {
    // 当成员管理没有添加过成员，但工作人员列表当中又有成员时，执行一次清空，这里是针对工作人员中只有当前创建者的情况，这个创建者是默认添加的，执行清空时不需要弹窗提示
    clearMembers();
  }

  activityFormData.basic.teamId = value;

  // 如果切换到组织，则判断对应组织是否已退出
  if (value !== getOpenid()) {
    try {
      await activityStore.checkIsInGroup(value, false, null, false, false);
    } catch {
      MessagePlugin.warning('已退出当前组织');
      activityFormData.basic.teamId = getOpenid();
      return;
    }
  } else {
    // 如果是个人创建活动，则清空报名费设置相关内容
    activityFormData.advanced.registFeeEnable = false;
    activityFormData.advanced.registFee.value = null;
  }

  // 如果是非组织内部创建活动并且不是专业版 默认将当前创建者添加到内部联系人中
  if (!isPersonal.value && !isProfessional) {
    addDefaultInnerContactor();
  }
};

// 活动类型切换
const onCategoryChange = async (categoryId) => {
  if (!categoryId) {
    return;
  }
  // 如果没有手动上传过主题图片，则获取对应类型的主题图片
  if (!assetUrlUploaded.value) {
    const res = await getAssetsList({ id: categoryId });
    activityFormData.basic.assetUrl = res?.data?.data.items?.[0]?.url;
  }
};

// 活动开始时间切换
const onChangeStartTime = () => {
  // 活动开始时间改变时如果设置了结束时间则清空活动结束时间
  if (activityFormData.basic.duration.endTime) {
    activityFormData.basic.duration.endTime = null;
  } else if (activityFormData.basic.duration.startTime) {
    // 如果没有设置活动结束时间，设置了活动开始时间，,则结束时间默认开始后2小时
    activityFormData.basic.duration.endTime = moment(activityFormData.basic.duration.startTime * 1000)
      .add(2, 'hours')
      .unix();
  }

  // 如果开启了报名，则通知高级设置组件重新校验一次报名结束时间，避免活动开始时间已经更新，但是报名结束时间的校验值未更新
  if (activityFormData.advanced.openRegist) {
    emitter.emit('validate-form-fields', { fields: ['registerTime.endTime'] });
  }
};

// 添加主办方项
const onSponsorSubmit = (sponsor) => {
  // 如果是添加新主办类型，则加入空占位
  if (!activityFormData.basic.sponsor.some((item) => item.title === sponsor.title)) {
    activityFormData.basic.sponsor.push({
      title: sponsor.title,
      name: null,
    });
  }
  activityFormData.basic.sponsor.push(sponsor);
};

// 删除主办方项
const clearSponsorItem = (sponsorItem) => {
  activityFormData.basic.sponsor = activityFormData.basic.sponsor.filter(
    (sponsor) => sponsor.title !== sponsorItem.title || sponsor.name !== sponsorItem.name,
  );
};

// 删除整个主办方类型
const clearSponsorItemArr = (sponsorItemArr) => {
  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: `确定删除 ${sponsorItemArr[0].title}？`,
    theme: 'info',
    confirmBtn: '确定删除',
    cancelBtn: '取消',
    onConfirm: () => {
      sponsorItemArr.forEach((sponsorItem) => {
        clearSponsorItem(sponsorItem);
      });

      MessagePlugin.success('删除成功');
      confirmDia.destroy();
    },
    onCancel: () => {
      confirmDia.destroy();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
    },
  });
};

// 切换参与人员范围
const onActorScopeChange = async (value, context) => {
  if (hasMembers.value) {
    // 如果切换归属时，在成员管理中添加过成员，则触发清空确认
    await dataClearConfirm(1);
  } else if (activityFormData.members.staffs.length) {
    // 执行一次清空，原因参考切换活动归属
    clearMembers();
  }

  // 使用单选模式
  if (context.type === 'uncheck') {
    activityFormData.basic.actorScope = context.option.value;
    return;
  }
  activityFormData.basic.actorScope = context.option.value;

  // 如果切换为不公开活动时，初始化报名管理相关的内容
  if (activityFormData.basic.actorScope !== 'Publish') {
    activityFormData.advanced.openRegist = false;
    activityFormData.advanced.registerTime = {
      startTime: null,
      endTime: null,
    };
    activityFormData.advanced.quota = null;
    activityFormData.advanced.registApprove = false;
    activityFormData.advanced.registerForms = _.cloneDeep(initialRegisterForms);
  }

  // 如果当前是组织内容创建活动并且不是专业版，则也将自己加入活动联系人
  if (!isPersonal.value && !isProfessional) {
    addDefaultInnerContactor();
  }
};

// 数据清空确认 type 1 清空成员 2 清空渠道 3 成员和渠道都清空
const dataClearConfirm = (type) => new Promise((resolve, reject) => {
  let clearMemberTypeText = '';

  // 根据活动是否是专业版和是否是公开活动，显示对应的提示文案
  if (isProfessional) {
    if (activityFormData.basic.actorScope === 'Publish') {
      clearMemberTypeText = '拟定人员/协作人';
    } else {
      clearMemberTypeText = '参与人员/协作人';
    }
  } else {
    clearMemberTypeText = '参与人员/工作人员';
  }

  const tip = {
    1: `修改后将清空【人员管理-${clearMemberTypeText}】中已添加的人员，确定修改？`,
    2: '修改后将清空【高级设置】中已选择的发布渠道，确定修改？',
    3: `修改后将清空【人员管理${clearMemberTypeText}】中已添加的人员和【高级设置】中已选择的发布渠道，确定修改？`,
  };

  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: tip[type],
    theme: 'info',
    confirmBtn: '确认修改',
    cancelBtn: '取消',
    onConfirm: async () => {
      // 执行清空
      clearMembers();

      confirmDia.destroy();
      resolve();
    },
    onCancel: () => {
      confirmDia.destroy();
      reject();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
      reject();
    },
  });
});

// 清空活动成员
const clearMembers = () => {
  activityFormData.members.actors = [];
  if (isProfessional) {
    // 专业版活动清空协作人和拟定人员
    activityFormData.members.proposers = [];
    activityFormData.members.collaborators = [];
  } else {
    // 标准版清空工作人员
    emitter.emit('clear-all-staffs');
  }
  activityFormData.advanced.publishChannels = [];

  // 如果不是新增数据，则需要调用接口清空所有参与人
  if (!isCreate.value) {
    // 以宏任务执行，因为下面清空接口中需要传入切换组织后的当前账号身份卡信息
    setTimeout(() => {
      activityRemoveAllActor({
        activityId: activityFormData.id,
        me: {
          openId: getOpenid(),
          teamId: selectedTeam.value.teamId,
          cardId: selectedTeam.value.uuid,
        },
      });
    }, 200);
  }
};

// 默认添加自己为内部联系人
const addDefaultInnerContactor = () => {
  const staff = {
    rowKey: `inner-${selectedTeam.value.uuid}`,
    name: selectedTeam.value.staffName,
    targetId: selectedTeam.value.uuid,
    cardId: selectedTeam.value.uuid,
    teamId: selectedTeam.value.teamId,
    openId: getOpenid(),
    type: 'INNERCONTACTOR',
  };
  emitter.emit('add-inner-contactor', staff);
};

watchEffect(() => {
  // 根据活动归属，设置归属下的身份卡信息
  if (activityFormData.basic.teamId) {
    // 个人身份创建活动，身份卡设置为空，组织身份创建则使用在组织中的uuid
    activityFormData.basic.cardId = isPersonal.value ? '' : selectedTeam.value.uuid;
  } else {
    // 清空活动归属，则把身份卡也清空
    activityFormData.basic.cardId = '';
  }
});

defineExpose({
  validate: (params) => formRef.value.validate(params),
});
</script>

<style lang="less" scoped>
:deep(.activity-basic-info-form) {
  .t-form__label {
    line-height: 22px;
    min-height: 22px;
    padding: 0;

    &.t-form__label--top {
      margin-bottom: 8px;
    }
  }

  .t-form__controls {
    min-height: 22px;
    .t-form__controls-content {
      min-height: 22px;
    }
  }

  .t-form__controls-content {
    .t-input {
      padding-left: 12px;

      &.t-is-disabled {
        border-color: #eceff5 !important;
        background: #fff !important;
        .t-input__inner {
          color: #acb3c0 !important;
        }
      }

      .t-input__limit-number {
        color: #acb3c0;
        font-size: 12px;
      }
    }

    .t-checkbox__input {
      border-radius: 50%;
    }
  }

  .t-form-item__subject {
    .t-textarea__inner {
      height: 34px;
      min-height: 34px;
    }
    .t-textarea__limit {
      display: inline !important;
      line-height: 20px;
      position: absolute;
      right: 7px;
      bottom: 7px;
      color: var(--text-kyy_color_text_5, #acb3c0);
      font-size: 12px;
    }
  }

  .activity-duration-form-item {
    .t-is-error .t-input__extra {
      position: absolute !important;
      color: var(--td-error-color) !important;
    }

    .activity-duration-end-time-form-item {
      .t-is-error .t-input__extra {
        left: 8px;
      }
    }
  }

  .activity-sponsor-form-item {
    .t-form__label {
      line-height: 32px;
    }
  }
}
</style>
