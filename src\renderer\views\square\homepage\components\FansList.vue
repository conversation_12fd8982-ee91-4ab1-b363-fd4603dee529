<template>
  <div>
    <div
      v-if="visibility"
      v-infinite-scroll="handleInfiniteOnLoad"
      :infinite-scroll-immediate-check="false"
      :infinite-scroll-distance="20"
      :infinite-scroll-disabled="loaded"
      infinite-scroll-watch-disabled="loaded"
      class="page-content"
      :style="`max-height: calc(100vh - ${listCalcHeight});`"
    >
      <PersonList :data="dataList" :show-empty="false" class="h-full">
        <template #name="{ item }">
          <div class="name-wrap">
            <div class="name line-1" v-html="item._name" />
            <div class="time w-180">{{ item.followedTime }} {{ $t('square.square.follow') }}</div>
          </div>
        </template>
        <template #desc="{ item }">
          <div class="desc line-1 pr-12">{{ item.intro }}</div>
        </template>
      </PersonList>

      <div v-if="dataList.length" class="text-center">
        <t-loading v-if="loading" :text="$t('components.infiniteLoading.loading')" size="small" />
        <span v-if="loaded">{{ $t('components.infiniteLoading.noMoreData') }}</span>
      </div>
    </div>

    <Empty v-if="!visibility" tip="该用户已设置粉丝列表不可见" />
    <Empty v-else-if="!dataList.length" name="no-fans" :tip="$t('square.square.noFansFollow')" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import to from 'await-to-js';
import PersonList from '@/views/square/components/PersonList.vue';
import { getSquareFansList } from '@/api/square/home';
import { useSquareStore } from '@/views/square/store/square';
import { formatDate } from '@/utils/date';
import Empty from '@/components/common/Empty.vue';

const props = defineProps({
  squareId: {
    type: String,
    default: '',
  },
  visibility: {
    type: Boolean,
    default: true,
  },
  listCalcHeight: {
    type: String,
    default: '80px',
  },
  stats: {
    type: Object,
    default: () => ({}),
  },
});

const store = useSquareStore();

const dataList = ref([]);
const loading = ref(false);
const loaded = ref(false);

const params = ref({
  'page.size': 20,
  'page.next_page_token': '',
  square_id: props.squareId || store.squareId,
});

const getList = async () => {
  if (loading.value || loaded.value) return;
  loading.value = true;

  try {
    const [err, res] = await to(getSquareFansList(params.value));

    if (err) {
      console.error('获取粉丝列表失败:', err);
      return;
    }

    const { squares, page } = res.data;

    // 处理数据
    const newData = (squares || []).map((v) => ({
      ...v,
      followed: !!v.followed,
      followedTime: formatDate(v.followedAt),
    }));

    dataList.value = dataList.value.concat(newData);

    // 处理分页逻辑
    if (page?.nextPageToken) {
      // 有下一页，更新 token
      params.value['page.next_page_token'] = page.nextPageToken;
    } else {
      // 没有下一页，标记为加载完成
      loaded.value = true;
    }

    // 如果返回的数据少于请求的页大小，说明没有更多数据了
    if (newData.length < params.value['page.size']) {
      loaded.value = true;
    }

  } catch (error) {
    console.error('获取粉丝列表异常:', error);
  } finally {
    loading.value = false;
  }
};

const handleInfiniteOnLoad = () => {
  if (!loaded.value && !loading.value) {
    getList();
  }
};

// 重置列表状态
const resetList = () => {
  dataList.value = [];
  loading.value = false;
  loaded.value = false;
  params.value['page.next_page_token'] = '';
};

watch(() => props.squareId, () => {
  resetList();
  if (props.visibility) {
    getList();
  }
}, { immediate: true });
</script>

<style lang="less" scoped>
.page-content {
  overflow: auto;
  position: relative;
}

.name-wrap {
  display: flex;
}
.name {
  color: var(--text-kyy-color-text-1, #1A2139);
  font-size: 16px;
  font-weight: 600;
  margin-right: 20px;
}
.time, .desc {
  font-size: 14px;
  color: var(--text-kyy-color-text-3, #828DA5);
}
</style>
