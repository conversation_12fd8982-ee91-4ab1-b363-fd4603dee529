<template>
  <div v-element-visibility="onElementVisibility" class="chat-message">
    <!-- 消息卡片 -->
    <div class="message-card">
      <!-- 消息头部 -->
      <AppCardHeader style="font-size: 16px">
        {{ getTitle(extendData?.digital_platform?.digital_uuid) }}
      </AppCardHeader>

      <!-- 消息内容 -->
      <div class="message-content">
        <!-- 用户信息 -->
        <UserInfo
          :avatar="extendData?.digital_platform?.avatar || ''"
          :company-name="extendData?.digital_platform?.name || ''"
          :apply-text="`个人会员【${extendData?.promoter?.name}】已提交活动推广申请`"
        />

        <!-- 活动信息 -->
        <ActivityCard
          section-title="申请"
          :thumbnail-url="extendData?.asset_url || ''"
          :title="extendData?.subject || ''"
          :time="time"
          :location="extendData?.address || ''"
        />

        <!-- 申请人信息 -->
        <SectionItem
          tag="个人"
          title="申请人"
          :value="extendData?.promoter?.name || ''"
        />

        <!-- 分割线 -->
        <div class="divider" />
      </div>

      <!-- 底部按钮 -->
      <ActionButtons
        :buttons="actionButtons"
        @button-click="handleButtonClick"
      />
    </div>
  </div>
  <Reject
    ref="rejectModalRef"
    @on-send="onSaveReject"
  />
</template>

<script setup lang="ts">
import { PropType, onMounted, onUnmounted, ref, computed } from 'vue';
import { AppCardHeader } from '@renderer/views/message/chat/MessageAppCard';
import { getTitle } from '@renderer/views/message/chat/msgTypeContent/AppWorks/constant';
import to from 'await-to-js';
import { queryActivityPromotion, agreeDPActivity, rejectDPActivity } from '@renderer/api/activity/platform';
import LynkerSDK from '@renderer/_jssdk';
import { vElementVisibility } from '@vueuse/components';
import { ActivityRefreshType } from '@renderer/views/message/common/constant';
import UserInfo from '../../../components/activity-promote/components/UserInfo.vue';
import ActivityCard from '../../../components/activity-promote/components/ActivityCard.vue';
import SectionItem from '../../../components/activity-promote/components/SectionItem.vue';
import ActionButtons from '../../../components/activity-promote/components/ActionButtons.vue';
import Reject from '../../../components/Reject.vue';
import { useSceneData, statusMap } from '../../../components/activity-promote/composables/useSceneData';

const { ipcRenderer } = LynkerSDK;

const props = defineProps({
  msg: { type: Object as PropType<any>, default: null },
});

const { time, extendData } = useSceneData(props);
const rejectModalRef = ref(null);

const actionButtons = computed(() => statusMap[status.value]?.buttons || []);

const handleButtonClick = (key: string) => {
  if (key === 'reject') {
    rejectModalRef.value.onOpen();
  } else if (key === 'agree') {
    handleApply('agree');
  } else if (key === 'info') {
    lookDetail();
  }
};

const lookDetail = () => {
  ipcRenderer.invoke('create-dialog', { url: `layoutActivity/activityParticipantDetailLayout/${extendData.value?.activity_id}?subject=${encodeURIComponent(extendData.value?.subject)}
  &type=aloneWindow`,
  opts: { x: 50, y: 50, width: 1296, minWidth: 1296, height: 720 } });
};

const reason = ref('');
const onSaveReject = (data) => {
  reason.value = data.reason;
  handleApply('reject');
};

const handleApply = async (opt) => {
  const params = {
    activityId: extendData.value?.activity_id,
    teamId: extendData.value?.digital_platform?.team_id,
  };
  const fn = opt === 'agree' ? () => agreeDPActivity(params) : () => rejectDPActivity(params);

  const [err, res] = await to(fn());
  if (err) {
    loadBatchStatus();
    return;
  }
  const { code } = res.data || {};
  if (code !== 0) {
    loadBatchStatus();
    return;
  }
  rejectModalRef.value.onClose();
  // loadBatchStatus();
};

const status = ref('');
const loadBatchStatus = async () => {
  const params = {
    activityId: extendData.value?.activity_id,
    teamId: extendData.value?.digital_platform?.team_id,
  };
  const [err, res] = await to(queryActivityPromotion(params, { hideMessage: true }));

  if (err) {
    return;
  }
  const { code, data } = res.data || {};
  if (code === 0) {
    status.value = data.status;
  }
};

const isVisible = ref(null);
function onElementVisibility(state) {
  isVisible.value = state;
  if (state) {
    loadBatchStatus();
  }
}

const update = (e, args) => {
  const { type, data } = args || {};
  console.error('活动通知4', type);
  if (isVisible.value && type === ActivityRefreshType.ActivityRefreshSyncDynamic && data?.activity_id === extendData.value?.activity_id) {
    loadBatchStatus();
  }
};

onMounted(() => {
  ipcRenderer.on('activity-refresh', update);
});
onUnmounted(() => {
  ipcRenderer.off('activity-refresh', update);
});
</script>

<style scoped>
.chat-message {
  width: 360px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-card {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.message-content {
  display: flex;
  flex-direction: column;
  padding: 16px 16px 0;
  gap: 12px;
}

.divider {
  height: 1px;
  background: #eceff5;
}
</style>
