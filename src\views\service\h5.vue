<template>
  <home title="另可服务详情">
    <template #content>
      <div class="page">
        <div class="header" v-show="!isRinkol && !isRingkolDesktop">
          <div class="left">
            <div class="logo">
              <!-- <img src="@/assets/img/logo24.png" alt="" /> -->
              <!-- <SvgIcon name="logo" class="icon" /> -->
              <img src="@/assets/img/new_logo_1.png" alt="" />
            </div>
            <!-- <div class="title">
              <div class="name">另可</div>
              <div class="log">另一种可能，更好</div>
            </div> -->
          </div>
          <div v-if="detailData" class="right">
            <!-- <t-button ghost
              ><a
                @click="onOpenRingkolApp"
                >打开另可</a
              >
            </t-button> -->
            <wx-open-launch-app
              v-if="isAndroid && isInWeixin"
              id="launch-btn"
              appid="wx77bf95d26088163a"
              :extinfo="extinfo"
              @error="handleErrorFn"
              @launch="handleLaunchFn"
            >
              <script is="vue:script" type="text/wxtag-template">
                <style>
                  .btn {
                    padding: 3px 12px;
                    background: #4D5EFF;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: 400;
                    text-align: center;
                    color: #ffffff;
                    line-height: 22px;
                  }
                  btn.lg {
                    padding: 5px 26px;
                  }
                </style>
                <div class="btn" @click="openApp">打开APP</div>
              </script>
            </wx-open-launch-app>
            <div v-else class="btn" @click="openApp">打开APP</div>

          </div>
          <div v-else class="right">
            <t-button ghost><a href="ringkol://ringkol.com">打开另可</a> </t-button>
          </div>
        </div>
        <div v-if="detailData" class="mian main-wd mt-64px">
          <!-- <div class="gcheader">
          <div class="left" @click="openrk">
            <div class="av" >
              <img v-if="square.avatar" :src="square.avatar" alt="" />
              <div v-else class="mav">{{ getNameContent(square.name) }}</div>
            </div>
            <div class="info">
              <div class="name">{{ square.name }}</div>
              <div class="time">{{ detailData.created_at }}</div>
            </div>
          </div>

        </div> -->

          <div v-if="detailData && currentImage" class="content">
            <div class="images" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
              <!-- <img
                v-show="currentIndex > 0"
                src="@/assets/square/icon_right2.svg"
                class="bleft"
                @click="previousImage"
              /> -->

              <!-- <transition name="slide">

              </transition> -->

              <img :key="currentImage" :src="currentImage" alt="当前图片" @click.stop="preview" />
              <!--
              <img
                v-show="currentIndex < detailData.image.length - 1"
                src="@/assets/square/icon_right2.svg"
                class="bright"
                @click="nextImage"
              /> -->

              <div class="banner">
                <span
                  v-for="(image, imageIndex) in detailData.image"
                  :key="imageIndex"
                  :class="{
                    cursor: true,
                    'banner-item': true,
                    'banner-active': currentIndex === imageIndex,
                  }"
                  @click="currentIndex = imageIndex"
                >
                </span>
              </div>
            </div>
            <div class="box">
              <div class="title">
                {{ detailData.name }}
              </div>
              <div class="cate">
                <span v-for="(cateItem, cateIndex) in detailData.category" :key="cateIndex" class="cate-item">
                  {{ cateItem.category_name }}
                </span>
              </div>

              <!-- <div id="html" v-html="detailData.content"></div> -->
              <div class="html ql-editor lk-editor lk-editor-dgz">
                <div v-html="detailData?.content"></div>
                <!-- <lk-editor ref="quill" :toolbar="false" is-preview-image @ready="onReady" @annex="onAnnex"></lk-editor> -->
              </div>
            </div>

            <!-- <div class="attachment" style="padding: 12px">
              <div class="title">附件</div>
              <div v-for="(item,index) in detailData.annex" style="display: flex;align-items: center;cursor: pointer" :key="index">
                <img :src="getFileIcon(item) || item.icon " alt="">
                <div class="line-1" style="flex:1;padding-left: 8px;">
                  <div style="font-size: 14px;color: #1A2139">{{item.file_name_short}}</div>
                  <div style="color:#ACB3C0;font-size: 12px;">{{ bytesToSize(item.size) }}</div>
                </div>
                <div style="padding-left: 10px;color: #4D5EFF;font-size: 14px;cursor: pointer" @click="download(item)">下载</div>
              </div>
            </div> -->
          </div>
        </div>
        <div v-if="isLoading && !detailData" class="noEmpty">
          <div class="boxs">
            <img src="@/assets/service/Rectangle.png" />
            <div class="tip">您想看的服务不存在或已被删除啦</div>
          </div>
        </div>
      </div>
    </template>
    <!-- <van-image-preview v-model:show="imgshow" :images="imageslist" @change="onChange">
    </van-image-preview> -->
  </home>
  <t-image-viewer v-model:visible="viewer" v-model:index="currentIndex" :images="detailData?.image" />
</template>

<script setup lang="ts">
import { showImagePreview } from 'vant';
import { onMounted, ref, computed, nextTick } from 'vue';
import { getServiceContentDetailAxios } from '@/api/service/index';
// import productDetail from './productDetail.vue';
import { useRoute } from 'vue-router';
import home from '@/views/keeppx/homeIndex.vue';
import { useAccountStore } from '@/stores/account';
import word from '@/assets/img/0.icon_file-icon-word.svg';
import excel from '@/assets/img/0.icon_file-icon-xls.svg';
import ppt from '@/assets/img/0.icon_file-icon-ppt.svg';
import pdf from '@/assets/img/0.icon_file-icon-pdf.svg';
import img from '@/assets/img/0.icon_file-icon-img.svg';
import other from '@/assets/img/0.icon_file-icon-other.svg';
import qs from 'qs';
import { downloadFile } from '@/utils/myUtils';
import { useWxConfig } from '@/hooks/useWxConfig';
import { getBaseUrl } from '@/api/requestApi';

const isAndroid = /android/.test(navigator.userAgent.toLowerCase());
const { isInWeixin, handleLaunchFn } =  useWxConfig();

const isRinkol = useAccountStore().isRingkol;
const isRingkolDesktop = useAccountStore().isRingkolDesktop;

const isLoading = ref(false);
const imgshow = ref(false);
const route = useRoute();
const detailData: any = ref(null);
const uuid: any = ref('');
const channelCode: any = ref();
onMounted(() => {
  uuid.value = route.query.id;
  document.title = '另可服务详情';
  channelCode.value = route.query.channelCode;
  getDetailRun();
});

const quill = ref<any>(null);
const onReady = () => {
  console.log('onReady');
  document.title = '另可服务详情';
  quill.value.disableEditor(true);
  // 页面渲染完成获取详情
  getDetailRun();
};

const currentIndex = ref(0);
const currentImage = computed(() =>
  detailData.value && detailData.value.image.length > 0 ? detailData.value.image[currentIndex.value] : null,
);
const imageslist = ref([]);
const onChange = () => {
  imgshow.value = false;
};

// const extinfo = computed(() => 'ringkol://ringkol.com/square-module?' + new URLSearchParams({
//   loginRequired: 'true',
//   id: detailData.value?.id as string,
//   teamId: detailData.value?.team_id as string,
//   path: 'square-post-detail',
// }).toString());

// const extinfo = computed(() => 'ringkol://ringkol.com/square-service-detail?id='+ detailData.id );
const extinfo = computed(() => 'ringkol://ringkol.com/square-service-detail?' + new URLSearchParams({
  id: detailData.value?.id as string,
  teamId: detailData.value?.team_id as string,
  package: route.query?.package as string,
}).toString());

const openApp = async () => {
  if (isAndroid) {
    // Android 直接通过唤起App
    location.href = extinfo.value;

    setTimeout(() => {
      redirectToDownload();
    }, 3000);
    return;
  }

  // 重定向到下载页
  redirectToDownload();
}
const redirectToDownload = () => {
  // console.log(`${getBaseUrl('square')}/v1/redirect_to_download${location.search}&from=post_share`);
  // location.href = `${getBaseUrl('square')}/v1/redirect_to_download${location.search}&from=post_share`;

  // const url = getBaseUrl('website')+'/mobile/downloadCenter?app=from=square-service-detail&sence=2&id=' +
  //                   detailData.value.id +
  //                   '&package=' +
  //                    (route.query?.package || 1) +
  //                   '&channel=LKsquare_service&type=open';
  const params = {
    app: encodeURIComponent(qs.stringify({from: 'square-service-detail', id:detailData.value.id })),
    package: route.query?.package || 1,
    channel: 'LKsquare_service',
    type: 'open',
    sence: 2,
  }
  const url = getBaseUrl('website')+'/downloadCenter?' + qs.stringify(params);
  console.log(url)


  location.href=url;
}
const handleErrorFn = (e: any) => {
  console.log('handleErrorFn', e);
  redirectToDownload();
}

const getDetailRun = async () => {
  // getServiceContentDetailAxios(uuid.value).then((res: any) => {
  //   console.log(res);mbox
  //   if (res.code === 0) {
  //     detailData.value = res.data;
  //     listData.value = res.data.goods;
  //     square.value = res.data.related_data;
  //   }
  // });
  let result = null;
  isLoading.value = false;
  try {
    result = await getServiceContentDetailAxios(uuid.value);
    console.log(result);
    detailData.value = result?.data;
    // nextTick(() => {
    //   let images = document.getElementById('html').querySelectorAll('img');
    //   images.forEach((img) => {
    //     img.addEventListener('click', () => {
    //       imageslist.value = [img.src];
    //       showImagePreview({
    //         images: [img.src],
    //         closeable: true,
    //       });
    //     });
    //   });
    // });
    let customData: any = [];
    console.log(result);
    detailData.value = result?.data;
    // 解析delta
    const delta = result.data.content_json || [];
    // 计算长度
    let scrollLength = 0;
    // 保存file的信息
    customData = [];
    const data = delta.filter((v: any) => {
      // 自定义custom 需要手动插入，用setcontents方法插入内部长度会计算出错导致位置错乱
      if (v.insert?.custom) {
        const data = JSON.parse(JSON.parse(v.insert.custom).attachment);
        customData.push({ length: scrollLength, value: data });
      }
      if (typeof v.insert === 'string') {
        scrollLength += v.insert.length;
      } else {
        // 自定义custom 在一开始插入长度为1，其他情况长度为2.应该是跟quill会自动生成\n有关
        const len = v.insert?.custom ? 2 : 1;
        scrollLength += len;
      }
      return !v.insert?.custom;
    });
    // 渲染内容-
    quill.value.renderContent({ ops: data });
    // 插入附件
    const quillEle = quill.value.getQuill();
    customData.forEach((v) => {
      quillEle.insertEmbed(v.length, 'custom', v.value);
    });
  } catch (error) {
    const errMsg: any = error instanceof Error ? error.message : error;
    console.log(error);
  }
  isLoading.value = true;
};

const typeList = ref<string[]>(['xlsx', 'xls', 'doc', 'docx', 'ppt', 'pptx']);

// 附件操作
const onAnnex = (events: HTMLDivElement, type: string, data: any) => {
  // console.log('onAnnex',events,type,data);
  if (!typeList.value.includes(data?.type)) return MessagePlugin.error('文件类型不支持预览');
  if (type === 'preview') {
    // 预览
    viewPreviewFile(data?.url);
  } else if (type === 'download') {
    // 下载
    window.open(data?.url);
    // downloadFile(data?.url, data?.name);
  }
};

const previousImage = () => {
  // currentIndex.value =
  //   (currentIndex.value - 1 + detailData.value?.image.length) %
  //   detailData.value?.image.length;

  currentIndex.value =
    // eslint-disable-next-line no-unsafe-optional-chaining
    detailData.value?.image.length - 1 > 0 && currentIndex.value > 0 ? currentIndex.value - 1 : 0;
};

const nextImage = () => {
  // currentIndex.value =
  //   (currentIndex.value + 1) % detailData.value?.image.length;
  currentIndex.value =
    // eslint-disable-next-line no-unsafe-optional-chaining
    detailData.value?.image.length - 1 > currentIndex.value && detailData.value?.image.length > 0
      ? currentIndex.value + 1
      : // eslint-disable-next-line no-unsafe-optional-chaining
        detailData.value?.image.length - 1;
};

let startX = 0;
const onTouchStart = (event) => {
  startX = event.touches[0].clientX; // 记录起始触摸点的X坐标
};
const onTouchMove = (event) => {
  const currentX = event.touches[0].clientX; // 当前触摸点的X坐标
  const deltaX = currentX - startX; // 计算X坐标的位移

  if (deltaX < 0) {
    console.log('向左滑动');
    if (currentIndex.value < detailData.value?.image.length - 1) {
      nextImage();
    }
  } else if (deltaX > 0) {
    console.log('向右滑动');
    if (currentIndex.value > 0) {
      previousImage();
    }
  }
};
const onTouchEnd = (event) => {
  // 清除起始触摸点的X坐标
  startX = 0;
};

const viewer = ref(false);
const imageFiles = ref([]);
const preview = (imgs: any) => {
  console.log(imgs);
  console.log(currentImage.value);
  // imageFiles.value = currentImage.value;
  viewer.value = true;
};

const openrk = () => {
  window.location.href = 'ringkol://ringkol.com/index/';
};

const getFileIcon = (item) => {
  console.log(item, '什么内容');
  let icon = other;
  // ['xlsx', 'xls', 'doc', 'docx', 'ppt', 'pptx', 'pdf', 'PDF'];
  if (item.file_name) {
    if (item.file_name.includes('pdf') || item.file_name.includes('PDF')) {
      icon = pdf;
    } else if (item.file_name.includes('ppt') || item.file_name.includes('pptx')) {
      icon = ppt;
    } else if (
      item.file_name.includes('image') ||
      item.file_name.includes('jpeg') ||
      item.file_name.includes('jpg') ||
      item.file_name.includes('png') ||
      item.file_name.includes('gif')
    ) {
      icon = item.file_name;
    } else if (
      item.file_name.includes('doc') ||
      item.file_name.includes('docx') ||
      (item?.type && item?.type.includes('word'))
    ) {
      icon = word;
    } else if (item.file_name.includes('xls') || item.file_name.includes('xlsx')) {
      icon = excel;
    }
  } else {
    icon = other;
  }
  return icon;
};

const bytesToSize = (size) => {
  const units = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  let formattedSize = size;
  let unitIndex = 0;
  while (formattedSize >= 1024 && unitIndex < units.length - 1) {
    formattedSize /= 1024;
    unitIndex++;
  }
  return `${formattedSize.toFixed(2)} ${units[unitIndex]}`;
};

const download = (item) => {
  downloadFile(item.file_name, item.file_name_short);
};
</script>

<style lang="less" scoped>
.btn {
  border-radius: 3px;
  border: 1px solid #4d5eff;
  padding: 4px 16px;
  color: #4d5eff;
  background-color: #fff;
}
.noEmpty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80%;

  color: var(--text-kyy-color-text-2, #516082);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;

  width: 100%;
  text-align: center;

  .boxs {
    img {
    }
    .tip {
      color: var(--text-kyy-color-text-2, #516082);
      text-align: center;

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
  }
}

.box {
  padding: 12px;
  padding-top: 0;
}
.html {
  color: var(--text-kyy-color-text-1, #1a2139);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  min-height: 100vh;
  :deep(img) {
    max-width: calc(100vw - 24px) !important;
  }
  :deep(.ql-editor) {
    overflow-y: initial;
  }
  :deep(.lk-toolbar) {
    display: none;
  }
}

.title {
  color: var(--text-kyy-color-text-1, #1a2139);

  /* kyy_fontSize_3/bold */
  font-family: PingFang SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
  margin-bottom: 16px;
  margin-top: 16px;
}
.cate {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  margin-bottom: 12px;
  &-item {
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_gray, #eceff5);
    padding: 0px 6px;

    color: var(--kyy_color_tag_text_gray, #516082);
    text-align: center;

    /* kyy_fontSize_1/regular */
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}
.images {
  position: relative;
  img {
    // border-radius: 8px;
    height: inherit;
    width: 100%;
    height: 250px;
    object-fit: cover; /* 保持图片原始比例，可能会留有空白区域 */
    transition: opacity 0.5s ease;
  }
  .bleft {
    position: absolute;
    left: 24px;
    top: 0;
    bottom: 0;
    margin: auto;
    transform: rotate(180deg);
    width: 48px;
    height: 48px;
    // opacity: 0;
    transition: all 0.25s linear;
  }
  .bright {
    position: absolute;
    right: 24px;
    width: 48px;
    height: 48px;
    top: 0;
    bottom: 0;
    margin: auto;
    // opacity: 0;
    transition: all 0.25s linear;
  }
  .banner {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 12px;
    &-item {
      background: #fff;
      border-radius: 50%;
      opacity: 0.7;
      width: 12px;
      height: 12px;
      transition: all 0.25s linear;
    }
    &-active {
      opacity: 1;
      width: 20px;
      border-radius: 6px;
      background: var(--bg-kyy-color-bg-light, #fff);
      transition: all 0.25s linear;
    }
  }
  &:hover {
    // .bright {
    //   opacity: 1;
    //   transition: all 0.25s linear;
    // }
    // .bleft {
    //   opacity: 1;
    //   transition: all 0.25s linear;
    // }
  }
}

.logo {
  height: 31px;
  img {
    height: inherit;
  }
}

.page {
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  background: #fff;
  .header {
    position: fixed;
    z-index: 2;
    height: 64px;
    left: 0;
    right: 0;
    display: flex;
    padding: 16px;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-kyy-color-bg-deep, #f5f8fe);
    gap: 12px;
    align-self: stretch;
    .left {
      display: flex;
      align-items: center;
      .icon {
        display: flex;
        width: 32px;
        height: 32px;
        justify-content: center;
        align-items: center;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .title {
        margin-left: 16px;
        .name {
          color: var(--lingke-black, #1a2139);
          font-feature-settings: 'clig' off, 'liga' off;

          /* kyy_fontSize_3/bold */
          font-family: PingFang SC;
          font-size: 17px;
          font-style: normal;
          font-weight: 600;
          line-height: 26px; /* 152.941% */
        }
        .log {
          color: var(--lingke-gray-3, #828da5);
          font-feature-settings: 'clig' off, 'liga' off;

          /* kyy_fontSize_2/regular */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }
    }
  }
  .con-box {
    width: 100%;
    padding: 12px 16px 24px 16px;
    .square-box {
      width: 100%;
      height: 64px;
      display: flex;
      border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);

      .av {
        display: flex;
        width: 44px;
        height: 44px;
        justify-content: center;
        align-items: center;
        border-radius: var(--kyy_avatar_radius_full, 999px);
        margin-right: 12px;
        img {
          width: 100%;
          height: 100%;
          border-radius: var(--kyy_avatar_radius_full, 999px);
        }
        .mav {
          width: 100%;
          height: 100%;
          color: white;
          font-size: 14px;
          text-align: center;
          line-height: 44px;
          border-radius: var(--kyy_avatar_radius_full, 999px);
          background-color: var(--kyy_color_tag_text_brand, #4d5eff);
        }
      }
      .info {
        .name {
          color: var(--text-kyy-color-text-1, #1a2139);

          /* kyy_fontSize_3/bold */
          font-family: PingFang SC;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px; /* 150% */
        }
        .time {
          color: var(--text-kyy-color-text-3, #828da5);

          /* kyy_fontSize_2/regular */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
          margin-top: 2px;
        }
      }
    }
    .title-box {
      margin-top: 12px;
      .tag {
        display: flex;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_brand, #eaecff);
        color: var(--kyy_color_tag_text_brand, #4d5eff);
        text-align: center;
        float: left;
      }
      .tag2 {
        display: flex;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_warning, #ffe5d1);
        color: var(--kyy_color_tag_text_warning, #fc7c14);
        text-align: center;
        float: left;
      }
      .hot-tag {
        display: flex;
        height: 20px;
        width: 20px;
        padding: 0px 4px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_error, #f7d5db);
        color: var(--kyy_color_tag_text_error, #d54941);
        text-align: center;

        /* kyy_fontSize_1/regular */
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
        float: left;
        margin-right: 4px;
      }
      .con {
        color: var(--text-kyy-color-text-1, #1a2139);

        /* kyy_fontSize_3/bold */
        font-family: PingFang SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
      }
    }
    .v-time {
      margin-top: 8px;
      margin-bottom: 16px;
      color: var(--text-kyy-color-text-3, #828da5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .cont {
      color: var(--text-kyy-color-text-1, #1a2139);
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .hot2 {
      width: 16px;
      height: 16px;
      float: left;
      margin-right: 4px;
      margin-top: 2px;
    }
  }
  .tables {
    width: 100%;
    padding: 0 16px;
    margin-bottom: 20px;
    .table-title {
      display: flex;
      align-items: center;
      margin: 12px 0;
      .tag {
        width: var(--checkbox-kyy-radius-checkbox, 4px);
        height: 14px;
        border-radius: 999px;
        background: var(--brand-kyy-color-brand-default, #4d5eff);
      }
      .cc {
        color: var(--text-kyy-color-text-1, #1a2139);

        /* kyy_fontSize_2/bold */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
        margin-left: 8px;
      }
    }
    .list {
      .item {
        display: flex;
        padding: 12px 0px;
        align-items: center;
        border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
        height: 102px;

        .ming {
          display: flex;
          width: 72px;
          height: 72px;
          justify-content: center;
          align-items: center;
          flex-shrink: 0;
          border-radius: 8px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 8px;
          }
        }
        .prod {
          margin-left: 12px;
          .name {
            .tc {
              color: var(--text-kyy-color-text-1, #1a2139);

              /* kyy_fontSize_2/regular */
              font-family: PingFang SC;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px; /* 157.143% */
              width: calc(100vw - 120px);
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
          }
          .num {
            color: var(--text-kyy-color-text-3, #828da5);
            font-feature-settings: 'clig' off, 'liga' off;

            /* kyy_fontSize_2/regular */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            margin-top: 4px;
          }
        }
      }
    }
  }
}

.nores {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200px;
  img {
    width: 133px;
    height: 112px;
  }
  .text {
    color: var(--text-kyy-color-text-2, #516082);
    text-align: center;

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    margin-top: 12px;
  }
}
</style>
