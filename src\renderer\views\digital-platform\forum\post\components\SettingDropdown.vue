<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ref } from 'vue';
import to from 'await-to-js';
import { DropdownOption, MessagePlugin } from 'tdesign-vue-next';

import CropperDialog from '@/views/square/components/CropperDialog.vue';
import { useForumStore } from '../../store';
import { getAnonDetail, setMyDetail } from '@renderer/api/forum/user';
import { getImageInfo } from '@renderer/views/square/utils/upload';
import AnonSetupDialog from '@/views/digital-platform/forum/components/AnonSetupDialog.vue';
import { AxiosError } from 'axios';

const emit = defineEmits(['change:cover', 'change:anonymous']);

const router = useRouter();
const forumStore = useForumStore();

const anonSetupDialogRef = ref<InstanceType<typeof AnonSetupDialog>>(null);

const getAnonDetailStats = async () => {
  const [err, res] = await to(getAnonDetail(forumStore.platformType || 'uni'));
  if (err) {
    const errContent = err as AxiosError<{ message: string }>;

    MessagePlugin.error(errContent?.response?.data?.message || errContent?.message);
    return;
  }
  anonSetupDialogRef.value?.open({
    avatarUrl: res.data?.data?.avatar || '',
    nickname: res.data?.data?.name || '',
  });
};

const onMoreClick = (option: DropdownOption) => {
  switch (option.value) {
    case 1:
      router.push({ name: 'forumSetting' });
      break;
    case 2:
      if (forumStore.platformType === 'uni') {
        getAnonDetailStats();
      }

      break;
    case 3:
      cropperRef.value.open(imgCropper.value);
      break;
    default:
      break;
  }
};

// 裁剪海报图片
const imgCropper = ref(forumStore.currCard.cover);
const cropperRef = ref(null);

// 格式校验
const supportFormats = ['image/jpg', 'image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/bmp'];
const accept = ref(supportFormats.join(','));
const MAX_FILE_SIZE_MB = 10;

const beforeUpload = async (file) => {
  const isImg = supportFormats.includes(file.type);
  if (!isImg) {
    MessagePlugin.warning(`请选择${supportFormats.map((v) => v.split('/')[1]).join('/')}格式的图片`);
    return false;
  }

  const res = await getImageInfo(file.raw);
  const overSize = res.size > 1024 * 1024 * MAX_FILE_SIZE_MB;
  if (overSize) {
    MessagePlugin.warning(`请选择小于${MAX_FILE_SIZE_MB}MB的图片`);
    return false;
  }

  return true;
};

// 裁剪后上传
const cropperConfirm = async (url: string) => {
  imgCropper.value = url;

  const [err] = await to(
    setMyDetail(forumStore?.platformType, {
      bg_cover: url,
    }),
  );
  if (err) return;

  forumStore.currCard.cover = url;
  emit('change:cover', url);
};

// 更新匿名身份
const handleAnonConfirm = async (data: { name: string; avatar: string }) => {
  emit('change:anonymous', data);
};
</script>

<template>
  <span>
    <t-dropdown
      placement="bottom-right"
      :popup-props="{ overlayInnerClassName: 'd-forum-o-setting' }"
      @click="onMoreClick"
    >
      <div class="icon-wrap">
        <iconpark-icon name="iconedit" class="icon" />
        <iconpark-icon name="iconarrowdown" class="icon" />
      </div>

      <t-dropdown-menu>
        <t-dropdown-item :value="1" class="max-w-none!">编辑个人资料</t-dropdown-item>
        <t-dropdown-item
          v-if="forumStore.platformType === 'uni' && forumStore.hasAnonymous"
          :value="2"
          class="max-w-none!"
          >编辑匿名资料</t-dropdown-item
        >
        <t-dropdown-item :value="3" class="max-w-none!">更换封面</t-dropdown-item>
      </t-dropdown-menu>
    </t-dropdown>

    <CropperDialog
      ref="cropperRef"
      :options="{ aspectRatio: 24 / 13 }"
      :accept="accept"
      :size-limit="10"
      :before-upload="beforeUpload"
      @confirm="cropperConfirm"
    />

    <AnonSetupDialog ref="anonSetupDialogRef" @confirm="handleAnonConfirm" />
  </span>
</template>

<style lang="less" scoped>
.icon-wrap {
  display: flex;
  cursor: pointer;
  color: #828da5;
  &:hover .icon {
    color: #4d5eff;
  }
}

.icon {
  font-size: 20px;
}
</style>

<style lang="less">
.d-forum-o-setting {
  width: 144px;
  padding: 4px !important;
  .t-dropdown__item {
    padding: 0 8px !important;
    height: 32px !important;
  }
}
</style>
