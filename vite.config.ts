import { fileURLToPath, URL } from 'node:url'
import path from 'path'

import { defineConfig, splitVendorChunkPlugin } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
// import pxToVW from 'postcss-px-to-viewport-8-plugin'
// import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { visualizer } from 'rollup-plugin-visualizer';
import viteCompression from 'vite-plugin-compression';
import Icons from 'unplugin-icons/vite'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import IconsResolver from 'unplugin-icons/resolver'
import Components from 'unplugin-vue-components/vite'
import Unocss from 'unocss/vite'
import { viteStaticCopy } from 'vite-plugin-static-copy'

const resolve = (dir: string) => path.join(__dirname, dir);

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    host: '0.0.0.0'
  },
  envDir: "env",
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },

  build: {
    sourcemap: false,
    chunkSizeWarningLimit: 1500
  },

  plugins: [
    // 复制文件
    viteStaticCopy({
      targets: [
        {
          src: 'public/AOgh4cXF0j.txt',
          dest: 'pages/webview'
        }
      ]
    }),
    vue({
      template: {
        compilerOptions: {
          // fix: Failed to resolve component: iconpark-icon
          isCustomElement: (tag) => ["iconpark-icon", 'wx-open-launch-app'].includes(tag),
        },
      },
    }),
    vueJsx(),
    // 生成 svg 图标
    Icons({
      compiler: 'vue3',
      customCollections: {
        svg: FileSystemIconLoader('src/assets/svg'),
        'svg-square': FileSystemIconLoader('src/assets/svg/square'),
        'svg-empty': FileSystemIconLoader('src/assets/svg/empty'),
      },
    }),
    Components({
      dts: true,
      resolvers: [
        IconsResolver({
          customCollections: [
            'svg',
            'svg-square',
            'svg-empty'
          ],
        }),
      ],
    }),
    // https://github.com/antfu/unocss
    // see uno.config.ts for config
    Unocss(),
    splitVendorChunkPlugin(),
    viteCompression({
      verbose: true, // 是否在控制台输出压缩结果
      disable: false, // 是否禁用,相当于开关在这里
      threshold: 10240, // 体积大于 threshold 才会被压缩,单位 b，1b=8B, 1B=1024KB  那我们这里相当于 9kb多吧，就会压缩
      algorithm: 'gzip', // 压缩算法,可选 [ 'gzip' , 'brotliCompress' ,'deflate' , 'deflateRaw']
      ext: '.gz', // 文件后缀
    }),
    visualizer({
      emitFile: false,
      file: "stats.html", // 分析图生成的文件名
      open: true // 如果存在本地服务端口，将在打包后自动展示
    })
  ],

  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          hack: `true; @import (reference) "${resolve(
            "src/style/mixins.less"
          )}";`,
        },
        javascriptEnabled: true,
      },
    },
    /* postcss: {
      plugins: [
        // 将 px 单位转换为视口单位(vw, vh, vmin, vmax)
        // https://github.com/lkxian888/postcss-px-to-viewport-8-plugin
        pxToVW({
          unitToConvert: 'px', // 需要转换的单位，默认为"px"
          viewportWidth: 375, // 设计稿的视口宽度
          unitPrecision: 5, // 单位转换后保留的精度
          // propList: ['*', '!font-size'], // 能转化为vw的属性列表,!font-size表示font-size后面的单位不会被转换
          viewportUnit: 'vw', // 希望使用的视口单位
          fontViewportUnit: 'vw', // 字体使用的视口单位
          selectorBlackList: ['keep-px'], // 类名中含有'keep-px'都不会被转换
          minPixelValue: 1, // 设置最小的转换数值，如果为1的话，只有大于1的值会被转换
          mediaQuery: false, // 媒体查询里的单位是否需要转换单位
          replace: true, //  是否直接更换属性值，而不添加备用属性
          include: [/src/], // 如果设置了include，那将只有匹配到的文件才会被转换

          exclude: [/src\/views\/keeppx/,/src\/views\/business/, /src\/components\/keeppx/, /components\/free-from/, /src\/style/, /node_modules\/tdesign-vue-next/],
          landscape: true, // 是否添加根据 landscapeWidth 生成的媒体查询条件 @media (orientation: landscape)
          landscapeUnit: 'vw', // 横屏时使用的单位
          landscapeWidth: 1024, // 横屏时使用的视口宽度
        }) as any
      ],
    }, */
  },
})
