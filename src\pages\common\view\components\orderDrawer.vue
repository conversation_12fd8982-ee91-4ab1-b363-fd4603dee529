<template>
  <div>
    <t-drawer v-model:visible="visibleFlag" :show-overlay="false" size="472" v-bind="$attrs" class="order-drawer">
      <template #header>
        <div class="order-drawer-header">
          <div>{{ t('order.orderD') }}</div>
          <img
            style="width: 24px; cursor: pointer; height: 24px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="closeDrawer"
          />
        </div>
      </template>

      <template #body>
        <div>
          <div style="display: flex; flex-direction: column; gap: 12px">
            <orderStatusStep v-if="tabs !== 0" />
            <!-- 退款 -->
            <div v-if="refundData && refundData.status === 4" class="fff-item">
              <div v-if="refundData && refundData.status === 4" class="lin head-item-lab">{{ t('order.tkxx') }}</div>
              <div v-if="refundData && refundData.status === 4" class="content-box">
                <div class="form-box">
                  <div class="flex-al-sb">
                    <div class="tkje">
                      {{ t('order.tkje') }}:

                      <span style="font-weight: 600">
                        {{ orderDetails.currency === 'CNY' ? '￥' : 'MOP' }}
                        {{ addCommasToNumber(refundData.refund ? refundData.refund + '' : '0') }}
                      </span>
                    </div>
                    <div class="tktext" @click="openRefundDetail">{{ t('order.tkxq') }}></div>
                  </div>
                  <div class="tktips">
                    {{ t('order.pty') }} {{ refundData.refundAt }}
                    {{ payInfoData?.channel == 6 ? t('order.pty2') : t('order.pty1') }}
                  </div>
                </div>
              </div>
            </div>
            <div class="fff-item">
              <!-- 会员信息 -->
              <div class="lin head-item-lab memberInformation">
                <div>
                  {{ t('order.memberInformation') }}
                </div>
              </div>
              <div class="content-box">
                <div class="form-box">
                  <div class="form-flex" style="width: 100%">
                    <div class="laber-item">{{ t('order.orderNo') }}</div>
                    <t-tooltip :content="orderDetails.sn">
                      <div class="value-item">{{ orderDetails.sn }}</div>
                    </t-tooltip>
                  </div>
                  <div class="form-flex" style="width: 100%">
                    <div class="laber-item">{{ t('order.orderstats') }}</div>
                    <div class="value-item">
                      <div
                        class="transaction-status"
                        :style="{
                          background:
                            orderDetails.status === 1 ? '#FFE5D1' : orderDetails.status === 2 ? '#E0F2E5' : '#ECEFF5',
                          color:
                            orderDetails.status === 1 ? '#FC7C14' : orderDetails.status === 4 ? '#499D60' : '#516082',
                        }"
                      >
                        {{
                          orderDetails.status === 1
                            ? '待付款'
                            : orderDetails.status === 4
                              ? '已完成'
                              : '已' + t('payment.close')
                        }}
                      </div>
                    </div>
                  </div>
                  <div class="form-flex" style="width: 100%">
                    <div class="laber-item">{{ t('order.orderTime') }}</div>
                    <t-tooltip :content="orderDetails.createdAt">
                      <div class="value-item">{{ orderDetails.createdAt ? orderDetails.createdAt : '--' }}</div>
                    </t-tooltip>
                  </div>
                  <div v-if="orderDetails.status === 4" class="form-flex" style="width: 100%">
                    <div class="laber-item">付款{{ t('order.time') }}</div>
                    <div class="value-item">
                      <!-- 222222222222 -->
                      {{ orderDetails.status === 4 ? (orderDetails.tradeAt ? orderDetails.tradeAt : '--') : '--' }}
                    </div>
                  </div>
                  <div v-if="orderDetails.status === 4" class="form-flex" style="width: 100%">
                    <div class="laber-item">完成{{ t('order.time') }}</div>
                    <t-tooltip :content="orderDetails.finishAt">
                      <div class="value-item">
                        {{ orderDetails.status === 4 ? orderDetails.finishAt : '--' }}
                      </div>
                    </t-tooltip>
                  </div>
                  <div v-if="orderDetails.status === 0" class="form-flex" style="width: 100%">
                    <div class="laber-item">{{ t('payment.close') + t('order.time') }}</div>
                    <t-tooltip :content="orderDetails.updatedAt">
                      <div class="value-item">{{ orderDetails.updatedAt }}</div>
                    </t-tooltip>
                  </div>
                </div>
              </div>
            </div>

            <!-- 购买信息 -->
            <div class="fff-item">
              <div class="lin head-item-lab">{{ t('order.by') }}信息</div>
              <div class="content-box">
                <div class="form-box">
                  <div class="form-flex" style="width: 100%">
                    <div class="laber-item">{{ t('order.fkfs') }}</div>
                    <div class="value-item">{{ orderDetails.pay_mode ? orderDetails.pay_mode : '--' }}</div>
                  </div>
                  <div class="form-flex" style="width: 100%">
                    <div class="laber-item">{{ t('order.skf') }}</div>
                    <div class="value-item">{{ orderDetails.payee ? orderDetails.payee : '--' }}</div>
                  </div>

                  <div class="form-flex" style="width: 100%">
                    <div class="laber-item">{{ t('order.invoiceStatus') }}</div>
                    <div class="value-item">
                      {{
                        orderDetails.invoiceStatus === 0
                          ? t('order.notInvoiced')
                          : orderDetails.invoiceStatus === 1
                            ? '开票中'
                            : orderDetails.invoiceStatus === 2
                              ? t('order.notInvoiced')
                              : t('order.Invoiced')
                      }}
                      <!-- {{
                      orderDetails.invoiceStatus === 0
                        ? t('order.notInvoiced')
                        : orderDetails.invoiceStatus === 1
                          ? '待审核'
                          : '已开票'
                    }} -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 商品信息 -->
            <div class="fff-item">
              <div class="lin head-item-lab">{{ t('order.spxx') }}</div>
              <ad-shop-info
                v-if="orderDetails?.goodsInfo?.productType === 'ad_lk'"
                class="ad-info"
                :order-details="orderDetails"
              />
              <!-- <div v-if="orderDetails?.goodsInfo?.productType==='ad_lk'" style="display: flex; ">
                <img style="width: 72px;height: 72px;" src="http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/lkad.svg" >
                <div style="margin-left: 12px;  display: flex;  flex-wrap: wrap;gap: 4px;flex: 1;">
                  <div style="color:#1A2139;width: 100%;">{{orderDetails?.goodsInfo?.title}}</div>
                  <div style="width: 100%;">{{orderDetails?.goodsInfo?.intro}}</div>
                  <div style="width: 100%;">投放时间: {{orderDetails?.goodsInfo?.begin_at}}~{{orderDetails?.goodsInfo?.end_at}}</div>
                  <div style="width: 100%;color:#1A2139">{{orderDetails?.goodsInfo?.symbol}}{{orderDetails?.goodsInfo?.unitPrice}}</div>
                </div>
                <div @click="openMx" style="cursor: pointer;display: flex;align-items: center;gap: 4px;color:#4D5EFF;font-size: 14px ;">
                  <span>明细</span>
                  <iconpark-icon  name="iconarrowright"></iconpark-icon>
                </div>
              </div> -->
              <order-table v-else-if="teamId === 1" :table-data-goods="tableDataGoods" :status="orderDetails.status" />
              <orderTeamTable v-else :table-data-goods="tableDataGoods" :status="orderDetails.status" />

              <div class="foot-box">
                <div class="flex-a-end" style="padding-bottom: 8px">
                  <div class="foot-lab">{{ t('order.Totalpriceofgoods') }}</div>
                  <div class="foot-val">
                    {{ orderDetails.currency === 'CNY' ? '￥' : 'MOP'
                    }}{{ addCommasToNumber(orderDetails.amount ? orderDetails.amount + '' : '0') }}
                  </div>
                </div>
                <div v-if="orderDetails.discount" class="flex-a-end" style="padding-bottom: 8px">
                  <div class="foot-lab">{{ t('order.Preferentialreduction') }}</div>
                  <div class="foot-val">
                    -{{ orderDetails.currency === 'CNY' ? '￥' : 'MOP'
                    }}{{ addCommasToNumber(orderDetails.discount ? orderDetails.discount + '' : '0') }}
                  </div>
                </div>
                <div class="flex-a-end">
                  <div class="foot-lab" style="color: #828da5">
                    {{ orderDetails.status === 4 ? t('order.Disbursements') : '需付款' }}
                  </div>
                  <div class="foot-val" style="color: #d54941; font-size: 16px; font-weight: 700">
                    {{ orderDetails.currency === 'CNY' ? '￥' : 'MOP'
                    }}{{ addCommasToNumber(orderDetails.payAmount.toFixed(2)) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="foot-btn-box">
          <div v-if="orderDetails.status === 1 && orderDetails.expiredAt !== 0 && countdown !== '00:00:00'">
            <span>{{ t('order.residue') }}</span>
            <span class="red-time">
              {{ countdown }}
            </span>
          </div>
          <div v-else />
          <div>
            <div>
              <t-button
                v-if="orderDetails.status === 1"
                class="min-80"
                theme="default"
                style="margin-right: 8px"
                variant="outline"
                @click="closeOrder(orderDetails)"
              >
                {{ t('order.cancellationoforder') }}
              </t-button>
              <t-button v-if="orderDetails.status === 1" class="min-80" @click="prompts(orderDetails)">
                立即支付
              </t-button>
            </div>
            <t-button
              v-if="orderDetails.status === 0"
              class="min-80"
              @click="shutdownReason(orderDetails.cancelReason)"
            >
              {{ t('order.closejet') }}
            </t-button>

            <!-- 订单111 -->
            <t-button
              v-if="orderDetails.invoiceStatus === 0 && orderDetails.status === 4"
              class="min-80"
              @click="applyInvoice(orderDetails, '申请开票')"
            >
              {{ t('order.Viewinvoice') }}
            </t-button>
            <!-- {{orderDetails.invoiceStatus}}//{{orderDetails.status}} -->
            <t-button
              v-if="orderDetails.invoiceStatus === 1 && orderDetails.status === 4"
              class="min-80"
              @click="applyInvoice(orderDetails, '开票信息')"
            >
              {{ t('order.Invoicinginformation') }}
            </t-button>
            <t-button
              v-if="orderDetails.invoiceStatus === 2 && orderDetails.status === 4"
              class="min-80"
              @click="applyInvoice(orderDetails, '拒绝开票原因')"
            >
              {{ t('order.Reasonrejection') }}
            </t-button>
            <t-button
              v-if="orderDetails.invoiceStatus === 3 && orderDetails.status === 4"
              class="min-80"
              @click="applyInvoice(orderDetails, '查看发票')"
            >
              {{ t('order.Viewinvoice') }}
            </t-button>
          </div>
        </div>
      </template>
    </t-drawer>
    <applyInvoiceDialog ref="applyInvoiceDialogRef" @apply-invoice-callback="orderCallback" />
    <paymentDialog ref="paymentDialogRef" @payment-callback="orderCallback" @get-data-list="orderCallback" />
    <t-dialog v-model:visible="closeFlag" :close-btn="false" :header="true" width="384">
      <template #header>
        <div
          style="
            display: flex;
            text-align: left;
            width: 100%;
            color: var(--kyy_color_modal_title, #1a2139);
            justify-content: space-between;
          "
        >
          {{ t('order.closejet') }}
          <!-- <img
            style="width: 20px; cursor: pointer; height: 20px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="closeFlag = false"
          > -->
        </div>
      </template>
      <div class="send-kr-box">
        {{ closeText }}
      </div>
      <template #footer>
        <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
          <t-button theme="default" class="min-80" variant="outline" @click="closeFlag = false">关闭</t-button>
        </div>
      </template>
    </t-dialog>
    <t-dialog v-model:visible="dilatationFlag" :close-btn="false" :header="true" width="384">
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>取消原因</div>
          <img
            style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="((dilatationFlag = false), (reason = ''))"
          />
        </div>
      </template>
      <div class="send-kr-box">
        <t-textarea
          v-model="reason"
          :maxlength="200"
          :autosize="{ minRows: 3, maxRows: 5 }"
          placeholder="请输入取消原因"
        />
      </div>
      <template #footer>
        <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
          <t-button theme="default" class="min-80" variant="outline" @click="((dilatationFlag = false), (reason = ''))">
            取消
          </t-button>
          <t-button :disabled="reason ? false : true" class="min-80" @click="sendKR">确定</t-button>
        </div>
      </template>
    </t-dialog>
    <InvoiceDrawer ref="InvoiceDrawerRef" :team-id="teamId" :visible="Invoicevisible" />
    <refundDrawer
      ref="refundDrawerRef"
      :pay-info-data="payInfoData"
      show-in-attached-element
      :order-details="orderDetails"
    ></refundDrawer>
    <orderRecord ref="orderRecordVisible"></orderRecord>
  </div>
</template>
<script setup lang="ts">
import applyInvoiceDialog from './applyInvoiceDialog.vue';
import paymentDialog from './paymentDialog/index.vue';

import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import InvoiceDrawer from './InvoiceDrawer.vue';
import refundDrawer from './refundDrawer.vue';

import { orderDetail, orderCancel, payProduct, refundDetail } from '../../api';
import { getTime } from '../../utils';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import orderTable from './orderTable.vue';
import orderTeamTable from './orderTeamTable.vue';
import adShopInfo from './adShopInfo.vue';
import orderRecord from '@pages/common/view/organize_payments/components/modals/OrderRecord.vue';

const InvoiceDrawerRef = ref(null);
const Invoicevisible = ref(false);

const applyInvoiceDialogRef = ref(null);
const paymentDialogRef = ref(null);
const reason = ref('');
let timer = null;
const addCommasToNumber = (str) => {
  let [integerPart, decimalPart] = str.split('.');
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  if (decimalPart) {
    decimalPart = decimalPart.length === 1 ? `${decimalPart}0` : decimalPart.slice(0, 2);
  } else {
    decimalPart = '00';
  }
  console.log(`${integerPart}.${decimalPart}`, '222222222222');

  return `${integerPart}.${decimalPart}`;
};
const refundDrawerRef = ref(null);
const { t } = useI18n();
const openRefundDetail = () => {
  refundDrawerRef.value.openWin(refundData.value.id, payInfoData.value);
};
let visibleFlag = ref(false);
// 申请发票
let applyInvoiceFlag = false;
const dilatationFlag = ref(false);
const openpaymentDialog = (val) => {
  console.log(val, '打开这个val');
  paymentDialogRef.value.openWin(val);
};
const closeText = ref('');
const closeFlag = ref(false);
const shutdownReason = (val) => {
  closeText.value = val;
  closeFlag.value = true;
};
// 创建订单
const prompts = (row) => {
  if (row === '') {
    MessagePlugin.error(t('order.Theorderstatusoperation'));
    orderCallback();
  } else if (row === '没有支付渠道') {
    console.log(row, 'rowwwwwwwwwwwwwwwwww');
    const confirmDia = DialogPlugin.alert({
      header: '提示',
      theme: 'info',
      body: t('order.closeTip'),
      closeBtn: null,
      confirmBtn: '知道了',
      className: 'delmode',
      onConfirm: async () => {
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  } else {
    // 第一个参数当前行row里面必须有个sn订单号和region订单区域
    let rowDatas = JSON.parse(JSON.stringify(row));
    rowDatas.amount = rowDatas.payAmount;
    paymentDialogRef.value.openWin(rowDatas);
  }
};
const closeOrder = (row) => {
  // 取消订单
  console.log(row.sn, 'rowwwwwwwwwwww');
  rowData.value = row;
  dilatationFlag.value = true;
};
const sendKR = () => {
  // 取消付款
  orderCancel(
    {
      sn: rowData.value.sn,
      reason: reason.value,
    },
    props.teamId,
  )
    .then((res) => {
      if (res.code === 0) {
        reason.value = '';
        dilatationFlag.value = false;
        orderCallback();

        visibleFlag.value = false;
      } else {
        MessagePlugin.error(t('order.Theorderstatusoperation'));
        if (visibleFlag.value) {
          orderDetailsFn(rowData.value);
        }
        reason.value = '';
        dilatationFlag.value = false;
      }
      orderCallback();

      console.log(res, 'resssssssssss');
    })
    .catch((err) => {
      MessagePlugin.error(err);

      console.log(err, 'errerrerrerr');
    });
};
const applyInvoice = (row, flag) => {
  console.log(row, 'applyInvoiceapplyInvoiceapplyInvoicech查看发票1111111111');
  if (flag === '开票信息' || flag === '查看发票' || flag === '申请开票' || flag === '拒绝开票原因') {
    // 打开发票抽屉
    InvoiceDrawerRef.value.openWin(row, props.teamId);
    return;
  }
  if (applyInvoiceFlag) {
    return;
  }
  applyInvoiceFlag = true;
  if (flag === '开票信息' || flag === '查看发票' || flag === '申请开票' || flag === '拒绝开票原因') {
    applyInvoiceDialogRef.value
      .openWin(
        {
          ...row,
          flag,
        },
        props.teamId,
      )
      .then(() => {
        applyInvoiceFlag = false;
      });
  }
};
let clickFlag = false;
const openApplyInvoiceDialog = (val) => {
  if (clickFlag) {
    return;
  }
  clickFlag = true;
  applyInvoiceDialogRef.value
    .openWin({
      ...val,
    })
    .then(() => {
      clickFlag = false;
    })
    .finally(() => {
      clickFlag = false;
    });
  clickFlag = false;

  console.log('打开这个', clickFlag);
};
const orderDetails = ref({
  createdAt: '',
  status: 0,
  tradeAt: '',
  expiredAt: 0,
  payAmount: 0,
  sn: '',
  currency: '',
  finishAt: '',
  updatedAt: '',
  discount: 0,
  amount: 0,
  cancelReason: '',
  invoiceStatus: 0,
  pay_mode: '',
  staff: {
    name: '',
  },
  payee: '',
});
const tableDataGoods = ref([{ productType: '' }]);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  tabs: {
    type: Number,
    default: 0,
  },
  teamId: {
    type: String || Number,
  },
});
const openWin = (row) => {
  orderDetailsFn(row);
};
defineExpose({
  openWin,
  openApplyInvoiceDialog,
  openpaymentDialog,
});
const rowData = ref({
  sn: '',
});
const refundData = ref(null);
const payInfoData = ref(null);

const orderDetailsFn = async (row) => {
  const res = await orderDetail(row.sn, props.teamId);
  if (timer) {
    countdown.value = '00:00:00';
    clearTimeout(timer);
  }
  console.log(res, 'ressssssss');
  if (res.data.info.refundInfo) {
    let refundInfoRes = await refundDetail(res.data.info.refundInfo.refund_id, props.teamId);
    refundData.value = refundInfoRes.data;
    console.log(refundData.value, 'refundInfoResrefundInfoRes');
    console.log(refundData.value, 'co111111111nsoconsolele');
    refundData.value.refundAt =
      refundInfoRes?.data.refundAt === 0 ? '' : getTime(new Date(refundInfoRes.data.refundAt * 1000), true);
    console.log(refundData.value, 'consoconsolele');
  } else {
    refundData.value = null;
  }
  payInfoData.value = res.data.payInfo;
  orderDetails.value = {
    ...res.data.info,
    createdAt: getTime(new Date(res.data.info.createdAt * 1000)),
    tradeAt: res.data.info.tradeAt === 0 ? '' : getTime(new Date(res.data.info.tradeAt * 1000)),
    finishAt: getTime(new Date(res.data.info.finishAt * 1000)),
    updatedAt: getTime(new Date(res.data.info.updatedAt * 1000)),
    cancelReason: row.cancelReason ? row.cancelReason : '',
  };
  console.log(orderDetails.value, 'orderDetailsorderDetails');
  if (res.data.info.goodsInfo.productType === 'ad_lk') {
    const timestamp = new Date().getTime();
    const endTime = orderDetails.value.expiredAt * 1000;

    const leftSeconds = (endTime - new Date(timestamp)) / 1000; // 剩余秒数
    startCountdown(leftSeconds); // 开始倒计时
  }

  if (res.data.info.goodsInfo.productType === 'album') {
    tableDataGoods.value[0].productType = 'album';
    tableDataGoods.value[0] = { ...res.data.info.goodsInfo, ...res.data.info.snapshot };
    tableDataGoods.value[0].currency = res.data.info.currency;
    visibleFlag.value = true;
    const timestamp = new Date().getTime();
    const endTime = orderDetails.value.expiredAt * 1000;

    const leftSeconds = (endTime - new Date(timestamp)) / 1000; // 剩余秒数
    startCountdown(leftSeconds); // 开始倒计时
    return;
  }

  if (res.data.info.goodsInfo.productType === 'TEMPLATE') {
    tableDataGoods.value[0] = res.data.info.goodsInfo;
    tableDataGoods.value[0].productType = 'TEMPLATE';
    tableDataGoods.value[0].currency = res.data.info.currency;

    visibleFlag.value = true;
    const timestamp = new Date().getTime();
    const endTime = orderDetails.value.expiredAt * 1000;

    const leftSeconds = (endTime - new Date(timestamp)) / 1000; // 剩余秒数
    startCountdown(leftSeconds); // 开始倒计时
    return;
  }
  console.log(
    res.data.info.goodsInfo.productType,
    'res.data.info.goodsInfo.productTyperes.data.info.goodsInfo.productType',
  );
  let e = null;
  console.log(props.teamId, 'props.teamIdprops.teamIdprops.teamId');

  if (props.teamId === 1) {
    e = await payProduct(row.sn);
    console.log(e, 'e1234556');
    console.log(e, '1111e1234556');

    rowData.value = row;
    console.log(rowData.value, 'rowData.valuerowData.value');

    if (!e?.orderType) {
      MessagePlugin.error(t('order.Productdoesnotexist'));
      return;
    }
    if (e?.orderType === 'UPGRADE_ANNUAL_FEE') {
      e.product.orderType = 'UPGRADE_ANNUAL_FEE';
    }
    if (e) {
      tableDataGoods.value[0] = e.product;
    }
    visibleFlag.value = true;
    const timestamp = new Date().getTime();
    const endTime = orderDetails.value.expiredAt * 1000;
    const leftSeconds = (endTime - timestamp) / 1000;
    startCountdown(leftSeconds);
  } else {
    console.log(res.data.info, 'res.data.info');

    tableDataGoods.value[0] = res.data.info.goodsInfo;
    if (res.data.info.goodsInfo.name === '创业合伙人') {
      tableDataGoods.value[0].productType = 'cy_order';
    }
    if (res.data.info.goodsInfo.name === '事业合伙人') {
      tableDataGoods.value[0].productType = 'sy_order';
    }
    if (res.data.info.goodsInfo.name === '机构') {
      tableDataGoods.value[0].productType = 'lk_order';
    }
    visibleFlag.value = true;
  }
};

watch(
  () => props.visible,
  (newValue) => {
    if (timer) {
      countdown.value = '00:00:00';

      clearTimeout(timer);
    }
    visibleFlag.value = newValue;
  },
);
const countdown = ref('');

const startCountdown = (leftSeconds) => {
  timer = setInterval(() => {
    if (leftSeconds > 0) {
      const hour = Math.floor(leftSeconds / 3600);
      const minute = Math.floor((leftSeconds - hour * 3600) / 60);
      const second = Math.floor(leftSeconds % 60);
      countdown.value = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second
        .toString()
        .padStart(2, '0')}`;
      leftSeconds--;
    } else {
      if (visibleFlag.value && orderDetails.value.expiredAt && orderDetails.value.status === 1) {
        visibleFlag.value = false;
        orderCallback();
      }
      console.log(visibleFlag.value, '走这里啊啊啊');
      clearInterval(timer);
      countdown.value = '00:00:00';
    }
  }, 1000);
};
// paymentCallback
const emits = defineEmits(['orderCallback', 'closeDrawer']);

const orderCallback = () => {
  console.log('orderCallbackorderCallback');
  emits('orderCallback');
  visibleFlag.value = false;
};
const closeDrawer = () => {
  emits('closeDrawer');
  if (timer) {
    countdown.value = '00:00:00';
    clearTimeout(timer);
  }
  visibleFlag.value = false;
};
</script>
<style lang="less" scoped>
.fff-item {
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  margin: 0 12px;
}

.head-item-lab {
  height: 22px;
  font-size: 14px;
  font-weight: 700;
  color: #13161b;
  margin-bottom: 12px;
}

.foot-lab {
  width: 70px;
  height: 22px;
  font-size: 14px;
  font-weight: 400;
  color: var(--text-kyy-color-text-3, #828da5);
  font-family: PingFang SC;
  line-height: 22px;
  text-align: right;
}

.foot-val {
  min-width: 120px;
  height: 22px;
  font-size: 14px;

  font-weight: 400;
  text-align: right;
  color: #13161b;
  line-height: 22px;
}

.w120pl {
  width: 120px;
  padding-left: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.my-order-box {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
}

.flex-a-js-w140 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 140px;
}

.w70text-r {
  width: 190px;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table-foot-box {
  font-size: 14px;

  font-weight: 400;
  text-align: left;
  color: #717376;
  line-height: 44px;

  .foot-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.foot-btn-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: end;
}

.form-box {
  display: flex;
  flex-wrap: wrap;

  .form-flex:last-child {
    margin-bottom: 0;
  }

  .form-flex {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    width: 100%;
  }

  .value-item {
    height: 22px;
    font-size: 14px;

    font-weight: 400;
    color: var(--text-kyy-color-text-1, #1a2139);
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .laber-item {
    width: 70px;
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: var(--text-kyy-color-text-3, #828da5);
  }
}

.foot-box {
  padding: 12px;
  margin-bottom: -16px;
}

.red-time {
  height: 22px;
  font-size: 14px;

  font-weight: 400;
  text-align: left;
  color: #da2d19;
  line-height: 22px;
}

.tabledian150 {
  width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table-thing-value {
  font-size: 14px;

  font-weight: 400;
  color: var(--text-kyy-color-text-3, #828da5);
}

.flex-a-end {
  display: flex;
  align-items: center;
  justify-content: end;
}

.table-thing-laber {
  font-size: 14px;

  font-weight: 400;
  color: var(--kyy-color-table-text, #1a2139);
}

.table-thing {
  display: flex;

  img {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }
}

.content-box {
  border-radius: 8px;
}

:global(.order-drawer .t-drawer__body) {
  padding: 0px !important;
  overflow: overlay !important;
}

:global(.order-drawer .t-drawer__content-wrapper) {
  background: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/bg_s1.png');
  background-repeat: no-repeat;
  background-repeat: no-repeat;
  background-color: #f5f8fe;
}

.memberInformation {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.t-drawer__header) {
  border: none !important;
  border-bottom: none !important;
}

.order-drawer-header {
  display: flex;
  -webkit-app-region: no-drag;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}

.head-search-box {
  padding-top: 72px;
  height: 185px;
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 24px;
  padding-left: 24px;
}

/* :deep(.t-drawer__body) {
  padding: 0 24px 16px 24px !important;
} */
.form-items {
  display: flex;
  align-items: center;
  padding-right: 32px;

  .labels {
    height: 22px;
    padding-right: 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: #13161b;
    line-height: 22px;
  }
}

.btn-box {
  font-size: 14px;

  font-weight: 400;
  color: #2069e3;
  cursor: pointer;
  line-height: 22px;
}

.transaction-status {
  width: 58px;
  height: 24px;
  // border-radius: 4px;
  border-radius: 12px;

  font-size: 14px;

  font-weight: 600;
  text-align: center;
  line-height: 24px;
}

.flex-a {
  display: flex;
  align-items: center;
}

.af-icon {
  margin-left: 8px;
  height: 32px;
}

.f-icon {
  display: flex;
  width: 32px;
  height: 32px;
  cursor: pointer;
  min-height: 32px;
  max-height: 32px;
  padding: 6px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-left: 8px;
  border-radius: var(--radius-kyy-radius-button-s, 4px);
  border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
  background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
}

.flex-al-sb {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 8px;
}

.tkje {
  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.tktext {
  color: var(--text-kyy_color_text_3, #828da5);
  text-align: right;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
  cursor: pointer;
}

.tktips {
  color: var(--text-kyy_color_text_2, #516082);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}
</style>
