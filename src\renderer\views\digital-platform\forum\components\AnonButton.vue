<template>
  <div v-if="isShowAnonSetup" class="anon-button" @click="handleOpen" :style="actionStyle">
    <img class="anon-button-bg" src="@/assets/forum/anon_button.png" alt="设置匿名" />
  </div>

  <AnonSetupDialog ref="anonSetupDialogRef" @skip="handleSkip" />
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import AnonSetupDialog from './AnonSetupDialog.vue';
import { useForumStore } from '../store';
import { SKIP_ANON_SETUP } from '../constant';
import { useLocalStorage } from '@vueuse/core';

const props = withDefaults(
  defineProps<{
    offset: [string, string];
  }>(),
  {
    offset: () => ['390px', '100px'] as [string, string],
  },
);

const forumStore = useForumStore();

const skipAnonSetup = useLocalStorage(SKIP_ANON_SETUP, [] as string[]);

const anonSetupDialogRef = ref<InstanceType<typeof AnonSetupDialog>>(null);

const isShowAnonSetup = computed(() => {
  return forumStore && forumStore.platformType === 'uni' && !forumStore.hasAnonymous;
});

const actionStyle = computed(() => {
  const [right, bottom] = props.offset;
  return `right: ${right}; bottom: ${bottom}`;
});

watch(
  [isShowAnonSetup, anonSetupDialogRef, skipAnonSetup],
  ([isShow]) => {
    if (isShow) {
      const queryId = `${forumStore.teamId}-${forumStore.ownerId}`;

      const hasSkip = skipAnonSetup.value.includes(queryId);
      console.log('hasSkip', hasSkip);

      if (!hasSkip) {
        anonSetupDialogRef.value?.open();
      }
    }
  },
  { immediate: true },
);

const handleSkip = () => {
  const queryId = `${forumStore.teamId}-${forumStore.ownerId}`;

  // queryId 存在时，不展示弹窗
  if (skipAnonSetup.value.includes(queryId)) {
    return;
  }

  skipAnonSetup.value = [...skipAnonSetup.value, queryId];
};

const handleOpen = () => {
  anonSetupDialogRef.value?.open({
    avatarUrl: '',
    nickname: '',
  });
};
</script>

<style scoped lang="less">
.anon-button {
  position: fixed;

  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9;
  cursor: pointer;
}
</style>
