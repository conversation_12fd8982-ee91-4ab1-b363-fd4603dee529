import { client_orgRequest, squareRequest } from '@axios/index';
import type { IOptions } from '@axios/types';
import { BondStatusEnum } from '../constants/enum';
import dayjs from 'dayjs';

export interface GetUserTeamResponse {
  code: number;
  data: GetUserTeamData;
  message: string;
  [property: string]: any;
}

export interface GetUserTeamData {
  items: GetUserTeamDataItem[];
  total: number;
  [property: string]: any;
}

export interface GetUserTeamDataItem {
  cardId: string;
  exclusive_name: string;
  idTeam: number;
  staffAvatar: string;
  staffId: number;
  staffName: string;
  teamAuth: number;
  teamFullName: string;
  teamId: string;
  teamLogo: string;
  teamRegion: string;
  teamType: number;
  user_ids: { idStaff: number; platformStaff: number };
  verify_auth: boolean;
}

export function getUserTeamList(options?: IOptions): Promise<GetUserTeamResponse> {
  return client_orgRequest(
    {
      url: '/common/teams',
      method: 'GET',
    },
    options,
  ) as Promise<GetUserTeamResponse>;
}

export interface ExternalAppListRequest {
  channel_type_not?: string;
  page?: number;
  pageSize?: number;
  bond_status_in?: string;
}

export interface ExternalAppListResponse {
  code: number;
  data: ExternalAppListData;
  message: string;
  [property: string]: any;
}

export interface ExternalAppListData {
  items: ExternalAppListDataItem[];
  total: number;
  [property: string]: any;
}

export interface ExternalAppListDataItem {
  /**
   * 应用id
   */
  app_id?: number;
  /**
   * 展示渠道
   */
  channels?: ExternalAppListDataChannel[];
  /**
   * 创建时间
   */
  created_at?: string;
  /**
   * 应用名称
   */
  name?: string;
  /**
   * 应用类型（App跳转：app，网页H5跳转：h5，数字平台：wechat_official，微信小程序：mini_program）
   */
  type?: string;
  [property: string]: any;
}

export interface ExternalAppListDataChannel {
  /**
   * 渠道类型（数智工场：workshop，广场号：square，数字平台：digital）
   */
  channel_type?: string;
  [property: string]: any;
}

// 获取应用列表
export function getExternalAppList(
  query?: ExternalAppListRequest,
  options?: IOptions,
): Promise<ExternalAppListResponse> {
  return client_orgRequest(
    {
      url: `/external_app/app`,
      method: 'GET',
      params: { ...query },
      headers: {
        teamId: options?.teamId,
      },
    },
    options,
  ) as Promise<ExternalAppListResponse>;
}

export interface AddExternalAppRequest {
  /**
   * 文章链接
   */
  article_link?: string;
  /**
   * 桌面端链接
   */
  desktop_link?: string;
  /**
   * 网页H5链接
   */
  h5_link?: string;
  /**
   * 小程序原始ID
   */
  mini_program_original_id?: string;
  /**
   * 小程序路径
   */
  mini_program_path?: string;
  /**
   * 应用名称
   */
  name: string;
  /**
   * 应用图片文件数据（前端自定义）
   */
  picture_file?: any[] | boolean | number | number | { [key: string]: any } | null | string;
  /**
   * 应用图片链接
   */
  picture_linking: string;
  /**
   * 分享链接
   */
  share_link?: string;
  /**
   * 应用类型（App跳转：app，网页H5跳转：h5，数字平台：wechat_official，微信小程序：mini_program）
   */
  type: string;

  bond_status?: BondStatusEnum;
  [property: string]: any;
}

export interface AddExternalAppResponse {
  code: number;
  message: string;
  [property: string]: any;
}
export function addExternalApp(data: ExternalAppListDataItem, options?: IOptions): Promise<AddExternalAppResponse> {
  return client_orgRequest(
    {
      url: `/external_app/app`,
      method: 'POST',
      data,
    },
    options,
  ) as Promise<AddExternalAppResponse>;
}

export interface DeleteExternalAppRequest {
  app_id: number;
}

export interface DeleteExternalAppResponse {
  code: number;
  message: string;
  [property: string]: any;
}

export function deleteExternalApp(
  data: DeleteExternalAppRequest,
  options?: IOptions,
): Promise<DeleteExternalAppResponse> {
  return client_orgRequest(
    {
      url: `/external_app/app/${data.app_id}`,
      method: 'DELETE',
      data,
    },
    options,
  ) as Promise<DeleteExternalAppResponse>;
}

export interface ExternalAppInfoResponse {
  code: number;
  data: ExternalAppInfoData;
  message: string;
}

export interface ExternalAppInfoOrder {
  /**
   * 支付渠道
   */
  pay_channel: string;
  /**
   * 支付方式
   */
  pay_method: string;
  /**
   * 付款人
   */
  pay_openid_name: string;
  /**
   * 付款时间
   */
  payed_at: string;
  /**
   * 收款方
   */
  receive_team_name: string;
  /**
   * 保证金编号
   */
  sn: string;
  /**
   * 支付状态 0待付款 1已付款
   */
  status: number;
}

export interface ExternalAppInfoRefund {
  /**
   * 银行账户
   */
  bank_account: string;
  /**
   * 币种
   */
  currency: string;
  /**
   * 开户行
   */
  opening_bank: string;
  /**
   * 退款金额
   */
  refund_amount: string;
  /**
   * 退款方式
   */
  refund_method: string;
  /**
   * 退款时间
   */
  refund_time: string;
  /**
   * 退款凭证
   */
  refund_voucher: string[];
}

export interface ExternalAppInfoData {
  /**
   * 应用id
   */
  app_id: number;
  /**
   * 文章链接
   */
  article_link: string;
  /**
   * 保证金金额
   */
  bond_amount: number;
  /**
   * 保证金状态
   */
  bond_status: number;
  /**
   * 保证金编号
   */
  bond_sn: string;
  /**
   * 币种
   */
  currency: string;
  /**
   * 桌面端链接
   */
  desktop_link: string;
  /**
   * 网页H5链接
   */
  h5_link: string;
  /**
   * 小程序原始ID
   */
  mini_program_original_id: string;
  /**
   * 小程序路径
   */
  mini_program_path: string;
  /**
   * 应用名称
   */
  name: string;
  /**
   * 支付信息
   */
  order: ExternalAppInfoOrder | null;
  /**
   * 应用图片链接
   */
  picture_linking: string;
  refund: ExternalAppInfoRefund | null;
  /**
   * 分享链接
   */
  share_link: string;
  /**
   * 应用类型（App跳转：app，网页H5跳转：h5，微信公众号：wechat_official，微信小程序：mini_program）
   */
  type: string;
}
export function getExternalAppInfo(app_id: number, options?: IOptions): Promise<ExternalAppInfoResponse> {
  return client_orgRequest(
    {
      url: `/external_app/app/${app_id}`,
      method: 'GET',
      params: { app_id },
    },
    options,
  ) as Promise<ExternalAppInfoResponse>;
}

export interface UpdateExternalAppRequest {
  /**
   * 文章链接
   */
  article_link?: string;
  /**
   * 桌面端链接
   */
  desktop_link?: string;
  /**
   * 网页H5链接
   */
  h5_link?: string;
  /**
   * 小程序原始ID
   */
  mini_program_original_id?: string;
  /**
   * 小程序路径
   */
  mini_program_path?: string;
  /**
   * 应用名称
   */
  name: string;
  /**
   * 应用图片文件数据（前端自定义）
   */
  picture_file?: any[] | boolean | number | number | { [key: string]: any } | null | string;
  /**
   * 应用图片链接
   */
  picture_linking: string;
  /**
   * 分享链接
   */
  share_link?: string;
  /**
   * 应用类型（App跳转：app，网页H5跳转：h5，数字平台：wechat_official，微信小程序：mini_program）
   */
  type: string;
  [property: string]: any;
}

export interface UpdateExternalAppResponse {
  code: number;
  message: string;
  [property: string]: any;
}
export function updateExternalApp(
  app_id: number,
  data: ExternalAppListDataItem,
  options?: IOptions,
): Promise<UpdateExternalAppResponse> {
  return client_orgRequest(
    {
      url: `/external_app/app/${app_id}`,
      method: 'PUT',
      data,
    },
    options,
  ) as Promise<UpdateExternalAppResponse>;
}

export interface ExternalAppAdminListResponse {
  code: number;
  data: ExternalAppAdminListData;
  message: string;
  [property: string]: any;
}

export interface ExternalAppAdminListData {
  /**
   * 当前用户是否超级管理员（0：否，1：是）
   */
  is_super: number;
  items: ExternalAppAdminListDataItem[];
  total: number;
  [property: string]: any;
}

export interface ExternalAppAdminListDataItem {
  /**
   * 员工头像
   */
  avatar: string;
  /**
   * 员工名称
   */
  name: string;
  /**
   * 员工id
   */
  staff_id: number;
  /**
   * 管理员类型（0：普通，1：超级）
   */
  type: number;
  [property: string]: any;
}
export function getExternalAppAdminList(query?: undefined, options?: IOptions): Promise<ExternalAppAdminListResponse> {
  return client_orgRequest(
    {
      url: `/external_app/admin`,
      method: 'GET',
    },
    options,
  ) as Promise<ExternalAppAdminListResponse>;
}
export interface AddExternalAppAdminRequest {
  /**
   * 员工ids
   */
  staff_ids: number[];
  [property: string]: any;
}
export interface AddExternalAppAdminResponse {
  code: number;
  message: string;
  [property: string]: any;
}
export function addExternalAppAdmin(
  data: AddExternalAppAdminRequest,
  options?: IOptions,
): Promise<AddExternalAppAdminResponse> {
  return client_orgRequest(
    {
      url: `/external_app/admin`,
      method: 'POST',
      data,
    },
    options,
  ) as Promise<ExternalAppAdminListResponse>;
}

export interface DeleteExternalAppAdminRequest {
  admin_id: number;
}
export interface DeleteExternalAppAdminResponse {
  code: number;
  message: string;
  [property: string]: any;
}
export function deleteExternalAppAdmin(
  data: DeleteExternalAppAdminRequest,
  options?: IOptions,
): Promise<DeleteExternalAppAdminResponse> {
  return client_orgRequest(
    {
      url: `/external_app/admin/${data.admin_id}`,
      method: 'DELETE',
      data,
    },
    options,
  ) as Promise<DeleteExternalAppAdminResponse>;
}

export interface TransferExternalAppAdminRequest {
  /**
   * 更改方式（1：删除源超级管理员，0：调整为普通管理员）  {     "staff_id": 0,     "mode": 0 }
   */
  mode: number;
  /**
   * 员工id
   */
  staff_id: number;
  [property: string]: any;
}
export interface TransferExternalAppAdminResponse {
  code: number;
  message: string;
  [property: string]: any;
}
// 转移超级管理员
export function transferExternalAppAdmin(
  data: TransferExternalAppAdminRequest,
  options?: IOptions,
): Promise<TransferExternalAppAdminResponse> {
  return client_orgRequest(
    {
      url: `/external_app/admin/transfer`,
      method: 'POST',
      data,
    },
    options,
  ) as Promise<TransferExternalAppAdminResponse>;
}
export interface ExternalAppDisplayChannelListRequest {
  /**
   * 渠道类型（数智工场：workshop，广场号：square，数字平台：digital）
   */
  channel_type?: string;
  /**
   * 保证金状态，多个值用逗号,隔开（0：无需支付，1：待支付，2：已支付，3：已退款）
   */
  bond_status_in?: string;
  [property: string]: any;
}
export interface ExternalAppDisplayChannelListResponse {
  code: number;
  data: ExternalAppDisplayChannelListData;
  message: string;
  [property: string]: any;
}

export interface ExternalAppDisplayChannelListData {
  items: ExternalAppDisplayChannelListDataItem[];
  [property: string]: any;
}

export interface ExternalAppDisplayChannelListDataItem {
  /**
   * 应用id
   */
  app_id?: number;
  /**
   * 应用名称
   */
  name?: string;
  /**
   * 应用图片链接
   */
  picture_linking?: string;
  /**
   * 应用类型（App跳转：app，网页H5跳转：h5，微信公众号：wechat_official，微信小程序：mini_program）
   */
  type?: string;
  [property: string]: any;
}

export function getExternalAppDisplayChannelList(
  query?: ExternalAppDisplayChannelListRequest,
  options?: IOptions,
): Promise<ExternalAppDisplayChannelListResponse> {
  return client_orgRequest(
    {
      url: `/external_app/channel/app`,
      method: 'GET',
      params: { ...query },
    },
    options,
  ) as Promise<ExternalAppDisplayChannelListResponse>;
}

export interface AddExternalAppDisplayChannelRequest {
  /**
   * 应用ids
   */
  app_ids: number[];
  /**
   * 渠道类型（数智工场：workshop，广场号：square，数字平台：digital）
   */
  channel_type: string;
  [property: string]: any;
}
export interface AddExternalAppDisplayChannelResponse {
  code: number;
  message: string;
  [property: string]: any;
}
export function addExternalAppDisplayChannel(
  data: AddExternalAppDisplayChannelRequest,
  options?: IOptions,
): Promise<AddExternalAppDisplayChannelResponse> {
  return client_orgRequest(
    {
      url: `/external_app/channel/app/add`,
      method: 'POST',
      data,
    },
    options,
  ) as Promise<AddExternalAppDisplayChannelResponse>;
}

export interface DeleteExternalAppDisplayChannelRequest {
  /**
   * 应用id
   */
  app_id: number;
  /**
   * 渠道类型（数智工场：workshop，广场号：square，数字平台：digital）
   */
  channel_type: string;
  [property: string]: any;
}
export interface DeleteExternalAppDisplayChannelResponse {
  code: number;
  message: string;
  [property: string]: any;
}
export function deleteExternalAppDisplayChannel(
  data: DeleteExternalAppDisplayChannelRequest,
  options?: IOptions,
): Promise<DeleteExternalAppDisplayChannelResponse> {
  return client_orgRequest(
    {
      url: `/external_app/channel/app/remove`,
      method: 'POST',
      data,
    },
    options,
  ) as Promise<DeleteExternalAppDisplayChannelResponse>;
}

export interface SortExternalAppDisplayChannelRequest {
  /**
   * 应用ids
   */
  app_ids: number[];
  /**
   * 渠道类型（数智工场：workshop，广场号：square，数字平台：digital）
   */
  channel_type: string;
  [property: string]: any;
}
export interface SortExternalAppDisplayChannelResponse {
  code: number;
  message: string;
  [property: string]: any;
}
export function sortExternalAppDisplayChannel(
  data: SortExternalAppDisplayChannelRequest,
  options?: IOptions,
): Promise<SortExternalAppDisplayChannelResponse> {
  return client_orgRequest(
    {
      url: `/external_app/channel/app/sort`,
      method: 'POST',
      data,
    },
    options,
  ) as Promise<SortExternalAppDisplayChannelResponse>;
}

export interface CheckSquareOpenRequest {
  /**
   * 获取个人广场号时使用
   */
  open_id?: string;
  /**
   * 知道广场 ID 时使用
   */
  square_id?: string;
  /**
   * 组织 ID（获取组织广场号时使用）
   */
  team_id?: string;
  [property: string]: any;
}
export interface CheckSquareOpenResponse {
  code: number;
  data: {
    opened: boolean;
    annualFeeExpiredAt?: boolean;
  };
  message: string;
  [property: string]: any;
}

export interface PayAnnualFeeResponse {
  code: number;
  data: {
    bond_sn: string;
    bond_amount: string;
    region: string;
  };
  message: string;
  [property: string]: any;
}

export function checkSquareOpen(query?: CheckSquareOpenRequest, options?: IOptions): Promise<CheckSquareOpenResponse> {
  return squareRequest(
    {
      url: `/v1/shared/square`,
      method: 'GET',
      params: { ...query },
    },
    options,
  ) as Promise<CheckSquareOpenResponse>;
}

export async function checkDigiPlatformOpen(options?: IOptions): Promise<boolean> {
  const data = (await client_orgRequest(
    {
      url: `/digit212/common/getSortTeams`,
      method: 'GET',
      params: {
        'page.size': 100,
      },
    },
    options,
  )) as Promise<CheckSquareOpenResponse>;
  // @ts-ignore
  const open_enabled = data?.data?.open_enabled || [];
  const isOpen = !!open_enabled?.find((i: any) => i.teamId === options?.teamId);
  return isOpen || false;
}

// 检查是否购买套餐
export async function checkAnnualFee(options?: IOptions): Promise<boolean> {
  const data = (await squareRequest(
    {
      url: `/v1/shared/team_annual_fee`,
      method: 'GET',
    },
    options,
  )) as Promise<CheckSquareOpenResponse>;
  // 2025-07-18T09:48:04Z
  const expiredAt = data?.annualFeeExpiredAt;
  if (!expiredAt) return true; // 没有购买套餐，视为过期
  return dayjs(expiredAt).isBefore(dayjs());
}

// 支付保证金
export async function payAnnualFee(
  params: {
    /**
     * 应用id
     */
    app_id: number;
  },
  options?: IOptions,
) {
  const data = (await client_orgRequest(
    {
      url: `/external_app/app/bond/pay`,
      method: 'POST',
      data: params,
    },
    options,
  )) as PayAnnualFeeResponse;

  return data;
}

export interface MarginRecordItem {
  /**
   * 金额
   */
  bond_amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 应用名称
   */
  name?: string;
  /**
   * 应用图标
   */
  picture_linking?: string;
}

export interface MarginRecordResponse {
  /**
   * 总金额
   */
  bond_amount: number;
  /**
   * 总金额币种
   */
  currency: string;
  items: MarginRecordItem[];
}

// 保证金列表
export async function getMarginRecord(options?: IOptions) {
  const data = (await client_orgRequest(
    {
      url: `/external_app/app/bond/pay`,
      method: 'GET',
    },
    options,
  )) as {
    code: number;
    data: MarginRecordResponse;
    message: string;
  };

  return data;
}
