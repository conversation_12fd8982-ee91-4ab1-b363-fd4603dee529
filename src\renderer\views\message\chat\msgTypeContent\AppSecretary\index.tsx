import { defineComponent, PropType, h } from 'vue';
import { jumpH5WithLink, jumpWeb } from '@renderer/views/contacts/utils';
import { goToDigitalPlatform_member } from '@renderer/views/member/utils/auth';
import { goToDigital_platform_politics_my } from '@renderer/views/politics/utils/auth';
import { goToDigital_platform_politics_my as goToDigital_platform_politics_my_asso, goToAdmin as goToAdminAssociation,
  goToDigitalPlatform_member as goToDigitalPlatform_member_asso } from '@renderer/views/association/utils/auth';
import { DialogPlugin } from 'tdesign-vue-next';
import { MessageToSave } from 'customTypes/message';
import { msgJumpToMember, msgJumpToGovernment } from '../../../service/msgUtils';
import { SceneTypeArr } from './constant';
import { SceneTypeArr as partnerSceneTypeArr } from './partner/constant';
import { i18nt } from '@/i18n';
import BizUnion from '@/assets/im/biz_union.png';

import { AppCard, AppCardHeader, AppCardBody, AppCardFooter } from '../../MessageAppCard';
import MsgBirthSecretary from '../../../cards/msg-birth-secretary.vue';
import MsgRank from '../../../cards/msg-rank.vue';
import getAppTeamsTextEle from '../../common/getAppTeamsTextEle';
import getAppSecretaryBillEle from './getAppSecretaryBillEle';
import CBD from './CBD.vue';
import Partner from './partner/index.vue';
import Visitor from './Visitor/index.vue';
import AnnoAlert from './annoAlert/index.vue';
import Digital from './Digital/index.vue';

import TransAgent from './transAgent/index.vue';
import { TransAgentSceneTypeArr } from './transAgent/constant';

import Agent from './agent/index.vue';
import { AgentSceneTypeArr } from './agent/constant';

import { naasSceneTypeArr } from './naas/constant';
import Naas from './naas/index.vue';

/** 广告 */
import { SceneTypeArr as AdCardTypeArr } from './AdCard/constant';
import { SceneTypeArr as VisitorTypeArr } from './Visitor/constant';
import { SceneTypeArr as AnnoAlertTypeArr } from './annoAlert/constant';
import { SceneTypeArr as DigitalTypeArr } from './Digital/constant';
import AdCard from './AdCard/index.vue';
/** 广告 */
import { SceneTypeArr as ShopTypeArr } from './Shop/constant';
import Shop from './Shop/index.vue';

export default defineComponent({
  props: {
    data: { type: Object as PropType<MessageToSave>, required: true },
  },
  setup(props) {
    const msg = props.data;
    const data = msg.contentExtra.data;
    const scene = msg.contentExtra?.scene || data.scene;
    // scene: 0 小秘书生日祝福提醒,后端scene为0可能被吃掉不返回
    if (scene === 0) {
      return () => h(MsgBirthSecretary, { msg });
    }
    // 21001: 家庭场景：被点赞，小秘书
    // 21002: 家庭场景：运动要求创建，小秘书
    if ([21001, 21002].includes(scene)) {
      return () => h(MsgRank, { msg });
    }

    // 10 身份卡转移
    if ([9, 10].includes(scene)) {
      const logo = data?.header?.team?.logo;
      const team = data?.header?.team?.name;
      return () => (
        <AppCard style={{ width: '320px', position: 'relative' }}>
          <AppCardHeader style="display:flex;flex-direction:row;justify-content:space-between;">
            {logo ? (
              <img width="20" height="20" src={logo} />
            ) : (
              <i class="i-svg-im_avatar_team text-20 color-[#488BF0]" />
            )}
            <div style="flex:1;margin-left:4px;"> {team} </div>
          </AppCardHeader>
          <AppCardBody>{data?.content?.title}</AppCardBody>
        </AppCard>
      );
    }

    // 权限移除和添加，在这里处理
    // 5015 添加权限
    // 5016 移除权限
    // 5025 成员加入
    // 14017 成员同意加入 写在这儿只是展示title样式
    // 14017 成员拒绝加入 只是展示title样式
    if ([5015, 5016, 5025, 14017, 14018, 19017, 19018].includes(scene)) {
      return () => getAppTeamsTextEle(msg);
    }

    // 6003 解除商务关系已同意 、6004 解除商务关系已拒绝
    if ([6003, 6004].includes(scene)) {
      // 因为调用内部身份卡会失败，先暂时不用接口请求
      // getSecretary_6004_status(msg);

      return () => (
        <AppCard>
          <AppCardHeader>{data?.content?.title}</AppCardHeader>
          <AppCardBody>
            {i18nt('im.msg.admin')}
            {
              data?.extend?.operator_card_name && <a>{data?.extend?.operator_card_name}</a>
            }
            {data.scene === 6003 ? i18nt('im.msg.agree') : i18nt('im.msg.refuse')}
            {i18nt('im.msg.deleteTip')}
          </AppCardBody>
        </AppCard>
      );
    }
    if ([8101, 8102].includes(scene)) {
      // 8101 发票通过 、8102 发票拒绝
      return () => getAppSecretaryBillEle(msg);
    }

    if ([21001, 21002].includes(scene)) {
      return () => h(MsgRank, { msg: { msg } });
    }

    // 5026: 数字商协会员管理退会
    // 5027: 联系人本人收到添加消息
    // 5028: 代表人本人收到添加消
    // 5029: 删除联系人本人收到删除消息
    // 5030: 删除代表人本人收到删除消息
    // 5031: 成为商协会应用管理员
    // 5032: 被移除商协会应用管理员
    // 5033: 个人会员被管理员操作退会
    // const MEMBER_ALL_ACTIVATE = 5040;//一键激活-发送给代表人
    // const MEMBER_ALL_ACTIVATE_ADMIN = 5041;//一键激活-发送给管理员

    if ([5026, 5027, 5028, 5029, 5030, 5031, 5032, 5033, 5040].includes(scene)
      || ([5076, 5077].includes(scene) && data?.extend.digital_uuid === 'member')
    ) {
      // 同一个样式
      const showDetailBtn = [5027, 5031, 5040].includes(scene) || ([5076, 5077].includes(scene) && data?.extend.digital_uuid === 'member');
      const onClickDetail = () => {
        // 5027 成为单位会员联系人 -> 跳转数字商协-打开该商协会组织下的会员端页面
        // 5031、5032 成为商协会应用管理员 -> 跳转数字商协-打开该商协会组织下的管理端页面
        const params = {
          from: 'message',
          redirect: '', // 进入管理端
          teamId: data?.extend?.teamId,
        };
        if (scene === 5027) {
          // params.redirect = `/PMember`; // 进入会员端
          // msgJumpToMember(params);
          /**
           * 修改为 数字平台-切到对应组织
           * https://www.tapd.cn/69781318/bugtrace/bugs/view/1169781318001040845
           */
          goToDigitalPlatform_member(params.teamId);
        } else if (scene === 5031) {
          params.redirect = '/PManage/PRegularMemberPanel'; // 进入管理端
          msgJumpToMember(params);
        } else if ([5040].includes(scene)) {
          goToDigitalPlatform_member(data?.extend?.teamId);
        } else if ([5076, 5077].includes(scene) && data?.extend.digital_uuid === 'member') {
          // 专属名称跳转
          goToDigitalPlatform_member(data?.extend?.team_id);
        }
      };

      const logo = data?.header?.team?.logo || BizUnion;
      const team = data?.header?.team?.name || '';
      return () => (
        <AppCard>
          <AppCardHeader>{i18nt('im.public.biz')}</AppCardHeader>
          <AppCardBody>
            <div style="display:flex;flex-direction:row;align-items:center;overflow: hidden;gap:4px;margin-bottom:6px;">
              <img width="24" height="24" src={logo} />
              <div style="flex:1;overflow:hidden;text-overflow: ellipsis;white-space:nowrap;">{team}</div>
            </div>
            {data?.content?.title}
            {showDetailBtn ? <div style="height:1px;background: #ECEFF5;margin-top: 16px;"></div> : null}
          </AppCardBody>
          {showDetailBtn ? (
            <AppCardFooter>
              <t-button variant="outline" onClick={onClickDetail} style="width: 100%;">
                {' '}
                {i18nt('im.public.detail')}{' '}
              </t-button>
            </AppCardFooter>
          ) : null}
        </AppCard>
      );
    }

    // const APPLY = 14001; //入会申请
    // const APPLY_REJECT = 14002; //入会申请拒绝
    // const ACTIVATE = 14003; //激活申请
    // const ACTIVATE_REJECT = 14004; //激活申请拒绝
    // const AGREE = 14005; //入会申请/激活会员同意

    // 小秘书不处理
    // const EXIT = 14006;//组织退出
    // const APPLY_ADMIN = 14007;//入会申请-发送管理员
    // const ACTIVATE_ADMIN = 14008;//激活申请-发送管理员
    // 小秘书不处理

    // const CONTACT_ADD = 14009;//成为组织联系人-组织代表人
    // const CONTACT_DEL = 14010;//删除组织联系人-组织代表人
    // const CONTACT_ADD_ONESELF = 14011;//成为组织联系人-联系人本人
    // const CONTACT_DEL_ONESELF = 14012;//删除组织联系人-联系人本人
    // const ADMIN_ADD = 14013;//成为应用管理员
    // const ADMIN_DEL = 14014;//删除应用管理员
    // const CONTACT_ADD_ADMIN = 14015;//成为组织联系人-管理员
    // const CONTACT_DEL_ADMIN = 14016;//删除组织联系人-管理员
    // const ALL_ACTIVATE = 14019;//一键激活-发送给代表人
    // const ALL_ACTIVATE_ADMIN = 14020;//一键激活-发送给管理员
    if (
      [14001, 14002, 14003, 14004, 14005, 14006, 14009, 14010, 14011, 14012, 14013, 14014, 14015, 14016, 14019].includes(scene)
      || ([5076, 5077].includes(scene) && data?.extend.digital_uuid === 'government')
    ) {
      // 同一个样式
      let showDetailBtn = [14001, 14002, 14003, 14004, 14005, 14011, 14013, 14019].includes(scene) || ([5076, 5077].includes(scene) && data?.extend.digital_uuid === 'government');
      // 非分组群可以查看
      if (data?.extend?.group_id > 0 && (scene === 14002)) {
        showDetailBtn = false;
      }
      const onClickDetail = () => {
        // 14011;//成为组织联系人-联系人本人 -> 跳转数字城市-打开该政府单位组织下的组织端页面
        // 14013;//成为应用管理员 -> 跳转数字城市-打开该政府单位组织下的管理端页面
        // 14001; //入会申请 -> 点击在独立弹窗打开该政府单位的加入申请H5
        // 14002; //入会申请拒绝 -> 点击在独立弹窗打开该政府单位的加入申请H5
        // 14003; //激活申请 -> 点击在独立弹窗打开对应待激活申请的激活申请H5
        // 14004; //激活申请拒绝 -> 点击在独立弹窗打开对应待激活申请的激活申请H5
        // 14005; //入会申请/激活会员同意 -> 点击查看详情-跳转到通讯录中对应的组织架构
        const params = {
          from: 'message',
          redirect: '', // 进入管理端
          teamId: data?.extend?.teamId,
        };
        if (scene === 14011) {
          goToDigital_platform_politics_my(data?.extend?.teamId);
        } else if (scene === 14013) {
          params.redirect = '/PManage/PRegularMemberPanel'; // 进入管理端
          msgJumpToGovernment(params);
        } else if ([14001, 14002, 14003, 14004].includes(scene)) {
          // 加入申请H5
          jumpH5WithLink(data?.extend?.link, data?.extend?.teamId);
        } else if ([14005, 14019].includes(scene)) {
          // 通讯录中对应的组织架构
          goToDigitalPlatform_member(data?.extend?.teamId);
        } else if ([5076, 5077].includes(scene) && data?.extend.digital_uuid === 'government') {
          // 专属名称跳转
          goToDigitalPlatform_member(data?.extend?.team_id);
        }
      };

      const logo = data?.header?.team?.logo || BizUnion;
      const team = data?.header?.team?.name || '';
      // maxWidth={"460px"}
      return () => (
        <AppCard>
          <AppCardHeader>{i18nt('im.public.government')}</AppCardHeader>
          <AppCardBody>
            <div style="display:flex;flex-direction:row;align-items:center;overflow: hidden;gap:4px;margin-bottom:6px;">
              <img class="rounded-full" width="24" height="24" src={logo} />
              <div style="flex:1;overflow:hidden;text-overflow: ellipsis;white-space:nowrap;">{team}</div>
            </div>
            {data?.content?.title}
            {showDetailBtn ? <div style="height:1px;background: #ECEFF5;margin-top: 16px;"></div> : null}
          </AppCardBody>
          {showDetailBtn ? (
            <AppCardFooter>
              <t-button style="font-weight: 600; width: 100%;" variant="outline" onClick={onClickDetail}>
                {' '}
                {i18nt('im.public.detail')}{' '}
              </t-button>
            </AppCardFooter>
          ) : null}
        </AppCard>
      );
    }
    if (
      [19001, 19002, 19003, 19004, 19005, 19006, 19009, 19010, 19011, 19012, 19013, 19014, 19015, 19016, 19019].includes(scene)
      || ([5076, 5077].includes(scene) && data?.extend.digital_uuid === 'association')
    ) {
      // 同一个样式
      let showDetailBtn = [19001, 19002, 19003, 19004, 19005, 19011, 19013, 19019].includes(scene) || ([5076, 5077].includes(scene) && data?.extend.digital_uuid === 'association');
      // 非分组群可以查看
      if (data?.extend?.group_id > 0 && (scene === 19002)) {
        showDetailBtn = false;
      }
      const onClickDetail = () => {
        const params = {
          from: 'message',
          redirect: '', // 进入管理端
          teamId: data?.extend?.teamId,
        };
        if (scene === 19011) {
          goToDigital_platform_politics_my_asso(data?.extend?.teamId);
        } else if (scene === 19013) {
          params.redirect = '/PManage/PRegularMemberPanel'; // 进入管理端
          goToAdminAssociation(params?.teamId, { origin: 'message', redirect: params?.redirect });
        } else if ([19001, 19002, 19003, 19004].includes(scene)) {
          // 加入申请H5
          jumpH5WithLink(data?.extend?.link, data?.extend?.teamId);
        } else if ([19005, 19019].includes(scene)) {
          // 通讯录中对应的组织架构
          goToDigitalPlatform_member_asso(data?.extend?.teamId);
        } else if ([5076, 5077].includes(scene) && data?.extend.digital_uuid === 'association') {
          // 专属名称跳转
          goToDigital_platform_politics_my_asso(data?.extend?.team_id);
        }
      };

      const logo = data?.header?.team?.logo || BizUnion;
      const team = data?.header?.team?.name || '';
      // maxWidth={"460px"}
      return () => (
        <AppCard>
          <AppCardHeader>{i18nt('niche.szsq')}</AppCardHeader>
          <AppCardBody>
            <div style="display:flex;flex-direction:row;align-items:center;overflow: hidden;gap:4px;margin-bottom:6px;">
              <img class="rounded-full" width="24" height="24" src={logo} />
              <div style="flex:1;overflow:hidden;text-overflow: ellipsis;white-space:nowrap;">{team}</div>
            </div>
            {data?.content?.title}
            {showDetailBtn ? <div style="height:1px;background: #ECEFF5;margin-top: 16px;"></div> : null}
          </AppCardBody>
          {showDetailBtn ? (
            <AppCardFooter>
              <t-button style="font-weight: 600;width: 100%;" variant="outline" onClick={onClickDetail}>
                {' '}
                {i18nt('im.public.detail')}{' '}
              </t-button>
            </AppCardFooter>
          ) : null}
        </AppCard>
      );
    }

    // const ALL_ACTIVATE = 16019;//一键激活-发送给代表人
    // const ALL_ACTIVATE_ADMIN = 16020;//一键激活-发送给管理员
    if ([16019, 5076].includes(scene)) {
      // 同一个样式
      const showDetailBtn = [16019, 5076].includes(scene);
      const onClickDetail = () => {
        // 5027 成为单位会员联系人 -> 跳转数字商协-打开该商协会组织下的会员端页面
        // 5031、5032 成为商协会应用管理员 -> 跳转数字商协-打开该商协会组织下的管理端页面
        const params = {
          from: 'message',
          redirect: '', // 进入管理端
          teamId: data?.extend?.teamId || data?.extend?.team_id,
        };
        if ([16019, 5076].includes(scene)) {
          console.log(params);
          // return;
          goToDigitalPlatform_member(params.teamId);

        }
      };

      const logo = data?.header?.team?.logo || BizUnion;
      const team = data?.header?.team?.name || '';
      return () => (
        <AppCard>
          <AppCardHeader>{i18nt('member.digital.c')}</AppCardHeader>
          <AppCardBody>
            <div style="display:flex;flex-direction:row;align-items:center;overflow: hidden;gap:4px;margin-bottom:6px;">
              <img width="24" height="24" src={logo} />
              <div style="flex:1;overflow:hidden;text-overflow: ellipsis;white-space:nowrap;">{team}</div>
            </div>
            {data?.content?.title}
            {showDetailBtn ? <div style="height:1px;background: #ECEFF5;margin-top: 16px;"></div> : null}
          </AppCardBody>
          {showDetailBtn ? (
            <AppCardFooter>
              <t-button variant="outline" onClick={onClickDetail} style="width: 100%;">
                {' '}
                {i18nt('im.public.detail')}{' '}
              </t-button>
            </AppCardFooter>
          ) : null}
        </AppCard>
      );
    }

    // 13001 对公支付订单审核
    if (msg.contentExtra?.scene === 13001) {
      return () => (
        <AppCard>
          <AppCardHeader>{data?.content?.title}</AppCardHeader>
          <AppCardBody>
            {data?.content?.body?.map((item) => (
              <div style="display:flex; flex-direction:row; align-items:center;gap: 16px;margin-bottom:8px;">
                <div style="color:#828DA5;min-width:88px;">{item.key}</div>
                <div class="line-2" style="flex:1;color:#1A2139;">
                  {item.value}
                </div>
              </div>
            ))}
            <div style="height:1px;background: var(--divider-kyy_color_divider_light, #ECEFF5);"></div>
          </AppCardBody>
          <AppCardFooter>
            <t-button
              variant="outline"
              style="width: 100%;"
              onClick={() => {
                const confirmDia = DialogPlugin({
                  header: i18nt('im.public.tips'),
                  body: i18nt('im.msg.invoiceTip'),
                  confirmBtn: i18nt('banch.qwzzht'),
                  onConfirm: () => {
                    if (data.extend?.team_id) {
                      jumpWeb('/#/orderManagement/myOrder', {
                        teamId: data.extend.team_id,
                        orderId: data.extend.order_sn,
                      });
                    }
                    confirmDia.hide();
                  },
                  onClose: () => {
                    confirmDia.hide();
                  },
                });
              }}
            >
              {' '}
              {i18nt('im.msg.checkOrder')}{' '}
            </t-button>
          </AppCardFooter>
        </AppCard>
      );
    }

    if (naasSceneTypeArr.includes(scene)) {
      return () => <Naas msg={props.data} />;
    }
    if (AgentSceneTypeArr.includes(scene)) {
      return () => <Agent msg={props.data} />;
    }
    if (TransAgentSceneTypeArr.includes(scene)) {
      return () => <TransAgent msg={props.data} />;
    }
    if (SceneTypeArr.includes(scene)) {
      return () => <CBD msg={props.data} />;
    }
    if (partnerSceneTypeArr.includes(scene)) {
      return () => <Partner msg={ props.data } />;
    }

    if (AdCardTypeArr.includes(scene)) {
      return () => <AdCard msg={ props.data } />;
    }

    if (VisitorTypeArr.includes(scene)) {
      return () => <Visitor msg={ props.data } />;
    }
    if (AnnoAlertTypeArr.includes(scene)) {
      return () => <AnnoAlert msg={ props.data } />;
    }
    if (DigitalTypeArr.includes(scene)) {
      return () => <Digital msg={ props.data } />;
    }
    if (ShopTypeArr.includes(scene)) {
      return () => <Shop msg={ props.data } />;
    }
    return () => (
      <AppCard>
        <AppCardHeader>{data?.content?.title}</AppCardHeader>
        <AppCardBody>{` ${msg.contentExtra.contentType} - ${msg.contentExtra?.scene}`}</AppCardBody>
      </AppCard>
    );
  },
});
