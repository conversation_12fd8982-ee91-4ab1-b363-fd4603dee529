/* eslint-disable no-nested-ternary */
import { defineComponent, PropType, ref, defineAsyncComponent, computed } from 'vue';
import LynkerSDK from '@renderer/_jssdk';
import { goToAdmin as goToAdminMember, goToDigitalPlatform_member } from '@renderer/views/member/utils/auth';
import { goToAdmin as goToAdminPolitics } from '@renderer/views/politics/utils/auth';
import { goToAdmin as goToAdminCBD } from '@renderer/views/cbd/utils/auth';
import { goToAdmin as goToAdminAssociation } from '@renderer/views/association/utils/auth';
import { goToAdmin as goToAdminUni } from '@renderer/views/uni/utils/auth';
import { getApplyStatus } from '@renderer/api/im/chat';
import to from 'await-to-js';

import { IMRefreshType } from '@renderer/views/message/common/constant';
import PreviewRelease from '@renderer/views/workBench/components/previewRelease.vue';
import { detailHonorAPI } from '@renderer/api/workBench';
import { MessagePlugin, Button } from 'tdesign-vue-next';
import { getFrontIntroduceDetailById } from '@renderer/api/workBench/introduce';
import { getFrontGrowthDetailById } from '@renderer/api/workBench/growth';
import AboutDialog from '@renderer/views/workBench/components/AboutDialog.vue';
import { getBaseUrl } from '@renderer/utils/apiRequest';
import Avatar from '@renderer/components/kyy-avatar/index.vue';
import { policyDetail, interpretDetail } from '@renderer/api/policy-express';
import { getPolicyTitle, PolicySceneTypeArr } from './Policy/constant';
import { CultureSceneTypeArr } from './CultureTourism/constant';
import { keyMap } from '../../common/constant';
import { AppCard, AppCardHeader, AppCardBody, AppCardFooter, AppCardBtn, AppCardText } from '../../MessageAppCard';
import { i18nt } from '@/i18n';
import { getBtnTxt } from './constant';
import { useChatExtendStore } from '../../../service/extend';
import BizUnion from '@/assets/im/biz_union.png';
import Anno from './Anno/index.vue';
import TransAgent from './transAgent/index.vue';
import PopularizeCard from './PopularizeCard/index.vue';
import AdCard from './AdCard/index.vue';
import FeePackage from './FeePackage/index.vue';
import Visitor from './Visitor/index.vue';
import CultureTourismEle from './CultureTourism/index.vue';
import SpServerEle from './SpServer/index.vue';
import PromoteEle from './promote/index.vue';
import { SpServerSceneTypeArr } from './SpServer/constant';
import { getAppTeamsBusssinessEle } from '../../MessageContent.vue';

const { ipcRenderer, shell } = LynkerSDK;

const AuditeeDetail = defineAsyncComponent(() => import('@renderer/components/pb/imReview/auditee/index.vue'));
const AuditeeDetailFengcai = defineAsyncComponent(
  () => import('@renderer/components/fengcai/imReview/auditee/index.vue'),
);

const heaaderColorMap = {
  15001: 'secondary', // 审核人审核通知
  15002: 'success', // 被审核人审核通过
  15003: 'danger', // 被审核人审核拒绝
  15004: 'secondary', // 历程审核通知
  15005: 'success', // 历程审核通过
  15006: 'danger', // 历程审核拒绝
  15007: 'secondary', // 介绍审核通知
  15008: 'success', // 介绍审核通过
  15009: 'danger', // 介绍审核拒绝
  15010: 'secondary', // 荣誉榜审核通知
  15011: 'success', // 荣誉榜被审核通过
  15012: 'danger', // 荣誉榜被审核拒绝
  15101: 'secondary', // 风采审核
  15102: 'success',
  15103: 'danger',
  15202: 'success',
  15203: 'danger',
  15201: 'secondary',
  15205: 'success',
  15206: 'danger',
  15204: 'secondary',
};
/**
 * 工作通知
 * @param msg
 * 5019 商协会会员过期管理员工作通知,商协会入会5023、商协会激活5024
 * 数字城市 14015 成为组织联系人-管理员，14016 删除组织联系人-管理员，14007 入会申请-发送管理员，14008 激活申请-发送管理员
 * 数字CBD 16015 成为组织联系人-管理员，16016 删除组织联系人-管理员，16007 入会申请-发送管理员，16008 激活申请-发送管理员
 */
const heaaderTitleMap = {
  15002: '党建已通过', // 被审核人审核通过
  15003: '党建已被拒绝', // 被审核人审核拒绝
  15102: '平台风采已通过', // 被审核人审核通过
  15103: '平台风采已被拒绝', // 被审核人审核拒绝
};
// 通知类型
const noticeTypeMap = (scene) => {
  // 入会5023、激活5024、会刊生成成功17001
  if ([5023, 14007, 19007, 16007, 51007].includes(scene)) return 'isDigitalApply';
  // 是否党建，除了党建的其他兼容以前的商协会的写死了
  if ([15001, 15002, 15003].includes(scene)) return 'isPb';
  // 是风采
  if ([15101, 15102, 15103].includes(scene)) return 'isFc';
  // 是否历程
  if ([15004, 15005, 15006].includes(scene)) return 'isProcess';
  // 是否介绍
  if ([15007, 15008, 15009].includes(scene)) return 'isIntroduction';
  // 是否荣誉榜
  if ([15010, 15011, 15012].includes(scene)) return 'isHonor';
  // 数字平台-开启应用及续费
  if ([5034, 7017, 5076, 5078].includes(scene)) return 'isAppRenew';
  // 公告
  if ([15013, 15014, 15015, 15016, 15017].includes(scene)) return 'Anno';
  // 访客
  if ([5082, 14042, 16042, 19042, 51042].includes(scene)) return 'TransAgent';
  // 推广
  if ([5037, 5038, 5039].includes(scene)) return 'PopularizeCard';
  // 广告
  if ([22001, 22002, 22003].includes(scene)) return 'Advertise';
  // 体验套餐
  if (scene === 7021) return 'FeePackage';
  // 入会申请
  if ([5060, 5061, 14030, 14031, 16030, 16031, 19030, 19031, 51031, 51030].includes(scene)) return 'Visitor';
  // 政策列车
  if (PolicySceneTypeArr.includes(scene)) return 'policyExpross';
  // 文旅
  if (CultureSceneTypeArr.includes(scene)) return 'cultureTourism';
  // 特色服务
  if (SpServerSceneTypeArr.includes(scene)) return 'spServer';
  if ([20053, 20055].includes(scene)) return 'acPromote';
  // 商协会用户消息场景值：5007、5008、5009、5010、5011、5012、5013、5014
  if ([5007, 5008, 5009, 5010, 5011, 5012, 5013, 5014].includes(scene)) return 'isBIZ';
};

export default defineComponent({
  props: {
    data: { type: Object as PropType<MessageToSave>, required: true },
  },
  setup(props) {
    const loading = ref(true);
    const data = props.data.contentExtra?.data;
    console.log('🚀 ~ setup ~ data:', data);
    const logo = data?.header?.team?.logo || BizUnion;
    const team = data?.header?.team?.name;
    const sceneType = noticeTypeMap(data.scene);
    // 以前没有罗列的场景值
    const beforeScene = [5019, 5020, 5021, 5022, 14015, 14016, 14020, 16015, 16016, 16020, 17002, 19015, 19016, 19020, 51020, 51016, 51015].includes(data.scene);
    // 查看详情内容
    const link = [5023, 5024, 14007, 14008, 19007, 19008, 51007, 51008, 16007, 16008, 17001, 5077].includes(data.scene);
    // eslint-disable-next-line no-nested-ternary
    let title = [14015, 14016, 14007, 14008, 14020].includes(data.scene)
      ? i18nt('im.public.government')
      : [16015, 16016, 16007, 16008, 16020].includes(data.scene)
        ? i18nt('application.digital_cbd')
        : i18nt('im.public.biz');

    // 数字平台 会刊生成失败17002、会刊生成成功17001
    if ([17001, 17002].includes(data.scene)) {
      title = data?.extend?.title || title;
    }
    if ([15101, 15102, 15103].includes(data.scene)) {
      title = i18nt('banch.ptfc');
    }
    if ([19007, 19008, 19015, 19016, 19020].includes(data.scene)) {
      title = '数字社群';
    }
    if ([51007, 51008, 51015, 51016, 51020].includes(data.scene)) {
      title = '数字高校';
    }
    // 专属名称分类
    if ([5076, 5077, 5078].includes(data.scene)) { // 平台专属名称
      console.log('kakaxi:', data?.extend?.digital_uuid, data.scene);
      switch (data?.extend?.digital_uuid) {
        case 'association':
          console.log('association', i18nt('niche.szsq'));
          title = i18nt('niche.szsq');
          break;
        case 'member':
          title = i18nt('im.public.biz');
          break;
        case 'government':
          title = i18nt('im.public.government');
          break;
        case 'cbd':
          title = i18nt('application.digital_cbd');
          break;
        case 'uni':
          title = i18nt('member.digital.e1');
          break;
        default:
          title = i18nt('im.public.biz');

      }
    }
    // 风采审核
    const isAuditee = [15002, 15003, 15102, 15103].includes(data?.scene);

    // 是否被审核人
    const isAuditeeRef = ref(false);

    // 是否展示详情弹窗（荣誉榜、历程、介绍共用）
    const showPreviewRelease = ref(false);
    const previewPublishVertifyVisible = ref(false);
    const aboutInfoData = ref();
    const aboutDialogType = computed(() => {
      if (sceneType === 'isIntroduction') {
        return 'introduce';
      }
      if (sceneType === 'isProcess') {
        return 'timeline';
      }
      return '';
    });

    const goToAlbum = async (url, pathType) => {
      if (!url) return;
      const fns = {
        Politics: () => goToAdminPolitics(data?.extend?.teamId, { origin: 'message', redirect: url }),
        CBD: () => goToAdminCBD(data?.extend?.teamId, { origin: 'message', redirect: url }),
        Member: () => goToAdminMember(data?.extend?.teamId, { origin: 'message', redirect: url }),
        Association: () => goToAdminAssociation(data?.extend?.teamId, { origin: 'message', redirect: url }),
        Uni: () => goToAdminUni(data?.extend?.teamId, { origin: 'message', redirect: url }),

      };
      fns[pathType]();
    };

    const status = ref(0);
    const loadBatchStatus = async () => {
      const { platform_uuid: type, id } = data?.extend || {};
      if (!id) {
        return;
      }
      const params = {
        ids: [String(id)],
        type,
      };
      const [err, res] = await to(getApplyStatus(params));

      if (err) {
        return;
      }
      const { code, data: { status_list } } = res.data || {};
      if (code === 0) {
        status.value = status_list[0]?.status;
      }
    };
    const updateStatus = (e, args) => {
      const id = data?.extend?.id;
      if (!id) {
        return;
      }
      const { type, data: { extend } } = args || {};
      if (type === IMRefreshType.IMRefreshDigital && extend?.id === id) {
        loadBatchStatus();
      }
    };
    if (sceneType === 'isDigitalApply') {
      loadBatchStatus();
      ipcRenderer.on('IM-refresh', updateStatus);
    }
    const goDigitalDetail = () => {
      loadBatchStatus();
      useChatExtendStore().showChatDialog('digital-apply-info', props.data);
    };
    const returnPage = () => {
      if (sceneType === 'isDigitalApply') {
        goDigitalDetail();
        return;
      }

      let url = '';
      let pathType = '';
      if (data.scene === 5023) {
        url = '/PManage/PRegularMemberPanel/PApplyMemberPanel';
        pathType = 'Member';
      } else if (data.scene === 5024) {
        url = '/PManage/PRegularMemberPanel/PActiveMemberPanel';
        pathType = 'Member';
      } else if (data.scene === 14007) {
        // 跳转到数字商协会-入会申请
        url = '/politicsIndex/politics_number/PManage/PApplyMemberPanel';
        pathType = 'Politics';
      } else if (data.scene === 14008) {
        // 跳转到数字商协会-激活申请
        url = '/politicsIndex/politics_number/PManage/PActiveMemberPanel';
        pathType = 'Politics';
      } else if (data.scene === 16007) {
        // 跳转到数字CBD-入会申请
        url = '/cbdIndex/cbd_number/PManage/PApplyMemberPanel';
        pathType = 'CBD';
      } else if (data.scene === 16008) {
        // 跳转到数字CBD-激活申请
        url = '/cbdIndex/cbd_number/PManage/PActiveMemberPanel';
        pathType = 'CBD';
      } else if (data.scene === 19007) {
        url = '/cbdIndex/association_number/PManage/PApplyMemberPanel';
        pathType = 'Association';
      } else if (data.scene === 51007) {
        url = '/uniIndex/uni_number/PManage/PApplyMemberPanel';
        pathType = 'Uni';
      } else if (data.scene === 19008) {
        url = '/cbdIndex/association_number/PManage/PActiveMemberPanel';
        pathType = 'Association';
      } else if (data.scene === 51008) {
        url = '/uniIndex/uni_number/PManage/PActiveMemberPanel';
        pathType = 'Association';
      } else if (data.scene === 5034) {
        switch (data?.extend?.uuid) {
          case 'member':
            goToAdminMember(data?.extend?.teamId);
            break;
          case 'government':
            goToAdminPolitics(data?.extend?.teamId);
            break;
          case 'cbd':
            goToAdminCBD(data?.extend?.teamId);
            break;
          case 'association':
            goToAdminAssociation(data?.extend?.teamId);
            break;
          case 'uni':
            goToAdminUni(data?.extend?.teamId);
            break;

          default:
            goToAdminMember(data?.extend?.teamId);
            break;
        }

        return;
      } else if ([5076, 5077].includes(data.scene)) { // 平台专属名称
        switch (data?.extend?.digital_uuid) {
          case 'association':
            break;
          case 'member':
            break;
          case 'government':
            break;
          case 'cbd':
            break;
          case 'uni':
            break;
          default:

            break;
        }
        goToDigitalPlatform_member(data?.extend?.team_id);

      } else if (data.scene === 5078) { // 立即续期
        useChatExtendStore().showChatDialog('exclusive-renew', props.data);
        return;
      } else if (data.scene === 7017) {
        useChatExtendStore().showChatDialog('square-helper', props.data);
        return;
      } else if (data.scene === 17001) {
        const url = `${getBaseUrl('h5')}/ebook/browse?id=${data.extend?.uuid}`;
        shell.openExternal(url);
        return;
      }
      goToAlbum(url, pathType);
    };
    const goToDetailFc = () => {
      if (isAuditee) {
        isAuditeeRef.value = true;
        return;
      }
      useChatExtendStore().showChatDialog('fc-detail', props.data);
    };
    const goToDetail = () => {
      if (isAuditee) {
        isAuditeeRef.value = true;
        return;
      }
      useChatExtendStore().showChatDialog('pb-detail', props.data);
    };

    const goToDetailHonor = async () => {
      // useChatExtendStore().showChatDialog([15010].includes(data?.scene) ? 'security-verification-honor' : 'preview-release-honor', props.data);
      if ([15010].includes(data?.scene)) {
        useChatExtendStore().showChatDialog('security-verification-honor', props.data);
      } else {
        const res = await detailHonorAPI(data?.extend?.id, data?.extend?.team_id);
        if (res?.data?.code === 0) {
          if (res?.data?.data?.is_delete) {
            return MessagePlugin.warning(i18nt('im.public.contentDeleted'));
          } if (res?.data?.data?.status === 5) {
            return MessagePlugin.warning(i18nt('im.public.contentRevoked'));
          }
        }
        showPreviewRelease.value = true;
      }
    };
    const goToDetailProcess = async () => {
      if ([15004].includes(data?.scene)) {
        useChatExtendStore().showChatDialog('security-verification-process', props.data);
      } else {
        const res = await getFrontGrowthDetailById(data?.extend?.id, data?.extend?.team_id);
        if (res?.data?.code === 0) {
          if (res?.data?.data?.is_delete) {
            return MessagePlugin.warning(i18nt('im.public.contentDeleted'));
          } if (res?.data?.data?.status === 5) {
            return MessagePlugin.warning(i18nt('im.public.contentRevoked'));
          }
        }
        aboutInfoData.value = res?.data?.data;
        previewPublishVertifyVisible.value = true;
      }
      // useChatExtendStore().showChatDialog([15004].includes(data?.scene) ? 'security-verification-process' : 'preview-release-process', props.data);
    };
    const goToDetailIntroduce = async () => {
      if ([15007].includes(data?.scene)) {
        useChatExtendStore().showChatDialog('security-verification-introduce', props.data);
      } else {
        const res = await getFrontIntroduceDetailById(data?.extend?.id, data?.extend?.team_id);
        if (res?.data?.code === 0) {
          if (res?.data?.data?.is_delete) {
            return MessagePlugin.warning(i18nt('im.public.contentDeleted'));
          } if (res?.data?.data?.status === 5) {
            return MessagePlugin.warning(i18nt('im.public.contentRevoked'));
          }
        }
        aboutInfoData.value = res?.data?.data;
        previewPublishVertifyVisible.value = true;
      }
      // useChatExtendStore().showChatDialog([15007].includes(data?.scene) ? 'security-verification-introduce' : 'preview-release-introduce', props.data);
    };
    const goToPolicyDetail = async () => {
      console.log('🚀 ~ goToPolicyDetail ~ data:', data.scene);
      useChatExtendStore().hideChatDialog();
      const isPolicy = [15201, 15202, 15203].includes(data.scene);
      const api = isPolicy ? policyDetail : interpretDetail;
      // 点击通过和拒绝，先校验文章是否存在，
      try {
        const id = data?.extend?.article_id;
        const res = await api(id, 1, 1);
        const result = res.status !== 200 ? res.response.data : res.data;
        console.log('await', res, result);
        // 申请去验证页面
        if ([15201, 15204].includes(data.scene)) {
          // 申请打开安全验证页, status 0 草稿状态；1 待审核；2 拒绝；3 已发布；4 撤回; code 110002 内容被删除 90005 无权限
          const status = result?.code !== 0 ? result?.code : result.data?.status;
          const remark = result.data?.status === 2 ? result.data?.remark : '';
          const param = { ...data, status, remark, isPolicy };
          useChatExtendStore().showChatDialog('policyExpross', param);
          return;
        }
        if (result?.code === 0) {
          if (result.data?.status === 4) {
            return MessagePlugin.warning(i18nt('im.public.contentRevoked'));
          }
          // 打开预览文章窗口
          const params = { url: `/im_policy/im_policy_review?id=${id}&isPolicy=${isPolicy}&title=审核详情&from=im` };
          LynkerSDK.openPolicyDetailWindow(params);
          return;
        }
        MessagePlugin.warning(result.message);
      } catch (error) {
        console.log('error', error.status);
        if (error.status === 404) {
          MessagePlugin.warning('文章不存在');
        }
      }
    };
    const onCloseDrawer = () => {
      showPreviewRelease.value = false;
    };
    // PoliceTODO 对接消息 验证无权限 独立弹窗审核详情状态。 拒绝验证
    const defaultEle = () => (
      <AppCard>
        <AppCardHeader>{title}</AppCardHeader>
        <AppCardBody>
          <div style="display:flex;flex-direction:row;justify-content:space-between;" class="mb-12">
            <img width="24" height="24" src={logo} style="border-radius: 50%;" />
            <div style="flex:1;margin-left:4px;"> {team}</div>
          </div>
          <div class="mt-8 mb-12">{data?.content?.title}</div>
          {
            sceneType === 'isDigitalApply'
              ? data?.extend?.content?.map((item) => (
                <>
                  <div class="text-#828DA5 mb-4">{item?.key}</div>
                    <div class="flex mb-12">
                      { keyMap[item.key] === 'none' ? null : item?.img ? <img class="w-24 h-24 rounded-full mr-4" src={item?.img} />
                        : keyMap[item.key] === 'defaultOrganize' ? <img class="w-24 h-24 rounded-full mr-4" src={BizUnion} />
                          : item?.value
                            ? <Avatar
                        class="mr-4"
                        image-url={item?.img}
                        user-name={item.value}
                        avatar-size="24px"
                        round-radius
                      /> : null }
                      <div>{item?.value}</div>
                  </div>
                </>
              ))
              : null
          }
          {
            <div class="h-1 bg-[#ECEFF5] mt-16" />
          }
        </AppCardBody>
        {link ? (
          <AppCardFooter>
            {
              sceneType === 'isDigitalApply' && getBtnTxt(status.value)
                ? <Button class="w-full fw-600 text-[#ACB3C0]!" variant="outline" disabled onClick={returnPage}>
                  { getBtnTxt(status.value) }
                </Button>
                : <Button class="w-full fw-600" variant="outline" onClick={returnPage}>查看详情</Button>
            }
          </AppCardFooter>
        ) : null}
      </AppCard>
    );
    const fcEle = () => (
      <AppCard>
        <AppCardHeader theme={heaaderColorMap[data?.scene]}>
          {isAuditee ? heaaderTitleMap[data?.scene] : data?.content?.title}
        </AppCardHeader>
        <AppCardBody>
          {data?.content?.body?.map((item) => (
            <div class="flex gap-16 overflow-hidden mb-8">
              <AppCardText type="info" class="w-88">
                {item.key}
              </AppCardText>
              <AppCardText class="flex-1 ellipsis-1">{item.value}</AppCardText>
            </div>
          ))}
          {isAuditee ? <div style="color: 'var(--text-kyy_color_text_1, #1A2139);">{data?.content?.title}</div> : null}
          {data?.content?.body?.length ? <div class="h-1 w-full mt-16 bg-[#ECEFF5]"></div> : null}
        </AppCardBody>
        <AppCardFooter>
          <Button class="w-full fw-600" variant="outline" onClick={goToDetailFc}>
            查看详情
          </Button>
        </AppCardFooter>
      </AppCard>
    );
    const pbEle = () => (
      <AppCard>
        <AppCardHeader theme={heaaderColorMap[data?.scene]}>
          {isAuditee ? heaaderTitleMap[data?.scene] : data?.content?.title}
        </AppCardHeader>
        <AppCardBody>
          {data?.content?.body?.map((item) => (
            <div class="flex gap-16 overflow-hidden mb-8">
              <AppCardText type="info" class="w-88">
                {item.key}
              </AppCardText>
              <AppCardText class="flex-1 ellipsis-1">{item.value}</AppCardText>
            </div>
          ))}
          {isAuditee ? <div style="color: 'var(--text-kyy_color_text_1, #1A2139);">{data?.content?.title}</div> : null}
          {data?.content?.body?.length ? <div class="h-1 w-full mt-16 bg-[#ECEFF5]"></div> : null}
        </AppCardBody>
        <AppCardFooter>
          <AppCardBtn text="查看详情" theme="text" onClick={goToDetail} />
        </AppCardFooter>
      </AppCard>
    );

    const honorEle = (toDetailMethod?) => (
      <AppCard>
        <AppCardHeader theme={heaaderColorMap[data?.scene]}>{data?.content?.title}</AppCardHeader>
        <AppCardBody>
          {data?.content?.body?.map((item) => (
            <div class="flex gap-16 overflow-hidden mb-8">
              <AppCardText type="info" class="w-88">
                {item.key}
              </AppCardText>
              <AppCardText class="flex-1 ellipsis-1">{item.value}</AppCardText>
            </div>
          ))}
          {data?.content?.body?.length ? <div class="h-1 w-full mt-16 bg-[#ECEFF5]"></div> : null}
        </AppCardBody>
        <AppCardFooter>
          <Button class="w-full fw-600" variant="outline" onClick={toDetailMethod || goToDetailHonor}>
            查看详情
          </Button>

        </AppCardFooter>
        <PreviewRelease
          visible={showPreviewRelease.value}
          type={'detail'}
          data={data?.extend}
          onCloseDrawer={onCloseDrawer}
        ></PreviewRelease>

        <AboutDialog
          modelValue={previewPublishVertifyVisible}
          type={aboutDialogType}
          data={aboutInfoData}
          // eslint-disable-next-line no-return-assign
          onClose={() => (previewPublishVertifyVisible.value = false)}
        ></AboutDialog>
      </AppCard>
    );
    let headerT = '';
    if (data.scene === 7017) {
      headerT = data?.extend?.title;
    } else if ([5076, 5077, 5078].includes(data.scene)) {
      switch (data.extend.digital_uuid) {
        case 'association':
          headerT = i18nt('niche.szsq');
          break;
        case 'member':
          headerT = i18nt('im.public.biz');
          break;
        case 'government':
          headerT = i18nt('im.public.government');
          break;
        case 'cbd':
          headerT = i18nt('member.digital.c');
          break;
        case 'uni':
          headerT = i18nt('member.digital.e1');
          break;
        default:
          headerT = i18nt('im.public.biz');
          break;
      }
    } else {
      headerT = data?.header?.title || i18nt('im.public.biz');
    }

    const appRenewEle = () => (
      <AppCard>
        <AppCardHeader>
          {headerT}
        </AppCardHeader>
        <AppCardBody>
          <div style="display:flex;flex-direction:row;justify-content:space-between;">
            <img width="24" height="24" src={logo} style="border-radius: 50%;" />
            <div style="flex:1;margin-left:4px;"> {team}</div>
          </div>
          <div style="margin-top:8px;">{data?.content?.title}</div>
          <div class="h-1 w-full mt-16 bg-[#ECEFF5]"></div>
        </AppCardBody>
        <AppCardFooter>
          <Button class="w-full fw-600" variant="outline" onClick={returnPage}>
            {[7017, 5078].includes(data.scene) ? (data.scene === 5078 ? '立即续期' : '立即续费') : '查看详情'}
          </Button>
        </AppCardFooter>
      </AppCard>
    );

    const policyEle = () => (
      <AppCard onClick={goToPolicyDetail}>
        <AppCardHeader theme={heaaderColorMap[data?.scene]}>
          { getPolicyTitle(data?.scene, data?.content?.title) }
        </AppCardHeader>
        <AppCardBody>
          { [15201, 15204].includes(data?.scene) ? data?.content?.body?.map((item) => (
            <div class="flex gap-16 overflow-hidden mb-8">
              <AppCardText type="info" class="w-88">
                {item.key}
              </AppCardText>
              <AppCardText class="flex-1 ellipsis-1">{item.value}</AppCardText>
            </div>
          )) : <div style="color: 'var(--text-kyy_color_text_1, #1A2139);">{data?.content?.title}</div> }
          <div class="h-1 w-full mt-16 bg-[#ECEFF5]"></div>
        </AppCardBody>
        <AppCardFooter>
          <Button class="w-full fw-600! text-[#516082]!" variant="outline">
              查看详情
          </Button>
        </AppCardFooter>
      </AppCard>
    );
    const getEle = () => {
      // return <FeePackage msg={props.data} />
      loading.value = false;
      switch (sceneType) {
        case 'isPb':
          return pbEle();
        case 'isFc':
          return fcEle();
        case 'isHonor':
          return honorEle();
        case 'isAppRenew':
          return appRenewEle();
        case 'isIntroduction':
          return honorEle(goToDetailIntroduce);
        case 'isProcess':
          return honorEle(goToDetailProcess);
        case 'Anno':
          return <Anno msg={props.data} />;
        case 'TransAgent':
          return <TransAgent msg={props.data} />;
        case 'PopularizeCard':
          return <PopularizeCard msg={props.data} />;
        case 'FeePackage':
          return <FeePackage msg={props.data} />;
        case 'Advertise':
          return <AdCard msg={props.data} />;
        case 'Visitor':
          return <Visitor msg={props.data} />;
        case 'policyExpross':
          return policyEle();
        case 'cultureTourism':
          return <CultureTourismEle msg={props.data} />;
        case 'spServer':
          return <SpServerEle msg={props.data} />;
        case 'acPromote':
          return <PromoteEle msg={props.data} />;
        case 'isBIZ':
          return getAppTeamsBusssinessEle(props.data);
        default:
          if (beforeScene || link) {
            return defaultEle();
          }
          console.error('未匹配到场景', data);
          return <AppCard> <AppCardHeader> 暂不支持类型 </AppCardHeader> </AppCard>;
      }
    };
    return () => (
      <div style={loading.value ? 'height:320px' : ''}>
        {getEle()}
        {isAuditeeRef.value ? (
          [15101, 15102, 15103].includes(data.scene) ? (
            <AuditeeDetailFengcai
              onClose={() => {
                isAuditeeRef.value = false;
              }}
              data={data}
              parentVisible={isAuditeeRef.value}
            />
          ) : (
            <AuditeeDetail
              onClose={() => {
                isAuditeeRef.value = false;
              }}
              data={data}
              parentVisible={isAuditeeRef.value}
            />
          )
        ) : null}
      </div>
    );
  },
});
