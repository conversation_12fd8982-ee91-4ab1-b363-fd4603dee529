<template>
  <div class="head-box">
    <iconpark-icon name="iconSpecial-graphics" class="icon-specials" />

    <div v-show="isShowTabsScrollIcon" class="tabs-arrow" @click="onScrollTabs(1)">
      <iconpark-icon name="iconarrowlift" />
    </div>
    <div ref="tabsListRef" class="tabs-list">
      <div
        class="tabs-list--box"
        :style="{ transform: `translate3d(${tabsScrollLeft}px, 0, 0)` }"
      >
        <div
          v-for="(item, index) in tabStore.tabs"
          :key="index"
          :class="['tabs-item', { selected: tabStore.activeIndex === index }]"
          @click.stop="switchTab(item, index)"
        >
          <div style="display: flex; align-items: center; gap: 8px;">
            <!-- <img class="tab-icon" :src="TAB_ICON" alt="" /> selected-->
            <img
              v-if="item.title === t('square.route.album')"
              class="tabsIcon mr-0"
              src="../../../assets/square/animoji.svg"
            >
            <i v-else :class="[`i-svg-color:${item.icon ? item.icon : props.icon}`, 'tabsIcon']" />
            <div class="title line-1" :title="item.title">{{ item.title }}</div>
          </div>
          <iconpark-icon
            v-if="!item.affix"
            name="iconerror"
            class="ml-0! !text-20"
            @click.stop="removeTab(item)"
          />
          <div v-if="tabStore.activeIndex !== index && tabStore.activeIndex - 1 !== index" class="tab-divider" />
        </div>
      </div>
    </div>
    <div v-show="isShowTabsScrollIcon" class="tabs-arrow" @click="onScrollTabs(-1)">
      <iconpark-icon name="iconarrowright" />
    </div>

    <t-popup
      v-model="accountVisible"
      placement="bottom-right"
      overlay-inner-class-name="square-account-list-popup"
      :disabled="accountList.length <= 1"
    >
      <div class="account">
        <t-badge dot :count="otherAccountCount">
          <SquareAvatar
            :square="store.squareInfo.square"
            size="24px"
            class="avatar"
            :symbol="false"
          />
        </t-badge>
        <div class="text">{{ store.squareInfo.square?.name }}</div>
        <iconpark-icon
          v-if="accountList.length > 1"
          name="iconarrowdown"
          class="arrow"
        />
      </div>

      <template #content>
        <div class="account-wrap">
          <div
            v-for="item in accountList"
            :key="item.square.squareId"
            :class="[
              'account-item',
              { active: item.square.squareId === store.squareId },
            ]"
            @click="accountClick(item)"
          >
            <iconpark-icon
              name="iconother"
              :class="{ 'op-0': item.square.squareId !== store.squareId }"
              class="icon"
            />
            <SquareAvatar
              :square="item.square"
              size="24px"
              class="avatar"
              :symbol="false"
            />
            <div class="name line-1">{{ item.square.name }}</div>

            <!--当前账号不显示红点-->
            <template v-if="item.square.squareId !== store.squareId">
              <t-badge
                v-if="item.square.squareType === SquareType.Individual"
                :count="item.notices || store.unreadPostCount"
                :dot="store.unreadPostCount > 0"
                size="small"
                class="mr-5"
              />
              <t-badge
                v-else
                :count="item.notices"
                size="small"
                class="mr-5"
              />
            </template>
          </div>
        </div>
      </template>
    </t-popup>

    <t-divider layout="vertical" style="border-color: #d5dbe4" />

    <iconpark-icon name="iconrefresh" class="btn-op" @click="refresh" />
    <iconpark-icon
      v-if="newWinFlag"
      name="iconwindow"
      class="btn-op"
      @click="openStandAloneWin"
    />
  </div>
</template>

<script setup lang="ts">
// import TAB_ICON from "@renderer/assets/svg/square/apply-notes.svg";
// import SERVICE_ICOM from "@renderer/assets/service/icon_service.svg";
import { useRoute, useRouter } from 'vue-router';
import {
  computed, onBeforeMount, ref, watch, nextTick,
} from 'vue';
import { DialogPlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import LynkerSDK from '@renderer/_jssdk';
import { useSquareStore } from '../store/square';
import SquareAvatar from '@/views/square/components/SquareAvatar.vue';
import { SquareType } from '@/api/square/enums';
import { toAccount } from '@/views/square/utils/business';
import { useStateStore } from '../store/state';
import { useTabsStore } from '@/components/page-header/store';

const { ipcRenderer } = LynkerSDK;
// const props = defineProps<{
//   from?: string;
//   icon?: string;
// }>();

const props = withDefaults(
  defineProps<{
    from?: string;
    icon?: string;
  }>(),
  {
    icon: 'apply-notes',
  },
);

const emit = defineEmits(['change', 'refresh', 'loaded']);

const router = useRouter();
const store = useSquareStore();
const tabStore = useTabsStore();
const stateStore = useStateStore();
const { t } = useI18n();

const newWinFlag = ref(true);

const accountList = ref([]);
const accountNoticeCount = ref(0);
const isShowAccountSwitchDialog = ref(false);
const accountVisible = ref(false);

// 状态管理：控制移动tabs的偏移距离
const tabsScrollLeft = ref(0);
// 状态管理：获取tabs-list的dom使用
const tabsListRef = ref(null);
// 状态管理：是否现实移动的图标
const isShowTabsScrollIcon = ref(false);
/**
 * 点击左右箭头图标移动tabs拦
 * @param normal 移动的方向
 */
const onScrollTabs = async (normal: -1 | 1, index = 0) => {
  await nextTick();
  const tabItemWidth = 180;
  tabsScrollLeft.value += tabItemWidth * normal * (index + 1);
  const scrollWidth = tabsListRef.value.scrollWidth;
  const offsetWidth = tabsListRef.value.offsetWidth;
  if (tabsScrollLeft.value < -scrollWidth + offsetWidth) {
    tabsScrollLeft.value = -scrollWidth + offsetWidth;
  } else if (tabsScrollLeft.value > 0) {
    tabsScrollLeft.value = 0;
  }
};

watch(() => tabStore.tabs, async () => {
  await nextTick();
  const scrollWidth = tabsListRef.value.scrollWidth;
  const offsetWidth = tabsListRef.value.offsetWidth;
  isShowTabsScrollIcon.value = scrollWidth > offsetWidth;
}, {
  immediate: true,
  deep: true,
});
watch(() => tabStore.activeIndex, async (newVal: number, oldVal: number) => {
  await nextTick();
  onScrollTabs(newVal - oldVal > 0 ? -1 : 1, newVal);
}, {
  immediate: true,
});

// 其他账号的未读数
const otherAccountCount = computed(() => {
  const otherCount = accountList.value
    .filter((v) => v.square.squareId !== store.squareId)
    .map((v) => v.notices)
    .reduce((total, val) => total + val, 0);

  // 个人账号的新动态也统计
  return store.isPersonal ? otherCount : otherCount || store.unreadPostCount;
});

onBeforeMount(async () => {
  await store.getSquaresList();
  await setAccountList();
});

watch(
  () => store.squareList,
  (val) => {
    if (!val) return;
    setAccountList();
  },
);

const setAccountList = async () => {
  accountList.value = store.squareList;

  emit('loaded', accountList.value);
  accountNoticeCount.value = store.noticesTotalCount;

  const item = store.squareList.find(
    (v) => v.square.squareId === store.squareId,
  );
  item && store.setSquareSelected(item);
};

watch(
  () => store.noticesTotalCount,
  (val) => {
    accountNoticeCount.value = val;
    accountList.value = store.squareList;
  },
);

const accountClick = async (item, callback?: Function) => {
  if (item.square.squareId === store.squareId) {
    return;
  }

  accountVisible.value = false;

  const confirmDia = DialogPlugin.confirm({
    header: t('square.switchAndRefresh'),
    body: t('square.switchAndRefreshContent'),
    confirmBtn: t('square.action.confirm'),
    cancelBtn: t('square.action.cancel'),
    placement: 'center',
    onConfirm: async () => {
      confirmDia.destroy();
      isShowAccountSwitchDialog.value = false;
      const res = await toAccount(item);
      if (res) await refresh(res.isIndividual);

      stateStore.isSwitchAccount = true;
      callback?.();
    },
    onClose: () => {
      confirmDia.hide();
      isShowAccountSwitchDialog.value = false;
    },
  });
  isShowAccountSwitchDialog.value = true;
};

const switchTab = (item, index: number) => {
  tabStore.switchTab(index);
  // 广场左侧菜单定位
  if (props.from === 'square-index' && index === 0) {
    const menu = store.menuList[store.activeMenuIdx];
    if (menu) {
      router.replace(menu.fullPath);
      emit('change', menu);
      return;
    }
  }

  let query = '';
  if (item.query) {
    query = `?${new URLSearchParams(item.query).toString()}`;
  }

  router.replace(`${item.path}${query}`);
  emit('change', item);
};

const removeTab = (item) => {
  item && tabStore.removeTab(item);

  const currentRoute = tabStore.getActiveTab();
  if (!currentRoute) return;
  // 广场左侧菜单定位
  if (currentRoute.fullPath === '/square/friend-circle') {
    // 拾光相册是新开的tab页签，当关闭时，需要跳转到第一个页签
    if (store.activeMenuIdx === 5) {
      store.activeMenuIdx = 0;
    }
    const menu = store.menuList[store.activeMenuIdx];
    if (menu) {
      router.replace(menu.fullPath);
      emit('change', menu);
      return;
    }
  }
  router.replace(currentRoute.fullPath);
};

const refresh = async (isIndividual: boolean) => {
  emit('refresh', isIndividual);
  await store.getSquaresList();
};

const route = useRoute();
const openStandAloneWin = () => {
  newWinFlag.value = false;
  console.log(route.fullPath.split('/')[1]);
  ipcRenderer.invoke('click-standalone-window', {
    url: 'projectLayout',
    flag: 'square',
  });
};

defineExpose({
  switchAccount: (id: string, callback?: Function) => {
    const item = store.squareList.find((v) => v.square.squareId === id);
    if (!item) return;
    accountClick(item, callback);
  },
});
</script>

<style lang="less" scoped>
.icon-specials {
  width: 8px;
  height: 8px;
  position: absolute;
  top: 0;
  left: 0;
  color: var(--bg-kyy-color-bg-software-foucs, #272b4f);
}

:deep(.t-badge--circle) {
  width: max-content;
}

.btn-op {
  font-size: 20px;
  margin-right: 16px;
  cursor: pointer;
}

.tabsIcon {
  font-size: 20px;
  // width: 20px;
  // height: 20px;
  color: #fff;
  border-radius: 4px !important;
  // margin-right: 8px;
}
.account {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 50px;
  .arrow {
    color: var(--icon-kyy-color-icon-deep, "#516082");
  }
  .text {
    max-width: 94px;
    font-size: 12px;
    font-weight: 400;
    margin: 0 4px;
    color: var(--text-kyy-color-text-2, #516082);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  :deep(.t-badge--dot) {
    border: 1px solid #fff;
    margin-right: 2px;
    margin-top: 3px;
    z-index: 1;
  }
}

.tabs-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 35px;
  border-radius: 5px 0 0 0;
  background: var(--kyy_color_tabbar_bg, #ebf1fc);
  &:hover {
    cursor: pointer;
    background: var(
      --kyy_color_tabbar_item_bg_hover,
      rgba(255, 255, 255, 0.5)
    );
  }
}
.head-box {
  height: 40px;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: var(--kyy_color_tabbar_bg, #ebf1fc);

  .tabs-list {
    display: flex;
    flex: 1;
    width: 0;
    overflow-x: hidden;
    background: var(--kyy_color_tabbar_bg, #ebf1fc);
    // &::-webkit-scrollbar {
    //   width: 100%;
    //   height: 8px;
    // }
    &--box {
      display: flex;
      transition: transform 0.5s;
    }
    .tabs-item {
      position: relative;
      cursor: pointer;
      min-width: 140px;
      justify-content: space-between;
      height: 32px;
      margin: 4px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      padding: 0 12px;
      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
      .title {
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: var(--kyy_color_tabbar_item_text, #1a2139);
        line-height: 22px;
        max-width: 80px;
      }
      &:hover {
        background: var(
          --kyy_color_tabbar_item_bg_hover,
          rgba(255, 255, 255, 0.5)
        );
      }
      &.selected {
        background: var(--kyy_color_tabbar_item_bg_active, #fff);
      }

      .tab-divider {
        display: flex;
        height: 20px;
        min-width: 1px;
        max-width: 1px;
        justify-content: flex-end;
        align-items: center;
        position: absolute;
        right: -1px;
        top: 6px;
        background-color: #d5dbe4;
      }
    }
  }
}
</style>

<style lang="less">
.square-account-list-popup {
  //width: 311px;
  .account-wrap {
    max-height: 276px;
    overflow-y: auto;
  }
  .account-item {
    display: flex;
    align-items: center;
    height: 36px;
    border-radius: 4px;
    margin-bottom: 4px;
    padding: 0 8px 0 12px;
    cursor: pointer;
    &.active,
    &:hover {
      background: var(--lingke-select, #e1eaff);
    }
    > .icon {
      margin-right: 8px;
      font-size: 20px;
    }
    .t-badge--circle {
      height: 20px;
      line-height: 20px;
      padding: 0 8px;
      color: var(--kyy_color_tag_text_magenta, #ff4aa1);
      background: var(--kyy_color_tag_bg_magenta, #ffe3f1);
    }
  }
  .avatar {
    width: 24px;
    height: 24px;
    border-radius: 5px;
    margin-right: 8px;
  }
  .name {
    max-width: 220px;
    margin-right: 20px;
  }
}
</style>
