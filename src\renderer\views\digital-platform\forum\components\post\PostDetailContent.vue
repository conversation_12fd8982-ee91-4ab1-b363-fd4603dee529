<template>
  <div v-scroll="[onScroll, { throttle: 20 }]" class="detail-wrap">
    <div class="post-header">
      <Avatar
        avatar-size="44px"
        :image-url="owner.avatar ?? ''"
        :user-name="owner?.remark || owner.name"
        round-radius
        :class="['avatar', { cursor: canHeadClick }]"
        @click.stop="clickHead"
      />

      <div class="flex-1 flex">
        <div class="flex-col">
          <div class="flex align-items-center">
            <div :class="['name', { cursor: canHeadClick }]" @click.stop="clickHead">
              {{ owner?.remark || owner.name }}
            </div>
            <!-- <span class="tag">{{ flagText }}</span> -->
          </div>
          <div class="time">
            {{ postedAtFormat }}
            <span v-if="post.ipRegion">{{ $t('square.post.publishRegion') }} {{ post.ipRegion }}</span>
          </div>
        </div>
        <t-button
          v-if="BtnStatusMap[post.friendStatus]?.show"
          :disabled="BtnStatusMap[post.friendStatus]?.disabled"
          class="add-friend-btn ml-auto px-8! h-24!"
          variant="outline"
          theme="primary"
          @click="applyVisible = true"
          >{{ BtnStatusMap[post.friendStatus]?.text }}</t-button
        >
      </div>
    </div>

    <div v-if="post.id" class="mx-24">
      <PostItemContent
        :data="postItem"
        :toolbar-active="toolbarActive"
        :scroll-to-input="scrollToInput"
        :owner="owner"
        @click-content="(e) => emit('click-content', e)"
        @toolbar-change="toolbarChange"
        @count-change="countChange($event)"
        @removed="emit('removed')"
      />

      <t-divider class="mx-0! mb-0! mt-12!" />
    </div>

    <!-- 点赞列表 -->
    <template v-if="isSelfPost && toolbarActive === 'like'">
      <LikedList
        v-infinite-scroll="getLikes"
        :infinite-scroll-immediate-check="false"
        :infinite-scroll-distance="100"
        :infinite-scroll-disabled="likeLoaded"
        infinite-scroll-watch-disabled="likeLoaded"
        :data="likeList"
        :empty-tip="$t('square.post.likeEmpty')"
        class="person-list"
      >
        <template #empty>
          <Empty name="no-like" />
        </template>
      </LikedList>
      <div class="text-center my-16">
        <t-loading v-if="likeLoading" size="small" />
        <span v-else-if="likeLoaded && likeList.length">{{ $t('components.infiniteLoading.noMoreData') }}</span>
      </div>
    </template>

    <!-- 评论列表 -->
    <CommentList
      v-show="post.id && toolbarActive === 'comment'"
      :key="commentRefreshId"
      :post="post"
      :focus="!forceTopCommentId && toolbarActive === 'comment'"
      :resource-id="post.id"
      :resource-type="ResourceType.Post"
      :owner="owner"
      :is-scrolling="isScrolling"
      @count-change="countChange"
    />
  </div>
  <apply-dialog
    v-model:visible="applyVisible"
    showOverlay
    :cardInfo="cardInfo"
    :myId="cardInfo?.myId"
    @onApplyContact="onConfirm"
    @onconfirm="onConfirm"
  />
</template>

<script setup lang="ts">
import { computed, inject, Ref, ref, watch } from 'vue';
import Avatar from '@renderer/components/kyy-avatar/index.vue';
import { PostDetail, PostType } from '@renderer/api/forum/models/post';
import { getLikeList } from '@renderer/api/forum/comment';
import { ResourceType } from '@renderer/api/forum/models/comment';
import applyDialog from '@renderer/views/identitycard/dialog/applyContact.vue';
import { POST_DATA_INJECT_KEY } from '@renderer/views/square/constant';
import { getPostStats } from '@renderer/api/forum/post';
import to from 'await-to-js';
import type { UseScrollReturn } from '@vueuse/core';
import { vScroll } from '@vueuse/components';
import LikedList from './LikedList.vue';
import CommentList from '../comment/CommentList.vue';
import PostItemContent from './PostItemContent.vue';
import { timeAgo } from '@/views/square/utils/time';
import Empty from '@/components/common/Empty.vue';
import { useForumStore } from '../../store';
import { BtnStatusMap } from '../../constant';
import { getOpenid } from '@renderer/utils/auth';

const props = defineProps({
  data: Object,
  mode: {
    type: String,
    default: PostType.Picture,
    validator: (value: PostType) => Object.values(PostType).includes(value),
  },
  defaultToolbar: {
    type: String,
    default: '',
  },
  // 仅可切换点赞列表，不可点赞
  onlyLikeList: {
    type: Boolean,
    default: false,
  },
  // 能否点赞同
  canLike: {
    type: Boolean,
    default: true,
  },
  scrollToInput: Boolean,
  canHeadClick: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits([
  'update:modelValue',
  'click-content',
  'confirm',
  'removed',
  'toggle-top',
  'refresh-item',
  'count-change',
  'click-head',
]);
const { forceTopCommentId } = inject(POST_DATA_INJECT_KEY, {}) || {};

const isScrolling = ref(false);
const onScroll = (state: UseScrollReturn) => {
  isScrolling.value = state.isScrolling.value;
};

const forumStore = useForumStore();
const postItem: Ref<PostDetail> = ref({ owner: {}, post: {} } as PostDetail);
const post = computed(() => postItem.value.post);
const owner = computed(() => postItem.value.owner || {});
const isSelfPost = computed(() => forumStore.currCard?.openid === owner.value?.openid);
// const flagText = computed(() => flagMap[owner.value.digitalPlatformFlag]);

const postedAtFormat = computed(() => timeAgo(post.value?.postedAt));
const commentRefreshId = ref(1);

watch(
  () => props.data,
  async (val) => {
    if (!val) return;
    postItem.value = val;
  },
  { immediate: true, deep: true },
);

// 点赞列表
const likeList = ref([]);
const likeLoading = ref(false);
const likeLoaded = ref(false);
const nextPageToken = ref('');
const likeParams = computed(() => ({
  'page.size': 20,
  resource_id: post.value.id,
  resource_type: ResourceType.Post,
  'page.next_page_token': nextPageToken.value,
}));
const getLikes = async () => {
  if (!isSelfPost.value || likeLoading.value || likeLoaded.value) return;

  likeLoading.value = true;
  const res = await getLikeList(likeParams.value);

  likeLoading.value = false;
  const { page, items } = res.data.data;
  likeLoaded.value = !page.nextPageToken;
  nextPageToken.value = page.nextPageToken;
  likeList.value = likeList.value.concat(items);
};

// 申请加好友
const applyVisible = ref(false);
const cardInfo = ref({
  myId: getOpenid() || '',
  openid: owner.value.openid,
  cardId: owner.value.openid,
  staffOpenId: owner.value.openid,
  name: owner.value.name,
  avatar: owner.value.avatar,
});
const onConfirm = () => {
  emit('refresh-item');
};

const toolbarActive = ref(props.defaultToolbar);
const toolbarChange = (type: string) => {
  toolbarActive.value = type;

  if (type === 'like') {
    likeList.value = [];
    likeLoading.value = false;
    likeLoaded.value = false;
    nextPageToken.value = '';

    setTimeout(() => {
      getLikes();
    }, 500);
  }
};
watch(
  () => props.defaultToolbar,
  (type) => {
    toolbarChange(type);
  },
  { immediate: true },
);

/*
// 发布评论后，实时刷新评论统计数据
const { refreshPost } = inject(POST_DATA_INJECT_KEY, {});
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const commentRefresh = () => {
  commentRefreshId.value++;
  refreshPost?.();
};
*/

const countChange = async (payload?) => {
  const [err, res] = await to(getPostStats(post.value.id));
  if (err) return;

  const data = res.data.data;
  postItem.value.post.comments = data.comments;
  emit('count-change', data, payload);
};

const clickHead = () => {
  if (!props.canHeadClick) return;
  emit('click-head', postItem.value);
};
</script>

<style lang="less" scoped>
.detail-wrap {
  height: 100%;
  overflow-y: auto;
  .scrollbar2();
}

.post-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
  font-weight: 400;
  margin: 0 24px 12px;
  background: #fff;
  z-index: 9;
  .avatar {
    width: 44px;
    height: 44px;
    cursor: pointer;
    align-self: center;
  }
  .name {
    color: var(--text-kyy_color_text_1, #1a2139);
    font-size: 14px;
    font-weight: 600;
    line-height: 24px; /* 150% */
    margin-bottom: 2px;
    margin-right: 4px;
    max-width: 380px;
    .ellipsis();
  }
  .tag {
    display: flex;
    height: 20px;
    min-height: 20px;
    max-height: 20px;
    padding: var(--checkbox-kyy_radius_checkbox, 2px) 4px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_brand, #eaecff);
    color: var(--kyy_color_tag_text_brand, #4d5eff);
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
  .time {
    display: flex;
    gap: 12px;
    color: var(--text-kyy_color_text_3, #828da5);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .add-friend-btn {
    :deep(.t-button__text) {
      font-size: 12px !important;
    }
  }
}

:deep(.person-list) {
  .person-item {
    border-radius: initial;
    border-top: none;
    border-right: none;
    border-left: none;
    padding: 8px !important;
  }
  .name {
    font-size: 14px !important;
    font-weight: initial !important;
    line-height: 22px !important; /* 150% */
  }
}
</style>
