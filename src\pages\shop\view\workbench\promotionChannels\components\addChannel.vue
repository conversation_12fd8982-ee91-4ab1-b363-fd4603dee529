<template>
  <t-drawer v-model:visible="visible" class="addChannel" size="472px">
    <template #header>
      <div class="header-box">
        <div class="tab-item">可推广平台</div>
        <div class="rbox">
          <div class="rbox-a">
            推广至其它数字平台
            <t-tooltip
              :show-arrow="true"
              theme="light"
              placement="bottom-right"
              overlay-class-name="custom-tooltip-class"
            >
              <template #content>
                <!-- v-if="auth" -->
                <div class="tipbox">
                  商品如需推广到其它数字平台，请点击
                  <a @click="toman">“组织入网关联“</a>
                  操作
                </div>
              </template>
              <iconpark-icon name="iconhelp" style="font-size: 20px; color: #516082; margin-left: 4px" />
            </t-tooltip>
          </div>
          <iconpark-icon name="iconerror" class="icon24" @click="close" />
        </div>
      </div>
    </template>
    <div class="sr-box">
      <t-input v-model="grno" style="width: 100%" placeholder="请输入组织名称、ID搜索" @change="search">
        <template #prefix-icon>
          <iconpark-icon name="iconsearch" class="icon20" />
        </template>
      </t-input>

      <div v-if="tabKey === 2" class="tips">
        商品如需推广到其它数字平台，请点击
        <t-tooltip
          v-if="!auth"
          :show-arrow="true"
          placement="bottom-right"
          content="当前用户暂无权限，请联系管理员开通"
        >
          <a @click="tomanIndex">"前往组织入网关联"</a>
        </t-tooltip>
        <a v-else @click="toman">"前往组织入网关联"</a>
        操作
      </div>

      <t-table
        :data="tableData"
        :columns="columns"
        :selected-row-keys="selectedRowKeys"
        :select-on-row-click="selectOnRowClick"
        row-key="relation_team_id"
        @select-change="rehandleSelectChange"
      >
        <template #name="{ row }">
          <div class="goods-box">
            <img class="goods-img" :src="row.team_logo || teamLogo" alt="商品图片" />
            <div class="goods-name">
              <div class="name">
                <div v-if="row.relation_team_id === teamid" class="name-tag">自</div>
                {{ row.team_name }}
              </div>
              <div class="id">{{ row.relation_team_id }}</div>
            </div>
          </div>
        </template>
        <template #channel="{ row }">
          <div class="chbox">
            <img :src="channelIcon[row.application_uuid]" alt="渠道图片" class="channel-image" />
            <div :class="`text${row.application_uuid}`">{{ channelTypeText[row.application_uuid] }}</div>
          </div>
        </template>

        <template #empty>
          <div class="empty-box">
            <REmpty :name="grno ? 'no-result' : 'no-data'" :tip="grno ? '搜索无结果' : '暂无推广数字平台'" />
            <t-button v-if="!grno" @click="openJoinDrwer">加入数字平台</t-button>
          </div>
        </template>
      </t-table>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <div class="yx" :style="{ opacity: selectNum ? 1 : 0 }">
          已勾选：
          <span class="num">{{ selectNum }}</span>
        </div>
        <div class="btns">
          <t-button
            theme="defauel"
            class="weight600"
            variant="outline"
            style="width: 80px; color: #516082"
            @click="close"
          >
            取消
          </t-button>
          <t-button theme="primary" class="weight600" style="width: 80px" :disabled="selectNum === 0" @click="addRun">
            确定
          </t-button>
        </div>
      </div>
    </template>
  </t-drawer>
</template>

<script setup lang="ts">
import _ from 'lodash';
import { ref, computed } from 'vue';
import { REmpty } from '@rk/unitPark';
import sdk from '@lynker-desktop/web';
import {
  batchAddPromotionChannels,
  checkIsAdmin,
  getShopCommissionBindList,
  listAddedPromotionChannelTeamIds,
} from '../apis';
import { MessagePlugin } from 'tdesign-vue-next';

const grno = ref('');
const teamLogo = ref('https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/172238828994927074432.png');

const visible = ref(false);
const batch = ref(false);
const spuId = ref('');
const tableData: any = ref([]);
const selectedRowKeys = ref([]);
const tabKey = ref(1);
const auth = ref(true);
const teamid = ref(localStorage.getItem('shopTeamId'));
const openDrwer = (sid, batchVal) => {
  spuId.value = sid;
  batch.value = batchVal;
  getData(batchVal ? null : sid);
  visible.value = true;
  selectedRowKeys.value = [];
  checkIsAdminRun();
};
const selectNum = computed(() => {
  return selectedRowKeys.value.length - disRowKeys.value.length;
});

const search = async () => {
  getData(batch.value ? null : spuId.value);
};

const disRowKeys = ref([]);
// const listAddedPromotionChannelTeamIdsRun = async () => {
//   const res = await listAddedPromotionChannelTeamIds(spuId.value);
//   console.log(res, 'resresres');
//   // selectedRowKeys.value = res.data?.teamIds || [];
//   disRowKeys.value = res.data?.teamIds || [];
// };

const checkIsAdminRun = async () => {
  const res = await checkIsAdmin({});
  console.log(res, 'resresres');
  auth.value = res.data;
};
const emit = defineEmits(['succ']);
const rehandleSelectChange = (keys: any) => {
  console.log(keys);
  selectedRowKeys.value = keys;
};
const selectOnRowClick = (row: any) => {
  console.log(row);
};
const channelIcon: any = {
  member: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/vip.png',
  cbd: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/cbd.png',
  association: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/community.png',
  government: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/type1.png',
  uni: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/community.png',
};
const channelTypeText = {
  member: '数字商协',
  cbd: '数字CBD',
  association: '数字社群',
  government: '数字政企',
  uni: '数字高校',
};
const columns = ref([
  {
    colKey: 'row-select',
    type: 'multiple',
    width: 48,
    // disabled: true,
    disabled: ({ row }) => disRowKeys.value.includes(row.relation_team_id),
  },
  { title: '渠道信息', colKey: 'name', width: 256 },
  { title: '渠道类型', colKey: 'channel', width: 120 },
]);
const getData = async (sid) => {
  // tableData.value = []
  try {
    const chres = await getShopCommissionBindList({ keyword: grno.value });
    if (chres.code === 0) {
      tableData.value = chres.data.list;
    }
    if (sid) {
      const se = await listAddedPromotionChannelTeamIds(sid);
      if (se.code === 0) {
        selectedRowKeys.value = se.data?.teamIds || [];
        disRowKeys.value = se.data?.teamIds || [];
      }
    }
  } catch (error) {
    console.log(error);
  }
};

const close = () => {
  visible.value = false;
  selectedRowKeys.value = [];
};

const batchAddPromotionChannelsRequest = async () => {
  console.log(selectedRowKeys.value);
  console.log(tableData.value);
  const spuids = batch.value ? spuId.value : [spuId.value];
  const items = tableData.value
    .filter((item) => selectedRowKeys.value.includes(item.relation_team_id))
    .map((item) => {
      const temp = {
        teamId: item.relation_team_id,
        teamName: item.team_name,
        channelType: item.application_uuid,
      };
      return temp;
    });
  console.log({ spuId: spuids, channels: items });
  try {
    const res = await batchAddPromotionChannels({ spuIds: spuids, channels: items });
    if (res.code === 0) {
      MessagePlugin.success('添加成功');
      emit('succ');
      close();
    }
  } catch (error) {
    console.log(error);
  }
};

const addRun = () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请勾选要添加的渠道');
    return;
  }
  batchAddPromotionChannelsRequest();
};
const toman = () => {
  console.log('toman');
  if (auth.value?.isAdmin || auth.value?.superAdmin || auth.value?.super) {
    sdk.workBench_openTabForEnterprise({ menuId: '62', tab: 'relation' });
  } else {
    MessagePlugin.error('当前用户暂无权限，请联系管理员开通');
    // sdk.workBench_openTabForEnterprise({ menuId: '62' });
  }
};

const openJoinDrwer = () => {
  console.log('openJoinDrwer');
  sdk.openJoinDigitalPlatformDrawer();
};

defineExpose({
  openDrwer,
});
</script>

<style scoped lang="less">
:global(.addChannel .t-drawer__content-wrapper) {
  background-image: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bg_img_code.png') !important;
  background-repeat: no-repeat !important;
  background-size: 100% 100% !important;
}
:global(.addChannel .t-drawer__body) {
  padding: 0 12px 12px 12px !important;
}
.sr-box {
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #fff);
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  min-height: 494px;
}
.chbox {
  display: flex;
  height: 24px;
  padding: 0px 4px;
  align-items: center;
  gap: 2px;
  border-radius: 4px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  width: fit-content;
  img {
    display: flex;
    width: 20px;
    height: 20px;
    padding: 2px;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
  }
  .textmember {
    color: var(--warning-kyy_color_warning_default, #fc7c14);

    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .textcbd {
    color: var(--brand-kyy_color_brand_default, #4d5eff);

    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .textassociation {
    color: var(--error-kyy_color_error_default, #d54941);

    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .textgovernment {
    color: var(--error-kyy_color_error_default, #d54941);

    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.name-tag {
  display: flex;
  height: 20px;
  width: 20px;
  padding: 0px 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_blue, #e8f0fb);
  color: var(--kyy_color_tag_text_blue, #4093e0);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  float: left;
  margin-right: 4px;
}

.goods-box {
  display: flex;
  gap: 12px;
  .goods-img {
    width: 64px;
    height: 64px;
    border-radius: 4px;
  }
  .name {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    flex: 1 0 0;
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    text-overflow: ellipsis;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .id {
    overflow: hidden;
    color: var(--text-kyy_color_text_3, #828da5);
    text-overflow: ellipsis;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}

.header-box {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  .tab {
    display: flex;
    align-items: center;
    gap: 32px;
    position: relative;
    .tab-item {
      color: var(--kyy_color_modal_title, #1a2139);
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
    }
    .tabact {
      color: var(--brand-kyy_color_brand_default, #4d5eff);
      font-size: 16px;
      font-weight: 600;
    }
    .tag {
      width: 16px;
      height: 3px;
      flex-shrink: 0;
      border-radius: 1.5px;
      background: var(--brand-kyy_color_brand_default, #4d5eff);
    }
    .tagp1 {
      position: absolute;
      left: 30px;
      bottom: -10px;
    }
    .tagp2 {
      position: absolute;
      left: 140px;
      bottom: -10px;
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // gap: 8px;
  .yx {
    color: var(--text-kyy_color_text_1, #1a2139);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    .num {
      color: var(--brand-kyy_color_brand_default, #4d5eff);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
  .btns {
    display: flex;
    align-items: center;
  }
}
.icon20 {
  font-size: 20px;
  color: #828da5;
}
.icon24 {
  font-size: 24px;
  color: #1a2139;
}
.tips {
  display: flex;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  align-self: stretch;
  border-radius: 8px;
  background: var(--kyy_color_alert_bg_bule, #eaecff);
  color: var(--kyy_color_alert_text, #1a2139);

  /* kyy_fontSize_2/regular */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  a {
    color: var(--brand-kyy_color_brand_default, #4d5eff);

    /* kyy_fontSize_2/regular */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
}
.empty-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
}
.rbox {
  color: var(--text-kyy_color_text_1, #1a2139);

  /* kyy_fontSize_2/regular */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  display: flex;
  align-items: center;
  gap: 24px;
  .rbox-a {
    display: flex;
  }
}
.tipbox {
  width: 276px;
  padding: 8px;
  color: #516082;
}
:deep(.t-table__content) {
  overflow-x: hidden;
}
// :global(.custom-tooltip-class .t-popup__content) {
//   margin-top: 8px !important;
// }
:global(.weight600) {
  font-weight: 600 !important;
}
</style>
