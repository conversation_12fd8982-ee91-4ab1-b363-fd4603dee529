// import memberRoute from './member';
// import politicsRoute from './politics';
import { i18n } from '@/i18n';
import { useForumStore } from '@/views/digital-platform/forum/store';

// @ts-ignore
const t = i18n.global.t;
export default [
  {
    path: '/digitalPlatformIndex',
    name: 'digitalPlatformIndex',
    component: () => import('@renderer/views/digital-platform/index.vue'),
    children: [
      {
        path: 'digital_platform_policy_express',
        name: 'digital_platform_policy_express', // 政策列车
        component: () => import('@renderer/views/policy-express/index.vue'),
        meta: {
          title: t('policy.policy_express'),
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_policy_express',
          icon: 'policy',
          affix: false,
          isAdd: true,
          role: 'personal',
        },
      },
      {
        path: 'digitalPlatformPolicyExpressInfo',
        name: 'DigitalPlatformPolicyExpressInfo', // 政策速递详情
        component: () => import('@renderer/views/policy-express/viewer/PolicyExpressInfo.vue'),
        meta: {
          title: '政策速递详情',
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_policy_express',
          icon: 'policy',
          affix: false,
          isAdd: true,
          role: 'personal',
        },
      },
      {
        path: 'digitalPlatformPolicyAnalysisInfo',
        name: 'DigitalPlatformPolicyAnalysisInfo', // 政策分析详情
        component: () => import('@renderer/views/policy-express/viewer/PolicyAnalysisInfo.vue'),
        meta: {
          title: '政策分析详情',
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_policy_express',
          icon: 'policy',
          affix: false,
          isAdd: true,
          role: 'personal',
        },
      },
      {
        path: 'digitalPlatformPolicyExpressList',
        name: 'DigitalPlatformPolicyExpressList', // 政策速递列表
        component: () => import('@renderer/views/policy-express/viewer/PolicyExpressList.vue'),
        meta: {
          title: '政策速递列表',
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_policy_express',
          icon: 'policy',
          affix: false,
          isAdd: true,
          role: 'personal',
        },
      },
      {
        path: 'digitalPlatformPolicyAnalysisList',
        name: 'DigitalPlatformPolicyAnalysisList', // 政策分析列表
        component: () => import('@renderer/views/policy-express/viewer/PolicyAnalysisList.vue'),
        meta: {
          title: '政策分析列表',
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_policy_express',
          icon: 'policy',
          affix: false,
          isAdd: true,
          role: 'personal',
        },
      },
      {
        path: 'uni_addAd',
        name: 'uni_addAd',
        component: () => import('@renderer/views/uni/member_home/panel/add-ad/addAdvertSing.vue'),
        meta: {
          title: '新建广告',
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_number',
          icon: 'uni',
          affix: false,
          isAdd: true,
          role: 'personal',
        },
      },
      {
        path: 'cbd_addAd',
        name: 'cbd_addAd',
        component: () => import('@renderer/views/cbd/member_home/panel/add-ad/addAdvertSing.vue'),
        meta: {
          title: '新建广告',
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_number',
          icon: 'cbd',
          affix: false,
          isAdd: true,
          role: 'personal',
        },
      },
      {
        path: 'fengcai_adDetails',
        name: 'fengcai_adDetails',
        component: () => import('@renderer/views/workBench/fengcai/detail/index.vue'),
        meta: {
          hidden: true,
          title: '风采详情',
          parentPath: '/digitalPlatformIndex/member_number',
          icon: '',
          isAdd: true,
          affix: false,
          role: 'personal',
        },
      },
      {
        path: 'cbd_adDetails',
        name: 'cbd_adDetails',
        component: () => import('@renderer/views/cbd/member_home/panel/add-ad/AdDetails.vue'),
        meta: {
          hidden: true,
          title: '广告详情',
          parentPath: '/digitalPlatformIndex/member_number',
          icon: 'cbd',
          affix: false,
          role: 'personal',
        },
      },
      {
        path: 'uni_adDetails',
        name: 'uni_adDetails',
        component: () => import('@renderer/views/uni/member_home/panel/add-ad/AdDetails.vue'),
        meta: {
          hidden: true,
          title: '广告详情',
          parentPath: '/digitalPlatformIndex/member_number',
          icon: 'uni',
          affix: false,
          role: 'personal',
        },
      },
      // {
      //   path: "cbd_addAdvertSing",
      //   name: "cbd_addAdvertSing", // 公告
      //   component: () => import("@renderer/views/cbd/member_home/panel/mark-advertising/addAdvertSing.vue"),
      //   meta: {
      //     title: "新建广告",
      //     icon: "addAdvertSing",
      //   },
      // },
      {
        path: 'politics_addAd',
        name: 'politics_addAd',
        component: () => import('@renderer/views/politics/member_home/panel/add-ad/addAdvertSing.vue'),
        meta: {
          title: '新建广告',
          hidden: true,
          isAdd: true,
          parentPath: '/digitalPlatformIndex/member_number',
          icon: 'government',
          affix: false,
          role: 'personal',
        },
      },
      {
        path: 'politics_adDetails',
        name: 'politics_adDetails',
        component: () => import('@renderer/views/politics/member_home/panel/add-ad/AdDetails.vue'),
        meta: {
          hidden: true,
          title: '广告详情',
          parentPath: '/digitalPlatformIndex/member_number',
          icon: 'government',
          affix: false,
          role: 'personal',
        },
      },
      // {
      //   path: "politics_addAdvertSing",
      //   name: "politics_addAdvertSing", // 公告
      //   component: () => import("@renderer/views/politics/member_home/panel/mark-advertising/addAdvertSing.vue"),
      //   meta: {
      //     title: "新建广告",
      //     icon: "addAdvertSing",
      //   },
      // },
      {
        path: 'member_addAd',
        name: 'member_addAd',
        component: () => import('@renderer/views/member/member_home/panel/add-ad/addAdvertSing.vue'),
        meta: {
          title: '新建广告',
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_number',
          icon: '',
          isAdd: true,
          affix: false,
          role: 'personal',
        },
      },

      {
        path: 'member_adDetails',
        name: 'member_adDetails',
        component: () => import('@renderer/views/member/member_home/panel/add-ad/AdDetails.vue'),
        meta: {
          hidden: true,
          title: '广告详情',
          parentPath: '/digitalPlatformIndex/member_number',
          icon: '',
          affix: false,
          role: 'personal',
        },
      },
      // {
      //   path: "addAdvertSing",
      //   name: "member_addAdvertSing",
      //   component: () => import("@renderer/views/member/member_home/panel/mark-advertising/addAdvertSing.vue"),
      //   meta: {
      //     title: "新建广告",
      //     icon: "addAdvertSing",
      //   },
      // },
      {
        path: 'digital_platform_notice',
        name: 'digital_platform_notice', // 公告
        component: () => import('@renderer/views/member/notice/index.vue'),
        meta: {
          title: '公告',
          icon: 'notice',
        },
      },
      {
        path: 'digital_platform_notice_item',
        name: 'digital_platform_notice_item', // 公告
        component: () => import('@renderer/views/notice/noticeSetting/noticeDetailRead.vue'),
        meta: {
          title: '公告详情',
          icon: 'notice',
        },
      },
      {
        path: 'digital_platform_forum',
        name: 'digital_platform_forum', // 论坛
        component: () => import('@renderer/views/digital-platform/forum/index.vue'),
        redirect: '/digitalPlatformIndex/digital_platform_forum/home',
        beforeEnter: async (to, from, next) => {
          // localStorage.removeItem(HEADER_FORUM_KEY);
          const forumStore = useForumStore();
          await forumStore.preload(to.query.team_id);
          next();
        },
        meta: {
          title: '论坛',
          icon: 'forum',
        },
        children: [
          {
            path: 'home',
            name: 'forumHome', // 论坛首页
            component: () => import('@renderer/views/digital-platform/forum/home/<USER>'),
            meta: {
              title: '论坛',
              icon: 'forum',
            },
          },
          {
            path: 'post',
            name: 'forumPost', // 我的帖子
            component: () => import('@renderer/views/digital-platform/forum/post/index.vue'),
            meta: {
              title: '我的帖子',
              icon: 'forum',
            },
          },
          {
            path: 'sub_post',
            name: 'forumPostSub', // 二级帖子列表
            component: () => import('@renderer/views/digital-platform/forum/post/SubList.vue'),
            meta: {
              title: '我的帖子',
              icon: 'forum',
            },
          },
          {
            path: 'forumNotice',
            name: 'forumNotice', // 论坛
            component: () => import('@renderer/views/digital-platform/forum/notice/index.vue'),
            meta: {
              title: '消息',
              icon: 'forum',
            },
          },
          {
            path: 'draft',
            name: 'forumDraft', // 论坛草稿
            component: () => import('@renderer/views/digital-platform/forum/draft/index.vue'),
            meta: {
              title: '草稿',
              icon: 'forum',
            },
          },
          {
            path: 'setting',
            name: 'forumSetting', // 论坛草稿
            component: () => import('@renderer/views/digital-platform/forum/setting/index.vue'),
            meta: {
              title: '编辑个人资料',
              icon: 'forum',
            },
          },
        ],
      },
      {
        path: 'digital_platform_forum_topic',
        name: 'digital_platform_forum_topic', // 论坛
        component: () => import('@renderer/views/digital-platform/forum/topic/index.vue'),
        meta: {
          title: '话题详情',
          icon: 'forum',
        },
      },
      {
        path: 'digital_platform_fengcai',
        name: 'digital_platform_fengcai', // 平台风采
        component: () => import('@renderer/views/member/fengcai/list/index.vue'),
        meta: {
          title: t('banch.ptfc'),
          icon: 'fengcai',
        },
      },
      {
        path: 'digital_platform_home',
        name: 'digital_platform_home',
        component: () => import('@renderer/views/digital-platform/home/<USER>'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_home',
          // title: "数字平台",
          title: t('member.squarek.m'),
          icon: 'digital',
          role: 'personal',
          affix: true,
        },
      },
      {
        path: 'digital_platform_leaflets',
        name: 'digital_platform_leaflets',
        component: () => import('@renderer/views/digital-platform/home/<USER>/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_leaflets',
          title: t('member.squarek.m'),
          icon: 'digital',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_number',
        name: 'digital_platform_member_number',
        component: () => import('@renderer/views/member/member_number/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_number',
          title: t('member.regular.number_member'),
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_leaflets',
        name: 'digital_platform_member_leaflets',
        component: () => import('@renderer/views/member/member_number/panel/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_leaflets',
          // title: "用另可数字商协，带领全体会员狂奔数字时代",
          title: t('member.squarek.p'),
          icon: '',
          role: 'personal',
          affix: false,
        },
      },

      {
        path: 'digital_platform_member_home',
        name: 'digital_platform_member_home',
        component: () => import('@renderer/views/member/member_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_home',
          // title: "会员管理",
          title: t('member.menu.vip_admin'),
          icon: '',
          role: 'personal',
          affix: false,
        },
      },

      {
        path: 'digital_platform_member_my',
        name: 'digital_platform_member_my',
        component: () => import('@renderer/views/member/member_number/panel/member-panel/my-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_home',
          // title: "会员管理",
          title: '会员中心',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },

      {
        path: 'digital_platform_member_manage',
        name: 'digital_platform_member_manage',
        component: () => import('@renderer/views/member/member_manage/form-design.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_manage',
          title: t('member.menu.vip_admin'),
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      // 1.9迭代，新增商机、名录、活动、会刊
      {
        path: 'digital_platform_member_rich',
        name: 'digital_platform_member_rich',
        component: () => import('@renderer/views/digital-platform/marketplace/home.vue'),
        // component: () => import("@renderer/views/member/member_number/components/rich-comp.vue"),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_rich',
          // title: "会员商机",
          title: t('member.squarek.b'),
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_shop',
        name: 'digital_platform_member_shop',
        component: () => import('@renderer/views/digital-platform/marketplace/shophome.vue'),
        // component: () => import("@renderer/views/member/member_number/components/rich-comp.vue"),
        meta: {
          hidden: true,
          title: '店铺',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_supply',
        name: 'digital_platform_member_supply',
        component: () => import('@renderer/views/digital-platform/marketplace/supply.vue'),
        meta: {
          hidden: true,
          title: '供应',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_desired',
        name: 'digital_platform_member_desired',
        component: () => import('@renderer/views/digital-platform/marketplace/desired.vue'),
        meta: {
          hidden: true,
          title: '需求',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_rich_detail',
        name: 'digital_platform_member_rich_detail',
        component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_member_rich_detail',
          // title: "会员商机详情",
          title: t('member.squarek.q'),
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_square',
        name: 'digital_platform_member_square',
        component: () => import('@renderer/views/member/member_number/components/square-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_square',
          // title: "会员广场号",
          title: t('member.eb.p'),
          icon: 'square',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_name',
        name: 'digital_platform_member_name',
        component: () => import('@renderer/views/member/member_number/components/name-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_name',
          // title: "会员名录",
          title: t('member.sv17.s_3'),
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_circle',
        name: 'digital_platform_circle',
        component: () => import('@renderer/views/member/member_number/components/ringkol-circle-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/circle',
          // title: "另可圈",
          title: '另可圈',
          icon: 'circle',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_album',
        name: 'digital_platform_album',
        component: () => import('@renderer/views/square/friend-ablum/albumIndex.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_album',
          // title: "另可圈",
          title: '拾光相册',
          icon: 'album',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_album_detail',
        name: 'digital_platform_album_detail',
        component: () => import('@renderer/views/square/friend-ablum/ablumDetail.vue'),
        meta: {
          hidden: true,
          title: '拾光相册详情',
          icon: 'albumDetail',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_ebook',
        name: 'digital_platform_member_ebook',
        component: () => import('@renderer/views/member/member_number/components/ebook.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_ebook',
          // title: "会刊",
          title: t('member.squarek.a'),
          icon: 'document',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_active',
        name: 'digital_platform_member_active',
        component: () => import('@renderer/views/member/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_active',
          // title: "活动",
          title: t('member.squarek.d'),
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_activeDetail/:id',
        name: 'digital_platform_member_activeDetail',
        component: () => import('@renderer/views/activity/activityDetail.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_active',
          // title: "活动",
          title: t('member.squarek.d'),
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_activityParticipantDetail/:id',
        name: 'digital_platform_member_activityParticipantDetail',
        component: () => import('@renderer/views/activity/activityParticipantDetail.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_active',
          // title: "活动",
          title: t('member.squarek.d'),
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_about',
        name: 'digital_platform_member_about',
        component: () => import('@renderer/views/workBench/aboutour/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_about',
          // title: "关于我们",
          title: t('member.squarek.l'),
          icon: 'about',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_member_about_honorDetail',
        name: 'digital_platform_member_about_honorDetail',
        component: () => import('@renderer/views/workBench/teamSting/honorDetail.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_about_honorDetail',
          // title: "关于我们",
          title: t('member.squarek.l'),
          icon: 'about_honorDetail',
          role: 'personal',
          affix: false,
        },
      },
      // 数字城市
      {
        path: 'digital_platform_politics_number',
        name: 'digital_platform_politics_number',
        component: () => import('@renderer/views/politics/member_number/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/politics_number',
          // title: "数字城市",
          title: t('member.squarek.r'),

          icon: 'government',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_politics_leaflets',
        name: 'digital_platform_politics_leaflets',
        component: () => import('@renderer/views/politics/member_number/panel/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/politics_leaflets',
          // title: "数字城市",
          title: t('member.squarek.r'),
          icon: 'government',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_politics_home',
        name: 'digital_platform_politics_home',
        component: () => import('@renderer/views/politics/member_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/politics_home',
          // title: "会员管理",
          title: t('member.menu.vip_admin'),
          icon: 'government',
          role: 'personal',
          affix: false,
        },
      },

      {
        path: 'digital_platform_politics_my',
        name: 'digital_platform_politics_my',
        component: () => import('@renderer/views/politics/member_number/panel/member-panel/my-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/politics_home',
          // title: "会员管理",
          title: '成员中心',
          icon: 'government',
          role: 'personal',
          affix: false,
        },
      },

      {
        path: 'digital_platform_politics_manage',
        name: 'digital_platform_politics_manage',
        component: () => import('@renderer/views/politics/member_manage/form-design.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/politics_manage',
          // title: "会员管理",
          title: t('member.menu.vip_admin'),
          icon: 'government',
          role: 'personal',
          affix: false,
        },
      },
      // 1.9迭代，新增商机、名录、活动、会刊
      {
        path: 'digital_platform_politics_rich',
        name: 'digital_platform_politics_rich',
        component: () => import('@renderer/views/digital-platform/marketplace/home.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/politics_rich',
          // title: "商机",
          title: t('member.squarek.b'),
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_politics_shop',
        name: 'digital_platform_politics_shop',
        component: () => import('@renderer/views/digital-platform/marketplace/shophome.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_rich',
          // title: "会员商机",
          title: '店铺',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_politics_supply',
        name: 'digital_platform_politics_supply',
        component: () => import('@renderer/views/digital-platform/marketplace/supply.vue'),
        meta: {
          hidden: true,
          title: '供应',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_politics_desired',
        name: 'digital_platform_politics_desired',
        component: () => import('@renderer/views/digital-platform/marketplace/desired.vue'),
        meta: {
          hidden: true,
          title: '需求',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_politics_rich_detail',
        name: 'digital_platform_politics_rich_detail',
        component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_politics_rich_detail',
          // title: "商机详情",
          title: t('member.squarek.s'),
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_politics_square',
        name: 'digital_platform_politics_square',
        // component: () => import("@renderer/views/politics/member_number/components/square-comp.vue"),
        component: () => import('@renderer/views/member/member_number/components/square-comp.vue'),

        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/politics_square',
          // title: "广场号",
          title: t('member.squarek.t'),
          icon: 'square',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_politics_name',
        name: 'digital_platform_politics_name',
        component: () => import('@renderer/views/politics/member_number/components/name-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/politics_name',
          // title: "名录",
          title: t('member.squarek.u'),
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },

      // {
      //   path: "digital_platform_politics_circle",
      //   name: "digital_platform_politics_circle",
      //   component: () => import("@renderer/views/member/member_number/components/ringkol-circle-comp.vue"),
      //   meta: {
      //     hidden: true,
      //     parentPath: "/digitalPlatformIndex/politics_circle",
      //     // title: "另可圈",
      //     title: '另可圈',
      //     icon: "circle",
      //     role: "personal",
      //     affix: false,
      //   },
      // },
      // {
      //   path: "digital_platform_politics_album",
      //   name: "digital_platform_politics_album",
      //   component: () => import("@renderer/views/square/friend-ablum/albumIndex.vue"),
      //   meta: {
      //     hidden: true,
      //     parentPath: "/digitalPlatformIndex/politics_album",
      //     // title: "另可圈",
      //     title: '拾光相册',
      //     icon: "album",
      //     role: "personal",
      //     affix: false,
      //   },
      // },
      {
        path: 'digital_platform_politics_name_detail',
        name: 'digital_platform_politics_name_detail',
        component: () => import('@renderer/views/politics/member_number/components/name-comp-detail.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_politics_name_detail',
          // title: "名录",
          title: t('member.squarek.u'),
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_politics_ebook',
        name: 'digital_platform_politics_ebook',
        component: () => import('@renderer/views/politics/member_number/components/ebook.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/politics_ebook',
          // title: "刊物",
          title: t('member.squarek.v'),
          icon: 'document',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_politics_active',
        name: 'digital_platform_politics_active',
        component: () => import('@renderer/views/politics/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/politics_active',
          // title: "活动",
          title: t('member.squarek.w'),
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_pb_list',
        name: 'digital_platform_pb_list', // 云盘route-view
        component: () => import('@renderer/views/digital-platform/pb/list/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_pb_list',
          title: t('square.partyBuild'),
          icon: 'pb',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_pb_detail/:id',
        name: 'digital_platform_pb_detail', // 云盘route-view
        component: () => import('@renderer/views/digital-platform/pb/detail/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_pb_detail',
          title: t('square.partyBuild'),
          icon: 'pb',
          role: 'personal',
          affix: false,
        },
      },

      {
        path: 'digital_platform_cbd_my',
        name: 'digital_platform_cbd_my',
        component: () => import('@renderer/views/cbd/member_number/panel/member-panel/my-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/cbd_home',
          // title: "会员管理",
          title: '租户中心',
          icon: 'cbd',
          role: 'personal',
          affix: false,
        },
      },
      // 数字高校
      {
        path: 'digital_platform_uni_leaflets',
        name: 'digital_platform_uni_leaflets',
        component: () => import('@renderer/views/uni/member_number/panel/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_leaflets',
          // title: "数字高校",
          title: '数字高校',
          icon: 'uni',
          role: 'personal',
          affix: false,
        },
      },
      // 数字CBD

      {
        path: 'digital_platform_cbd_leaflets',
        name: 'digital_platform_cbd_leaflets',
        component: () => import('@renderer/views/cbd/member_number/panel/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/cbd_leaflets',
          // title: "数字城市",
          title: '数字CBD',
          icon: 'cbd',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_cbd_rich',
        name: 'digital_platform_cbd_rich',
        // component: () => import("@renderer/views/cbd/member_number/components/rich-comp.vue"),
        component: () => import('@renderer/views/digital-platform/marketplace/home.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/cbd_rich',
          // title: "商机",
          title: t('member.squarek.b'),
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_cbd_shop',
        name: 'digital_platform_cbd_shop',
        component: () => import('@renderer/views/digital-platform/marketplace/shophome.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_rich',
          // title: "会员商机",
          title: '店铺',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_cbd_supply',
        name: 'digital_platform_cbd_supply',
        component: () => import('@renderer/views/digital-platform/marketplace/supply.vue'),
        meta: {
          hidden: true,
          title: '供应',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_cbd_desired',
        name: 'digital_platform_cbd_desired',
        component: () => import('@renderer/views/digital-platform/marketplace/desired.vue'),
        meta: {
          hidden: true,
          title: '需求',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_cbd_rich_detail',
        name: 'digital_platform_cbd_rich_detail',
        component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_cbd_rich_detail',
          // title: "商机详情",
          title: t('member.squarek.s'),
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_cbd_square',
        name: 'digital_platform_cbd_square',
        // component: () => import("@renderer/views/cbd/member_number/components/square-comp.vue"),
        component: () => import('@renderer/views/member/member_number/components/square-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/cbd_square',
          // title: "广场号",
          title: t('member.squarek.t'),
          icon: 'square',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_cbd_name',
        name: 'digital_platform_cbd_name',
        component: () => import('@renderer/views/cbd/member_number/components/name-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/cbd_name',
          // title: "名录",
          title: t('member.squarek.u'),
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },

      // {
      //   path: "digital_platform_cbd_circle",
      //   name: "digital_platform_cbd_circle",
      //   component: () => import("@renderer/views/member/member_number/components/ringkol-circle-comp.vue"),
      //   meta: {
      //     hidden: true,
      //     parentPath: "/digitalPlatformIndex/cbd_circle",
      //     // title: "另可圈",
      //     title: '另可圈',
      //     icon: "circle",
      //     role: "personal",
      //     affix: false,
      //   },
      // },
      // {
      //   path: "digital_platform_cbd_album",
      //   name: "digital_platform_cbd_album",
      //   component: () => import("@renderer/views/square/friend-ablum/albumIndex.vue"),
      //   meta: {
      //     hidden: true,
      //     parentPath: "/digitalPlatformIndex/cbd_album",
      //     // title: "另可圈",
      //     title: '拾光相册',
      //     icon: "album",
      //     role: "personal",
      //     affix: false,
      //   },
      // },
      {
        path: 'digital_platform_cbd_ebook',
        name: 'digital_platform_cbd_ebook',
        component: () => import('@renderer/views/cbd/member_number/components/ebook.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/cbd_ebook',
          // title: "刊物",
          title: t('member.squarek.v'),
          icon: 'document',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_cbd_active',
        name: 'digital_platform_cbd_active',
        component: () => import('@renderer/views/cbd/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/cbd_active',
          // title: "活动",
          title: t('member.squarek.w'),
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'post_list',
        name: 'post_list',
        component: () => import('@renderer/views/digital-platform/post/List.vue'),
        meta: { // 平台动态
          // hidden: true,
          // parentPath: "/digitalPlatformIndex/member_rich",
          title: t('member.eb.n'),
          icon: 'square',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'video_list',
        name: 'video_list',
        component: () => import('@renderer/views/digital-platform/video/index.vue'),
        meta: { // 视频
          title: '视频',
          icon: 'icon-video',
          role: 'personal',
          affix: false,
        },
      },

      // 数字社群
      {
        path: 'digital_platform_association_leaflets',
        name: 'digital_platform_association_leaflets',
        component: () => import('@renderer/views/association/member_number/panel/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/association_leaflets',
          // title: "数字城市",
          title: t('association.workbench.a'),
          icon: 'association',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_association_number',
        name: 'digital_platform_association_number',
        component: () => import('@renderer/views/association/member_number/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/association_number',

          title: t('member.squarek.r'),

          icon: 'association',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_association_home',
        name: 'digital_platform_association_home',
        component: () => import('@renderer/views/politics/member_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/association_home',
          // title: "会员管理",
          title: t('member.menu.vip_admin'),
          icon: 'association',
          role: 'personal',
          affix: false,
        },
      },

      {
        path: 'digital_platform_association_my',
        name: 'digital_platform_association_my',
        component: () => import('@renderer/views/association/member_number/panel/member-panel/my-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/association_home',
          // title: "会员管理",
          title: '成员中心',
          icon: 'association',
          role: 'personal',
          affix: false,
        },
      },

      {
        path: 'digital_platform_association_manage',
        name: 'digital_platform_association_manage',
        component: () => import('@renderer/views/association/member_manage/form-design.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/association_manage',
          // title: "会员管理",
          title: t('member.menu.vip_admin'),
          icon: 'government',
          role: 'personal',
          affix: false,
        },
      },
      // 1.9迭代，新增商机、名录、活动、会刊
      {
        path: 'digital_platform_association_rich',
        name: 'digital_platform_association_rich',
        component: () => import('@renderer/views/digital-platform/marketplace/home.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/association_rich',
          // title: "商机",
          title: t('member.squarek.b'),
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_association_shop',
        name: 'digital_platform_association_shop',
        component: () => import('@renderer/views/digital-platform/marketplace/shophome.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_rich',
          // title: "会员商机",
          title: '店铺',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_association_supply',
        name: 'digital_platform_association_supply',
        component: () => import('@renderer/views/digital-platform/marketplace/supply.vue'),
        meta: {
          hidden: true,
          title: '供应',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_association_desired',
        name: 'digital_platform_association_desired',
        component: () => import('@renderer/views/digital-platform/marketplace/desired.vue'),
        meta: {
          hidden: true,
          title: '需求',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_association_rich_detail',
        name: 'digital_platform_association_rich_detail',
        component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_association_rich_detail',
          // title: "商机详情",
          title: t('member.squarek.s'),
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_association_square',
        name: 'digital_platform_association_square',
        // component: () => import("@renderer/views/politics/member_number/components/square-comp.vue"),
        component: () => import('@renderer/views/member/member_number/components/square-comp.vue'),

        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/association_square',
          // title: "广场号",
          title: t('member.squarek.t'),
          icon: 'square',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_association_name',
        name: 'digital_platform_association_name',
        component: () => import('@renderer/views/association/member_number/components/name-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/association_name',
          // title: "名录",
          title: t('member.squarek.u'),
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },

      // {
      //   path: "digital_platform_association_circle",
      //   name: "digital_platform_association_circle",
      //   component: () => import("@renderer/views/member/member_number/components/ringkol-circle-comp.vue"),
      //   meta: {
      //     hidden: true,
      //     parentPath: "/digitalPlatformIndex/association_circle",
      //     // title: "另可圈",
      //     title: '另可圈',
      //     icon: "circle",
      //     role: "personal",
      //     affix: false,
      //   },
      // },
      // {
      //   path: "digital_platform_association_album",
      //   name: "digital_platform_association_album",
      //   component: () => import("@renderer/views/square/friend-ablum/albumIndex.vue"),
      //   meta: {
      //     hidden: true,
      //     parentPath: "/digitalPlatformIndex/association_album",
      //     // title: "另可圈",
      //     title: '拾光相册',
      //     icon: "album",
      //     role: "personal",
      //     affix: false,
      //   },
      // },
      {
        path: 'digital_platform_association_name_detail',
        name: 'digital_platform_association_name_detail',
        component: () => import('@renderer/views/association/member_number/components/name-comp-detail.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_association_name_detail',
          // title: "名录",
          title: t('member.squarek.u'),
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_association_ebook',
        name: 'digital_platform_association_ebook',
        component: () => import('@renderer/views/association/member_number/components/ebook.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/association_ebook',
          // title: "刊物",
          title: t('member.squarek.v'),
          icon: 'document',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_association_active',
        name: 'digital_platform_association_active',
        component: () => import('@renderer/views/association/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/association_active',
          // title: "活动",
          title: t('member.squarek.w'),
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_association_active',
        name: 'digital_platform_association_active',
        component: () => import('@renderer/views/association/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/association_active',
          // title: "活动",
          title: t('member.squarek.w'),
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },

      // 数字高校
      {
        path: 'digital_platform_uni_leaflets',
        name: 'digital_platform_uni_leaflets',
        component: () => import('@renderer/views/uni/member_number/panel/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_leaflets',
          // title: "数字城市",
          title: t('association.workbench.a1'),
          icon: 'uni',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_uni_number',
        name: 'digital_platform_uni_number',
        component: () => import('@renderer/views/uni/member_number/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_number',

          title: t('member.squarek.r'),
          icon: 'uni',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_uni_home',
        name: 'digital_platform_uni_home',
        component: () => import('@renderer/views/politics/member_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_home',
          // title: "会员管理",
          title: t('member.menu.vip_admin'),
          icon: 'uni',
          role: 'personal',
          affix: false,
        },
      },

      {
        path: 'digital_platform_uni_my',
        name: 'digital_platform_uni_my',
        component: () => import('@renderer/views/uni/member_number/panel/member-panel/my-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_home',
          // title: "会员管理",
          title: '成员中心',
          icon: 'uni',
          role: 'personal',
          affix: false,
        },
      },

      {
        path: 'digital_platform_uni_manage',
        name: 'digital_platform_uni_manage',
        component: () => import('@renderer/views/uni/member_manage/form-design.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_manage',
          // title: "会员管理",
          title: t('member.menu.vip_admin'),
          icon: 'government',
          role: 'personal',
          affix: false,
        },
      },
      // 1.9迭代，新增商机、名录、活动、会刊
      {
        path: 'digital_platform_uni_rich',
        name: 'digital_platform_uni_rich',
        component: () => import('@renderer/views/digital-platform/marketplace/home.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_rich',
          // title: "商机",
          title: t('member.squarek.b'),
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_uni_shop',
        name: 'digital_platform_uni_shop',
        component: () => import('@renderer/views/digital-platform/marketplace/shophome.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/member_rich',
          // title: "会员商机",
          title: '店铺',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_uni_supply',
        name: 'digital_platform_uni_supply',
        component: () => import('@renderer/views/digital-platform/marketplace/supply.vue'),
        meta: {
          hidden: true,
          title: '供应',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_uni_desired',
        name: 'digital_platform_uni_desired',
        component: () => import('@renderer/views/digital-platform/marketplace/desired.vue'),
        meta: {
          hidden: true,
          title: '需求',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_uni_rich_detail',
        name: 'digital_platform_uni_rich_detail',
        component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_uni_rich_detail',
          // title: "商机详情",
          title: t('member.squarek.s'),
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_uni_square',
        name: 'digital_platform_uni_square',
        // component: () => import("@renderer/views/politics/member_number/components/square-comp.vue"),
        component: () => import('@renderer/views/member/member_number/components/square-comp.vue'),

        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_square',
          // title: "广场号",
          title: t('member.squarek.t'),
          icon: 'square',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_uni_name',
        name: 'digital_platform_uni_name',
        component: () => import('@renderer/views/uni/member_number/components/name-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_name',
          // title: "名录",
          title: t('member.squarek.u'),
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },

      // {
      //   path: "digital_platform_uni_circle",
      //   name: "digital_platform_uni_circle",
      //   component: () => import("@renderer/views/member/member_number/components/ringkol-circle-comp.vue"),
      //   meta: {
      //     hidden: true,
      //     parentPath: "/digitalPlatformIndex/uni_circle",
      //     // title: "另可圈",
      //     title: '另可圈',
      //     icon: "circle",
      //     role: "personal",
      //     affix: false,
      //   },
      // },
      // {
      //   path: "digital_platform_uni_album",
      //   name: "digital_platform_uni_album",
      //   component: () => import("@renderer/views/square/friend-ablum/albumIndex.vue"),
      //   meta: {
      //     hidden: true,
      //     parentPath: "/digitalPlatformIndex/uni_album",
      //     // title: "另可圈",
      //     title: '拾光相册',
      //     icon: "album",
      //     role: "personal",
      //     affix: false,
      //   },
      // },
      {
        path: 'digital_platform_uni_name_detail',
        name: 'digital_platform_uni_name_detail',
        component: () => import('@renderer/views/uni/member_number/components/name-comp-detail.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/digital_platform_uni_name_detail',
          // title: "名录",
          title: t('member.squarek.u'),
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_uni_ebook',
        name: 'digital_platform_uni_ebook',
        component: () => import('@renderer/views/uni/member_number/components/ebook.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_ebook',
          // title: "刊物",
          title: t('member.squarek.v'),
          icon: 'document',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_uni_active',
        name: 'digital_platform_uni_active',
        component: () => import('@renderer/views/uni/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_active',
          // title: "活动",
          title: t('member.squarek.w'),
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_uni_active',
        name: 'digital_platform_uni_active',
        component: () => import('@renderer/views/uni/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/digitalPlatformIndex/uni_active',
          // title: "活动",
          title: t('member.squarek.w'),
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'digital_platform_webview/:webviewOnlyId',
        name: 'digital_platform_webview',
        component: () => import('@renderer/views/digital-platform/iframe/index.vue'),
      },
    ],
  },
];
