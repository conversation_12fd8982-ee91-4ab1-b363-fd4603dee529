<template>
  <div class="goods-page">
    <div class="top-box">
      <div v-if="goodsData && goodsData.length > 0" class="bulk-purchase" @click="purchasedListClick">批量选购</div>
      <div v-if="purchased" class="purchased-list" @click="bulkPurchaseClick">已购列表</div>
    </div>
    <GoodsList :list="goodsData" @click="goodClick" />
    <div v-if="goodsData.length === 0" class="noData">
      <REmpty name="no-data" tip="暂无产品/服务" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { jsbridge } from '@rk/unitPark';
import { REmpty } from '@rk/unitPark';
import GoodsList from '../../components/Goods/List/Index.vue';
import { getProductList, getPurchasedProducts } from '../../api/index';
const router = useRouter();
const activityId = router.currentRoute.value.query.activityId ? Number(router.currentRoute.value.query.activityId) : '';
const goodsData = ref([]);
const purchased = ref(false);

jsbridge.useJsBridge((opt: any) => {
  console.log('jsbridge', opt);
  const { action, data } = opt;
  if (action === 'goodsServe-pay') {
    if (data.payStatus === 1) {
      initData();
    }
  }
});
const getPurchased = () => {
  const params: any = {
    // 'page.size': 10,
    // 'page.number': 1,
  };
  params.activityId = activityId;
  purchased.value = false;
  getPurchasedProducts(params).then((res) => {
    if (res.code === 0) {
      const data = res.data;
      if (data?.products && data.products.length > 0) {
        purchased.value = true;
      }
    }
  });
};

const getData = () => {
  getProductList({
    activityId: activityId,
    // id: 23983,
    'page.size': 10,
    'page.number': 1,
  }).then((res) => {
    console.log('res', res);
    if (res.code === 0) {
      const data = res.data;
      if (data?.products && data.products.length > 0) {
        goodsData.value = data.products;
      }
    }
  });
};
const initData = () => {
  getData();
  getPurchased();
};

onMounted(() => {
  initData();
});

const bulkPurchaseClick = () => {
  console.log('bulkPurchaseClick');
  jsbridge.send('openIframe', {
    url: location.origin + '/goodsServe/index.html#/bought?activityId=' + activityId,
  });
};
const purchasedListClick = () => {
  console.log('purchasedListClick');
  jsbridge.send('openIframe', {
    url: location.origin + '/goodsServe/index.html#/purchase?type=2&activityId=' + activityId,
    // url: 'http://*************:8080/goodsServe/index.html#/purchase?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHAiOiJNQU5BR0VfT1AiLCJ6b25lIjoiQ04iLCJvcGVuaWQiOiIwdWFkYzAwMDE0b2RqY3Q5cmQiLCJmZWF0aGVyIjoiOTY5MjMwODE4MyIsInBsYXRmb3JtIjoiV0VCIn0.kTCpF6MAjMihuqU17nMvIXpyIIJQik0fj6Tr4lhaX2c&activityId=24085&type=2',
  });
};

const goodClick = (item) => {
  console.log('goodClick', item);
  jsbridge.send('openIframe', {
    url: location.origin + '/goodsServe/index.html#/purchase?productId=' + item.id + '&activityId=' + activityId,
  });
};
</script>

<style scoped>
.goods-page {
  height: 100%;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 0;
  }
}
.top-box {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 24px;
  .bulk-purchase,
  .purchased-list {
    flex: initial;
    width: 158px;
    height: 42px;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 17px;
    font-weight: 600;
    line-height: 26px;
    cursor: pointer;
  }
  .bulk-purchase {
    background: linear-gradient(97deg, #99e3de -4.45%, #e6f9f8 98.76%);
    color: var(--cyan-kyy_color_cyan_active, #0ea197);
    position: relative;
    &:after {
      content: '';
      position: absolute;
      top: 5px;
      right: 8px;
      width: 32px;
      height: 32px;
      background: url('./assets/icon-bulk-purchase.png') no-repeat center;
      background-size: 100%;
    }
  }

  .purchased-list {
    background: linear-gradient(103deg, #febe89 0.24%, #ffe5d1 71.5%);
    backdrop-filter: blur(10px);
    color: var(--warning-kyy_color_warning_active, #be5a00);
    position: relative;
    &:after {
      content: '';
      position: absolute;
      top: 5px;
      right: 8px;
      width: 32px;
      height: 32px;
      background: url('./assets/icon-purchased-list.png') no-repeat center;
      background-size: 100%;
    }
  }
}
</style>
