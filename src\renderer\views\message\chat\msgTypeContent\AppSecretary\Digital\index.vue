<template>
  <AppCard>
    <AppCardHeader> {{ getTitle(platform) }}</AppCardHeader>
    <AppCardBody>
      <div class="flex items-center gap-4 mb-6">
        <img class="w-24 h-24 rounded-full" :src="sceneData?.header?.team?.logo || BizUnion">
        <div class="flex-1 overflow-hidden ellipsis-1">{{ sceneData?.header?.team?.name || "" }}</div>
      </div>
      {{ sceneData?.content?.title }}
      <div class="h-1 bg-[#ECEFF5] mt-16" />
    </AppCardBody>
    <AppCardFooter>
      <Button
        v-if="statusFinal === 0"
        class="w-full"
        variant="outline"
        theme="danger"
        @click.stop.prevent="handleClick('reject')"
      >
        {{ i18nt('ebook.visrefu') }}
      </Button>
      <Button
        class="w-full btn-primary"
        variant="outline"
        :theme="getBtnTxt(statusFinal)?.theme"
        :disabled="statusFinal !== 0"
        @click.stop.prevent="handleClick('agree')"
      >
        {{ getBtnTxt(statusFinal)?.txt }}
      </Button>
    </AppCardFooter>
  </AppCard>
</template>

<script setup lang="ts">
import { PropType, computed, nextTick, onMounted, ref, watch } from 'vue';
import { Button } from 'tdesign-vue-next';
import { digitalAgree, digitalContactStatus, digitalReject } from '@renderer/api/common';
import { MessageToSave } from 'customTypes/message';
import { i18nt } from '@/i18n';
import { getTitle, getBtnTxt } from './constant';
import BizUnion from '@/assets/im/biz_union.png';
import { AppCard, AppCardHeader, AppCardBody, AppCardFooter } from '../../../MessageAppCard';
import { useMessageStore } from '@/views/message/service/store';

const msgStore = useMessageStore();

const props = defineProps({
  msg: { type: Object as PropType<MessageToSave>, default: null },
});

const sceneData = computed(() => props.msg?.contentExtra?.data);
const platform = computed(() => sceneData.value?.extend?.type || sceneData.value?.extend?.platform_uuid || '');
// 为null的话高校按钮不显示
const status = ref(sceneData.value?.extend?.status || 0);

const statusFinal = computed(() => {
  if (platform.value === 'member') {
    const map = {
      0: null, // 3 暂时无用
      1: 1, // 已同意
      2: 0, // 待处理
      3: 2, // 已拒绝
      4: 4, // 已失效
    };
    return map[status.value];
  }
  return status.value;
});

const handleClick = async (opt) => {
  const data = {
    uuid: sceneData.value?.extend?.uuid,
    type: platform.value,
  };
  const fn = opt === 'agree' ? () => digitalAgree(data) : () => digitalReject(data);

  const { data: { code } } = await fn();
  if (code === 0) {
    getStatus();
  }
};
const getStatus = async () => {
  const data = {
    uuid_ids: [sceneData.value?.extend?.uuid],
    type: platform.value,
  };
  const { data: { data: res, code } } = await digitalContactStatus(data);
  if (code === 0 && res?.status_list?.length) {
    status.value = res.status_list[0].status;
  }
};

watch(() => msgStore.needUpdateRefreshMessageUId, async (val) => {
  if (val && val === props.msg?.messageUId && sceneData.value?.extend?.uuid) {
    await getStatus();
    nextTick(() => {
      useMessageStore().refreshMessageByNeedUpdateMessageUId({ messageUId: '', scene: sceneData.value.scene });
    });
  }
});

onMounted(() => {
  getStatus();
});
</script>

<style scoped lang="less">
.btn-primary {
  // background-color: #EAECFF !important;
}
</style>
