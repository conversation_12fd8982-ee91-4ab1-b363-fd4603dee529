<template>
  <t-popup
    :header="null"
    :close-btn="false"
    :placement="placement"
    width="336"
    attach="body"
    zIndex="2500"
    :footer="null"
    overlay-inner-class-name="square-account-list-popup"
    :close-on-overlay-click="false"
    :close-on-esc-keydown="false"
  >
    <template #content>
      <div
        v-if="accountLists && accountLists.length > 0"
        class="account-wrap"
      >
        <div
          v-for="item in accountLists"
          :key="item.id"
          :class="[
            'account-item',
            { active: item.id ===  currentCard?.id }
          ]"
          @click="accountClick(item)"
        >

          <span class="left">
            <iconpark-icon
              name="iconother"
              :class="{
                icon: true,
                'op-0': item.id !== currentCard?.id
              }"
            />
            <!--
            <t-badge :count="1" dot size="small" class="mr-5">

            </t-badge> -->
            <kyy-avatar v-show="item.type === 1" class="rd-10" :avatar-size="'24px'"
              :image-url="item.logo ? getSrcThumbnail(item.logo): ORG_DEFAULT_AVATAR" :user-name="item?.name" :shape="'circle'" />
            <kyy-avatar v-show="item.type === 2" class="rd-10" :avatar-size="'24px'"
              :image-url="item.logo ? getSrcThumbnail(item.logo): ''"
              :user-name="item?.name" :shape="'circle'" />
            <div class="name line-1">{{ item?.name }}</div>
            <div v-show="item?.type === 1 && item?.is_contact" class="tag tag-contact">
              {{typeMap[props.originType]}}
            </div>
            <div v-show="item?.type === 1  && !item?.is_contact" class="tag tag-company">  {{typeMapRespector[props.originType]}}</div>
            <div v-show="item?.type === 2" class="tag tag-person">个人</div>
          </span>
          <!-- 商协会1.2暂时去掉 -->
          <!-- <span class="right">
            <t-badge
              :count="store.getCountByTeam(item.teamId)"
              size="small"
              class="mr-5"
            />
          </span> -->
        </div>
      </div>
    </template>
    <slot />
  </t-popup>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
import KyyAvatar from "@/components/kyy-avatar/index.vue";
import { getSrcThumbnail, getSrcLogo } from "@renderer/views/message/service/msgUtils";
import { originType }  from "@renderer/views/digital-platform/utils/constant"
const props = defineProps({
  accountLists: {
    type: Array,
    default: ()=> [],
  },
  currentCard: {
    type: Object,
    default: ()=> null,
  },
  placement: {
    type: String,
    default: 'bottom-right'
  },
  originType: {
    type: String,
    default: originType.Member
  }
});

const typeMap = {
  [originType.Member]: '代表人',
  [originType.CBD]: '代表人',
  [originType.Government]: '代表人',
  [originType.Association]: '代表人',
}
const typeMapRespector = {
  [originType.Member]: '负责人',
  [originType.CBD]: '负责人',
  [originType.Government]: '负责人',
  [originType.Association]: '负责人',
}
// const visible = ref(false);
// const accountLists = ref([]);
// const currentCard = ref(null);
const emits = defineEmits(['onSetCard']);
// const onClose = () => {
//   visible.value = false;
// };

// const onOpen = (ds, cur) => {
//   accountLists.value = ds;
//   currentCard.value = cur;
//   visible.value = true;
// };

const accountClick = (row)=> {
  // currentCard.value = row;
  // onClose();
  emits('onSetCard', row);
}



// defineExpose({
//   onOpen,
//   onClose
// });
</script>

<style lang="less" scoped>
/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  // height: 2px;
  // background-color: #f5f5f5;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  // background-color: #e3e6eb;
  // background-color: #fff;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 7px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  // background-color: #D5DBE4;
  background-color: var(--divider-kyy_color_divider_deep, #D5DBE4);
}
.tag {
  height: 20px;
  padding: 0 4px;
  /* kyy_fontSize_1/regular */
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  border-radius: var(--kyy_radius_tag_s, 4px);
  flex: none;
}

.tag-person {
  color: var(--kyy_color_tag_text_purple, #CA48EB);
  background: var(--kyy_color_tag_bg_purple, #FAEDFD);
}
.tag-company {
  color: var(--kyy_color_tag_text_success, #499D60);
  background: var(--kyy_color_tag_bg_success, #E0F2E5);
}
.tag-contact {
  color: var(--kyy_color_tag_text_brand, #4D5EFF);
  background: var(--kyy_color_tag_bg_brand, #EAECFF);
}


.account-wrap {
  max-height: 276px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.account-item {
  flex:none;
  display: flex;
  align-items: center;
  height: 32px;
  border-radius: 4px;
  // margin-bottom: 4px;
  padding: 0 8px 0 12px;
  justify-content: space-between;
  transition: all 0.25s linear;
  .left {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 320px;
    .icon {
      font-size: 20px;
    }
  }
  .right {
    display: flex;
    align-items: center;
    // padding-right: 8px;
  }
  // gap: 12px;
  cursor: pointer;
  &.active,
  &:hover {
    background: var(--lingke-select, #e1eaff);
    transition: all 0.25s linear;
  }
  > .icon {
    margin-right: 8px;
    font-size: 20px;
  }
}
</style>
