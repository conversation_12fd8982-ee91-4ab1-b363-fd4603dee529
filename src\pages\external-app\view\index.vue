<template>
  <div class="page-container">
    <div class="menu-box">
      <div class="py-8">外部应用</div>

      <ul class="mt-8 flex flex-col gap-8">
        <li
          v-for="menu in menus"
          :key="menu.id"
          class="menu-item"
          :class="{ active: activeMenuId === menu.id }"
          @click="handleMenuClick(menu)"
        >
          <iconpark-icon :name="menu.iconName" class="text-20" />
          <span>{{ menu.label }}</span>
        </li>
      </ul>
    </div>

    <div class="content-box">
      <div class="h-full">
        <Suspense>
          <component :is="asyncComponents[activeMenuId]" :key="activeMenuId" />
          <template #fallback>
            <div class="flex items-center justify-center h-full text-gray-500">加载中...</div>
          </template>
        </Suspense>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ComponentPublicInstance, computed, ref, watch } from 'vue';
import { menus } from '../constants/menu.ts';
import { useRoute, useRouter } from 'vue-router';
import { useAsyncComponent } from '@hooks/useAsyncComponent.ts';

const route = useRoute();
const router = useRouter();

// 从路由参数获取初始菜单状态
const getInitialMenuState = () => {
  const { menuId } = route.query;

  if (menuId) {
    return {
      activeMenuId: Number(menuId),
    };
  }

  // 返回默认状态
  return {
    activeMenuId: menus[0].id,
  };
};

const { activeMenuId: initialActiveMenuId } = getInitialMenuState();

const activeMenuId = ref(initialActiveMenuId);

// 创建带错误处理的异步组件
const { createAsyncComponent } = useAsyncComponent();

// 异步加载组件
const asyncComponents = computed(() =>
  menus.reduce<Record<number, new () => ComponentPublicInstance>>((acc, menu) => {
    acc[menu.id] = createAsyncComponent(menu.component);
    // console.log(menu.component);
    // acc[menu.id] = menu.component;
    return acc;
  }, {}),
);

const handleMenuClick = ({ id: menuId }: { id: number }) => {
  router.replace({
    path: '/',
    query: {
      ...route.query,
      menuId,
    },
  });
};

watch(
  () => route.query.menuId,
  (newMenuId) => {
    if (!newMenuId) return;

    activeMenuId.value = Number(newMenuId);
  },
);
</script>

<style lang="less" scoped>
.page-container {
  height: 100%;
  width: 100%;
  padding: 4px;
  display: flex;
  gap: 8px;
  background: #f5f8fe;

  .menu-box {
    width: 240px;
    padding: 8px 8px 0 8px;
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    background: var(--bg-kyy_color_bg_light, #fff);
    color: #1a2139;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;

    .menu-item {
      cursor: pointer;
      height: 48px;
      display: flex;
      align-items: center;
      gap: 8px;
      padding-left: 12px;
      border-radius: 4px;

      &.active {
        background: #e1eaff;
      }
    }
  }

  .content-box {
    flex: 1;
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    background: var(--bg-kyy_color_bg_light, #fff);
  }
}
</style>
