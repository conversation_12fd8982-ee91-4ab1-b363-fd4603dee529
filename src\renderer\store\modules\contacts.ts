import { defineStore } from 'pinia';
import { notifyCount, notifyRead } from '@renderer/api/contacts/api/common';
import { followList } from '@renderer/api/contacts/api/follow';
import { getOpenid } from '@renderer/utils/auth';

export const useContactsStore = defineStore('contacts', {
  state: () => {
    return {
      organizationInfo: {
        teamId: '',
        team: '',
        avatar: '',
        cardId: '',
        isAdmin: 0,
        canAdmin: 0,
        type:'',
        departmentId:''
      },
      addContactsVisible: false,
      addContactsType: 'friend', // friend/business
      addContanctsInfo: {
        remarks: '',
        source: '',
      },
      cardAgree: false,  //身份卡显示同意添加好友状态
      newContactsNum: 0,  // 新的联系人未读数量
      newContactsIds: [], // 新的联系人未读id
      followList: [], // 我的关注 cardid
      contactsOrgJumpId: '', // 跳转到通讯录某个组织下
    }
  },
  getters: {
    isFollow: (state) => {
      return (id:string) => !!~state.followList.findIndex(v => v === id);
    }
  },
  actions: {
    setOrganizationInfo (info) {
      this.organizationInfo = info;
    },
    setContanctsInfo (info) {
      this.addContanctsInfo = info;
    },
    setCardAgree(v) {
      this.cardAgree = v;
    },
    setNewContactsNum(ids?: number[] = []) {
      notifyCount().then(res => {
        console.log('===>notifyCount', res);
        this.newContactsNum = res.data.data?.count || 0;
      })
    },
    async setFollowList(list?: string[], isAdd = false) {
      if (list && list.length) {
        this.followList = isAdd ? [...new Set(this.followList.concat(list))] : list;
        return;
      }
      const { data } = await followList();
      const cards = data.data.follows.map(v => v.followCardId);
      this.followList = cards;
    },
    setOrgId(id) {
      this.contactsOrgJumpId = id;
    },
    clearNotice() {
      if (this.newContactsNum) {
        notifyRead().then(res => {
          this.setNewContactsNum();
        });
      }
    }
  }
});
