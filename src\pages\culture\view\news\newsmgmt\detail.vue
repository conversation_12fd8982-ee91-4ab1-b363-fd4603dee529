<template>
  <div class="news-detail-container">
    <div v-if="!newsData" class="wh-full flex-center">
      <REmpty v-if="!isLoading" name="no-data" tip="暂无数据" />
      <t-loading v-else size="small" text="加载中..." />
    </div>
    <template v-if="newsData">
      <!-- 第二个区域：内容区 -->
      <NewsBanner :data="newsData" />
      <!-- 第一个区域：头图区 -->
      <NewsContent :data="newsData" />
      <!-- 第三个区域：审核区 -->
      <ReviewSection :data="newsData" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { NewsContent, NewsBanner, ReviewSection } from './comp/detail';
import { REmpty } from '@rk/unitPark';
import { Information } from '@/types/api/culture/news';
import { useCultureStore } from '@pages/culture/store';
import { getInformationDetail } from '@pages/culture/api/news';
import { MessagePlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import { useRoute } from 'vue-router';
import { resDetailMap } from './constant';
import { useNavigate } from '@pages/culture/composables/useNavigate';

const { closeCurrentTab } = useNavigate();

const route = useRoute();
const type = route.query.type as 'draft' | 'newsmgmt';
const isLoading = ref(false);

const cultureStore = useCultureStore();

const me = computed(() => ({
  'me.openId': cultureStore.query?.openId,
  'me.cardId': cultureStore.query?.cardId,
  'me.teamId': cultureStore.query?.teamId,
}));

// 新闻详情数据
const newsData = ref<Information>();

const getInformationDetailInfo = async (id: number) => {
  isLoading.value = true;
  const [err, res] = await to(getInformationDetail({ id, ...me.value }, cultureStore.query?.teamId));
  if (err) {
    MessagePlugin.error(err.message || '获取资讯详情失败，请重试');
    isLoading.value = false;
    return false;
  }
  newsData.value = res.data.information as any;
  if (type === 'newsmgmt' && resDetailMap[newsData.value?.status as keyof typeof resDetailMap]) {
    isLoading.value = false;
    MessagePlugin.error(resDetailMap[newsData.value?.status as keyof typeof resDetailMap]);
    setTimeout(() => {
      closeCurrentTab();
    }, 2000);
    return false;
  }
  isLoading.value = false;
  return true;
};

onMounted(() => {
  getInformationDetailInfo(Number(route.params.id));
});
</script>

<style lang="less" scoped>
.news-detail-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100vh;
  padding: 16px 0;
  overflow-y: overlay;
  background: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/h5/culture/cullture_bg.png') no-repeat top
    center;
  background-size: cover;
}

.content-placeholder {
  font-size: 16px;
  line-height: 1.8;
  color: #1f2329;
  text-align: justify;

  p {
    margin-bottom: 16px;
    text-indent: 2em;
  }
}
</style>
