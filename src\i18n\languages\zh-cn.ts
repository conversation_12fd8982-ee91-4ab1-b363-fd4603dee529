import { testCn } from './test/zh-cn';
import { approval_CN } from './approval/approval-cn';
import { device_CN } from './device/device_cn';
import { engineer_C<PERSON> } from './engineer/engineer-cn';
import { member_CN } from './member/member-cn';
import { partner_CN } from './partner/partner_cn';
import { free_form_CN } from './free-form/free-form-cn';
import { approve_CN } from './approve/approve-cn';
import { customer_CN } from './customer/customer_cn';
import { supplier_CN } from './supplier/supplier_cn';
import square_CN from './square/zh-cn';
import { zx_CN } from './zx/zh-cn';
import components_CN from './components/zh-cn';
import { niche_CN } from './niche/cn';
import { customer_service_cn } from './customerService/customer_service_cn';
import { im_CN } from './message/message-cn';
import { activity_CN } from './activity/zh-cn';
import { album_CN } from './album/album-cn';
import { complain_CN } from './complain/cn';
import { lssCN } from './teamInfo/zh-cn';
import { service_CN } from './service/zh-cn';
import { help_CN } from './help/zh-cn';
import { ebook_CN } from './ebook/zh-cn';
import { dJManagement_CN } from './public-page/zh-cn';
import editorCN from './editor/zh-cn';
import { orderBusinessCN } from './orderBusiness/zh-cn';

export const zhCN = {
  item: {
    item: '繁体',
  },
  banch: {
    gczf: '广场转发',
    fzljcg: '复制链接成功',
    deltip22: '当前专栏已删除,请刷新',
    zwptfcnr: '暂无平台风采内容',
    zcfb: '再次发布',
    cgbj: '草稿编辑',
    sftip: '由于你是审核人，所以无需走安全验证，确定要发布当前平台风采吗',
    yrxg: '预览效果',
    zltip: '所選專欄未開啟該展示渠道',
    max10: '最多支持上传10个',
    zxfm: '资讯封面',
    gxfm: '更换封面',
    zl: '专栏',
    gliy: '管理员',
    zlan: '专栏管理',
    cjsj: '创建时间：',
    cjr: '创建人：',
    fbq: '确定要发布当前平台风采吗?',
    timek: '时间不能为空',
    zwk: '正文不能为空',
    xuanzl: '请选择栏目',
    fmk: '资讯封面不能为空',
    fbsh: '发布内容已提交审核人处理',
    fbsucc: '发布成功',
    mkpx: '模块排序',
    zltj: '添加专栏',
    zlname: '专栏名称',
    zsqd: '展示渠道',
    bianjic: '编辑成功',
    zyc: '转移超级管理员',
    cg1: '1.请选择新的超级管理员',
    cg2: '2.原超级管理员：',
    cg3: '将原超级管理员调整为管理员',
    cg4: '从管理后台中移除',
    cg5: '设置成功',
    cg6: '暂无平台风采内容',
    cg7: '搜索专栏名称',
    cg8: '添加专栏',
    cg9: '删除后，关联的平台风采资讯内容也会被删除',
    cg10: '关联资讯条数',
    cg11: '显示展示渠道',
    cg12: '单个专栏无法排序',

    cg22: '搜索标题/作者/专栏',
    cg23: '修改渠道',
    cg24: '撤回',
    cgdel: '删除',
    cg25: '删除后将不可恢复，确定要删除该条风采吗？',
    cg26: '修改发布渠道',
    cg27: '平台风采列表',
    cg28: '创建人',
    cg29: '审核时间',
    cg30: '请开通广场后，再发布动态。',
    cg31: '你没有发布该组织动态的权限，请联系组织广场管理员。',

    fj: '附件',
    ptfctz: '平台风采发布申请',
    ptfcytg: '平台风采已通过',
    ptfcyjj: '平台风采已拒绝',
    fbzx: '发布资讯',
    ptfc: '平台风采',
    dqzlysc: '当前专栏已删除,请刷新',
    ptfcxq: '平台风采详情',
    ptfcgl: '平台风采管理',
    tysy: '体验剩余',
    tyjs: '体验已结束',
    ddtzht: '组织订单跳转到组织管理后台进行查看',
    fptzht: '组织发票跳转到组织管理后台进行查看',
    qwzzht: '前往组织后台',
    qwzzdd: '前往组织订单',
    qwzzfp: '前往组织发票',
    scbtzl: '搜索标题/类型/内容/署名',
    fgg: '发公告',
    tb: '通报',
    zwsj: '暂无数据',
    zwssnr: '无搜索结果',
    shsz: '审核设置',
    fbsz: '发布设置',
    fbnrsxyrsh: '发布内容时,需要人员进行审核',
    tjshry: '添加审核人员',
    pltj: '批量添加',
    jsy: '结算页',
    xzfp: '选择发票',
    tjfpxx: '添加发票信息',
    fplx: '发票类型',
    ptfp: '普通发票',
    zzszyfp: '增值税专用发票',
    ttlx: '抬头类型',
    zcdzbt: '注册地址必填',
    khyhbt: '开户银行必填',
    yhzhbt: '银行账号必填',
    fpxx: '发票信息',
    zffs: '支付方式',
    jhdzzczlzxzf: '即时到账,支持主流在线支付',
    tgptzzzzh15ggzrndz: '通过平台转账,转账后1-5个工作日内到账',
    zbkp: '暂不开票',
    kfp: '开发票',
    zzsfp: '增值税专用发票',
    fptt: '发票抬头',
    gr: '个人',
    dw: '单位',
    ljzf: '立即支付',
    qd: '确定',
    zzwrzbnkp: '组织暂未认证,不能开票,购买套餐后再认证开票',
    ddyscqzddglzfdd: '订单已生成，请在"订单管理"支付订单。',

    mfb: '免费版',
    tcsj: '套餐升级',
    ylz: '已离职',
    tcygq: '套餐已过期',
    by1: '你购买的套餐未包含“数字平台搭建”权益，请升级套餐',
    by2: '你购买的套餐已过期，请续费套餐',
    ljsj: '立即升级',
    ljxf: '立即续费',
    gjbb: '高级版本',
    lcgx1: '数智工场简化任务流程，显著提升操作效率与准确性。',
    zyzh: '资源融合',
    zyzh1: '打破信息壁垒，整合商机资源，推动组织业务增长。',
    gxgg: '高效沟通',
    gxgg1: '促进团队与外界高效协同，快速信息流通，提升响应速度。',
    cxqd: '创新驱动',
    cxqd1: '推动企业探索新商策与服务，加速数字化转型，增强竞争力。',
    gobanch: '进入数智工场',
    ktszgcglyqx: '开通数智工场的人为超级管理员，添加的管理员具有进入管理后台的权限。',
    tjgly: '添加管理员',
    admin1: '主管理员',
    admin: '管理员',
    supadmin: '超级管理员',
    changsupadmin: '请选择新的超级管理员',
    yuansupadmin: '原超级管理员',
    jybcjgly: '将原超级管理员调整为管理员',
    admindel: '从管理后台中移除',
    opterror: '操作失败',
    delok: '你确定移除该管理员吗?',
    delokhead: '是否确定删除该管理员?',
    delokal: '移除的同时也会失去相应的功能,数据权限操作',
    addzj: '添加组件',
    allsee: '全员可见',
    noadmin: '需要有"应用管理"权限的管理员才能操作',
    bfsee: '部分可见',
    nichetip: '组织公告,快速编辑,发送,触达通知全员',
    deltip: '移除后,使用范围中的成员在数智工场将看不到该组件,请问是否确认移除该组件?',
    set: '设置',
    zjbj: '组件布局',
    ydxmbj: '移动下方组件可调整数智工场各个组件的展示顺序',
    xsfs: '显示方式',
    cysz: '常用设置',
    addapp: '添加应用',
    addappcy: '添加应用到常用',
    okdel1: '确定移除',
    okdel12: '确定要移除组件吗?',
    endaddapp: '已添加组件',
    allapp: '全部组件',
    groupapp: '组织下能使用的全部应用',
    groupgga: '组织公告,快速编辑,发送,触达通知全员',
    appsetting: '组件设置',
    appname: '组件名称',
    apptip: '组件简介',
    allyy: '全部应用',
    apply: '组件来源',
    syfw: '使用范围',
    ccappbsx: '超出应用使用范围的成员不生效',
    allkj: '全部可见',
    bfkj: '部分可见',
    addlxr: '添加联系人',
    xzry: '选择人员',
    jnxzapp: '仅能选择应用使用范围内的成员',
    glht: '管理后台',
    yqcy: '邀请成员',
    zzsz: '组织设置',
    banch: '数智工场',
    zjstting: '组件设置',
    adminstting: '管理员设置',
    honor: '荣誉榜',
    published: '已发布',
    auditing: '审核中',
    rejected: '已拒绝',
    draft: '草稿箱',
    publishhonor: '发布荣誉',
    honorSearchPh: '搜索标题',
    honorList: '荣誉列表',
    creator: '创建人',
    publishTime: '发布时间',
    createTime: '创建时间',
    auditTime: '审核时间',
    opreate: '操作',
    honorNoData: '暂无荣誉榜',
    honorDate: '获得荣誉时间',
    sendTo: '发布至',
    changeMethod: '修改渠道',
    reserve: '撤回',
    publishagain: '再次提交',
    delete: '删除',
    edit: '编辑',
    publishverify: '已开启发布安全验证',
    honorTitle: '荣誉标题',
    honorTitleph: '请输入',
    honorUrl: '荣誉封面',
    honorTime: '获得荣誉时间',
    honorTimeph: '请选择',
    honorContent: '正文内容',
    square: '广场号',
    member: '数字平台',
    publish: '发布',
    reserveTip: '撤回后，审核人将无法再进行审核，并将内容转移到草稿箱中。',
    reserveTip1: '撤回后，荣誉榜中将不再显示此荣誉，并转移到草稿箱中，确定要撤回吗？',
    deleteTip: '删除后将不可恢复，确定要删除该条荣誉吗？',
    deleteTip1: '删除后将不可恢复，确定要删除当前草稿吗？',
    saveDraft: '存草稿',
    saveDraftTip: '是否将当前内容保存为草稿？',
    changePublish: '修改发布渠道',
    reserveHonor: '撤回荣誉',
    reserveHonorTip: '撤回後，當前內容會被刪除並轉移至草稿箱',
    addHonorTip: '请填写必填项',
    addHonorTip1: '必填项',
    growth: '历程列表',
    intro: '组织介绍',
    dqr: '待确认',
    createUser: '创建人',
    postedTime: '发布时间',
    operate: '操作',
    auditer: '审核人',
    introduceTitle: '介绍模块',
    introduceContent: '正文内容',
    introduceTitleTip: '请输入介绍模块',
    introduceContentTip: '请输入内容',
    publishTip: '请选择发布的类型',
    rejectReason: '拒绝原因',
    growthTime: '历程时间',
    growthTimeTitle: '历程时间（已发布、审核中日期不可选择 ）',
    growthContent: '历程内容',
    growthTitleTip: '历程标题',
    growthCover: '历程图片',
    growthTimeTip: '请选择历程时间',
    growthContentTip: '请输入历程内容',
    growthCoverTip: '请选择历程封面图',
    sendToTips: '请选择发布至',
    addGrowthTitle: '发布历程',
    editGrowthTitle: '编辑历程',
    growthDetailTitle: '历程详情',
    addIntroTitle: '发布组织介绍',
    editIntroTitle: '编辑组织介绍',
    introDetailTitle: '介绍详情',
    norSortBySingleModule: '单个模块无法排序',
    sortSettingSuccess: '排序设置成功',
    deleteModuleTip: '删除后将不可恢复，确定要删除该介绍模块吗？',
    cancelTip: '撤回后，审核人将无法再进行审核，并将内容转移到草稿箱中。确定要撤回吗？',
    publishConfirmTip: '确定发布内容？',
    createUserTitle: '创建人',
    createTimeTitle: '创建时间',
    auditUserTitle: '审核人',
    auditTimeTitle: '审核时间',
    refuseReasonTitle: '拒绝原因',
    publishTeamTitle: '组织名称',
    publishUserTitle: '发布人',
    publishOperateTime: '操作时间',
    publishContentTitle: '发布内容',
    publishPreviewTIitle: '预览发布内容',
    publishConfirmTitle: '确认发布',
    deleteModuleIconTip: '删除模块',
    auditSubmitTip: '发布内容已提交审核人处理',
    cancelPublishTip: '撤回后，{0}列表中将不再显示此{1}，并转移到草稿箱中。确定要撤回吗？',
    onlyPublishIntroduce: '只能发布1篇组织介绍',
    contentIsDeleted: '内容已删除',
    addIntroduceModule: '新增介绍模块',
    setModuleOrder: '调整模块排序',
    introduceTitleNotSame: '组织介绍的标题不能相同',
    titleNotEmpty: '标题不能为空',
    growthTitle: '历程',
    growthTimeRepeat: '历程时间已重复，请重新选择',
    growthTimeNotEmpty: '历程时间不能为空',
    notSetTitleModule: '未设置标题模块',
    iknow: '我知道了',
    inpuAllMustContent: '请填写完必填项后再预览。',
    publishNeedAuth: '由于你是审核人，所以无需走安全验证，确定要发布当前{0}吗？',
    publishNotNeedAuth: '确定要发布当前{0}吗？',
    notFoundContent: '暂时没有找到相关内容~',
    notIntroduceContent: '暂无介绍',
    notGrowthContent: '暂无历程',
    deleteTip2: '删除后将不可恢复，确定要删除该条{0}吗？',
    publishIntroduceTitle: '发布介绍',
    publishGrowthTitle: '发布历程',
    rePublishTitle: '再次发布',
    deletedData: '当前内容已被删除',
    notIntroduce: '暂无组织介绍',
    notGrowth: '暂无历程',
    organizationSquare: '你没有发布该组织动态的权限，请联系组织广场管理员。',
    newsSetting: '资讯审核设置',
  },
  network: {
    timeout: '请求超时',
    error: '网络连接失败，请检查网络后重试',
    fail: '请求失败',
  },
  system: {
    otherLogged: '当前账号已在其他设备登录',
  },
  notend: '无限',
  SensitiveContent: '你输入的信息包含敏感内容,请修改后重试',
  address: {
    book_info: '通讯录信息',
    new_period: '新增字段',
    period_name: '字段名称',
    detail_info_show: '通讯录详情展示',
    member_available: '成员可编辑',
    operate: '操作',
    submit_success: '提交成功',
    role_deactivated: '角色已停用',
    sure_delete_tips: '确定要删除该字段吗？删除后已有的数据也会被删除。',

    new_role: '新增角色',
    role_name: '角色名称',
    please_role_name: '请输入角色名称',
    role_desc: '角色描述',
    setCompanyTips: '设置企业全体成员在通讯录详情中展示的字段信息',
    delete: '删除',
    operate_success: '操作成功',
    sure: '确定',
    save: '保存',
    cancel: '取消',
    please_depart_member: '请选择部门成员',
    please_depart_or_member: '请选择部门或成员',

    please_select_see_area: '请选择可见范围部门或成员',
    all_person: '所有人',
    owner_depart: '本部门',
    select_depart_or_member: '选择部门或成员',
    unselect_value: '当前未选中值',
    tip: '提示',
    no_data: '暂无数据',
    app_cate: '应用分类',
    role: '角色',
    please_select_role: '请选择角色',

    all: '全部',
    all_member_available: '全员可用',
    part_member_available: '部分成员可用',
  },
  order: {
    // {{t('order.qqb')}}
    // t('order.qqb')
    gmzt: '购买主体',
    sqtk: '申请退款',
    txtkxx: '填写退款信息',
    tksm: '退款说明',
    tkjeaylfh: '退款金额按原路退回',
    txtkxx1: '若为个人则退款账户绑定购买人名下银行信息；若为组织则退款账户绑定组织名下银行信息。',
    ckrgsmz: '持卡人/公司名字',
    qtxckrgsmz: '请填写持卡人/公司名字',
    zwmc: '中文名称',
    qsryhkdckr: '请输入银行卡的持卡人/公司中文名称',
    ywmc: '英文名称',
    qsrckrywmc: '请输入银行卡的持卡人/公司英文名称',
    khh: '开户行',
    khhtip: '开户行 = 所属银行 + 市 + 地区支行例如：中国建设银行珠海湾仔支行',
    qtxkhhmc: '请填写开户银行名称',
    qtxyhdz: '请填写银行地址',
    qtxyhzh: '请填写银行账户',
    znsrsz: '只能输入数字',
    yhzh: '银行账户',
    qsrayzh: '请输入澳门元账户',
    qtxyhxx: '请填写银行账号',
    qsr1030wyhzh: '请输入10到30位银行账号',
    yhzh1: '银行账号',
    scyhtpsb: '上传银行图片识别',
    qtxyhkh: '请填写银行卡号',
    znrszhzyw: '只能输入数字或者英文',

    tkxx: '退款信息',
    tkjl: '退款记录',
    tkzt: '退款状态',
    tkz: '退款中',
    bftk: '部分退款',
    ytk: '已退款',
    tkje: '退款金额',
    tkxq: '退款详情',
    pty: '平台于',
    pty1: '将退款金额退至你所填写的退款账号上，请注意查收。（银行卡退款预计将在3个工作日内处理完成）',
    pty2: '将退款金额原路退回，请注意查收。',

    fkfs: '付款方式',
    skf: '收款方',
    skly: '收款来源',
    tksj: '退款时间',
    spxx: '商品信息',
    yhdz: '银行地址',
    yhkh: '银行卡号',

    jsy: '结算页',
    zbkp: '暂不开票',
    kfp: '开发票',
    groupkptip: '组织暂未认证,不能开票,购买套餐后再认证开票',
    orderendopen: '订单已生成，请在"订单管理"支付订单。',
    dsh: '待审核',
    dgzfdsh: '对公支付状态：待审核',
    dgzfyjj: '对公支付状态：已拒绝',
    scpz: '上传凭证',
    qsczzjljthdzhd: '请上传转账记录截图或电子回单',
    itemgroup: '当前组织名称',
    sn: '订单编号',
    zsszygmrtt: '至少设置一个默认抬头',
    zf: '支付',
    zzyhxx: '转账银行信息',
    zysx: '注意事项',
    qbcopy: '全部复制',
    qzxyd: '(请仔细阅读)',
    zzjt: '1、转账截图',
    zzjt1: '在完成下单转账后，请提供',
    zzjt2: '转账记录截图或电子回单',
    zzjt3: '，这将有助于加快款项核销速度',
    jeyz: '2、金额一致',
    jeye1:
      '务必确保转账金额与订单金额一致，如若出现多转、少转或分次转账的情况，平台将判定该订单为未付款，并将全部转账金额退还。',
    jjdf: '3、拒绝代付',
    jjdf1: '不接受其他形式的代付转账，如有此类情况，平台将判定该订单为未付款，系统将自动取消订单并退还全部转账金额。',
    bzqx: '4、备注清晰',
    bzqx1:
      '在汇款时，请将汇款识别码和汇款组织名称填写在汇款单的“用途/备注/摘要”等栏，以便于快速核销款项。若未填写，平台将判定该订单为未付款，系统将自动取消订单并退还全部转账金额。',
    ddyq: '5、逾期退款',
    ddyq1: '若平台在1～5个工作日内未收到转账或未完成对账，系统将自动拒绝订单并退还全部转账金额。',
    ddyq2: '6、审核生效',
    ddyq3: '审核通过后，订单内的套餐内容才会生效，请提前下单并进行对公支付，以免服务套餐到期未及时续费等情况发生。',
    ddyq4: '7、差额说明',
    ddyq5: '由于对公支付需经过审核流程，下单时所计算的支付金额与实际审核通过后所计算的支付金额可能存在一定差额。',
    zzfkhqscdydpz: '转账付款后请上传对应的凭证',
    tips: '提示',
    chinese: '中国大陆',
    chinese1: '转账后，请把对应的转账凭证截图或电子回单上传到对应的待审核的订单的凭证上，例如：',
    chinesemo: '中国澳门',
    chinesemo1: '转账后，请把对应的转账凭证截图或电子回单上传到对应的待审核的订单的凭证上，例如：',
    qsczzhd: '请上传转账后的',
    pzhzdzhd: '凭证或者电子回单',
    pzhzdzhd1: '，以便平台快速核对。若无上传凭证，则平台会对该订单进行取消处理，并会将转账金额全部退回。',
    updtip: '上传凭证（支持PNG，JPG，JPEG格式上传，最多上传5张）',
    lxdh: '联系电话',
    ptshyywfbynlx: '平台审核有疑问,方便与你联系',
    qsrlxdh: '请输入联系电话',
    clickpd: '点击上传',
    bz: '备注',
    qsrbz: '请输入备注',
    jjyy: '拒绝原因',
    qxdd1: '1、取消订单：确保转账的款项退回后，再进行订单取消。',
    qxdd2: '2、重新提交：将当前订单状态改为“待审核”，并重新审核。请确保所提问题修改，否则平台将再去拒绝。',
    qxdd: '取消订单',
    cxtj: '重新提交',
    receiver: '收款公司名称',
    bank: '开户银行',
    account: '银行卡号',
    remark: '用途/备注/摘要栏',
    receiver_en: '英文名称',
    bank_address: '银行地址',
    accountMo: '澳门元账户',
    dgzf: '对公支付',
    qscpz: '请上传凭证',
    yscdsgddtip: '已生成待审核订单，请尽快转账支付，并上传对应的截图凭证或电子回单。',
    czcg: '复制成功',
    wjgscw: '文件格式错误',
    cgzdscsl: '超出最大上传数量',
    czsb: '操作失败',

    qy: '权益',
    gmxy: '购买协议',
    qsryqm: '请输入邀请码',
    palseordername: '请输入产品名称',
    info: '产品信息',
    byer: '购买人',
    kptime: '开票时间',
    fpinfo: '发票信息',
    kpfile: '开票附件',
    invoiceD: '发票详情',
    Pleasenternumbersorletters1: '单位税号为10到20位数字字母组合',
    Pleasenternumbersorletters3: '银行卡号为10到30位数字',
    PackagesListtip: '动态发布:全场景公域流量社交渗透',
    PackagesListtip1: '广场号专有简称:提升品牌辨识度',
    PackagesListtip2: '广场-另可圈展位:品牌价值的无限穿透',
    PackagesListtip3: `广场-官网模板:品牌数字化根据地`,
    PackagesListtip4: `广场-拾光相册:企业数字资产的抢救方案`,
    PackagesListtip5: '在线客服:重要的私域搭建入口',
    PackagesListtip6: '商机:联通供需商机闭环',
    PackagesListtip7: '服务:涵盖所有商业形态的线上化',
    //
    dow: '下载',
    orderNo: '订单编号',
    orderstats: '订单状态',
    nodata: '暂无数据',
    paymentamount: '支付金额',
    order: '订单',
    ditiles: '详情',
    by: '购买',
    orderlistinfo: '订单信息',
    time: '时间',
    memberInformation: '订单信息',

    notInvoiced: '未开票',
    wait_pay: '待付款',
    finished: '已完成',
    closed: '已关闭',
    no_piao: '未开票',
    wait_valid: '待审核',
    valided: '已开票',
    sure: '确定',
    save: '保存',
    ge: '个',
    alldow: '批量下载',
    cancel: '取消',
    pay: '付款',
    cancel_order: '取消订单',
    close_reason: '关闭原因',
    please_input_order: '请输入取消原因',
    pay_type: '付款方式',
    Invoiceheadermanagement: '发票管理',
    updtip20: '单个图片不可超过',
    Invoicingfile: '发票附件',
    search: '搜索',
    reset: '重置',
    applyfor: '申请',
    Reinvoicing: '重新开票',
    Pleaseselectadate: '请选择日期',
    //
    ReasonforRefusingInvoicing: '拒绝开票原因',
    Invoicingtip1: '若开具的发票抬头与购买人不一致，请在“开票资料”上传“付款回执”。',
    selethead: '选择发票抬头',
    danwei: '单位',
    Applyforinvoicing: '申请开票',
    gchpro: '广场号使用权限',
    Invoicinginformation: '开票信息',
    Invoicinginformationzl: '开票资料',
    Viewinvoice: '查看发票',
    Reasonrejection: '拒绝原因',
    closejet: '关闭原因',
    unitprice: '单价',
    Productquantity: '商品数量',
    Productdoesnotexist: '商品信息不存在',
    amountofmoney: '金额',
    Expirationtime: '到期时间',
    cancellationoforder: '取消订单',
    Theorderstatusoperation: '订单状态已变更,请重新操作',
    closeTip: '支付渠道已关闭,请联系平台客服处理!',
    Preferentialreduction: '优惠立减',
    Totalpriceofgoods: '商品总价',
    Disbursements: '实付款',
    residue: '剩余',
    invoiceManagement: '发票管理',
    addinvoiceHeader: '添加发票信息',
    invoiceHeader: '发票抬头',
    book: '增值税专用发票确认书',
    VATinvoice: '增值税专用发票',
    ordinaryInvoice: '普通发票',
    geren: '个人',
    unit: '单位',
    Invoiceheaderisrequired: '发票抬头必填',
    morem: '默认抬头',
    IreadAgree: '我已阅读并同意',
    Confirmtdeletetheinvoiceheader: '确定删除该发票抬头?',
    Emile: '电子邮箱',
    Pleaseenteranemailaddress: '请输入电子邮箱',
    Pleasebankaccountnumber: '请输入银行账号',
    Bankaccount: '银行账号',
    BankaccountRequired: '银行账号必填',
    PleaseenterRegistrationPhoneNumberOpeningBank: '请输入开户银行',
    OpeningBankRequired: '开户银行必填',
    OpeningBank: '开户银行',
    pleaseenteranumber: '请输入数字',
    PleaseenterRegistrationPhoneNumber: '请输入注册电话',
    Pleaseenterisokphone: '请输入正确的电话',
    RegistrationPhoneNumberIsrequired: '注册电话必填',
    RegistrationPhoneNumber: '注册电话',
    RegisteredaddressIsRequired: '注册地址必填',
    Registeredaddress: '注册地址',
    Unittaxnumber: '单位税号',
    Pleasenternumbersorletters: '请输入数字或者字母',
    Unittaxnumberisrequired: '单位税号必填',
    personalInvoice: '个人发票',
    confirmationVATSpecialInvoice: '增值税专用发票确认书',
    text: `根据国家税法及发票管理相关规定，任何单位和个人不得要求他人为自己开具与实际经营业务情况不符的增值税专用发票【包括并不限于
    （1）在没有货物采购或者没有接受应税劳务的情况下要求他人为自己开具增值税专用发票；
    （2）虽有货物采购或者接受应税劳务但要求他人为自己开具数量或金额与实际情况不符的增值税专用发票】，
    否则属于“虚开增值税专用发票”。
     我已充分了解上述各项相关国家税法和发票管理规定，并确认仅就我司实际购买商品或服务索取发票。
     如我司未按国家相关规定申请开具或使用增值税专用发票，由我司自行承担相应法律后果。`,
    isok: '知道了',
    InvoiceType: '发票类型',
    EditinvoiceHeader: '编辑发票信息',
    Headingtype: '抬头类型',
    //

    orderName: '产品名称',
    orderManagement: '订单管理',
    fapstat: '当前订单发票状态已变更,申请失败',
    outeOrderTime: '下单时间',
    stats: '状态',
    invoiceStatus: '发票状态',
    orderTime: '下单时间',
    orderD: '订单详情',
    pendingReview: '待审核',
    kpz: '开票中',
    Invoiced: '已开票',
    jsdz: '(即时到账,支持主流在线支付)',
    tgptzz: '通过平台转账,转账后1-5个工作日内到账',

    PleaseCustomerProcessing: '请联系平台客服处理',
    qrCodeHasExpired1: '页面重新获取二维码',
    qrCodeHasExpired: '二维码已过期',
    close: '关闭',
    repaymentTip: '支付已取消，请在“我的订单”重新支付当前订单',
    orderStatusHasChanged: '订单状态已变更，请重新确认',
    confirmDeparture: '确认离开',
    continuePayment: '继续支付',
    scanCodePayment: '扫码支付',
    orderTip: '订单还未完成支付，请尽快支付',
    qrCodeExTime: '距离二维码过期还剩',
    paymentErr: '支付失败',
    paymentClose: '支付渠道已关闭',
    qrCodeExTime1: '秒,过期后请刷新页面重新获取二维码',
    scanQRCodePaymentForPayment: '扫描二维码支付',
    payableAmount: '应付金额',
    // mine
    qqb: '请确保退款信息与订单购买主体一致，提交成功后，待财务审核退款。',
    qsr1030wyhkh: '请输入10到30位银行卡号',

    qsr510wyhzh: '请输入5到20位的swift code',
    zfqdygbqlxkfcl: '支付渠道已关闭，请联系客服处理！',

    paycximg: '支付结果查询中',
    saocode: '扫一扫',
    plaisit: '请使用',
    kpje: '开票金额',
    sqtime: '申请时间',
    orderaddInvo: '增值税专用发票',
    saveas: '另存为',
    fistimg: '已是第一个',
    lastimg: '已是最后一个',
    notFilePlaseDownload: '文件不存在,请重新下载',
    fileTypeNotview: '文件类型无法预览',
    openFiles: '打开文件夹',
    sendmile: '发送邮箱',
    notupd: '企业盘根目录不可上传文件',
    closeTime: '关闭时间',
    transOrder: '转订单',
    transedOrder: '已转订单',
    consultOrder: '咨询单',
    consultOrderSn: '咨询单编号',
    consultOrderType: '咨询类型',
    consultPerson: '咨询人',
    consultType: '类型',
    consultMain: '咨询主体',
    consultTime: '咨询时间',
    consultTimeRemaining: '剩余时间',
    buyPerson: '购买人',
    bugMain: '购买主体',
    goodsName: '商品名称',
    packageNotExsit: '转订单失败，套餐不存在',
    transOrderTips: '转订单失败，当前主体还有未完成的订单',
    consultExpired: '咨詢單已過期',
  },
  payment: {
    jsdz: '(即时到账,支持主流在线支付)',
    tgptzz: '(通过平台转账,转账后1-5个工作日内到账)',
    PleaseCustomerProcessing: '请联系平台客服处理',
    qrCodeHasExpired1: '页面重新获取二维码',
    qrCodeHasExpired: '二维码失效，请',
    cxhq: '重新获取',
    close: '关闭',
    repaymentTip: '支付已取消，请在“我的订单”重新支付当前订单',
    orderStatusHasChanged: '订单状态已变更，请重新确认',
    confirmDeparture: '确认离开',
    continuePayment: '继续支付',
    scanCodePayment: '扫码支付',
    orderTip: '订单还未完成支付，请尽快支付',
    qrCodeExTime: '距离二维码过期还剩{num}秒',
    paymentErr: '支付失败',
    paymentClose: '支付渠道已关闭',
    qrCodeExTime1: '秒,过期后请刷新页面重新获取二维码',
    scanQRCodePaymentForPayment: '扫描二维码支付',
    payableAmount: '应付金额',
    smzf: '扫码支付',
  },
  clouddisk: {
    xzwz: '选择位置',
    lswz: '历史位置',
    sxz: '刷新中',
    sxdw: '刷新定位',
    zkgddz: '展开更多地址',
    fjdz: '附近地址',
    deltip1: '确定删除当前位置?',
    allerfile: '全员文件夹',
    allKind: '全部分类',
    type: '类型',
    dowendtip: '已经下载完成',
    orsaveml: '选择保存目录',
    time: '时间',
    linkTimeend: '分享链接已过期,无法查看',
    updend: '上传完成',
    savetoclo: '保存到云盘',
    screen: '筛选',
    fileNote: '文件路径不存在',
    groupsThatHaveStartedSynchronization: '已开启同步的群',
    wantToDeleteThisAdministrator: '确定要删除该管理员吗?',
    removeFromCloudDiskManagementBackend: '从云盘管理后台中移除',
    AdministratorToCloudDiskAdministrator: '将原云盘超级管理员调整为云盘管理员',
    originalCloudDiskSuperAdministrator: '原云盘超级管理员',
    selectSuperAdministrator: '请选择新的云盘超级管理员',
    cloudDiskSuperAdministrator: '云盘超级管理员',
    cloudDiskAdministrator: '云盘管理员',
    transferSuperAdministrator: '转移超级管理员',
    updErrorTip: '上传失败,请检查网络',
    updError: '上传失败',
    notspace: '无可用空间',

    send: '发送',
    addAdministrator: '添加管理员',
    cloudDiskPermissionstip: '开通云盘应用的人为超级管理员，添加的云盘管理员具有进入云盘管理后台的权限。',
    expansionTip: '管理员你好，我正在使用企业云盘存储重要文件，当前可用的容量不足，申请扩容',
    requestWillSenthroughChat: '将通过聊天向云盘管理员发送申请',
    applyCloudDiskAdministrator: '向云盘管理员申请',
    schedule: '进度',
    upload: '上传',
    uploadnumTip: '项正在上传',

    continueUploading: '继续上传',
    updatedTip: '上传尚未完成,是否取消上传?',
    uploadFailed: '上传失败',
    copyLink: '复制链接',
    generateLink: '生成链接',
    extractCode: '提取码',
    fileOwnership: '文件归属',
    enterpriseCloudDisk: '企业云盘',
    externalLinks: '外部链接',
    noPermissionAccessCloudDisk: '无权限访问该云盘',
    text: '文档',
    folderName: '文件夹名称',
    fileName: '文件名称',
    thegeneratedLinkTip: '生成后的链接为公开链接，所有人都可以下载文件',
    goBack: '返回上一级',
    remainingEffectiveTime: '剩余有效时间',
    reduction: '还原',
    wantToRestoreThisFile: '确定要还原该文件吗?',
    clearRecycle: '确定要清空回收站吗?',
    recycleTip: '回收站文件保存7天后将被自动清除',
    fromFarToNear: '从远到近',
    fromNearToFar: '从近到远',
    copiedOrDeleted: '标记为重要文件后,该文件不可被重命名,移动,复制和删除',
    contentsOfTheFolder: '你确定退出该文件夹吗？退出后，你将无法继续查看文件夹中的内容。',
    MovedToTheRecycleBin: '删除后,文件将移入到回收站',
    unmark: '取消标记',
    lastUpdate: '最后更新',
    unmarkTip: '确定取消标记重要文件吗?,该文件不可被重命名,移动,复制和删除',
    name: '名称',
    updtime: '更新时间',
    clouddiskCapacityend: '在云盘中已使用的容量：',
    readOnly: '只读权限',
    yourCurrentPermissionsAre: '你当前的权限为',
    ConfirmDeletionFilePermissionsForUser: '确定删除此用户文件权限',
    youCanContactTheAdministrator: '无法进入权限管理.可联系管理员',
    addFile: '新建文件夹',
    PermissionToUploadAndDownload: '可上传下载权限',
    editablePermissions: '可编辑权限',
    help: '帮助',
    viewableOnly: '仅可查看',
    copy: '复制',
    creator: '创建者',
    lu: '录',
    pleaseSelectDirectory: '请选择目录',
    dow: '下载',
    sendLink: '发送链接',
    externalChainSharing: '外链分享',
    selectMoveDirectory: '选择移动目录',
    selectCopyDirectory: '选择复制目录',
    selectSaveDirectory: '选择存入目录',
    rightsAdjusting: '权限调整',
    transferOwner: '转移所有者',
    sendMessageNotification: '发送消息通知',
    notificationOnDeletion: '删除时发送通知',
    notificationWhenModifying: '修改时发送通知',
    editable: '可编辑',
    canbeuploaded: '可上传/下载',
    cannotBeDownloaded: '仅可在线预览,不能下载',
    rightsManagement: '权限管理',
    nodata: '暂无内容',
    personaLisk: '个人盘',
    clouddiskfx: '通过云盘分享的',
    selected: '已选',
    xiang: '项',
    move: '移动',
    expansion: '扩容',
    upd: '上传',
    searchKeywords: '搜索关键词',
    markImportantFiles: '标记重要文件',
    cloudDiskGroupFiles: '云盘群文件',
    clouddiskFile: '云盘文件',
    cloudDiskManagementBackground: '云盘管理后台',
    enterpriseStorage: '企业存储',
    cloudDiskadministrator: '云盘管理员',
    cloudDiskCapacity: '云盘容量',
    by: '购买',
    haveStartedSynchronization: '企业可对已开启同步的群的占用空间进行统一管理',
    notHavelimitsOfAuthority: '你没有云盘后台管理权限',
    clearGroupFile: '清理无效群文件',
    lastActiveTime: '最后活跃时间',
    searchGroupFile: '搜索群名称',
    willBeRecordedAsActive: '以下行为会被记录为活跃：上传、下载',
    fromlargetosmall: '从大到小',
    frombigsmalltobig: '从小到大',
    used: '已使用/总量',
    numberOfAvailableUsers: '可使用人数',
    enterpriseBackupCapacity: '企业备份容量',
    storageCapacityManagement: '存储容量管理',
    thePlatformIsCurrentlyUnder: '平台正在研发中,敬请期待!',
    delGroupFileTip: '删除群文件后，群文件夹将从企业云盘中移除，备份的群文件将会被清空。该群的同步开关也会被关闭。',
    clearGroupFileTip: '对于已经开启了同步群，若群内没有文件，且该群已解散的，清理后这些群文件将会被删除。',

    clearBtn: '开始清理',
    max5G: '企业备份容量不足,当前最大可设置的容量是5GB',
    removeThisMemberTip:
      '确定要移除该成员吗？移除后该成员无法再使用本企业云盘。若该成员具有所有者资源，会进行资源转移。',
    maxnotmin: '设置的容量上限不得小于已使用容量',
    groupFilesTip: '仅包含开启了同步到云盘的内部群群文件',
    expandedByPurchasingPackage: '专享容量可通过购买套餐进行扩容',
    teamFiles: '团队文件',
    exclusiveCapacity: '专享容量',
    sharedBackupCapacity: '共享备份容量',
    assignmembers: '分配成员',
    extfiles: '退出文件夹',
    doesTheOriginalOwnerExitTheFolderAfterTransfer: '转移后原所有者是否退出文件夹',
    delFiletip: '确定删除该文件吗?',
    membersWithCloudDiskPermissionsAbove: '请从以上具有云盘权限的成员中选择',
    numberOfPeopleUsed: '已使用人数',
    addnumMaxTip: '操作失败,已选人数超过可使用人数',
    numberOfPeopleToBeAdded: '待添加人数',
    addMembers: '添加成员',
    cloudDiskHypertube: '云盘超管',
    transfer: '转移',
    Transferee: '被转移人',
    no: '工号',
    department: '部门/岗位',
    success: '操作成功',
    applyName: '应用名称',
    lastUdateTime: '最后更新时间',
    noPermissions: '无权限',
    clouddisk: '云盘',
    cloudDiskAndNeedsToBeTransferred: '该成员在云盘中拥有所有者资源，需要进行转移',
  },

  welcome: '欢迎登錄',
  buttonTips: '您可以点击的按钮测试功能',
  waitDataLoading: '等待数据读取',
  home: {
    openPreloadWindowError: {
      title: '提示',
      content: '请移步项目的strict分支',
      confirm: '确定',
    },
  },
  about: {
    system: '关于系统',
    language: '语言：',
    languageValue: '中文简体',
    currentPagePath: '当前页面路径：',
    currentPageName: '当前页面名称：',
    vueVersion: 'Vue版本：',
    electronVersion: 'Electron版本：',
    nodeVersion: 'Node版本：',
    systemPlatform: '系统平台：',
    systemVersion: '系统版本：',
    systemArch: '系统位数：',
  },
  buttons: {
    console: '控制台打印',
    checkUpdate: '检查更新',
    checkUpdate2: '检查更新（第二种方法）',
    checkUpdateInc: '检查更新（增量更新）',
    startServer: '启动内置服务端',
    stopServer: '关闭内置服务端',
    viewMessage: '查看消息',
    openNewWindow: '打开新窗口',
    simulatedCrash: '模拟崩溃',
    changeLanguage: 'Change language',
    ForcedUpdate: '强制更新模式',
    printDemo: '打印例子',
    incrementalUpdateTest: '增量更新测试',
    openPreloadWindow: 'preload.js测试',
  },
  print: {
    print: '打印',
    silentPrinting: '静默打印',
    backgroundColor: '背景色',
    use: '使用',
    unuse: '不使用',
    notUse: '非',
    tips: '建议使用打印PDF测试（省纸），成功后再使用真实情况',
    blackAndWhite: '黑白',
    colorful: '彩色',
    margin: '边距',
    top: '上',
    bottom: '下',
    right: '右',
    left: '左',
  },
  account: {
    welcomkyy: '欢迎使用另可',
    captcha: '验证码登录',
    passwordLogin: '密码登录',
    emailLogin: '邮箱登录',
    scanLogin: '扫码登录',
    inputTel: '请输入手机号',
    inputEamil: '请输入邮箱',
    inputTelOrEmail: '请输入手机号/邮箱',
    inputCode: '请输入验证码',
    sendCode: '获取验证码',
    resend: '重新获取',
    inputPw: '请输入密码',
    login: '登录',
    autoLogin: '自动登录',
    forget: '忘记密码',
    exclusiveAccount: '专属账号',
    registerAccount: '注册账号',
    back: '返回',
    registerkyy: '欢迎注册另可',
    tel: '手机',
    mail: '邮箱',
    inputName: '请输入姓名',
    registerLogin: '注册并登录',
    read: '我已阅读并同意',
    agreement: '服务协议，',
    agreement1: '服务协议',
    privacy: '隐私政策',
    setPw: '设置密码',
    confirm: '确认',
    pwRule: '至少8个字符，同时包含字母和数字',
    confirmPw: '确认密码',
    reConfirmPw: '请再次确认密码',
    cancel: '取消',
    pwRequired: '密码必填',
    pwCheckRule: '密码必须同时包含数字字母且最少8个字符',
    pwDif: '两次密码不一致',
    agree: '同意',
    pleaseAgree: '请先同意',
    search: '搜索',
    logining: '正在登录',
    setting: '设置',
    tip: '提示',
    proxySetting: '代理设置',
    proxyType: '代理类型',
    noProxy: '不使用代理',
    sysProxy: '使用系统代理',
    httpProxy: '使用HTTP代理',
    adress: '地址',
    port: '端口',
    userName: '用户名',
    pw: '密码',
    selectInput: '选填',
    checking: '检测',
    hot: '热门',
    otherType: '其他类别',
    changeAccount: '切换账户',
    preference: '设置',
    myOrder: '我的订单',
    logout: '退出登录',
    modifyPw: '修改密码',
    oldPw: '原密码',
    newPw: '新密码',
    cardCheck: '身份验证',
    cardCheckTip: '完成个人信息验证',
    cardCheckType1: '手机验证码',
    cardCheckType2: '登录密码',
    cardCheckType3: '邮箱验证码',
    appAuthTip: '无该应用访问权限，请联系组织管理员',
    appDisabledTip: '应用已停用',
    newVersionTip: '当前软件有新版本，请更新后使用',
    immediateUpdate: '立即更新',
    noUpdate: '暂不更新',
    langRestartTip: '切换语言需要重启另可才能生效',
    restart: '重启',
    online: '当前账号',
    offline: '已离线',
    addOtherAccount: '添加其他账号',
    loginOtherAccount: '登录其他账号',
    loginAccount: '登录账号',
    deleteAccount: '移除账号',
    deleteAccountTip: '移除后将无法快速切换账号',
    deleteAccountSuccess: '移除成功',
    changeAccountError: '切换失败',
    changeAccountSuccess: '切换成功',
    changeAccountTip: '账号已离线，需重新登录',
    cancelDelete: '暂不移除',
    confirmDelete: '确定移除',
    cancelLogin: '暂不登录',
    confirmLogin: '重新登录',
    changeAccountIng: '切换中',
    logoutAccount: '退出',
    account: '账号',
    logoutAllAccount: '退出所有账号',
    logoutAllAccountTip: '退出登录后，你将无法接收到通知',
    cancelLogout: '暂不退出',
    confirmLogout: '继续退出',
  },
  contacts: {
    selectOrgType: '创建组织类型',
    pleaseSelectOrgType: '请选择要创建的组织类型',
    orgType: '组织类型',
    pleaseSelect: '请选择',
    pleaseInput: '请输入',
    nextStep: '下一步',
    preStep: '上一步',
    other: '其他',
    enterprise: '企业',
    government: '政府',
    government1: '政府单位',
    setOrgInfo: '设置组织信息',
    setOrgTip: '创建新组织，并填写真实信息',
    orgLogo: '组织logo',
    logoTip: '建议上传图片尺寸为200*200,大小不超过1M支持格式：svg、jpg、png、webp',
    orgName: '组织名称',
    orgAddress: '组织所在地',
    allowEmai: '允许员工通过企业邮箱加入',
    emailDomain: '企业邮箱域名',
    setPersonal: '设置个人信息',
    setPersonalTip: '完成个人信息验证，即可注册成功，你将默认成为企业或组织管理员，并拥有全部管理权限',
    bindMobile: '绑定手机号',
    bindMobileTip: '绑定手机号即可成功创建组织，你将默认成组织管理员，并拥有全部管理权限',
    userName: '姓名',
    tel: '手机号',
    verifyCode: '验证码',
    createOrgSuc: '创建成功',
    createOrgSucTip: '前往添加成员，开启高效协作',
    createOrgSucTip1: '建议立即完成',
    manageBack: '管理后台',
    orgVerify: '组织认证',
    createOrgSucTip2: '完成认证，获取更多权益',
    inviteMember: '邀请成员',
    btnClose: '关闭',
    contacts: '通讯录',
    structure: '组织架构',
    platformMember: '平台成员',
    receiver: '联络人',
    verifying: '审核中',
    createOrganize: '创建组织',
    joinOrganize: '加入组织',
    recent: '最近联系人',
    follow: '特别关注',
    new: '新的联系人',
    friend: '我的好友（个人）',
    groups: '我的群组',
    organizePerson: '组织联系人',
    internalOrganization: '内部组织',
    businessAssociation: '商协会',
    verify: '认证中',
    joinOrg: '加入组织',
    joinOrgTip1: '请输入组织邀请码',
    joinOrgTip2: '域名',
    joinOrgTip3: '企业邮箱加入组织',
    joinInputTip: '请输入组织邀请码/域名/组织邮箱',
    explain1: '组织邀请码是一个字母组成的8位编号',
    explain2: '域名示例：kyymart.com',
    joinPersonalTip: '完成个人信息验证，即可加入',
    checkEmail: '邮箱验证',
    checkEmailTip: '验证码已发送至指定邮箱，请查看后填入正确验证码',
    emailCodeResend: '未收到验证码',
    resend: '重新发送',
    applySuc: '提交成功',
    waitVerify: '正在等待管理员审核',
    joinSuc: '加入成功',
    joinSucTip: '你已成功加入',
    applyCancelTip1: '企业管理员正在审核中，请耐心等待',
    applyCancelBtn: '撤销申请',
    iknow: '知道了',
    confirmCancel: '确定撤销',
    applyCancelTip2: '撤销则视为放弃加入，如要重新加入，需要再次申请，请谨慎操作',
    applyCancelTip3: '已撤销加入申请',
    friends: '好友',
    addContanc: '添加联系人',
    msg: '消息',
    refuse: '未添加',
    agree: '已添加',
    expired: '已过期',
    delete: '已解除',
    sub: '下级',
    admin: '负责人',
    administrators: '管理员',
    superAdmin: '主管理员',
    temporary: '临时',
    selectMember: '选择人员',
    selected: '已选',
    addContacts: '添加好友',
    selectPlaceholder: '请选择',
    searchLkIdPlaceholder: '搜索手机号/另可ID',
    addStatusPassed: '已添加',
    addStatusPassing: '待通过',
    addStatusAdd: '添加',
    changeIdentityTips: '请选择身份后搜索',
    emptyDataTips: '暂无数据',
    orgLocation: '请选择国家/地区',
    selectOrg: '选择组织',
    selectOrgIn: '请选择要加入的组织',
    orgID: '组织ID',
    noJoinOrg: '不能加入该组织',
    unregisteredImTip: '对方已注销，不允许发送消息',
    guide_hututu: '糊涂兔',
    guide_profile: '个人',
    guide_iknow: '知道了',
    guide_tip1: '不同身份添加，会建立不同类型的关系链',
    guide_tip2: '不同身份创建，会建立不同类型的群聊',
    guide_tip3: '外部身份（外部客户的沟通协作）',
    guide_tip4: '个人身份（好友之间的日常交流）',
    guide_tip5: '内部身份（组织内部的沟通协作）',
    guide_lk: '另可科技',
    guide_add_tip1: '使用个人身份添加，建立好友关系',
    guide_add_tip2: '使用组织外部身份添加，建立商务关系',
    guide_profile_card: '个人身份',
    guide_out_card: '外部身份',
    guide_inner_card: '内部身份',
    guide_platform_card: '平台身份',
    groupNamePlaceholder: '请输入群名称',
    view: '查看',
    startTimeTip: '开始日期必须大于结束日期',
  },
  identity: {
    editPersonalInfo: '编辑个人信息',
    editOrgInfo: '编辑组织信息',
    lkID: '另可ID',
    lkIDEdit: '另可ID修改',
    lkIDInputTip: '请输入',
    moreInfo: '更多信息',
    phone: '手机号',
    deleteField: '确定删除信息',
    deleteOrg: '确认删除这个组织',
    delete: '删除',
    inputContent: '请输入内容',
    email: '邮箱',
    emailAddress: '邮箱地址',
    gender: '性别',
    man: '男',
    woman: '女',
    birthday: '生日',
    Y_M_D: '年/月/日',
    area: '地区',
    selectArea: '请选择地区',
    inputInfoName: '请输入信息名称',
    addInfo: '添加信息',
    submit: '提交',
    save: '保存',
    nextStep: '下一步',
    confirm: '确定',
    cancel: '取消',
    edit: '编辑',
    jobNumber: '工号',
    department_position: '部门/岗位',
    departmentLabel: '部门',
    relatedOrg: '关联组织',
    addOrg: '添加组织',
    related: '关联',
    noData: '暂无数据',
    relatedBusinessAssociation: '关联商协会',
    editIdTip1: '你的另可ID一年只可能修改一次',
    editIdTip2:
      '长度限制6-20个字符，可使用字母（必须字母开头）、数字、下划线、减号、英文句号。设置后，一年内不可变更。',
    editIdTip3: '另可ID必须是6-20个字符，可使用字母（必须字母开头）、数字、下划线、减号、英文句号',
    editIdTip4: '该另可ID与原来一致，请重新输入',
    editIdTip5: '该另可ID已被占用，请重新输入',
    orgName: '组织名称',
    position: '岗位',
    telephone: '联系电话',
    fixedTelephone: '固定电话',
    extensionNumber: '分机号',
    areaCode: '区号',
    inputFixedTelephone: '请输入固定电话',
    orgAddress: '组织地址',
    inputDetailAddress: '请输入详细地址',
    orgEmail: '组织邮箱',
    inputOrgEmail: '请输入组织邮箱',
    website: '网址',
    inputWebsite: '请输入网址',
    businessCard: '上传名片',
    pictureTip1: '大小不超过1M，支持格式：JPG、GIF、PNG、BMP、WEBP',
    pictureTip2: '最多2张',
    pictureTip3: '头像大小不可超過100M',
    pictureTip4: '请上传jpg/jpeg/png文件!',
    pictureTip5: '大小不超过1M',
    pictureTip6: '支持格式：JPG、GIF、PNG、BMP、WEBP',
    inputTipOneItem: '请至少填写一项',
    inputTipPhone: '手机号不能为空',
    inputTipPhoneFormat: '手机号格式不正确，请重新输入',
    inputTipTelPhoneFormat: '联系电话格式不正确，请重新输入',
    inputTipRequiredName: '姓名必填',
    required: '必填',
    inputTipEmail: '请输入正确的邮箱地址',
    inputTipRequiredInfoName: '信息名称不能为空',
    inputTipRequiredInfoDetail: '详细信息不能为空',
    inputTipRequiredOrgName: '组织名称不能为空',
    inputTipSpecialChar: '不支持特殊字符',
    inputTipRequiredPosition: '岗位不能为空',
    inputTipWebsite: '请输入正确的网址',
    everyoneShow: '所有人可见',
    onlyShow: '联系人可见',
    everyoneNotShow: '所有人不可见',
    editSuccess: '修改成功',
    saveSuccess: '保存成功',
    operationSuccess: '操作成功!',
    personalInfo: '个人信息',
    name: '姓名',
    setNotesAndDes: '设置备注和描述',
    notes: '备注名',
    describe: '描述',
    addMoreDesInfo: '添加更多描述信息',
    enterSquare: '进入广场号',
    squareAuth: '广场权限',
    noTAWatch: '不让TA看广场动态',
    noWatchTA: '不看TA的广场动态',
    setSquare: '设置广场号权限',
    squareNo: '广场号',
    personalSquareNo: '个人广场',
    orgSquareNo: '组织广场',
    contactVerification: '联系人验证',
    notesInfo: '备注信息',
    send: '发送',
    contactRequestSent: '已发送联系人请求',
    deleteContact: '删除联系人',
    afterDeleteTip: '删除后将不再接受此用户消息',
    clearChat: '清空聊天记录',
    deleteContactExamineTip: '管理员已开启“删除联系人审核”解除关系链需要管理员同意',
    editName: '修改名字',
    inputTipRequiredNickName: '姓名不能为空',
    enableSquareTip: '你当前未启用广场应用，确定启用？',
    shareBusinessCards: '分享名片',
    follow: '关注',
    unfollow: '取消关注',
    addContact: '添加联系人',
    qrcode: '二维码',
    qrcodeScan: '扫描二维码，查看名片',
    savePicture: '保存到本地',
    setting: '设置',
    moreIdentity: '切换身份',
    groupNickname: '群昵称',
    source: '来源',
    pass: '通过',
    entryName: '项目名称',
    duties: '职务',
    agree: '同意',
    voice: '语音',
    video: '视频',
    message: '发送消息',
    phoneNumber: '手机号码',
    withinGroup: '群内',
    mobileSearch: '手机号搜索',
    recommend: '推荐',
    consult: '咨询',
    scanCode: '扫码添加',
    shareCard: '分享名片',
    operationFailed: '操作失败',
    addContactFailed: '添加联系人失败',
    accountCancelled: '账号已注销',
    outer: '外部',
    inner: '内部',
    personal: '个人',
    basicInfo: '基本信息',
    otherOrg: '其他组织',
    e_sign: '已实名',
    e_signed: '已实名认证',
    allTag: '全部标签',
    addTag: '添加标签',
    setTag: '设置标签',
    setTagTip: '请输入标签',
    tag: '标签',
    editTagContent: '编辑内容',
    outerTag: '外部',
    innerTag: '内部',
    contactVerification_new: '发送验证信息',
    notesInfo_new: '验证信息',
    contactRequestSent_new: '已发送添加请求',
    introduce: '自我介绍',
    uploadTips3: '最多支持上传3 个',
    anyoneShow: '部分可见',
    anyoneNotShow: '不给谁看',
    copy: '复制',
    copySuc: '复制成功',
    contactAuthTips: '由于对方隐私设置，无法发起沟通',
    seeIdentity: '查看个人身份',
    everyoneShowContact: '所有人可联系',
    anyoneShowContact: '部分人可联系',
    anyoneNotShowContact: '不给谁联系',
    platform: '平台',
    associationJob: '会内职务',
    notesInfoTips: '添加好友时使用的常用申请语',
    businesCardInfo: '名片信息',
    setApplyText: '设置申请语',
    applyText: '常用申请语',
  },
  application: {
    message: '消息',
    square: '广场',
    address_book: '通讯录',
    zhixing: '知行',
    disk: '云盘',
    approve: '审批',
    project: '工程',
    workBench: '数智工场',
    gcgl: '工程管理',
    member: '数字商协',
    government: '数字政企',
    digital_platform: '数字平台',
    digital_cbd: '数字CBD',
    serve: '服务',
    'chain-device': '设备',
    'chain-customer': '客户',
    'chain-supplier': '供应商',
    'chain-partners': '合作伙伴',
    kefu: '客服应用',
    niche: '商机',
    activities: '活动',
    workshop: '数智工场',
    digital_platform_home: '数字平台',
    management: '管理后台',
  },
  test: testCn,
  approval: approval_CN,
  device: device_CN,
  partner: partner_CN,
  engineer: engineer_CN,
  member: member_CN,
  approve: approve_CN,
  customer: customer_CN,
  supplier: supplier_CN,
  niche: niche_CN,
  square: square_CN,
  free_form: free_form_CN,
  zx: zx_CN,
  components: components_CN,
  editor: editorCN,
  customerService: customer_service_cn,
  im: im_CN,
  activity: activity_CN,
  album: album_CN,
  complain: complain_CN,
  service: service_CN,
  lss: lssCN,
  helpc: help_CN,
  ebook: ebook_CN,
  djMan: dJManagement_CN,
  orderBusiness: orderBusinessCN,
};
