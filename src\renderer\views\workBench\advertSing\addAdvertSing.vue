<template>
  <div class="addAdvertSing">
    <!-- 新增广告 -->
    <div class="banner">
      <div class="header-tab">
        <div class="tab-box">
          <div class="tab-item">
            <div class="flex-box" :class="{ active: activeTab === 0 }">
              <div class="num">1</div>
              <div class="num-text">{{ t("ad.xzggw") }}</div>
            </div>
            <div class="lin"></div>
            <div class="flex-box" :class="{ active: activeTab === 1 }">
              <div class="num">2</div>
              <div class="num-text">{{ t("ad.xztfrq") }}</div>
            </div>
            <div class="lin"></div>
            <div class="flex-box" :class="{ active: activeTab === 2 }">
              <div class="num">3</div>
              <div class="num-text">{{ t("ad.szggnr") }}</div>
            </div>
            <div class="lin"></div>
            <div class="flex-box" :class="{ active: activeTab === 3 }">
              <div class="num">4</div>
              <div class="num-text">{{ t("ad.createSuccess") }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- || activeTab === 3 -->
      <div class="content-box-activeTab0" :class="activeTab === 0 ? 'min-height432px' : ''"
        v-show="(activeTab === 0 || activeTab === 3)">
        <div class="tab-activeTab0" v-show="activeTab === 0">
          <div class="tab-activeTab0-btn" @click="changtabFlag(0)" :class="tabFlag === 0 ? 'activeBtn' : ''">
            市场店铺广告
          </div>
          <div class="tab-activeTab0-btn" @click="changtabFlag(1)" :class="tabFlag === 1 ? 'activeBtn' : ''">
            {{ t("ad.ptscgg") }}
          </div>
          <div class="tab-activeTab0-btn" @click="changtabFlag(2)" :class="tabFlag === 2 ? 'activeBtn' : ''">
            {{ t("ad.ptsygg") }}
          </div>
        </div>
        <div v-show="listData.length === 0 && activeTab === 0" style="height: 100%; margin: 0 auto; text-align: center;flex-direction: column;">
          <img style="width: 200px; height: 200px" src="@/assets/member/Rectangle.svg" />
          <div style="font-size: 14px; font-style: normal; font-weight: 400; line-height: 22px">暂无可投放的广告位</div>
        </div>
        <div v-show="activeTab === 0" style="display: flex; gap: 16px; position: absolute; top: 58px">
          <div class="content-activeTab0" v-for="(item, index) in listData" :key="index">
            <div class="div-img" @click="viewadfn(item)">
              <img class="img0" v-if="(item.name === 'PC_市场_主轮播图' ||item.name === 'PC_市场店铺_主轮播图' )&& (tabFlag === 1||tabFlag === 0)"
                src="@/assets/member/add1.svg" />
              <img class="img0" v-if="(item.name === 'PC_市场_右上'||item.name === 'PC_市场店铺_右上') && (tabFlag === 1||tabFlag === 0)"
                src="@/assets/member/add2.svg" />
              <img class="img0" v-if="(item.name === 'PC_市场_右下'||item.name === 'PC_市场店铺_右下') && (tabFlag === 1||tabFlag === 0)"
                src="@/assets/member/add3.svg" />
              <img class="img0" v-if="(item.name === 'APP_市场_主轮播图'||item.name === 'APP_市场店铺_主轮播图'  )&& (tabFlag === 1||tabFlag === 0)"
                src="@/assets/member/add4.svg" style="object-fit: contain;" />
              <img class="img0" v-if="item.name === 'PC_平台_主轮播图' && tabFlag === 2" src="@/assets/member/app5.svg" />
              <img class="img0" v-if="item.name === 'PC_平台_右上' && tabFlag === 2" src="@/assets/member/app6.svg" />
              <img class="img0" v-if="item.name === 'PC_平台_右下' && tabFlag === 2" src="@/assets/member/app7.svg" />
              <img class="img0" v-if="item.name === 'APP_平台_主轮播图' && tabFlag === 2" src="@/assets/member/app8.svg"
                style="object-fit: contain;" />
            </div>
            <div class="text0">{{ item?.name }}</div>
            <div class="text-price">
              <span style="font-size: 12px">{{ item?.symbol == "¥" ? "¥" : "MOP" }}</span>
              <span>{{
                item.price_count == 1 ? item.min_price_text : `${item.min_price_text}~${item.max_price_text}`
                }}</span>
              <span style="font-size: 12px">/天</span>
            </div>
            <t-button class="tfbtn" theme="primary" @click="tf(item, index)"> {{ t("ad.touf") }}</t-button>
          </div>
        </div>
        <div class="lastsue" v-show="activeTab === 3">
          <img src="@/assets/member/icon_success.svg" />
          <div class="addsec">{{ t("ad.xjcg") }}</div>
          <div class="labsec" style="overflow: inherit; white-space: inherit">
            <div style="display: flex; flex-wrap: wrap">
              <div>{{ t("ad.title") }}: </div>
              <div style="width: 372px">{{ callBackFormValue?.title }}</div>
            </div>
          </div>
          <div class="labsec">
            {{ t("ad.placementDate") }}: {{ dayPriceData?.timeValue[0] }}～{{ dayPriceData?.timeValue[1] }}
          </div>
          <div class="labsec">{{ t("ad.tfwz") }}: {{ listData[imgIndex]?.name }}</div>
          <div class="footers">
            <t-button style="min-width: 80px;font-weight: 600" theme="default" variant="outline" @click="viewAd"
              v-show="activeTab === 3">
              {{ t("ad.viewDetails") }}
            </t-button>
            <t-button style="min-width: 80px;font-weight: 600" theme="primary" @click="nextFn" v-show="activeTab === 3">
              {{ t("ad.fhlb") }}
              <!-- cbd -->
            </t-button>
          </div>
        </div>
      </div>
      <!-- <div class="content-box-activeTab0" v-show="listData.length === 0 && activeTab === 0">
        <div style="height: 100%; margin: 0 auto; text-align: center">
          <img style="width: 200px; height: 200px" src="@/assets/member/Rectangle.svg" />
          <div style="font-size: 14px; font-style: normal; font-weight: 400; line-height: 22px">暂无可投放的广告位</div>
        </div>
      </div> -->
      <div class="content-box-activeTab1">
        <div :class="activeTab === 1 ? 'h528' : 'h688'" class="left-box" v-show="activeTab === 1 || activeTab === 2">
          <div class="div-img">
            <img class="img0" v-if="(imgItem?.name === 'PC_市场_主轮播图' ||imgItem?.name === 'PC_市场店铺_主轮播图' )&& (tabFlag === 1||tabFlag === 0)"
              src="@/assets/member/add1.svg" />
            <img class="img0" v-if="(imgItem?.name === 'PC_市场_右上'||imgItem?.name === 'PC_市场店铺_右上') && (tabFlag === 1||tabFlag === 0)"
              src="@/assets/member/add2.svg" />
            <img class="img0" v-if="(imgItem?.name === 'PC_市场_右下'||imgItem?.name === 'PC_市场店铺_右下') && (tabFlag === 1||tabFlag === 0)"
              src="@/assets/member/add3.svg" />
            <img class="img0" v-if="(imgItem?.name === 'APP_市场_主轮播图'||imgItem?.name === 'APP_市场店铺_主轮播图') && (tabFlag === 1||tabFlag === 0)"
              src="@/assets/member/add4.svg" style="object-fit: contain;" />
            <img class="img0" v-if="imgItem?.name === 'PC_平台_主轮播图' && tabFlag === 2" src="@/assets/member/app5.svg" />
            <img class="img0" v-if="imgItem?.name === 'PC_平台_右上' && tabFlag === 2" src="@/assets/member/app6.svg" />
            <img class="img0" v-if="imgItem?.name === 'PC_平台_右下' && tabFlag === 2" src="@/assets/member/app7.svg" />
            <img class="img0" v-if="imgItem?.name === 'APP_平台_主轮播图' && tabFlag === 2" src="@/assets/member/app8.svg"
              style="object-fit: contain;" />
          </div>
          <div class="activeTab1-title">
            {{ imgItem?.name }}
          </div>
          <div class="activeTab1-info" v-if="dayPriceData && activeTab === 2">
            <div class="lable-active">{{ t("ad.tfrq") }}</div>
            <div class="value-active">{{ `${dayPriceData.timeValue[0]}～${dayPriceData.timeValue[1]}` }}</div>
            <div class="lable-active">{{ t("ad.duration") }}</div>
            <div class="value-active">{{ dayPriceData.totalDay }}天</div>
            <div class="lable-active">{{ t("ad.cost") }}</div>
            <div class="value-active">{{ dayPriceData.symbol == "¥" ? "¥" : "MOP" }} {{ dayPriceData.price }}</div>
          </div>
        </div>
        <div class="right-box">
          <div class="content3" v-show="activeTab === 2">
            <editFormDataPage v-if="activeTab !== 0" ref="editFormDataPageRef" :imgTypeIndex="imgIndex + 1"
              :imgItemName="imgItem?.name" :tabFlag="tabFlag"  @callBackFormData="callBackFormData" @onSave="onSave"
              @updateImg="updateImg"></editFormDataPage>
          </div>
          <priceCalendar @restImgIndex="activeTab = 0" v-if="activeTab !== 0" :setId="imgItem?.id" @subTime="subTime"
            :priceCalendar="'manage'" ref="priceCalendarRef" v-show="activeTab === 1">
          </priceCalendar>
          <div class="footer-box" v-if="activeTab === 1 || activeTab === 2">
            <t-button style="min-width: 80px;font-weight: 600;" theme="default" variant="outline" @click="backFn"
              v-show="activeTab === 2 || activeTab === 1">
              上一步
            </t-button>
            <t-button style="min-width: 80px;font-weight: 600;" theme="default" variant="outline" v-show="activeTab === 2 && viewImgUrl"
              @click="viewFn">
              {{ t("ad.view") }}
            </t-button>
            <t-button :loading="loading" style="min-width: 80px;font-weight: 600;" theme="primary" @click="nextFn"
              v-show="activeTab !== 3">
              {{ t("ad.nextStep") }}
            </t-button>
          </div>
        </div>
      </div>
    </div>
    <viewAdPage ref="viewAdRef"></viewAdPage>

    <!-- 产品需求暂时取消 -->
    <costDetails ref="costDetailsRef"> </costDetails>
    <viewAdImg ref="viewAdImgref"></viewAdImg>
  </div>
</template>
<script setup lang="ts" name="bench_add_advert_sing">
  import {
    spaceselectlist,
    managepublish,
    spaceselectlistplatform,
    existvacancy,
  } from "@renderer/api/member/api/ebookApi";
  import viewAdPage from "@/views/member/member_home/panel/mark-advertising/components/viewAd.vue";

  import editFormDataPage from "@/views/member/member_home/panel/mark-advertising/components/editFormData.vue";
  import costDetails from "@/views/member/member_home/panel/mark-advertising/components/costDetails.vue";
  import { ref, onMounted, watch } from "vue";
  import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import priceCalendar from "@/components/priceCalendar/index.vue";
  import { useI18n } from "vue-i18n";
  import { useRouter, useRoute } from "vue-router";
  import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
  import LynkerSDK from "@renderer/_jssdk";
  const { ipcRenderer } = LynkerSDK;

  const props = defineProps({
    activationGroupItem: {
      type: Object,
      default: () => { },
    },
  });

  let pageType = null;

  const router = useRouter();
  const route = useRoute();

  let teamId = null;
  const tabFlag = ref(null);
  let platform_type = null;
  const { t } = useI18n();
  const activeTab = ref(0);
  const imgIndex = ref(0);
  const imgItem = ref(null);
  const viewAdRef = ref(null);
  const costDetailsRef = ref(null);
  const priceCalendarRef = ref(null);
  const timeArr = ref([]);
  const listData = ref([]);
  const changtabFlag = (val) => {
    tabFlag.value = val;
    console.log(val, '啊实打实大师大师');
    imgItem.value = null;
    activeTab.value = 0;
    imgIndex.value = 0;
    getSpaceselectlist();
  };
  import viewAdImg from "@renderer/views/member/member_home/panel/mark-advertising/components/viewAdImg.vue";

  const viewAdImgref = ref(null)

  const viewadfn = (row) => {
    viewAdImgref.value?.openWin({
      tabFlag: tabFlag.value,
      imgItem: {
        name: row.name,
      },
    });
  };
  watch(
    () => activeTab.value,
    async (newValue, oldValue) => {
      //  重置数据
      console.log(newValue, oldValue, "activeTab重置数据");

      if (newValue === 0) {
        dayPriceData.value = null;
        imgItem.value = null;
        imgIndex.value = 0;
        viewImgUrl.value = null;
        // getSpaceselectlist();
      }
    },
  );
  watch(
    () => tabFlag.value,
    (newValue, oldValue) => {
      if (route.query.form) {
        activeTab.value = 0;

      }
      console.log(route, '触发这里11');
    },
  );
  watch(
    () => route.query,
    (newValue, oldValue) => {
      console.log("route.query进来", newValue, oldValue);
      if (route.path === "/workBenchIndex/addAdvertSing" && newValue.restFlag) {
        activeTab.value = 0;
      }
    },
  );

  const backFn = () => {
    activeTab.value--;
  };
  const viewImgUrl = ref("");
  const updateImg = (url) => {
    viewImgUrl.value = url;
  };
  const dayPriceData = ref(null);
  const callBackFormValue = ref(null);
  const resData = ref(null);
  const callBackFormData = async (val) => {
    console.log(val,55555555556);

    try {
      let str = "";
      if (val.skip_type === 1) {
        str = val.skip_param.uuid;
      } else if (val.skip_type === 2) {
        str = val.skip_param.squareId;
      } else if (val.skip_type === 3) {
        str = val.skip_param;
      }else if (val.skip_type === 5) {
        str = val.skip_param.spuId;
      }
      callBackFormValue.value = val;
      console.log(val, "qingqiujiekou");
      let objs = {
        // 1商机 2广场 3自定义连接 4无跳转
        set_id: imgItem.value?.id,
        begin_at: dayPriceData.value?.timeValue[0],
        end_at: dayPriceData.value?.timeValue[1],
        title: val.title,
        image_url: val.image_url,
        skip_type: val.skip_type,
        skip_param: str,
        remark: val.remark,
        platform_type,
      };

      const res = await managepublish(objs, teamId);
      console.log(res, "awaitawaitasdasdasdasd");
      resData.value = res;
      activeTab.value = 3;
    } catch (error) {
      if (error.code === 90014) {
        editFormDataPageRef.value.closeWin();
      }

      MessagePlugin.error(error?.message);
    }
  };
  let loading = ref(false);
  import { debounce } from "lodash";
  const nextFn = debounce(async () => {
    if (loading.value) {
      return;
    }
    console.log(props.activationGroupItem.user_ids, "activationGroupItemactivationGroupItem");
    console.log(route, "activationGroupItemactivationGroupItemrouteroute");

    if (activeTab.value === 1) {
      loading.value = true;
      if (!dayPriceData.value) {
        if (dayPriceData.value?.timeValue.length === 1) {
          MessagePlugin.error(t("ad.qxztfrq"));
          loading.value = false;
          return;
        }
        MessagePlugin.error(t("ad.qxztfrq"));
        loading.value = false;
        return;
      }
      try {
        const res = await existvacancy({
          set_id: imgItem.value?.id,
          begin_at: dayPriceData.value?.timeValue[0],
          end_at: dayPriceData.value?.timeValue[1],
        });
        console.log(res.data, "啊实打实大");
        console.log(res.data.data, "啊实打实大");
        console.log(res.data.data.open, "啊实打实大");
        if (!res.data.data.open) {
          const myDialog = DialogPlugin({
            header: t("ad.ggwyjy"),
            theme: "info",
            body: t("ad.adPositionDisabledtip1"),
            className: "dialog-classp32",
            confirmBtn: t("ad.xzggw"),
            cancelBtn: null,
            closeBtn: null,
            closeOnOverlayClick: false,
            onConfirm: () => {
              activeTab.value = 0;
              myDialog.hide();
            },
          });
          return;
        }
        if (!res.data.data.not_full) {
          const myDialog = DialogPlugin({
            header: t("ad.adPositionFull"),
            theme: "info",
            body: t("ad.selectDate"),
            className: "dialog-classp32",
            closeBtn: null,
            cancelBtn: null,
            confirmBtn: t("ad.xztfrq"),
            closeOnOverlayClick: false,
            onConfirm: () => {
              myDialog.hide();
              dayPriceData.value = null;
              activeTab.value = 1;
              // editFormDataPageRef.value.onClose();
              if (priceCalendarRef.value) {
                priceCalendarRef.value.restPriceCalendar();
                priceCalendarRef.value.getAllPrice();
              }
            },
          });
          return;
        }
        activeTab.value++;
        return;
      } catch (error) {
        MessagePlugin.error(error.message);
      } finally {
        console.log("变成false");
        loading.value = false;
      }
    }
    if (activeTab.value === 2) {
      console.log(loading.value, "第二步");
      if (loading.value) {
        console.log(loading.value, "第二步return");

        return;
      }
      loading.value = true;
      try {
        const res = await existvacancy({
          set_id: imgItem.value?.id,
          begin_at: dayPriceData.value?.timeValue[0],
          end_at: dayPriceData.value?.timeValue[1],
        });
        console.log(res, "第二步的res");
        console.log(res.data, "第二步的res");
        console.log(res.data.data, "第二步的res");
        if (!res.data.data.open) {
          const myDialog = DialogPlugin({
            header: t("ad.ggwyjy"),
            theme: "info",
            body: t("ad.adPositionDisabledtip1"),
            className: "dialog-classp32",
            confirmBtn: t("ad.xzggw"),
            cancelBtn: null,
            closeBtn: null,
            closeOnOverlayClick: false,
            onConfirm: () => {
              activeTab.value = 0;
              myDialog.hide();
            },
          });
          return;
        }
        if (!res.data.data.not_full) {
          const myDialog = DialogPlugin({
            header: t("ad.adPositionFull"),
            theme: "info",
            body: t("ad.selectDate"),
            className: "dialog-classp32",
            closeBtn: null,
            cancelBtn: null,
            confirmBtn: t("ad.xztfrq"),
            closeOnOverlayClick: false,
            onConfirm: () => {
              myDialog.hide();
              dayPriceData.value = null;
              activeTab.value = 1;
              // editFormDataPageRef.value.onClose();
              if (priceCalendarRef.value) {
                priceCalendarRef.value.restPriceCalendar();
                priceCalendarRef.value.getAllPrice();
              }
            },
          });
          return;
        }

        onSave();
        return;
      } catch (error) {
        MessagePlugin.error(error.message);
      } finally {
        loading.value = false;
      }
    }
    console.log(activeTab.value, "activeTab.valueactiveTab.value");
    console.log(route.path, "activeTab.valueactiveTab.valueroute.puthroute.puth");
    if (activeTab.value === 3) {
      if (route.path === "/workBenchIndex/addAdvertSing") {
        const query = {
          platform: route.path.includes("/workBenchIndex") ? "digital_workbench" : "digital_platform",
          teamId: teamId,
          ...props.activationGroupItem,
          user_ids: JSON.stringify(props.activationGroupItem.user_ids),
        };
        ipcRenderer.invoke("del-work-bench-tab-item", route.name); //cbd增加

        console.log(route, "router啊实打实大");
        router.push({
          path: `${pageType.path}?teamId=${teamId}`,
          query,
        });
        activeTab.value = 0
        ipcRenderer.invoke("set-work-bench-tab-item", {
          path: pageType.path,
          name: pageType.name,
          path_uuid: pageType.uuid,
          title: pageType.title,
          type: pageType.type,
          teamId: teamId,
          query,
        });
      }
      return;
    }
  }, 300);

  const getSpaceselectlist = () => {
    console.log(route.query, 'route.queryroute.queryroute.query触发接口');
    console.log(tabFlag.value, route.query.form, 'rtabFlag.valuetabFlag.value触发接口');

    if (tabFlag.value === 1) {
      spaceselectlist(
        {
          platform_type,
        },
        teamId,
      ).then((res) => {
        listData.value = res.data.data.list;
        console.log(res, "触发市场");
      });
    } else {
      spaceselectlistplatform(
        {
          platform_type,
          ad_type : tabFlag.value === 0?3:2
        },
        teamId,
      ).then((res) => {
        listData.value = res.data.data.list;
        console.log(res, "触发平台");
      });
    }
  };
  onMountedOrActivated(() => {
    if (route.query.form === "mark") {
      tabFlag.value = 1
    } else if (route.query.form === "shop") {
      tabFlag.value = 0
    } else {
      tabFlag.value = 2

    }
    console.log(tabFlag.value, "tabFlag.valuetabFlag.value");
    // mark
    // platform
    if (route.query.platform_type == 1) {
      pageType = {
        uuid: "member",
        type: 21,
        name: "member_home",
        title: t("niche.szsxx"),
        path: "/workBenchIndex/member_home",
      };
    } else if (route.query.platform_type == 2) {
      pageType = {
        uuid: "politics",
        name: "bench_politics_home",
        type: 20,
        title: t("niche.szzqx"),
        path: "/workBenchIndex/politics_home",
      };
    }  else if (route.query.platform_type == 5) {
      pageType = {
        uuid: "uni",
        name: "bench_uni_home",
        type: 32,
        title: t("niche.szgx"),
        path: "/workBenchIndex/uni_home",
      };
    }else if (route.query.platform_type == 4) {
      pageType = {
        uuid: "association",
        name: "bench_association_home",
        type: 28,
        title: t("niche.szsq"),
        path: "/workBenchIndex/association_home",
      };
    } else {
      pageType = {
        name: "bench_cbd_home",
        uuid: "cbd",
        title: t("niche.szcbdx"),
        type: 23,
        path: "/workBenchIndex/cbd_home",
      };
    }
    teamId = route.query.teamId;
    platform_type = route.query.platform_type;
    getSpaceselectlist();
    console.log(route.query, "routerrrrrrrrrr");
  });
  const viewAd = () => {
    console.log(route.query.platform_type, "route.query.platform_typeroute.query.platform_type");

    router.push({
      path: `/workBenchIndex/AdDetails?id=${resData.value.data.data.id}&teamId=${teamId}`,
      query: {
        id: resData.value.data.data.id,
        teamId: teamId,
      },
    });
    ipcRenderer.invoke("del-work-bench-tab-item", route.name); //cbd增加

    ipcRenderer.invoke("set-work-bench-tab-item", {
      path: `/workBenchIndex/AdDetails`,
      name: "bench_ad_details",
      path_uuid: pageType.uuid,
      title: t('ad.ggxq'),
      query: {
        id: resData.value.data.data.id,
        teamId: teamId,
      },
      type: pageType.type,
    });
  };
  const editFormDataPageRef = ref(null);
  const onSave = () => {
    editFormDataPageRef.value.onSave();
  };

  const subTime = (val) => {
    dayPriceData.value = val;
    console.log(val, "选择的时间");
    // timeArr.value = val;
  };
  const viewFn = () => {
    viewAdRef.value.openWin({
      url: viewImgUrl.value,
      imgIndex: imgIndex.value,
      imgItem: imgItem.value,
      tabFlag: tabFlag.value,
    });
  };
  const openMx = (id) => {
    costDetailsRef.value.openWin(dayPriceData.value, id, teamId, null, true);
  };
  const tf = (item, index) => {
    console.log(tabFlag.value, "tabFlagvalue11122");
    console.log(item, "item11111");
    if (tabFlag.value === 1) {
      spaceselectlist(
        {

          platform_type,
        },
        teamId,
      ).then((res) => {
        const resData = res.data.data.list.find((res) => res.id === item.id);
        if (!resData) {
          getSpaceselectlist();
          MessagePlugin.warning(t("ad.ggwyjy"));

          return;
        } else {
          imgIndex.value = index;
          activeTab.value = 1;
          imgItem.value = item;
          console.log(item, "itemmmm");
          if (priceCalendarRef.value) {
            priceCalendarRef.value.getAllPrice();
          }
        }
      });
    } else {
      spaceselectlistplatform(
        {
          platform_type,
          ad_type: tabFlag.value === 0 ? 3 : 2

        },
        teamId,
      ).then((res) => {
        const resData = res.data.data.list.find((res) => res.id === item.id);
        if (!resData) {
          getSpaceselectlist();

          MessagePlugin.warning(t("ad.ggwyjy"));
          return;
        } else {
          imgIndex.value = index;
          activeTab.value = 1;
          imgItem.value = item;
          console.log(item, "itemmmm");
          if (priceCalendarRef.value) {
            priceCalendarRef.value.getAllPrice();
          }
        }
      });
    }
  };
</script>
<style lang="less" scoped>
  .addAdvertSing {
    background: url("@/assets/member/bg_big.svg") no-repeat 100%;
    background-size: cover;
    background-position: center;

    .h528 {
      height: 528px;

    }

    .h688 {
      /* height: 688px; */
      height: calc(100vh - 148px);
    }

    .tab-item {
      display: flex;
      align-items: center;
    }

    .tab-box {
      display: flex;
      align-items: center;

      .flex-box {
        display: flex;
        align-items: center;
      }

      .active {
        .num {
          color: #fff;
          background: #4d5eff;
        }

        .num-text {
          color: #4d5eff;
        }
      }

      .num {
        width: 28px;
        height: 28px;
        line-height: 28px;
        background: #f5f8fe;
        border-radius: 50%;
        color: #828da5;
        color: var(--text-kyy_color_text_3, #828da5);
        text-align: center;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        margin-right: 8px;
      }

      .num-text {
        color: var(--text-kyy_color_text_2, #516082);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        /* 150% */
      }

      .lin {
        display: flex;
        width: 112px;
        height: 1px;
        margin: 0 16px;
        flex-direction: column;
        align-items: flex-start;
        background: #d5dbe4;
      }
    }

    .banner {
      /* padding: 0 16px; */
      width: 100%;
    }

    .content3 {
      padding: 9px 20px 0;
      height: calc(100vh - 205px);
      overflow-y: auto;
      overflow-x: hidden;
      border-radius: 8px 8px 0 0;
      border: 1px solid var(--border-kyy_color_border_white, #fff);
      background: var(--bg-kyy_color_bg_light, #fff);
    }

    .tab-box {
      margin: 16px auto;
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      /* 150% */
    }

    .header-tab {
      width: 1184px;
      border-radius: 8px;
      border: 1px solid var(--border-kyy_color_border_white, #fff);
      background: rgba(255, 255, 255, 0.5);
      backdrop-filter: blur(2px);
      display: flex;
      padding: 16px 0px;
      justify-content: center;
      align-items: center;
      margin: 16px auto 16px;
      height: 60px;
    }

    .div-img {
      padding: 16px;
      border: 1px solid transparent;

      margin-bottom: 4px;
    }

    .lable-active {
      color: var(--text-kyy_color_text_3, #828da5);

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      margin-bottom: 4px;
    }

    .footers {
      display: flex;
      align-items: center;
      gap: 12px;
      padding-top: 12px;
    }

    .value-active {
      color: var(--text-kyy_color_text_1, #1a2139);

      font-size: 14px;
      font-style: normal;
      margin-bottom: 12px;
      font-weight: 400;
      line-height: 22px;

      /* 157.143% */
      .mx {
        color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
        text-align: center;
        font-size: 14px;
        font-style: normal;
        cursor: pointer;
        margin-left: 12px;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }
    }

    .tab-activeTab0 {
      height: 36px;
    padding: 4px;
    width: 332px;
      display: flex;
      position: absolute;
      top: 16px;
      align-items: center;
      border-radius: 4px;
      background: var(--bg-kyy_color_bg_light, #fff);
      justify-content: center;
    }

    .tab-activeTab0-btn {
      display: flex;
      height: 100%;
      padding: 3px 12px;
      cursor: pointer;
      justify-content: center;
      border-radius: 4px;

      align-items: center;
      color: var(--text-kyy_color_text_1, #1a2139);
      font-size: 14px;
      font-style: normal;
      width: 110px;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .tab-activeTab0-btn:hover {
      background: #eaecff;
    }

    .activeBtn {
      border-radius: 4px;
      color: #fff;
      background: var(--brand-kyy_color_brand_default, #4d5eff) !important;
    }

    .lastsue {
      display: flex;
      padding: 48px;
      width: 528px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin: 55px auto;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_light, #fff);

      img {
        width: 48px;
        height: 48px;
        margin: 0 auto 16px;
      }

      .labsec {
        color: var(--text-kyy_color_text_1, #1a2139);
        font-size: 14px;
        font-style: normal;
        margin-bottom: 12px;
        font-weight: 400;
        line-height: 22px;
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
      }

      .addsec {
        color: var(--text-kyy_color_text_1, #1a2139);
        text-align: center;
        width: 100%;
        margin-bottom: 24px;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        /* 157.143% */
      }
    }

    .content-box-activeTab1 {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      margin: 0 auto;
      gap: 16px;

      .footer-box {
        display: flex;
        width: 912px;
        padding: 12px 24px;
        align-items: flex-start;
        gap: 8px;
        background: #fff;
        border-top: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
        border-radius: 0 0 8px 8px;
      }

      .left-box {
        width: 256px;
        overflow: auto;
        border-radius: 8px;
        border: 1px solid var(--border-kyy_color_border_white, #fff);
        padding: 16px 0 12px;
        background-repeat: no-repeat;
        background: url('@/assets/member/left_bg.png') no-repeat 100% 100%;
        background-size: 100%;
        background-color: #fff;

        .div-img:hover {
          border: none !important;
        }

        .div-img {
          padding: 0 16px !important;
          border: none !important;
          margin-bottom: 12px;
        }

        .img0 {
          width: 100%;
          height: 100%;
        }

        .activeTab1-info {
          display: flex;
          padding: 0 12px;
          flex-direction: column;
          align-items: flex-start;
          align-self: stretch;
          border-top: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
          padding-top: 8px;
        }

        .activeTab1-title {
          color: #1a2139;
          font-size: 14px;
          padding: 0 12px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px;
          /* 157.143% */
          margin-bottom: 12px;
        }
      }


    }

    .img0 {
      width: 226px;
      height: 189px;
    }

    .tfbtn {
      min-width: 242px;
      margin: 0 auto 12px;
      display: block;
    }

    .div-img:hover {
      border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
      border-radius: 8px;
    }

    .text-price {
      color: var(--error-kyy_color_error_default, #d54941);
      text-overflow: ellipsis;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      display: flex;
      padding-left: 8px;
      align-items: center;
      line-height: 24px;
      /* 150% */
      margin-bottom: 20px;
      display: flex;
      align-items: baseline;
    }

    .text0 {
      color: #1a2139;
      font-size: 14px;
      padding-left: 8px;
      font-style: normal;
      width: 242px;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }

    .content-box-activeTab0 {
      min-height: 525px;
      border-radius: 8px;
      border: 1px solid var(--border-kyy_color_border_white, #fff);
      background: url("@/assets/member/content_bg.svg") no-repeat 100%;

      backdrop-filter: blur(2px);
      /* height: 328px; */
      display: flex;
      padding: 16px 20px;
      align-items: center;
      width: 1184px;
      flex-wrap: wrap;
      margin: 0 auto;
      gap: 16px;

      .content-activeTab0 {
        padding: 8px;
        width: 274px;
        border-radius: 8px;
        position: relative;
        background: var(--bg-kyy_color_bg_light, #fff);
      }
    }

    .min-height432px {
      min-height: 432px !important;
    }
  }
</style>
