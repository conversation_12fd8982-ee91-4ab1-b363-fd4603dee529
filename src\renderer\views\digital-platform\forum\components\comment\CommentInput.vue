<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
import to from 'await-to-js';
import { MessagePlugin } from 'tdesign-vue-next';
import { onClickOutside, useElementVisibility } from '@vueuse/core';
import { AxiosError, AxiosResponse } from 'axios';
import { addComment } from '@renderer/api/forum/comment';
import pick from 'lodash/pick';
import Avatar from '@/components/kyy-avatar/index.vue';
import EmojiSelector from '@/views/square/components/EmojiSelector.vue';
import { DATE_TIME_FORMAT, formatDateTime } from '@/utils/date';
import { useForumStore } from '../../store';
import { checkPerms } from '../../utils/business';
import UserSelect from '../UserSelect.vue';
import MentionEditor from '../mentionEditor/editor.vue';
import { Flag } from '@renderer/api/forum/models/post';

const props = defineProps<{
  ownerId?: string;
  // 资源id（与资源类型匹配的id）
  resourceId: string;
  // 资源类型
  // resourceType: ResourceType;
  // 是否聚焦输入框
  focus?: boolean;
  // 是否有回复评论被点击（处理评论状态）
  hasReplies?: boolean;
  // 输入框栏位于顶部
  top?: boolean;
  isScrolling?: boolean;
}>();

interface Emits {
  (e: 'success', payload: any): void;
  (e: 'visible', payload: boolean): void;
}
const emit = defineEmits<Emits>();

const forumStore = useForumStore();

const topComment = ref();

const disabledMention = ref(false);

const mentionEditor = ref<InstanceType<typeof MentionEditor>>(null);
// 选表情
// const { onSelectEmoji } = useEmoji(inputRef, formData.post);
const onSelectEmoji = (e) => {
  mentionEditor.value.insertNode(e.i);
};
const emojiVisible = ref(false);
const mentionPop = (event) => {
  // 获取鼠标位置
  let mouseY = event.clientY - 30;
  // let mouseX = event.clientX;
  mouseY = mouseY > 360 ? 360 : mouseY < 0 ? 20 : mouseY;
  mentionEditor.value.showMentionModal(false, { x: 130, y: mouseY });
};

const hasMultiCards = computed(() => forumStore.cardList.length > 1);

// 当前评论的所有者（发布评论时使用）
forumStore.postOwner = { ...forumStore.currCard };

const userChange = (val) => {
  forumStore.postOwner = val;
};

// 匿名评论处理

watch(
  [() => forumStore.postOwner, mentionEditor],
  ([owner]) => {
    if (owner.flag === Flag.Anonymous) {
      disabledMention.value = true;
      mentionEditor?.value?.clearMentions();
    } else {
      disabledMention.value = false;
    }

    nextTick(() => {
      mentionEditor?.value?.focus();
      isFocus.value = true;
    });
  },
  {
    immediate: true,
  },
);

// 发布评论
const submitting = ref(false);
const submit = async () => {
  if (submitting.value) return;

  const perms = await checkPerms();
  if (perms && perms.canComment !== true) {
    await MessagePlugin.warning('你暂时无法评论，请联系管理员');
    return;
  }

  submitting.value = true;
  const textData = mentionEditor.value?.getContents();
  const at = textData[0]?.atInfo;
  const text = textData[0]?.text;
  const [err, res] = await to<AxiosResponse, AxiosError<{ reason: string }>>(
    addComment({
      comment: {
        content: text,
        resourceId: props.resourceId,
        ownerId: forumStore.postOwner.id,
        // ipRegion: ipInfo.value ? JSON.stringify(ipInfo.value) : '',
        at,
      },
    }),
  );
  submitting.value = false;

  if (err) return;

  addLocalComment(res.data.data.commentId, text, res.data.data.comment?.comment?.at);
  mentionEditor.value?.clear();

  await MessagePlugin.success('评论成功！');
};

// 将当前评论的内容显示在最新一条中显示，如果回复他人的评论，则在该评论下方显示
const addLocalComment = (id, content, at = []) => {
  const commentItem = {
    owner: pick(forumStore.postOwner || {}, ['id', 'name', 'avatar']),
    comment: {
      id,
      content,
      // status: CommentStatus.Normal,
      // commentType: CommentType.Top,
      createdAt: formatDateTime(new Date(), DATE_TIME_FORMAT),
      // ipRegion: ipInfo.value?.province ? shortProvince(ipInfo.value?.province) : '',
      at,
    },
    likes: 0,
    liked: false,
    replies: 0,
    stickOnTop: false,
    isSelfPost: props.ownerId === forumStore.postOwner?.id,
    isSelfComment: true,
  };
  emit('success', commentItem);
};
const hasTextContent = ref(false);
const disableChange = (data) => {
  hasTextContent.value = data;
};
onMounted(() => {
  setTimeout(async () => {
    await nextTick();
    if (props.focus) {
      mentionEditor.value?.focus();
    }
  }, 600);
});

// 自动聚焦输入框
watch(
  () => props.focus,
  async (val) => {
    if (!val) return;

    await nextTick();
    mentionEditor.value?.focus();
  },
);

// 下滑时输入框置于底部
const topPlaceholder = ref(null);
const topVisible = useElementVisibility(topPlaceholder);

// 输入框聚焦时可发布
const isFocus = ref(true);
const inputFocus = () => {
  isFocus.value = true;
};

watch(topVisible, (val) => {
  if (!props.top) return;

  emit('visible', val);
  // if (val) isFocus.value = false;
});

// 点击输入栏之外收起操作
onClickOutside(topComment, () => {
  if (emojiVisible.value) return;
  isFocus.value = false;
});

watch(
  () => props.isScrolling,
  (val) => {
    if (val) {
      emojiVisible.value = false;
    }
  },
);
</script>

<template>
  <div v-if="top" ref="topPlaceholder" class="h-0">&nbsp;</div>

  <div ref="topComment" :class="['top-comment', { sticky: !top && !hasReplies }]">
    <UserSelect
      isolation
      placement="bottom-left"
      :disabled="!hasMultiCards"
      :default-value="forumStore.postOwner"
      :class="['inline-flex!', { cursor: hasMultiCards }]"
      @change="userChange"
    >
      <div class="avatar-wrap">
        <Avatar
          avatar-size="32px"
          :image-url="forumStore.postOwner?.avatar ?? ''"
          :user-name="forumStore.postOwner?.name"
          round-radius
        />
        <iconpark-icon v-if="hasMultiCards" name="iconstanding" class="icon" />
      </div>
    </UserSelect>

    <div class="right-content">
      <MentionEditor
        ref="mentionEditor"
        @click="inputFocus"
        @disable-change="disableChange"
        placeholder="写评论"
        :offset="{ left: 80, top: 80 }"
        :maxLength="140"
        :disabled-mention="disabledMention"
      />

      <!-- <t-textarea
        ref="inputRef"
        v-model="formData.text"
        :autofocus="focus"
        size="large"
        :placeholder="$t('square.post.writeComment')"
        :maxlength="140"
        :autosize="{ minRows: 1, maxRows: 4 }"
        class="comment-input"
        @click="inputFocus"
        @focus="inputFocus"
        @change="inputFocus"
      /> -->

      <div :class="['action', { 'hidden!': !isFocus }]">
        <div class="left-tool">
          <EmojiSelector
            v-model="emojiVisible"
            placement="right-bottom"
            class="emoji"
            :popper-props="{
              modifiers: [
                {
                  name: 'flip',
                  options: {
                    flipVariations: false,
                  },
                },
              ],
            }"
            @select="onSelectEmoji"
          />
          <template v-if="!disabledMention">
            <t-tooltip content="@" class="item">
              <span @click="mentionPop">
                <img src="@renderer/assets/digital/svg/icon24_@.svg" class="w-24 h-24" />
              </span>
            </t-tooltip>
          </template>
        </div>
        <t-button theme="primary" :disabled="!hasTextContent" :loading="submitting" class="w-88 h-28" @click="submit">
          {{ $t('square.post.comment') }}
        </t-button>
      </div>
    </div>
  </div>

  <t-divider class="my-0!" />
</template>

<style scoped lang="less">
.top-comment {
  display: flex;
  padding: 12px 0;
  align-items: flex-start;
  gap: 12px;
  background: #fff;
  transition: all 0.6s cubic-bezier(0.25, 1, 0.5, 1);
  &.sticky {
    order: 1;
    position: sticky;
    bottom: 0;
    z-index: 99;
    border-top: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    margin-top: -1px;
  }
}

.avatar-wrap {
  position: relative;
  .icon {
    position: absolute;
    right: -6px;
    bottom: -6px;
    border-radius: 66px;
    border: 1px solid var(--border-kyy_color_border_white, #fff);
    background: var(--bg-kyy_color_bg_deepest, #eceff5);
    z-index: 1;
  }
}

.right-content {
  flex: 1;
  .action {
    /* @apply mt-12 flex flex-justify-between; */
    margin-top: 12px;
    display: flex;
    justify-content: space-between;
    transition: all 0.6s cubic-bezier(0.25, 1, 0.5, 1);
  }
  :deep(.chat-rich-editor) {
    padding: 4px 8px;
    border-radius: 8px;

    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
    .w-e-max-length-info {
      bottom: 0px;
    }
    .w-e-text-container .w-e-scroll {
      min-height: 24px;
      max-height: 70px;
    }
  }
  .left-tool {
    display: flex;
    align-items: start;
    justify-content: center;
    gap: 12px;
  }
}

.comment-input :deep(.t-textarea__inner) {
  border: none;
  border-radius: 8px;
  background: var(--bg-kyy-color-bg-deep, #f5f8fe);
}

.comment-input :deep(.t-textarea__info_wrapper) {
  display: none;
}

:deep(.emoji .icon) {
  font-size: 24px;
}
</style>
