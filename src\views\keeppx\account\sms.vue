<template>
  <home className="containerPc">
    <template #content>
      <div class="content">
        <div class="logo">
          <img v-if="smsInfo.logo" :src="smsInfo.logo" alt="">
          <img v-else src="@/assets/svg/building-fill.svg" alt="">
        </div>
        <div class="text name">
          {{ t('account.hai') }}{{ smsInfo?.name }}
        </div>
        <div class="text text-left">
          {{ t('account.sms1') }} <span style="font-weight: 700">{{ smsInfo?.team }}</span> {{ t('account.sms2') }}
        </div>
        <div class="btn-group">
          <div class="login">
            <t-button class="btn" @click="agree">{{ t('account.btnJoin') }}</t-button>
          </div>
          <div class="register">
            <t-button class="btn" variant="outline" theme="danger" @click="showRefuse">{{ t('account.btnRefuse') }}</t-button>
          </div>
        </div>
      </div>

      <t-dialog v-model:visible="refuseVisible" theme="info" :header="t('account.tip')" width="300">
        <template #body>
          <div>
            <div class="tip mb24">{{ t('account.smsTip') }}</div>
          </div>
        </template>
        <template #footer>
          <div style="margin-right: 8px">
            <t-button
              class="btn cancel"
              variant="outline"
              theme="default"
              @click="refuseVisible = false"
            >{{ t('account.cancel') }}</t-button>
            <t-button
              class="btn confirm"
              theme="danger"
              variant="base"
              @click="refuse"
            >{{ t('account.confirmRefuse') }}</t-button>
          </div>
        </template>
      </t-dialog>

      <t-dialog v-model:visible="visible"  :header="false" :closeBtn="false" :footer="false" width="300" :closeOnEscKeydown="false" :closeOnOverlayClick="false">
        <template #body>
          <div class="f-c-c">
            <div>
              <img src="@/assets/svg/annulus_error.svg" alt="">
            </div>
            <div class="refuse-text">{{ t('account.refused') }}</div>
            <div class="tip">{{ t('account.smsTip1') }} “{{ smsInfo?.team }}” {{ t('account.smsTip2') }}</div>
          </div>
        </template>
      </t-dialog>
    </template>
  </home>
</template>

<script setup lang="ts">
import home from '../homeIndex.vue';
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { smsRefuse, uuidRefuse } from '@/api/account/login';
import { useI18n } from 'vue-i18n';
import { useAccountStore } from '@/stores/account';
import {showToast} from "vant";
import {MessagePlugin} from "tdesign-vue-next";
const accountStore = useAccountStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();

const refuseVisible = ref(false);
const visible = ref(false);
const smsInfo:any = ref(accountStore.$state.smsInfo);
let smsid:any;

onMounted(() => {
  smsid = route.query?.id || 0;
});

const agree = () => {
  if (['member', 'government', 'cbd', 'association','uni'].includes(route.query?.app)) {
    router.push({name: 'accountJoin', query: {...route.query}});
    return;
  } else if (['staff'].includes(route.query?.app)) {
    router.push({name: 'accountJoin', query: {sms: smsid, isSms: route.query.isSms}});
    return;
  }
  router.push({name: 'accountJoin', query: {sms: smsid, isSms: route.query.isSms}})
};

const refuse = () => {
  if (['member', 'government', 'cbd', 'association','uni'].includes(route.query?.app)) {
    const params = {
      app: route.query.app,
      uuid: route.query.uuid
    };
    uuidRefuse(params).then((res:any) => {
      refuseVisible.value = false;
      visible.value = true;
    }).catch((err:any) => {
      console.log(err,err.response.data.code, '内容')
      const code = err.response.data.code;
      if (code === -1) {
        MessagePlugin.error({
          content: err.response.data.message,
          duration: 3000,
        })
      }
    })
    return;
  }
  smsRefuse({id:smsid}).then((res:any) => {
    refuseVisible.value = false;
    visible.value = true;
  }).catch((err:any) => {
    console.log(err,err.response.data.code, '内容')
    // 待审核：10016，已加入：10017，已拒绝：10018
    const code = err.response.data.code;
    if (code === -1) {
      // showToast({position:'bottom',message:err.response.data.message,className:'pzy-toast',zIndex: 10000000000000});
      MessagePlugin.error({
        content: err.response.data.message,
        duration: 3000,
      })
    }
  }).finally(() => {

  })
};

const showRefuse = () => {
  refuseVisible.value = true;
};
</script>

<style lang="less" scoped>
@import url('../css/base.less');
.f-c-c {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.content {
  // max-width: 600px;
  // min-height: 100vh;
  // margin: auto;
  // background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  .logo {
    width: 64px;
    height: 64px;
    font-size: 0;
    margin-top: 24px;
    img{
      width:100%;
      height:100%;
    }
  }
  .name {
    margin: 16px 0 8px;
  }
  .text {
    width: 320px;
    font-size: 14px;
    font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;
    text-align: center;
    color: #13161b;
    line-height: 22px;
  }
  .text-left {
    text-align: left;
  }
  .btn-group {
    width: 320px;
    margin-top: 32px;

    .btn {
      width:100%;
      height:40px;
      font-size: 16px;
      font-family: Microsoft YaHei, Microsoft YaHei-Regular;
      font-weight: 400;
      text-align: center;
      line-height: 24px;
    }
    .register {
      margin-top: 24px;
    }
  }
}
.tip {
  font-size: 14px;
  font-family: Microsoft YaHei, Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: left;
  color: #717376;
  line-height: 22px;
}
.refuse-text {
  font-size: 16px;
  font-family: Microsoft YaHei, Microsoft YaHei-Bold;
  font-weight: 700;
  text-align: center;
  color: #13161b;
  line-height: 24px;
  margin: 8px 0 4px;
}
</style>
