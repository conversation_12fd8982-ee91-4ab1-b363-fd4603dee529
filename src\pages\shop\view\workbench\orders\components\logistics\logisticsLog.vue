<template>
  <t-drawer
    :close-btn="true"
    :close-on-overlay-click="true"
    :visible="visible"
    :header="'物流详情'"
    :on-close="onClose"
    :footer="null"
    size="472px"
    class="drawer-containerlog"
  >
    <template #closeBtn>
      <iconpark-icon name="iconerror" class="iconorientation" />
    </template>
    <div class="logbox" :class="errorInfo && !loading ? 'emcent' : ''">
      <div v-if="!loading && !errorInfo" class="header">
        <div class="black" @click="copyRun">
          <span>{{ logData.comName }}： {{ logData.nu }}</span>
          <iconpark-icon name="iconcopy" style="font-size: 20px; color: #828da5" />
        </div>
      </div>
      <div class="listbox">
        <section v-if="loading" class="t-skeleton-demo-card">
          <div class="content">
            <t-skeleton animation="gradient" :row-col="rowCol1"></t-skeleton>
            <t-skeleton animation="gradient" :row-col="rowCol"></t-skeleton>
            <t-skeleton animation="gradient" :row-col="rowCol"></t-skeleton>
          </div>
        </section>

        <REmpty v-if="errorInfo && !loading" :name="'no-data'" :tip="'查询不到相关物流信息'" />

        <div v-for="(item, index) in logData.data" :key="item.time" class="item">
          <div class="left">
            <div class="left-icon">
              <iconpark-icon v-if="item.status === 3" name="iconsuccess-g4ljdnnb" style="font-size: 20px" />
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                <circle cx="6" cy="6" r="6" fill="#D5DBE4" />
              </svg>
            </div>
            <div v-if="index !== logData.data?.length - 1" class="left-line"></div>
          </div>
          <div class="right">
            <div class="title" :class="index === 0 ? '' : 'old-c'">{{ item.status }}</div>
            <div class="time">{{ item.ftime }}</div>
            <div :class="index === 0 ? 'detail' : 'detail old-c'">
              {{ item.context }}
            </div>
            <div v-if="index !== logData.data?.length - 1" class="right-line"></div>
          </div>
        </div>
      </div>
    </div>

    <div style="display: flex; justify-content: end; padding: 16px 0">
      <t-tooltip v-if="showTelFlag && telNum" theme="light" trigger="click">
        <template #content>
          <div class="tooltip-title">联系商家</div>
          <div class="flex tooltip-phone" @click="copyRunNum(telNum)">
            电话：{{ telNum }}
            <iconpark-icon name="iconcopy" style="font-size: 20px; margin-left: 4px; color: #828da5" />
          </div>
        </template>
        <t-button theme="default" variant="outline" style="width: 88px; font-weight: 600">联系商家</t-button>
      </t-tooltip>
    </div>
  </t-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import useClipboard from 'vue-clipboard3';
import { MessagePlugin } from 'tdesign-vue-next';
import { queryLogisticsOrder, sellerOrderDetail, businessOrderDetail } from '../../apis';
import { REmpty } from '@rk/unitPark';
const _props = defineProps({
  showTelFlag: {
    type: Boolean,
    default: false,
  },
});
const { toClipboard } = useClipboard();
const visible = ref(false);
const errorInfo = ref(false);
const logData = ref([]);
const onClose = () => {
  visible.value = false;
};
const telNum = ref('');
const copyRunNum = async (val: any) => {
  try {
    await toClipboard(val);
    MessagePlugin.success({
      content: '复制成功',
      zIndex: *********,
    });
  } catch (e) {
    MessagePlugin.error({
      content: '复制失败',
      zIndex: *********,
    });
  }
};

const show = async (sn, personal = false, teamId?) => {
  visible.value = true;
  loading.value = true;
  logData.value = [];
  try {
    let res = null;
    if (personal) {
      res = await businessOrderDetail(sn, teamId);
    } else {
      res = await sellerOrderDetail(sn, teamId);
    }
    const data = res.data;
    const params = {
      logisticsCompanyCode: data.logistic_company_code,
      logisticsOrderId: data.logistic_sn,
      receiverPhone: data?.logistic_address?.phone?.number,
      storeId: Number(data.store_id),
    };

    const qres = await queryLogisticsOrder(params);
    telNum.value = qres.data.merchantPhone;
    if (!qres.data.success) {
      loading.value = false;
      errorInfo.value = true;

      // MessagePlugin.error(qres.data.msg);
      return;
    }
    console.log(qres, 'qresqresqres');
    logData.value = qres.data.resp;
    loading.value = false;
    errorInfo.value = false;
  } catch (error) {
    console.log(error, 'errorerrorerror');
    loading.value = false;
    errorInfo.value = true;
    // MessagePlugin.error(error.response?.data?.message || '获取物流信息失败');
  }
};

const copyRun = async () => {
  try {
    const val = `${logData.value.comName}： ${logData.value.nu}`;
    await toClipboard(val);
    MessagePlugin.success({
      content: '复制成功',
      zIndex: *********,
    });
  } catch (e) {
    MessagePlugin.error({
      content: '复制失败',
      zIndex: *********,
    });
  }
};
const loading = ref(false);
const rowCol1 = [[{ type: 'rect', margin: '0 0 24px 0px', width: '224px', height: '24px' }]];
const rowCol = [
  [
    { type: 'circle', size: '24px' },
    { type: 'rect', margin: '0 0 4px 0 ', width: '124px', height: '24px' },
  ],
  [
    { type: 'rect', margin: '0 0 0 10px ', width: '2px', height: '34px' },
    { type: 'rect', margin: '0 0 0 28px ', width: '256px', height: '24px' },
  ],
  [
    { type: 'rect', margin: '0 0 0 10px ', width: '2px', height: '34px' },
    { type: 'rect', margin: '0 0 0 28px ', width: '392px', height: '24px' },
  ],
];
defineExpose({
  show,
});
</script>

<style lang="less">
.drawer-containerlog {
  .t-drawer__close-btn {
    background-color: transparent;
    right: 24px;
  }
  .t-drawer__close-btn:hover {
    background-color: transparent;
  }
  .t-drawer__content-wrapper {
    background: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bg_all.png');
    background-size: 100% 100%;
  }
  .t-drawer__header {
    padding: 0 24px;
  }
  .t-drawer__body {
    padding: 0 12px;
    padding-right: 6px;
  }
}
.tooltip-title {
  color: var(--kyy_color_popcomfirm_title, #1a2139);

  /* kyy_fontSize_3/bold */
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
}
.tooltip-phone {
  color: var(--kyy_color_popcomfirm_content, #516082);

  /* kyy_fontSize_2/regular */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  margin-right: 12px;
}
.logbox {
  display: flex;
  padding: 0px 12px 12px 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  align-self: stretch;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #fff);
  min-height: 560px;
  .header {
    padding: 8px 0px;
    .black {
      padding: 4px;
      display: flex;
      align-items: center;
      gap: 4px;
      border-radius: 4px;
      width: fit-content;
      color: var(--text-kyy_color_text_2, #516082);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
    width: 100%;
    cursor: pointer;
    border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  }
  .black:hover {
    background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
  }
  .listbox {
    .item {
      display: flex;
      min-height: 50px;
      align-items: flex-start;
      gap: 8px;
      align-self: stretch;
      .left {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        align-self: stretch;
        .left-icon {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .left-line {
          width: 1px;
          height: 100%;
          border-left: 1px dashed #d5dbe4;
        }
      }
      .right {
        flex: 1;
        .title {
          color: var(--text-kyy_color_text_1, #1a2139);

          /* kyy_fontSize_3/bold */
          font-family: 'PingFang SC';
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px; /* 150% */
        }
        .time {
          color: var(--text-kyy_color_text_3, #828da5);

          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
        .detail {
          color: var(--text-kyy_color_text_1, #1a2139);

          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
      }
    }
  }
}
.old-c {
  color: #516082 !important;
}
.right-line {
  border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  width: 100%;
  margin: 12px 0;
}
.t-skeleton-demo-card {
  margin-top: 16px;
}
.emcent {
  justify-content: center;
  align-items: center;
}
.iconorientation {
  font-size: 24px;
  color: #516082;
}
</style>
