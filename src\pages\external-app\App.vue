<script setup lang="ts">
import { setAccesstoken } from '@axios/auth';
import sdk from '@lynker-desktop/web';
import { Utils } from '@utils/index';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { RTricks } from '@rk/unitPark';
import LoadingWrapper from '@components/common/LoadingWrapper.vue';
const route = useRoute();
const loading = ref(true);
const init = async () => {
  loading.value = true;
  try {
    if (sdk.isDesktop) {
      const config = await sdk.getConfig();
      Utils.config = config;
      // @ts-ignore
      window.LynkerSDK = window.LynkerSDK || {};
      // @ts-ignore
      window.LynkerSDK.config = config;
      console.log(Utils.config, 'sdksdksdk');
    } else {
      if (route.query.token) {
        Utils.config.token = route.query.token as string;
      }
    }
    Utils.config.token && setAccesstoken(Utils.config.token);
    // @ts-ignore
    window.rkUtils = Utils;
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  setTimeout(() => {
    init();
  }, 100);
});
</script>

<template>
  <div id="app-box">
    <template v-if="!loading">
      <router-view />
      <RTricks uuid="外部应用-首页" token="" :offset="{ x: -32, y: -32 }" />
    </template>
    <template v-else>
      <LoadingWrapper />
    </template>
  </div>
</template>

<style lang="less">
body {
  margin: 0;
}
#app-box {
  width: 100%;
  height: 100%;
  min-height: 100vh;
}
</style>
