<template>
  <div class="editor-box" @click.stop="unitVisible = false">
    <div class="edit-content">
      <div class="niche-form-box form-box">
        <div class="box-one-fff" style="">
          <div v-if="alertShow" class="alert">
            <iconpark-icon name="iconinfo-b7dcijg4" class="iconerror" />
            <div class="text">
              {{ t('niche.safetip') }}<a @click="toKefu">{{ t('niche.set') }}</a>
            </div>
            <iconpark-icon name="iconerror" class="iconerror iconc" @click="alertClick" />
          </div>
          <div class="step-header">
            <div class="one">
              <svg v-if="formStep === 1" xmlns="http://www.w3.org/2000/svg" width="416" height="66" viewBox="0 0 418 66"
                fill="none">
                <path d="M417 33L393.629 1H5C2.79086 1 1 2.79086 1 5V61C1 63.2091 2.79087 65 5.00001 65H393.629L417 33Z"
                  fill="#4D5EFF" stroke="#4D5EFF" stroke-linejoin="round" />
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="416" height="66" viewBox="0 0 418 66" fill="none">
                <path d="M417 33L393.629 1H5C2.79086 1 1 2.79086 1 5V61C1 63.2091 2.79087 65 5.00001 65H393.629L417 33Z"
                  fill="white" stroke="#D5DBE4" stroke-linejoin="round" />
              </svg>
              <div class="con">
                <div class="index" :class="formStep === 1 ? 'index-active' : ''">01</div>
                <div class="title" :class="formStep === 1 ? 'title-active' : ''">
                  <p style="font-weight: 600">{{ t('niche.re_niche') }}</p>
                  <p>{{ t('niche.basic') }}</p>
                </div>
              </div>
            </div>
            <div class="one" style="left: -8px">
              <svg v-if="formStep === 1" xmlns="http://www.w3.org/2000/svg" width="416" height="66" viewBox="0 0 418 66"
                fill="none">
                <path d="M1 65H413C415.209 65 417 63.2091 417 61V5C417 2.79086 415.209 1 413 1H1L25.1158 33L1 65Z"
                  fill="white" stroke="#D5DBE4" stroke-linejoin="round" />
              </svg>

              <svg v-else xmlns="http://www.w3.org/2000/svg" width="416" height="66" viewBox="0 0 418 66" fill="none">
                <path d="M1 65H413C415.209 65 417 63.2091 417 61V5C417 2.79086 415.209 1 413 1H1L25.1158 33L1 65Z"
                  fill="#4D5EFF" stroke="#4D5EFF" stroke-linejoin="round" />
              </svg>
              <div class="con conbg" style="padding-left: 44px">
                <div class="index" :class="formStep === 2 ? 'index-active' : ''">02</div>
                <div class="title" :class="formStep === 2 ? 'title-active' : ''">
                  <p style="font-weight: 600">{{ t('niche.res_ch') }}</p>
                  <p>{{ t('niche.sechan') }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-show="formStep === 1" class="form-one">
          <div class="from niche-edit-form">
            <t-form ref="form" :rules="FORM_RULES" :data="formData" :colon="false" scroll-to-first-error="smooth"
              label-align="top" @submit="onSubmit">
              <div class="fbox1">
                <div class="title-tag" style="margin-bottom: 4px">
                  <div class="title-text">基本信息</div>
                </div>

                <div class="type-re">
                  <div v-if="formData.id && formData.type === 2" class="mock-type-btn btn-dis">
                    {{ t('niche.gy') }}
                  </div>
                  <div v-else :class="formData.type === 1 ? 'mock-type-btn-a2' : 'mock-type-btn'"
                    @click="changeType(1)">
                    {{ t('niche.gy') }}
                  </div>
                  <div v-if="formData.id && formData.type === 1" class="mock-type-btn btn-dis">
                    {{ t('niche.xq') }}
                  </div>
                  <div v-else :class="formData.type === 2 ? 'mock-type-btn-a' : 'mock-type-btn'" @click="changeType(2)">
                    {{ t('niche.xq') }}
                  </div>
                </div>

                <t-form-item :key="formData.classify_id + 'classify_id'" :label="t('niche.hyfl2')" name="classify_id">
                  <t-cascader v-model="formData.classify_id" :options="classifytree"
                    :keys="{ label: 'name', value: 'id', children: 'children' }" clearable trigger="hover"
                    :placeholder="t('niche.cla_tip')" @change="cascaderChange" />
                </t-form-item>

                <template v-if="formData.type === 1">
                  <t-form-item :label="'关联外部链接'" name="link"
                    :style="{ marginBottom: formData.is_external_link ? '8px' : '32px' }">
                    <t-switch :disabled="dischange" v-model="formData.is_external_link" @change="changeLink" />
                    <span style="margin-left: 8px">开启可填写淘宝、京东、美团等外部平台的商品链接</span>
                  </t-form-item>
                  <div class="linkboxs" :style="{display: formData.is_external_link ? 'flex' : 'none'}">
                    <div class="fb">
                      <t-form-item v-if="!formData.is_external_link_same" :label="''" name="external_app_link"
                        style="margin-bottom: 12px" :requiredMark="false" :rules="formData.is_external_link ? [
                          {
                            required: true,
                            message: '请输入在手机APP打开的商品链接',
                            trigger: 'blur',
                          },
                          {
                             pattern: /^(https?|http):\/\/[^\s/$.?#].[^\s]*$/,trigger: 'blur', message: '请输入正确的商品链接'
                          },
                        ]: null">
                        <t-textarea v-model="formData.external_app_link" placeholder="请输入在手机APP打开的商品链接"
                          name="external_app_link" style="width: 592px" :autosize="{ minRows: 1, maxRows: 5 }" clearable
                          :maxlength="500" />
                      </t-form-item>
                      <t-form-item :label="''" name="external_pc_link" :requiredMark="false" :rules="formData.is_external_link ? [
                          {
                            required: true,
                            message: formData.is_external_link_same ? '请输入商品链接' : '请输入在电脑浏览器打开的商品链接',
                            trigger: 'blur',
                          },
                        {
                             pattern: /^(https?|http):\/\/[^\s/$.?#].[^\s]*$/,trigger: 'blur', message: '请输入正确的商品链接'
                          },
                        ] : null">
                        <t-textarea v-model="formData.external_pc_link" style="width: 592px"
                          :placeholder="formData.is_external_link_same ? '请输入商品链接' : '请输入在电脑浏览器打开的商品链接'"
                          name="external_pc_link" :autosize="{ minRows: 1, maxRows: 5 }" clearable :maxlength="500" />
                      </t-form-item>
                    </div>
                    <div class="ckbox">
                      <t-checkbox v-model="formData.is_external_link_same">APP和电脑端共用一个链接</t-checkbox>
                    </div>
                  </div>
                </template>

                <t-form-item :label="t('niche.sp_name')" class="title" name="title">
                  <t-textarea v-model="formData.title" class="textareaPadd"
                    :class="{ textareaPadd: formData.title.length < 46 }" :maxlength="80"
                    :autosize="{ minRows: 1, maxRows: 2 }" show-limit-number clearable
                    :placeholder="t('niche.sp_name_in')" @blur="titleblur" />
                </t-form-item>
                <productImage ref="productImageRef" :attrs="attrsImage" :size-limit="{ size: 5, unit: 'MB' }"
                  :count-limit="formData.is_external_link ? 1 : 5" accept="image/png,image/jpg,image/jpeg"
                  @clear-validate="clearValidateRun" />
                <t-form-item v-if="formData.type === 1" :label="t('niche.spprice')" name="price" :rules="[
                    {
                      validator: priceValidator,
                      required: true,
                      trigger: 'change',
                    },
                  ]">
                  <div class="price-input">
                    <t-radio v-model="formData.custom" @change="customChange">{{ t('niche.zdyi') }}</t-radio>
                    <t-input-number v-model="formData.price" :min="0.01" :label="pricePX" theme="normal"
                      style="width: 408px" :max="999999" :format="(value) => formatValue(value)" :decimal-places="2"
                      :placeholder="t('niche.prtip')" />
                  </div>

                  <div class="radio-1">
                    <t-radio v-model="formData.discuss" @change="discussChange">{{ t('niche.priceface') }}</t-radio>
                  </div>
                </t-form-item>

                <t-form-item key="time" :label="t('niche.he_time2')" name="time" :rules="[
                    {
                      validator: timeValidator,
                      required: true,
                      trigger: 'change',
                    },
                  ]">
                  <div class="radio-1">
                    <t-radio-group v-model="formData.effective_unlimited" @chang="effective_unlimitedChange">
                      <t-radio :value="1">长期</t-radio>
                      <t-radio :value="0">自定义</t-radio>
                    </t-radio-group>
                  </div>

                  <div class="price-input" style="margin-left: -8px">
                    <t-date-range-picker v-model="formData.time" :key="formData.classify_id" style="width: 408px"
                      :placeholder="[t('approval.approval_data.start_time'), t('approval.approval_data.end_time')]"
                      clearable />
                  </div>
                </t-form-item>
              </div>

              <template v-if="formData.classify_id && !formData.is_external_link">
                <div class="fbox2">
                  <div class="title-tag" style="margin-bottom: 16px">
                    <div class="title-text">{{ t('niche.sjxx') }}</div>
                  </div>
                  <t-form-item :label="t('niche.sjmd')" class="title" name="selling_point" :rules="[
                      {
                        validator: selling_pointValidator,
                        required: true,
                        trigger: 'blur',
                      },
                    ]">
                    <div class="selling_point-box">
                      <div v-for="(item, index) in formData.selling_point" :key="'selling_point' + index"
                        class="selling_point">
                        <t-input v-model="item.content" :maxlength="19" show-limit-number style="width: 768px" clearable
                          :placeholder="t('niche.yjh')" />
                        <iconpark-icon
                          v-show="index === formData.selling_point?.length - 1 && formData.selling_point?.length < 3"
                          name="iconaddcircle" class="iconaddcircle" @click="selling_pointAdd" />
                        <iconpark-icon name="iconremove" class="iconremove" @click="selling_pointRemove(index)" />
                      </div>
                    </div>
                  </t-form-item>

                  <t-form-item v-if="formData.template_uuid === 'physical_products'" :label="t('niche.spspec')"
                    name="spec">
                    <t-input v-model="formData.spec" :maxlength="20" show-limit-number clearable
                      :placeholder="t('niche.spspectip')" />
                  </t-form-item>

                  <t-form-item :label="t('niche.lat')" name="address">
                    <div style="width: 100%">
                      <template v-if="!markerPosition.length || !markerPosition[0]">
                        <t-button type="button" style="margin-bottom: 8px" theme="primary" variant="outline"
                          @click="editAddress">
                          <iconpark-icon style="color: #4d5eff" name="iconorientation" class="iconorientation" />
                          {{ t('niche.lat_se') }}
                        </t-button>
                        <t-input v-show="false" v-model="formData.address" :maxlength="150" :disabled="true"
                          show-limit-number :placeholder="t('niche.lat_se2')" />
                      </template>
                      <div v-else>
                        <BaiduMapCard :location="{
                            ...location,
                            name: formData.address_name,
                            address: formData.address,
                          }" large @confirm="mapConfirm" />
                        <div style="margin-top: 8px" class="title">
                          <t-textarea v-model="formData.address" :maxlength="150" :placeholder="t('niche.adr')"
                            autosize />
                        </div>
                      </div>
                    </div>
                  </t-form-item>

                  <t-form-item v-if="formData.template_uuid === 'physical_products'">
                    <div class="quantity-box">
                      <div class="quantity-top">
                        <t-form-item :label="t('niche.sp_count')" class="quantitylabel" name="quantity">
                          <t-input-number v-model="formData.quantity" :min="0.0001" theme="normal" style="width: 408px"
                            :max="999999" :format="(value) => formatValueQu(value)" :decimal-places="4"
                            :placeholder="t('niche.sp_count_tip')" />
                        </t-form-item>
                        <t-form-item :key="ckdomRef" :label="t('niche.sp_unit')" class="unitlabel" name="unit_id">
                          <t-select v-model="formData.unit_id" v-replace-svg :options="filteredOptions"
                            style="width: 408px" :keys="{
                              label: 'name',
                              value: 'id',
                            }" :popup-props="{
                              attach: 'body',
                              overlayClassName: 'unitPopup',
                              placement: 'top-start',
                            }" :placeholder="t('niche.unitse')" @change="unitChange" @focus="unitFocus">
                            <template #panelTopContent>
                              <div>
                                <t-input v-model="unitKeyword" style="width: 100%" clearable
                                  :placeholder="t('niche.unitsh')" @change="unitKeywordInput" @focus="unitKeywordBlur">
                                  <template #prefix-icon>
                                    <iconpark-icon name="iconsearch" class="name-icon" />
                                  </template>
                                </t-input>
                              </div>
                              <div v-show="!unitKeying" style="width: 408px" class="panelUnit">
                                <t-tabs v-model="nowUnitClassId" :list="tabListData" @change="unitClassIdChange" />
                              </div>
                              <div v-show="unitKeying" class="unit-saerch-pan">
                                <div v-for="item in unitOptions" :key="item.id" class="item"
                                  @click="unitOptionClick(item)">
                                  <span v-html="item.name"></span>
                                </div>
                              </div>
                            </template>
                          </t-select>
                        </t-form-item>
                      </div>
                      <div class="quantity-btm">
                        <div class="hoit">
                          <div v-for="item in releaseUnitData" :key="item.id" class="item" @click="setUnit(item)">
                            {{ item.name }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </t-form-item>
                </div>

                <div class="fbox2">
                  <div class="title-tag">
                    <div class="title-text">商机介绍</div>
                  </div>
                  <t-form-item class="content" name="content" :rules="[
                      {
                        validator: contentValidator,
                        required: true,
                        trigger: 'change',
                      },
                    ]">
                    <template #label>
                      <span style="flex: 1">{{ t('niche.sjxq') }}</span>
                      <t-tooltip content="图片建议上传宽度896px或以上的尺寸">
                        <iconpark-icon name="iconhelp" class="icon"
                          style="font-size: 20px; color: #828da5; position: relative; top: 5px; left: 4px" />
                      </t-tooltip>
                    </template>

                    <Editor class="niche-lk-editors notice-lk-editors niche-no-seh" ref="editorRef" type="B"
                      :editor-type="'C'" :special="true" root-dir="niche"
                      :options="{ toolbar: ['annex'], height: 320, placeholder: t('niche.qsjxq') }"
                      @update="handleContentChange" @img-insert="imgInsert" />
                  </t-form-item>

                  <t-form-item class="content" name="attr" :requiredMark="false">
                    <template #label>
                      <span style="flex: 1; margin-right: 12px">商机属性</span>
                      <t-switch v-model="formData.attributeShow" @change="attrChange" />
                    </template>
                    <div v-if="formData.attributeShow" class="attr-box">
                      <div v-for="(item, index) in formData.attribute" :key="index" class="attr-item">
                        <t-form-item :requiredMark="index === 0 ? true : false" :label="index === 0 ? '标题' : ''"
                          class="attr-title" style="margin-right: 0">
                          <t-form-item :requiredMark="false" :key="`attribute-t-${index}`"
                            :name="`attribute[${index}].title`" :rules="[
                              {
                                validator: attrtValidator,
                                required: true,
                                trigger: 'change',
                              },
                            ]" style="margin-right: 0">
                            <t-input style="width: 240px" maxlength="10" showLimitNumber
                              @blur="removeEmoji(index, 'title')" v-model="formData.attribute[index].title"
                              placeholder="如：品牌"></t-input>
                          </t-form-item>
                        </t-form-item>
                        <t-form-item :requiredMark="index === 0 ? true : false" :label="index === 0 ? '内容' : ''"
                          name="school" class="attr-con">
                          <t-form-item style="margin-right: 0" :key="`attribute-c${index}`"
                            :name="`attribute[${index}].content`" :requiredMark="false" :rules="[
                              {
                                validator: attrcValidator,
                                required: true,
                                trigger: 'change',
                              },
                            ]">
                            <div style="display: flex; gap: 8px; align-items: center">
                              <t-input style="width: 516px" showLimitNumber maxlength="40"
                                @blur="removeEmoji(index, 'content')" v-model="formData.attribute[index].content"
                                placeholder="如：另可"></t-input>
                              <iconpark-icon :style="{ opacity: index }" name="iconremove" class="iconremove"
                                @click="attrRemove(index)" />
                            </div>
                          </t-form-item>
                        </t-form-item>
                      </div>
                      <!-- <div style="display: flex;gap: 8px;align-items: center;">

                      </div> -->
                      <t-button type="button" style="margin-bottom: 8px" theme="primary" variant="outline"
                        v-if="formData.attribute.length < 20" @click="addAttr">
                        <iconpark-icon style="color: #4d5eff" name="iconadd" class="iconorientation" />
                        添加属性
                      </t-button>
                    </div>
                  </t-form-item>
                </div>
              </template>
            </t-form>
          </div>
        </div>
        <div v-show="formStep === 2" class="form-one formr8">
          <div class="step-tables">
            <div class="lable-se" v-if="!formData.is_external_link">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <g clip-path="url(#clip0_2400_62241)">
                  <path
                    d="M10.0001 18.3333C11.0946 18.3347 12.1787 18.1198 13.1899 17.701C14.2012 17.2821 15.1196 16.6675 15.8926 15.8925C16.6676 15.1195 17.2822 14.2011 17.7011 13.1898C18.1199 12.1786 18.3348 11.0946 18.3334 10C18.3348 8.90544 18.1199 7.82141 17.701 6.81018C17.2821 5.79894 16.6676 4.88046 15.8926 4.1075C15.1196 3.33251 14.2012 2.71791 13.1899 2.29903C12.1787 1.88015 11.0946 1.66525 10.0001 1.66666C8.90554 1.66527 7.8215 1.88019 6.81027 2.29906C5.79904 2.71794 4.88055 3.33253 4.10759 4.1075C3.33262 4.88046 2.71804 5.79894 2.29916 6.81018C1.88028 7.82141 1.66536 8.90544 1.66676 10C1.66534 11.0946 1.88024 12.1786 2.29912 13.1898C2.718 14.2011 3.3326 15.1195 4.10759 15.8925C4.88055 16.6675 5.79904 17.2821 6.81027 17.7009C7.8215 18.1198 8.90554 18.3347 10.0001 18.3333Z"
                    fill="#FC7C14" stroke="#FC7C14" stroke-width="1.66667" stroke-linejoin="round" />
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M9.99992 15.4167C10.2762 15.4167 10.5411 15.3069 10.7365 15.1116C10.9318 14.9162 11.0416 14.6513 11.0416 14.375C11.0416 14.0987 10.9318 13.8338 10.7365 13.6384C10.5411 13.4431 10.2762 13.3333 9.99992 13.3333C9.72365 13.3333 9.4587 13.4431 9.26335 13.6384C9.068 13.8338 8.95825 14.0987 8.95825 14.375C8.95825 14.6513 9.068 14.9162 9.26335 15.1116C9.4587 15.3069 9.72365 15.4167 9.99992 15.4167Z"
                    fill="white" />
                  <path d="M10 5V11.6667" stroke="white" stroke-width="1.66667" stroke-linecap="round"
                    stroke-linejoin="round" />
                </g>
                <defs>
                  <clipPath id="clip0_2400_62241">
                    <rect width="20" height="20" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              {{ t('niche.addsqtip') }}
            </div>
            <div class="table-box" v-if="!formData.is_external_link">
              <div class="table-header" :class="{ bbr: !sqChanDataShow }">
                <div class="left">
                  <span>{{ t('niche.gch') }}</span>
                  <iconpark-icon v-if="sqChanDataShow" name="iconarrowup" class="table-icon"
                    @click="sqChanDataShow = !sqChanDataShow" />
                  <iconpark-icon v-else name="iconarrowdwon" class="table-icon"
                    @click="sqChanDataShow = !sqChanDataShow" />
                </div>
                <div class="right" @click="addSquareRun">
                  <iconpark-icon name="iconadd" class="table-icon a-icon" />
                  <a>{{ t('niche.addoth') }}</a>
                </div>
              </div>
              <div v-show="sqChanDataShow" class="table-content">
                <t-table cell-empty-content="--" :row-key="'id'" :data="sqChanData" :columns="channelColumns"
                  :pagination="null">
                  <template #related_name="{ row }">
                    <div class="related-box" :class="{ tableDisabled: row.disabled }">
                      <t-checkbox v-model="row.checked" :disabled="row.disabled"
                        @change="checkboxSqChange($event, row)"></t-checkbox>
                      <div v-if="row.source_type === 1" class="self-tag">自</div>
                      <!-- <span :style="{ color: row.disabled ? '#ACB3C0' : '#1a2139' }">{{ row.name }}</span> -->
                      <t-tooltip :content="row.name">
                        <div :style="{ color: row.disabled ? '#ACB3C0' : '#1a2139' }" class="text">
                          {{ row.name }}
                        </div>
                      </t-tooltip>
                    </div>
                  </template>
                  <template #id="{ row }">
                    <span :style="{ color: row.disabled ? '#ACB3C0' : '#1a2139' }">{{ row.uuid }}</span>
                  </template>
                  <template #empty>
                    <Empty />
                  </template>
                </t-table>
              </div>
            </div>
            <div class="table-box" :class="{ bbr: !nbChanDataShow }">
              <div class="table-header">
                <div class="left">
                  <span>{{ t('niche.szpt') }}</span>
                  <iconpark-icon v-if="nbChanDataShow" name="iconarrowup" class="table-icon"
                    @click="nbChanDataShow = !nbChanDataShow" />
                  <iconpark-icon v-else name="iconarrowdwon" class="table-icon"
                    @click="nbChanDataShow = !nbChanDataShow" />
                </div>
                <div class="right" @click="addFigureRun">
                  <iconpark-icon name="iconadd" class="table-icon a-icon" />
                  <a>{{ t('niche.addos') }}</a>
                </div>
              </div>
              <join-box ref="joinRef" style="margin-bottom: 12px; width: fit-content" />
              <div v-show="nbChanDataShow" class="table-content">
                <t-table cell-empty-content="--" :row-key="'id'" :data="nbChanData" :columns="channelColumns"
                  :pagination="null">
                  <template #related_name="{ row }">
                    <div class="related-box" :class="{ tableDisabled: row.disabled }">
                      <t-checkbox v-model="row.checked" :disabled="row.disabled"
                        @change="nbDataChange($event, row, true)"></t-checkbox>
                      <div v-if="row.source_type === 1" class="self-tag">自</div>
                      <template v-if="row.tip">
                        <div class="tip-box">
                          <img class="icon" src="@renderer/assets/niche/icon_warning.svg" alt="" />
                          <span class="tip-text">{{ t('niche.noopens') }}</span>
                          <div class="open-btn" @click="jumpFigureRun">{{ t('niche.toopen') }}</div>
                        </div>
                      </template>
                      <template v-else>
                        <div v-if="row.digital_type === 1" class="type-tag type-tag1" style="padding: 0 4px">
                          <img class="icon" src="@renderer/assets/niche/type2.png" alt="" />
                          <span>{{ t('niche.szsxx') }}</span>
                        </div>
                        <div v-if="row.digital_type === 2" class="type-tag type-tag2" style="padding: 0 4px">
                          <img class="icon" src="@renderer/assets/niche/city.png" alt="" />
                          <span>数字城市</span>
                        </div>
                        <div v-if="row.digital_type === 3" class="type-tag type-tag3" style="padding: 0 4px">
                          <img class="icon" src="@renderer/assets/niche/type3.png" alt="" />
                          <span>{{ t('niche.szcbdx') }}</span>
                        </div>
                        <div v-if="row.digital_type === 4" class="type-tag type-tag4" style="padding: 0 4px">
                          <img class="icon" src="@renderer/assets/niche/type4.png" alt="" />
                          <span>{{ t('niche.szsq') }}</span>
                        </div>
                        <div v-if="row.digital_type === 5" class="type-tag type-tag5" style="padding: 0 4px">
                          <img class="icon" :src="schoolicon" />
                          <span>{{ t('niche.szgx') }}</span>
                        </div>
                        <t-tooltip :content="row.name">
                          <div :style="{ color: row.disabled ? '#ACB3C0' : '#1a2139' }" class="text">
                            {{ row.name }}
                          </div>
                        </t-tooltip>
                      </template>
                    </div>
                  </template>
                  <template #id="{ row }">
                    <div :style="{ color: row.disabled ? '#ACB3C0' : '#1a2139' }" class="text">
                      {{ row.promotion_related }}
                    </div>
                  </template>
                  <template #empty>
                    <Empty />
                  </template>
                </t-table>
              </div>
            </div>

            <!-- <div class="table-box">
              <div class="table-header">
                <div class="left">
                  <span>{{ t('niche.lkpt') }}</span>
                  <iconpark-icon v-if="lkptShow" name="iconarrowup" class="table-icon" @click="lkptShow = !lkptShow" />
                  <iconpark-icon v-else name="iconarrowdwon" class="table-icon" @click="lkptShow = !lkptShow" />
                </div>
                <div class="right"></div>
              </div>

              <div v-for="item in platformChanData" :key="item.team_id" class="table-check" v-show="lkptShow">
                <div style="height: 22px">
                  <t-checkbox
                    v-model="item.checked"
                    :disabled="item.disabled"
                    @change="changePlatform($event, item, true)"
                    >{{ t('niche.lkfind') }}</t-checkbox
                  >
                </div>
                <div style="margin-left: 24px; color: #828da5">
                  阅读并确认 <a @click="openAgreement">{{ t('niche.ruzhuxiey') }}</a>
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
    <div class="footer family">
      <t-button theme="default" style="width: 80px" @click="draftBtnRun"> {{ t('niche.cgsave') }} </t-button>
      <template v-if="formStep === 1">
        <t-button theme="default" style="min-width: 80px" @click="viewRun"> {{ t('niche.ylxs') }} </t-button>
        <t-button style="width: 80px" @click="nextRun"> {{ t('niche.guide_step5') }} </t-button>
      </template>
      <template v-else>
        <t-button style="width: 80px" theme="default" @click="backOne"> {{ t('niche.guide_step7') }} </t-button>
        <t-button style="min-width: 80px" :disabled="channelIsEmpty" @click="publishRun">
          {{ t('niche.lijifabu') }}
        </t-button>
      </template>
      <t-button v-if="false" @click="formStep = 2">开发时用</t-button>
    </div>
  </div>
  <!--
  <MapSelector
    v-model="mapVisible"
    module-source="activity"
    :loc="location.latLng?.latitude ? location : null"
    @activity-confirm="mapConfirm"
  />
  -->
  <BaiduMapSelector v-model:visible="mapVisible" :loc="location.latLng?.latitude ? location : null"
    @confirm="mapConfirm" />
  <review ref="reviewRef" />
  <addSquare ref="addSquareRef" :square-id="squareData?.square?.squareId" @update-package="updatePackage"
    @add-event="addEvent" />

  <RenewalDialog v-if="renewalDialogVisible" v-model="renewalDialogVisible" :square-id="squareData?.square?.squareId"
    :upgrade="upgrade" :team-id="teamId" @success="renewalSuccess" />
  <OpenSquare v-if="openSquareVisible" v-model="openSquareVisible" @success="renewalSuccess" />

  <privacy ref="privacyRef" />
</template>

<script setup lang="ts">
  import { computed, nextTick, onActivated, onMounted, reactive, ref, watch } from 'vue';
  import productImage from '@renderer/views/niche/components/nicheHome/productImage.vue';
  import review from '@renderer/views/niche/components/nicheHome/review.vue';
  import privacy from '@renderer/views/niche/components/nicheHome/privacy.vue';
  import schoolicon from "@renderer/assets/member/svg/schoolicon.svg";

  import joinBox from '@renderer/views/niche/components/nicheHome/join-box.vue';
  import addSquare from '@renderer/views/niche/components/nicheHome/addSquare.vue';
  import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
  import { useI18n } from 'vue-i18n';
  import { useRoute, useRouter } from 'vue-router';
  import { getSquareInfo, sharedSquare, getTeamAnnualFee } from '@renderer/api/business/manage';
  import _ from 'lodash';
  import to from 'await-to-js';
  import { openAppListFn } from '@renderer/views/workBench/utils';
  import { goToDigitalPlatform_member } from '@renderer/views/member/utils/auth';
  import { AppAllApp } from '@renderer/api/workBench';
  import uploadImage, { blobToPngFile } from '@renderer/views/square/utils/upload';
  import upload from '@renderer/views/zhixing/components/upload.vue';
  import { businessReleaseAdd, businessReleasePut, getDetail, marketClassifytree } from '../apis';
  // import MapSelector from "@/views/square/components/MapSelector.vue";
  import BaiduMapCard from '@renderer/components/common/map/BaiduMapCard.vue';
  import BaiduMapSelector from '@renderer/components/common/map/BaiduMapSelector.vue';
  import { markerIcon } from '@/views/square/components/page-designer/utils';
  import {
    businessDraftAdd,
    businessDraftGet,
    businessDraftPut,
    getAppsState,
    promotionList,
    releaseUnit,
    unitClassify,
  } from '../apis/create';
  import OpenSquare from '@/views/square/components/OpenSquare.vue';
  import RenewalDialog from '@/views/square/components/annual-fee/AnnualFeeDialog.vue';
  import Editor from '@/components/editor/index.vue';
  import LynkerSDK from '@renderer/_jssdk';

  const { ipcRenderer } = LynkerSDK;
  const route = useRoute();

  const { t } = useI18n();
  const emits = defineEmits(['deltabItem']);

  const closePage = () => {
    emits('deltabItem', true);
  };

  const checkDraft = () => {
    if (!formData.title) {
      return true;
    }
    if (formData.effective_unlimited !== 1 && !formData.time.length) {
      return true;
    }
    return false;
  };

  const closePageDraftRun = () => {
    if (checkDraft()) {
      const myDialog = DialogPlugin({
        header: t('niche.tip'),
        theme: 'info',
        body: t('niche.cgtips'),
        className: 'dialog-classp32',
        cancelBtn: null,
        onConfirm: () => {
          myDialog.hide();
        },
      });
    } else {
      draftReq();
    }
  };

  const closeOrSaveFn = () => {
    if (havaValue.value) {
      const myDialog = DialogPlugin({
        header: t('niche.tip'),
        theme: 'info',
        body: t('niche.sfbccg'),
        className: 'dialog-classp32',
        confirmBtn: '保存',
        cancelBtn: '不保存',
        onConfirm: () => {
          myDialog.hide();
          closePageDraftRun();
        },
        onCancel: () => {
          closePage();
          myDialog.hide();
        },
      });
    } else {
      closePage();
    }
  };

  const props = defineProps({
    activationGroupItem: {
      type: Object,
      default: null,
    },
  });

  defineExpose({
    closeOrSaveFn,
  });

  onActivated(() => {
    // settabItem();
    getTeamAnnualFeeRun();
    getAppAuth();
  });
  const teamId = ref(localStorage.getItem('businessTeamId') || '');
  const figureAuth = ref(false);
  const kefuAuth = ref(false);



  const getAppAuth = async () => {
    const [err, res] = await to(getAppsState(teamId.value));
    if (err) {
      return;
    }
    const { data } = res;
    if (data.data?.government || data.data?.member || data.data?.cbd || data.data?.association) {
      figureAuth.value = true;
    } else {
      if (!nbChanData.value[0]?.tip) {
        nbChanData.value.unshift({
          digital_type: 1,
          id: '',
          promotion_related: '',
          name: t('niche.noopens'),
          promotion_type: 2,
          selected: 0,
          disabled: true,
          tip: true,
          source_type: 1,
        });
      }
      figureAuth.value = false;
    }
    AppAllApp(teamId.value).then((res) => {
      if (res.data?.data) {
        kefuAuth.value = res.data.data.some((item) => item.uuid === 'kefu');
      }
    });
  };

  const setSellingPoint = (data) => {
    if (data?.length) {
      const temp = data.map((item) => ({
        content: item,
      }));
      formData.selling_point = temp;
    }
  };

  const unitDataTemp = ref(null);
  const unitDataHandle = (unit) => {
    formData.unit_id = unit?.id;
    unitDataTemp.value = unit;
    lastSelectedOption.value = unit?.name;
    console.log('unitDataHandle', unitDataTemp.value);
    releaseUnitReq();
  };
  const imageDataHandle = (imgs) => {
    attrsImage.value.value = imgs;
  };
  const setDataRun = (data) => {
    formData.type = data.type;
    formData.title = data.title;
    formData.id = data.id;
    formData.address = data.address;
    formData.time[0] = data.effective_begin;
    formData.time[1] = data.effective_end;
    // formData.content = data.content;
    if (data.address_longitude) {
      markerPosition.value = [Number(data.address_longitude), Number(data.address_latitude)];
      location.value.latLng.longitude = Number(data.address_longitude);
      location.value.latLng.latitude = Number(data.address_latitude);
      selectPos(location.value.latLng);
    }
    formData.address_name = data.address;
    setSellingPoint(data.selling_point);
    formData.template_uuid = data.template_uuid;
    formData.square_id = data.square_id;
    formData.price_option = data.price_option;
    formData.price = data.price;
    formData.quantity = data.quantity;
    formData.spec = data.spec;
    formData.attributeShow = !!data.attribute?.length;
    formData.attribute = data.attribute || [];
    formData.square_data = data.square_data;
    formData.effective_unlimited = data.effective_unlimited;
    if (data.classify[1]) {
      formData.classify_id = data.classify[1] ? data.classify[1]?.id : data.classify[0]?.id;
      const classifyCh = data.classify[1]?.alias ? `/ ${data.classify[1]?.alias}` : '';
      formData.classify_name = `${data.classify[0].alias} ${classifyCh}`;
    }
    formData.is_external_link = data.is_external_link === 1;
    formData.external_app_link = data.external_app_link;
    formData.external_pc_link = data.external_pc_link;
    formData.is_external_link_same = data.external_pc_link === data.external_app_link;
    formData.custom = data.price_option === 0;
    formData.discuss = data.price_option === 1;
    unitDataHandle(data.unit);
    imageDataHandle(data.images);
    nextTick(() => {
      formData.time[0] = data.effective_begin;
      formData.time[1] = data.effective_end;
      if (data.content) {
        if (data.content.includes('"insert":')) {
          editorRef.value.renderContent({ ops: JSON.parse(data.content) });
        } else {
          editorRef.value.renderContent({ ops: [{ insert: `${data.content}` }] });
        }
      } else {
        editorRef.value.renderContent({ ops: [] });
      }
    });
    setTimeout(() => {
      form.value.clearValidate();
    }, 500);
  };
  const getDetailRun = (id) => {
    getDetail(id).then((res) => {
      if (res.data) {
        setDataRun(res.data.data);
      }
    });
  };

  const getDratfDetailRun = (id) => {
    businessDraftGet(id).then((res) => {
      if (res.data) {
        const data = res.data.data;
        data.id = null;
        setDataRun(data);
        getSquaresListRun();
      }
    });
  };
  const dischange = computed(() => {
    return !!formData.id;
  });

  const isDraft: any = ref(0);
  const idDraft: any = ref(0);
  const initRun = async () => {
    console.log('route', route);
    console.log('route', route.query.id);
    const id: any = route.query?.id || 0;
    formData.id = id;
    isDraft.value = route.query?.isDraft || 0;
    idDraft.value = route.query?.idDraft || 0;
    formData.draft_id = route.query?.idDraft || undefined;
    if (id) {
      await getDetailRun(id);
    } else if (idDraft.value) {
      await getDratfDetailRun(idDraft.value);
    } else {
      await releaseUnitReq();
    }
    getPromotionList();
    getMarketClassifytree();
    getSquaresListRun();
    getTeamAnnualFeeRun();
    if (isDraft.value) {
      formData.id = 0;
    }
    console.log(markerPosition.value, 'markerPosition.valueinit');
  };



  const promotionData = ref({
    square_id: '',
    channel: [],
  });
  const chanDataHandle = () => {
    sqChanData.value = promotionData.value?.channel.find((item) => item.type === 1).list;
    for (const sq of sqChanData.value) {
      sq.checked = sq.selected === 1;
      console.log(
        sq.source_type === 1 && sq.selected !== 1 ? false : formData.id !== 0,
        '(sq.source_type === 1 && sq.selected !== 1 ? false : formData.id !== 0)',
      );
      console.log(formData.id !== 0, '(sqa.id !== 0)');
      sq.disabled = isDraft.value ? false : sq.source_type === 1 && sq.selected !== 1 ? false : formData.id !== 0;
    }
    nbChanData.value = promotionData.value?.channel.find((item) => item.type === 2).list;
    for (const sq of nbChanData.value) {
      // eslint-disable-next-line no-nested-ternary
      sq.disabled = isDraft.value ? false : formData.id !== 0 ? sq.selected === 1 : false;
      sq.checked = sq.selected === 1;
    }
    platformChanData.value = promotionData.value?.channel.find((item) => item.type === 3).list;
    for (const sq of platformChanData.value) {
      sq.checked = sq.selected === 1;
      // eslint-disable-next-line no-nested-ternary
      sq.disabled = isDraft.value ? false : formData.id !== 0 ? sq.selected === 1 : false;
    }
    console.log('promotionData', promotionData.value);
  };

  const getPromotionList = () => {
    promotionList(isDraft.value ? idDraft.value : formData.id, isDraft.value).then((res) => {
      console.log(res);
      if (res.data) {
        promotionData.value = res.data.data;
        chanDataHandle();
        getAppAuth();
      }
    });
  };

  const releaseUnitData = ref([]);
  const allUnitData = ref([]);
  const tabListData = ref([]);
  const releaseUnitReq = () => {
    releaseUnit().then((res) => {
      console.log(res);
      if (res.data) {
        releaseUnitData.value = res.data.data.list;
        console.log('releaseUnitData', releaseUnitData.value);
      }
    });
    const area = props.activationGroupItem.teamRegion;
    unitClassify(area).then((res) => {
      console.log(res);
      tabListData.value = [];
      if (res.data) {
        allUnitData.value = res.data.data;
        units.value = [];
        for (const item of allUnitData.value) {
          tabListData.value.push({ label: item.name, value: item.id, panel: null });
          const unitItems = item.unit.map((ut) => ({
            classId: item.id,
            id: ut.id,
            name: ut.name,
          }));
          units.value.push(...unitItems);
        }
        console.log('units.value', units.value);
        console.log('unitDataTemp.value', unitDataTemp.value);
        if (unitDataTemp.value) {
          const item = allUnitData.value.find((item) => item.id === unitDataTemp.value?.classify_id);
          console.log('unitDataTemp.value-item', item);
          filteredOptions.value = item?.unit;
          console.log('unitDataTemp.value', unitDataTemp.value);
          nowUnitClassId.value = unitDataTemp.value?.classify_id;
        } else {
          filteredOptions.value = allUnitData.value[0]?.unit;
          nowUnitClassId.value = allUnitData.value[0]?.id;
        }
        console.log('allUnitData', allUnitData.value);
      }
    });
  };


  onMounted(() => {
    initRun();
  });
  const classifytree = ref([]);
  const pricePX = ref('');
  const getMarketClassifytree = () => {
    const area = props.activationGroupItem.teamRegion;
    pricePX.value = area === 'CN' ? '￥' : 'MOP';
    marketClassifytree(area).then((res) => {
      console.log(res);
      if (res.data) {
        classifytree.value = res.data.data;
      }
    });
  };

  const alertShow = ref(true);
  const alertClick = () => {
    alertShow.value = false;
  };
  const quantityValidator = () => {
    if (!formData.quantity) {
      return {
        message: t('niche.sp_count_tip'),
        result: false,
        type: 'error',
      };
    }
    if (!formData.unit_id) {
      return {
        message: t('niche.unitse'),
        result: false,
        type: 'error',
      };
    }
    return { result: true, message: '', type: 'success' };
  };
  const priceValidator = () => {
    if (formData.custom && !formData.price) {
      return {
        message: '请输入商机价格',
        result: false,
        type: 'error',
      };
    }
    return { result: true, message: '', type: 'success' };
  };
  const timeValidator = () => {
    if (
      formData.effective_unlimited === 0 &&
      (!formData.time || !formData.time?.length || !formData.time.filter((item) => item.length).length)
    ) {
      return {
        message: '请选择有效时间',
        result: false,
        type: 'error',
      };
    }
    return { result: true, message: '', type: 'success' };
  };
  const selling_pointValidator = () => {
    if (!formData.selling_point[0]?.content) {
      return {
        message: '请输入商机卖点',
        result: false,
        type: 'error',
      };
    }
    return { result: true, message: '', type: 'success' };
  };
  const getContentText = () => {
    let text = '';
    try {
      if (formData.content) {
        const content = JSON.parse(formData.content);
        for (const item of content) {
          if (typeof item.insert !== 'object') {
            const str = item.insert.replace(/\s*/g, '');
            if (str !== '\n' && str !== '\n\n' && str.length) {
              text += item.insert;
            }
          }
          if (typeof item.insert === 'object' && item.insert.image) {
            text += `[图片]`;
          }
        }
      } else {
        text = '';
      }
    } catch (err) {
      text = '';
    }
    return text;
  };

  const contentValidator = () => {
    if (!formData.content?.length) {
      return {
        message: '请输入商机详情',
        required: true,
        trigger: 'change',
      };
    }
    if (!getContentText().length) {
      return {
        message: '请输入商机详情',
        required: true,
        trigger: 'change',
      };
    }
    return { result: true, message: '', type: 'success' };
  };

  const FORM_RULES = {
    type: [{ required: true, message: t('niche.type_rq') }],
    spec: [{ required: true, message: '请输入商机规格' }],
    title: [{ required: true, message: t('niche.title_rq') }],
    classify_id: [{ required: true, message: '请选择商机分类' }],
    address: [{ required: true, message: t('niche.address') }],
    quantity: [{ required: true, validator: quantityValidator, trigger: 'change' }],
    unit_id: [{ required: true, message: t('niche.unitse'), trigger: 'change' }],
  };
  const formData = reactive({
    type: 1,
    effective_unlimited: 1,
    title: '',
    squareName: '',
    time: [],
    selling_point: [
      {
        content: '',
      },
    ],
    template_uuid: undefined,
    images: [],
    channel: [],
    classify_id: undefined,
    classify_name: undefined,
    address: undefined,
    address_name: undefined,
    address_longitude: undefined,
    address_latitude: undefined,
    content: '',
    effective_end: '',
    effective_begin: '',
    id: 0,
    square_id: 0,
    price_option: 0,
    spec: undefined,
    quantity: undefined,
    unit_id: undefined,
    price: undefined,
    custom: true,
    discuss: false,
    draft_id: undefined,
    square_data: {},
    attributeShow: false,
    attribute: [
      {
        title: '',
        content: '',
      },
    ],
    is_external_link: false,
    is_external_link_same: true,
    external_app_link: '',
    external_pc_link: '',
  });
  const form = ref(null);

  const channelDataHandle = () => {
    const temp = [];
    for (const sq of sqChanData.value) {
      if (!formData.is_external_link && sq.checked) {
        temp.push(sq);
      }
    }
    for (const sq of nbChanData.value) {
      if (sq.checked) {
        temp.push(sq);
      }
    }
    // for (const sq of platformChanData.value) {
    //   if (sq.checked) {
    //     temp.push(sq);
    //   }
    // }
    return temp;
  };

  // const location = ref({ latLng: { longitude: 113, latitude: 22 } });
  const location = ref({ latLng: { longitude: null, latitude: null } });
  const mapVisible = ref(false);
  const loading = ref(false);
  const businessReleaseAddReq = () => {
    const params = dataHandle();
    params.channel = channelDataHandle();
    params.id = 0;
    businessReleaseAdd(params).then((res: any) => {
      loading.value = false;
      if (res.data.code === 0) {
        subSucc();
      } else {
        // MessagePlugin.error(res.data.message);
      }
    });
  };

  const dataHandle = (draft?) => {
    const params: any = _.cloneDeep({ ...formData });
    params.effective_begin = formData.time[0];
    params.effective_end = formData.time[1];
    params.address_longitude = location.value.latLng.longitude;
    params.address_latitude = location.value.latLng.latitude;
    params.images = attrsImage.value.value;
    params.price_option = formData.custom ? 0 : 1;
    params.attribute = formData.attributeShow ? formData.attribute : [];
    params.selling_point = formData.selling_point.map((item) => item.content).filter((item) => item.length);
    if (params.type === 2) {
      params.is_external_link = false;
    }
    if (formData.is_external_link_same) {
      params.external_app_link = params.external_pc_link;
    }
    params.is_external_link = params.is_external_link ? 1 : 0;
    if (!params.is_external_link) {
      params.external_app_link = '';
      params.external_pc_link = '';
    } else {
      if (!params.id && !idDraft.value && !draft) {
        params.address_longitude = '';
        params.address_latitude = '';
        params.address = '';
        params.address_name = '';
      }
    }
    return params;
  };

  const businessReleasePutReq = () => {
    const params = dataHandle();
    params.channel = channelDataHandle();
    businessReleasePut(params, params.id).then((res: any) => {
      loading.value = false;
      if (res.data.code === 0) {
        subSucc();
      }
    });
  };
  const subSucc = () => {
    MessagePlugin.success('发布成功');
    closePage();
  };

  const markerDragend = (data) => {
    console.log(data.lnglat);
    // getAddress(data.lnglat);
  };
  const editAddress = () => {
    mapVisible.value = true;
  };
  const markerPosition = ref([]);
  const center = ref([113.498449, 22.193527]);
  const zoom = ref(10);
  const selectPos = (item) => {
    console.log(item, '定位');
    let position = [];
    if (item) {
      position = [item.longitude, item.latitude];
    } else {
      position = [121.59996, 31.197646];
    }
    markerPosition.value = position;
    center.value = position;
    zoom.value = 18;
  };
  const mapConfirm = (data) => {
    console.log(data, '地址');
    if (data) {
      formData.address = `${data.addressComponent?.province}${data.addressComponent?.city}${data.addressComponent?.district}${data.addressComponent?.township}${data.addressComponent?.street}${data.addressComponent?.streetNumber}`;
      formData.address_name = `${data.address}`;
      location.value.latLng = {
        longitude: data.location.lng,
        latitude: data.location.lat,
      };
      markerPosition.value = [data.location.lng, data.location.lat];
      selectPos(location.value.latLng);
    }
  };
  const getSquaresListRun = () => {
    sharedSquare().then(async (res: any) => {
      if (res.data) {
        const squareId = res.data.square?.squareId;
        formData.square_data = {
          name: res.data.square?.name,
          id: squareId,
          avatar: res.data.square?.avatar,
        };
        formData.squareName = res.data.square?.name;
        formData.square_id = res.data.square?.squareId;
        console.log(squareId, 'squareId');
        console.log(formData.square_id, 'formData.square_id');

        if (squareId && !formData.id && !isDraft.value) {
          // 编辑不去拿地址
          const squareInfo = await getSquareInfo(squareId, localStorage.getItem('businessTeamId'));
          if (squareInfo?.data?.info) {
            const organizationProfile = squareInfo?.data?.info?.organizationProfile;
            const locationReq = organizationProfile?.address?.location;
            if (locationReq?.latLng?.longitude) {
              location.value.latLng.longitude = locationReq.latLng.longitude;
              location.value.latLng.latitude = locationReq.latLng.latitude;
              markerPosition.value = [locationReq.latLng.longitude, locationReq.latLng.latitude];
              formData.address_name = locationReq.name;
              formData.address = locationReq.address;
              selectPos(location.value.latLng);
            }
          }
        }
      }
    });
  };

  const formatValue = (number) => {
    let val = number;
    if (number > 999999) {
      formData.price = 999999;
      val = 999999;
    }
    if (number < 1) {
      formData.price = 1;
      val = 1;
    }
    // const regex = /\.(\d+)/;
    // const match = regex.exec(number.toString());

    // if (match) {
    //   let decimalPart = match[1];
    //   if (decimalPart.length > 2) {
    //     decimalPart = decimalPart.slice(0, 2);
    //   }

    //   let result = number.toFixed(decimalPart.length);
    //   result = result.replace(regex, `.${decimalPart}`);

    //   if (result > 999999) {
    //     val = 999999;
    //   } else {
    //     val = result;
    //   }
    // }
    const res = val.toLocaleString('zh-CN', {
      style: 'decimal',
      minimumFractionDigits: 2,
    });
    console.log('res', res);

    return res;
  };

  const formatValueQu = (number) => {
    if (number > 999999) {
      formData.quantity = 999999;
      return 999999;
    }
    if (number < 1) {
      formData.quantity = 1;
      return 1;
    }
    const regex = /\.(\d+)/;
    const match = regex.exec(number.toString());

    if (match) {
      let decimalPart = match[1];
      if (decimalPart.length > 4) {
        decimalPart = decimalPart.slice(0, 4);
      }

      let result = number.toFixed(decimalPart.length);
      result = result.replace(regex, `.${decimalPart}`);

      if (result > 999999) {
        return '999999';
      }

      return result;
    }
    return number;
  };
  const customChange = (e) => {
    console.log(e);
    formData.discuss = false;
  };
  const discussChange = (e) => {
    form.value.clearValidate(['price']);
    console.log(e);
    formData.custom = false;
  };
  const unitClassIdChange = (e) => {
    console.log(e);
    console.log('unitClassIdChange');
    nowUnitClassId.value = e;
    const data = allUnitData.value.find((item) => item.id === e);
    filteredOptions.value = data?.unit;
    setTimeout(() => {
      setTagWidth();
    }, 100);
  };

  const unitChange = (event, data) => {
    console.log(event, 'event');
    console.log(data, 'data');
    lastSelectedOption.value = data.option.label;
  };

  const nowUnitClassId = ref(null);
  const unitVisible = ref(false);
  const lastSelectedOption = ref('');
  const setUnit = (item) => {
    const uns = allUnitData.value.find((all) => all.id === item.classify_id);
    nowUnitClassId.value = item.classify_id;
    filteredOptions.value = uns?.unit;
    formData.unit_id = item.id;
    lastSelectedOption.value = item.name;
  };
  const filteredOptions = ref([]);

  const draftSucc = () => {
    MessagePlugin.success(t('niche.ysave'));
    setTimeout(() => {
      closePage();
    }, 200);
  };

  const draftReq = () => {
    const params = dataHandle(true);
    params.channel = channelDataHandle();
    if (isDraft.value) {
      const temp = _.cloneDeep(params);
      temp.id = idDraft.value;
      if (!markerPosition.value.length) {
        temp.address_latitude = undefined;
        temp.address_longitude = undefined;
      }
      businessDraftPut(temp).then((res: any) => {
        if (res.data.code === 0) {
          draftSucc();
        }
      });
    } else {
      params.id = undefined;
      if (!markerPosition.value.length) {
        params.address_latitude = undefined;
        params.address_longitude = undefined;
      }
      businessDraftAdd(params).then((res: any) => {
        if (res.data.code === 0) {
          draftSucc();
        }
      });
    }
  };

  const draftBtnRun = () => {
    draftRun();
  };

  const draftRun = () => {
    console.log('draftRun');
    if (checkDraft()) {
      const myDialog = DialogPlugin({
        header: t('niche.tip'),
        theme: 'info',
        body: t('niche.cgtips'),
        className: 'dialog-classp32',
        cancelBtn: null,
        onConfirm: () => {
          myDialog.hide();
        },
      });
    } else {
      const myDialog = DialogPlugin({
        header: t('niche.tip'),
        theme: 'info',
        body: t('niche.sfbccg'),
        className: 'dialog-classp32',
        confirmBtn: '保存',
        cancelBtn: '不保存',
        onConfirm: () => {
          myDialog.hide();
          draftReq();
        },
        onCancel: () => {
          closePage();
          myDialog.hide();
        },
      });
    }
  };
  const nextAndViewBtnDis = computed(
    () =>
      formData.title &&
      formData.time.length &&
      attrsImage.value.value.length &&
      formData.quantity &&
      formData.classify_id &&
      formData.unit_id &&
      markerPosition.value.length,
  );

  const havaValue = computed(
    () =>
      formData.title ||
      formData.time.length ||
      attrsImage.value.value.length ||
      formData.quantity ||
      formData.classify_id ||
      formData.unit_id ||
      formData.discuss ||
      formData.spec ||
      formData.content ||
      attrsImage.value.value.length ||
      markerPosition.value.length,
  );

  const nextRun = () => {
    // formStep.value = 2;
    actionKey.value = 2;
    form.value.submit({ showErrorMessage: true });
  };

  const reviewRef = ref(null);
  const actionKey = ref(1);
  const viewRun = () => {
    if (formData.is_external_link) {
      MessagePlugin.warning('暂不支持外部链接预览查看');
      return;
    }
    actionKey.value = 1;
    form.value.submit({ showErrorMessage: true });
  };
  const clearValidateRun = (id) => {
    form.value.clearValidate([id]);
  };
  const onSubmit = ({ validateResult, firstError }) => {
    console.log('onSubmit', formData);

    if (validateResult === true) {
      if (actionKey.value === 1) {
        const data: any = dataHandle();
        data.unit = {
          id: 'xx',
          name: lastSelectedOption.value,
        };
        data.region = props.activationGroupItem.teamRegion;
        data.selling_point = data.selling_point.filter((item) => item.length);
        reviewRef.value.reviewOpen(data);
      } else if (actionKey.value === 2) {
        formStep.value = 2;
      } else {
        console.log('发布');
      }
    } else {
      console.log('Validate Errors: ', firstError, validateResult);
      // const editContent = document.querySelector(".edit-content");
      // const firstErrorDom = editContent.querySelector(".t-is-error");
      // firstErrorDom.scrollIntoView({ behavior: "smooth", block: "start" });
      if (actionKey.value === 1) {
        MessagePlugin.warning(t('niche.retip'));
      } else {
        // MessagePlugin.warning(firstError);
        MessagePlugin.warning(`请填写必填项`);
      }
    }
  };
  const productImageRef = ref(null);
  const attrsImage = ref({
    id: 'productImage',
    name: t('niche.sp_img'),
    value: [],
    editable: true,
    required: true,
  });
  const cascaderChange = (e, ctx) => {
    console.log(e, ctx);
    let Parent = ctx.node?.getParent()?.label;
    // const data = [{ id: 1, alias: Parent }, { id: 2, alias: ctx.node?.label }];
    Parent = Parent ? `${Parent}/` : '';
    formData.classify_name = `${Parent}${ctx.node?.label}`;
    formData.template_uuid = ctx.node?.data?.template_uuid;
  };

  const formStep = ref(1);
  const backOne = () => {
    formStep.value = 1;
  };

  const channelIsEmpty = computed(() => {
    const channel = channelDataHandle();
    return channel.length === 0;
  });
  const publishReqRun = () => {
    if (!formData.id || isDraft.value) {
      businessReleaseAddReq();
    } else {
      businessReleasePutReq();
    }
  };
  const isMac = ref(process.platform === 'darwin');
  const mactip = () => {
    const myDialog = DialogPlugin({
      header: t('niche.tip'),
      theme: 'info',
      body: t('niche.vip_open_tip2'),
      className: 'dialog-classp32',
      cancelBtn: null,
      confirmBtn: '知道了',
      onConfirm: () => {
        myDialog.hide();
      },
    });
  };
  const plazaRenew = () => {
    if (upgrade.value && isMac.value) {
      mactip();
      return;
    }
    const myDialog = DialogPlugin({
      header: tipsData.value.tipHeader || t('niche.tip'),
      theme: 'info',
      body: tipsData.value.con,
      className: 'dialog-classp32',
      confirmBtn: tipsData.value.btn,
      onConfirm: () => {
        if (squareShow.value) {
          openSquareVisible.value = true;
        } else {
          renewalDialogVisible.value = true;
        }
        myDialog.hide();
      },
      onCancel: () => {
        myDialog.hide();
      },
    });
  };
  const checkExpiration = (expiredAt) => {
    const now = new Date();
    const expirationDate = new Date(expiredAt);
    return now > expirationDate;
  };
  const openSquareVisible = ref(false);
  const squareShow = ref(false);
  const squareData = ref(null);
  const pageShow = ref(true);
  const tipsData = ref({
    con: '',
    btn: '',
    tipHeader: null,
  });
  const getTeamAnnualFeeRun = async () => {
    const res = await getTeamAnnualFee(teamId.value);
    if (res.data) {
      squareData.value = res.data;
      if (squareData.value.opened) {
        pageShow.value = false;
        squareShow.value = false;
        if (checkExpiration(squareData.value.annualFeeExpiredAt)) {
          if (squareData.value?.annualFeeDetail?.trial) {
            pageShow.value = true;
            tipsData.value.tipHeader = t('niche.vip_open_tiph');
            tipsData.value.con = t('niche.vip_open_tip5');
            tipsData.value.btn = t('niche.vip_open_btn4');
          } else {
            pageShow.value = true;
            tipsData.value.con = t('niche.vip_open_tip1');
            tipsData.value.btn = t('niche.vip_open_btn1');
          }
        } else {
          pageShow.value = false;
          if (squareData.value?.annualFeeDetail?.package) {
            const item = squareData.value?.annualFeeDetail?.package?.items.find((item) => item.itemType === 'NICHE');
            if (!item) {
              if (squareData.value?.annualFeeDetail?.trial) {
                tipsData.value.con = t('niche.vip_open_tip4');
                tipsData.value.btn = t('niche.vip_open_btn4');
                tipsData.value.tipHeader = t('niche.vip_open_tiph');
                upgrade.value = false;
              } else {
                tipsData.value.con = t('niche.vip_open_tip2');
                tipsData.value.btn = t('niche.vip_open_btn2');
                upgrade.value = true;
              }
              pageShow.value = true;
            } else {
              pageShow.value = false;
            }
          } else {
            tipsData.value.con = t('niche.vip_open_tip2');
            tipsData.value.btn = t('niche.vip_open_btn2');
            upgrade.value = true;
            pageShow.value = true;
          }
        }
      } else {
        tipsData.value.con = t('niche.vip_open_tip3');
        tipsData.value.btn = t('niche.vip_open_btn3');
        pageShow.value = true;
        squareShow.value = true;
      }
    }
  };
  const renewalDialogVisible = ref(false);
  const upgrade = ref(false);
  const renewalSuccess = (e) => {
    renewalDialogVisible.value = false;
    openSquareVisible.value = false;
    // getTeamAnnualFeeRun();
  };

  const publishRun = async () => {
    console.log(formData, 'formData');

    // await getTeamAnnualFeeRun();
    if (pageShow.value) {
      plazaRenew();
    } else {
      const myDialog = DialogPlugin({
        header: t('niche.tip'),
        theme: 'info',
        body: t('niche.lijicm'),
        className: 'dialog-classp32',
        confirmBtn: '发布',
        onConfirm: () => {
          myDialog.hide();
          publishReqRun();
        },
        onCancel: () => {
          myDialog.hide();
        },
      });
    }
  };
  const publishRunDebounce = _.debounce(publishRun, 500, {
    leading: true, // 延长开始后调用
    trailing: false, // 延长结束前调用
  });
  const channelColumns = ref([
    {
      colKey: 'related_name',
      title: t('niche.mchen'),
      width: '360',
    },
    {
      colKey: 'id',
      title: 'ID',
      width: '248',
      ellipsis: true,
    },
  ]);
  const sqChanData = ref([]);
  const sqChanDataShow = ref(true);
  const addSquareRef = ref(null);
  const addSquareRun = () => {
    addSquareRef.value.sqOpen(1, isDraft.value ? idDraft.value : formData.id, sqChanData.value, isDraft.value);
  };

  const invertSelection = () => {
    sqChanData.value.map((item) => {
      if (item.source_type !== 1) {
        item.checked = false;
        // item.disabled = true;
      }
    });
    nbChanData.value.map((item) => {
      if (item.source_type !== 1) {
        item.checked = false;
      }
    });
    platformChanData.value.map((item) => {
      item.checked = false;
      // item.disabled = true;
    });
  };
  const disCancelHandle = () => {
    sqChanData.value.map((item) => {
      item.disabled = false;
    });
    nbChanData.value.map((item) => {
      item.disabled = false;
    });
    platformChanData.value.map((item) => {
      item.disabled = false;
    });
  };
  const checkboxSqChange = (val, row) => {
    console.log(val, row);
    if (!val && row.source_type === 1) {
      const myDialog = DialogPlugin({
        header: t('niche.tip'),
        theme: 'info',
        body: t('niche.quxfb'),
        className: 'dialog-classp32',
        confirmBtn: t('niche.qding'),
        onConfirm: () => {
          myDialog.hide();
          invertSelection();
        },
        onCancel: () => {
          row.checked = true;
          myDialog.hide();
        },
      });
    } else if (val && row.source_type !== 1) {
      sqCheck();
    }
  };

  const jumpFigureRun = () => {
    // ipcRenderer
    //   .invoke("click-menu-item", {
    //     url: "/digitalPlatformIndex/digital_platform_home",
    //     query: null,
    //     selected_path_uuid: "digital_platform",
    //     click_path_uuid: "digital_platform",
    //   })
    //   .then((res) => {
    //     console.log("goPath res: ", res);
    //     res && ipcRenderer.send("update-nume-index", 4);
    //   })
    //   .catch((err) => {
    //     console.log("goPath error: ", err);
    //   });
    goToDigitalPlatform_member(teamId.value);
  };
  const addFigureRun = () => {
    addSquareRef.value.sqOpen(2, isDraft.value ? idDraft.value : formData.id, nbChanData.value, isDraft.value);
  };

  const seleSqHandle = () => {
    sqChanData.value.map((item) => {
      if (item.source_type === 1) {
        item.checked = true;
      }
      item.disabled = false;
    });
    nbChanData.value.map((item) => {
      item.disabled = false;
    });
    platformChanData.value.map((item) => {
      item.disabled = false;
    });
  };

  const addEvent = (data) => {
    if (data.promotion_type === 1) {
      sqChanData.value.push({
        ...data,
        disabled: false,
        checked: true,
      });
      sqChanData.value.map((item) => {
        if (item.source_type === 1) {
          item.checked = true;
        }
      });
    } else {
      nbChanData.value.push({
        ...data,
        disabled: false,
        checked: true,
      });
      sqChanData.value.map((item) => {
        if (item.source_type === 1) {
          item.checked = true;
        }
      });
    }
    if (!formData.id) {
      // seleSqHandle();
    }
  };
  const nbChanData = ref([]);
  const nbChanDataShow = ref(true);

  const sqCheck = () => {
    sqChanData.value.map((item) => {
      if (item.source_type === 1) {
        item.checked = true;
      }
    });
  };

  const platformChanData = ref([]);
  const changePlatform = (e, item, click?) => {
    console.log(e, item, click);
    if (e && click) {
      const myDialog = DialogPlugin({
        header: t('niche.tip'),
        theme: 'info',
        body: t('niche.tgqdzj'),
        className: 'dialog-classp32',
        confirmBtn: t('niche.qding'),
        onConfirm: () => {
          myDialog.hide();
          sqCheck();
          MessagePlugin.success(`添加成功`);
        },
        onCancel: () => {
          myDialog.hide();
          item.checked = false;
        },
      });
    }
  };
  const nbDataChange = (e, item, click?) => {
    if (e && click && item.source_type !== 1) {
      sqCheck();
    }
  };
  const unitFocus = () => {
    unitKeyword.value = '';
    unitKeying.value = false;
    unitClassIdChange(nowUnitClassId.value);
    setTimeout(() => {
      setTagWidth();
    }, 300);
  };

  const setTagWidth = () => {
    const dom: any = document.querySelectorAll('.t-tabs__nav-wrap .t-is-active');
    if (dom?.length) {
      const width = dom[0].clientWidth;
      const offsetLeft = dom[0].offsetLeft;
      const newOffsetLeft = offsetLeft + (width - 16) / 2;
      const bar: any = document.querySelectorAll('.t-tabs__nav-wrap .t-tabs__bar');
      if (bar?.length) {
        bar[0].style.left = `${newOffsetLeft}px`;
      }
    }
  };
  const titleblur = () => {
    const regex = regextext();
    // const regex = /([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2694-\u2697]|\uD83E[\uDD10-\uDD5D])/g;
    formData.title = formData.title.replace(regex, '');
    formData.title = formData.title.replace(/\r?\n/g, '');
  };
  const contentblur = () => {
    const regex = /([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2694-\u2697]|\uD83E[\uDD10-\uDD5D])/g;
    formData.content = formData.content.replace(regex, '');
  };

  const unitKeyword = ref('');
  const ckdomRef = ref('null');
  const unitKeying = ref(false);
  const units = ref([]);
  const unitOptions = ref([]);
  const unitKeywordInput = (e) => {
    console.log(e);
    const unitOption = units.value
      .filter((unit) => unit.name.includes(e))
      .map((unit) => {
        // 创建一个正则表达式，全局匹配，忽略大小写
        const regex = new RegExp(e, 'gi');
        // 使用正则表达式替换字符串中的匹配项
        const updatedName = unit.name.replace(regex, `<a>${e}</a>`);
        // 返回更新后的单位对象
        return {
          ...unit,
          name: updatedName,
        };
      });
    // 假设你需要将更新后的数组赋值给某个响应式变量
    unitOptions.value = unitOption; // 如果 unitOption 是响应式变量的话
  };
  const unitKeywordBlur = (e) => {
    console.log(e);
    unitKeying.value = true;
    filteredOptions.value = [];
    unitKeywordInput('');
  };
  const unitOptionClick = (data) => {
    unitClassIdChange(data.classId);
    formData.unit_id = data.id;
    unitKeying.value = false;
    unitKeyword.value = '';
    // const dom = document.querySelector('.unitPopup');
    // dom.removeAttribute('style');
    // dom.style.display = 'none';
    ckdomRef.value = new Date().toString();
  };

  const router = useRouter();
  const toKefu = () => {
    if (!kefuAuth.value) {
      MessagePlugin.error(t('niche.kfnoopen'));
    } else {
      router.push(openAppListFn({ uuid: 'kefu', name: '客服应用' }, props.activationGroupItem));
    }
  };
  const changeType = (type) => {
    formData.type = type;
    formData.is_external_link = false;
  };

  // 正文内容（富文本）
  const noteData = ref({
    content: {
      images: [],
    },
    user_id: '',
    openid: '',
  });

  /**
   * 处理富文本中正文内容
   */
  const handleContentChange = (contents) => {
    const content = {
      imageUrl: '',
      description: '',
      images: [],
      attachments: [],
      delta: '',
    };
    /**
     * 处理content里的数据
     * description： 所有文本信息加起来
     * imageUrl：第一张图片
     * images：所有的图片集合
     * attachments：所有的文件集合
     * delta：富文本内容
     */
    const delta = [];
    // 附件大小
    let size = 0;
    contents.forEach((v) => {
      let deltaItem = _.cloneDeepWith(v);
      if (typeof v.insert === 'string') {
        content.description += v.insert.trim();
      }
      if (v.insert?.image) {
        !content.imageUrl && (content.imageUrl = v.insert.image);
        const imgItem = noteData.value.content.images.find((img) => img.url === v.insert.image);
        content.images.push(imgItem);
        imgItem?.size && (size += imgItem.size);
      }
      if (v.insert?.custom) {
        size += v.insert.custom.size;
        content.attachments.push(v.insert.custom);
        const atta = { attachment: JSON.stringify(v.insert.custom) };
        const custom = { custom: JSON.stringify(atta) };
        deltaItem.insert = custom;
      }
      delta.push(deltaItem);
    });
    content.delta = JSON.stringify(delta);
    if (content.delta === '[{"insert":"\\n"}]') {
      formData.content = '';
    } else {
      formData.content = content.delta;
    }
    console.log(formData.content);
  };
  const editorRef = ref(null);

  const imgInsert = (val) => {
    val.forEach((img) => {
      const imgObj = {
        name: img.name,
        size: img.size,
        type: img.name?.substring(img.name.lastIndexOf('.') + 1),
        url: img.url,
      };
      noteData.value.content.images.push(imgObj);
    });
  };

  const joinRef = ref(null);
  const updatePackage = (data) => {
    joinRef.value?.connectReData(data);
  };

  const effective_unlimitedChange = (e) => {
    console.log(e, 'effective_unlimitedChange');
    if (e === 1) {
      form.value.clearValidate(['time']);
    }
  };

  const selling_pointAdd = () => {
    if (formData.selling_point.length === 3) {
      MessagePlugin.warning('最多只能添加3个卖点');
      return;
    }
    formData.selling_point.push({
      content: '',
    });
  };
  const selling_pointRemove = (index) => {
    if (formData.selling_point.length === 1) {
      MessagePlugin.warning('至少添加一个卖点');
      return;
    }
    formData.selling_point.splice(index, 1);
  };
  const privacyRef = ref(null);
  const openAgreement = () => {
    // window.open(`https://ringkol.com/privacyView?uuid=RKGYS01`);
    privacyRef.value.open('RKGYS01');
    // ipcRenderer.invoke("create-iframe", "RKGYS01");
  };

  const attrChange = (e) => {
    if (!e) {
      const myDialog = DialogPlugin({
        header: t('niche.tip'),
        theme: 'info',
        body: `确定要关闭商机属性吗？`,
        className: 'dialog-classp32',
        confirmBtn: t('niche.qding'),
        onConfirm: () => {
          myDialog.hide();
          formData.attributeShow = false;
        },
        onCancel: () => {
          formData.attributeShow = true;
          myDialog.hide();
        },
      });
    }
  };
  const attrRemove = (index) => {
    if (index === 0) {
      return false;
    }
    formData.attribute.splice(index, 1);
  };
  const addAttr = () => {
    if (formData.attribute.length > 19) {
      MessagePlugin.warning('最多只能添加20个属性');
      return;
    }
    formData.attribute.push({
      title: '',
      content: '',
    });
  };
  const attrtValidator = (val) => {
    console.log(val, 'attrtValidator');
    if (!val || !val.length || !val.trim().length) {
      return { result: false, message: '请输入标题', type: 'error' };
    }
    return { result: true, message: '', type: 'success' };
  };
  const attrcValidator = (val) => {
    console.log(val, 'attrcValidator');
    if (!val || !val.length || !val.trim().length) {
      return { result: false, message: '请输入内容', type: 'error' };
    }
    return { result: true, message: '', type: 'success' };
  };

  const regextext = () => {
    return /[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g;
  };

  const removeEmoji = (index, key) => {
    const regex = regextext();
    formData.attribute[index][key] = formData.attribute[index][key].replace(regex, '');
  };
  const lkptShow = ref(true);

  const handleAttrsImage = () => {
    attrsImage.value.value = attrsImage.value.value.slice(0, 1);
  }
  const linkkey = ref(1212)
  const changeLink = (val) => {
    linkkey.value += 1;
    if (val && attrsImage.value?.value?.length > 1) {
      const myDialog = DialogPlugin({
        header: t('niche.tip'),
        theme: 'info',
        body: '开启“关联外部链接”时，图片只能上传一张，确定开启',
        className: 'dialog-classp32',
        closeOnOverlayClick: false,
        onConfirm: () => {
          myDialog.hide();
          handleAttrsImage();
        },
        onCancel: () => {
          myDialog.hide();
          formData.is_external_link = false;
        }
      });
    } else {

    }
  };
  const handleKeydown = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  };

  watch(() => route.query, (val, old) => {
    console.log('watch', route.query);
    if (val.id && route.path === '/workBenchIndex/nicheEdit' || val.idDraft && route.path === '/workBenchIndex/nicheDraft') {
      initRun()
    }
  }, { immediate: true });

</script>

<style lang="less" scoped>
  @import '../styles/common.less';

  .type-tag5 {
    color: #49BBFB;

  }

  .editor-box {
    height: 100%;
    width: 100%;
    flex: 1;
    overflow-x: hidden;
    background-image: url(@/assets/niche/bg_small.png);
    overflow-y: auto;
    background-position: center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    overflow-y: auto;
    padding-top: 16px;

    .edit-content {
      width: 872px;
      // padding: 16px 24px;
      // background-color: #fff;
      border-radius: 8px;
      height: calc(100vh - 148px);
      overflow: auto;

      .form-box {
        .alert {
          display: flex;
          padding: 8px 24px;
          justify-content: center;
          align-items: center;
          gap: 8px;
          align-self: stretch;
          border-radius: 8px;
          background: var(--kyy_color_alert_bg_bule, #eaecff);

          .iconerror {
            font-size: 20px;
          }

          .iconc {
            cursor: pointer;
          }

          .text {
            color: var(--kyy_color_alert_text, #1a2139);
            flex: 1;
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
          }
        }

        .step-header {
          margin: 16px 0;
          display: flex;
          margin-bottom: 0;

          .one {
            width: 416px;
            position: relative;

            // .conbg{
            //   background-color: #fff;
            // }
            .con {
              position: absolute;
              top: 0px;
              padding: 12px 24px;
              width: 100%;
              display: flex;
              gap: 12px;

              .index {
                display: flex;
                text-align: center;
                margin-top: 2px;
                font-family: 'PingFang SC';
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px;
                /* 150% */
                width: 40px;
                height: 40px;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 10px;
                border-radius: 20px;
                border: 2px solid var(--border-kyy_color_border_default, #d5dbe4);
                color: var(--text-kyy_color_text_3, #828da5);
              }

              .index-active {
                border: 2px solid var(--border-kyy_color_border_white, #fff);
                color: var(--text-kyy_color_text_white, #fff);
              }

              .title {
                font-family: 'PingFang SC';
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                color: var(--text-kyy_color_text_3, #828da5);
              }

              .title-active {
                color: var(--text-kyy_color_text_white, #fff);
              }
            }
          }
        }

        .form-one {
          .title-tag {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;

            .tag {
              width: 3px;
              height: 16px;
              border-radius: 8px;
              background: var(--brand-kyy_color_brand_default, #4d5eff);
            }

            .title-text {
              color: var(--text-kyy_color_text_1, #1a2139);
              font-family: 'PingFang SC';
              font-size: 16px;
              font-style: normal;
              font-weight: 600;
              line-height: 24px;
              /* 150% */
            }
          }

          .price-input {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-right: 24px;
          }

          .quantity-box {
            display: flex;
            flex-direction: column;

            .quantity-top {
              display: flex;
              gap: 8px;
            }

            .quantity-btm {
              display: flex;
              justify-content: end;
              // height: 46px;
              margin-top: 8px;

              .hoit {
                width: 408px;
                display: flex;
                gap: 8px;

                .item {
                  cursor: pointer;
                  display: flex;
                  height: 32px;
                  padding: 0px 16px;
                  justify-content: center;
                  align-items: center;
                  border-radius: 100px;
                  border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);
                  background: var(--bg-kyy_color_bg_light, #fff);
                  color: var(--text-kyy_color_text_2, #516082);
                  text-align: center;

                  /* kyy_fontSize_2/regular */
                  font-family: 'PingFang SC';
                  font-size: 14px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 22px;
                  /* 157.143% */
                }
              }
            }
          }
        }

        .formr8 {
          border-bottom-left-radius: 8px;
          border-bottom-right-radius: 8px;
        }
      }
    }

    .edit-content::-webkit-scrollbar {
      width: 0px;
    }

    .footer {
      position: fixed;
      bottom: 16px;
      display: flex;
      height: 64px;
      padding: 16px 24px;
      align-items: center;
      gap: 8px;
      align-self: stretch;
      background: var(--bg-kyy_color_bg_light, #fff);
      justify-content: center;
      width: 872px;
      border-radius: 8px;

      .mock-abtn:hover {
        border-radius: 4px;
        background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
      }

      .mock-abtn {
        display: flex;
        width: 64px;
        height: 32px;
        min-height: 32px;
        max-height: 32px;
        justify-content: center;
        align-items: center;
        padding: 4px;
      }

      .def {
        color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        cursor: pointer;
        line-height: 22px;
        /* 157.143% */
      }

      .disabt {
        color: var(--color-button_text_brand-kyy_color_button_text_brand_font_disabled, #c9cfff);
        text-align: center;
        cursor: no-drop;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }
    }
  }

  .iconorientation {
    font-size: 20px;
    margin-right: 4px;
  }

  .map-container {
    position: relative;
    display: flex;
    width: 100%;
    height: 228px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    background: var(--bg-kyy_color_bg_light, #fff);

    .position-map {
      display: flex;
      height: 64px;
      padding: 8px 12px;
      align-items: center;
      justify-content: space-between;
      gap: 16px;
      align-self: stretch;

      .position-map-left-icon {
        display: flex;
        height: 22px;
      }

      .position-map-left {
        overflow: hidden;
        color: var(--text-kyy_color_text_2, #516082);
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .address_name {
          width: 500px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .detail-address {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        align-self: stretch;
        overflow: hidden;
        color: var(--text-kyy_color_text_3, #828da5);
        text-overflow: ellipsis;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        overflow: hidden;
        text-overflow: ellipsis;
        width: 500px;
      }
    }
  }

  .el-vue-amap-container {
    height: 164px;
    width: 100%;
  }

  .el-vue-amap-container .el-vue-amap {
    border-bottom-right-radius: 8px;
  }

  :deep(.t-cascader__item:hover:not(.t-is-expanded):not(.t-is-disabled)) {
    border-radius: 4px;
    background: var(--select-kyy_color_select_cascade_item_bg_hover, #f3f6fa) !important;
  }

  .util-box {
    display: flex;
    // width: 300px;
    // max-height: 362px;
    // min-height: 260px;
    padding: 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    flex-shrink: 0;
    border-radius: var(--addressSelector-kyy-radius_AddressSelector_option, 8px);
    background: var(--addressSelector-kyy_color_addressSelector_bg_default, #fff);

    .util-class::-webkit-scrollbar {
      width: 0px;
      height: 1px;
    }

    .util-class {
      display: flex;
      align-items: flex-start;
      gap: 24px;
      width: 284px;
      overflow-x: auto;
      height: 36px;
      align-self: stretch;
      border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
      padding-bottom: 0;

      .cla-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: -3px;
        height: 36px;
        cursor: pointer;

        .text {
          color: var(--text-kyy_color_text_1, #1a2139);
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
          /* 150% */
        }

        .text-act {
          color: var(--brand-kyy_color_brand_default, #4d5eff);

          /* kyy_fontSize_3/bold */
          font-family: 'PingFang SC';
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px;
          /* 150% */
        }

        .dot {
          width: 16px;
          height: 3px;
          border-radius: 1.5px;
          margin-top: 8px;
          background: var(--brand-kyy_color_brand_default, #4d5eff);
        }
      }
    }

    .util-list {
      .u-item {
        color: var(--addressSelector-kyy_color_addressSelector_option_text_hover, #1a2139);
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 142.857% */
        display: flex;
        width: 274px;
        height: 36px;
        min-height: 36px;
        max-height: 36px;
        padding: 0px 12px;
        align-items: center;
        gap: 12px;
        color: var(--addressSelector-kyy_color_addressSelector_option_text_hover, #1a2139);
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 142.857% */
        border-radius: var(--addressSelector-kyy-radius_AddressSelector, 4px);
        cursor: pointer;
      }

      .uact {
        color: var(--addressSelector-kyy_color_addressSelector_option_text_active, #4d5eff);
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 142.857% */
        background: var(--addressSelector-kyy_color_addressSelector_bg_active, #e1eaff);
      }

      .u-item:hover {
        background: var(--addressSelector-kyy_color_addressSelector_bg_active, #e1eaff);
      }
    }
  }

  :deep(.t-textarea) {
    position: relative;
  }

  :deep(.content .t-textarea__inner) {
    padding-bottom: 20px;
  }

  :deep(.textareaPadd .t-textarea__inner) {
    padding-right: 40px;
  }

  :deep(.title .t-textarea__inner) {
    padding-right: 40px;
  }

  :deep(.title .t-textarea__info_wrapper) {
    position: absolute;
    right: 10px;
    bottom: 7px;
  }

  :deep(.content .t-textarea__info_wrapper) {
    position: absolute;
    right: 4px;
    bottom: 1px;
    width: 99%;
    background: #fff;
  }

  :deep(.t-textarea__limit) {
    color: var(--text-kyy_color_text_5, #acb3c0);
    text-align: right;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
  }

  .step-tables {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    padding: 16px 24px;
    background: #fff;
    padding-top: 8px;

    .lable-se {
      display: flex;
      gap: 4px;
      color: var(--warning-kyy_color_warning_default, #fc7c14);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      align-items: center;
    }

    .table-box {
      width: 100%;

      .table-header {
        display: flex;
        padding-bottom: 12px;
        align-items: center;
        gap: 12px;
        align-self: stretch;

        .table-icon {
          font-size: 20px;
          color: #828da5;
          cursor: pointer;
        }

        .left {
          display: flex;
          align-items: center;
          gap: 4px;
          flex: 1 0 0;
          color: var(--checkbox-kyy_color_checkbox_text_default, #1a2139);

          /* kyy_fontSize_3/bold */
          font-family: 'PingFang SC';
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px;
          /* 150% */
        }

        .right {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 4px;

          .a-icon {
            color: #4d5eff;
          }
        }
      }

      .table-content {
        .self-tag {
          display: flex;
          height: 20px;
          width: 20px;
          padding: 2px 4px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: var(--kyy_radius_tag_s, 4px);
          background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
          color: #21acfa;
          text-align: center;
          font-family: PingFang SC;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          /* 166.667% */
          // float: left;
          margin-right: 4px;
        }

        .related-box {
          display: flex;
          align-items: center;
          // gap: 8px;
          flex-shrink: 0;
          align-self: stretch;

          .text {
            overflow: hidden;
            color: var(--checkbox-kyy_color_checkbox_text_active, #1a2139);
            text-overflow: ellipsis;
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            width: 100%;
            height: 22px;
            white-space: nowrap;
          }
        }
      }

      .table-check {
        display: flex;
        padding: 12px;
        // align-items: center;
        flex-direction: column;
        gap: 8px;
        flex: 1 0 0;
        align-self: stretch;
        border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
        background: var(--kyy_color_table_bg_default, #fff);
      }
    }
  }

  :deep(.t-tabs__bar.t-is-top) {
    width: 16px !important;
    //   // height: 3px!important;
    //   // margin: 0 23px!important;
    //   border-radius: 1.5px!important;
    //   background:#4D5EFF !important;
  }

  :deep(.t-input__limit-number) {
    color: var(--text-kyy_color_text_5, #acb3c0);
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  :deep(.unitlabel .t-form__label) {
    opacity: 0;
  }

  :deep(.quantitylabel) {
    margin-right: 0;
  }

  :deep(.t-select-option:not(.t-is-disabled):not(.t-is-selected):hover) {
    border-radius: var(--addressSelector-kyy-radius_AddressSelector, 4px);
    background: var(--addressSelector-kyy_color_addressSelector_bg_hover, #f3f6fa);
  }

  .bbr {
    border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  }

  .type-tag {
    margin-right: 4px;
  }

  .tableDisabled .type-tag {
    opacity: 0.6;
  }

  .tableDisabled .self-tag {
    background: #f0f9fe !important;
    color: var(--kyy_blue-kyy_color_kyyBlue_disabled, #a0dbfd) !important;
  }

  .t-select__dropdown .t-tabs__btn--right {
    border-left: none;
    background: #fff;
    width: 28px;
  }

  .t-select__dropdown .t-tabs__btn--left {
    border-right: none;
    background: #fff;
    width: 28px;
  }

  .unit-saerch-pan {}

  .ckdom {
    // display: none;
  }

  .type-re {
    display: flex;
    padding: 12px 0px;
    align-items: center;
    align-self: stretch;
    gap: 12px;
    margin-bottom: 12px;

    .mock-type-btn-a {
      display: flex;
      width: 88px;
      padding: 4px 24px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      border-radius: 16px;
      border: 1px solid var(--warning-kyy_color_warning_default, #fc7c14);
      background: var(--tagBG-kyy_color_tagBg_warning, #ffe5d1);
      cursor: pointer;
      color: var(--warning-kyy_color_warning_default, #fc7c14);
      text-align: center;

      /* kyy_fontSize_3/bold */
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      /* 150% */
    }

    .mock-type-btn-a2 {
      display: flex;
      width: 88px;
      padding: 4px 24px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      border-radius: 16px;
      border: 1px solid var(--cyan-kyy_color_cyan_active, #0ea197);
      background: var(--tagBG-kyy_color_tagBg_cyan, #e6f9f8);
      color: var(--cyan-kyy_color_cyan_default, #11bdb2);
      text-align: center;

      /* kyy_fontSize_3/bold */
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      /* 150% */
    }

    .mock-type-btn {
      border-radius: 16px;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);
      cursor: pointer;
      display: flex;
      width: 88px;
      padding: 4px 24px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      color: var(--text-kyy_color_text_3, #828da5);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      /* 150% */
    }

    .mock-type-btn:hover {
      // border: 1px solid var(--lingke-brand-hover, #707eff) !important;
      // background: var(--lingke-brand-12, rgba(76, 94, 255, 0.12)) !important;
      // color: var(--color-button-border-kyy-color-button-border-text-hover, #707eff) !important;
    }
  }

  .tip-box {
    display: flex;
    align-items: center;
    margin-left: 8px;

    .icon {
      width: 20px;
      height: 20px;
    }

    .tip-text {
      overflow: hidden;
      color: var(--warning-kyy_color_warning_default, #fc7c14);
      text-overflow: ellipsis;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      margin-left: 4px;
      margin-right: 12px;
    }

    .open-btn {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0px 12px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4d5eff);
      background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #eaecff);
      color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4d5eff);
      cursor: pointer;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .niche-lk-editors {
    width: 100%;
    /* min-height: 270px !important; */
    border: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4) !important;
    border-radius: 4px;

    .lk-toolbar {
      border-bottom: none !important;
      border-radius: 4px 4px 0 0 !important;
    }

    .ql-container {
      border-radius: 4px;
      border-top: none !important;
      // padding:0 12px;
      text-indent: 12px;
    }
  }

  .t-is-error .niche-lk-editors {
    border: 1px solid var(--color-button-secondary-error-kyy-color-button-secondary-error-border-dedault, #d54941) !important;
  }

  .selling_point-box {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    column-gap: 24px;

    .selling_point {
      display: flex;
      min-width: 220px;
      align-items: center;
      gap: 8px;

      .iconaddcircle {
        color: #4d5eff;
        font-size: 20px;
        cursor: pointer;
      }

      .iconremove {
        color: #d54941;
        font-size: 20px;
        cursor: pointer;
      }
    }
  }

  .type-tag1 {
    color: #ea8330;
  }

  .type-tag2 {
    color: #11BDB2;
  }

  .type-tag3 {
    color: #4d5eff;
  }

  .type-tag4 {
    color: #ed565c;
  }

  .fbox1 {
    background: rgb(255, 255, 255);
    padding: 16px 24px;
    border-radius: 4px;
  }

  .fbox2 {
    background: #fff;
    padding: 16px 24px;
    margin-top: 12px;
    border-radius: 4px;
  }

  .box-one-fff {
    background: #fff;
    padding: 16px 24px 0 24px;
  }

  .attr-boxbg {}

  .attr-box {
    display: flex;
    padding: 16px;
    width: 100%;
    align-items: flex-start;
    flex-direction: column;
    gap: 12px;
    align-self: stretch;
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
    border-radius: 8px;

    .iconremove {
      color: #828da5;
      font-size: 20px;
      cursor: pointer;
    }
  }

  :deep(.attr-box .t-form__controls-content) {
    flex-direction: column;
    background: none;
    gap: 12px;
  }

  .attr-item {
    width: 100%;
    display: flex;
    gap: 8px;
  }

  .linkboxs {
    display: flex;
    padding: 12px;
    gap: 12px;
    border-radius: 4px;
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
    margin-bottom: 32px;

    .fb {}

    .ckbox {
      min-width: 196px;
      height: 32px;
      display: flex;
      align-items: center;
    }
  }

  :deep(.fb .t-textarea__info_wrapper) {
    display: none;
  }
</style>