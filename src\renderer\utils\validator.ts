/**
 * 检查给定的字符串是否为有效的URL。
 *
 * 该函数使用正则表达式来匹配和验证URL的格式。它旨在匹配大多数常见的URL格式，
 * 包括http和https协议，域名，IPv4地址，端口号，路径，查询字符串和片段标识符。
 *
 * @param url 待验证的URL字符串。
 * @returns 如果字符串是有效的URL，则返回true；否则返回false。
 */
export function isValidUrl (url: string): boolean {
  const urlPattern = new RegExp(
    '^(https?:\\/\\/)?' + // protocol
    '((([a-zA-Z\\d]([a-zA-Z\\d-]*[a-zA-Z\\d])*)\\.)+[a-zA-Z]{2,}|' + // domain name
    '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
    '(\\:\\d+)?(\\/[-a-zA-Z\\d%_.~+]*)*' + // port and path
    '(\\?[;&a-zA-Z\\d%_.~+=-]*)?' + // query string
    '(\\#[-a-zA-Z\\d_]*)?$', // fragment locator
    'i'
  );
  return urlPattern.test(url);
};

/**
 * 检查给定的字符串是否为有效的中国大陆手机号。
 */
export function isValidPhone(phone: string): boolean {
  const phonePattern = /^1[3-9]\d{9}$/;
  return phonePattern.test(phone);
}
