import { showDialog } from '@renderer/utils/DialogBV';
import {  h, ref, reactive } from 'vue';
import { defineStore } from 'pinia'
import { logHandler } from "@renderer/log";
import useChatSendStore from './sendStore';
import { useMessageStore } from './store';
import { getImCardIds, getOpenid, setAccountAuthRouters, getCurrentAccount } from '@renderer/utils/auth';
import { getSessionLocalIdByCards, saveMergedMessageTemp, speedModeSaveCommon } from './utils';
import { copyImage } from '@renderer/utils/myUtils'
import {
  getMergedMessage,
  getDurationText,
  getConversationMessagePreview,
  getMsgSenderName,
  getConversationName,
  getSelectDialogData
} from './msgUtils';
import { searchMessages } from '@/views/message/service/msgHistory'

import { getVoiceMsgText } from './request'
import { useZhixingStore } from '@/views/zhixing/store';
import { postLaterEventsApi } from '@renderer/api/zhixing/api/todo'
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { msgEmit } from '@renderer/views/message/service/msgEmit'
import { useImToolStore } from '../tool/service/tool';
import { ImToolContainer, SceneTool } from '../tool/service/type';
import { i18nt } from "@/i18n";
import { openExternalMap } from '@renderer/components/common/map/utils';
import { createPlayground} from '@rk/editor';
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;
/**
 * store分为 data store 、 action store 、im store
 * - data store 用于存储数据
 * - action store 用于各种操作的中间状态，以及修改数据的方法
 * - im store 专门处理与融云连接相关的操作
 */
export const useChatActionStore = defineStore("chatAction", () => {

  const editorHtml = ref("");
  const messageHtml = ref("");//留言

  // 选中文本，用于复制
  const selectedText = ref('');
  const selectedHtml = ref('');

  // 是否正在选择消息
  const isSelecting = ref(false);

  // 当前右键消息
  const contextMsg = ref<MessageToSave>(null);

  // 重新编辑的消息
  const recallEdit = ref<MessageToSave>(null);

  // 转发模式
  const forwardType = ref<'one-by-one' | 'merged'>('one-by-one');
  // 转发消息选人弹窗，是否可见
  const forwardDialogVisiable = ref(false);
  // 转发消息选人弹窗参数
  const dialogData = ref({
    cardId: [],
    // teamId: [],
    // menus: [],
  });
  // 待转发消息
  const toForwardMessages = ref<MessageToSave[]>(null);
 
  // 消息点击事件
  const onClickMessage = (msg: MessageToSave) => {
    console.log(msg);
    if (msg.messageType !== 'text') {
      return;
    }
    switch (msg.contentExtra?.contentType) {
      case 'location': openLocationMsg(msg.contentExtra?.data); break;
      // case 'image': useGlobalDialogStore().showPictureViewer(msg); break;
      case 'image':
        previewFilesAction(msg);
        break;
      case 'video':
        previewFileAction({ url:msg.contentExtra?.data.videoUrl,type:msg.contentExtra?.data.contentType, size:msg.contentExtra?.data.size, duration: msg.contentExtra?.data.duration,height: msg.contentExtra?.data.height,width: msg.contentExtra?.data.width});
        break;
      case 'card_id_card': showCardWindow(msg.contentExtra.data); break;
      case 'PredefinedDayReminder':
        previewFilesAction(msg);
        break;
    }
  }

  // 图片视频单文件预览
  const previewFileAction = (data:PreviewFiles) => {
    const conversation = useMessageStore().chatingSession;


    // 图片data里面返回的type 可能会是image/png,从url获取格式
    const type = data.type || data.url?.split('.').pop()
    const title = data.title || data.url?.split('/').pop()
    console.log('预览', { ...data, type, title, myCardId: conversation.myCardId });

    const size = (data.size / 1024).toFixed(2);
    ipcRenderer.invoke(
      "preview-file",
      JSON.stringify({ ...data, size, type, title, myCardId: conversation.myCardId })
    );
  }

  // 群组多图图片预览
  const previewFilesAction = async (msg:MessageToSave) => {
    const conversation = useMessageStore().chatingSession;
    const searchParams = {
      conversationID: conversation.conversationID,
      keywordList: [''],
      senderUserIDList:[],
      subContentTypeList:['image'],
      messageTypeList:[101,114],
      pageIndex: 1,
      count: 1000
    }
    const temp = [];
    // 查不到历史或者融云老数据
    console.log('====>msg.clientMsgID',msg, msg.clientMsgID);
    if (msg.contentExtra?.contentType === 'PredefinedDayReminder') {
      temp.push(msg);
    }
    if (!msg.clientMsgID) {
      temp.push(msg);
    } else {
     const images = (await searchMessages(searchParams))?.result;
     if (!images?.length) {
      temp.push(msg);
     } else {
       temp.push(...images);
     }

    }
    let imgIndex = 0
    const images = []
    // 获取历史记录的图片需要反转顺序，左边为上一张按钮。
    temp?.reverse().map((item,ind)=>{
      if (item.messageUId === msg.messageUId) {
        imgIndex = ind;
      }
      const msgTyp = item.contentExtra?.contentType;
      const msgData = item.contentExtra?.data;

      if (msgTyp === 'image') {
        let url = msgData?.imgUrl
        if(!url) {
          // 上传失败用本地的图片
          if(msgData.toUploading?.path){
            url = `file://${msgData.toUploading.path}`
          }else{
            url = msgData?.thumbnail
          }
        }
        images.push({
          title: msgData?.imgUrl,
          url,
          type: 'image',
          width: msgData.width,
          size: msgData.size,
          myCardId: conversation.myCardId
        });
      } else if (msgTyp === 'PredefinedDayReminder') {
        images.push({
          title: msgData?.image,
          url: msgData?.image,
          type: 'image',
          myCardId: conversation.myCardId,
          width: msgData.width,
          size: msgData.size,
        });
      }
    });
    images.forEach(v=>{v.imgIndex = imgIndex})
    ipcRenderer.invoke(
      "preview-file",
      JSON.stringify(images)
    );
  }


  // 分享名片打开身份卡
  const showCardWindow = (data) => {
    const chatsession = useMessageStore().chatingSession;
    ipcRenderer.invoke("identity-card", { cardId: data.cardId, myId: chatsession.myCardId });
  }

  // 撤回的文字消息，重新编辑
  const onReEditMessage = (msg: MessageToSave) => {
    const chatsession = useMessageStore().chatingSession;
    let referMsg = null;

    // 统一处理两种引用消息格式
    if (msg.content?.referMsg) {
        const extra = JSON.parse(msg.content.referMsg.extra);
        referMsg = {
            conversationType: chatsession.conversationType,
            localSessionId: chatsession.localSessionId,
            targetId: chatsession.targetId,
            contentExtra: extra,
            senderUserId: extra.senderId,
            messageUId: msg.content.referMsgUid,
            messageType: msg.content.contentType,
            content: msg.content.referMsg.content,
            from: 'recall',
        };
    } else if (msg.revokedElem?.revokedMessage?.quoteElem?.quoteMessage) {
        const quoteMsg = msg.revokedElem.revokedMessage.quoteElem.quoteMessage;
        const ex = JSON.parse(quoteMsg.textElem.ex);
        referMsg = {
            conversationType: chatsession.conversationType,
            localSessionId: chatsession.localSessionId,
            targetId: chatsession.targetId,
            contentExtra: JSON.parse(ex.extra),
            senderUserId: quoteMsg.sendID,
            messageUId: quoteMsg.clientMsgID,
            messageType: quoteMsg.contentType,
            content: quoteMsg.textElem.content,
            from: 'recall',
        };
    }

    if (referMsg) {
        useMessageStore().onReplyEditing(referMsg);
    } else {
        useMessageStore().onCleanReferMsg();
    }

    recallEdit.value = { ...msg };
}

  // 右键菜单展示前先暂存数据
  const onShowContext = (msg: MessageToSave) => {
    contextMsg.value = msg;
  }
  const onHideContext = () => {
    contextMsg.value = null;
  }

  const onCopyMessage = async () => {
    console.log('=====>onCopyMessage', contextMsg.value);
    const data = contextMsg.value?.contentExtra?.data;
    const type = contextMsg.value?.contentExtra?.contentType;
    console.log('====>type', type, type === 'richText');
    if (type === 'richText') {
      console.log('====>richText');
      let copyText = data.text;
      let copyHtml = '';
      if (copyText) {
        try {
          const editor = await createPlayground({
            content: JSON.parse(copyText),
          })
          const html = editor.getHtml();
          copyHtml = html;
          copyText = editor.getText();
          editor.destroy();
          console.log('====>html', html);
        } catch (error) {
          console.log('====>error', error);
        }
      }
      try {
        if (copyHtml) {
          const blobHtml = new Blob([copyHtml], { type: "text/html" });
          const blobText = new Blob([copyText], { type: "text/plain" });
          const dataInfo = [new ClipboardItem({
            ["text/plain"]: blobText,
            ["text/html"]: blobHtml,
          })];
          navigator.clipboard.write(dataInfo)
        } else {
          // 纯文本复制
          navigator.clipboard.writeText(copyText);
        }
        MessagePlugin.success(i18nt("im.msg.copied"));
      } catch (error) {
        console.error('复制失败:', error);
        // 降级使用纯文本复制
        navigator.clipboard.writeText(copyText).then(
          () => MessagePlugin.success(i18nt("im.msg.copied")),
          () => MessagePlugin.error(i18nt("im.msg.copy_failed"))
        );
      }
    } else if (type === 'text') {
      let copyText = data.text;
      let copyHtml = '';
      // console.log('====>dgz data', data);
      if (data?.atInfo?.length) {
        const regex = /@([^~]+)~/g;
        let lastIndex = 0;
        let atIndex = 0;
        let match;
        if (selectedText.value && selectedHtml.value) {
          console.log('====>dgz copyHtml', selectedHtml.value);
          copyHtml = `<p class="custom-p" style="font-size: 14px;">` + selectedHtml.value + `</p>`;
          copyText = selectedText.value;
        } else {
          copyHtml += `<p class="custom-p" style="font-size: 14px;">`;
          while ((match = regex.exec(copyText)) !== null) {
            const atInfo = data.atInfo[atIndex++];
            const cardId = atInfo?.cardId ?? '';
            const openId = atInfo?.openId ?? '';
            const sessionId = atInfo?.sessionId ?? '';
            const name = match[1];
            copyHtml += copyText.slice(lastIndex, match.index);
            copyHtml += `<span class="mention" data-session-id="${sessionId}" data-value="${name}" data-card-id="${cardId}" data-open-id="${openId}" data-denotation-char="@">
              <span contenteditable="false">@${name}</span>
            </span>`;
            lastIndex = regex.lastIndex;
          }
          copyHtml += copyText.slice(lastIndex);
          copyHtml += `</p>`;
          copyText = copyText.replace(/@([^~]+)~/g, '@$1');
          // console.log('====>dgz copyHtml', copyHtml);
          // console.log('====>dgz copyText', copyText);
        }
      }
      // 匹配 [xxx]，xxx 不能包含 ]
      const emojiRegex = /\[([^\]]+)\]/g;
      if (copyText.match(emojiRegex)) {
        if (copyHtml.match(emojiRegex)) {
          copyHtml = copyHtml.replace(emojiRegex, (match, p1) => {
            return `<img src="${p1}" alt="${p1}" data-emoji="${p1}" data-alt="${p1}" />`;
          });
        } else {
          copyHtml += `<p class="custom-p" style="font-size: 14px;">`;
          copyHtml += copyText;
          copyHtml = copyHtml.replace(emojiRegex, (match, p1) => {
            return `<img src="${p1}" alt="[${p1}]" data-emoji="[${p1}]" data-alt="[${p1}]" />`;
          });
          copyHtml += `</p>`;
        }

      }

      if (copyHtml) {
        const blobHtml = new Blob([copyHtml], { type: "text/html" });
        const blobText = new Blob([copyText], { type: "text/plain" });
        const dataInfo = [new ClipboardItem({
          ["text/plain"]: blobText,
          ["text/html"]: blobHtml,
        })];
        navigator.clipboard.write(dataInfo)
      } else {
        // 纯文本复制
        (selectedText.value && data.text?.indexOf(selectedText.value) !== -1)
        ? navigator.clipboard.writeText(selectedText.value)
        : navigator.clipboard.writeText(copyText);
      }

        MessagePlugin.success(i18nt("im.msg.copied"));
    } else if (type === 'image') {
      copyImage(data.imgUrl)
    } else {
      // TODO: 其他类型的复制
    }
  }
  // 下载文件
  const onDownloadMessageFile = async () => {
    const data = contextMsg.value?.contentExtra?.data;
    if (contextMsg.value?.contentExtra?.contentType === 'file') {
       console.log('====>download-info', { title: data?.fileName, url: data?.fileUrl });
       const res = await ipcRenderer.invoke('download-file', { title: data?.fileName, url: data?.fileUrl });
       if(res){
        MessagePlugin.success(i18nt('im.public.download_suc'));
      }

    } else if(contextMsg.value?.contentExtra?.contentType === 'image') {
       console.log('====>download-info',  { title: data?.name || `${new Date().getTime()}.${data?.type}`, url: data?.imgUrl });
       const res = await ipcRenderer.invoke('download-file', { title: data?.name || `${new Date().getTime()}.${data?.type}`, url: data?.imgUrl });
        if(res){
          MessagePlugin.success(i18nt('im.public.download_suc'));
        }
    } else if (contextMsg.value?.contentExtra?.contentType === 'video') {
          // 数据中没有type和name，设置默认名称格式
         let temp1 = data?.videoUrl?.split('/');
         let temp2 = temp1[temp1.length - 1]?.split('.');
         let type = temp2[temp2.length - 1] ?? 'mp4';
         console.log('====>download-info',  { title: data?.videoName || `${new Date().getTime()}.${type}`, url: data?.videoUrl });
        const res =  await ipcRenderer.invoke('download-file', { title: data?.videoName || `${new Date().getTime()}.${type}`, url: data?.videoUrl });
        if(res){
          MessagePlugin.success(i18nt('im.public.download_suc'));
        }

    }
}

  const onDeleteMessages = async (messages: MessageToSave[]) => {
    messages.map(async msg => {
      await useChatSendStore().deleteMessageLocalStorage(msg.messageUId);
      useMessageStore().onDeleteMessageFromList(msg.messageUId);
      return msg.messageUId
    })
  }

  const onDeleteMessage = async () => {
    const messageUId = contextMsg.value.messageUId;
    const confirmDia = DialogPlugin({
      header: i18nt('im.public.delMsg'),
      body: i18nt('im.public.delMsgTips'),
      theme: 'warning',
      onConfirm: async () => {
        await useChatSendStore().deleteMessageLocalStorage(messageUId);
        useMessageStore().onDeleteMessageFromList(messageUId);
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
      onCancel: () => {
        confirmDia.hide();
      }
    });
  }

  const onRecallMessage = () => {
    useChatSendStore().recallMsg(contextMsg.value);
  }

  const onVoiceTextShow = async () => {
    const msg = contextMsg.value;
    const data = contextMsg.value?.contentExtra?.data as IMessageVoiceExtra;

    if (!data?.audioText) {
      const { convertVocice } = useChatVoiceStore();
      await convertVocice(msg);
    }

    msg.contentExtra.data.showText = true;
    // await onDbMessageExtraUpdate(msg);
  }


  const onVoiceTextHide = () => {
    const data = contextMsg.value?.contentExtra?.data as IMessageVoiceExtra;
    data.showText = false;
    // onDbMessageExtraUpdate(contextMsg.value);
  }
  const updateSpeed=ref(false)
  const onSaveCommon = async () => {
    updateSpeed.value = false;
    const msg = contextMsg.value;
    const typeList = {
      emoji_image: {
        label: i18nt('im.public.emoji'),
        type: 'APP_ACCOUNT_EMO'
      },
      image: {
        label: i18nt('im.public.img'),
        type: 'APP_ACCOUNT_PICTURE'
      },
      video: {
        label: i18nt('im.public.file'),
        type: 'APP_ACCOUNT_FILE'
      },
      file: {
        label: i18nt('im.public.file'),
        type: 'APP_ACCOUNT_FILE'
      },
      location: {
        label: i18nt('im.public.address'),
        type: 'APP_ACCOUNT_ADDRESS'
      },
      text: {
        label: i18nt('im.public.text'),
        type: 'APP_ACCOUNT_WORDS'
      },
      richText: {
        label: i18nt('im.public.text'),
        type: 'APP_ACCOUNT_WORDS'
      }
    }

    // 系统默认表情是文本类型 过滤@和默认表情 只留纯文本
    const contentType = msg.contentExtra.contentType;
    let res = await speedModeSaveCommon({
      type: typeList[contentType]?.type,
      openid: getOpenid(),
      msg
    })
    if(res === 'toLong') {
      MessagePlugin.error(i18nt('im.public.add_common_tolong'));
      return;
    }
    if (res) {
      MessagePlugin.success({
        offset: [0, 52],
        content: i18nt('im.public.add_common') + typeList[contentType]?.label
      });
      updateSpeed.value = true;
    } else {
      MessagePlugin.error({
        offset: [0, 52],
        content: i18nt('im.public.add_common_fail')
      })
    }
  };

  const onAddRemind = (data?: { title: string, content?: { icon: string, title: string, type: string, jumpId: string } }) => {
    const zhixingStore = useZhixingStore()
    // todo 待提供方法获取业务卡数据 { title: string, content: {icon: string, title: string, type: string, jumpId: string}}
    console.log('onAddRemind', contextMsg.value)
    if (data) {
      zhixingStore.addRemindDialogData.props = {
        title: data.title || '',
        content: data.content || null,
        noticeTyp: 'ONCE'
      }
    } else {
      // 相关数据
      const msgStore = useMessageStore();
      const msg = contextMsg.value;
      // 来源 id
      const fromId = msgStore.chatingSession.targetCardId;
      // 来源名，第二个参数表示是否取备注名，true表示取，false表示不取
      const fromName = getConversationName(msgStore.chatingSession, false);
      // 自己的身份卡
      const cardId = msgStore.chatingSession.myCardId;
      // 会话类型
      const typ = msgStore.chatingSession.conversationType === 3 ? 'GROUP' : 'PAIR';
      const props = {
        title: getConversationMessagePreview(msg, msgStore.chatingSession.myCardId),
        content: null,
        fromMsgInfo: { fromId, fromName, cardId, typ, text: '', conversationType: msg.conversationType }
      }
      zhixingStore.addRemindDialogData.props = props
    }
    zhixingStore.addRemindDialogData.type = 'add'
    zhixingStore.addRemindDialogData.visible = true
  }

  const onOpenRemindDetail = (data) => {
    const zhixingStore = useZhixingStore();
    zhixingStore.remindDetailDialogData.props = data;
    zhixingStore.remindDetailDialogData.type = data?.owner === getOpenid() ? 'edit' : 'detail';
    zhixingStore.remindDetailDialogData.visible = true;
  }
const sessionTyp = (conversationType) => {
  switch (conversationType) {
    case 1:
      return 'PAIR'
    case 6:
      return 'ASSISTANT'
    default:
      return 'GROUP'
  }
}
  const onAddRemindNew = (flag, isFromScene = false) => {
    const zhixingStore = useZhixingStore()

    const props = { title: '', content: null, fromMsgInfo: null }

    if (flag) {
      const msgStore = useMessageStore();
      // 来源 id
      const fromId = msgStore.chatingSession.targetCardId || msgStore.chatingSession.targetId;
      // 来源名，第二个参数表示是否取备注名，true表示取，false表示不取
      const fromName = getConversationName(msgStore.chatingSession, false);
      // 自己的身份卡
      const cardId = msgStore.chatingSession.myCardId;
      // 会话类型
      const typ = sessionTyp(msgStore.chatingSession.conversationType);
      props.fromMsgInfo = { fromId, fromName, cardId, typ, text: '', conversationType: msgStore.chatingSession.conversationType, isFromScene }
    }

    zhixingStore.addRemindDialogData.props = props;
    zhixingStore.addRemindDialogData.type = 'add'
    zhixingStore.addRemindDialogData.visible = true
    zhixingStore.addRemindDialogData.from = flag
  }

  const onAddLaterEvent = () => {
    const msg = contextMsg.value;
    const msgStore = useMessageStore();

    postLaterEventsApi({
      typ: sessionTyp(msgStore.chatingSession.conversationType),
      card_id: msgStore.chatingSession.myCardId,
      from_id: msgStore.chatingSession.targetCardId || msgStore.chatingSession.targetId,
      from_name: getMsgSenderName(msg),
      message_id: msg.messageUId,
      content: getConversationMessagePreview(msg, msgStore.chatingSession.myCardId)
    }).then((res) => {
      msgEmit.emit('later');
      res.data?.data ? MessagePlugin.success({
        offset: [0, 52],
        content: () => h('div', [
          i18nt('im.public.add_later_suc'),
          h('a', {
            onClick: () => {
              useImToolStore().toggleTool(ImToolContainer.Scene, { scene: SceneTool.later, sceneData: res.data.data })
            }
          }, i18nt('im.public.detail')),
        ])
      }) : MessagePlugin.success({
        offset: [0, 52],
        content: i18nt('im.public.del_later_suc')
      })
    })

  }

  const onStartSelecting = () => {
    useMessageStore().chatingMessages.forEach(c => {
      if (c.messageUId === contextMsg.value?.messageUId) {
        c.selected = true;
      }
    });
    onHideContext();
    isSelecting.value = true;
  }

  const onEndSelecting = (changeChat = false) => {
    isSelecting.value = false;
    if (!changeChat){
      useMessageStore().chatingMessages.forEach(c => {
        c.selected = false;
      });
    }
  }

  // 切换选人组件
  const toggleForwardDialogVisiable = (visiable = false) => {
    visiable && setDialogData();
    forwardDialogVisiable.value = visiable ?? !forwardDialogVisiable.value;
  }

  const setDialogData = () => {
    const chatsession = useMessageStore().chatingSession;
    console.log('====>chatsession', chatsession);
    if (!chatsession?.myCardId) {
      const currentAccountTemp = getCurrentAccount();
      logHandler({ name: '转发选人', info: `chatsession:${JSON.stringify(chatsession)}`, desc: `name:${currentAccountTemp?.name}; openid:${currentAccountTemp?.openid}；actionStore.ts->setDialogData` });
    }
    // dialogData.value = getSelectDialogData(chatsession.myCardId);
    dialogData.value = { cardId: [chatsession.myCardId] };
  };

  const onContextForward = () => {
    toForwardMessages.value = [contextMsg.value];
    forwardType.value = 'one-by-one';
    toggleForwardDialogVisiable(true);
  }

  const onSelectForward = (selectedList: MessageToSave[], isMerged: boolean) => {
    toForwardMessages.value = selectedList;
    forwardType.value = isMerged ? 'merged' : 'one-by-one';
    toggleForwardDialogVisiable(true);
    onEndSelecting();
  }

  // avatar: ""
  // cardId: "00hth6us0asqo"
  // conversationType: 3
  // from: "groups"
  // name: "真哥十二群"
  // openId: ""
  // select: false
  // targetId: "00hth6us0asqo"
  // team: ""
  // teamId: ""
  const onForwardConfirm = async (relations: any[]) => {
    console.log('===>relations',relations)
    if (relations.length === 0) {
      return;
    }
    let canSend = true
    const conversations = [];
    const { allMembers, chatingSession } = useMessageStore();
    relations.forEach(item => {
      if (item.conversationType === 1) {
        const myCardId = item?.attachment?.member?.find(c => c.cardId !== item.cardId)?.cardId;
        if(!myCardId || myCardId === item.cardId) {
          console.error('ERROR,选了自己',item,myCardId)
          MessagePlugin.error('不能给自己发消息');
          canSend = false
          return false
        }
        conversations.push({
          conversationType: 1,
          targetId: item.openId,
          localSessionId: getSessionLocalIdByCards(item.cardId, myCardId),
          myCardId: myCardId,
          targetCardId: item.cardId,
        })

      } else {
        const members = allMembers.get(item.targetId);
        const myCards = getImCardIds().filter(c => members?.get(c));
        const openId = getOpenid()
        const myCardId = myCards.find(c => c !== openId) || openId;
        conversations.push({
          conversationType: item.conversationType,
          targetId: item.targetId,
          localSessionId: getSessionLocalIdByCards(item.targetId, myCardId),
          myCardId,
          targetCardId: item.cardId,
        })
      }
    })
    if(!canSend) return false;
    console.log('conversations', conversations);
    const { forwardMsg, forwardMergedMsg } = useChatSendStore();
    if (forwardType.value === 'one-by-one') {
      conversations.forEach(conversation => {
          forwardMsg(toForwardMessages.value, conversation);
      });

    } else {
      // 合并消息
      const mergedMesssages = getMergedMessage(toForwardMessages.value, chatingSession);
      // 保存到临时文件
      const { filePath, ...others } = await saveMergedMessageTemp(mergedMesssages);
      // 交互优化修改为预览前四条
      const previewMessages = mergedMesssages.slice(0, 4);
      conversations.forEach(conversation => {
        forwardMergedMsg(previewMessages, { ...others, path: filePath }, conversation);
      })
    }

    MessagePlugin.success(i18nt('im.public.forwardSuc'));
  }

  // 消息界面iframe弹窗
  const isDialogIframe = ref(false);
  const dialogIframeData = ref<{[property: string]: any;}>({})
  const showDialogIframe = (show, data?) => {
    isDialogIframe.value = show;
    dialogIframeData.value = data || {};  
    console.log('===>isDialogIframe', isDialogIframe.value, dialogIframeData.value);
    
  } 
  return {
    selectedText,
    selectedHtml,
    editorHtml,
    messageHtml,
    recallEdit,
    isSelecting,
    forwardDialogVisiable,
    dialogData,
    contextMsg,
    updateSpeed,
    toForwardMessages,
    onClickMessage,
    onReEditMessage,
    onShowContext,
    onHideContext,
    onCopyMessage,
    onDeleteMessage,
    onDeleteMessages,
    onDownloadMessageFile,
    onRecallMessage,
    onVoiceTextShow,
    onVoiceTextHide,
    onStartSelecting,
    onEndSelecting,
    onContextForward,
    onSelectForward,
    toggleForwardDialogVisiable,
    onForwardConfirm,
    onAddRemind,
    onOpenRemindDetail,
    onAddRemindNew,
    onAddLaterEvent,
    onSaveCommon,
    previewFileAction,
    previewFilesAction,

    isDialogIframe,
    dialogIframeData,
    showDialogIframe,
  }
})

export const useChatVoiceStore = defineStore("chatVoice", () => {
  const percent = ref(0)
  const currentTime = ref(0);
  const currentTimeText = ref('0:00');

  const voicePlayTimer = ref(null);

  const playingMsg = ref<MessageToSave>(null);
  const playQueue = ref<MessageToSave[]>(null);

    let player: HTMLAudioElement = null;

    // 待转换的语音消息
    const convertings = reactive(new Map<string, 'loading' | 'error'>());

  const _startTimer = () => {
    if (playingMsg.value?.contentExtra?.data?.duration) {
      voicePlayTimer.value = setInterval(playing, 50);
    }
  }

  const _stopTimer = () => {
    voicePlayTimer.value && clearInterval(voicePlayTimer.value);
  }

  const playing = () => {
    currentTime.value += 50;
    const total = playingMsg.value.contentExtra?.data?.duration || 1;
    currentTimeText.value = getDurationText(Math.floor(currentTime.value / 1000));

    percent.value = Math.floor(currentTime.value / total / 20);
    if (percent.value >= 100) {
      _stopTimer();
    }
  }

  const playVoice = () => {
    console.log('=====>',playingMsg.value);
    if (!player) {
      player = new Audio(playingMsg.value.contentExtra?.data?.audioUrl);
      player.addEventListener('error', _stopTimer);
      player.addEventListener('abort', _stopTimer);
      player.addEventListener('playing', _startTimer);
      player.addEventListener('ended', stopVoice);

    } else {
      player.src = playingMsg.value.contentExtra?.data?.audioUrl
      player.load();
    }
    player.play();

      if (playingMsg.value.readTime === 1) {
          // 标记真实已读
          const readParams = {
              conversationID: playingMsg.value.conversationID,
              targetId: playingMsg.value.targetId,
              messageUId: playingMsg.value.targetId,
              sentTime: playingMsg.value.sentTime,
              readTime: Date.now(),
              receipts: null
          }
          // onDbMessageRead(readParams);
          useMessageStore().changeMsgRead(readParams);
      }
  }

  const stopVoice = () => {
      if (player) {
        player.pause();
        player.src = '';
        player = null;
      }
      playingMsg.value = null;
      percent.value = 0;
      currentTime.value = 0;
      currentTimeText.value = '0:00';
      clearInterval(voicePlayTimer.value);

      if (playQueue.value?.length) {
          playingMsg.value = playQueue.value.shift();
          playVoice();
          if (playQueue.value.length === 0) {
              playQueue.value = null;
          }
      }
  }

  const playVoiceMsg = (msg: MessageToSave) => {
      endVoiceMsgPlay();

      playingMsg.value = msg;
      playVoice();

      const store = useMessageStore();
      const list = store.chatingMessages.filter(item => {
          // 消息未读，且是语音消息，且在当前消息之后
          const isRead =  !item.msg.readTime || item.msg.readTime === 1;
          if (store.chatingSession.myCardId === item.msg.contentExtra?.senderId && item.msg.contentExtra?.contentType === 'voice' && item.msg.sentTime > msg.sentTime && isRead) {
              return true;
          }
          return false;
      });
      playQueue.value = list.length ? list.map(item => item.msg) : null;
  }

  const endVoiceMsgPlay = () => {
      playQueue.value = null;
      stopVoice();
  }

  const convertVocice = async (msg: MessageToSave) => {
    convertings.set(msg.messageUId, 'loading');
    try {
      const audioId = msg.contentExtra.data.audioId;
      const res = await getVoiceMsgText(audioId);
      if (res?.text) {
        msg.contentExtra.data.audioText = res.text;
      } else {
        msg.contentExtra.data.audioText = '';
        msg.contentExtra.data.convertError = 'error';
      }
      convertings.delete(msg.messageUId);

  } catch (error) {
      convertings.set(msg.messageUId, 'error');
    }
  }

  return {
      playingMsg,

      percent,
      currentTimeText,

      playVoiceMsg,
      endVoiceMsgPlay,

      convertings,
      convertVocice
  }
})

function openLocationMsg(data: any) {
  // data?.latLng?.latitude && data?.latLng?.longitude
  const { latitude, longitude } = data.latLng || {};
  openExternalMap({ lat: latitude, lng: longitude, name: data.title })

  /**
  if (data?.poiId) {
    const url = `https://www.amap.com/place/${data?.poiId}`;
    shell.openExternal(url);

  } else if (data?.latLng?.latitude && data?.latLng?.longitude) {
    const url = `https://www.amap.com/regeo?lng=${data.latLng.latitude}&lat=${data.latLng.longitude}`;
    shell.openExternal(url);
  }
  */
}
