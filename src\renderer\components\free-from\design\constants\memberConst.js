export default [
  {
    type: "BaseInfoMember", // 所使用的套件
    vModel: "baseInfoMember", // baseInfoMember基础信息 organizeInfoMember组织详情信息
    passiveRelevance: false,
    name: "基础信息",
    typeName: "基础信息",
    // addButtonText: "新增明细",
    value: [],
    fromType: "person", // 单位unit、个人person
    origin: [
      {
        type: "Divider",
        vModel: "divider",
        value: "",
        name: "基础信息",
        typeName: "基础信息",
        placeholder: "请输入",
        lineType: "noBorder", // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "efba144c-680b-4608-a71b-fbaa2c8b943c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }

      },
      {
        type: "Logo",
        vModel: "nameLogo",
        value: undefined,
        name: "名录照片",
        typeName: "名录照片",
        placeholder: "请上传",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        aspectRatio: 0.7142,
        id: "namfe-638b-4608-a71b-f644432b233c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      },

      {
        type: "Input",
        vModel: "name",
        value: "",
        name: "姓名",
        typeName: "姓名",
        placeholder: "请输入",
        required: true, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "efba144c-680b-4608-a71b-fbaa2c8b233c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      },
      {
        type: "Logo",
        vModel: "logo",
        value: undefined,
        name: "头像",
        typeName: "头像",
        placeholder: "请上传",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: "efba144c-680b-4608-a71b-fba234b233c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      },
      {
        type: "MemberLevel",
        vModel: "memberLevel",
        value: "",
        name: "会员级别",
        typeName: "会员级别",
        placeholder: "请选择",
        required: true, // 是否必填
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "efba144c-680b-4608-322b-fbaa2c8b233c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      },
      {
        type: "Phone",
        vModel: "phone",
        value: "",
        name: "手机号",
        typeName: "手机号",
        placeholder: "请输入",
        required: true, // 是否必填
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "efba23c-680b-4608-a71b-fbaa2c84233c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      },
      {
        type: "InputUnborder",
        vModel: "email",
        value: "",
        name: "邮箱",
        typeName: "邮箱",
        placeholder: "请输入",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "efba124c-6330b-4608-a71b-fbaa253b233c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      },
      {
        type: "TextArea",
        vModel: "interest",
        value: "",
        name: "兴趣爱好",
        typeName: "兴趣爱好",
        placeholder: "请输入",
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "u6722th4c-635630b-467808-a71b-fb909y3c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      },

      // {
      //   type: "Select",
      //   vModel: "department",
      //   value: "",
      //   name: "部门",
      //   typeName: "部门",
      //   placeholder: "请选择",
      //   required: false, // 是否必填
      //   disabled: true, // 是否禁用、是否可编辑
      //   isShow: false, // 是否显示
      //   id: "efba144c-630b-4608-a71b-fb1222b233c",
      //   parentInfo: {
      //     id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
      //     name: '基础信息'
      //   }
      // },
      {
        type: "Select",
        vModel: "joinTime",
        value: "",
        name: "入会时间",
        typeName: "入会时间",
        placeholder: "请选择",
        required: false, // 是否必填
        disabled: true, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: "efba144c-65b-4608-a71b-fbe2328b233c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      },
      {
        type: "Select",
        vModel: "expireTime",
        value: "",
        name: "到期时间",
        typeName: "到期时间",
        placeholder: "请选择",
        required: false, // 是否必填
        disabled: true, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: "efba345-680b-4608-a71b-fbaa328b233c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      },
      {
        type: "InputUnborder",
        vModel: "memberNum",
        value: "",
        name: "会员编号",
        typeName: "会员编号",
        placeholder: "请输入",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: "efba804c-646b-4608-a71b-fb3233c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      },

      {
        type: "Divider",
        vModel: "divider",
        value: "",
        name: "推荐人",
        typeName: "推荐人",
        placeholder: "请输入",
        lineType: "noBorder", // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "e3b24c-680b-4668-a77b-fba54c8b943c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }

      },
      {
        type: "Input",
        vModel: "reference",
        value: "",
        name: "推荐人姓名",
        typeName: "推荐人姓名",
        placeholder: "请输入",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "ef3344c-680b-444-a71b-f222c85333c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      },
      {
        type: "InputUnborder",
        vModel: "referenceUnit",
        value: "",
        name: "推荐人单位",
        typeName: "推荐人单位",
        placeholder: "请输入",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "e23r3344c-630b-444-a2b-f2g5333c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息'
        }
      }
    ],
    printable: true,
    editable: true,
    id: "e21906b7-c81f-4a73-aa76-c8cb65895eb2"
  },

  {
    type: "BaseInfoMember", // 所使用的套件
    vModel: "baseInfoMember", // baseInfoMember基础信息 organizeInfoMember组织详情信息
    passiveRelevance: false,
    name: "基础信息",
    typeName: "基础信息",
    // addButtonText: "新增明细",
    value: [],
    fromType: "unit", // 单位unit、个人person
    origin: [
      {
        type: "Divider",
        vModel: "divider",
        value: "",
        name: "基础信息",
        typeName: "基础信息",
        placeholder: "请输入",
        lineType: "noBorder", // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "5b1eedee-a3fb-4e02-8cd9-9308d88bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },
      {
        type: "Select",
        vModel: "organizeName",
        value: "",
        name: "组织名称",
        typeName: "组织名称",
        placeholder: "请选择",
        required: true, // 是否必填
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "5b1eedee-a332-4e02-842-93348d53bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },
      {
        type: "MemberLevel",
        vModel: "memberLevel",
        value: "",
        name: "会员级别",
        typeName: "会员级别",
        placeholder: "请选择",
        required: true, // 是否必填
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "5b143dee-a45-4642-7d9-93088bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },

      {
        type: "Select",
        vModel: "joinTime",
        value: "",
        name: "入会时间",
        typeName: "入会时间",
        placeholder: "请选择",
        required: false, // 是否必填
        disabled: true, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: "77eedee-66b-4e02-8cd9-93055bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },
      {
        type: "Select",
        vModel: "expireTime",
        value: "",
        name: "到期时间",
        typeName: "到期时间",
        placeholder: "请选择",
        required: false, // 是否必填
        disabled: true, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: "444edee-a44fb-442-8cd9-94488bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },
      {
        type: "InputUnborder",
        vModel: "memberNum",
        value: "",
        name: "会员编号",
        typeName: "会员编号",
        placeholder: "请输入",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: "221e-a3fb-44445-8cd9-9333bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },


      {
        type: "Divider",
        vModel: "divider",
        value: "",
        name: "单位负责人",
        typeName: "单位负责人",
        placeholder: "请输入",
        lineType: "noBorder", // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "db133dee-a3fb-452-8cd9-934321c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },


      {
        type: "Input",
        vModel: "relateRespector",
        value: "",
        code_name: '', // 代表人姓名
        code_value: null, // 存储选择代表人对象
        name: "关联代表人",
        typeName: "关联代表人",
        placeholder: "请输入",
        required: true, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: "77432e-a366-5p502-8cd9-95hj81c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        },
        options: [
          { label: '新建代表人', value: 1 },
          { label: '选择代表人', value: 2 }
        ]
      },


      {
        type: "Input",
        vModel: "name",
        value: "",
        name: "负责人姓名",
        typeName: "负责人姓名",
        placeholder: "请输入",
        required: true, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "3b14edee-a3fb-48502-8cd9-930588781c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },

      {
        type: "InputUnborder",
        vModel: "unitJob",
        value: "",
        name: "所在单位岗位",
        typeName: "所在单位岗位",
        placeholder: "请输入代表人在该单位会员的岗位",
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "3bgj6ee-a3fb-4702-8cd9-930898781c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },
      {
        type: "Logo",
        vModel: "logo",
        value: undefined,
        name: "负责人头像",
        typeName: "负责人头像",
        placeholder: "请上传",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: "55eedee-a66b-4772-8cd9-9308d88bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },

      {
        type: "Logo",
        vModel: "nameLogo",
        value: undefined,
        name: "名录照片",
        typeName: "名录照片",
        placeholder: "请上传",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        aspectRatio: 0.7142,
        id: "3558b-4t8-a71b-f700ssc",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },

      {
        type: "PhoneUnborder",
        vModel: "phone",
        value: "",
        name: "负责人手机号",
        typeName: "负责人手机号",
        placeholder: "请输入",
        code_value: 86,
        code_placeholder: '请选择',
        required: true, // 是否必填
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "5b1ee7dee-a3fb-4e02-8cd9-9307980d21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },
      {
        type: "Input",
        vModel: "email",
        value: "",
        name: "邮箱",
        typeName: "邮箱",
        placeholder: "请输入",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "5b22dee-a39-4e02-8cd9-93078bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },

      {
        type: "TextArea",
        vModel: "interest",
        value: "",
        name: "兴趣爱好",
        typeName: "兴趣爱好",
        width: '100%',
        placeholder: "请输入",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "u666c-638830b-46908-a71b-fp9y3c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },


      // {
      //   type: "Select",
      //   vModel: "department",
      //   value: "",
      //   name: "部门",
      //   typeName: "部门",
      //   placeholder: "请选择",
      //   required: false, // 是否必填
      //   disabled: true, // 是否禁用、是否可编辑
      //   isShow: false, // 是否显示
      //   id: "333dee-a3fb-4e02-8cd9-944488bd21c",
      //   parentInfo: {
      //     id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
      //     name: '基础信息'
      //   }
      // },

      {
        type: "Divider",
        vModel: "divider",
        value: "",
        name: "推荐人",
        typeName: "推荐人",
        placeholder: "请输入",
        lineType: "noBorder", // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "d25dee-a3fb-4666621c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },
      {
        type: "Input",
        vModel: "reference",
        value: "",
        name: "推荐人姓名",
        typeName: "推荐人姓名",
        placeholder: "请输入",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "6666-a3fb-4e02-8cd9-9306668bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      },
      {
        type: "InputUnborder",
        vModel: "referenceUnit",
        value: "",
        name: "推荐人单位",
        typeName: "推荐人单位",
        placeholder: "请输入",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "6336-a3fb-4e02-8cd9-93056bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息'
        }
      }
    ],
    printable: true,
    editable: true,
    id: "e21906b7-c81f-4a73-aa76-c8cb65895eb6"
  },
  {
    type: "BaseInfoMember",
    vModel: "organizeInfoMember",
    passiveRelevance: false,
    name: "组织详细信息",
    typeName: "组织详细信息",
    // addButtonText: "新增明细",
    value: [],
    fromType: "unit", // 单位unit、个人person
    origin: [
      {
        type: "Divider",
        vModel: "divider",
        value: "",
        name: "组织详细信息",
        typeName: "组织详细信息",
        placeholder: "请输入",
        lineType: "noBorder", // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "5b1eedee-a3fb-4e02-8cd9-44445558bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息'
        }
      },
      {
        type: "Logo",
        vModel: "organizeLogo",
        value: undefined,
        name: "组织logo",
        typeName: "组织logo",
        placeholder: "请上传",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "aaaee-a3fb-4e02-8cd9-44445558bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息'
        }
      },
      {
        type: "Input",
        vModel: "organizeAbbrName",
        value: "",
        name: "组织简称",
        typeName: "组织简称",
        placeholder: "请输入",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "33334edee-a3fb-4e02-34679-4444558bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息'
        }
      },

      {
        type: "Select",
        vModel: "organizeType",
        value: "",
        name: "组织类型",
        typeName: "组织类型",
        placeholder: "请选择",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "5b13323-323-45-6-d21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息'
        }
      },
      {
        type: "Select",
        vModel: "industryType",
        value: undefined,
        name: "行业类型",
        typeName: "行业类型",
        placeholder: "请选择",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "512ee-a32b-4e02-8c22-44441c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息'
        }
      },
      {
        type: "Select",
        vModel: "organizeScale",
        value: undefined,
        name: "组织规模",
        typeName: "组织规模",
        placeholder: "请选择",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "3fedee-a3ff-4ff2-8cd9-445h8bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息'
        }
      },

      {
        type: "TextArea",
        vModel: "business",
        value: "",
        name: "业务范围",
        typeName: "业务范围",
        placeholder: "请输入",
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        width: '100%',
        id: "unnc-638nn30b-469nn8-anb-fccy3c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息'
        }
      },

      {
        type: "Address",
        vModel: "organizeAddress",
        value: [],
        address_value: undefined, // 详细地址
        address_placeholder: "请输入详细地址",

        name: "组织地址",
        typeName: "组织地址",
        placeholder: "请选择",
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: "5b2-a3fb-4e02-8cd9-44bd21c",
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息'
        }
      }
    ],
    printable: true,
    editable: true,
    id: "e21906b7-c81f-4a73-aa76-c8cb65895e22"
  },
];
