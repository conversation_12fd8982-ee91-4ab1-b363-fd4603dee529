<template>
  <div>
    <t-dialog :on-close="closeBv" class="dialogSet dialogNoDefault dialogSetHeader dialogSet-noBodyPadding drawerSetForm contactsJoinOrg" v-model:visible="curStep[0]" :closeBtn="false"
    :footer="currentWay.value !== ways[1].value"
    :header="t('contacts.joinOrg')" width="480">
      <template #header>
        <div class="custom-header ">
          <span> {{ t('contacts.joinOrg') }} </span>
          <div class="tricks">
            <Tricks size="small" :isDrag="false"  uuid="通讯录-加入组织" />
          </div>
        </div>
        <iconpark-icon name="iconerror" class="btn-close" @click="curStep[0] = false; closeBv()" />
      </template>
      <template #body>
        <div class="boxtag mt-16px">
          <div class="tabs">
            <span v-for="(item, itemIndex) in ways" :key="itemIndex" :class="{'tabs-item':true, 'cursor': true, 'active': item.value === currentWay.value }" @click="onChangeWay(item)">{{ item.label }}</span>
          </div>
        </div>
        <div class="toBody" :style="{'padding-bottom': currentWay.value === ways[1].value ?'24px': '0'}">
          <div class="scroll" v-show="currentWay.value === ways[0].value">
            <div class="body-tip f-align">{{ t('contacts.joinOrgTip1') }}
              <t-tooltip placement="bottom" show-arrow>
                <img style="margin: 0 4px;" src="@/assets/icon_help.svg" alt="">
                <template #content>
                  <div>{{ t('contacts.explain1') }}</div>
                </template>
              </t-tooltip>/{{ t('contacts.joinOrgTip2') }}
              <t-tooltip placement="bottom" show-arrow>
                <img style="margin: 0 4px;" src="@/assets/icon_help.svg" alt="">
                <template #content>
                  <div>{{ t('contacts.explain2') }}</div>
                </template>
              </t-tooltip>/ {{ t('contacts.joinOrgTip3') }}
            </div>
            <t-form
              reset-type="initial"
              label-align="top"
              :data="formData"
              :rules="formRules"
            >
              <t-form-item>
                <t-input v-model="seatchOrgWord" :maxlength="50" :placeholder="t('contacts.joinInputTip')" />
              </t-form-item>
            </t-form>
          </div>
          <div class="digital" v-show="currentWay.value === ways[1].value">
            <div class="serch-bar">
              <t-input
                  ref="inputRef"
                  v-model="keyword"
                  autofocus
                  :placeholder="`请输入要搜索的组织名称/简称`"
                  clearable
                  @clear="onSearch"
                  @change="onSearch"
                >
                  <template #prefix-icon>
                    <iconpark-icon name="iconsearch" class="name-icon" />
                  </template>
              </t-input>
            </div>
            <div class="box">
              <div class="listScroll"
                v-infinite-scroll="handleInfiniteOnLoad"
                :infinite-scroll-immediate-check="false"
                :infinite-scroll-disabled="scrollDisabled"
                infinite-scroll-watch-disabled="scrollDisabled"
                :infinite-scroll-distance="20"
                v-if="datas?.length > 0"
              >
                <div class="item" v-for="item in datas" :key="item?.teamId">
                  <span class="sleft">
                    <t-image class="logo" :src="item?.logo" fit="cover">
                      <template #loading>
                        <img :src="ORG_DEFAULT_AVATAR" />
                      </template>
                      <template #error>
                        <img :src="ORG_DEFAULT_AVATAR" />
                      </template>
                    </t-image>
                    <div class="con">
                      <span class="tip line-1"  v-html="highlightText(item?.exclusive_name || item?.fullName, keyword?.trim())"></span>
                      <span class="type government" v-show="type.Government === item?.platform_uuid">
                        <img :src="government" class="img">
                        数字城市
                      </span>
                      <span class="type member"  v-show="type.Member === item?.platform_uuid">
                        <img :src="member" class="img">
                        数字商协
                      </span>
                      <span class="type cbd" v-show="type.Cbd === item?.platform_uuid">
                        <img :src="cbd" class="img">
                        数字CBD
                      </span>
                      <span class="type association" v-show="type.Association === item?.platform_uuid">
                        <img :src="association" class="img">
                        数字社群
                      </span>
                        <span class="type school" v-show="type.Uni === item?.platform_uuid">
                        <img :src="schoolicon" class="img">
                        数字高校
                      </span>

                    </div>
                  </span>
                  <span class="sright">
                    <t-button theme="primary" class="max-w-80px btn" @click="onInvitApply(item)">申请加入</t-button>
                  </span>
                </div>

                <div class="no-more" v-show="listCount <= datas?.length">
                  <span class="line"></span>
                  <span class="more">没有更多了</span>
                  <span class="line"></span>
                </div>
              </div>
              <div class="cont" v-show="!datas || datas.length < 1">
                <Empty
                  :tip="keyword?.trim() ?  '搜索无结果': `输入关键词搜索`"
                  :name="keyword?.trim() ? 'no-result' : 'no-data-new'"
                />
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="footer">
          <t-button
            class="btn cancel"
            variant="outline"
            theme="default"
            @click="curStep[0] = false; closeBv()"
          >{{ t('account.cancel') }}</t-button>
          <t-button
            class="btn confirm"
            theme="primary"
            variant="base"
            :disabled="!seatchOrgWord"
            @click="checkOrgList()"
          >{{ t('contacts.nextStep') }}</t-button>
        </div>
      </template>
    </t-dialog>
    <t-dialog v-model:visible="curStep[1]" :on-close="closeBv" :header="t('contacts.checkEmail')" width="384">
      <template #body>
        <div>
          <div class="body-tip mb24">{{ t('contacts.checkEmailTip') }}</div>
          <t-form
            reset-type="initial"
            label-align="top"
            :data="formData"
            :rules="formRules"
          >
            <t-form-item>
              <t-input v-model="emailCode" :placeholder="t('account.inputCode')" />
            </t-form-item>
          </t-form>
          <div v-if="countdown <= 0">{{ t('contacts.emailCodeResend') }}
            <span
              v-if="countdown <= 0"
              class="verify pointer"
              aria-role="button"
              @click="getEmailCode"
            >{{ t('account.sendCode') }}
            </span>
          </div>
          <div v-else class="verify">{{ `${t('account.resend')}${countdown}` }}</div>
        </div>
      </template>
      <template #footer>
        <div>
          <t-button
            class="btn cancel"
            variant="outline"
            theme="default"
            @click="preStep(1)"
          >{{ t('contacts.preStep') }}</t-button>
          <t-button
            class="btn confirm"
            theme="primary"
            variant="base"
            :disabled="!emailCode"
            @click="checkEmailCode"
          >{{ t('contacts.nextStep') }}</t-button>
        </div>
      </template>
    </t-dialog>
    <t-dialog v-model:visible="curStep[2]" :on-close="closeBv" :header="t('contacts.setPersonal')" width="384">
      <template #body>
        <div style="max-height: 400px; overflow: auto">
          <div class="body-tip mb24">{{ t('contacts.joinPersonalTip') }}</div>
          <t-form
            ref="formStep"
            reset-type="initial"
            label-align="top"
            :data="formData"
            :rules="formRules"
          >
            <t-form-item :label="t('contacts.userName')" name="name">
              <t-input v-model="formData.name" :maxlength="50" :placeholder="t('contacts.pleaseInput')" />
            </t-form-item>
            <t-form-item :label="t('contacts.tel')" name="phone" class="tel">
              <t-input-adornment>
                <template #prepend>
                  <div>
                    <area-code v-model="formData.telCode" />
                  </div>
                </template>
                <t-input v-model="formData.phone" :placeholder="t('account.inputTel')" />
              </t-input-adornment>
            </t-form-item>
            <t-form-item :label="t('contacts.verifyCode')" name="code">
              <t-input v-model="formData.code" :placeholder="t('account.inputCode')">
                <template #suffix>
                  <div style="display: flex; align-items: center">
                    <div class="suffix--line" />
                    <div
                      v-if="countdown <= 0"
                      class="verify pointer"
                      aria-role="button"
                      @click="checkSM"
                    >{{ t('account.sendCode') }}</div>
                    <div v-else class="verify">{{ `${t('account.resend')}${countdown}` }}</div>
                  </div>
                </template>
              </t-input>
            </t-form-item>
            <t-checkbox v-model="agreeProto" style="margin-top: 15px"><span>{{ t('account.read') }}</span><a href="javascript:void(0)" @click="showIframe('PlatformServices')">{{ t('account.agreement') }}</a><a href="javascript:void(0)" @click="showIframe('PrivacyPolicy')">{{ t('account.privacy') }}</a></t-checkbox>
          </t-form>
        </div>
      </template>
      <template #footer>
        <div>
          <t-button
            class="btn cancel"
            variant="base"
            theme="primary"
            @click="preStep(2)"
          >{{ t('contacts.preStep') }}</t-button>
          <t-button
            class="btn confirm"
            theme="primary"
            variant="base"
            style="width:78px"
            :disabled="joinDisabled"
            @click="joinTeam"
          >{{ t('identity.confirm') }}</t-button>
        </div>
      </template>
    </t-dialog>
    <t-dialog
      v-model:visible="curStep[3]"
      :header="false"
      :close-btn="false"
      width="384"
    >
      <template #body>
        <div class="suc-body">
          <div class="suc-icon">
            <img src="@renderer/assets/svg/icon_success.svg" alt="">
          </div>
          <div class="tip">{{ joinType === 1 ? t('contacts.joinSuc') : t('contacts.applySuc') }}</div>
          <div class="sub-tip">{{ joinType === 1 ? `${t('contacts.joinSucTip')} ${teamName}` : t('contacts.waitVerify') }}</div>
        </div>
      </template>
      <template #footer>
        <div class="footer">
          <t-button
            class="btn confirm"
            theme="primary"
            variant="base"
            style="width:86px"
            @click="curStep[3] = false; closeBv()"
          >{{ t('contacts.btnClose') }}</t-button>
        </div>
      </template>
    </t-dialog>

    <t-dialog
      v-model:visible="notifivisible"
      theme="info"
      width="384"
      :header="t('account.tip')"
      :close-btn="false"
    >
      <div>
        <span style="color: #717376">{{ t('account.pleaseAgree') }}</span><a href="javascript:void(0)" @click="showIframe('PlatformServices')">{{ t('account.agreement') }}</a><a href="javascript:void(0)" @click="showIframe('PrivacyPolicy')">{{ t('account.privacy') }}</a>
      </div>
      <template #footer>
        <div>
          <t-button
            class="btn cancel"
            variant="outline"
            theme="default"
            @click="notifivisible = false"
          >{{ t('account.cancel') }}</t-button>
          <t-button
            class="btn confirm"
            theme="primary"
            variant="base"
            @click="confirmAgree"
          >{{ t('account.agree') }}</t-button>
        </div>
      </template>
    </t-dialog>

    <t-dialog
      v-model:visible="changeOrgListVisible"
      class="changeOrgListVisible"
      width="384"
      :header="t('contacts.selectOrg')"
      :close-btn="false"
      :closeOnEscKeydown="false"
      :closeOnOverlayClick="false"
      @close="changeOrg(null)"
    >
      <template #body>
        <div style="padding-bottom: 6px;color: #000;font-size: 14px;font-weight: 400;">{{ t('contacts.selectOrgIn') }}</div>
        <div style="overflow: auto;max-height: 300px;">
          <t-card :class="['org-card', activeOrgInfo?.idTeam === item.idTeam ? 'active-card' : 'default-card']" @click="changeOrg(item)" v-for="item in orgList" :key="item.idTeam" bordered>
            <div class="card-box">
              <t-avatar style="border-radius: 50%;" shape="round" size="44px" :image="item.logo || defaultOrgLogo"></t-avatar>
              <div class="org-card-info">
                <div class="name">{{ item.fullName }}</div>
                <div class="value">{{ `${t('contacts.orgID')}: ${item.teamId}` }}</div>
              </div>
              <img v-if="activeOrgInfo?.idTeam === item.idTeam" src="@renderer/assets/svg/radioButton_default.svg" alt="">
            </div>
          </t-card>
        </div>
      </template>
      <template #footer>
        <div>
          <t-button
            class="btn cancel"
            variant="outline"
            theme="default"
            @click="closeOrgListVisible"
          >{{ t('contacts.preStep') }}</t-button>
          <t-button
            class="btn confirm"
            theme="primary"
            variant="base"
            :disabled="!activeOrgInfo"
            @click="checkOrgListNext"
          >{{ t('contacts.nextStep') }}</t-button>
        </div>
      </template>
    </t-dialog>

    <tip v-model:visible="tipVisible" :tip="checkPhoneTip" :btn-confirm="t('zx.other.confirmChange')" :btn-cancel="t('account.cancel')" @onconfirm="changeRegion" />
  </div>
</template>

<script lang="ts" setup>
import { ref, nextTick, watch, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import tip from '@renderer/views/setting/dialog/tip.vue';
import { MessagePlugin } from 'tdesign-vue-next';
import Empty from "@renderer/components/common/Empty.vue";
import {
  getSms,
  searchOrg,
  joinOrg,
  getEmailSms,
  checkEmailSms,
  searchOrgList, checkJoinOrg
} from '@renderer/api/contacts/api/organize';
import areaCode from '@renderer/components/account/AreaCode.vue';
import { getProfilesInfo } from '@renderer/utils/auth';
import { checkPhoneAndMatch } from '@renderer/components/account/util';
import _ from 'lodash';
import defaultOrgLogo from '@renderer/assets/defaultOrgLogo.svg';
import to from 'await-to-js';
import { getSMCaptcha, getSMCaptchaResult } from '@renderer/utils/shumei';
import { getByAppUuidGetTeamApi } from '@renderer/api/digital-platform/api/businessApi';
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
// import digital from "@renderer/assets/member/svg/digital.svg";
import association from "@renderer/assets/member/svg/association.svg";
  import schoolicon from "@renderer/assets/member/svg/schoolicon.svg";
import government from "@renderer/assets/member/svg/government.svg";
// import business from "@renderer/assets/member/svg/business.svg";
import member from "@renderer/assets/member/svg/member.svg";
import cbd from "@/assets/member/svg/cbd.svg";
// import { getResponseResult } from '@renderer/utils/myUtils';
import { getMemberApplyLinkAxios  as getMemberLinkAxios} from '@renderer/api/member/api/businessApi';
import { getMemberApplyLinkAxios as getPoliticsLinkAxios} from '@renderer/api/politics/api/businessApi';
import { getMemberApplyLinkAxios as getCbdLinkAxios } from '@renderer/api/cbd/api/businessApi';
import { getMemberApplyLinkAxios as getAssociationLinkAxios } from '@renderer/api/association/api/businessApi';
import { getMemberApplyLinkAxios as getUniLinkAxios } from '@renderer/api/association/api/businessApi';
import { getResponseResult } from '@renderer/utils/myUtils';
import { inviteUrl } from '@renderer/utils/baseUrl';
import Qs from "qs";
import { jumpH5WithLink } from "@renderer/views/contacts/utils";
import LynkerSDK from '@renderer/_jssdk';

const userInfo = getProfilesInfo();
const { t } = useI18n();
const curStep = ref([false, false, false, false]);
const joinDisabled = ref(true);
const formStep = ref(null);
const seatchOrgWord = ref('');
const emailCode = ref('');
const formData = ref({
  name: userInfo.title || '',
  telCode: userInfo.account_mobile_region || '86',
  phone: userInfo.account_mobile || '',
  code: '',
  teamId: '',
  from: '',
});
const formRules = ref({
  name: [
    { required: true, message: t('contacts.pleaseInput'), type: 'error' },
  ],
  telCode: [
    { required: true, message: t('contacts.pleaseInput'), type: 'error' },
  ],
  phone: [
    { required: true, message: t('contacts.pleaseInput'), type: 'error' },
  ],
  code: [
    { required: true, message: t('contacts.pleaseInput'), type: 'error' },
  ],
});

const ways = [
  {
    label: '加入内部组织', value: 'link'
  },
  {
    label: '加入数字平台', value: 'qrcode'
  },
]

const currentWay = ref(ways[0]);

const onChangeWay = (tab) => {
  currentWay.value = tab;
}

// 加入数字平台
const keyword = ref('');
enum type  {
  Member = 'member',
  Government = 'government',
  Cbd = 'cbd',
  Uni = 'uni',
  Association = 'association',
}
const datas: any = ref([]);

let listCount = ref(0);
const pagination = {
  pageSize: 20,
  page: 1,
  // total: 0,
};
const handleInfiniteOnLoad = () => {
  console.log("handleInfiniteOnLoad");
  // 异步加载数据等逻辑
  if (scrollDisabled.value) {
    // 数据加载完毕
  } else {
    // 加载数据列表
    pagination.page++;
    onGetOrgList().then((res:any)=> {
      datas.value = datas.value.concat(
        datas.value = res?.list?.map((v:any)=> {
          if(v.title) {
            v.fullName = v.fullName + `（${v.title}）`
          }
          return v;
        }) || []
      );
      listCount.value = res?.count || 0;
    });
  }
};

const scrollDisabled = computed(() => {
  console.log(datas.value.length, listCount.value, datas.value.length >= listCount.value);
  console.log(listCount.value, "dd", listCount.value > 0 ? datas.value.length >= listCount.value : false);
  return listCount.value > 0 ? datas.value.length >= listCount.value : false;
  // return memberLevels.value.length >= listCount ;
});

const highlightText = (text, searchTerm) => {
  // 如果搜索词为空，则直接返回原始文本
  if (!searchTerm) {
    return text;
  }

  // 使用正则表达式删除HTML标签
  text = text.replace(/<[^>]+>/g, "");

  // 使用正则表达式进行全局不区分大小写的文本匹配
  const regex = new RegExp(searchTerm, "gi");

  // 使用 <span> 标签包裹匹配到的文本，并添加样式
  const highlightedText = text.replace(
    regex,
    (match) => `<span class="highlight">${match}</span>`
  );

  return highlightedText;
};


const onGetOrgList = () => {
  return new Promise(async (resolve, reject) => {
    const [err2, res2]: any = await to(getByAppUuidGetTeamApi({name: keyword.value?.trim(), ...pagination }));
    if (err2) {
      reject(err2);
      return;
    }
    const { data } = res2?.data;
    resolve(data)
  });
}
const onSearch = ()=> {
  pagination.page = 1;
  console.log('kak', Boolean(keyword.value?.trim()))
  if(keyword.value && keyword.value?.trim()) {
    onGetOrgList().then((res:any)=> {
      console.log(res)
      datas.value = res?.list?.map((v:any)=> {
        if(v.title) {
          v.fullName = v.fullName + `（${v.title}）`
        }
        return v;
      }) || [];
      listCount.value = res?.count || 0;
    });
  } else {
    datas.value = [];
  }

}

// 获取邀请链接
const getInviteLinkAxios = (item, handlFunc) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await handlFunc({}, item.teamId);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if(errMsg === 'Network Error' ) {

      } else {
        MessagePlugin.error(errMsg);
      }
    }
  });
};

const onInvitApply = (item: any) => {
  let url = ''
  let path = ""
  let handleFunc: Function = ()=> {};
  switch (item?.platform_uuid) {
    case type.Member:
      path = "/account/jump?to=memberInvite";
      handleFunc = getMemberLinkAxios;
      break;
    case type.Government:
      path = "/account/jump?to=politicsInvite"
      handleFunc = getPoliticsLinkAxios;
      break;
    case type.Cbd:
      path = "/account/jump?to=cbdInvite"
      handleFunc = getCbdLinkAxios;
      break;
    case type.Association:
      path = "/account/jump?to=associationInvite"
      handleFunc = getAssociationLinkAxios;
      break;
         case type.Uni:
      path = "/account/jump?to=uniInvite"
      handleFunc = getUniLinkAxios;
      break;
    default:
      break;
  }

  getInviteLinkAxios(item, handleFunc).then((res: any)=> {
    if (res) {

      let params = {
        link: res.link,
      }
      url = `${inviteUrl}${path}&${Qs.stringify(params, { encode: true })}`
    }
    console.log(url)
    // shell.openExternal(url);
    jumpH5WithLink(url);
  })
}







const countdown = ref(0);
const agreeProto = ref(false);
const notifivisible = ref(false);
const changeOrgListVisible = ref(false);
const orgList = ref([]);
const activeOrgType = ref('');
const activeOrgInfo = ref(null);
const isEmail = ref(false);
const timer = ref(null);
const joinType= ref();
const teamName = ref('');
const tipVisible = ref(false);
const checkPhoneTip = ref('');
let checkRegion = 0;
const emits = defineEmits(['sucess', 'close-bv']);
const changeRegion = () => {
  tipVisible.value = false;
  formData.value.telCode = checkRegion.toString();
  formData.value.code ? joinTeam() : getCode();
};

const checkPhone = () => {
  checkRegion = checkPhoneAndMatch(+formData.value.telCode, formData.value.phone);
  if (!checkRegion) {
    MessagePlugin.error({
      content: t('zx.account.phoneIllegal'),
      duration: 3000,
    });
    return false;
  }
  if (checkRegion !== +formData.value.telCode) {
    checkPhoneTip.value = `${t('zx.account.checkPhoneRegion1')}“+${checkRegion}”，${t('zx.account.checkPhoneRegion2')}`,
    tipVisible.value = true;
    return false;
  }
  return true;
};
const beginJoin = () => {
  nextTick(() => {
    seatchOrgWord.value = '';
    emailCode.value = '';
    formStep.value.reset();
    countdown.value = 0;
    curStep.value[0] = true;
  });
};
const closeBv = () => {
  emits('close-bv');
};
const confirmAgree = () => {
  agreeProto.value = true;
  notifivisible.value = false;
  joinTeam();
};
const showIframe = (uuid) => {
  LynkerSDK.ipcRenderer.invoke('create-iframe', uuid);
};
const checkOrgList = async () => {
  let [err, res] = await to(searchOrgList({ keyword: seatchOrgWord.value }));
  if (res?.status === 200 && res?.data?.data?.list?.length > 1) {
    activeOrgType.value = res?.data?.data?.type;
    orgList.value = res?.data?.data?.list;
    changeOrgListVisible.value = true;
  } else {
    checkOrg();
  }
};
const changeOrg = (val) => {
  activeOrgInfo.value = val;
};
const closeOrgListVisible = () => {
  changeOrgListVisible.value = false;
  activeOrgInfo.value = null;
}
const checkOrgListNext = async () => {
  let [err, res] = await to(checkJoinOrg({ teamId: activeOrgInfo.value?.teamId, keyword: seatchOrgWord.value }));
  if (res?.data?.data?.res === false) return MessagePlugin.error(t('contacts.noJoinOrg'));
  changeOrgListVisible.value = false;
  formData.value.from = activeOrgType.value;
  formData.value.teamId = activeOrgInfo.value?.teamId;
  teamName.value = activeOrgInfo.value?.fullName;
  // 验证输入的是否是邮箱
  const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
  isEmail.value = reg.test(seatchOrgWord.value);
  isEmail.value && getEmailCode();
  curStep.value[0] = false;
  curStep.value[isEmail.value ? 1 : 2] = true;
};
const checkOrg = () => {
  searchOrg({ keyword: seatchOrgWord.value }).then(({ data }) => {
    console.log(data, 'searchOrg');
    if (data.code === 0) {
      formData.value.from = data.data.type;
      formData.value.teamId = data.data.team.teamId;
      teamName.value = data.data.team.fullName;
      // 验证输入的是否是邮箱
      const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
      isEmail.value = reg.test(seatchOrgWord.value);
      isEmail.value && getEmailCode();
      curStep.value[0] = false;
      curStep.value[isEmail.value ? 1 : 2] = true;
    }
  }).catch(err => {
    MessagePlugin.error(err.response.data.message);
  });
};
const getEmailCode = () => {
  let params = { mail: seatchOrgWord.value, scene: 'JOIN_TEAM;4' };
  if (formData.value.teamId) {
    params = Object.assign(params, {teamId: formData.value.teamId})
  }
  getEmailSms(params);
  codeCount();
};
const checkEmailCode = () => {
  checkEmailSms({ mail: seatchOrgWord.value, scene: 'JOIN_TEAM;4', code: emailCode.value }).then(({ data }) => {
    if (data.code === 0) {
      curStep.value[1] = false;
      curStep.value[2] = true;
      countdown.value = 0;
    } else {
      MessagePlugin.error(data.message);
    }
  });
};
const preStep = (i) => {
  // 判断上一个页面是不是邮箱验证
  const ind = i === 1 || (i === 2 && isEmail.value) ? 1 : 2;
  curStep.value[i-ind] = true;
  curStep.value[i] = false;
  countdown.value = 0;
};
const getCode = (data?) => {
  if (!checkPhone()) return;
  const params = {
    phone: formData.value.phone,
    scene: 'joinTeam;4',
    telCode: formData.value.telCode,
  };
  if (data) {
    params.rid = data?.rid || '';
  }
  getSms(params).then(({ data }) => {
    if (data.code === 0) {
      codeCount();
    }
  });
};
const codeCount = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
  countdown.value = 60;
  timer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer.value);
      timer.value = null;
    }
  }, 1000);
};
const joinTeam = () => {
  formStep.value.validate().then((result) => {
    if (result === true) {
      if (!checkPhone()) return;
      if (!agreeProto.value) {
        notifivisible.value = true;
        return;
      }
      joinOrg(formData.value).then(({ data }) => {
        if (data.code === 0) {
          emits('sucess');
          joinType.value = +data.data.type;
          curStep.value[2] = false;
          curStep.value[3] = true;
        } else {
          MessagePlugin.error(data.message);
        }
      }).catch((err) => {
        if (err?.response?.data?.code === -1) {
          MessagePlugin.error(err?.response?.data?.message);
        }
      });
    }
  });
};
defineExpose({
  beginJoin,
});
watch(formData, (newValue) => {
  if (newValue.name && newValue.phone && newValue.code) {
    joinDisabled.value = false;
  } else {
    joinDisabled.value = true;
  }
},{
  deep: true,
});

const checkSM = () => {
  if (!SMCaptcha.value) {
    return;
  }
  getSMCaptchaResult(SMCaptcha.value, getCode);
};

const SMCaptcha = ref(null);
onMounted(async () => {
  try {
    SMCaptcha.value = await getSMCaptcha({ width: 300 });
    console.error(SMCaptcha.value);
  } catch (error) {
    console.error(error);
  }
});
</script>

<style lang="less" scoped>


/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 6px;
  height: 4px;
  // height: 2px;
  background-color: transparent !important;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  // background-color: #e3e6eb;
  // background-color: #fff;
  background-color: transparent !important;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
}

.cont {
  .wrap {
    margin-top: 0;
  }
}
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  .line {
    background-color: #ECEFF5;
    height: 1px;
    // width: 100%;
    flex: 1;

  }
  .more {
    color: var(--text-kyy_color_text_2, #516082);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.digital {
  display: flex;
  flex-direction: column;
  height: 100%;

}
.box {
  height: 358px;

}

.listScroll {
  display: flex;
  flex-direction: column;
  padding: 0 22px;
  // background-color: var(--bg-kyy_color_bg_deep, #F5F8FE);
  // max-height: 358px;
  height: 100%;
  gap: 12px;
  overflow-y: auto;
  .item {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    align-items: center;
    border-radius: 8px;
    background-color: var(--bg-kyy_color_bg_deep, #F5F8FE);
    .sright {
      flex: none;
      .btn {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
      }
    }
    .sleft {
      display: flex;
      position:relative;
      gap: 12px;
      flex: 1;
      .logo {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        img {
          width: 44px;
          height: 44px;
          border-radius: 50%;
        }
      }
      .con {
        display: flex;
        flex-direction: column;
        flex: 1;
        :deep(.highlight) {
          color: #4d5eff;
        }

        .tip {
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          flex:inherit;
          max-width: 260px;
          color: var(--text-kyy_color_text_1, #1A2139);
        }
        .type {
          width: fit-content;
          // padding:  4px 0;
          // padding: 0 4px;
          border-radius: 4px;
          background: var(--bg-kyy_color_bg_deep, #F5F8FE);
          display: flex;
          height: 20px;
          align-items: center;
          gap: 2px;

          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          .img {
            width: 16px;
            height: 16px;
          }
        }
        .government {
          color: var(--cyan-kyy_color_cyan_default, #11BDB2);
        }
        .member {
          color: #FC7C14;
        }
        .cbd {
          color: #4D5EFF;
        }
        .association {
          color: #ED565C;
        }
        .school{
          color: #49BBFB;
        }
      }

    }
  }
}

.serch-bar{
  display: flex;
  padding: 24px;
  align-items: flex-start;
  gap: 10px;
  align-self: stretch;
  // border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
  // background: var(--bg-kyy_color_bg_light, #FFF);
  .name-icon {
    font-size: 20px;
    color: #828DA5;
  }
}

.body-tip {
  margin-bottom: 8px;
}
.toBody {
  //   max-height: 70vh;
  //   overflow: auto;
  // padding: 0 24px;
  display: flex;
  flex-direction: column;
  // padding: 16px 24px;
  // align-items: center;
  padding: 0 2px;
  .scroll {
    padding: 0 22px;
    height: 374px;
    overflow: overlay;
    padding-top: 24px;
  }
}

.boxtag {
  padding: 0 24px;
}
.tabs {
  display: flex;
  align-items: center;
  height: 36px;
  // padding: 0 8px;
  gap: 24px;
  border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
  &-item {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
    height: 24px;
    position: relative;
    padding: 0 4px;
    border-radius: 4px;
    transition: all 0.15s linear ;

    &:hover {
      background: var(--kyy_color_tabbar_iconBg_hover, #EAECFF);
      transition: all 0.15s linear ;
    }


  }
  .active {
    color: var(--brand-kyy_color_brand_default, #4D5EFF);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 150% */
    &::before {
      content: '';
      bottom: -6px;
      border-radius: 1.5px;
      background: var(--brand-kyy_color_brand_default, #4D5EFF);
      position: absolute;
      width: 16px;
      height: 3px;
      margin: auto;
      left: 0;
      right: 0;
    }

  }
}

.custom-header {
  flex: 1;
  position: relative;
  .tricks {
    position: absolute;
    top: 0;
    left: 78px;
  }
}
.btn-close {
  font-size: 24px;
  font-weight: normal;
  cursor: pointer;
  color: #516082;
}
:global(.contactsJoinOrg .t-dialog__body){
  padding: 12px 0;
}
:global(.contactsJoinOrg .t-dialog__close){
  //width: 26px;
  //font-size: 26px;
}
:global(.changeOrgListVisible .t-dialog__body__icon){
  padding: 18px 0;
  margin-right: 0;
  color: #000;

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
:global(.changeOrgListVisible .t-dialog__footer){
  padding-top: 0;
}
:deep(.contactsJoinOrg input::-webkit-input-placeholder){
  color: var(--lingke-body-tips, #ACB3C0) !important;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
:deep(.contactsJoinOrg input::input-placeholder){
  color: var(--lingke-body-tips, #ACB3C0) !important;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.btn{
  min-width: 80px;
}
:deep(.contactsJoinOrg .t-dialog){
  transform-origin: right !important;
}
.changeOrgListVisible .t-dialog {
  .card-box{
    display: flex;
    align-items: center;
  }
  .org-card-info{
    flex: 1;
    width: 0;
    margin-left: 10px;
    .name{
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: var(--text-kyy_color_text_1, #1A2139);
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
    .value{
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: var(--text-kyy_color_text_3, #828DA5);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
  }
  .org-card{
    margin: 6px 0;
    :deep(.t-card__body){
      padding: 12px 16px;
    }
  }
  .org-card:not(:last-child){
    margin-bottom: 12px;
  }
  .active-card{
    border-radius: 8px;
    border: 1px solid var(--bg-kyy_color_bg_list_foucs, #E1EAFF);
    background: var(--bg-kyy_color_bg_list_foucs, #E1EAFF);
  }
  .default-card{
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_deep, #D5DBE4);
    background-color: #FFFFFF;
  }
  .default-card:hover{
    border-radius: 8px;
    border: 1px solid var(--bg-kyy_color_bg_list_hover, #F3F6FA);
    background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
  }
}

.t-form {
  :deep(.t-form__item) {
    margin-bottom: 12px;
  }
}
.suffix--line {
  width: 1px;
  height: 16px;
  background: var(--divider-kyy-color-divider-light, #ECEFF5);
  margin-right: 12px;
}
.tel {
  :deep(.t-input--auto-width) {
    min-width: 82px;
  }
  :deep(.t-input-adornment__prepend) {
    background: #fff;
  }
}
.t-input-adornment {
  width: 100%;
}
.verify {
  color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4D5EFF);
  text-align: center;

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.suc-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  .suc-icon {
    font-size: 0;
    width: 48px;
    height: 48px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .tip {
    margin-top: 8px;
    margin-bottom: 4px;
    color: var(--text-kyy-color-text-1, #1A2139);
    text-align: center;

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .sub-tip {
    color: var(--text-kyy-color-text-2, #516082);
    text-align: center;

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.footer {
  display: flex;
  justify-content: flex-end;
  padding: 24px;
}
.f-align {
  display: flex;
  align-items: center;
}
.body-tip {
  color: var(--font-kyy-font-gy-2, #516082);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
a:hover {
  color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4D5EFF);
}
</style>
