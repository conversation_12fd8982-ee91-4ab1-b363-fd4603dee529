<template>
    <div class="root" :data-sender="isSender">
        <div class="reply">
            <template v-if="Array.isArray(textSplits)" v-for="item in textSplits">
                <img class="emoji" :data-large="textSplits.length === 1" v-if="item.type === MsgTextType.Emoji"
                    :src="item.emojiSrc">
                <span class="text-item" v-else :data-type="item.type" @click="onClickTextItem(item)">
                    {{ item.str }}
                </span>
            </template>
            <template v-else-if="textSplits?.type === 'richText'">
              <LkIMEditorPlayground
                :content="textSplits.content"
                @onOnMentionClick="(data) => {
                  const cardId = data.cardId;
                  const myId = useMessageStore().chatingSession?.myCardId;
                  openIdentityCardById(myId, cardId)
                }"
                @onOnLinkClick="openUrlByBrowser"
              >
              </LkIMEditorPlayground>
            </template>
        </div>
        <!-- 被回复图片 -->
        <div v-if="props.msg.msg.content?.contentType === 2101 || props.msg.msg.content?.contentType === 111" class="reply-box">
          {{ referedName }}
          <span>[撤回一条消息]</span>
        </div>
        <!-- 被回复文本内容 -->
        <t-popup v-else-if="referedText" overlayClassName="reply-popup" v-model:visible="popupVisible" trigger="click" showArrow :placement=" isSender ? 'left' : 'right' ">
            <template #triggerElement>
                <div class="reply-text clickable">
                    {{ referedName }}
                    <template v-if="referedText?.type === 'richText'">
                      <LkIMEditorPlayground
                        :content="referedText.content"
                        @onOnMentionClick="(data) => {
                          const cardId = data.cardId;
                          const myId = useMessageStore().chatingSession?.myCardId;
                          openIdentityCardById(myId, cardId)
                        }"
                        @onOnLinkClick="openUrlByBrowser"
                      >
                      </LkIMEditorPlayground>
                    </template>
                    <template v-else v-for="item in referedText">
                        <img class="reply-emoji" v-if="item.type === MsgTextType.Emoji" :src="item.emojiSrc">
                        <img class="reply-icon" v-else-if="item.icon" :src="item.icon">
                        <span v-else-if="item.str">
                            {{ item.str }}
                        </span>
                    </template>

                </div>
            </template>
            <template #content>
                <div class="reply-text-all">
                  <template v-if="referedText?.type === 'richText'">
                      <LkIMEditorPlayground
                        :content="referedText.content"
                        @onOnLinkClick="openUrlByBrowser"
                      >
                      </LkIMEditorPlayground>
                    </template>
                    <template v-else v-for="item in referedText">
                        <img class="reply-emoji" v-if="item.type === MsgTextType.Emoji" :src="item.emojiSrc">
                        <img class="reply-icon" v-else-if="item.icon" :src="item.icon">
                        <span v-else-if="item.str">
                            {{ item.str }}
                        </span>
                    </template>
                </div>
            </template>
        </t-popup>

        <!-- 被回复图片 -->
        <div v-else-if="referedImg" class="reply-box">
            {{ referedName }}
            <img class="pic clickable" :src="referedImg.thumbnail" @click="openImgPreview">
        </div>
        <!-- 被回复视频 -->
        <div v-else-if="referedVideo" class="reply-box">
            {{ referedName }}
            <div class="reply-video clickable" @click="openVideoPreview">
                <img draggable="false" class="pic" :src="referedVideo.videoImageUrl">
                <i class="i-svg:video_play pic-video" draggable="false" />
            </div>
    </div>
    <!-- 被回复文件 -->
    <div v-else-if="referedFile" class="reply-text clickable" @click="openFilePreview">
      {{ referedName }}
      <img :src="referedFile.fileIcon" class="reply-emoji" />
      {{ referedFile.fileName }}
    </div>

    <!-- 被回复其他消息类型 -->
    <div v-else class="reply-text clickable" @click="clickRefer(msg)">
      {{ referedName + referedOthers }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, onUnmounted, ref } from 'vue';
import {
  MsgTextType,
  getMsgImageSrc,
  getParsedText,
  getParsedTextMessage,
  getReferMessagePreview,
} from '../service/msgUtils';
import { fileImage } from "@renderer/utils/myUtils";
import { useMessageStore } from '../service/store';
import MsgWrapper from '../service/message';
import SvgIcon from '@/components/SvgIcon.vue';
import { onMounted } from 'vue';
import { useChatActionStore } from '../service/actionStore';
import { getMeetingText } from "@renderer/views/message/chat/msgTypeContent/meetingMsg";
import { LkIMEditorPlayground } from '@rk/editor';
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer, shell } = LynkerSDK;

const actionStore = useChatActionStore();

const msgStore = useMessageStore();

const props = defineProps({
  msg: { type: Object as PropType<MsgWrapper>, required: true },
  isSender: { type: Boolean, required: true },
});

// 回复的内容
const textSplits = computed(() => {
  const splits = getParsedTextMessage(props.msg.msg);
  return splits;
});

/** 引用消息结构
 * content: ""
 * extra: "{\"senderId\":\"26b5s0000ikn484ss4\",\"receiverId\":\"00o2ukkggg2dc\",\"data\":{\"text\":\"435\"},\"clientId\":\"1692070787436\",\"source\":\"darwin\",\"contentType\":\"text\"}"
 * objName: "text"
 * referMsg: {content: 'e', user: {…}, extra: '{"senderId":"0pa800000hk60zq967","receiverId":"00o…ll,"source":"app_ios","clientId":"1692008913682"}'}
 * referMsgUid: "C9UE-BHV3-S46D-UIUS"
 * referMsgUserId: "0pa800000hk60zq967"
 * user: {id: '26b5s0000ikn484ss4', name: '张荣', portrait: '张荣'}
 */
const referMsgData = computed(() => {
  const { referMsg } = props.msg.msg.content
  let extra: MessageToSave['contentExtra'] = null;
  try {
    extra = JSON.parse(referMsg.extra);
  } catch (e) {
    // extra
  }
  return extra;
});

// 被回复类型是文本
const referedText = computed(() => {
  const msgType = referMsgData.value?.contentType;
  // if (msgType === 'text') {
  //   const text = referMsgData.value.data?.text;
  //   return getParsedText(text);
  // }

  if (msgType === 'richText') {
    try {
      const textArray = JSON.parse(referMsgData.value.data?.text);
      return {
        type: 'richText',
        content: textArray,
      }
    } catch (error) {
      return {
        type: 'richText',
        content: referMsgData.value.data?.text,
      };
    }
  }

  if (['text', 'emoji_coco_image'].includes(msgType)) {
    const text = referMsgData.value.data?.text;
    return getParsedText(text, false, msgType);
  }else if ('APP_MEETING_MSG' === msgType) {
    const isSender =  msgStore.chatingSession?.myOpenImId === referMsgData.value.senderId
    const {text,icon} = getMeetingText(referMsgData.value.data, isSender)
    return [{ str:text, icon }];
  }

  return null;
});

// 被回复类型是图片
const referedImg = computed(() => {
  const msgType = referMsgData.value?.contentType;
  if (['image', 'emoji_image'].includes(msgType)) {
    return getMsgImageSrc(referMsgData.value.data);
  }
  return null;
});

// 被回复类型是视频
const referedVideo = computed(() => {
  const msgType = referMsgData.value?.contentType;
  if ('video' === msgType) {
    return referMsgData.value.data;
  }
  return null;
});

const referedFile = computed(() => {
  const msgType = referMsgData.value?.contentType;
  if ('file' === msgType) {
    const fileName = referMsgData.value.data?.fileName;
    const fileIcon = fileImage(referMsgData.value.data?.type);
    return { fileName, fileIcon };
  }
  return null;
});


// 其他被回复类型
const referedOthers = computed(() => {
  return getReferMessagePreview(referMsgData.value);
});

// 被回复消息的发送者
const referedName = computed(() => {
  const msg = props.msg.msg;
  const conversationId = msg.conversationType === 1 ? msg.localSessionId : msg.targetId;
  const members = msgStore.allMembers.get(conversationId);
  const member = members?.get(referMsgData.value?.senderId);
  const senderName = member?.staffName || member?.nickname || msg.content?.referMsg?.user?.name || '';
  return senderName ? `${senderName}： ` : '';
});

// 打开图片预览
const openImgPreview = () => {
  const { imgUrl: url, size } = referMsgData.value?.data;
  actionStore.previewFileAction({ url, size });
};

// 打开视频预览
const openVideoPreview = () => {
  const { videoUrl: url, size, type } = referMsgData.value?.data;
  actionStore.previewFileAction({ url, size, type });
};

// 打开文件预览
const openFilePreview = () => {
  const { fileUrl: url, size, fileName: title, type, fileId: officeId } = referMsgData.value?.data;
  actionStore.previewFileAction({ url, size, title, type, officeId });
};
// 去掉定位并打开聊天信息
const clickRefer = (msg) => {
  const content = msg?.msg?.content?.referMsg?.extra;
  if (content) {
    const extra = JSON.parse(content);
    if (extra?.data?.fileUrl) {
      ipcRenderer.invoke('merged-message', { fileUrl: extra.data.fileUrl });
    }
  }
  // msgStore.gotoChatingSessionMessage(props.msg.msg.content.referMsgUid);
};
const popupVisible = ref(false);
const onScroll = () => {
  popupVisible.value = false;
};

onMounted(() => {
  document.querySelector('.chat-content')?.addEventListener('scroll', onScroll);
});

onUnmounted(() => {
    document.querySelector('.chat-content')?.removeEventListener('scroll', onScroll);
})

const onClickTextItem = (item: ReturnType<typeof getParsedTextMessage>[number]) => {
    if (item.type === MsgTextType.At) {
        openIdentityCard(item)

    } else if (item.type === MsgTextType.Url) {
        openUrlByBrowser(item.str);
    }
}

function openIdentityCard(item) {
  const cardId = props.msg.msg.contentExtra?.data?.atInfo?.[item?.atIndex || 0]?.cardId;
  const myId = useMessageStore().chatingSession?.myCardId;
  openIdentityCardById(myId, cardId);
}

function openIdentityCardById(mycard: string, targetCard: string) {
  if (mycard && targetCard) {
    ipcRenderer.invoke("identity-card", { cardId: targetCard, myId: mycard });
  }
}

function openUrlByBrowser(url: string) {
  shell.openExternal(url);
}


</script>

<style scoped>
.root {
  max-width: 320px;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-line;
  padding: 8px 12px;
}

.root[data-sender='true'] {
  background-color: #e1eaff;
  border-radius: 8px 0 8px 8px;
  border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);

  & .reply {
    border-bottom: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4);
  }
}

.root[data-sender='false'] {
  background-color: #fff;
  border-radius: 0px 8px 8px 8px;
  border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);

  & .reply {
    border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  }
}

.reply {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  white-space: pre-line;

  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 14px;
  line-height: 22px;
  padding-bottom: 8px;
  margin-bottom: 8px;

}

.reply-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: pre-line;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  word-wrap: break-word;
  font-size: 12px!important;
  font-weight: 400;
  line-height: 20px!important;
  color: var(--text-kyy_color_text_3, #828da5)!important;
  :deep(*) {
    font-size: 12px!important;
    color: #828da5!important;
  }
}

.reply-text-all {
  width: 216px;
  display: -webkit-box;
  vertical-align: middle;
  white-space: pre-line;
  word-break: break-all;
  word-wrap: break-word;
  font-size: 14px!important;
  line-height: 22px!important;
  color: var(--text-kyy_color_text_1, #1a2139)!important;
  background-color: #fff;
  overflow: scroll;
  max-width: 256px;
  max-height: 208px;
  padding-right: 6px;
  :deep(*) {
    font-size: 14px!important;
    line-height: 22px!important;
    color: #1a2139!important;
  }
}

.pic {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 8px;
  overflow: hidden;
}

.reply-video {
  position: relative;

  .pic-video {
    /* width: 20px;
    height: 20px; */
    font-size: 20px;
    color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.emoji {
  width: 32px;
  height: 32px;
  display: inline;
  margin: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  vertical-align: middle;
}

.text-item[data-type='3'], .text-item[data-type='6'] {
    color: var(--brand-kyy_color_brand_default, #4D5EFF);
    cursor: pointer;
    margin-right: 4px;
}

.emoji[data-large="true"] {
    width: 44px;
    height: 44px;
}

.reply-emoji {
  display: inline;
  width: 20px;
  height: 20px;
  margin: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  vertical-align: middle;
}
.reply-icon{
  display: inline;
  width: 16px;
  height: 16px;
  margin: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  vertical-align: middle;
}
.reply-box {
  display: flex;
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  color: var(--text-kyy_color_text_3, #828da5);
}

.clickable {
  cursor: pointer;
}
::-webkit-scrollbar{
  width:6px;
}
::-webkit-scrollbar-thumb{
  background: var(--divider-kyy_color_divider_deep, #D5DBE4);
}
</style>


<style>
.reply-popup .t-popup__content {
  padding: 8px 12px;
}
</style>
