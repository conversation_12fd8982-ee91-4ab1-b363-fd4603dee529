<template>
  <StyledGradientDialog
    class="app-margin-record-dialog"
    title="保证金说明"
    :visible="visible"
    :footer="false"
    :background-image="{
      src: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/h5/extapp_dialog_bg.png',
    }"
    padding="0px 12px 12px 12px"
    @update:visible="updateVisible"
  >
    <div>
      <div class="bg-white rounded-8 p-12">
        <ul class="flex flex-col gap-10">
          <li class="text-14 space-y-3">
            <div class="text-#1A2139 font-600">1.定义</div>
            <div class="text-#516082">由商家缴纳至平台，用于绑定第三方应用</div>
          </li>
          <li class="text-14 space-y-3">
            <div class="text-#1A2139 font-600">2.解绑与退还</div>
            <div class="text-#516082">商家可随时申请解除绑定，并全额退还保证金</div>
          </li>
          <li class="text-14 space-y-3">
            <div class="text-#1A2139 font-600">3.退款时效</div>
            <div class="text-#516082">平台将在收到完整申请后的7个工作日内完成退款</div>
          </li>
        </ul>
      </div>

      <div class="mt-16 flex justify-center pb-4">
        <t-button variant="outline" theme="primary" @click="handleShowAgreement">
          <span class="font-600">查看保证金服务协议</span>
        </t-button>
      </div>
    </div>
  </StyledGradientDialog>

  <AgreementDialog
    ref="agreementDialogRef"
    :confirm-btn="{
      theme: 'primary',
      content: '知道了',
    }"
    :cancel-btn="null"
  />
</template>

<script setup lang="tsx">
import StyledGradientDialog from '@components/common/StyledGradientDialog.vue';
import { ref } from 'vue';
import AgreementDialog from '@components/common/AgreementDialog.vue';
import { AgreementType } from '@api/common';

const visible = ref(false);

const agreementDialogRef = ref<InstanceType<typeof AgreementDialog>>();

const updateVisible = (val: boolean) => {
  visible.value = val;
};

const handleShowAgreement = () => {
  agreementDialogRef.value?.open(AgreementType.Margin);
};

defineExpose({
  show() {
    console.log('show');

    visible.value = true;
  },
  hide() {
    visible.value = false;
  },
});
</script>

<style lang="less">
.app-margin-record-dialog.style-gradient-dialog .t-dialog__footer {
  height: 56px;
}
</style>
