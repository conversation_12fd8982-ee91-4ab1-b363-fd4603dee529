import { AxiosRequestConfig } from 'axios'

interface AnyObject {
  [key: string]: any;
}

interface memoryInfo {
  jsHeapSizeLimit: number;
  totalJSHeapSize: number;
  usedJSHeapSize: number;
}

interface Window {
  performance: {
    memory: memoryInfo;
  };
  __lib: string;
  __static: string;
}

declare namespace NodeJS {
  interface ProcessEnv {
    // [key: string]: string | undefined
    NODE_ENV?: 'development' | 'production' | 'staging'
    // @ts-ignore
    userConfig?: ImportMetaEnv
    BUILD_TARGET?: string
  }
}

declare module 'axios' {
  interface AxiosRequestConfig {
    hideMessage?: boolean;
  }
}

declare module '@rk/unitPark';
