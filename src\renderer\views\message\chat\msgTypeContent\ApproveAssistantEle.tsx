/**
 * 审批助手消息
 * @param msg
 * {
    "content": {
        "body": [],
        "title": "issac002提交的【测试审批A】待你审批"
    },
    "extend": {
        "approval_id": 336,
        "log_id": 247,
        "project_id": 0,
        "team_id": "333aae668001"
    },
    "header": {
        "team": {
            "logo": "",
            "name": "issac测试1018公司"
        },
        "project": {
          "name": "issac测试1018公司"
        }
    },
    "scene": 4003,
    "template": "normal",
    "type": "APP_APPROVAL"
}
 */

// -1：不可审批，0：待审批，1：已同意，2：已被他人处理，3：已拒绝，4：已转交，5：已被转交，6：已退回，7：已加签，8：发起人已撤销
// 4001 发起人提交的审核通过了
// 4002 发起人提交的审核被拒绝了
// 4003 审批人收到了需要我审批的单据
// 4004 审批人收到了催办
// 4005 审批人收到了转交
// 4006 审批人收到了加签
// 4007 审批人收到了退回
// 4008 审批人收到了审批通过后撤销的单据
// 4009 审批人收到了审批通过后撤销单据的催办
// 4010 审批人收到了审批通过后撤销单据的转交
// 4011 审批人收到了审批通过后撤销单据的加签
// 4012 审批人收到了审批通过后撤销单据的退回
// 4013 被抄送人收到了抄送
// 4014 被抄送人收到了审批通过后撤销单据的抄送
// 4015 发起人提交的撤销申请审核通过了
// 4016 发起人提交的撤销审核被拒绝了
// 4017 审批人已经审批同意的审批单据还没有完结的情况下，被发起人撤销了
// 4018 审批人已经审批通过单据被发起人撤销了，审批已经审批同意了，审批单据还没有完结情况下，又被发起人撤销了
// 4019 被@的人在审批评论中使用的@功能
// 4020 被@的人已经审批通过单据被发起人撤销了，然后再撤销流程的审批评论中使用的@功能
// 4021 交接人在审批交接中将待交接人的审批身份进行了交接。
// 4022 发起人发起的申请在审批过程中表单控件被编辑过
// 4023 审批人审批通过后，由后续的审批人在审批时对审批表单控件进行了编辑。
import { PropType, defineComponent, ref } from 'vue';
import { AppCard, AppCardHeader, AppCardBody, AppCardFooter, AppCardBtn, AppCardText } from '../MessageAppCard';
import { getAppApprovalStatus, reqApproveAgree, reqApproveReject } from '../../service/extend/statusUtils';
import { useChatExtendStore } from '../../service/extend';
import { i18nt } from "@/i18n";
import { Button } from 'tdesign-vue-next';

export const ApproveAssistantStatelessEle = defineComponent({
    props: {
        data: { type: Object as PropType<MessageToSave>, default: null },
    },
    setup(props, ctx) {
        return () => {
            const data = props.data.contentExtra?.data;
            return (
                <AppCard>
                    <AppCardBody>
                        <div style="display:flex;flex-direction:row;gap:8px;">
                        { data?.header?.team?.logo
                            ? <img width='40' height='40' src={data?.header?.team?.logo} />
                            : <i class="i-svg-im_avatar_team text-40 color-[#488BF0]" />
                        }
                        <div style='flex:1;'>
                            <AppCardText weight='400'>{data?.header?.team?.name ?? ''}</AppCardText>
                            <AppCardText type="info" font={12}>{data?.header?.project?.name ?? ''}</AppCardText>
                        </div>
                        </div>
                        <div style="margin: 12px 0;">
                        {data?.content?.title}
                        </div>
                        { data?.content?.body?.map((item) => (
                            <div style="display:flex;flex-direction:row;justify-content:space-between;gap:8px;overflow: hidden;">
                            <AppCardText type='info' style='width:80px' >{item.key}</AppCardText>
                            <AppCardText style="flex:1;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
                                {item.value}
                            </AppCardText>
                            </div>
                        ))
                        }
                    </AppCardBody>
                </AppCard>
            );
        };
    }

})



export default defineComponent({
    props: {
        data: { type: Object as PropType<MessageToSave>, default: null },
    },
    setup(props, ctx) {
        const isLoading = ref(true);
        const isLoadError = ref(false);
        const getData = (msg) => {
            isLoadError.value = false;
            isLoading.value = true;
            getAppApprovalStatus(msg).then(res => {
                isLoading.value = false;
            }).catch(error => {
                isLoading.value = false;
                isLoadError.value = true
            });
        }

        getData(props.data)
        console.log('====>isLoading1', isLoading.value)
        return () => {
          // 先展示框架
          if (isLoading.value) {
            console.log('====>isLoading2', isLoading.value)
            return (
              <AppCard isLoading={isLoading.value}>
              </AppCard>
            );
          }
            const msg = props.data;
            const data = msg.contentExtra?.data;
            const scene = msg.contentExtra?.scene;

            const showDetail = () => {
                console.log('showDetail')
                useChatExtendStore().showApproveDialog('approve-helper', msg)
            };

            const onApprove = (e) => {
                e.stopPropagation();
                if (data?.apiData?.must_opinion === 0) {
                    reqApproveAgree(msg);

                } else {
                    useChatExtendStore().showApproveDialog('approve-helper', msg, 'agree')
                }
            };

            const onReject = (e) => {
                e.stopPropagation();
                if (data?.apiData?.must_opinion === 0) {
                    reqApproveReject(msg);

                } else {
                    useChatExtendStore().showApproveDialog('approve-helper', msg, 'reject')
                }
            };

            // 审批状态，取值如下：
            // -1：不可审批，0：待审批，1：已同意，2：已被他人处理，3：已拒绝，4：已转交，5：已被转交，6：已退回，7：已加签，8：发起人已撤销
            const approveStatus = data?.apiData?.status;
            const theme = { 4001: 'success', 4002: 'danger', 4005: 'none', 4013: 'none', 4014: 'none', 4015: 'success', 4016: 'danger' }[data?.scene] ?? 'secondary';
            // 刷新回调
            const refreshCb = () => {
                console.log('refreshCb')
                getData(msg)
            }


            if ([4021].includes(msg.contentExtra?.scene)) {
                return <ApproveAssistantStatelessEle data={props.data} />
            }

            return (
                <AppCard onClick={showDetail} isLoadError={isLoadError.value} refreshCb={refreshCb}>
                    <AppCardHeader theme={theme} style="font-size: 16px;">{data?.content?.title}</AppCardHeader>
                        { theme === 0 ? <div style="height:1px; margin: 0 12px; background-color:#E3E6EB;" /> : null }
                        <AppCardBody>
                            <div style="display:flex;flex-direction:row;gap:8px;margin-bottom:12px;">
                            { data?.header?.team?.logo
                                ? <img width='40' height='40' src={data?.header?.team?.logo} />
                                : <i class="i-svg-im_avatar_team text-40 color-[#488BF0]" />
                            }
                            <div style='flex:1;'>
                            <AppCardText weight='400'>{data?.header?.team?.name ?? ''}</AppCardText>
                            <AppCardText type="info" font={12}>{data?.header?.project?.name ?? ''}</AppCardText>
                            </div>
                            </div>
                            <div>
                            { data?.content?.body?.map((item) => (
                                <div style="display:flex;flex-direction:row;justify-content:space-between;gap:8px;overflow: hidden;">
                                <AppCardText type='info' style='width:80px' >{item.key}</AppCardText>
                                <AppCardText style="flex:1;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
                                    {item.value}
                                </AppCardText>
                                </div>
                            ))
                            }
                            </div>
                        </AppCardBody>

                        <AppCardFooter>
                            {/* // -1：不可审批，0：待审批，1：已同意，2：已被他人处理，3：已拒绝，4：已转交，5：已被转交，6：已退回，7：已加签，8：发起人已撤销 */}
                            { approveStatus === 0 ? <Button style='flex:1;' content={i18nt('im.msg.refuse')} theme='danger' variant="outline" onClick={ onReject} /> : null }
                            { approveStatus === 0 ? <Button style='flex:1;' content={i18nt('im.msg.agree')} theme='primary' variant="outline" onClick={ onApprove } /> : null }
                            { approveStatus === -1 ? <AppCardBtn style='flex:1;' text={i18nt('im.public.detail')} theme='text' /> : null }
                            { approveStatus === 1 ? <AppCardBtn style='flex:1;' text={i18nt('im.msg.agreed')} theme='text' /> : null }
                            { approveStatus === 2 ? <AppCardBtn style='flex:1;' text={i18nt('im.msg.handled')} theme='text' /> : null }
                            { approveStatus === 3 ? <AppCardBtn style='flex:1;' text={i18nt('im.msg.refused')} theme='text' /> : null }
                            { approveStatus === 4 ? <AppCardBtn style='flex:1;' text={i18nt('im.msg.transfer')} theme='text' /> : null }
                            { approveStatus === 5 ? <AppCardBtn style='flex:1;' text={i18nt('im.msg.transferred')} theme='text' /> : null }
                            { approveStatus === 6 ? <AppCardBtn style='flex:1;' text={i18nt('im.msg.back')} theme='text' /> : null }
                            { approveStatus === 7 ? <AppCardBtn style='flex:1;' text={i18nt('im.msg.countersign')} theme='text' /> : null }
                            { approveStatus === 8 ? <AppCardBtn style='flex:1;' text={i18nt('im.msg.revoke')} theme='text' /> : null }
                        </AppCardFooter>
                </AppCard>
            );
        }
    }
})

