<!--
 * @Author: ZHANGXIAO
 * @Date: 2024-01-11 17:11:21
 * @LastEditors: ZHANGXIAO
 * @LastEditTime: 2024-05-14 17:40:29
 * @FilePath: \lynker-desktop\src\renderer\components\contacts\MultiIdTag.vue
 * @Description: 多身份展示标签
-->
<template>

  <div class="multi-id" v-if="show" :class="{ inner: inner }">
    <div>{{ theTeamName || myCardName }}</div>
    <template v-if="!theTeamName && myCardName">
      <!-- <SvgIcon name="icon_standing" class="svg-icon" /> -->
      <i class="i-svg:icon_standing svg-icon" />
      <div>{{ otherCardName }}</div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import SvgIcon from "@/components/SvgIcon.vue";
import { cardIdType } from "@/views/identitycard/data";
import { useMessageStore } from '@renderer/views/message/service/store';


import { onMounted } from "vue";
import { ref, watch } from "vue";
const msgStore = useMessageStore();

const props = defineProps({
  // 我的身份卡
  myCard: {
    type: Object,
    default: {
      cardId: "",
      teamName: "",
    },
  },
  // 对方身份卡
  anotherCard: {
    type: Object,
    default: {
      cardId: "",
      teamName: "",
    },
  },
  // 群组展示单独组织
  teamName: {
    type: String,
    default: "",
  },
  // 哪个页面的。特别关注需要单独判断，对方信息为$则展示内部
  from: {
    type: String,
    default: "",
  },
});
const myCardName = ref("");
const otherCardName = ref("");
const theTeamName = ref("");
const show = ref(true);
const inner = ref(false);

const exclusiveName = (item) => {
  if(msgStore?.exclusiveNames?.get(item?.teamId)) {
    return  msgStore?.exclusiveNames.get(item.teamId)
  } else {
    return item?.teamName;
  }
}

const getMyCardTeam = () => {
  show.value = true;
  inner.value = false;
  // 获取id首字母。# 外部，$ PT内部，其他为个人
  const myType = cardIdType(props.myCard.cardId);
  const otherType = cardIdType(props.anotherCard?.cardId);
  // const otherTeamName = otherType === "platform" ? "[平台]" + props.myCard.teamName : otherType === "outer" ? props.anotherCard.teamName: props.myCard.teamName;
  const otherTeamName = otherType === "platform" ? "[平台]" + exclusiveName(props.myCard) : otherType === "outer" ? props.anotherCard.teamName: props.myCard.teamName;
  // 个人身份只能同个人，外部建立关系
  if (myType == "personal") {
    if (otherType !== "outer") {
      show.value = false;
      return;
    }
  }
  if (otherType == "personal") {
    if (myType !== "outer") {
      show.value = false;
      return;
    }
  }

  // if (props.from === "follow" && (otherType === "inner" || otherType === "platform")) {
  //   theTeamName.value = otherTeamName;
  //   inner.value = true;
  //   return;
  // }
  if ((otherType === "inner" || otherType === "platform") && (myType === "inner" || myType === "platform")) {
    // 内部或平台身份展示一个组织
    theTeamName.value = otherType === "platform" ? otherTeamName : props.myCard.teamName;
    inner.value = true;
  } else {
    const isCompany = myType !== "personal";
    const otherIsCompany = otherType !== "personal";
    myCardName.value = isCompany ? props.myCard?.teamName : "个人";
    otherCardName.value = otherIsCompany ? otherTeamName : "个人";
    show.value = isCompany || otherIsCompany; // 双方为个人身份则不展示
  }
};
onMounted(() => {
  if (props.teamName) {
    theTeamName.value = props.teamName;
  } else {
    theTeamName.value = null;
    getMyCardTeam();
  }
});
watch(
  () => props.myCard,
  () => {
    theTeamName.value = props.teamName;
    getMyCardTeam();
  },
  {
    deep: true,
  },
);
</script>

<style lang="less" scoped>
.multi-id {
  display: flex;
  align-items: center;
  color: var(--error_kyy_color_warning_default, #fc7c14);
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  margin-top: 2px;
  .svg-icon {
    margin: 0 2px;
    // height: 16px;
    // width: 16px;
    font-size: 16px;
  }
}
.inner {
  color: #49bbfb;
}
</style>
