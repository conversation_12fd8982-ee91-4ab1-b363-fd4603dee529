<template>
  <div class="my-order-box">
    <title-bar
      :is-title="titleBarTitle"
      :team-id="teamId"
      :teams-auth-list="teamsAuthList"
      @change-team-fn="changeTeam"
    />

    <div style="padding: 0 24px">
      <div class="settingGroup">
        <div :class="tabs === 1 ? 'active tabs' : 'tabs'" @click="tabChangeTwo(1)">平台订单</div>
        <div :class="tabs === 2 ? 'active tabs' : 'tabs'" @click="tabChangeTwo(2)">业务订单</div>
        <div :class="tabs === 3 ? 'active tabs' : 'tabs'" @click="tabChangeTwo(3)">退款/售后</div>
      </div>
    </div>
    <!-- 头部搜索 -->
    <div class="seach-head-box">
      <div class="flex-a">
        <t-input
          v-if="tabs === 1"
          v-model="searchFormData.goods"
          :maxlength="20"
          style="width: 384px"
          :placeholder="'请输入' + t('order.orderName')"
          @blur="searchFor()"
        >
          <template #suffixIcon>
            <span style="color: #acb3c0; font-size: 12px">{{ searchFormData.goods.length }}/20</span>
          </template>
        </t-input>
        <t-input
          v-else
          v-model="searchFormData.keyword"
          :maxlength="20"
          style="width: 384px"
          :placeholder="'请输入' + t('order.orderName')"
          @blur="searchFor()"
        >
          <template #suffixIcon>
            <span style="color: #acb3c0; font-size: 12px">{{ searchFormData.keyword.length }}/20</span>
          </template>
        </t-input>
        <div v-if="searchFlag" class="af-icon" @click="drawerSeachFlag = true">
          <img src="@assets/approval/icons/fbutton.svg" style="width: 32px; height: 32px" />
        </div>
        <div v-else class="f-icon" @click="drawerSeachFlag = true">
          <img src="@assets/approval/icons/icon_screen.svg" style="width: 20px; height: 20px" />
        </div>
      </div>
      <!-- <t-button
        v-if="teamsAuthList.length > 0"
        class="gogrouporder"
        theme="default"
        variant="outline"
        style="font-weight: 600; color: #516082; font-size: 14px"
        @click="openGroup"
      >
        {{ t('banch.qwzzdd') }}
      </t-button> -->
    </div>
    <div v-if="searchFlag" class="filter-res">
      <div class="tit">{{ t('approval.approval_data.sures') }}</div>
      <div v-if="formData.goods !== ''" class="kword te">
        <span>{{ t('order.orderName') }}：{{ formData.goods }}</span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          @click="((formData.goods = ''), (searchFormData.goods = ''), searchFor())"
        />
      </div>
      <div v-if="formData.keyword !== ''" class="kword te">
        <span>{{ t('order.orderName') }}：{{ formData.keyword }}</span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          @click="((formData.keyword = ''), (searchFormData.keyword = ''), searchFor())"
        />
      </div>
      <div v-if="formData.created_at.length" class="ov-time te">
        <span>
          {{ tabs === 3 ? '提交时间' : t('order.orderTime') }}： {{ formData.created_at[0] }} ~
          {{ formData.created_at[1] }}
        </span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          alt=""
          @click="((formData.created_at = []), (searchFormData.created_at = []), searchFor())"
        />
      </div>
      <div v-if="formData.status !== ''" class="kword te">
        <span v-if="tabs === 1">
          {{ t('order.orderstats') }}：{{
            formData.status === 1 ? '待付款' : formData.status === 4 ? '已完成' : '已' + t('payment.close')
          }}
        </span>
        <span v-else-if="tabs === 2">
          {{ t('order.orderstats') }}：{{
            formData.status === '2,4,5,6,7,8'
              ? '待收货/使用'
              : formData.status === 1
                ? '待付款'
                : formData.status === 100
                  ? '已完成'
                  : '已' + t('payment.close')
          }}
        </span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          alt=""
          @click="((formData.status = ''), (searchFormData.status = ''), searchFor())"
        />
      </div>
      <div v-if="formData.is_refund !== ''" class="kword te">
        <span>是否退款：{{ formData.is_refund === 1 ? '是' : '否' }}</span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          alt=""
          @click="((formData.is_refund = ''), (searchFormData.is_refund = ''), searchFor())"
        />
      </div>
      <div v-if="formData.order_mode !== ''" class="kword te">
        <span>
          订单类型：{{
            formData.order_mode === 0
              ? '其他收款'
              : formData.order_mode === 1
                ? '同城配送'
                : formData.order_mode === 2
                  ? '自提'
                  : '物流配送'
          }}
        </span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          alt=""
          @click="((formData.order_mode = ''), (searchFormData.order_mode = ''), searchFor())"
        />
      </div>
      <div v-if="formData.has_comment !== ''" class="kword te">
        <span>评价状态：{{ formData.has_comment === 0 ? '待评价' : '已评价' }}</span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          alt=""
          @click="((formData.has_comment = ''), (searchFormData.has_comment = ''), searchFor())"
        />
      </div>

      <div v-if="formData.invoice_status !== ''" class="kword te">
        <span>
          {{ t('order.invoiceStatus') }}：{{
            formData.invoice_status === '0,2'
              ? t('order.notInvoiced')
              : formData.invoice_status === 1
                ? t('order.kpz')
                : t('order.Invoiced')
          }}
        </span>
        <img
          src="@assets/icon_error.svg"
          @click="((formData.invoice_status = ''), (searchFormData.invoice_status = ''), searchFor())"
        />
      </div>
      <div v-if="formData.sn !== ''" class="kword te">
        <span>订单编号 :{{ formData.sn }}</span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          alt=""
          @click="((formData.sn = ''), (searchFormData.sn = ''), searchFor())"
        />
      </div>
      <div v-if="tabs === 2 && formData.refund_status !== ''" class="kword te">
        <span>
          退款情况 :{{
            formData.refund_status === 0
              ? '无退款'
              : formData.refund_status === 1
                ? '退款中'
                : formData.refund_status === 2
                  ? '部分退款'
                  : '退款成功'
          }}
        </span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          alt=""
          @click="((formData.refund_status = ''), (searchFormData.refund_status = ''), searchFor())"
        />
      </div>
      <div v-if="formData.submit_at.length !== 0" class="kword te">
        <span>提交时间 :{{ formData.submit_at[0] }}-{{ formData.submit_at[1] }}</span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          alt=""
          @click="((formData.submit_at = []), (searchFormData.submit_at = []), searchFor())"
        />
      </div>
      <div v-if="tabs === 3 && formData.refund_status !== ''" class="kword te">
        <span>
          退款状态 :{{
            formData.refund_status === 0
              ? '待商家处理'
              : formData.refund_status === 1
                ? '待买家处理'
                : formData.refund_status === 2
                  ? '已退款'
                  : '已取消'
          }}
        </span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          alt=""
          @click="((formData.refund_status = ''), (searchFormData.refund_status = ''), searchFor())"
        />
      </div>
      <div v-if="formData.initiate_type !== ''" class="kword te">
        <span>发起方: {{ formData.initiate_type === 0 ? '买家' : '商家' }}</span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          alt=""
          @click="((formData.initiate_type = ''), (searchFormData.initiate_type = ''), searchFor())"
        />
      </div>
      <div v-if="formData.origin !== ''" class="kword te">
        <span>收款来源: {{ searchFormData.origin === 'store_product' ? '店铺订单' : '活动收款' }}</span>
        <img
          class="close2"
          src="@assets/icon_error.svg"
          alt=""
          @click="((formData.origin = ''), (searchFormData.origin = ''), searchFor())"
        />
      </div>
      <div class="icon" @click="rest">
        <img src="@assets/approval/icons/del8.svg" alt="" />
        <a>{{ t('approval.approval_data.clearFilters') }}</a>
      </div>
    </div>
    <!-- <div v-if="searchFlag" class="searchtotal">
        共找到{{ total }}条记录
      </div> -->
    <div style="margin: 0px 24px; overflow: auto; flex: 1; height: 100%">
      <t-config-provider>
        <t-table
          row-key="id"
          :fixed-rows="undefined"
          height="100%"
          hide-sort-tips
          :class="tableData.length === 0 ? 'orderTable' : ''"
          :columns="tabs === 1 ? columns : tabs === 2 ? columnsBusiness : columnsRufend"
          :data="tableData"
          style="position: relative; overflow: auto; height: 100%"
          :loading="isLoading"
        >
          <template #sn="{ row }">
            <div
              v-if="(row.refund_status === 3 || row.refund_status === 1 || row.refund_status === 2) && tabs === 2"
              class="refund-icon"
            >
              {{
                row.refund_status === 3
                  ? '退款成功'
                  : row.refund_status === 1
                    ? '退款中'
                    : row.refund_status === 2
                      ? '部分退款'
                      : ''
              }}
            </div>
            <div
              style="color: #4d5eff; cursor: pointer"
              :class="row.refundInfo && row.refundInfo?.refund_status === 4 ? 'tui' : ''"
            >
              {{ row.sn || row.refund_sn }}
            </div>
          </template>
          <template #payAmount="{ row }">
            <div>
              {{ row.currency === 'CNY' ? '￥' : 'MOP' }}
              <span v-if="row.payAmount">
                {{ row.payAmount !== 0 ? addCommasToNumber(row.payAmount.toFixed(2)) : '0.00' }}
              </span>
              <span v-else-if="row.pay_amount">{{ addCommasToNumber(row.pay_amount.toFixed(2)) }}</span>
              <span v-else-if="row.refund_amount">{{ addCommasToNumber(row.refund_amount.toFixed(2)) }}</span>
              <span v-else>0.00</span>
            </div>
          </template>

          <template #goods="{ row }">
            <div>
              <div>{{ row.snapshot.title }}</div>
              <t-tooltip :content="row.snapshot.subtitle">
                <div
                  style="
                    color: rgb(130, 141, 165);
                    width: 210px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    display: block;
                    word-break: break-all;
                  "
                  class="flex-a"
                >
                  {{ row.snapshot.subtitle }}
                </div>
              </t-tooltip>
            </div>
          </template>
          <template #orderMode="{ row }">
            <div class="order-mode-box">
              <img
                class="order-mode-box-img"
                :src="orderMode.find((item) => item.value === row.order_mode)?.img"
                alt=""
              />
              <div
                class="order-mode-box-label"
                :style="{ color: orderMode.find((item) => item.value === row.order_mode)?.color }"
              >
                {{ orderMode.find((item) => item.value === row.order_mode)?.label }}
              </div>
            </div>
          </template>

          <template #status="{ row }">
            <div
              v-if="tabs === 1"
              class="transaction-status"
              :style="{
                background: row.status === 1 ? '#FFE5D1' : row.status === 2 ? '#E0F2E5' : '#ECEFF5',
                color: row.status === 1 ? '#FC7C14' : row.status === 4 ? '#499D60' : '#516082',
              }"
            >
              {{ row.status === 1 ? '待付款' : row.status === 4 ? '已完成' : '已' + t('payment.close') }}
            </div>
            <div
              v-else-if="tabs === 2"
              class="transaction-status"
              :style="{
                background: orderStatusMap.find((item) => item.value === row?.status)?.bgc,
                color: orderStatusMap.find((item) => item.value === row?.status)?.color,
              }"
            >
              {{ orderStatusMap.find((item) => item.value === row?.status)?.text }}
            </div>
            <div v-else-if="tabs === 3">{{ row.initiate_type === 0 ? '买家' : '商家' }}</div>
          </template>
          <template #empty>
            <div>
              <!-- <Empty style="margin-top: 10vh" /> -->
              <REmpty name="no-data"></REmpty>
            </div>
          </template>
          <template #invoiceStatus="{ row }">
            <div style="display: flex; align-items: center">
              <div v-if="tabs === 1">
                {{
                  row.invoiceStatus === 0
                    ? t('order.notInvoiced')
                    : row.invoiceStatus === 1
                      ? t('order.kpz')
                      : row.invoiceStatus === 2
                        ? t('order.notInvoiced')
                        : t('order.Invoiced')
                }}
              </div>
              <div
                v-else-if="tabs === 3"
                class="refund-status"
                :style="{
                  background:
                    row.refund_status === 0 || row.refund_status === 4
                      ? '#FFE5D1'
                      : row.refund_status === 1
                        ? '#FFE5D1'
                        : row.refund_status === 2
                          ? '#E0F2E5'
                          : '#ECEFF5',
                  color:
                    row.refund_status === 0 || row.refund_status === 4
                      ? '#FC7C14'
                      : row.refund_status === 1
                        ? '#FC7C14'
                        : row.refund_status === 2
                          ? '#499D60'
                          : '#516082',
                }"
              >
                {{
                  row.refund_status === 4
                    ? '退款审核中'
                    : row.refund_status === 0
                      ? '待商家处理'
                      : row.refund_status === 1
                        ? '待买家处理'
                        : row.refund_status === 2
                          ? '已退款'
                          : '已取消'
                }}
              </div>
              <div v-else>{{ t('order.notInvoiced') }}</div>
            </div>
          </template>

          <template #createdAt="{ row }">
            <div style="display: flex; align-items: center">
              <div v-if="row.createdAt">{{ getTime(new Date(row.createdAt * 1000), true) }}</div>
              <div v-else-if="row.created_at">{{ getTime(new Date(row.created_at * 1000), true) }}</div>
            </div>
          </template>
          <template #operate="{ row }">
            <div class="btn-box">
              <!--  -->
              <div v-if="tabs === 1" style="display: flex; flex-direction: column; align-items: start">
                <div class="my-order-btn" @click="openorderDetails(row)">{{ t('order.ditiles') }}</div>
                <div v-if="row.status === 1" class="my-order-btn" @click="prompts(row)">立即支付</div>
                <div v-if="row.status === 1" class="my-order-btn" @click="closeOrderFn(row)">
                  取消{{ t('order.order') }}
                </div>
                <!-- 订单1111 -->
                <!-- ('order.Applyforinvoicing') -->
                <div
                  v-if="row.invoiceStatus === 0 && row.status === 4"
                  class="my-order-btn"
                  @click="applyInvoice(row, '申请开票')"
                >
                  {{ t('order.Viewinvoice') }}
                </div>
                <div
                  v-if="row.invoiceStatus === 1 && row.status === 4"
                  class="my-order-btn"
                  @click="applyInvoice(row, '开票信息')"
                >
                  {{ t('order.Viewinvoice') }}
                </div>
                <div
                  v-if="row.invoiceStatus === 3 && row.status === 4"
                  class="my-order-btn"
                  @click="applyInvoice(row, '查看发票')"
                >
                  {{ t('order.Viewinvoice') }}
                </div>
                <div
                  v-if="row.invoiceStatus === 2 && row.status === 4"
                  class="my-order-btn"
                  @click="applyInvoice(row, '拒绝开票原因')"
                >
                  {{ t('order.Viewinvoice') }}
                </div>
                <div v-if="row.status === 0" class="my-order-btn" @click="shutdownReason(row.cancelReason)">
                  {{ t('payment.close') }}原因
                </div>
              </div>
              <div
                v-else-if="tabs === 2"
                class="operation-container"
                style="display: flex; flex-direction: column; align-items: start"
              >
                <!-- 按钮数量等于3时，直接显示全部3个按钮 -->
                <template v-if="getBusinessOrderButtons(row).length === 3">
                  <template v-for="(btn, index) in getBusinessOrderButtons(row)" :key="index">
                    <div v-permission="btn.permission" class="my-order-btn" @click="btn.action">
                      {{ btn.text }}
                    </div>
                  </template>
                </template>

                <!-- 按钮数量超过3个时，显示前2个按钮加"更多"按钮 -->
                <template v-else>
                  <template v-for="(btn, index) in getBusinessOrderButtons(row)" :key="index">
                    <div v-if="index < 2" v-permission="btn.permission" class="my-order-btn" @click="btn.action">
                      {{ btn.text }}
                    </div>
                  </template>

                  <!-- 只有按钮数大于2时才显示"更多"按钮 -->
                  <div v-if="getBusinessOrderButtons(row).length > 2" class="more-menu-container">
                    <t-popup placement="left" :z-index="9" overlay-inner-class-name="order-more-menu-container-popup">
                      <div class="more-btn"><iconpark-icon class="iconmoreicon" name="iconmore"></iconpark-icon></div>
                      <template #content>
                        <div
                          v-for="(btn, index) in getBusinessOrderButtons(row)"
                          :key="index"
                          class="more-menu-container-popup-item"
                        >
                          <div v-if="index >= 2" v-permission="btn.permission" class="menu-item" @click="btn.action">
                            {{ btn.text }}
                          </div>
                        </div>
                      </template>
                    </t-popup>
                  </div>
                </template>
              </div>
              <div v-else-if="tabs === 3" style="display: flex; flex-direction: column; align-items: start">
                <div class="my-order-btn" @click="refundDetailFn(row)">详情</div>
                <div v-if="row.initiate_type === 0 && row.refund_status === 1" class="my-order-btn" @click="again(row)">
                  重新申请
                </div>
                <div
                  v-if="row.initiate_type === 0 && (row.refund_status === 1 || row.refund_status === 0)"
                  class="my-order-btn"
                  @click="cancelRefundFn(row)"
                >
                  取消申请
                </div>
                <div
                  v-if="row.initiate_type === 1 && row.refund_status === 1"
                  class="my-order-btn"
                  @click="agreeRefundFn(row)"
                >
                  同意退款
                </div>
                <div
                  v-if="row.initiate_type === 1 && row.refund_status === 1"
                  class="my-order-btn"
                  @click="RejustRefundFn(row)"
                >
                  拒绝退款
                </div>
              </div>
            </div>
          </template>
        </t-table>
      </t-config-provider>
    </div>
    <div v-if="total && total > 10" class="m20">
      <t-pagination
        :total="total"
        :total-content="`共${total}条`"
        show-previous-and-next-btn
        :show-page-size="true"
        :current="pagination.current"
        :page-size="pagination.pageSize"
        @change="pageChange"
      />
    </div>
    <t-dialog v-model:visible="closeFlag" :close-btn="false" :header="true" width="384">
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>{{ t('order.closejet') }}</div>
          <img
            style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="closeFlag = false"
          />
        </div>
      </template>
      <div class="send-kr-box" style="color: #516082">
        {{ closeText }}
      </div>
      <template #footer>
        <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
          <t-button theme="default" class="min-80" variant="outline" @click="closeFlag = false">
            {{ t('payment.close') }}
          </t-button>
        </div>
      </template>
    </t-dialog>
    <t-dialog v-model:visible="dilatationFlag" :close-btn="false" :header="true" width="384">
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>{{ $t('order.cancel_order') }}</div>

          <img
            style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="((dilatationFlag = false), (reason = ''))"
          />
        </div>
      </template>
      <div class="send-kr-box">
        <t-textarea
          v-model="reason"
          :maxlength="200"
          :autosize="{ minRows: 3, maxRows: 5 }"
          :placeholder="$t('order.please_input_order')"
        />
      </div>
      <template #footer>
        <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
          <t-button theme="default" class="min-80" variant="outline" @click="((dilatationFlag = false), (reason = ''))">
            取消
          </t-button>
          <t-button :disabled="reason ? false : true" class="min-80" @click="sendKR">
            {{ $t('identity.confirm') }}
          </t-button>
        </div>
      </template>
    </t-dialog>
    <!-- 高级筛选 -->
    <t-drawer v-model:visible="drawerSeachFlag" :close-btn="false" size="472px">
      <template #header>
        <div class="drawer-header flex-al-space">
          <div>{{ t('approval.approval_data.sur') }}</div>
          <img
            style="width: 20px; cursor: pointer; height: 20px; -webkit-app-region: no-drag"
            src="@assets/<EMAIL>"
            @click="drawerSeachFlag = false"
          />
        </div>
      </template>

      <div class="form-boxxx">
        <div v-if="tabs === 1" class="fitem">
          <div class="title">{{ t('order.orderstats') }}</div>
          <div class="ctl">
            <!-- <img :src="asd"> -->
            <!-- ="asd"  -->
            <t-select v-model="searchFormData.status" :placeholder="t('contacts.pleaseSelect')">
              <!-- 订单状态 -->
              <t-option key="apple" label="待付款" :value="1" />
              <t-option key="orange" label="已完成" :value="4" />
              <t-option key="banana" :label="'已' + t('payment.close')" :value="0" />
            </t-select>
          </div>
        </div>
        <div v-if="tabs === 2 || tabs === 3" class="fitem">
          <div class="title">订单类型</div>
          <div class="ctl">
            <t-select v-model="searchFormData.order_mode" :placeholder="t('contacts.pleaseSelect')">
              <t-option key="0" label="其他收款" :value="0" />
              <t-option key="1" label="同城配送" :value="1" />
              <t-option key="2" label="自提" :value="2" />
              <t-option key="3" label="物流配送" :value="3" />
            </t-select>
          </div>
        </div>
        <div v-if="tabs === 2" class="fitem">
          <div class="title">评价状态</div>
          <div class="ctl">
            <t-select v-model="searchFormData.has_comment" :placeholder="t('contacts.pleaseSelect')">
              <t-option key="1" label="已评价" :value="1" />
              <t-option key="0" label="待评价" :value="0" />
            </t-select>
          </div>
        </div>
        <div v-if="tabs === 2" class="fitem">
          <div class="title">{{ t('order.orderstats') }}</div>
          <div class="ctl">
            <t-select v-model="searchFormData.status" :placeholder="t('contacts.pleaseSelect')">
              <t-option key="apple" label="待付款" :value="1" />
              <!-- [2,4,5,6] -->
              <t-option key="appl1e" label="待收货/使用" value="2,4,5,6,7,8" />

              <t-option key="orange" label="已完成" :value="100" />
              <t-option key="banana" :label="'已' + t('payment.close')" :value="0" />
            </t-select>
          </div>
        </div>
        <div v-if="tabs === 1" class="fitem">
          <div class="title">{{ t('order.invoiceStatus') }}</div>
          <div class="ctl">
            <t-select v-model="searchFormData.invoice_status" :placeholder="t('contacts.pleaseSelect')">
              <t-option key="apple" :label="t('order.notInvoiced')" :value="'0,2'" />
              <t-option key="orange" label="开票中" :value="1" />
              <t-option key="banana" :label="t('order.Invoiced')" :value="3" />
            </t-select>
          </div>
        </div>
        <div v-if="tabs === 1 || tabs === 2" class="fitem">
          <div class="title">{{ t('order.orderTime') }}</div>
          <div class="ctl">
            <t-date-range-picker
              v-model="searchFormData.created_at"
              :placeholder="['开始日期', '结束日期']"
              style="width: 100%"
              allow-input
              clearable
            />
          </div>
        </div>
        <div v-if="tabs === 1" class="fitem">
          <div class="title">是否退款</div>
          <div class="ctl">
            <t-select v-model="searchFormData.is_refund" :placeholder="t('contacts.pleaseSelect')">
              <t-option key="apple" label="是" :value="1" />
              <t-option key="orange" label="否" :value="0" />
            </t-select>
          </div>
        </div>
        <div v-if="tabs === 1" class="fitem">
          <div class="title">订单编号</div>
          <div class="ctl">
            <t-input v-model="searchFormData.sn"></t-input>
          </div>
        </div>
        <div v-if="tabs === 2" class="fitem">
          <div class="title">退款情况</div>
          <div class="ctl">
            <t-select v-model="searchFormData.refund_status">
              <t-option key="0" label="无退款" :value="0" />
              <t-option key="1" label="退款中" :value="1" />
              <t-option key="2" label="部分退款" :value="2" />
              <t-option key="3" label="退款成功" :value="3" />
            </t-select>
          </div>
        </div>
        <div v-if="tabs === 3" class="fitem">
          <div class="title">提交时间</div>
          <div class="ctl">
            <t-date-range-picker
              v-model="searchFormData.created_at"
              :placeholder="t('order.Pleaseselectadate')"
              style="width: 100%"
              allow-input
            />
          </div>
        </div>
        <div v-if="tabs === 3" class="fitem">
          <div class="title">退款状态</div>
          <div class="ctl">
            <t-select v-model="searchFormData.refund_status">
              <t-option key="0" label="待商家处理" :value="0" />
              <t-option key="1" label="待买家处理" :value="1" />
              <t-option key="2" label="已退款" :value="2" />
              <t-option key="3" label="已取消" :value="3" />
            </t-select>
          </div>
        </div>
        <div v-if="tabs === 3" class="fitem">
          <div class="title">发起方</div>
          <div class="ctl">
            <t-select v-model="searchFormData.initiate_type">
              <t-option key="0" label="买家" :value="0" />
              <t-option key="1" label="商家" :value="1" />
            </t-select>
          </div>
        </div>
        <div v-if="tabs === 2" class="fitem">
          <div class="title">收款来源</div>
          <div class="ctl">
            <t-select v-model="searchFormData.origin">
              <t-option key="1" label="店铺订单" value="store_product" />
              <t-option key="0" label="活动收款" value="campaign_activity" />
            </t-select>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="foot" style="float: right">
          <t-button theme="default" style="font-weight: 600" class="min-80" @click="rest">重置</t-button>
          <!-- restSearchFormData -->
          <t-button :disabled="!searchFormDataFlag" style="font-weight: 600" class="min-80" @click="searchFor()">
            {{ t('account.confirm') }}
          </t-button>
        </div>
      </template>
    </t-drawer>
    <t-dialog :visible="agreeRefundModal" theme="info" header="提示">
      <template #body>
        <div>
          <span>退款金额：</span>
          <span v-if="agreeData" style="color: #d54941">
            {{ agreeData?.currency === 'CNY' ? '¥' : 'MOP' }} {{ agreeData?.refund_amount }}
          </span>
          ，同意退款后金额原路返回，是否同意退款？
        </div>
      </template>
      <template #footer>
        <t-button theme="default" @click="agreeRefundModal = false">取消</t-button>
        <t-button theme="primary" @click="agreeWay">同意</t-button>
      </template>
    </t-dialog>
    <!-- visible控制抽屉开关   order-callback更新列表数据你那边应该用不上  orderDrawerRef.value.openWin(row);row里必传sn,row里cancelReason拒绝原因非必填-->
    <InvoiceDrawer
      ref="InvoiceDrawerRef"
      :team-id="teamId"
      :visible="Invoicevisible"
      @order-callback="paymentCallback"
    />
    <orderDrawer
      ref="orderDrawerRef"
      :tabs="tabs"
      :team-id="teamId"
      :visible="visible"
      @order-callback="paymentCallback"
    />
    <!-- <selectOrganizationGoWeb ref="selectOrganizationGoWebRef" :team-id="teamId"></selectOrganizationGoWeb> -->
    <public-payment
      ref="paymentDialogTwoRef"
      :team-id="teamId"
      @payclose="tabChange(2)"
      @payment-callback="tabChange(2)"
    />
    <reback-modal ref="rebackModalRef" :team-id="teamId" @get-data-list="tabChange(2)" />
    <close-order ref="closeOrderRef" :team-id="teamId" @update="tabChange" />
    <RejustRefund ref="RejustRefundRef" :team-id="teamId" @update="tabChange(3)" />
    <businessDetail
      ref="businessDetailRef"
      :team-id="teamId"
      @get-bussiness-list="getBussinessList"
      @index-get-list="tabChange(2)"
    />
    <refundDetail ref="refundDetailRef" :team-id="teamId" @index-get-list="tabChange(3)" />
    <refundListModal ref="refundListModalRef" :team-id="teamId" @get-data-list="tabChange(3)" />
    <!-- 评论 -->
    <EvaluationMg ref="evaluationMgRef" :is-show-fotter="true" @get-list="getBussinessList"></EvaluationMg>
    <!-- 物流信息 -->
    <logisticsLog ref="LogisticsInfoRef" :show-tel-flag="true" />
    <!-- 配送信息 -->
    <RouteMap ref="RouteMapRef" :hasfooter="false" :is-customer="true" :type="deliveryOrmerchantFlag"></RouteMap>
    <!-- <paymentDialog
      ref="paymentDialogRef"
      @payment-callback="paymentCallback"
      @get-data-list="getDataList"
    /> -->
  </div>
</template>
<script setup lang="ts">
import InvoiceDrawer from './components/InvoiceDrawer.vue';
import orderDrawer from './components/orderDrawer.vue';
import RouteMap from '@pages/shop/view/workbench/orders/components/deliveryRouteDetails/index.vue';
// import selectOrganizationGoWeb from './components/selectOrganizationGoWeb.vue';
import { useRoute } from 'vue-router';
import { orderStatusMap } from '@pages/shop/view/workbench/orders/orderStatusHelpers.ts';
import TitleBar from '@components/common/BusinessBar.vue';
import { REmpty } from '@rk/unitPark';
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
// import { jumpWeb } from '@renderer/views/contacts/utils';
const Invoicevisible = ref(false);
const orderDrawerRef = ref(null);
const InvoiceDrawerRef = ref(null);
import { getTime } from '../utils';
import { bussinessOrderDetail } from '@pages/common/api/index.ts';
import { orderList, orderCancel, bussinessOrderList, refundList, refundProcess, teamsauth } from '../api';
import { finishOrder } from '@pages/shop/view/workbench/orders/apis';
import EvaluationMg from '@pages/shop/view/comment-management/components/EvaluationMg.vue';
const route = useRoute();
// import asd from "../../assets/notData.svg";
import publicPayment from './components/publicPayment/index.vue';
import rebackModal from './components/teamReceipt/rebackModal.vue';
import closeOrder from './components/teamReceipt/closeOrder.vue';
import RejustRefund from './components/teamReceipt/rejustRefund.vue';
import businessDetail from './components/teamReceipt/bussinessDetail.vue';
import refundDetail from './components/teamReceipt/refundDetail.vue';
import refundListModal from './components/teamReceipt/refundList.vue';
import logisticsLog from '@pages/shop/view/workbench/orders/components/logistics/logisticsLog.vue';
import {
  payNowOrCancelOrder,
  applyRefund,
  evaluate,
  getPickupCode,
  confirmReceiptBtn,
  viewLogisticsBtn,
  refundReason,
  viewps,
  refundRecord,
} from '@pages/shop/view/workbench/orders/orderStatusHelpers.ts';
import { PickupCode } from '@components/pickup-code';

// const jump = () => {
//   // 跳转官网
//   // jumpWeb('/lkunion', { inviteCode: 'LAL202752', region: 'CN', sign: 'kyy_partner_invite' }, 'website');
// };
const deliveryOrmerchantFlag = ref('delivery');
const orderMode = ref([
  {
    label: '其他收款',
    value: 0,
    img: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/skdd.svg',
    color: '#40BAFF',
  },
  {
    label: '同城配送',
    value: 1,
    img: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/tcps.svg',
    color: '#706AFF;',
  },
  {
    label: '自提',
    value: 2,
    img: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/zt.svg',
    color: '#55B870;',
  },
  {
    label: '物流配送',
    value: 3,
    img: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/wlps.svg',
    color: '#FF8324',
  },
]);
const tabs = ref(1);
const teamId = ref(0);
const tabChangeTwo = (val: number) => {
  searchFormData.value.invoice_status = '';
  searchFormData.value.goods = '';
  searchFormData.value.is_refund = '';
  searchFormData.value.sn = '';

  searchFormData.value.status = '';
  searchFormData.value.created_at = [];
  searchFormData.value.refund_status = '';
  searchFormData.value.submit_at = [];
  formData.value.invoice_status = '';
  formData.value.goods = '';
  formData.value.is_refund = '';
  formData.value.sn = '';
  formData.value.refund_status = '';
  formData.value.submit_at = [];
  formData.value.status = '';
  formData.value.created_at = [];
  pagination.current = 1;
  pagination.pageSize = 10;
  tableData.value = [];
  tabs.value = val;
  if (val === 1) {
    getList();
  } else if (val === 2) {
    getBussinessList();
  } else {
    getRefundList();
  }
};
const titleBarTitle = ref('个人订单');
const changeTeam = (teamid, title) => {
  console.log('chufazheliaaaaaaaaaa', teamid);
  titleBarTitle.value = title;
  teamId.value = teamid;
  tabs.value = 1;
  tabChangeTwo(1);
};
const tabChange = (val: number) => {
  if (val === 1) {
    getList();
  } else if (val === 2) {
    getBussinessList();
  } else {
    getRefundList();
  }
};
// // 立即支付,取消订单,申请退款,评价,取货码,确认收货,查看物流,退款原因,退款记录
// const ljzf=()=>{
// // 收款订单
// if (orderDetail.value.order_mode === 0) {

// }
// // 配送订单
// if (orderDetail.value.order_mode === 1) {

// }
// // 自提订单
// if (orderDetail.value.order_mode === 2) {

// }
// // 物流订单
// if (orderDetail.value.order_mode === 3) {

// }

// }

const businessDetailRef = ref(null);
const refundDetailRef = ref(null);
const businessDetailFn = (row) => {
  nextTick(() => {
    if (businessDetailRef.value) {
      businessDetailRef.value.open(row, getBusinessOrderButtons(row));
    } else {
      console.log('未初始化完成businessDetailRef');
    }
  });
};

const refundDetailFn = (val) => {
  nextTick(() => {
    if (refundDetailRef.value) {
      refundDetailRef.value.open(val, teamId.value);
    } else {
      console.log('未初始化完成refundDetailRef');
    }
  });
};

const refundListModalRef = ref(null);
const refundListFn = (val) => {
  refundListModalRef.value.open(val.id);
};

const paymentDialogTwoRef = ref(null);
const promptsTwo = (row) => {
  if (row === '') {
    MessagePlugin.error('订单状态已变更,请重新操作');

    getBussinessList();
  } else if (row === '没有支付渠道') {
    const confirmDia = DialogPlugin.alert({
      header: '提示',
      theme: 'info',
      body: t('order.closeTip'),
      closeBtn: false,
      confirmBtn: '知道了',
      className: 'delmode',
      onConfirm: async () => {
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  } else {
    rowData.value = JSON.parse(JSON.stringify(row));
    rowData.value.amount = row.payAmount;
    paymentDialogTwoRef.value.openWin(row);
  }
};
const rebackModalRef = ref(null);
const rebackMoney = (row) => {
  rebackModalRef.value.open(row);
};

const closeOrderRef = ref(null);
const again = (row) => {
  closeOrderRef.value.open(row.refund_id, 1, row);
};

const closeOrderWay = (row) => {
  // 取消订单
  closeOrderRef.value.open(row.id, 0, row);
};

const cancelRefundFn = (row) => {
  // 取消退款
  const confirmDia = DialogPlugin({
    header: '提示',
    body: '确定取消该退款订单申请？',
    theme: 'info',
    confirmBtn: {
      content: '确定',
    },
    cancelBtn: {
      content: '取消',
      variant: 'outline',
      theme: 'default',
    },
    onConfirm: () => {
      const obj = {
        refund_id: row.refund_id,
        process_type: 4,
      };
      console.log(teamId.value, 7777);

      refundProcess(obj, teamId.value)
        .then((res) => {
          if (res.code === 0) {
            visible.value = false;
            MessagePlugin.success('退款订单已取消');
            tabChange(3);
          } else if (res.code === 60300) {
            ToBeConfirmedTwo(res.message, 3);
          } else {
            MessagePlugin.error(res.message);
          }
        })
        .catch((err) => {
          if (err.response.data.code === 60300) {
            visible.value = false;
            ToBeConfirmedTwo(err.response.data.message, 3);
          }
        });
      confirmDia.hide();
    },
    className: 'shutdownReasonmode',
    onClose: () => {
      confirmDia.hide();
    },
  });
};

const ToBeConfirmedTwo = (val, val2) => {
  // 待确认
  const ToBeConfirmedDia = DialogPlugin({
    header: '提示',
    body: `${val}`,
    theme: 'info',
    confirmBtn: {
      content: '知道了',
    },
    cancelBtn: null,
    onConfirm: () => {
      tabChange(val2);
      ToBeConfirmedDia.hide();
    },
  });
};

// const ToBeConfirmed = () => {
//   // 待确认
//   const ToBeConfirmedDia = DialogPlugin({
//     header: '提示',
//     body: '退款订单状态已变更,请重新确认',
//     theme: 'info',
//     confirmBtn: {
//       content: '知道了',
//     },
//     cancelBtn: null,
//     onConfirm: () => {
//       ToBeConfirmedDia.hide();
//     },
//   });
// };
const agreeRefundModal = ref(false);
const agreeData = ref(null);
const agreeRefundFn = (row) => {
  agreeRefundModal.value = true;
  agreeData.value = row;
};

const agreeWay = () => {
  const obj = {
    refund_id: agreeData.value.refund_id,
    process_type: 1,
  };
  console.log(teamId.value, 66655);

  refundProcess(obj, teamId.value)
    .then((res) => {
      if (res.code === 0) {
        visible.value = false;
        const rebackTips = DialogPlugin({
          header: '提示',
          body: '退款金额原路退回中，退款成功后会发送通知，请注意查收！',
          theme: 'info',
          confirmBtn: {
            content: '知道了',
          },
          cancelBtn: null,
          onConfirm: () => {
            rebackTips.hide();
            tabChange(3);
          },
        });
      } else if (res.code === 60300) {
        ToBeConfirmedTwo(res.message, 3);
      } else {
        MessagePlugin.error(res.message);
      }
    })
    .catch((err) => {
      if (err.response.data.code === 60300) {
        visible.value = false;
        ToBeConfirmedTwo(err.response.data.message, 3);
      }
    });
  agreeRefundModal.value = false;
};

const RejustRefundRef = ref(null);
const RejustRefundFn = (row) => {
  RejustRefundRef.value.open(row);
};

const openorderDetails = (row) => {
  orderDrawerRef.value.openWin(row);
};
const applyInvoice = (row, flag) => {
  console.log(row, flag, 'applyInvoiceapplyInvoiceapplyInvoicech查看发票');

  if (flag === '开票信息' || flag === '查看发票' || flag === '申请开票' || flag === '拒绝开票原因') {
    // 打开发票抽屉
    InvoiceDrawerRef.value.openWin(row);

    return;

    // if (flag!=='申请开票') {
    //   // flag==='开票信息'||flag==='查看发票'||flag==='申请开票'||flag==='拒绝开票原因'
    //   // row.invoiceId:发票id
    //   // row.sn:订单id
    //   orderDrawerRef.value.openApplyInvoiceDialog({
    //     ...row,
    //     flag,
    //   });
    // } else {
    //   orderDrawerRef.value.openApplyInvoiceDialog({
    //     ...row,
    //     flag,
    //   });
    // }
  }
};
const searchFormData = ref({
  created_at: [],
  status: '',
  goods: '',
  sn: '',
  is_refund: '',
  invoice_status: '',
  order_mode: '',
  has_comment: '',
  refund_status: '',
  submit_at: [],
  initiate_type: '',
  origin: '',
  keyword: '',
});

const formData = ref({
  created_at: [],
  status: '',
  goods: '',
  sn: '',
  is_refund: '',
  invoice_status: '',
  refund_status: '',
  order_mode: '',
  has_comment: '',
  submit_at: [],
  initiate_type: '',
  origin: '',
  keyword: '',
});
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log('pagination.onChange', pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    getList();
  },
});

// const selectOrganizationGoWebRef = ref(null);
// const openGroup = () => {
//   selectOrganizationGoWebRef.value.openWin(null, 'order');
// };
// 评价
const evaluationMgRef = ref(null);
const openEvaluate = (row) => {
  // viewEvaluationMg.value = row.has_comment === 1;
  evaluationMgRef.value.openWin(row);
};
// 取货码
const pickUp = (row) => {
  console.log(row, 'rowwwwwwwwwwww');

  PickupCode(row.pickup_sn);
};
const LogisticsInfoRef = ref(null);
// 查看物流
const viewLogistics = (row) => {
  console.log(row, teamId.value, 'viewLogisticsviewLogisticsviewLogistics');
  // if (teamId.value === 0) {
  //   LogisticsInfoRef.value.show(row.sn, false);
  // } else {
  LogisticsInfoRef.value.show(row.sn, true, teamId.value);
  // }
};
// 确认收货
const confirmReceipt = (row) => {
  console.log(row, 'resssssssssssss');

  const confirmDia = DialogPlugin({
    header: '提示',
    theme: 'info',
    body: `订单还在"${orderStatusMap.find((item) => item.value === row.status)?.text}订单状态"中，请确认商品收到后再点击，是否确认完成订单？`,
    confirmBtn: '确认',
    cancelBtn: '取消',
    onConfirm: () => {
      finishOrder({ id: row.id }, teamId.value)
        .then((res) => {
          if (res.code === 0) {
            MessagePlugin.success('订单已确认完成');
            // getList();
            businessDetailRef.value.getDt(row);
            getBussinessList();
          } else {
            MessagePlugin.error(res.message);
          }
        })
        .catch((err) => {
          const alertDia = DialogPlugin.alert({
            header: '提示',
            theme: 'info',
            body: err.response.data.message,
            confirmBtn: '我知道了',

            cancelBtn: null,
            onConfirm: ({ e }) => {
              getBussinessList();
              businessDetailRef.value.getDt(row);

              console.log('confirm e: ', e);
              alertDia.hide();
            },
          });
        });
      confirmDia.hide();
      // business-order/finish
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
  console.log(row, 'confirmReceiptconfirmReceiptconfirmReceipt');
};
const dealWithParams = () => {
  const params = new URLSearchParams(window.location.hash.split('?')[1]);
  const tab = Number(params.get('type'));
  const sn = params.get('sn');
  if (tab) {
    tabs.value = tab;
    tabChangeTwo(tab);
  }
  if (!sn) {
    return;
  }
  setTimeout(() => {
    if (tab === 2) {
      businessDetailFn({ sn });
    } else if (tab === 3) {
      refundDetailFn({ sn, refund_sn: sn });
    }
  }, 400);
};

onMounted(() => {
  if (route.query.teamId && Number(route.query.teamId) !== 0) {
    teamId.value = route.query.teamId;
  } else {
    teamId.value = 0;
  }
  getList();
  // 处理传参跳转事件
  dealWithParams();

  // document.addEventListener('click', () => {
  //   ipcRenderer.invoke('set-popbv', { show: true, type: 'personal' });
  // }, {
  //   capture: true,
  // });
});
// onActivated(() => {
//   getList();
// });
const searchFlag = computed(
  () =>
    formData.value.goods !== '' ||
    formData.value.is_refund !== '' ||
    formData.value.status !== '' ||
    formData.value.order_mode !== '' ||
    formData.value.has_comment !== '' ||
    formData.value.sn !== '' ||
    formData.value.created_at.length > 0 ||
    formData.value.invoice_status !== '' ||
    formData.value.refund_status !== '' ||
    formData.value.submit_at.length > 0 ||
    formData.value.initiate_type !== '' ||
    formData.value.origin !== '' ||
    formData.value.keyword !== '',
);
const searchFormDataFlag = computed(
  () =>
    searchFormData.value.status !== '' ||
    searchFormData.value.created_at.length > 0 ||
    searchFormData.value.invoice_status !== '' ||
    searchFormData.value.is_refund !== '' ||
    searchFormData.value.order_mode !== '' ||
    searchFormData.value.has_comment !== '' ||
    searchFormData.value.sn !== '' ||
    searchFormData.value.refund_status !== '' ||
    searchFormData.value.submit_at.length > 0 ||
    searchFormData.value.initiate_type !== '' ||
    searchFormData.value.origin !== '' ||
    searchFormData.value.goods !== '' ||
    searchFormData.value.keyword !== '',
);

const paymentCallback = () => {
  // 支付成功后的回调
  getList();
};
const teamsAuthList = ref([]);
const getList = async () => {
  teamsauth('order').then((res) => {
    teamsAuthList.value = res.data;
    if (teamId.value !== 0) {
      const team = teamsAuthList.value.find((item) => item.team_id === teamId.value);
      if (team) {
        titleBarTitle.value = team.full_name;
        teamId.value = team.team_id;
      }
    }
  });
  const param = {
    pageSize: pagination.pageSize,
    page: pagination.current,
    ...formData.value,
  };
  try {
    let res = await orderList(param, teamId.value);
    total.value = res.data.count;
    tableData.value = res.data.list;
  } catch (error) {
    console.error('获取订单列表失败:', error);
    MessagePlugin.error('获取订单列表失败');
  }
  // if (teamId.value !== 0) {
  //   teamOrderList(param,teamId.value).then((res) => {
  //     total.value = res.data.count;
  //     tableData.value = res.data.list;
  //   });
  // } else {
  //   orderList(param).then((res) => {
  //     total.value = res.data.count;
  //     tableData.value = res.data.list;
  //   });
  // }
};

const getBussinessList = () => {
  const param = {
    pageSize: pagination.pageSize,
    page: pagination.current,
    ...formData.value,
  };
  bussinessOrderList(param, teamId.value).then((res) => {
    pagination.total = res.data.total;
    tableData.value = res.data.list;
    total.value = res.data.total;
    window.localStorage.removeItem('orderId');
  });
};

const getRefundList = () => {
  const param = {
    pageSize: pagination.pageSize,
    page: pagination.current,
    ...formData.value,
  };
  refundList(param, teamId.value).then((res) => {
    pagination.total = res.data.total;
    total.value = res.data.total;
    tableData.value = res.data.list;
    console.log(tableData.value, res, 'qqqqqqqqqqqqqq');
  });
};

const searchFor = () => {
  formData.value.created_at = JSON.parse(JSON.stringify(searchFormData.value.created_at));
  formData.value.status = JSON.parse(JSON.stringify(searchFormData.value.status));
  formData.value.goods = JSON.parse(JSON.stringify(searchFormData.value.goods));
  formData.value.is_refund = JSON.parse(JSON.stringify(searchFormData.value.is_refund));
  formData.value.order_mode = JSON.parse(JSON.stringify(searchFormData.value.order_mode));
  formData.value.has_comment = JSON.parse(JSON.stringify(searchFormData.value.has_comment));
  formData.value.sn = JSON.parse(JSON.stringify(searchFormData.value.sn));

  formData.value.invoice_status = JSON.parse(JSON.stringify(searchFormData.value.invoice_status));
  formData.value.refund_status = JSON.parse(JSON.stringify(searchFormData.value.refund_status));
  formData.value.submit_at = JSON.parse(JSON.stringify(searchFormData.value.submit_at));
  formData.value.keyword = JSON.parse(JSON.stringify(searchFormData.value.keyword));
  formData.value.initiate_type = JSON.parse(JSON.stringify(searchFormData.value.initiate_type));
  formData.value.origin = JSON.parse(JSON.stringify(searchFormData.value.origin));
  pagination.current = 1;
  drawerSeachFlag.value = false;
  if (tabs.value === 1) {
    getList();
  } else if (tabs.value === 2) {
    getBussinessList();
  } else {
    getRefundList();
  }
};
const drawerSeachFlag = ref(false);
const rest = () => {
  searchFormData.value.invoice_status = '';
  searchFormData.value.goods = '';
  searchFormData.value.is_refund = '';
  searchFormData.value.sn = '';

  searchFormData.value.status = '';
  searchFormData.value.created_at = [];
  searchFormData.value.refund_status = '';
  searchFormData.value.submit_at = [];
  searchFormData.value.keyword = '';
  searchFormData.value.initiate_type = '';
  searchFormData.value.origin = '';
  searchFormData.value.order_mode = '';
  searchFormData.value.has_comment = '';
  formData.value.invoice_status = '';
  formData.value.goods = '';
  formData.value.is_refund = '';
  formData.value.sn = '';
  formData.value.refund_status = '';
  formData.value.submit_at = [];
  formData.value.status = '';
  formData.value.created_at = [];
  formData.value.keyword = '';
  formData.value.initiate_type = '';
  formData.value.origin = '';
  formData.value.order_mode = '';
  formData.value.has_comment = '';
  drawerSeachFlag.value = false;
  pagination.pageSize = 10;
  pagination.current = 1;
  if (tabs.value === 1) {
    getList();
  } else if (tabs.value === 2) {
    getBussinessList();
  } else {
    getRefundList();
  }
};

const { t } = useI18n();
const visible = ref(false);
const reason = ref('');
const total = ref(200);

const pageChange = (e) => {
  pagination.current = e.current;
  pagination.pageSize = e.pageSize;
  if (tabs.value === 1) {
    getList();
  } else if (tabs.value === 2) {
    getBussinessList();
  } else {
    getRefundList();
  }
};
const rowData = ref({
  sn: '',
});
const addCommasToNumber = (str) => {
  let [integerPart, decimalPart] = str.split('.');
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  if (decimalPart) {
    decimalPart = decimalPart.length === 1 ? `${decimalPart}0` : decimalPart.slice(0, 2);
  } else {
    decimalPart = '00';
  }
  console.log(`${integerPart}.${decimalPart}`, '222222222222');

  return `${integerPart}.${decimalPart}`;
};
const isLoading = ref(false);
const dilatationFlag = ref(false);
const closeOrderFn = (row) => {
  // 取消订单
  console.log(row.sn, 'rowwwwwwwwwwww');
  rowData.value = row;
  dilatationFlag.value = true;
};
// 创建订单
const prompts = (row) => {
  if (row === '') {
    MessagePlugin.error(t('order.Theorderstatusoperation'));
    getList();
  } else if (row === '没有支付渠道') {
    console.log(row, 'rowwwwwwwwwwwwwwwwww');
    const confirmDia = DialogPlugin.alert({
      header: '提示',
      theme: 'info',
      body: t('order.closeTip'),
      closeBtn: null,
      confirmBtn: '知道了',
      className: 'delmode',
      onConfirm: async () => {
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  } else {
    // 第一个参数当前行row里面必须有个sn订单号和region订单区域
    let rowDatas = JSON.parse(JSON.stringify(row));
    rowDatas.amount = rowDatas.payAmount;
    console.log(rowDatas, 'rowDatasrowDatasrowDatas');
    orderDrawerRef.value.openpaymentDialog(rowDatas);
    // paymentDialogRef.value.openWin(rowDatas);//打开支付弹窗
  }
};
const closeText = ref('');
const closeFlag = ref(false);
const shutdownReason = (val) => {
  closeText.value = val;
  closeFlag.value = true;
};

const sendKR = () => {
  // 取消付款
  orderCancel(
    {
      sn: rowData.value.sn,
      reason: reason.value,
    },
    teamId.value,
  )
    .then((res) => {
      if (res.code === 0) {
        visible.value = false;
        dilatationFlag.value = false;
        reason.value = '';
      } else {
        MessagePlugin.error(t('order.Theorderstatusoperation'));
        if (visible.value) {
          // orderDetailsFn(rowData.value);注释这个要取消
        }
        reason.value = '';
        dilatationFlag.value = false;
      }
      getList();

      console.log(res, 'resssssssssss');
    })
    .catch((err) => {
      // MessagePlugin.error(err);
      MessagePlugin.error(err.response?.data?.message);
      console.log(err, 'errerrerrerr');
    });
};
const tableData = ref([]);
const RouteMapRef = ref(null);

const columns = ref([
  { colKey: 'sn', title: t('order.orderNo'), ellipsis: true },
  { colKey: 'goods', title: '产品信息', ellipsis: true },
  { colKey: 'createdAt', title: t('order.outeOrderTime'), width: 170, ellipsis: true },
  { colKey: 'payAmount', title: t('order.paymentamount'), ellipsis: true, align: 'right' },
  { colKey: 'status', title: `交易${t('order.stats')}` },
  { colKey: 'invoiceStatus', title: `发票${t('order.stats')}`, ellipsis: true },
  { colKey: 'operate', title: '操作', width: 100 },
]);
const columnsBusiness = ref([
  { colKey: 'sn', title: t('order.orderNo'), ellipsis: true, width: 200 },
  { colKey: 'goods', title: '产品信息', ellipsis: true },
  { colKey: 'createdAt', title: t('order.outeOrderTime'), width: 112, ellipsis: true },
  { colKey: 'payAmount', title: t('order.paymentamount'), width: 200, ellipsis: true, align: 'right' },
  { colKey: 'status', title: `交易${t('order.stats')}`, width: 120 },
  { colKey: 'orderMode', title: `订单类型`, width: 120 },
  { colKey: 'invoiceStatus', title: `发票${t('order.stats')}`, width: 104, ellipsis: true },
  { colKey: 'operate', title: '操作', width: 152 },
]);
const columnsRufend = ref([
  { colKey: 'sn', title: '退款编号', ellipsis: true, width: 200 },
  { colKey: 'goods', title: '退款信息', ellipsis: true },
  { colKey: 'createdAt', title: '提交时间', width: 112, ellipsis: true },
  { colKey: 'payAmount', title: '退款金额', ellipsis: true, align: 'right', width: 200 },
  { colKey: 'status', title: '发起方', width: 120 },
  { colKey: 'invoiceStatus', title: `退款${t('order.stats')}`, ellipsis: true, width: 136 },
  { colKey: 'operate', title: '操作', width: 152 },
]);
const viewpsFn = (row) => {
  console.log(row, 'rowwwwwww');

  deliveryOrmerchantFlag.value = row.delivery_type === 0 ? 'merchant' : 'delivery';
  nextTick(() => {
    if (deliveryOrmerchantFlag.value === 'delivery') {
      bussinessOrderDetail(row.sn, teamId.value).then((res) => {
        RouteMapRef.value.openWin({
          serialNumber: res.data.delivery_info.delivery_order_sn,
          orderId: row.sn,
        });
      });
    } else {
      RouteMapRef.value.openWin({
        orderId: row.sn,
      });
    }
  });
};
// 获取业务订单操作按钮列表
const getBusinessOrderButtons = (row) => {
  const buttons = [];

  // 添加各种按钮到列表中
  buttons.push({ text: '详情', action: () => businessDetailFn(row), alwaysShow: true });

  if (payNowOrCancelOrder(row)) {
    buttons.push({
      text: '立即支付',
      action: () => promptsTwo(row),
      permission: 'value_added_order',
    });
  }

  if (confirmReceiptBtn(row)) {
    buttons.push({ text: '确认收货', action: () => confirmReceipt(row) });
  }

  if (payNowOrCancelOrder(row)) {
    buttons.push({
      text: '取消订单',
      action: () => closeOrderWay(row),
      permission: 'value_added_order',
    });
  }

  if (evaluate(row)) {
    buttons.push({ text: '评价', action: () => openEvaluate(row) });
  }

  if (viewps(row)) {
    buttons.push({ text: '查看配送', action: () => viewpsFn(row) });
  }
  if (viewLogisticsBtn(row)) {
    buttons.push({ text: '查看物流', action: () => viewLogistics(row) });
  }
  if (applyRefund(row)) {
    buttons.push({
      text: '申请退款',
      action: () => rebackMoney(row),
      permission: 'value_added_order',
    });
  }

  if (refundRecord(row)) {
    buttons.push({ text: '退款记录', action: () => refundListFn(row) });
  }

  if (getPickupCode(row)) {
    buttons.push({ text: '取货码', action: () => pickUp(row) });
  }

  if (refundReason(row)) {
    buttons.push({
      text: t('payment.close') + '原因',
      action: () => shutdownReason(row.cancel_reason),
      permission: 'value_added_order',
    });
  }

  return buttons;
};
</script>

<style lang="less" scoped>
:global(.orderTable .t-table--layout-fixed) {
  height: 100%;
}
:global(.orderTable .t-table__body) {
  height: 100%;
}
.order-mode-box {
  display: flex;
  align-items: center;
  width: fit-content;
  border-radius: 12px;
  background: #f5f8fe;
  gap: 4px;
  padding: 2px 8px 2px 4px;
}

.order-mode-box-img {
  width: 20px;
  height: 20px;
}

.order-mode-box-label {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.head-item-lab {
  height: 22px;
  font-size: 14px;

  font-weight: 700;
  color: #13161b;
  padding-left: 8px;
  margin-bottom: 12px;
}

.foot-lab {
  width: 70px;
  height: 22px;
  font-size: 14px;

  font-weight: 400;
  color: #13161b;
  line-height: 22px;
  text-align: right;
}

.foot-val {
  min-width: 120px;
  height: 22px;
  font-size: 14px;

  font-weight: 400;
  text-align: right;
  color: #13161b;
  line-height: 22px;
}

.my-order-box {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  height: 100vh;
}

.flex-a-js-w140 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 140px;
}

.w70text-r {
  width: 75px;
  text-align: right;
}

.my-order-btn:hover {
  border-radius: 4px;
  background: #dbdfff;
  padding: 4px;
  height: 22px;
  line-height: 22px;
  box-sizing: content-box;
}

.my-order-btn {
  width: fit-content;
  box-sizing: content-box;
  height: 22px;
  padding: 4px;
  line-height: 22px;
}

.more-menu-container-popup-item {
  border-radius: 4px;
}

.table-foot-box {
  font-size: 14px;

  font-weight: 400;
  text-align: left;
  color: #717376;
  line-height: 44px;

  .foot-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.foot-btn-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: end;
}

.form-box {
  display: flex;
  flex-wrap: wrap;

  .form-flex {
    display: flex;
    align-items: center;
  }

  .value-item {
    height: 22px;
    font-size: 14px;
    color: var(--text-kyy-color-text-1, #1a2139);

    font-weight: 400;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .laber-item {
    width: 70px;
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: var(--text-kyy-color-text-3, #828da5);
  }
}

.foot-box {
  padding: 12px;
  margin-bottom: -16px;
  border: 1px solid #e3e6eb;
  border-top: none;
}

.red-time {
  height: 22px;
  font-size: 14px;

  font-weight: 400;
  text-align: left;
  color: #da2d19;
  line-height: 22px;
}

.table-thing-value {
  font-size: 14px;

  font-weight: 400;
  color: #13161b;
}

.flex-a-end {
  display: flex;
  align-items: center;
  justify-content: end;
}

.table-thing-laber {
  font-size: 14px;

  font-weight: 400;
  color: #717376;
}

.table-thing {
  display: flex;

  img {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }
}

:deep(.t-pagination__select) {
  margin-left: 24px;
}

.content-box {
  border: 1px solid #e3e6eb;
}

.lin {
  position: relative;
}

.lin::after {
  content: '';
  width: 2px;
  height: 14px;
  background: #2069e3;
  position: absolute;
  top: 4px;
  border-radius: 2px;
  left: 0;
}

.head-search-box {
  padding-top: 72px;
  height: 185px;
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 24px;
  padding-left: 24px;
}

.form-items {
  display: flex;
  align-items: center;
  padding-right: 32px;

  .labels {
    height: 22px;
    padding-right: 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: #13161b;
    line-height: 22px;
  }
}

.seach-head-box {
  display: flex;
  align-items: center;
  padding: 16px 0 24px 24px;
  justify-content: space-between;

  .flex-a {
    display: flex;
    align-items: center;
  }

  .gogrouporder {
    margin-right: 24px;
  }
}

.btn-box {
  font-size: 14px;
  color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff);

  font-weight: 400;
  cursor: pointer;
  line-height: 22px;
}

.f-icon {
  display: flex;
  width: 32px;
  height: 32px;
  cursor: pointer;
  min-height: 32px;
  max-height: 32px;
  padding: 6px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-left: 8px;
  border-radius: var(--radius-kyy-radius-button-s, 4px);
  border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
  background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
}

.af-icon {
  margin-left: 8px;
  height: 32px;
}

.form-boxxx {
  .fitem {
    margin-bottom: 24px;

    .title {
      color: var(--text-kyy-color-text-3, #828da5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .ctl {
      margin-top: 8px;
    }
  }
}

.refund-icon {
  color: #fff;
  font-size: 10px;
  background: #fc7c14;
  padding: 0px 4px;
  border-radius: 0px 0px 8px 0px;
  height: 20px;
  line-height: 20px;
  position: absolute;
  top: 0px;
  left: 0px;
}

:global(.order-more-menu-container-popup) {
  padding: 4px !important;
  display: flex;
  flex-direction: column;
}

.tui::after {
  content: '';
  position: absolute;
  top: 0px;
  left: 0px;
  width: 32px;
  height: 32px;
  background: url('@assets/img/icon_subscript.png') 100% center no-repeat;
  background-position: center;
  background-size: 100%;
}

.transaction-status {
  width: 58px;
  height: 24px;
  border-radius: 12px;
  font-size: 14px;
  text-align: center;
  line-height: 24px;
  color: var(--kyy_color_tag_text_success, #499d60);
  font-weight: 600;
}

.flex-a {
  display: flex;
  align-items: center;
}

.filter-res {
  display: flex;
  margin-bottom: 25px;
  margin-left: 24px;

  .tit {
    color: var(--text-kyy-color-text-2, #516082);

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    /* 157.143% */
  }

  .ov-time {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }

  .close2 {
    margin-left: 8px;
    width: 20px;
    height: 20px;
    font-size: 20px;
  }

  .te {
    color: var(--kyy-color-tag-text-black, #1a2139);
    cursor: pointer;
    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    border-radius: 4px;
    background: var(--kyy-color-tag-bg-gray, #eceff5);
    margin-right: 8px;
  }

  .stat {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }

  .kword {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }

  .icon {
    display: flex;
    margin-left: 4px;
    cursor: pointer;

    img {
      width: 14px;
      height: 14px;
      margin-top: 4px;
      margin-right: 4px;
    }
  }
}

.searchtotal {
  color: var(--text-kyy-color-text-2, #516082);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
  margin-bottom: 12px;
  padding-left: 24px;
}

.flex-al-space {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.refund-status {
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
  border-radius: 16px;
  text-align: center;
}

.settingGroup {
  display: flex;
  width: 100%;
  padding-bottom: 6px;
  border-bottom: 1px solid #e3e6eb;

  // margin: 15px 0;
  * {
    text-align: center;
  }

  .tabs {
    width: 72px;
    text-align: center;
    height: 22px;
    position: relative;
    color: #1a2139;
    cursor: pointer;
    margin-right: 32px;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
  }

  .tabs.active {
    color: #4d5eff;
    font-weight: 600;
  }

  .active::after {
    width: 16px;
    height: 3px;
    content: '';
    display: inline-block;
    background: #4d5eff;
    position: absolute;
    bottom: -6px;
    left: 50%;
    margin-left: -14px;
    border-radius: 1.5px;
  }
}

.operation-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  flex-direction: column;
}

.more-menu-container {
  position: relative;
  display: inline-block;
}

.more-btn:hover {
  background-color: #eaecff;

  .iconmoreicon {
    color: #707eff !important;
  }
}

.more-btn {
  cursor: pointer;
  border-radius: 4px;
  text-align: center;
  line-height: 28px;

  display: flex;
  align-items: center;
  justify-content: center;

  width: 28px;
  height: 28px;

  .iconmoreicon {
    color: #828da5;
    font-size: 20px;
  }
}

.more-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 100px;
  background-color: #fff;
  border: 1px solid #e3e6eb;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 4px;
}

.more-menu-container:hover .more-menu {
  display: block;
}

.menu-item {
  padding: 0px 16px;
  white-space: nowrap;
  cursor: pointer;
  color: #516082;
  width: 136px;
  height: 32px;
  font-size: 14px;
  transition: background-color 0.3s;
  border-radius: 4px;
  line-height: 32px;
}

.menu-item:hover {
  background-color: #f5f8fe;
  color: #4d5eff;
}
</style>
