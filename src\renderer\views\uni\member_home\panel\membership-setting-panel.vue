<template>
  <div class="outer">
    <div class="container">
      <!-- <div class="head-btn">
      <t-button
        theme="primary"
        style="width: 80px"
        variant="base"
        @click="onSave"
      >
        保存</t-button>
     </div> -->
      <div class="header">
        <h1 class="header-title">{{ $t('member.digital.r') }}</h1>
        <!-- <ul class="header-buttons">
        <li>
          <t-button theme="primary" variant="base" @click="onSave">
            保存</t-button>
        </li>
      </ul> -->
      </div>

      <div class="setting">
        <div class="setting-form">
          <!-- 入会会费设置 -->
          <!-- <div class="setting-form-item">

            <span class="title">{{ $t("member.membership.setting_fee") }}</span>
            <span class="value">
              <t-radio-group
                v-model="formData.pay_setting"
                name="city"
                :options="itemPayOptions"
                class="font"
                @change="onChangePay"
              />
            </span>
          </div> -->
          <div class="setting-form-item !flex-items-start">
            <!-- 入会申请设置 -->
            <span class="title pt-5"> {{ $t('member.digital.s') }}</span>
            <span class="value">
              <span class="team">
                <t-checkbox v-model="formData.personal_apply"
                  :disabled="formData.personal_apply && !formData.team_apply" @change="onSave">
                  <template #label>
                    <span class="font">
                      {{ $t('member.digital.t') }}
                    </span>
                  </template>
                </t-checkbox>
                <!-- :disabled="formData.personal_apply && !formData.team_apply" -->
                <t-button theme="default" variant="outline" style="height: 28px" @click="goFormDesignPage('person')">
                  <!-- 个人入会表单设计 -->
                  <!-- {{ $t("member.membership.person_form_design") }} -->
                  {{ $t('member.digital.u') }}
                </t-button>
              </span>
              <span class="team">
                <!-- 支持单位入会 -->
                <t-checkbox v-model="formData.team_apply" v-show="!loading"
                  :disabled="formData.team_apply && !formData.personal_apply" @change="onSave">
                  <template #label>
                    <span class="font">
                      {{ $t('member.digital.v') }}
                    </span>
                  </template>
                </t-checkbox>
                <!-- 单位入会表单设计 -->
                <t-button theme="default" variant="outline" style="height: 28px" @click="goFormDesignPage('unit')">
                  <!-- {{ $t("member.membership.unit_form_design") }} -->
                  {{ $t('member.digital.w') }}
                </t-button>
              </span>
              <span class="team">
                <t-checkbox v-show="!loading" v-model="formData.contact_add" @change="onSave">
                  <template #label>
                    <span class="font">{{ $t('member.digital.x') }}</span>
                    <span class="tdesc">（{{ $t('member.digital.y') }}）</span>
                  </template>
                </t-checkbox>
              </span>
            </span>
          </div>
          <div class="setting-form-item !flex-items-start" style="margin-bottom: 16px">
            <!-- 标签设置 -->
            <span class="title pt-5"> 自动审核</span>
            <span class="value">
              <span class="team pt-5" style="color:#828DA5 ;">
                <t-switch :customValue="[1, 0]" v-model="leabFormData.platform.enable"
                  @change="onChangePlatformTag"></t-switch>开启后，成员申请加入将无需审核表单信息自动审核通过
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="container" v-if="leabFormData">
      <div class="header">
        <h1 class="header-title">标签设置</h1>
      </div>
      <div class="setting">
        <div class="setting-form" style="gap: 0">
          <div class="setting-form-item !flex-items-start" style="margin-bottom: 16px">
            <!-- 标签设置 -->
            <span class="title pt-5"> 平台标签</span>
            <span class="value">
              <span class="team pt-5" style="color:#828DA5 ;">
                <t-switch :customValue="[1, 0]" v-model="leabFormData.platform.enable"
                  @change="onChangePlatformTag"></t-switch>开启后，平台成员可选择可选标签中设置的标签
              </span>
            </span>
          </div>
          <div class="setting-form-item !flex-items-start" style="margin-bottom: 24px">
            <img src="@/assets/digital/eg.png" style="width: 280px; height: 144px; margin-right: 12px" />
            <div style="display: flex; flex-direction: column; justify-content: space-between; height: 144px">
              <t-button theme="default" variant="outline" class="w600" style="font-weight: 600;width: fit-content"
                @click="openGrtag(2)">设置平台标签</t-button>
              <div style="color: #828da5; font-size: 14px; font-weight: 400">
                <div>1. 平台标签将在名录卡片和名录详情中显示</div>
                <div>2. 平台成员可从设置好的标签中选择符合自己的平台标签</div>
                <div>3. 例如平台标签为车型，则平台成员可选择：Model Y、Model X ..</div>
              </div>
            </div>
          </div>

          <div class="setting-form-item !flex-items-start" style="margin-bottom: 16px">
            <!-- 标签设置 -->
            <span class="title pt-5"> 个人标签</span>
            <span class="value">
              <span class="team pt-5" style="color:#828DA5 ;">
                <t-switch :customValue="[1, 0]" v-model="leabFormData.personal.enable"
                  @change="onChangeGeTag"></t-switch>开启后，平台成员可自定义符合自己的个人标签
              </span>
            </span>
          </div>
          <div class="setting-form-item !flex-items-start">
            <img src="@/assets/digital/greg.png" style="width: 280px; height: 144px; margin-right: 12px" />
            <div style="display: flex; flex-direction: column; justify-content: space-between; height: 144px">
              <t-button theme="default" class="w600" variant="outline" style="width: fit-content;font-weight: 600;"
                @click="openGrtag(1)">设置个人标签</t-button>
              <div style="color:  #828da5; font-size: 14px; font-weight: 400">
                <div>1. 个人标签将在名录卡片和名录详情中显示</div>
                <div>2. 平台成员可自定义符合自己的个人标签</div>
                <div>3. 例如个人标签为车牌号，则平台成员可输入：粤C00001</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="container">
      <div class="header">
        <h1 class="header-title">{{ $t("member.remind.title") }}</h1>
      </div>
      <div class="setting">
        <div class="setting-form">
          <div class="setting-form-item">

            <span class="vip">{{ $t("member.winter.vip_to_date_tip") }}</span>
            <span class="value" v-show="!loading">
              <t-switch
                :value="formData_two.is_remind === 1 ? true : false"
                @change="onSetRemind"
              />
            </span>
          </div>
          <div class="setting-form-item">

            <span class="title">{{ $t("member.remind.range") }}</span>
            <span class="value value1">
              {{ remindRangeTextCtd }}
            </span>

          </div>
          <div class="setting-form-item">

            <span class="title">
              {{ $t("member.remind.time") }}
            </span>
            <span class="value value1">

              <span class="countColor" v-for="(dayItem, dayIndex) in formData_two.days">到期前{{  dayItem }}天
                <template v-if="dayIndex + 1 !== formData_two.days.length">，</template>
              </span>
              提醒会员
              <t-link
                theme="primary"
                class="ml-8"
                hover="color"
                @click="onRemindTime"
              >{{ $t('member.edit') }}</t-link>
            </span>
          </div>
          <div class="setting-form-item">

            <span class="title">{{ $t("member.remind.way") }}</span>
            <span class="value value1">
              {{ $t("member.remind.on_site_text") }}
            </span>
          </div>
        </div>
      </div>
    </div> -->
    <div class="container" v-if="groupInfo && formData.im_group">
      <div class="header flexBetween">
        <!-- 平台群设置 -->
        <h1 class="header-title">{{ $t('member.rongeb.h') }}</h1>
        <t-link class="dissolution" v-show="groupInfo" hover="color" @click="onDissolutionGroup">解散该群</t-link>
      </div>
      <div class="setting">
        <div class="setting-form" v-if="!formData.im_group">
          <div class="setting-form-item">
            <t-button theme="primary" @click="onCreatePlatformGroup">{{ $t('member.rongeb.n') }}</t-button>
            <t-tooltip>
              <template #content>
                <div>
                  提示:<br />
                  {{ $t('member.rongeb.i') }}<br />
                  {{ $t('member.rongeb.j') }}<br />
                  {{ $t('member.rongeb.k') }}<br />
                  {{ $t('member.rongeb.l') }}
                </div>
              </template>
              <iconpark-icon name="iconhelp" style="font-size: 20px; color: #828da5; margin-left: 4px"></iconpark-icon>
            </t-tooltip>
          </div>
          <div class="setting-form-item">
            <t-checkbox v-model="formData.auto_create_group" @change="onSaveAutoGroup">
              <template #label>
                <span class="font"> 平台成员或平台管理员有2人以上时自动创建群聊 </span>
              </template>
            </t-checkbox>
          </div>
        </div>
        <div class="setting-form" v-else-if="groupInfo">
          <div class="setting-form-item">
            <span class="title"> 群名称 </span>
            <span class="value value1"> {{ groupInfo?.name }} </span>
          </div>
          <div class="setting-form-item">
            <span class="title"> 群主 </span>
            <span class="value value1">
              <span class="groupOwner">
                <kyyAvatar data-id="isclick" :image-url="groupInfo.owner_avatar" avatar-size="24px"
                  :user-name="groupInfo.owner_name" :shape="'circle'" />
                {{ groupInfo.owner_name }}
              </span>
              <t-link theme="primary" class="ml-8" hover="color" @click="onSelectPerson">选择群主</t-link>
            </span>
          </div>
          <div class="setting-form-item">
            <span class="title"> 群人数 </span>
            <span class="value value1"> {{ groupInfo?.count }} </span>
          </div>
        </div>
      </div>
    </div>

    <div class="container">
      <div class="header">
        <h1 class="header-title">{{ t('ebook.mset5') }}</h1>
      </div>
      <div class="setting">
        <div class="setting-form">
          <div class="setting-form-item !flex-items-start">
            <span class="title"> {{ t('ebook.mset6') }}</span>
            <span class="value" style="flex-direction: row; align-items: center">
              <t-switch v-model="formData.dynamics_auto" @change="switchChange"></t-switch>{{ t('ebook.mset7') }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="container">
      <div class="header">
        <h1 class="header-title">访客设置</h1>
      </div>
      <div class="setting">
        <div class="setting-form">
          <div class="setting-form-item !flex-items-start">
            <span class="title"> 允许访客加入</span>
            <span class="value" style="flex-direction: row; align-items: center">
              <t-switch v-model="formData.allow_visitor" @change="joinSwitchChange"></t-switch>开启后，用户可通过邀请链接申请成为访客
            </span>
          </div>
          <template v-if="formData.allow_visitor">
            <div class="setting-form-item !flex-items-start">
              <span class="title"> 允许平台成员邀请访客</span>
              <span class="value" style="flex-direction: row; align-items: center">
                <t-switch v-model="formData.allow_platform_visitor"
                  @change="approvalSwitchChange"></t-switch>开启后，正式平台成员可邀请访客加入
              </span>
            </div>
            <div class="setting-form-item !flex-items-start">
              <span class="title"> 访客审核</span>
              <span class="value" style="flex-direction: row; align-items: center">
                <t-switch v-model="formData.visitor_verify" @change="inviteSwitchChange"></t-switch>开启后，申请加入的访客均需要管理员审核
              </span>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div class="container">
        <div class="header">
          <h1 class="header-title">市场设置</h1>
        </div>
        <div class="setting">
          <div class="setting-form">
            <div class="setting-form-item !flex-items-start">
              <span class="title">广场号主页市场入口</span>
              <span class="value" style="flex-direction: row;align-items: center">
                <t-switch :value="!formData.square_market_disable" @change="onSetSquareSwitch"></t-switch>开启后，组织广场号主页将显示市场入口
              </span>
            </div>
          </div>
        </div>
      </div>
  </div>
  <!-- <div v-if="loading" v-lkloading="{ show: loading, opacity: true }" class="white-page"></div> -->
  <SettingRangeModal ref="settingRangeModalRef" @on-emits="onSettingRange" />
  <SettingTimeModal ref="settingTimeModalRef" @on-emits="onSettingTime" />
  <SelectGroupModal ref="selectMemberModalRef" :options="optionsMembers" :header="'选择群主'" :is-only="true"
    @sub-form="onListenMembers" />
  <tagDrawer ref="tagDrawerRef"></tagDrawer>
</template>

<script lang="tsx" setup>
  // import noData from "@renderer/components/approvalSatellite/approvalData/noData.vue";
  import { useI18n } from 'vue-i18n';
  import { reactive, ref, toRaw, watch, computed, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useUniStore } from '@renderer/views/uni/store/uni';
  import useRouterHelper from '@renderer/views/square/hooks/routerHelper';
  // import { SettingApplyInterface } from "@renderer/views/uni/member_home/panel/membership-setting-panel/interface";
  import { useMemberFormDesignStore } from '@renderer/views/uni/store/formDesign';
  import {
    getMemberSettingAxios,
    memberSettingApplyAxios,
    memberSettingRemindAxios,
    memberSettingGroupAxios,
    memberSettingGroupCreateAxios,
    delGroupGovAxios,
    memberSettingDynamicsAxios,
    settingVisitor,
    onSetDisableSquareMarketAxios,
  } from '@renderer/api/uni/api/businessApi';
  import { getResponseResult } from '@renderer/utils/myUtils';
  import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
  import SelectGroupModal from '@renderer/views/digital-platform/modal/select-group-modal.vue';
  import kyyAvatar from '@renderer/components/kyy-avatar/index.vue';
  const digitalRouter = useRouterHelper('digitalPlatformIndex');
  const tagDrawerRef = ref(null);
  import tagDrawer from '@renderer/views/digital-platform/components/tagDrawer.vue';

  // 通用
  import { SettingRemindInterface } from '@renderer/views/uni/member_home/panel/membership-setting-panel/interface';

  import SettingRangeModal from '@renderer/views/uni/member_home/panel/membership-setting-panel/modal/setting-range-modal.vue';
  import SettingTimeModal from '@renderer/views/uni/member_home/panel/membership-setting-panel/modal/setting-time-modal.vue';
  import { useDigitalPlatformStore } from '@renderer/views/digital-platform/store/digital-platform-store';
  import { platform } from '@renderer/views/digital-platform/utils/constant';
  import { getUniTeamID } from '@renderer/views/uni/utils/auth';
  import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
  import { ClientSide } from '@renderer/types/enumer';
  import {
    commonDigitalInfoAxios,
    getDigitalMember,
    setDigitalOwner,
  } from '@renderer/api/digital-platform/api/businessApi';
  import { onSureCreatePlatformGroup } from '@renderer/views/digital-platform/utils/auth';
  import { GetMemberSettingSetTagApi } from '@renderer/api/politics/api/businessApi';
  import LynkerSDK from "@renderer/_jssdk";
  const { ipcRenderer } = LynkerSDK;

  const digitalPlatformStore = useDigitalPlatformStore();
  const route = useRoute();
  const optionsMembers = ref([]);

  const props = defineProps({
    platform: {
      type: String,
      default: '',
    },
  });
  // 平台类型 目前只有digital-platform
  const platformCpt = computed(() => {
    return props.platform || route.query?.platform;
  });

  const activeAccount = computed(() => {
    if (platformCpt.value === platform.digitalPlatform) {
      return digitalPlatformStore.activeAccount;
    } else if (platformCpt.value === platform.digitalWorkbench) {
      return route.query;
    } else {
      return store.activeAccount;
    }
  });
  const openGrtag = (val) => {
    tagDrawerRef.value.openWin(val, currentTeamId.value);
  };

  const currentTeamId = computed(() => {
    if (platformCpt.value === platform.digitalPlatform) {
      return digitalPlatformStore.activeAccount?.teamId;
    } else if (platformCpt.value === platform.digitalWorkbench) {
      return route.query?.teamId || 0;
    } else {
      return getUniTeamID();
    }
  });
  const selectMemberModalRef = ref(null);
  // 添加群主
  const onListenMembers = async (arr) => {
    console.log(arr);
    if (arr && arr.length < 1) return;
    // const formData = reactive({
    //   id: 0, // 需要转移的管理员
    //   staffName: "",
    //   staffAvatar: "",
    //   idStaff: 0,
    //   action: 1
    // });
    const option = optionsMembers.value.find((v) => v.idStaff === arr[0]);
    if (option) {
      // formData.idStaff = option.idStaff;
      // formData.staffName = option.name;
      // formData.staffAvatar = option.avatar;

      // 设置群主接口
      onSetDigitalGroup(option.idStaff).then(() => {
        MessagePlugin.success('设置成功');
        onGetGroupInfo(formData.im_group);
      });
    }
    selectMemberModalRef.value.onClose();
  };
  const onSetDigitalGroup = (owner) => {
    let result = null;
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
      try {
        result = await setDigitalOwner({ im_group: formData?.im_group, owner }, currentTeamId.value);
        result = getResponseResult(result);
        if (!result) {
          reject();
          return;
        }

        resolve('success');
      } catch (error) {
        reject();
        const errMsg = error instanceof Error ? error.message : error;
        MessagePlugin.error(errMsg);
      }
    });
  };

  const onDelGroupGovAxios = () => {
    let result = null;
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
      try {
        result = await delGroupGovAxios({ im_group: formData.im_group }, currentTeamId.value);
        result = getResponseResult(result);
        if (!result) {
          reject();
          return;
        }

        resolve('success');
      } catch (error) {
        reject();
        const errMsg = error instanceof Error ? error.message : error;
        MessagePlugin.error(errMsg);
      }
    });
  };

  // 解散该群
  const onDissolutionGroup = () => {
    const confirmPlugin = DialogPlugin({
      header: '解散该群',
      theme: 'info',
      body: '解散该群，群聊中的聊天记录，图片和文件将同时被删除',
      closeBtn: null,
      closeOnOverlayClick: false,
      cancelBtn: '取消',
      confirmBtn: '解散',
      className: 'delmode',
      onConfirm: async () => {
        onDelGroupGovAxios()
          .then((res) => {
            MessagePlugin.success('解散成功');
            onInitData();
          })
          .catch((err) => {
            MessagePlugin.error(err || '解散失败');
          });
        confirmPlugin.hide();
      },
      onClose: () => {
        confirmPlugin.hide();
      },
    });
  };

  // 创建平台群
  const onCreatePlatformGroup = async () => {
    if (formData.can_join_num < 2) {
      MessagePlugin.error('平台成员或平台管理员有2人及以上才能创建群聊');
      return;
    } else {
      const bodyRender = () => (
        <div>
          <p>{'1、当平台成员和平台管理员中的人数有2人后才会创建此群聊。'}</p>
          <p>{'2、新加入的平台成员或管理员会自动加入到群聊。'}</p>
          <p>{'3、如果成员或管理员被删除了，群聊中也会移除此人。'}</p>
          <p>{'4、创建群聊时会把平台超级管理员作为群主。群主可在群管理中转移群主身份。'}</p>
        </div>
      );
      const confirm = await onSureCreatePlatformGroup(bodyRender);
      if (!confirm) return;
    }

    let result = null;
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
      try {
        result = await memberSettingGroupCreateAxios({}, currentTeamId.value);
        result = getResponseResult(result);
        if (!result) {
          reject();
          return;
        }
        // onGetGroupInfo(result.data)
        onGetMemberSetting();
        MessagePlugin.success('创建成功');
        resolve('success');
      } catch (error) {
        reject();
        const errMsg = error instanceof Error ? error.message : error;
        MessagePlugin.error(errMsg);
      }
    });
  };

  const { t } = useI18n();
  const router = useRouter();
  const store = useUniStore();
  const formDesignStore = useMemberFormDesignStore();
  // const { menuList, routeList, roleFilter } = useRouterHelper("associationIndex");

  const itemPayOptions = [
    {
      value: 1,
      label: t('member.membership.setting_fee_after'),
    },
    {
      value: 2,
      label: t('member.membership.setting_fee_no'),
    },
  ];
  const formData: any = reactive({
    pay_setting: 1,
    personal_apply: 1,
    personal_form: [],
    team_apply: 0,
    team_form: [],
    contact_add: true, // 邀请权限
    can_join_num: 0,
    allow_visitor: true,
    allow_platform_visitor: false,
    visitor_verify: true,
    dynamics_auto: false,
    square_market_disable: 1, // 是否禁用广场号主页市场入口
  });
  let leabFormData = ref({
    personal: {
      enable: 0,
      setting_id: '',
    },
    platform: {
      enable: 0,
      setting_id: '',
    },
  });
  const changTagFlagFn = (enable, settingId, flag?) => {
    GetMemberSettingSetTagApi(
      'uni',
      {
        enable,
      },
      settingId,
      currentTeamId.value,
    )
      .then((res) => {
        MessagePlugin.success(t('member.save') + t('member.success'));
        onGetMemberSetting(true);
      })
      .catch((error) => {
        const errMsg = error instanceof Error ? error.message : error;
        MessagePlugin.error(errMsg);
        onGetMemberSetting();
      });
  };

  const onChangeGeTag = (e) => {
    if (!e) {
      const myDialog = DialogPlugin({
        header: '确定关闭个人标签?',
        theme: 'info',
        body: '关闭后，平台成员已设置的个人标签将不再显示',
        className: 'dialog-classp32',
        confirmBtn: t('ebook.mset3'),
        cancelBtn: t('ebook.mset4'),
        closeOnOverlayClick: false,
        onConfirm: () => {
          myDialog.hide();
          changTagFlagFn(0, leabFormData.value.personal?.setting_id, '个人');
        },
        onCancel: () => {
          leabFormData.value.personal.enable = 1;
          myDialog.hide();
        },
      });
    } else {
      changTagFlagFn(1, leabFormData.value.personal?.setting_id, '个人');
    }
  };
  const onChangePlatformTag = (e) => {
    if (!e) {
      const myDialog = DialogPlugin({
        header: '确定关闭平台标签?',
        theme: 'info',
        body: '关闭后，平台成员已设置的平台标签将不再显示',
        className: 'dialog-classp32',
        confirmBtn: t('ebook.mset3'),
        cancelBtn: t('ebook.mset4'),
        closeOnOverlayClick: false,
        onConfirm: () => {
          myDialog.hide();
          changTagFlagFn(0, leabFormData.value.platform?.setting_id);
        },
        onCancel: () => {
          leabFormData.value.platform.enable = 1;
          myDialog.hide();
        },
      });
    } else {
      changTagFlagFn(1, leabFormData.value.platform?.setting_id);
    }
  };
  const onGetMemberSetting = (noloading?) => {
    let result = null;
    if (!noloading) {
      loading.value = true;
    }
    return new Promise(async (resolve, reject) => {
      try {
        result = await getMemberSettingAxios({}, currentTeamId.value);
        result = getResponseResult(result);
        loading.value = false;
        if (!result) {
          reject();
          return;
        }
        leabFormData.value = result.data?.label_setting ? result.data?.label_setting : null;

        formData.pay_setting = result.data?.pay_setting;
        formData.personal_apply = result.data?.personal_apply;
        formData.personal_form = result.data?.personal_form;
        formData.team_apply = result.data?.team_apply;
        formData.team_form = result.data?.team_form;
        formData.contact_add = !!result.data?.contact_add;
        formData.allow_visitor = !!result.data?.allow_visitor;
        formData.allow_platform_visitor = !!result.data?.allow_platform_visitor;
        formData.visitor_verify = !!result.data?.visitor_verify;
        formData.auto_create_group = !!result.data?.auto_create_group;
        formData.dynamics_auto = !!result.data?.dynamics_auto;
        formData.im_group = result.data?.im_group;
        groupInfo.value = null;
        if (formData.im_group) {
          onGetGroupInfo(formData.im_group);
        }
        formData.can_join_num = result.data?.can_join_num;
        formData.square_market_disable = result.data?.square_market_disable;

        formDesignStore.setPersonForm(formData.personal_form);
        formDesignStore.setTeamForm(formData.team_form);
        resolve(result);
      } catch (error) {
        const errMsg = error instanceof Error ? error.message : error;
        MessagePlugin.error(errMsg);
        loading.value = false;

        reject();
      }
    });
  };

  const onGetMemberSetting_two = () => {
    let result = null;
    return new Promise(async (resolve, reject) => {
      try {
        result = await getMemberSettingAxios();
        result = getResponseResult(result);

        if (!result) {
          reject();
          return;
        }
        // formData.pay_setting = result.data?.pay_setting;
        // formData.personal_apply = result.data?.personal_apply;
        // formData.personal_form = result.data?.personal_form;
        // formData.team_apply = result.data?.team_apply;
        // formData.team_form = result.data?.team_form;
        console.log(result);
        const data = result.data;
        formData_two.days = data.days;
        formData_two.is_remind = data.is_remind;
        formData_two.members = data.members;
        formData_two.type = data.type;
        resolve(data);
      } catch (error) {
        reject();
        const errMsg = error instanceof Error ? error.message : error;
        MessagePlugin.error(errMsg);
      }
    });
  };

  const groupInfo = ref(null);
  const onGetGroupInfo = async (imGroup) => {
    //
    let result = null;
    try {
      result = await commonDigitalInfoAxios({ im_group: imGroup }, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) return;
      // MessagePlugin.success(t("member.save") + t("member.success"));
      groupInfo.value = result?.data;
    } catch (error) {
      groupInfo.value = null;
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      console.log(errMsg);
    }
  };

  // onActivated(() => {
  //   if (store.activeAccount) {
  //     onGetProjectList();
  //   }
  // });
  const loading = ref(false);
  // onGetMemberSetting();
  // onGetMemberSetting_two();
  // onMounted(async () => {
  //   loading.value = true;
  //   console.log(123);
  //   try {
  //     const res = await Promise.all([onGetMemberSetting(), onGetMemberSetting_two()])
  //     loading.value = false;
  //   } catch (error) {
  //     loading.value = false;
  //   }
  //   // await onGetMemberSetting();
  //   // await onGetMemberSetting_two();
  //   console.log(456);
  //   // setTimeout(() => {
  //   //   loading.value = false;
  //   // }, 500);
  // });

  // watch(
  //   () => store.activeAccount,
  //   async (val) => {

  //     if (val) {
  //       // updateAllCount();
  //     await onGetMemberSetting();
  //     await onGetMemberSetting_two();
  //     }
  //   },
  //   {
  //     deep: true,
  //     // immediate: true
  //   }
  // );

  const getAppMemberList = () => {
    let result = null;
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
      try {
        result = await getDigitalMember({ im_group: formData?.im_group }, currentTeamId.value);
        result = getResponseResult(result);
        if (!result) {
          reject();
          return;
        }
        optionsMembers.value = result.data?.map((v) => {
          v.idStaff = v.owner;
          return v;
        });
        resolve('success');
      } catch (error) {
        reject();
        const errMsg = error instanceof Error ? error.message : error;
        MessagePlugin.error(errMsg);
      }
    });
  };
  const onSelectPerson = () => {
    // teamId.value = getProjectTeamID();

    // organizeSelectModalRef.value.onOpen();
    getAppMemberList().then(() => {
      setTimeout(() => {
        selectMemberModalRef.value.onOpen();

        if (groupInfo.value?.owner) {
          selectMemberModalRef.value.onSetSelectedValue([groupInfo.value.owner]);
        }
      });
    });
  };

  onMountedOrActivated(() => {
    onInitData();
  });

  const onInitData = () => {
    if (currentTeamId.value) {
      onGetMemberSetting();
      // onGetMemberSetting_two();
    }
  };

  const goFormDesignPage = (type) => {
    // router.push({
    //   path: "/associationIndex/member_manage",
    //   query: {
    //     // projectId: props.projectId
    //   }
    // });
    const searchMenu = digitalRouter.routeList.find((v) => v.name === 'digital_platform_uni_manage');
    // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
    searchMenu.query = { type };
    let path = '/workBenchIndex/uni_manage';
    let name = 'bench_uni_manage';

    if (type === 'person') {
      searchMenu.title = '个人表单设计';
    } else {
      searchMenu.title = '组织表单设计';
      path = '/workBenchIndex/uni_manage_unit';
      name = 'bench_uni_manage_unit';
    }

    if (platformCpt.value === platform.digitalWorkbench) {
      const query = { ...route.query, type };
      ipcRenderer.invoke('set-work-bench-tab-item', {
        name,
        path,
        path_uuid: 'uni',
        title: searchMenu.title,
        type: ClientSide.UNI,
        // addNew: true,
        query,
      });
      console.log({ ...route.query, type });
      // return ;
      router.push({ path, query });
    } else {
      router.push({ path: searchMenu.fullPath, query: { type } });
      store.addTab(toRaw(searchMenu), true);
    }
  };

  // 保存表单
  const onSave = async () => {
    const params = {
      ...toRaw(formData),
      // personal_form: toRaw(formDesignStore.personal_form),
      // team_form: toRaw(formDesignStore.team_form)
    };
    params.contact_add = Number(params.contact_add);
    params.personal_apply = Number(params.personal_apply);
    params.team_apply = Number(params.team_apply);

    delete params.personal_form;
    delete params.team_form;

    console.log(params);

    let result = null;
    try {
      result = await memberSettingApplyAxios(params, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) return;
      MessagePlugin.success(t('member.save') + t('member.success'));
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  };

  const onChangePay = (checkedValues) => {
    console.log(checkedValues);
    // console.log('checkedValues:', value.value, checkedValues);
    onSave();
  };

  const onSaveAutoGroup = async (e) => {
    console.log(e);
    // memberSettingGroupAxios
    let result = null;
    try {
      result = await memberSettingGroupAxios({ auto_create_group: Number(e) }, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) return;
      MessagePlugin.success(t('member.save') + t('member.success'));
      onInitData();
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  };

  // 通用
  let formData_two: SettingRemindInterface = reactive({
    days: [], // 提前提醒天数

    is_remind: 0,
    members: [1, 2], // [integer]部门提醒人员集合
    type: 1, // 提醒类型 1：全部，2：部门人员
  });

  const remindRangeTextCtd = computed(() => {
    let text = '';
    if (formData_two.type === 1) {
      // 全部
      text = t('member.winter.all_members');
    } else if (formData_two.type === 2) {
      // formData.
      text = t('member.winter.wait_set');
    }
    return text;
  });

  // watch(
  //   () => store.activeAccount,
  //   (val) => {
  //     if (val) {
  //       onGetMemberSetting();
  //       onGetMemberSetting_two();
  //     }
  //   },
  //   {
  //     deep: true
  //   }
  // );

  const onSetRemind = (val) => {
    formData_two.is_remind = val ? 1 : 2;
    onSave_two();
  };

  // onActivated(() => {
  //   if (store.activeAccount) {
  //     onGetProjectList();
  //   }
  // });

  // onMounted(() => {
  // });
  // onGetMemberSetting();

  const onSettingRange = (range) => {
    formData_two.type = range.type;
    formData_two.is_remind = range.is_remind;
    // onGetMemberSetting();
    onSave();
  };

  const onSettingTime = (time) => {
    console.log(time);
    formData_two.days = time.map((v) => v.day);
    // onGetMemberSetting();
    onSave_two();
  };

  const settingRangeModalRef = ref(null);
  const onReminderScope = () => {
    settingRangeModalRef.value.onOpen({
      type: formData_two.type,
      members: formData_two.members,
    });
  };

  const settingTimeModalRef = ref(null);
  const onRemindTime = () => {
    settingTimeModalRef.value.onOpen(formData_two.days);
  };

  const goFormDesignPages_two = (type) => {
    // router.push({
    //   path: "/associationIndex/member_manage",
    //   query: {
    //     // projectId: props.projectId
    //   }
    // });
    const searchMenu = digitalRouter.routeList.find((v) => v.name === 'member_manage');
    // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
    router.push({ path: searchMenu.fullPath, query: { type } });
    store.addTab(toRaw(searchMenu));
  };

  // 保存表单
  const onSave_two = async () => {
    const params = {
      ...toRaw(formData_two),
    };

    console.log(params);

    let result = null;
    try {
      result = await memberSettingRemindAxios(params);
      result = getResponseResult(result);
      if (!result) return;
      MessagePlugin.success(t('member.save') + t('member.success'));
      onGetMemberSetting_two();
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  };

  const rSettingApply = async (val) => {
    let result = null;
    try {
      result = await memberSettingDynamicsAxios({ dynamics_auto: val }, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) return;
      MessagePlugin.success(`${val ? '开启' : '关闭'}成功`);
      onGetMemberSetting();
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
    }
  };

  const rSettingSquare = async (val) => {
    let result = null;
    try {
      result = await onSetDisableSquareMarketAxios({ square_market_disable: val }, currentTeamId.value)
      result = getResponseResult(result);
      if (!result) return;
      MessagePlugin.success(`${!val ? '开启' : '关闭'}成功`);
      onGetMemberSetting();
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
    }
  }

  const onSetSquareSwitch = (e) => {
    console.log(formData.square_market_disable);
    if (!e) {
      const myDialog = DialogPlugin({
        header: '确定关闭【广场号主页市场入口】？',
        theme: "info",
        body: '关闭后，组织广场号主页将不显示市场入口',
        className: "dialog-classp32",
        confirmBtn: t("ebook.mset3"),
        cancelBtn: t("ebook.mset4"),
        closeOnOverlayClick: false,
        onConfirm: () => {
          formData.square_market_disable = 1;
          rSettingSquare(1)
          myDialog.hide();
        },
        onCancel: () => {
          // formData.square_market_disable = 1;
          myDialog.hide();
        },
      });
    } else {
      formData.square_market_disable = 0;
      rSettingSquare(0)

    }
  }

  const switchChange = (e) => {
    console.log('e', e);
    if (!e) {
      const myDialog = DialogPlugin({
        header: t('ebook.mset1'),
        theme: 'info',
        body: t('ebook.mset2'),
        className: 'dialog-classp32',
        confirmBtn: t('ebook.mset3'),
        cancelBtn: t('ebook.mset4'),
        closeOnOverlayClick: false,
        onConfirm: () => {
          rSettingApply(0);
          myDialog.hide();
        },
        onCancel: () => {
          formData.dynamics_auto = true;
          myDialog.hide();
        },
      });
    } else {
      rSettingApply(1);
    }
  };
  const rSettingVisitor = async (data, val) => {
    let result = null;
    try {
      result = await settingVisitor(data, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) return;
      MessagePlugin.success(`${val ? '开启' : '关闭'}成功`);
      onGetMemberSetting(true);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  };

  const joinSwitchChange = (e) => {
    const val = e ? 1 : 0;
    rSettingVisitor({ allow_visitor: val }, val);
  };
  const approvalSwitchChange = (e) => {
    const val = e ? 1 : 0;
    rSettingVisitor({ allow_platform_visitor: val }, val);
  };
  const inviteSwitchChange = (e) => {
    const val = e ? 1 : 0;
    rSettingVisitor({ visitor_verify: val }, val);
  };
</script>

<style lang="less" scoped>
  // @import "./public.less";
  .groupOwner {
    border-radius: 4px;
    background: var(--kyy_color_tag_bg_gray, #eceff5);
    padding: 2px 8px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .outer {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .vip {
    color: var(--text-kyy-color-text-1, #1a2139);

    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    /* 157.143% */
  }

  .container {
    border: 1px solid var(--divider-kyy-color-divider-deep, #d5dbe4);
  }

  .flexBetween {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header {
    padding: 12px;
    background: var(--kyy_color_table_hrading_bg, #e2e6f5);

    &-title {
      color: var(--kyy_color_table_hrading_text, #516082);
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }

    .dissolution {
      color: var(--error-kyy_color_error_default, #d54941);

      /* kyy_fontSize_2/regular */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  :deep(.t-button__text) {
    font-weight: 600 !important;
  }

  .tdesc {
    color: var(--text-kyy-color-text-3, #828da5);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .setting {
    background-color: @kyy_color_bg_light;
    padding: 16px;

    // height: inherit;
    &-form {
      display: flex;
      flex-direction: column;
      gap: 20px;

      &-item {
        display: flex;
        align-items: center;

        .title {
          color: var(--text-kyy-color-text-1, #1a2139);

          /* kyy_fontSize_2/bold */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px;
          /* 157.143% */
        }

        .value {
          display: flex;
          flex-direction: column;
          margin-left: 24px;
          gap: 16px;
          color: rgb(130, 141, 165);

          .team {
            display: flex;
            align-items: center;
            gap: 16px;

            :deep(.t-checkbox__label) {
              color: var(--checkbox-kyy_color_checkbox_text_default, #1a2139);
            }
          }
        }

        .value1 {
          display: flex;
          flex-direction: row;
          gap: 1px;
          color: var(--text-kyy-color-text-1, #1a2139);

          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
        }
      }
    }
  }

  .head-btn {
    margin-bottom: 24px;
  }

  :deep(.t-switch) {
    width: 44px;
    height: 24px;
  }

  .white-page {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    right: 0;
    background: #fff;
  }

  .font {
    color: rgb(130, 141, 165);

    // color: var(--checkbox-kyy_color_checkbox_text_active, red);

    /* kyy_fontSize_2/regular */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */

    :deep(.t-radio__label) {
      // color: var(--checkbox-kyy_color_checkbox_text_active, red);
      color: var(--checkbox-kyy_color_checkbox_text_active, #1a2139);
    }
  }

  :deep(.t-switch__handle) {
    width: 20px !important;
    height: 20px !important;
    top: 1.5px !important;
  }
</style>

