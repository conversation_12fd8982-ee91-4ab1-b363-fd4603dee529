<template>
  <div class="h-full flex" :class="isScene ? 'scene-wrapper' : 'bg-[#F5F8FE]'">
    <activity-create-step-bar v-model:value="currentStep" :class="{ 'rounded-8 !border-r-0': isScene }" />

    <div :class="isScene ? '' : 'flex-1'">
      <div class="create-form-wrapper" :class="{ 'w-1004 !pt-0 !pr-0': isScene }">
        <div class="create-form-content">
          <div class="create-form-content-scroll">
            <div v-if="isScene" class="pt-16 px-24">
              <div class="px-24 py-8 bg-[#EAECFF] rounded-8 flex gap-8">
                <span class="text-[#1A2139]">来自{{ sceneConversationType === '1' ? '单聊' : '群聊' }}：{{ sceneConversationName }}</span>
                <span class="text-[#4D5EFF] cursor-pointer" @click="openSceneConversation">点击查看</span>
              </div>
            </div>

            <component
              :is="stepComponent.value"
              v-for="stepComponent in stepComponents"
              v-show="currentStep === stepComponent.key"
              :key="stepComponent.key"
              :ref="(el) => setStepRefs(el, stepComponent.key)"
              :class="{ 'px-24 py-16': currentStep !== 'memberManager' }"
              @check-start-time="checkStartTime"
            />
          </div>
        </div>
      </div>

      <div class="operate-box">
        <t-button
          class="publish-button font-bold"
          theme="default"
          :loading="saveLoading"
          :disabled="(currentStep === 'process' && operateDisabled) || saveLoading || publishLoading"
          @click="saveDraft"
        >
          {{ t('activity.activity.saveDraft') }}
        </t-button>
        <t-button
          class="font-bold"
          theme="default"
          :disabled="currentStep === 'process' && operateDisabled"
          @click="preview"
        >
          {{ t('activity.activity.preview') }}
        </t-button>
        <t-button
          class="publish-button"
          theme="primary"
          :loading="publishLoading"
          :disabled="(currentStep === 'process' && operateDisabled) || saveLoading || publishLoading"
          @click="publish"
        >
          {{ t('activity.activity.publish') }}
        </t-button>
      </div>
    </div>

    <activity-publish-success-dialog
      ref="activityPublishSuccessDialogRef"
      :attach="isScene ? '.create-form-wrapper' : 'body'"
      :success-id="publishSuccessId"
      @close="closeTab"
      @back-list="onBackList"
      @go-detail="onGoDetail"
    />
  </div>
</template>

<script setup lang="tsx">
import {
  ref,
  onMounted,
  reactive,
  provide,
  shallowRef,
  computed,
  onActivated,
  onDeactivated,
  onBeforeUnmount,
} from 'vue';
import { useI18n } from 'vue-i18n';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import { useRoute, useRouter } from 'vue-router';
import _ from 'lodash';
import moment from 'moment';
import LynkerSDK from '@renderer/_jssdk';
import ActivityCreateStepBar from './components/ActivityCreateStepBar.vue';
import ActivityBasicInfo from './components/ActivityBasicInfo.vue';
import ActivityParticulars from './components/ActivityParticulars.vue';
import ActivityProcess from './components/ActivityProcess.vue';
import ActivityMemberManager from './components/ActivityMemberManager.vue';
import ActivityAdvanceSetting from './components/ActivityAdvanceSetting.vue';
import ActivityPublishSuccessDialog from './components/ActivityPublishSuccessDialog.vue';
import { useActivityStore } from '@/views/activity/store';
import { activityRelease, activitySaveDraft, activityUpdate, getActivityDetail } from '@/api/activity';
import { cardIdType } from '@/views/identitycard/data';
import { getOpenid, setAccountAuthRouters } from '@/utils/auth';
import { initialRegisterForms } from '@/views/activity/utils';
import emitter from '@/utils/MittBus';
import { openChat } from '@/utils/share';

const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();

const emit = defineEmits(['setActiveIndexAndName', 'settabItem', 'setActiveIndex', 'deltabItem']);

const route = useRoute();
const router = useRouter();

const activityStore = useActivityStore();

// 存储所有步骤组件的引用
const stepRefs = reactive({
  basicRef: null,
  particularsRef: null,
  memberManagerRef: null,
  processRef: null,
  advanceSettingRef: null,
});

const activityPublishSuccessDialogRef = ref(null);

// 活动配置向导的步骤组件列表
const stepComponents = shallowRef([
  {
    key: 'basic',
    value: ActivityBasicInfo,
  },
  {
    key: 'particulars',
    value: ActivityParticulars,
  },
  {
    key: 'memberManager',
    value: ActivityMemberManager,
  },
  {
    key: 'process',
    value: ActivityProcess,
  },
  {
    key: 'advanceSetting',
    value: ActivityAdvanceSetting,
  },
]);

// 当前页面是否激活状态（当前路由是否定位到了此页面）
const isActive = computed(() => {
  if (isDraft.value) {
    return route.path === `/activity/activityCreate/${activityFormData.id}`;
  }
  return route.path === '/activity/activityCreate';
});

// 当前是否是新建专业版活动
const isProfessional = route.query?.mold === '2';

// 当前活动是否是草稿，是否有活动id来判断
const isDraft = computed(() => !!activityFormData.id);

// 当前活动预览页的路径（非场景活动情况）
const previewPath = computed(() => {
  const path = isProfessional ? 'activityProfessionalPreview' : 'activityPreview';
  return isDraft.value ? `/activity/activityPreview/${activityFormData.id}` : `/activity/${path}`;
});

// 当前步骤
const currentStep = ref('basic');

// 存草稿按钮的loading状态
const saveLoading = ref(false);
// 发布按钮loading状态
const publishLoading = ref(false);

// 操作栏禁用状态（仅在流程管理中生效）
const operateDisabled = ref(false);

// 当前选中的活动归属组织
const selectedTeam = computed(
  () => activityStore.groupList.find((group) => group.teamId === activityFormData.basic.teamId) || {},
);

// 当前活动归属是否是个人身份
const isPersonal = computed(() => !selectedTeam.value.uuid || cardIdType(selectedTeam.value.uuid) === 'personal');

// 是否是场景新建活动（场景新建活动为layoutActivity独立窗口打开，独立窗口时会应用一套特别的样式）
const isScene = route.path.indexOf('layoutActivity') !== -1;
// 场景新建活动会话类型 1单聊 3群聊
const sceneConversationType = ref(route.query?.conversationType);
// 场景新建活动会话名称
const sceneConversationName = ref(route.query?.targetName);
// 场景新建活动会话id
const sceneConversationId = ref(route.query?.targetId);

// 活动表单数据
const activityFormData = reactive({
  // 活动主键，草稿箱状态的活动才有此主键
  id: route.params?.id || null,
  // 基础信息
  basic: {
    // 活动创建人
    creator: null,
    // 活动归属（草稿箱列表进入或场景活动独立窗口打开时会在路由参数中默认带入）
    teamId: route.query?.teamId || null,
    // 活动归属下的身份卡
    cardId: '',
    // 活动主题
    subject: null,
    // 活动类型
    categoryId: null,
    // 主题图片
    assetUrl: null,
    // 活动时间
    duration: {
      startTime: null,
      endTime: null,
    },
    // 活动地点
    location: {
      title: null,
    },
    // 活动主办方
    sponsor: [],
    // 参与人员范围 Publish-公开；Internal-组织内部/我的好友
    actorScope: 'Publish',
    // 活动创建模型类型：0-标准版，1-极速版 2-专业版
    mold: route.query?.mold === '2' ? 2 : 0,
    // 排座类型（专业版） 0-排/座(默认)；1-桌/号
    seatType: 0,
  },
  // 详情设置
  particulars: {
    // 活动详情
    content: null,
    // 活动附件
    files: {
      files: [],
    },
  },
  // 成员管理
  members: {
    // 活动参与人
    actors: [],
    // 活动嘉宾
    guests: [],
    // 工作人员
    staffs: [],
    // 协作人（专业版）
    collaborators: [],
    // 拟定人员（专业版）
    proposers: [],
  },
  // 活动流程
  process: {
    // 活动流程项列表
    processItems: [],
  },
  // 高级设置
  advanced: {
    // 是否开启报名
    openRegist: false,
    // 报名时间
    registerTime: {
      startTime: null,
      endTime: null,
    },
    // 报名人数
    quota: null,
    // 开启报名审核
    registApprove: false,
    // 报名表单
    registerForms: _.cloneDeep(initialRegisterForms),
    // 是否开启报名费
    registFeeEnable: false,
    // 报名费金额
    registFee: {
      // 货币单位，这里固定用cny
      currencyCode: 'CNY',
      // 金额
      value: null,
    },
    // 发布渠道
    publishChannels: [],
    // 发布到数字平台
    platforms: [],
    // 是否开启评论
    enableComment: false,
    // 活动现场图
    sceneDrawing: null,
    // 活动提醒
    reminders: [],
  },
  // 创建场景活动的chat信息
  chatId: null,
  // 专业版补充字段
  majorSuppliment: {
    // 权限菜单
    menus: [],
    // 协作人类型
    collaboratorTypes: [],
    // 活动标签
    tags: [],
  },
});

// 注入是否是新建专业版活动
provide('isProfessional', isProfessional);
// 注入新建模式
provide('mold', activityFormData.basic.mold);
// 注入活动表单数据
provide('activityFormData', activityFormData);
// 注入活动归属组织信息计算值
provide('selectedTeam', selectedTeam);
// 注入活动归属是否是个人身份计算值
provide('isPersonal', isPersonal);
// 注入活动是否是场景活动
provide('isScene', isScene);
// 注入非详情标识
provide('isInDetail', false);
// 注入可编辑标识
provide('isAllowEdit', true);
// 注入是否是活动详情管理
provide('isManage', false);
// 注入空的活动详情值，因为这里是创建，如果在创建时使用了activityDetailData会导致报错，便于排查
provide('activityDetailData', null);
// 注入空的权限值，原因同活动详情
provide('myPermission', { value: {} });

// 提交的表单数据
const submitFormData = computed(() => ({
  ...activityFormData,
  basic: {
    ...activityFormData.basic,
    teamId: isPersonal.value ? '' : activityFormData.basic.teamId,
    // 过滤掉空字符串占位的主办单位
    sponsor: activityFormData.basic.sponsor.filter((sponsor) => !_.isEmpty(sponsor.name)),
  },
  advanced: {
    ...activityFormData.advanced,
    // 报名表单，开启报名则正常提交，否则传空
    registerForms: activityFormData.advanced.openRegist ? activityFormData.advanced.registerForms : [],
    // 报名费，开启报名费则将金额转为字符串，否则整个对象传空
    registFee: activityFormData.advanced.registFeeEnable ? {
      ...activityFormData.advanced.registFee,
      value: activityFormData.advanced.registFee.value.toString(),
    } : null,
  },
}));

// 原始表单数据JSON，用于对比是否修改
const originalFormData = ref({});

// 发布成功后的响应活动id
const publishSuccessId = ref(null);

// 设置步骤组件的引用
const setStepRefs = (el, key) => {
  if (el) {
    stepRefs[`${key}Ref`] = el;
  }
};

// 存草稿
const saveDraft = async () => {
  saveLoading.value = true;

  // 更新草稿使用更新方法，新的草稿使用保存方法
  const saveMethod = isDraft.value ? activityUpdate : activitySaveDraft;

  const [error] = await to(saveMethod(submitFormData.value));

  saveLoading.value = false;

  if (error) {
    return;
  }

  MessagePlugin.success('保存成功');

  updateCacheTeamId();

  if (isScene) {
    ipcRenderer.invoke('refresh-scence-activity');
  }

  closeTab();

  setTimeout(() => {
    router.push('/activity/activityListDraft');
  }, 0);
};

// 发布活动
const publish = async () => {
  // 基本信息表单校验
  const basicFormResult = await stepRefs.basicRef.validate();
  if (basicFormResult !== true) {
    // 未通过校验跳转基本信息
    currentStep.value = 'basic';
    // 再进行一次校验，这里是为了在表单组件显示后触发校验失败的定位，其他地方同理
    await stepRefs.basicRef.validate();
    return;
  }

  // 高级设置表单校验
  const advancedFormResult = await stepRefs.advanceSettingRef.validate();
  if (advancedFormResult !== true) {
    // 未通过校验跳转高级设置
    currentStep.value = 'advanceSetting';
    await stepRefs.advanceSettingRef.validate();
    return;
  }

  await startTimeConfirm();

  publishLoading.value = true;

  // 提交数据
  const [error, res] = await to(activityRelease(submitFormData.value));

  publishLoading.value = false;

  if (error) {
    return;
  }

  updateCacheTeamId();

  // 给发布成功的活动主键赋值
  publishSuccessId.value = res.data.data.id;

  // 打开发布成功弹窗
  activityPublishSuccessDialogRef.value.open();

  if (isScene) {
    ipcRenderer.invoke('refresh-scence-activity');
  }
};

// 预览活动
const preview = async () => {
  // 活动预览需要进行基本信息表单校验
  const basicFormResult = await stepRefs.basicRef.validate();
  if (basicFormResult !== true) {
    currentStep.value = 'basic';
    await stepRefs.basicRef.validate();
    return;
  }

  // 预览数据的key，草稿数据为活动id，否则为固定的key
  const keyMap = {
    0: 'NEW',
    2: 'PROFESSIONAL_NEW',
  };

  const previewKey = isDraft.value ? activityFormData.id : keyMap[activityFormData.basic.mold];
  activityStore.insertActivityPreviewData(previewKey, _.cloneDeep(submitFormData.value));

  if (isScene) {
    const path = isProfessional ? 'activityProfessionalPreviewLayout' : 'activityPreviewLayout';

    // 场景活动跳转activityPreviewLayout
    const scenePreviewPath = isDraft.value ? `/layoutActivity/${path}/${activityFormData.id}` : `/layoutActivity/${path}`;
    router.push({
      path: scenePreviewPath,
      query: {
        targetName: sceneConversationName.value,
        conversationType: sceneConversationType.value,
      },
    });
  } else {
    router.push(previewPath.value);
  }
};

// 发布成功后返回活动列表
const onBackList = async () => {
  if (isScene) {
    // 场景活动打开活动应用
    await setAccountAuthRouters('click-menu-item');
    await ipcRenderer.invoke('click-menu-item', {
      url: '/activity/activityList',
    });
    ipcRenderer.send('update-nume-index', 8);
    closeTab();
  } else {
    closeTab();
    setTimeout(() => {
      router.push('/activity/activityListCreated');
    }, 0);
  }
};

// 发布成功后跳转活动详情
const onGoDetail = () => {
  closeTab();

  const queryTeamId = isPersonal.value ? '' : activityFormData.basic.teamId;
  const queryCardId = isPersonal.value ? '' : activityFormData.basic.cardId;

  if (isScene) {
    // 场景活动通过独立弹窗打开详情
    ipcRenderer.invoke('create-dialog', {
      url: `layoutActivity/activityParticipantDetailLayout/${publishSuccessId.value}?subject=${encodeURIComponent(
        activityFormData.basic.subject,
      )}&teamId=${encodeURIComponent(queryTeamId)}&cardId=${encodeURIComponent(queryCardId)}`,
      opts: {
        x: 50,
        y: 50,
        width: 1296,
        minWidth: 1296,
        height: 720,
      },
    });
  } else {
    setTimeout(() => {
      router.push({
        path: `/activity/manage/${publishSuccessId.value}`,
        query: {
          subject: activityFormData.basic.subject,
          teamId: queryTeamId,
          cardId: queryCardId,
          mold: activityFormData.basic.mold,
        },
      });
    }, 0);
  }
};

// 初始化活动表单
const init = async () => {
  if (isScene) {
    // 如果是场景活动，查询一次组织信息（场景活动是独立窗口打开，可能活动应用中的store还没获取值）
    await activityStore.getGroupList();
    // 如果场景归属为组织的标准版活动，则将默认将自己添加为内部联系人
    if (!isPersonal.value && !isProfessional) {
      emitter.emit('add-inner-contactor', {
        rowKey: `inner-${selectedTeam.value.uuid}`,
        name: selectedTeam.value.staffName,
        targetId: selectedTeam.value.uuid,
        cardId: selectedTeam.value.uuid,
        teamId: selectedTeam.value.teamId,
        openId: getOpenid(),
        type: 'INNERCONTACTOR',
      });
    }
  }

  if (isDraft.value) {
    // 如果是编辑草稿，则获取活动详情
    const res = await getActivityDetail(route.params.id, {
      openId: getOpenid(),
      teamId: activityFormData.basic.teamId,
      cardId: activityFormData.basic.cardId,
    });

    const data = res.data.data;

    // 处理获取的数据（不处理活动成员，成员信息在活动创建后时通过接口单独查询，详情接口中不返回成员信息）
    // 没有活动归属，则默认选中个人
    data.basic.teamId = data.basic.teamId || getOpenid();
    // 处理分类id数据类型
    data.basic.categoryId = data.basic.categoryId ? data.basic.categoryId.toString() : null;
    // 处理活动地址
    data.basic.location = data.basic.location ?? {};
    // 如果报名人数返回为0，则设置为null
    data.advanced.quota = data.advanced.quota || null;
    // 未开启报名，则使用初始化的报名表单
    data.advanced.registerForms = data.advanced.openRegist
      ? data.advanced.registerForms
      : _.cloneDeep(initialRegisterForms);

    // 将时间戳都转为int（报名时间如果为0，则转为null）
    data.basic.duration.startTime = Number(data.basic.duration.startTime);
    data.basic.duration.endTime = Number(data.basic.duration.endTime);
    data.advanced.registerTime.startTime = data.advanced.registerTime.startTime === '0' ? null : Number(data.advanced.registerTime.startTime);
    data.advanced.registerTime.endTime = data.advanced.registerTime.endTime === '0' ? null : Number(data.advanced.registerTime.endTime);
    data.process.processItems = data.process.processItems.map((processItem) => ({
      ...processItem,
      lastTime: Number(processItem.lastTime),
    }));

    // 活动主键赋值
    activityFormData.id = data.id;
    // 活动基本信息数据赋值
    activityFormData.basic = data.basic;
    // 活动详情数据赋值
    activityFormData.particulars = data.particulars;
    // 活动流程数据赋值
    activityFormData.process = data.process;
    // 高级设置数据赋值
    activityFormData.advanced = data.advanced;
    // 场景活动会话信息赋值
    if (isScene) {
      activityFormData.chatId = data.chatId;
      sceneConversationType.value = data.chatId.groupId ? '3' : '1';
      sceneConversationName.value = data.chat.name;
      sceneConversationId.value = data.chatId[sceneConversationType.value === '1' ? 'cardId' : 'groupId'];
    }

    // 活动详情富文本内容回显
    stepRefs.particularsRef.editorRenderContent(activityFormData.particulars.content);
    // 初始化活动流程组件的processList
    stepRefs.processRef.initProcessList(activityFormData.process.processItems);
  } else if (isScene) {
    activityFormData.chatId = sceneConversationType.value === '1' ? { cardId: sceneConversationId } : { groupId: sceneConversationId };
  }

  if (!isDraft.value && !isScene) {
    // 非草稿箱且非场景活动，则加载缓存的活动归属
    await loadCacheTeamId();
  }

  // 给原始表单数据赋值
  originalFormData.value = _.cloneDeep(activityFormData);
};

// 加载缓存中选择过的活动归属
const loadCacheTeamId = async () => {
  const cacheTeamId = JSON.parse(localStorage.getItem('activityFormTeamId') || '{}')[getOpenid()] || getOpenid();

  // 缓存活动归属没用则直接使用个人
  if (!cacheTeamId) {
    activityFormData.basic.teamId = getOpenid();
  } else {
    // 判断缓存的归属值是否还在所属组织中，如果已经退出缓存的组织，则选中个人
    try {
      await activityStore.checkIsInGroup(cacheTeamId, false, null, false, false);
      activityFormData.basic.teamId = cacheTeamId;
    } catch {
      activityFormData.basic.teamId = getOpenid();
    }
  }

  // 活动归属默认加载后，如果是标准版活动，则将自己在组织中的内部身份添加为内部联系人
  if (!isProfessional) {
    emitter.emit('add-inner-contactor', {
      rowKey: `inner-${selectedTeam.value.uuid}`,
      name: selectedTeam.value.staffName,
      targetId: selectedTeam.value.uuid,
      cardId: selectedTeam.value.uuid,
      teamId: selectedTeam.value.teamId,
      openId: getOpenid(),
      type: 'INNERCONTACTOR',
    });
  }
};

// 更新活动归属缓存
const updateCacheTeamId = () => {
  const newCache = {
    ...JSON.parse(localStorage.getItem('activityFormTeamId') || '{}'),
    [getOpenid()]: activityFormData.basic.teamId,
  };
  localStorage.setItem('activityFormTeamId', JSON.stringify(newCache));
};

// 触发引导选择活动开始时间
const checkStartTime = () => {
  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: (
      <div>
        请在<span class="text-[#FC7C14]">基本信息</span>中设置<span class="text-[#FC7C14]">活动时间</span>
        后再添加流程
      </div>
    ),
    theme: 'info',
    confirmBtn: '去设置',
    cancelBtn: '取消',
    onConfirm: async () => {
      // 跳转基本信息，并触发活动归属的校验
      currentStep.value = 'basic';
      stepRefs.basicRef.validate({ fields: ['duration.startTime', 'duration.endTime'] });
      confirmDia.destroy();
    },
    onCancel: () => {
      confirmDia.destroy();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
    },
  });
};

// 活动开始时间小于当前系统时间的确认
const startTimeConfirm = () => new Promise((resolve, reject) => {
  const currentTime = moment().unix();
  if (activityFormData.basic.duration.startTime > currentTime) {
    resolve();
    return;
  }
  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: '活动开始时间小于当前系统时间，确定发布？',
    theme: 'info',
    confirmBtn: '确定',
    cancelBtn: '选择时间',
    onConfirm: async () => {
      confirmDia.destroy();
      resolve();
    },
    onCancel: () => {
      // 跳转基本信息
      currentStep.value = 'basic';
      confirmDia.destroy();
      reject();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
      reject();
    },
  });
});

// 打开场景活动会话
const openSceneConversation = () => {
  openChat({
    main: activityFormData.basic.cardId || getOpenid(),
    [sceneConversationType.value === '1' ? 'peer' : 'group']: sceneConversationId.value,
  });
};

// 监听页面关闭
const onDelActivityCreateTab = async (path) => {
  const formFields = ['basic', 'particulars', 'process', 'advanced'];

  // 获取表单的JSON数据
  const getFormJSON = (data) => {
    const formData = formFields.reduce((acc, field) => {
      acc[field] = data[field];
      return acc;
    }, {});

    if (!isDraft.value) {
      // 如果是新建，则需要比对成员信息（草稿中的成员信息在修改时会直接调接口更新，因此不需要在这里对比）
      formData.members = data.members;
    }

    return JSON.stringify(formData);
  };

  // 关闭确认弹窗
  const showDelTabConfirm = () => {
    const confirmDia = DialogPlugin.confirm({
      header: t('activity.activity.tip'),
      theme: 'info',
      body: '是否保存已编辑的内容',
      confirmBtn: t('activity.activity.save'),
      cancelBtn: `不${t('activity.activity.save')}`,
      onConfirm: async () => {
        saveDraft();
        confirmDia.destroy();
      },
      onCancel: () => {
        confirmDia.destroy();
        closeTab(path);
      },
      onCloseBtnClick: () => {
        confirmDia.destroy();
      },
    });
  };

  const originalFormJSON = getFormJSON(originalFormData.value);
  const activityFormJSON = getFormJSON(activityFormData);

  const isModified = originalFormJSON !== activityFormJSON;

  if (isModified) {
    // 有过更改，则弹窗提示（如果当前路由不在当前tab，先定位到当前tab，再弹窗）
    if (isActive.value) {
      showDelTabConfirm();
    } else {
      await router.push(path);
      showDelTabConfirm();
    }
  } else {
    // 无更改，直接关闭
    closeTab(path);
  }
};

// 监听操作栏禁用状态改变
const onToggleOperateDisableStatus = (disabled) => {
  operateDisabled.value = disabled;
};

// 设置当前tab
const setTabItem = () => {
  const createPath = isProfessional ? 'activityProfessionalCreate' : 'activityCreate';

  if (!activityStore.tabList?.some((tab) => tab.tag === (isDraft.value ? route.path : createPath))) {
    const newTabItem = {
      path: isDraft.value ? route.path : `/activity/${createPath}`,
      name: route.path,
      title: isDraft.value ? t('activity.activity.editActivity') : t('activity.activity.addActivity'),
      query: route.query,
      tag: isDraft.value ? route.path : createPath,
      type: 13,
    };

    emit('settabItem', newTabItem);
    emit('setActiveIndex', activityStore.tabList.length - 1);
  }
};

// 关闭当前tab
const closeTab = (path = route.path) => {
  if (isScene) {
    // 场景活动独立弹窗，则关闭弹窗
    ipcRenderer.invoke('close-dialog');
  } else {
    // 关闭对应预览页tab
    const previewIndex = activityStore.tabList.findIndex((e) => e.path === previewPath.value);
    if (previewIndex !== -1) {
      emit('deltabItem', previewIndex, true);
    }

    setTimeout(() => {
      const index = activityStore.tabList.findIndex((e) => e.path === path);
      emit('deltabItem', index, true);
    }, 0);
  }
};

onMounted(() => {
  setTabItem();
  init();

  emitter.on('del-activity-create-tab', onDelActivityCreateTab);
});

onBeforeUnmount(() => {
  emitter.off('del-activity-create-tab', onDelActivityCreateTab);
});

onActivated(() => {
  emitter.on('toggle-operate-disable-status', onToggleOperateDisableStatus);
});

onDeactivated(() => {
  emitter.off('toggle-operate-disable-status', onToggleOperateDisableStatus);
});
</script>

<style lang="less" scoped>
.scene-wrapper {
  background: url('https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/caadb0b/bg.png') no-repeat center / 100% 100%;
  padding: 12px 0;
  justify-content: center;

  .operate-box {
    box-shadow: none;
    border-radius: 8px;
    margin-left: 12px;
  }
}

.create-form-wrapper {
  height: calc(100% - 64px);
  padding: 12px;

  .create-form-content {
    height: 100%;
    border-radius: 8px;
    background: #fff;

    .create-form-content-scroll {
      height: 100%;
      overflow: overlay;

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
    }
  }
}

.operate-box {
  background: #fff;
  box-shadow: 10px -3px 8px 0px rgba(0, 0, 0, 0.08);
  padding: 16px 0;
  display: flex;
  justify-content: center;
  gap: 8px;

  .t-button {
    width: 88px;

    &.publish-button {
      min-width: 88px;
      width: auto;
    }
  }
}
</style>
