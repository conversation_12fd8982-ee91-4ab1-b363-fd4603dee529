.icon-specials {
  width: 8px;
  height: 8px;
  position: absolute;
  top: 0;
  left: 0;
  //color: linear-gradient(0deg,
  //    var(--bg-kyy-color-bg-software-foucs, #272b4f) 0%,
  //    var(--bg-kyy-color-bg-software-foucs, #272b4f) 100%),
  //  var(--bg-kyy-color-bg-software-lose-foucs, rgba(20, 26, 66, 0.8));
  color: var(--bg-kyy-color-bg-software-foucs, #272b4f);
}

.operate-item:hover {
  background: #f0f8ff;
}

.operate-item-fliter-group-item {
  min-width: 320px !important;
  cursor: pointer;
}

.refreshleft {
  position: relative;

  &.divider::after{
    content: '';
    width: 1px;
    height: 16px;
    position: absolute;
    top: 3px;
    left: -8px;
    background: var(--divider-kyy-color-divider-deep, #D5DBE4) !important;
  }
}

.operate-item-fliter-group-item:hover {
  border-radius: 4px;
  background: var(--lingke-select, #e1eaff);
}

.pl44 {
  padding-left: 44px;
}

.bgc-fff {
  background: #fff !important;
}

.bgc-fff::after {
  display: none !important;
}

.bgc-fff::before {
  content: '';
  width: 1px;
  height: 20px;
  position: absolute;
  top: 7px;
  left: -1px;
  background: #EBF0FC;
  z-index: 999;
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
  background-color: #f5f5f5;
}

.zhixing-content .scrollbar::-webkit-scrollbar {
  width: 0 !important;
  height: 7px;
  background-color: #f5f5f5;
}

.red-roud-badge-box {
  display: block;
  min-width: 20px;
  height: 32px;
  text-align: center;

}

.tabs-item:first-child {
  margin-left: 4px !important;
}

.tabs-item:last-child::after {
  display: none;
}

.head-box {
  background: var(--kyy-color-tabbar-bg, #EBF1FC) !important;

  .tabs-item {
    position: relative;
    // min-width: 168px !important;
    min-width: 140px;
    max-width: 168px;
    box-shadow: none !important;
    background: transparent;

  }

  .tabs-item::after {
    content: '';
    width: 1px;
    height: 20px;
    position: absolute;
    top: 7px;
    right: 0;
    background: #D5DBE4;
  }



  .tap-close-item:hover {
    color: #707EFF;


  }

  .tabs-item:hover::after {
    display: none;
  }

  .tap-close-item {
    width: 20px !important;
    height: 20px !important;
    margin-right: 12px !important;
    margin-left: 8px !important;
  }

  .tab-icon {
    margin-left: 12px !important;
    margin-right: 8px !important;
  }
}

.red-dot-tap {

  width: 8px !important;
  height: 8px !important;
  position: absolute !important;
  top: 0px;
  left: 20px;
}

.org-img-arrow {
  position: relative;
}

.pl12 {
  padding-left: 12px;
}

.m012 {
  margin: 0 12px;
}

.w24h24br50 {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.org-Infor {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: relative;
  max-width: 239px;

  .org-img {
    width: 24px;
    height: 24px;
    border-radius: 5px;
    margin-left: 4px;
    border-radius: 100%;
  }

  .org-img-arrow {
    width: 16px;
    height: 16px;
    margin-right: 16px;
  }

  .org-text {
    // max-width: 135px;
    font-size: 14px;

    font-weight: 400;
    margin: 0 4px;
    color: #000;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .red-dot {
    width: 6px;
    height: 6px;
    background: #da2d19;
    border-radius: 50%;
    position: absolute;
    right: 16px;
    top: 2px;
  }
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  // box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  // border-radius: 10px;
  background-color: #e3e6eb;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #c8c8c8;
}

.ovfdian {
  width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block !important;
}

.tabs-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 35px;
  border-radius: 5px 0 0 0;
  background: var(--kyy_color_tabbar_bg, #ebf1fc);
  &:hover {
    cursor: pointer;
    background: var(--kyy_color_tabbar_item_bg_hover, rgba(255, 255, 255, 0.5));
  }
}

.head-box {
  // ps
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  overflow: hidden;
  // background: #e3e6eb;
  // border-radius: 8px 0px 0px 0px;
  background: var(--kyy-color-tabbar-bg, #ebf1fc);

  .tabs-item:hover {
    // background: #fff !important;
    background: var(--kyy_color_tabbar_item_bg_hover, rgba(255, 255, 255, 0.50)) !important;

    .tap-close-item {
      color: #707EFF !important;
    }
  }

  .tabs-list:hover {
    // overflow-x: overlay !important;
  }

  .tabs-list {
    display: flex;
    flex: 1;
    width: 0;
    overflow-x: hidden;
    background: var(--kyy_color_tabbar_bg, #ebf1fc);
    border-top-left-radius: 8px;
    &--box {
      display: flex;
      transition: transform 0.5s;
    }
    .tabs-item {
      cursor: pointer;
      min-width: 140px;
      justify-content: space-between;
      height: 32px;
      margin: 4px 0;
      display: flex;
      align-items: center;
      // background: transparent;
      background: var(--kyy_color_tabbar_bg, #ebf1fc);

      border-radius: 4px;
      // box-shadow: 0px 1px 4px 0px rgba(19, 22, 27, 0.24);

      .left-head-color {
        width: 5px;
        border-radius: 4px 0 0 4px;
        height: 32px;
        background: #2069e3;
      }

      span {
        overflow: hidden;
        color: var(--kyy_color_tabbar_item_text, #1A2139);
        text-overflow: ellipsis;

        /* kyy_fontSize_2/regular */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        // height: 20px;
        // font-size: 12px;

        // font-weight: 400;
        // color: #13161b;
        // line-height: 20px;
      }
    }
  }
}


.tab-icon {
  width: 20px;
  height: 20px;
}

.flex-a {
  display: flex;
  align-items: center;
}

.org-img-window:hover {
  color: #707EFF;
}

.org-img-window {
  width: 20px;
  cursor: pointer;
  height: 20px;
  margin-right: 16px;
}

.flex-align-sb {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2px;
}

.red-roud-badge {
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  background: var(--kyy-color-tag-bg-magenta, #ffe3f1);
  color: var(--kyy-color-tag-text-magenta, #ff4aa1);
  text-align: center;
  font-family: PingFang SC;
  font-size: 12px;
  border-radius: 50%;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  margin-top: 5px;
}

.flex-align {
  display: flex;
  align-items: center;
}

:deep(.t-badge--circle) {
  color: #fff;
}
