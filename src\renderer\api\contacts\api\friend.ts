

import {iam_srvRequest, client_orgRequest, im_syncRequest, ringkolRequestApi } from '@/utils/apiRequest'

export function getFriendList() {
  return client_orgRequest({
    method: "get",
    url: "/addressbook/contacts",
  });
}

// 搜索个人用户
export function searchProfilesCardList(data) {
  return iam_srvRequest({
    method: "post",
    url: "/v1/profiles/search",
    data,
  });
}

// 搜索外部身份卡
export function searchExternalCardList(params, teamId: string) {
  return client_orgRequest({
    method: "get",
    url: "/external/idCard",
    params,
    headers: {
      teamId,
    },
  });
}

// 全部的好友申请记录
export function getAllApplyList(params,teamId?: string) {
  return ringkolRequestApi({
    method: "get",
    url: "/im/v1/friend/apply/listFriendApplies",
    params,
    headers: {
      teamId,
    },
  });
}

// 检查是否有添加联系人的申请记录
export function checkApply(params) {
  return ringkolRequestApi({
    method: "get",
    url: "/im/v1/friend/apply/checkFriendApply",
    params
  });
}

// 判断是否可以发起好友申请
export function canApplyFriend(data:{main:string,peer:string}) {
  return ringkolRequestApi({
    method: "post",
    url: "/im/v1/friend/apply/canApplyFriend",
    data
  });
}