import { ReceiptInfo } from '@rk/im-sdk/dist/types/entity';
import { getImCardIds, getOpenid, getCurrentAccount, checkNewVersionApi } from '@renderer/utils/auth';
import { MsgShareType } from '@renderer/utils/share';
import { debounce, delay } from 'lodash';
import { CbEvents } from '@rk/im-sdk';
import { logHandler } from '@renderer/log';
import { getMsgToStore, getSessionLocalIdByCards, fileReplaceUrl } from './utils';
import { useMessageStore } from './store';
import useChatSendStore, { ConversationToSend } from './sendStore';
import { onNotifyMessages } from './extend/statusUtils';
import { loadPrivateSession } from './request';
import { IMStatus, useNetStore } from './imStatus';
import { resumeConnect } from './rkimConnect';

import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;

// const SYSTEM_IS_ACTIVE = true;
export const addMsgEventHanders = () => {
  const eventHandles = {
    IMCONNECT: onIMConnectChange,
    MESSAGES: messageHander, // 收到别人发送的，和其他设备同步的消息。
    [CbEvents.OnRecvC2CReadReceipt]: onReadReceiptEvent, // 收到已读回执（单聊）
    [CbEvents.OnRecvGroupReadReceipt]: onReadReceiptResponseEvent, // 收到已读回执响应（群聊）
    [CbEvents.OnNewRecvMessageRevoked]: onRevokedRecvMessage, // 撤回消息
    [CbEvents.OnConversationChanged]: onConversationChanged, // 会话变更
    [CbEvents.OnSyncServerFinish]: onSyncServerFinish, // 收到同步完成
    [CbEvents.OnMsgSend]: OnMsgSend, // 发送消息后sdk消息回执。
  };

  ipcRenderer.removeAllListeners('im.msg.send');
  ipcRenderer.removeAllListeners('im.chat.open');
  ipcRenderer.removeAllListeners('im.main.av');
  ipcRenderer.removeAllListeners('im.events');
  ipcRenderer.removeAllListeners('im.msg.open');
  ipcRenderer.removeAllListeners('im.card.update');
  // ipcRenderer.removeAllListeners('im.group.member.update');
  // ipcRenderer.removeAllListeners('system-activate');
  ipcRenderer.removeAllListeners('system-lock-screen');

  // ipcRenderer.on('system-activate', debounce(() => {
  //     resumeConnect('system-activate');
  //     // SYSTEM_IS_ACTIVE = true;
  // }, 300));

  ipcRenderer.on('system-lock-screen', (evt, args) => {
    useMessageStore().setPowerShutdown(args?.type);
    if (['suspend', 'lock-screen'].includes(args?.type)) {
      console.log(`=====>触发了${args?.type}`, new Date());
      // SYSTEM_IS_ACTIVE = false;
      delay(() => {
        // if (SYSTEM_IS_ACTIVE) return;
        useNetStore().disconnectIm(args?.type);
      }, 10 * 1000);
    } else {
      // SYSTEM_IS_ACTIVE = true;
      resumeConnect(args?.type);
      checkNewVersionApi();
    }
    logHandler({ name: '系统休眠唤醒', info: `${JSON.stringify(args?.type)}`, desc: 'system-lock-screen' });
  });

  ipcRenderer.on('im.msg.open', (evt, args: { messageUId: string, sentTime: number, localSessionId: string, conversationType: number, targetId: string }) => {
    useMessageStore().gotoMessage(args);
  });

  ipcRenderer.on('im.card.update', (evt, params: IChatCardUpdateNotificationItem) => {
    useMessageStore().checkPeerInfo(params);
  });

  ipcRenderer.on('im.msg.send', sendApplicationMsg);

  ipcRenderer.on('im.chat.open', async (event, args) => {
    await useMessageStore().onStartChat(args);
  });

  ipcRenderer.on('im.main.av', (event, args) => {
    switch (args?.action) {
      case 'meeting_end': {
        const endInfo = args.data as MeetingEndInfo;
        const conversation = {
          conversationType: endInfo.conversation.type,
          targetId: endInfo.conversation.target,
          localSessionId: endInfo.conversation.id,
          myCardId: endInfo.senderId,
          targetCardId: endInfo.receiverId,
        };
        const { sendData } = useChatSendStore();
        endInfo.data && sendData(endInfo.data, 'meeting_end', conversation);
        endInfo.text && sendData({ text: endInfo.text }, 'text', conversation);
        break;
      }
      case 'meeting_start': {

      }
    }
  });

  ipcRenderer.on('im.events', (_, data: string) => {
    // args:IMBridgeCallbackData
    const args = JSON.parse(data);
    const handler = eventHandles[args.eventName];
    console.log('[IMEvents]', `[${args.eventName}]`, args.eventArgs);

    if (handler) {
      handler(...args.eventArgs);
    }
  });
};

/**
 *
 * @param type 消息类型
 * @param content 消息类容，不同业务自行定义，移动端和 PC 端需要保持结构一致。
 * @param sessionList 消息发送选中的会话
 * @param sessionList.targetId 会话 id，单聊为openId字段，群聊为群group字段
 * @param sessionList.conversationType 会话类型，1为单聊，3为群聊
 * @param sessionList.myCard 当前用户的身份卡，没有则不传
 * @param sessionList.targetCard 对方的身份卡，没有则不传
 */
export const sendApplicationMsg = async (_, params: { type: string, content: any, sessionList?: any[], approverCards?: string[] }) => {
  const { type, content, sessionList } = params;
  if (!type || !content) {
    console.error(new Error('发送消息参数错误'), type, content, sessionList);
    return;
  }

  if (type === MsgShareType.approve_comment) {
    return sendApproveComment(params);
  }

  const conversations: ConversationToSend[] = [];
  const { allMembers, onPrivateChattingFromContact } = useMessageStore();
  const { sendData } = useChatSendStore();
  const openId = getOpenid();
  console.log('=====>sendApplicationMsg', sessionList);
  sessionList?.forEach((item) => {
    if (item.conversationType === 1) {
      const myInfo = item?.attachment?.member?.find((c) => c.cardId !== item.cardId);
      const myCardId = myInfo?.cardId || openId;
      conversations.push({
        conversationType: 1,
        targetId: item.openId,
        localSessionId: getSessionLocalIdByCards(item.cardId, myCardId),
        myCardId,
        targetCardId: item.cardId,
      });

    } else {
      const members = allMembers.get(item.targetId);
      // 选中身份卡来决定发送消息
      const mainInfo = members?.get(item.cardId);
      let myCardId = item.cardId
      if (!mainInfo) {
        const myCards = getImCardIds().filter((c) => members?.get(c));
        myCardId = myCards.find((c) => c !== openId) || openId;
      }
      conversations.push({
        conversationType: item.conversationType,
        targetId: item.targetId,
        localSessionId: getSessionLocalIdByCards(item.targetId, myCardId),
        myCardId,
        myOpenImId: members.get(myCardId).openImId,
        targetCardId: item.cardId,
        targetOpenImId: item.targetId,
      });
    }
  });

  conversations.forEach(async (item) => {
    if (item.conversationType === 1) {
      // 分享进来 单聊需要先检测是否建立聊天关系再发送消息。
      const res = await loadPrivateSession(item.myCardId, item.targetCardId, { ...conversations, type: 'sendApplicationMsg' });
      console.log('=====>res?.session', res);
      if (res?.session) {
        await onPrivateChattingFromContact(res);
        const myInfo = res?.members?.[0];
        const toConversation = { staffName: myInfo?.staffName || myInfo.nickname, avatar: myInfo.avatar, ...res?.session };
        sendData(content, type, toConversation);
      }
    } else {
      sendData(content, type, item);
    }

  });
};

export const sendApproveComment = async (params: { type: string, content: any, approverCards?: string[] }) => {
  const { sendData } = useChatSendStore();
  params.approverCards?.forEach(async (card) => {
    const info = await loadPrivateSession(params.content?.commentatorId, card);
    if (info?.session) {
      console.log(card, info, params.content);
      sendData(params.content, params.type, info.session);
    }
  });
};

/**
 * 更新IM连接状态
 * @param args
 */
const onIMConnectChange = (args) => {
  console.log('====>args', args);
  useNetStore().setImStatus(args);
};

// 新增消息的回调处理
const messageHander = async ({ messages }) => {
  // sdk同步完成再去更新
  if (!useNetStore().isServerFinish) {
    onApplaunchIsServerFinish('messageHander');
    return false;
  }
  const convertedMessages = messages.map((val) => getMsgToStore(val));

  // 收到消息现在只更新聊天界面，会话摘要排序都通过onConversationChange更新。
  if (useNetStore().isServerFinish) {
    // 需要保存到数据库的消息
    const toSaveList = convertedMessages.filter((item) => {
      // 是否系统通知
      if (item.conversationType === 4 || !item.localSessionId) {
        return false;
      }
      // 音视频信令
      if (item.contentExtra?.contentType === 'meeting') {
        if (['ringing', 'accept'].includes(item.contentExtra.data.action)) {
          return false;
        }
        // 群聊的信令过滤掉单聊通知
        if (item.contentExtra?.data.meetingType === 'group' && item.conversationType === 1) {
          return false;
        }
      }
      return true;
      // 自定义是否存储的消息
      // const isStore = isStorageMsg(item);
    });
    console.log('=====>toSaveList', toSaveList);
    // 将待处理消息按会话分组，map的key为会话id（单聊 localSessionId， 群聊 targetId）
    const messageMap: Map<string, ConversationMsgReceiveData> = new Map();
    // 1.将需要保存的消息按会话分组，并且确定未读数、@数和是否需要清零未读数
    toSaveList.forEach((msg) => {
      const conversationId = msg.conversationType === 1 ? msg.localSessionId : msg.targetId;
      const msgItem = fileReplaceUrl(msg);
      const item = messageMap.get(conversationId);

      if (item) {
        item.msgList.push(msgItem);

      } else {
        messageMap.set(conversationId, {
          localSessionId: msg.localSessionId,
          targetId: msg.targetId,
          conversationType: msg.conversationType,
          msgList: [msgItem],
          cleans: [],
        });
      }
    });

    // 4.消息分发到状态管理，并更新界面
    if (messageMap.size) {
      const receiveList = Array.from(messageMap.values());
      useMessageStore().onReceiveRemoteMessages(receiveList);
    }
  }
  // 5.处理通知消息
  convertedMessages.forEach((item) => {
    if(['APP_ADDRESS_BOOK', 'APP_TEAMS', 'APP_APPROVAL'].includes(item.contentExtra?.contentType)){
      onNotifyMessages(item);
      return;
    }
    if (item.isOffline) return;
    if ([4, 11].includes(item.conversationType) || ['server_group_card_update', 'APP_PAIRS', 'APP_ASSISTANTS'].includes(item.contentExtra?.contentType)) {
      onNotifyMessages(item);
    }
  });
};

// 撤回消息
const onRevokedRecvMessage = async (revokedInfo) => {
  const msg = {
    recallTime: revokedInfo.revokeTime,
    messageUId: revokedInfo.clientMsgID,
    conversationID: revokedInfo.conversationID,
  };

  // 更新界面
  useMessageStore().onMessageRecalled(msg);
};

const onPrivateReadReceiptEvent = async (evt) => {
  const { msgIDs, readTime } = evt;

  const { changeMsgRead } = useMessageStore();
  const readParams: Parameters<typeof changeMsgRead>[0] = {
    messageUId: msgIDs,
    conversationType: 1,
    readTime,
    receiptTime: Date.now(),
    receipts: null,
    conversationID: evt.conversationID,
  };
  // 更新界面
  changeMsgRead(readParams);
};

const onGroupReadReceiptEvent = async (evt) => {
  const { chatingSession, changeMsgRead } = useMessageStore();
  // 异步队列更新的已读未读，再判断一次不在聊天窗口的已读响应不处理已读未读
  if (!chatingSession || chatingSession.conversationID !== evt.conversationID) {
    return false;
  }
  const { msgIDList, msgReceiptList, readTime } = evt;

  if (!msgReceiptList.length) {
    return false;
  }

  const readParams: Parameters<typeof changeMsgRead>[0] = {
    targetId: evt.groupID,
    messageUId: msgIDList,
    conversationType: 3,
    readTime,
    receiptTime: Date.now(),
    receipts: msgReceiptList,
    conversationID: evt.conversationID,
  };
  changeMsgRead(readParams);

  return 'readDbDone';
};
// 收到已读回执（单聊）
const onReadReceiptEvent = async (evt: ReceiptInfo) => {
  // sdk同步完成或者在聊天界面再去更新单聊已读未读,摘要已读未读onconversationchange修改
  if (useMessageStore().chatingSession && useMessageStore().chatingSession.conversationID === evt.conversationID) {
    onPrivateReadReceiptEvent(evt);
  }
};

const GroupReadStackData = []; // 群聊已读响应栈
let reading = false; // 是否真正更新已读状态
// 收到已读回执响应（群聊）
const onReadReceiptResponseEvent = async (evt) => {
  const { chatingSession } = useMessageStore();
  // 不在聊天窗口的已读响应不处理已读未读
  if (!chatingSession || chatingSession.conversationType !== 3 || chatingSession.conversationID !== evt.conversationID) {
    return false;
  }
  GroupReadStackData.push(evt);
  if (!reading) {
    onGroupReadReceiptQueue();
  }
};

/**
 * 群聊已读更新队列，使已读更新流程同步执行
 * @returns
 */
const onGroupReadReceiptQueue = async () => {
  reading = true;
  const readingData = GroupReadStackData.shift();
  if (!readingData) {
    reading = false;
    return;
  }
  const res = await onGroupReadReceiptEvent(readingData);
  onGroupReadReceiptQueue();
};

/** 会话变更队列
 *  通过需要更新的会话ID队列取map里面更新的数据
 */
const conversationChangeMap = new Map();
const conversationChangeIDs = [];
let isUpdating = false;
const updateConversationChanged = () => {
  if (conversationChangeIDs.length === 0) {
    isUpdating = false;
    return;
  }
  isUpdating = true;
  // 取队列第一个进行更新
  const currentItem = conversationChangeIDs.shift();
  const item = conversationChangeMap.get(currentItem);
  if (item) {
    conversationChangeMap.delete(currentItem);
    useMessageStore().onConversationChanged(item);
    setTimeout(updateConversationChanged, 100);
    console.log('====>conversationChangeIDs', currentItem, item);
    // logHandler({ name: 'updateConversationChanged', info: `${currentItem}, ${JSON.stringify(item)},`, desc: `handler->updateConversationChanged` });
  } else {
    updateConversationChanged();
  }
};
// 更新updateTime = latestMsgSendTime，latestMsg，unreadCount
const onConversationChanged = async ({ data }) => {
  // 同步完成第一次之后处理，做清零处理，红点数量更新， 单聊摘要处理已读未读状态
  const isServerFinish = useNetStore().isServerFinish;
  if (!isServerFinish) {
    onApplaunchIsServerFinish('onConversationChanged');
  }
  //   const openId = getOpenid();
  data.forEach((item) => {
    // if (item.userID.includes(openId)) {
    conversationChangeIDs.push(item.conversationID);
    const { latestMsgSendTime: updateTime, latestMsg, conversationID, conversationType, unreadCount, isPinned } = item;
    conversationChangeMap.set(item.conversationID, {
      updateTime, latestMsg, conversationID, conversationType, unreadCount, isPinned
    });
    // }
  });
  // 同步完成之后才做更新，未同步完成之前只存在conversationChangeMap中
  isServerFinish && !isUpdating && updateConversationChanged();
};
/** 会话变更队列结束 */

/**
  * 会话拉取完成
  */
let isFinishing = false;
const onSyncServerFinish = async (data) => {
  useNetStore().setImStatus(1);
  if (data?.type !== 'firstInit' && useNetStore().isServerFinishTimes < 2) {
    useNetStore().isServerFinishTimes += 1;
  }
  if (isFinishing) return;
  isFinishing = true;
  if (!useNetStore().isServerFinish) { // 数据同步一次
    await useMessageStore().onSyncServerFinish([], '', 'onSyncServerFinish');
    // 同步的时候看是否存在打开的会话，拉取第一页历史记录seq对比chatingMessages
    useMessageStore().chattingMsgRefresh();
    useMessageStore().setCurrentTime(false);
    logHandler({ name: '同步拉取数据', info: `${JSON.stringify(data)}, isServerFinish: ${useNetStore().isServerFinish}, onSyncServerFinish: ${useNetStore().isServerFinishTimes}, date:${new Date()}`, desc: 'Hander->onSyncServerFinish' });
  }
  useNetStore().setServerFinish(true);
  isFinishing = false;
  // 同步完成触发会话更新
  !isUpdating && updateConversationChanged();
  logHandler({ name: 'onSyncServerFinish', info: `${JSON.stringify(data)}, isServerFinish: ${useNetStore().isServerFinish}，date:${new Date()}`, desc: 'Hander->onSyncServerFinish' });
  console.log('onSyncServerFinisht', useNetStore().isServerFinish, data, new Date());
};

// 12秒内没有onConversationChanged,没有新消息，则认为加载完成，可能sdk不会主动发送过来
export const onApplaunchIsServerFinish = debounce((origin?) => {
  console.log('====>onApplaunchIsServerFinish', origin);
  if (origin === 'onSyncServerFinish') {
    useNetStore().setServerFinish(true);
    // 同步完成触发会话更新
    !isUpdating && updateConversationChanged();
    useMessageStore().setCurrentTime(false);
    return;
  }
  onSyncServerFinish(origin);
}, 12 * 1000);

const OnMsgSend = (msg) => {
  useMessageStore().updateSendMsg(msg.msg);
};
