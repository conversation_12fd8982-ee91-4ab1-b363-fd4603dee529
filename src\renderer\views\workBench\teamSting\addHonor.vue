<template>
  <div class="addhonor" style="padding: 16px 304px">
    <t-form ref="honorForm" class="mt16 honorForm" :label-width="600" :data="formData" :rules="rules"
      scroll-to-first-error="smooth" @submit="onSubmit">
      <!-- 是否开启发布验证 -->
      <div v-if="openFlag" class="chatBox">
        <img src="@renderer/assets/bench/icon_info.svg" alt="" />
        <div>{{ `${t("banch.publishverify")}` }}</div>
      </div>
      <!-- 荣誉标题 -->
      <t-form-item :label="t('banch.honorTitle')" name="title" label-align="top" style="flex: 1">
        <t-textarea v-model="formData.title" class="title" :maxlength="200" show-limit-number
          :autosize="{ minRows: 1, maxRows: 5 }" :placeholder="t('banch.honorTitleph')" @keydown="preventEnter" />
      </t-form-item>
      <!-- 荣誉封面 -->
      <t-form-item :label="t('banch.honorUrl')" label-align="top" name="avatar">
        <div v-show="!formData.avatar">
          <div style="display: flex; align-items: center">
            <div class="upload-box" @click="triggerUpload">
              <img src="@renderer/assets/activity/upload.svg" alt="" />
              <div>{{ t("activity.activity.clickUpload") }}</div>
            </div>
            <div class="upload-box-tips">
              <div></div>
              <div>
                <div>{{ t("activity.activity.uploadTip7") }}</div>
                <div>{{ t("activity.activity.uploadTip8") }}</div>
                <div>{{ t("activity.activity.uploadTip9") }}</div>
              </div>
            </div>
          </div>
        </div>
        <t-upload v-show="formData.avatar" ref="uploadRef" v-model="avatarImages" class="avatarUpload" theme="image"
          :max="2" :upload-all-files-in-one-request="false" :before-all-files-upload="uploadCheck"
          :request-method="assetUrlUploadImage" :on-remove="avatarRemove" :size-limit="{
            size: 2,
            unit: 'MB',
          }" :locale="{
            triggerUploadText: {
              image: t('activity.activity.clickUpload'),
            },
          }" @click.stop="imageclick">
          <template #fileListDisplay>
            <div class="t-upload__card-content t-upload__card-box" @click="triggerUpload">
              <t-image :src="formData.avatar" fit="cover" class="t-upload__card-image" />
            </div>
          </template>
          <template #tips>
            <div></div>
            <div>
              <div>{{ t("activity.activity.uploadTip7") }}</div>
              <div>{{ t("activity.activity.uploadTip8") }}</div>
              <div>{{ t("activity.activity.uploadTip9") }}</div>
            </div>
          </template>
        </t-upload>
      </t-form-item>
      <!-- 获得荣誉时间 -->
      <t-form-item :label="t('banch.honorTime')" name="obtain_date" label-align="top" style="flex: 1">
        <t-date-picker id="durationRef" v-model="formData.obtain_date" clearable format="YYYY-MM-DD" style="width: 100%"
          :placeholder="t('banch.honorTimeph')"
          :default-time="[moment().format('HH:mm:ss'), moment().format('HH:mm:ss')]" @change="startChange" />
      </t-form-item>
      <!-- 正文内容 -->
      <t-form-item :label="t('banch.honorContent')" name="content" label-align="top" style="flex: 1" class="QuillBox">
        <Editor :editor-type="'B'" class="activity-lk-editors" ref="editorRef" type="B" root-dir="honor" :options="{ toolbar: ['annex', 'link'],
          height: 320,
           showBubble: true
           }" @update="handleContentChange" />

        <div class="upload-input">
          <!-- <upload
            ref="uploadImg"
            :total-size="totalSize"
            :cur-size="noteData.annex_size"
            @update-upload="imgInsert"
          /> -->
          <t-upload style="display: none" ref="uploadImg" multiple theme="image" :max="10"
            :upload-all-files-in-one-request="false" :before-all-files-upload="uploadCheck"
            :allowUploadDuplicateFile="true" :request-method="imagesInsert" :size-limit="{
              size: 2,
              unit: 'MB',
            }" :locale="{
              triggerUploadText: {
                image: t('activity.activity.clickUpload'),
              },
            }">
          </t-upload>
        </div>
      </t-form-item>
      <!-- 发布至 -->
      <t-form-item :label="t('banch.sendTo')" name="channel_type" label-align="top" class="sendTo">
        <t-checkbox-group v-model="formData.channel_type" @change="onChange2">
          <t-checkbox key="1" value="square_number" v-if="sendTo.square">{{ t("banch.square") }}</t-checkbox>
          <t-checkbox key="2" value="digital_factory" v-if="sendTo.workshop">{{ t("banch.banch") }}</t-checkbox>
          <t-checkbox key="3" value="digital_platform"
            v-if="sendTo.member || sendTo.government || sendTo.cbd || sendTo.association|| sendTo.uni">{{
            t("banch.member")
            }}</t-checkbox>
        </t-checkbox-group>
      </t-form-item>
      <!-- 底部按钮 -->
      <t-form-item :label-width="0" label-align="left" class="bottomButtons">
        <div>
          <t-button class="activityDraft" style="margin-right: 8px" theme="default" :loading="draftLoading"
            @click="saveDraft">{{ t("activity.activity.saveDraft") }}</t-button>
          <t-button style="margin-right: 8px" theme="default" @click="preview">预览效果</t-button>
          <t-button :class="activityDetail?.details?.status == 'Released' ? 'activityDraft' : 'activitySubmit'"
            :theme="activityDetail?.details?.status == 'Released' ? 'default' : 'primary'" :disabled="draftLoading"
            :loading="submitLoading" type="submit">{{ t("banch.publish") }}</t-button>
        </div>
      </t-form-item>
    </t-form>
    <!-- 点击存草稿的弹窗 -->
    <t-dialog v-model:visible="saveDraftFlag" class="appAuthTipDialog" theme="info" :cancel-btn="t('address.cancel')"
      :close-btn="false" :confirm-btn="t('address.sure')" :header="t('banch.saveDraft')" :body="t('banch.saveDraftTip')"
      @confirm="debouncedSaveDraftConfirm">
    </t-dialog>

    <!-- 预览 -->
    <PreviewRelease v-if="previewVisible" v-model:visible="previewVisible" type="list" :data="formData" />

    <!-- 审核人或未开启安全认证点击发布的确认弹窗 -->
    <SecurityVerification ref="SecurityVerificationRef" :data="data" :activation-group-item="activationGroupItem"
      :config="{
        showOverlay: true,
        closeOnEscKeydown: false,
        closeOnOverlayClick: false,
        verificationType: 'releaseConfirmHonor',
      }" @handle="auditConfirm" />
    <!-- 非审核人的弹窗 -->
    <select-body-dialog v-if="selectVisible" v-model:visible="selectVisible" options-type="honor"
      :id-team="activationGroupItem.teamId" @confirm="selectConfirm" />
  </div>
</template>

<script setup>
  import {
    ref,
    onMounted,
    computed,
    watch,
    nextTick,
    onUnmounted,
    reactive,
    onActivated,
    toRef,
    onDeactivated,
  } from "vue";
  import { useI18n } from "vue-i18n";
  import moment from "moment";
  import { PutObjectResult } from "ali-oss";
  import _, { stubFalse } from "lodash";
  import {
    addHonorAPI,
    draftHonorAPI,
    editHonorAPI,
    detailHonorAPI,
    auditHonorAPI,
    auditAPI,
  } from "@renderer/api/workBench/index.ts";
  import { useHonorStore } from "@renderer/views/workBench/teamSting/honorStore";
  import upload from "@renderer/views/zhixing/components/upload.vue";
  import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import SecurityVerification from "@renderer/views/workBench/components/SecurityVerification.vue";
  import SelectBodyDialog from "@renderer/views/workBench/components/select-body-dialog.vue";
  import PreviewRelease from "@renderer/views/workBench/components/previewRelease.vue";
  import { preventEnter } from "@renderer/views/workBench/utils";
  import { getFileType } from "@/views/message/service/utils";
  import { imgType, fileTypes } from "@/views/zhixing/constant";
  import uploadImage, { blobToPngFile } from "@/views/square/utils/upload";
  import { useImageUpload } from "@/views/square/hooks/upload";
  import { getAppsState } from "@/api/pb/manage";
  import { useRoute, useRouter } from "vue-router";
  import Editor from "@/components/editor/index.vue";
  const route = useRoute();
  // 公共store
  const honorStore = useHonorStore();
  const { t } = useI18n();
  // 表单数据
  const formData = reactive({
    title: "",
    avatar: "",
    obtain_date: "",
    content: "",
    channel_type: [],
    reviewer: null,
    content_text: "",
  });
  // 清空表单数据
  const honorForm = ref(null);
  const clearFormData = () => {
    honorForm.value.clearValidate();
    honorForm.value.reset();
    // formData.title='';
    // formData.avatar='';
    // formData.obtain_date='';
    // formData.content='';
    // formData.channel_type=[];
    // formData.content_text=''
    // uploadImg.value.clear();
    avatarImages.value = [];
    formData.id = null;
    noteData.value.content.images = [];
    editorRef.value.renderContent({ ops: [] });
    honorForm.value.clearValidate();
    honorForm.value.reset();
    submitLoading.value = false;
    draftLoading.value = false;
  };
  // 表单校验规则
  const rules = ref({
    title: [{ required: true, message: t("banch.addHonorTip1") }],
    avatar: [{ required: true, message: t("banch.addHonorTip1") }],
    channel_type: [{ required: true, message: t("banch.addHonorTip1") }],
    obtain_date: [{ required: true, message: t("banch.addHonorTip1") }],
  });
  // 上传荣誉封面
  const uploadRef = ref(null);
  const avatarImages = ref([]);
  const { images, uploadImage: assetUrlUploadImage } = useImageUpload((res) => {
    formData.avatar = res.url;
    avatarImages.value = res.url;
    console.log(formData.avatar, res);
  });
  const triggerUpload = () => {
    console.log("triggerUpload上传主题图片------------");
    uploadRef.value?.triggerUpload();
  };
  const imageclick = (e) => {
    if (e.target.tagName == "IMG" || e.target.tagName == "INPUT") {
      triggerUpload();
    }
  };
  const avatarRemove = () => {
    formData.avatar = "";
  };
  // 选择图片校验
  const uploadCheck = (files) => {
    console.log(files);
    if (files?.[0]?.size > 2 * 1024 * 1024) {
      MessagePlugin.error(t("activity.activity.uploadTip10"));
      return false;
    }
    if (!imgType.includes(getFileType(files?.[0]?.name.toLowerCase()))) {
      MessagePlugin.error(t("activity.activity.uploadTip11"));
      return false;
    }
  };
  // 荣誉时间切换
  const startChange = (val) => {
    if (val) {
      console.log(val, "val----荣誉时间切换-----------");
    } else {
      console.log(
        val,
        "val----荣誉时间切换---------荣誉时间切换荣誉时间切换荣誉时间切换荣誉时间切换荣誉时间切换荣誉时间切换--",
      );
      formData.obtain_date = "";
    }
  };
  // 正文内容（富文本）
  const uploadImg = ref(null);
  const editorRef = ref(null);
  const noteData = ref({
    content: {
      images: [],
    },
    annex_size: 0,
    user_id: "",
    openid: "",
  });

  // 处理富文本中正文内容
  const handleContentChange = (contents) => {
    const content = {
      imageUrl: "",
      description: "",
      images: [],
      attachments: [],
      delta: "",
    };
    /**
     * 处理content里的数据
     * description： 所有文本信息加起来
     * imageUrl：第一张图片
     * images：所有的图片集合
     * attachments：所有的文件集合
     * delta：富文本内容
     */
    const delta = [];
    // 附件大小
    let size = 0;
    contents.forEach((v) => {
      let deltaItem = _.cloneDeepWith(v);
      if (typeof v.insert === "string") {
        content.description += v.insert.trim();
      }
      if (v.insert?.image) {
        !content.imageUrl && (content.imageUrl = v.insert.image);
        const imgItem = noteData.value.content.images.find((img) => img.url === v.insert.image);
        content.images.push(imgItem);
        imgItem?.size && (size += imgItem.size);
      }
      if (v.insert?.custom) {
        size += v.insert.custom.size;
        content.attachments.push(v.insert.custom);
        const atta = { attachment: JSON.stringify(v.insert.custom) };
        const custom = { custom: JSON.stringify(atta) };
        deltaItem.insert = custom;
      }
      delta.push(deltaItem);
    });
    content.delta = JSON.stringify(delta);
    console.log("content", content.description);
    formData.content_text = content.description;
    formData.content = content.delta;
    formData.id = null;
    console.log(formData.content, "formData.content=========formData.content");
    if (content.delta == '[{"insert":"\\n"}]') {
      formData.content = "";
    }
  };
  // 上传图片
  const { uploadImage: imagesInsert } = useImageUpload((res) => {
    console.log(res, "res----------------files-----------files");
    const { file, files } = res;
    if (files && files.length) {
      const imgObj = {
        name: file[0].name,
        size: file[0].size,
        type: file[0].type.split("/")[1],
        url: files[0].url,
      };
      noteData.value.content.images.push(imgObj);
    }
  });

  // 预览
  const previewVisible = ref(false);
  const preview = async () => {
    const valid = await honorForm.value.validate();
    if (typeof valid !== "boolean") {
      const confirmDia = DialogPlugin.confirm({
        header: "提示",
        body: "请填写完必填项后再预览",
        theme: "warning",
        cancelBtn: null,
        confirmBtn: "知道了",
        onConfirm: () => confirmDia.destroy(),
        onClose: () => confirmDia.destroy(),
      });
      return;
    }

    previewVisible.value = true;
  };

  // 点击发布
  const SecurityVerificationRef = ref(null);
  const activationGroupItem = ref({});
  const selectVisible = ref(false);
  const data = ref({});
  const draftLoading = ref(false); //存草稿的按钮loading
  const submitLoading = ref(false); //提交的按钮loading
  const onSubmit = ({ validateResult, firstError }) => {
    // 判断必填项是否完整
    if (validateResult === true) {
      submitLoading.value = true;
      console.log(formData, "点击了发布按钮-------------------");
      // 数美审核
      auditAPI({ params: JSON.stringify(formData) })
        .then((res) => {
          if (res.data.message == "success") {
            // 审核通过
            // if (honorStore.edithonor) { // 编辑
            //   formData.reviewer=0
            //   editHonorAPI(formData, localStorage.getItem('honorteamid')).then((res) => {
            //     console.log(res.data, '编辑接口----------data.valuedata.valuedata.valuedata.value------------');
            //     if (res.data.code==0) {
            //       MessagePlugin.success('发布成功');
            //       honorStore.listShresh=true
            //       honorStore.setcloseAddHonor(true);
            //       honorStore.setedithonor(false);
            //       honorStore.listShresh=true
            //     }
            //   }).catch((err) => {
            //     honorStore.setedithonor(false);
            //     MessagePlugin.error(err.message);
            //   });
            // } else { // 新增/再次发布
            submitLoading.value = false;
            activationGroupItem.value = JSON.parse(localStorage.getItem("honorteam"));

            if (!openFlag.value || openPersonl.value) {
              let tip = "";
              // 未开启安全验证
              if (!openFlag.value) {
                tip = "确定要发布当前荣誉吗？";
              } else if (openPersonl.value) {
                // 开启了安全验证-既是发布人也是审核人
                tip = "由于你是审核人，所以无需走安全验证，确定要发布当前荣誉吗？";
              }
              const confirmDia = DialogPlugin.confirm({
                header: "提示",
                body: tip,
                theme: "warning",
                onConfirm: () => {
                  confirmDia.destroy();
                  auditConfirm();
                },
                onClose: () => confirmDia.destroy(),
              });
            } else {
              selectVisible.value = true;
            }

            // if (openPersonl.value||!openFlag.value) { // 当前人有审核权限或者未开启安全认证弹出弹窗
            //    activationGroupItem.value=JSON.parse(localStorage.getItem('honorteam'));
            //    data.value=formData;
            //    data.value.team_id=localStorage.getItem('honorteamid');
            //    data.value.teamname=activationGroupItem.value.teamFullName;
            //    data.value.creator_name=activationGroupItem.value.staffName;
            //    nextTick(() => { SecurityVerificationRef.value.onOpen('approvalVisible'); });
            // } else { // 非审核人弹出选人弹窗
            //   selectVisible.value=true;
            //   console.log(activationGroupItem.value, 'activationGroupItem.value---------');
            // }
            // }
          }
        })
        .catch((err) => {
          submitLoading.value = false;
          if (err?.response?.status !== 418) {
            MessagePlugin.warning(err?.response?.data?.message);
          }
        });
    } else {
      MessagePlugin.warning(t("banch.addHonorTip"));
      console.log(firstError);
    }
  };
  // 是审核人或者没有安全验证的弹窗确认发布
  const auditConfirm = () => {
    // submitLoading.value=true
    formData.reviewer = activationGroupItem.value.staffId; // 自己的组织身份id
    console.log(
      formData,
      honorStore.honorItem.status,
      honorStore,
      "auditConfirm formData-----------------------------formData531531531535135153515355135",
    );
    if (honorStore.edithonor && honorStore.honorItem.status == 4) {
      const data = { ...formData, id: honorStore.honorItem.id }
      // 编辑
      editHonorAPI(data, localStorage.getItem("honorteamid"))
        .then((res) => {
          console.log(res.data, "编辑接口----------data.valuedata.valuedata.valuedata.value------------");
          if (res.data.code == 0) {
            MessagePlugin.success("发布成功");
            honorStore.listShresh = true;
            honorStore.setcloseAddHonor(true);
            honorStore.setedithonor(false);
            honorStore.listShresh = true;
            // submitLoading.value=false
          }
        })
        .catch((err) => {
          honorStore.setedithonor(false);
          MessagePlugin.error(err.message);
          // submitLoading.value=false
        });
    } else {
      if (honorStore.edithonor && honorStore.honorItem.status == 5) {
        //已发布内容撤回再发布要传
        formData.draft_id = honorStore.honorItem.id;
        // formData.id = null;
      }
      addHonorAPI(formData, localStorage.getItem("honorteamid"))
        .then((res) => {
          console.log(res.data, "发布接口----------data.valuedata.valuedata.valuedata.value------------");
          if (res.data.code == 0) {
            // submitLoading.value=false
            MessagePlugin.success("新增成功");
            honorStore.listShresh = true;
            honorStore.setcloseAddHonor(true);
          }
        })
        .catch((err) => {
          // submitLoading.value=false
          // MessagePlugin.error(err.message);
        });
    }
  };
  // 选择审核人的事件
  const selectConfirm = (list) => {
    // submitLoading.value=true
    console.log('selectConfirm data', honorStore);
    formData.reviewer = parseInt(list[0].cardId.substring(1));
    console.log(honorStore.honorItem.status, "honorStore.honorItem.status--------------honorStore.honorItem.status");
    if (honorStore.edithonor && honorStore.honorItem.status == 4) {
      const data = { ...formData, id: honorStore.honorItem.id }
      // 编辑
      editHonorAPI(data, localStorage.getItem("honorteamid"))
        .then((res) => {
          console.log(res.data, "编辑接口----------data.valuedata.valuedata.valuedata.value------------");
          if (res.data.code == 0) {
            MessagePlugin.success("发布成功");
            honorStore.listShresh = true;
            honorStore.setcloseAddHonor(true);
            honorStore.setedithonor(false);
            honorStore.listShresh = true;
            // submitLoading.value=false
          }
        })
        .catch((err) => {
          honorStore.setedithonor(false);
          MessagePlugin.error(err.message);
          // submitLoading.value=false
        });
    } else {
      if (honorStore.edithonor && honorStore.honorItem.status == 5) {
        //已发布内容撤回再发布要传
        formData.draft_id = honorStore.honorItem.id;
      }
      addHonorAPI(formData, localStorage.getItem("honorteamid"))
        .then((res) => {
          if (res.data.code == 0) {
            MessagePlugin.success("发布内容已提交审核人处理");
            honorStore.setcloseAddHonor(true);
            honorStore.listShresh = true;
            // submitLoading.value=false
          }
        })
        .catch((err) => {
          // submitLoading.value=false
          // MessagePlugin.error('err.message');
        });
    }
  };
  // 点击存草稿按钮
  const saveDraftFlag = ref(false);
  const saveDraft = () => {
    if (formData.title) {
      saveDraftFlag.value = true;
    } else {
      MessagePlugin.error("标题不能为空");
    }
  };
  const saveDraftConfirm = () => {
    console.log(formData, "点击了存草稿-----------------");
    draftLoading.value = true;
    auditAPI({ params: JSON.stringify(formData) })
      .then((res1) => {
        if (res1.data.message == "success") {
          // 审核通过
          draftHonorAPI({ ...formData, id: honorStore.honorItem.id }, localStorage.getItem("honorteamid"))
            .then((res) => {
              saveDraftFlag.value = false;
              honorStore.setcloseAddHonor(true);
              draftLoading.value = honorStore.listShresh = true;
              draftLoading.value = false;
              MessagePlugin.success(res.data.message);
              console.log(honorStore.closeAddHonor, "存草稿接口-----------------------");
            })
            .catch((err) => {
              draftLoading.value = false;
              // MessagePlugin.error(err.message);
            });
        }
      })
      .catch((err) => {
        draftLoading.value = false;
        MessagePlugin.warning("你输入的信息包含敏感内容，请修改后重试");
      });
    honorStore.saveDraftConfirm = false;
  };

  const debouncedSaveDraftConfirm = _.debounce(saveDraftConfirm, 1000);

  watch(
    () => formData.title,
    (val) => {
      if (val) {
        honorStore.saveDraftFlag = true;
      } else {
        honorStore.saveDraftFlag = false;
      }
      // console.log(useHonorStore.saveDraftFlag,'useHonorStore.saveDraftFlag-----useHonorStore.saveDraftFlag')
    },
  );
  watch(
    () => honorStore.saveDraftConfirm,
    (val) => {
      if (val) {
        saveDraftConfirm();
      }
    },
    { deep: true },
  );
  // 是否开启发布验证
  const openFlag = ref(false);
  const openPersonl = ref(false); // 当前人是否有审核权限
  const open = () => {
    auditHonorAPI(localStorage.getItem("honorteamid")).then((res) => {
      if (res.data.data.honor_status == 1) {
        openFlag.value = true;
      } else {
        openFlag.value = false;
      }
      if (res.data.data.honor_auth == 1) {
        openPersonl.value = true;
      } else {
        openPersonl.value = false;
      }
      // console.log(res.data.data,'//是否开启发布验证//是否开启发布验证//是否开启发布验证//是否开启发布验证//是否开启发布验证')
    });
  };
  //判断发布渠道是否开启的方法
  const sendTo = reactive({
    square: false,
    member: false,
    government: false,
    workshop: false,
    cbd: false,
    uni: false,
    association: false,
  });
  const sendToFlag = () => {
    getAppsState()
      .then((res) => {
        const data = res.data.data;
        Object.keys(sendTo).forEach((key) => {
          if (Object.prototype.hasOwnProperty.call(data, key)) {
            sendTo[key] = data[key];
          }
        });
        console.log(res.data.data, "判断发布渠道是否开启的方法---------------------------判断发布渠道是否开启的方法");
      })
      .catch((err) => {
        MessagePlugin.error(err.message);
      });
  };

  onActivated(() => {
    if (honorStore.honorItem.title) {
      // 回显
      formData.id = honorStore.honorItem.id;
      formData.title = honorStore.honorItem.title;
      formData.avatar = honorStore.honorItem.avatar;
      formData.obtain_date = honorStore.honorItem.obtain_date;
      formData.content = honorStore.honorItem.content;
      formData.channel_type = honorStore.honorItem.channel_type
        .filter((item) => item.is_selected)
        .map((item) => item.channel_type);
      avatarImages.value = [{ url: honorStore.honorItem.avatar }];
      editorRef.value.renderContent({ ops: JSON.parse(honorStore?.honorItem?.content || "[]") || [] });
      formData.team_id = localStorage.getItem("honorteamid");
      console.log("999-----", honorStore.honorItem?.content);
    }
    if (honorStore.detailHaved) {
      clearFormData();
    }
    if (honorStore.AddHonorItem.flag && honorStore.detailHaved) {
      formData.id = honorStore.AddHonorItem?.id;
      formData.title = honorStore.AddHonorItem?.title;
      formData.avatar = honorStore.AddHonorItem?.avatar;
      formData.obtain_date = honorStore.AddHonorItem?.obtain_date;
      formData.content = honorStore.AddHonorItem?.content;
      formData.channel_type = honorStore.AddHonorItem?.channel_type
        ?.filter((item) => item.is_selected)
        .map((item) => item.channel_type);
      avatarImages.value = [{ url: honorStore.AddHonorItem?.avatar }];
      editorRef.value.renderContent({ ops: JSON.parse(honorStore?.AddHonorItem?.content || "[]") || [] });
      formData.team_id = localStorage.getItem("honorteamid");
    }
    open();
    sendToFlag();
    // console.log(honorStore.detailHaved,honorStore.AddHonorItem, formData, 'honorItem----------------onDeactivated--------------------honorItem89898989');
  });
  onDeactivated(() => {
    honorStore.setAddHonorItem({ ...formData, flag: true });
    honorStore.setHonorItem({});
    console.log(honorStore.AddHonorItem, "onDeactivated----------------onDeactivated--------------------onDeactivated");
  });
  onMounted(() => {
    sendToFlag();
    clearFormData();
    console.log(
      formData.avatar,
      honorStore.honorItem,
      "honorItem-------------第一次挂在-----------------------honorItem",
    );
  });
</script>

<style lang="less" scoped>
  .addhonor {
    display: flex;
    width: 100%;
    padding: 16px 304px !important;
    // margin:16px auto;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    background: var(--bg-kyy_color_bg_light, #fff);
    overflow-y: auto;
  }

  :deep(.t-form) {
    margin: 0;
  }

  // 发布验证
  .chatBox {
    width: 608px;
    text-align: center;
    display: flex;
    padding: 8px 24px;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    background: var(--kyy_color_alert_bg_bule, #eaecff);
    color: var(--kyy_color_alert_text, #1a2139);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-bottom: 16px;

    // margin-top:-16px;
    /* 157.143% */
    .overHiddenName {
      max-width: calc(100% - 140px);
      padding-left: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .clickViewButton {
      height: 22px;
      padding: 0;
      color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff) !important;
      background-color: transparent !important;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;

      /* 157.143% */
      :deep(.t-button__suffix) {
        margin-left: 0;
      }
    }

    >img {
      width: 20px;
      height: 20px;
    }
  }

  // 表单
  .honorForm {
    width: 608px;

    :deep(.title) {
      .t-textarea__info_wrapper {
        display: none !important;
        // position: absolute;
        // right: 5px;
        // top: 50%;
        // top:17px;
        // transform: translate(0%,-50%);
      }

      .t-textarea__limit {
        font-size: 14px;
      }

      // .t-textarea__inner {
      //   min-height: 42px !important;
      //   // color: var(--text-kyy-color-text-1, #1A2139);
      //   text-overflow: ellipsis;

      //   /* kyy_fontSize_4/bold */
      //   font-family: PingFang SC;
      //   // font-size: 18px;
      //   font-style: normal;
      //   font-weight: 600;
      //   line-height: 26px;
      //   padding-right:54px;
      //   /* 144.444% */
      // }
    }

    // 荣誉封面
    .upload-box {
      cursor: pointer;
      display: flex;
      width: 128px;
      height: 96px;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      gap: 4px;
      border-radius: 8px;
      border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
      background: var(--kyy_color_upload_bg, #fff);
      color: var(--kyy_color_upload_text_default, #516082);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;

      /* 157.143% */
      img {
        width: 48px;
        height: 48px;
      }
    }

    .upload-box-tips {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
      gap: 8px;
      margin-top: 0;
      margin-left: 16px;
      color: var(--text-kyy-color-text-5, #acb3c0);

      /* kyy_fontSize_1/regular */
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;

      /* 166.667% */
      >div:nth-child(1) {
        height: 23px;
      }

      .t-button {
        border-radius: var(--radius-kyy-radius-button-s, 4px);
        border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
        color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
        text-align: center;

        /* kyy_fontSize_2/regular */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }
    }

    :deep(.avatarUpload) {
      display: flex;
      justify-content: space-between;

      .t-link {
        display: none !important;
      }

      .t-upload__tips {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;
        gap: 8px;
        margin-top: 0;
        margin-left: 16px;
        color: var(--text-kyy-color-text-5, #acb3c0);

        /* kyy_fontSize_1/regular */
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 166.667% */

        .t-button {
          border-radius: var(--radius-kyy-radius-button-s, 4px);
          border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
          color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
          text-align: center;

          /* kyy_fontSize_2/regular */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
        }
      }

      .t-upload__card-container {
        width: 128px;
        height: 96px;
        border-radius: 8px;
        border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
      }

      .t-upload__card-content {
        display: inline-block;
        width: 128px;
        height: 96px;
        border-radius: 8px;
        border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
        padding: 0;

        &:hover {
          cursor: pointer;

          &::before {
            background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.5));
            content: " ";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 2;
          }

          &::after {
            content: "更换封面";
            position: absolute;
            color: white;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 56px;
            z-index: 3;
            padding: 4px 8px;
            border-radius: 4px;
            background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.5));
          }
        }
      }
    }

    //富文本
    :deep(.QuillBox) {
      /* min-height: 303px; */

      .t-form__controls-content {
        flex-direction: column;
      }

      .upload-input {
        display: none;
      }

      .ql-toolbar {
        width: 100%;
        border-radius: 4px 4px 0 0;
        background: var(--bg-kyy-color-bg-deep, #f5f8fe);
        border: 1px solid var(--divider-kyy-color-divider-deep, #d5dbe4);
        border-bottom: none;
      }

      .ql-container {
        width: 100%;
        border-radius: 0 0 4px 4px;
        // border: 1px solid var(--divider-kyy-color-divider-deep, #D5DBE4);
        border-top: none;
      }
    }

    :deep(.t-form__label) {
      margin-bottom: 8px;
      line-height: 22px;
    }

    //發送至
    .sendTo {
      margin-bottom: 48px;
    }

    //底部按鈕
    .bottomButtons {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 64px;
      padding: 16px 0;
      align-items: flex-start;
      gap: 8px;
      align-self: stretch;
      background: #fff;
      box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.08);
      z-index: 1009;

      :deep(.t-button) {
        width: 88px;
      }

      :deep(.t-form__controls) {
        width: 608px;
        margin: auto;
      }
    }
  }

  :deep(.t-image--fit-fill) {
    object-fit: cover !important;
  }

  :deep(#lk-editor) {
    .ql-editor {
      padding: 12px;

      &::before {
        left: 12px;
      }
    }
  }
</style>
<style lang="less">
  .activity-lk-editors {
    width: 100%;
    /* min-height: 270px !important; */
    border: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4) !important;
    border-radius: 4px;

    .lk-toolbar {
      border-bottom: none !important;
      border-radius: 4px 4px 0 0 !important;
    }

    .ql-container {
      border-radius: 4px;
      border-top: none !important;
      // padding:0 12px;
      // text-indent: 12px;
    }
  }

  .addhonor .t-dialog {
    width: 480px;
    padding: 32px;
  }

  .avatarUpload .t-upload__card-mask {
    display: none;
  }
</style>