<script setup lang="ts">
import { provide, reactive, ref, watch, onMounted, onActivated, computed, onUnmounted } from 'vue';
import to from 'await-to-js';
import { getVideoOrImageText } from '@renderer/api/member/api/businessApi';
import { Label } from '@renderer/api/square/model-v2/label';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRoute } from 'vue-router';
import { getSquareInfo } from '@renderer/api/square/home';
import { getPostsByLabel } from '@renderer/api/square/label';
import { PostType } from '@renderer/api/forum/models/post';
import { getSquarePostList } from '@/api/square/home';
import Empty from '@/components/common/Empty.vue';
import { useSquareStore } from '@/views/square/store/square';
import PostDetail from '@/views/square/components/post/PostDetail.vue';

import PostPublishDialog from '@/views/square/components/post/PostPublishDialog.vue';
import LabelVideoSelector from '@/views/square/components/label/LabelVideoSelector.vue';

import { usePostTip } from '@/views/square/hooks/tip';
// import useNavigate from '@/views/square/hooks/navigate';
import { POST_IS_FROM_OUTER } from '@/views/square/constant';
import Card from '../components/Card.vue';
import CardSkeleton from '../components/CardSkeleton.vue';
import { ConfirmPayload } from '@/views/square/components/label/types';
import { usePromotion } from '../../components/post/hooks';
import PostPromotion from '@/views/square/post-promotion/components/PostPromotionDrawer.vue';
import AnnualFeeDialog from '@/views/square/components/annual-fee/AnnualFeeDialog.vue';

const emit = defineEmits(['loadData', 'refresh']);
const props = defineProps({
  cols: {
    type: String,
    default: '3',
  },
  type: {
    type: String,
    default: 'digital-platform',
  },
  tag: {
    type: Object,
    default: () => ({}),
  },
  channel_type: {
    type: String,
    default: '',
  },
  skeletonNum: {
    type: Number,
    default: 5,
  },
  emptyType: {
    type: Number,
    default: 2, // 默认为有发布视频按钮
  },
  isFromOuter: { // 除广场外 外部打开
    type: Boolean,
    default: false,
  },
  isShow: { // 已经在dom上 是否显示
    type: Boolean,
    default: true,
  },
});
const store = useSquareStore();
const route = useRoute();
const { checkInvisible } = usePostTip();

provide(POST_IS_FROM_OUTER, true);
// const { goHomePage } = useNavigate({ fromOuter: true });

const videoSelectProps = ref<{
  type?: 'add-video' | 'edit-label' | 'add-label';
  label?: Label;
}>({
  type: 'edit-label',
});
// 平台类型
type PlatformType = 'member' | 'government' | 'cbd' | 'association'| 'uni';
const type : PlatformType =props.channel_type === 'uni' ? 'uni' : props.channel_type === 'number' ? 'member' : (props.channel_type as PlatformType || 'association');
const param = reactive({
  page: 1,
  pageSize: 30,
});
const emptyType = ref(props.emptyType);
const dataList = ref([]);
const loading = ref(false);
const loaded = ref(false);
const isPublishVideo = ref(false);
const isAddVideo = ref(false);
const tag = ref(props.tag?.value);
const isFirst = ref(true);
const squareInfo = ref(null);
// 当前标签下，没有打上标签的视频
const noTagVideo = ref({});

// 是不是本人
const isSelf = computed(() => store.isSelfSquare(route.query.id));
const listRef = ref(0);
const pageWidth = ref(0);
const itemWidth = computed(() => {
  const moreW = props.cols - 1 * 16;
  const pageW = pageWidth.value - moreW;
  return pageW / props.cols;
});
const onResize = () => {
  pageWidth.value = listRef.value.offsetWidth;
};
watch(() => props.tag, (info) => {
  videoSelectProps.value.label = info.data;
  if (tag.value !== info.value) {
    tag.value = info.value;
    initData();
  }
}, {
  deep: true,
});

watch(() => props.isShow, (val) => {
  if (val) {
    setTimeout(() => {
      onResize();
    });
  }
});

let nextPageToken = '';

const initData = () => {
  param.page = 1;
  param.pageSize = 30;
  loaded.value = false;
  dataList.value = [];
  nextPageToken = '';
  getList();
};
// 加载列表
const getList = async () => {
  if (loaded.value || loading.value) return;
  loading.value = true;
  let res = null;
  let list = [];
  let total = 0;
  if (props.type === 'square') {
    // 广场
    res = await getSquarePostList({
      'page.size': param.pageSize,
      'page.next_page_token': nextPageToken,
      type: 'VIDEO',
      video_label_id: tag.value,
      square_id: route.query.id,
    });
    list = res.data?.posts;
    nextPageToken = res.data?.page?.nextPageToken;
    if (list.length <= 0) {
      checkPostsByLabel();
    }
  } else if (props.type === 'digital-platform') {
    // 数字平台
    res = await getVideoOrImageText({
      ...param,
      channel_type: type,
      is_video: 1,
    });
    list = res.data?.data?.list.map((v) => ({
      ...v.raw_json,
      stickOnTop: v.stick_on_top,
    }));
    total = res.data?.data?.total;

  }

  if (!res) {
    loading.value = false;
    console.log('error, res is null', res);
    return;
  }

  loading.value = false;
  isFirst.value = false;
  dataList.value = dataList.value.concat(list);
  emit('loadData', dataList.value);
  if (props.type === 'square') {
    loaded.value = !nextPageToken;
  } else {
    loaded.value = dataList.value.length >= total;
    param.page++;
  }
  onResize();
};
onActivated(() => {
  console.log('onActivated');
  initData();
});
onMounted(() => {
  // 广场需要调用
  if (!props.isFromOuter) {
    squareInfoApi();
  }
  window.addEventListener('resize', onResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', onResize);
});
// 查看详情
const detailId = ref('');
const detailIndex = ref(0);
const detailVisible = ref(false);
const detailArticleDirectly = ref(false);
const detailCardDirectly = ref(false);

type ClickOpts = { article?: boolean; card?: boolean; }

// watch(() => detailVisible.value, (show) => {
//   if (!show) {
//     // 广场需要调用
//     if (!props.isFromOuter) {
//       initData();
//     }
//   }
// });
const clickContent = async (item, index: number, opts?: ClickOpts) => {
  if (!store.isSelfSquare(item.post.squareId)) {
    const { invisible, deleted } = await checkInvisible(item.post.id, store.squareId);
    if (invisible || deleted) return;
  }

  detailVisible.value = true;
  detailId.value = item.post.id;
  detailIndex.value = index;

  const { article = false, card = false } = opts || {};
  detailArticleDirectly.value = article;
  detailCardDirectly.value = card;
};

const defaultToolbar = ref('');
const publishVideo = () => {
  isPublishVideo.value = true;
};
const addVideo = () => {
  isAddVideo.value = true;
};

const submitPost = () => {
  isAddVideo.value = false;
  initData();
};
// 标签新增/编辑、视频选择器回调
const onVideoSuccess = async (payload: ConfirmPayload) => {
  const { type } = payload;
  // if (type !== 'add-video') getList();
  isAddVideo.value = false;
  MessagePlugin.success(type === 'add-label' ? '请发布新视频，视频都已打上标签' : '保存成功');
  initData();
};
const toolbarClick = (type: string, post, index: number) => {
  defaultToolbar.value = type;

  // 点赞不进详情
  if (type === 'like') return;
  clickContent(post, index);
};

const squareInfoApi = async () => {
  let [err, res] = await to(getSquareInfo({ square_id: route.query.id }));
  if (!err) {
    console.log('getSquareInfo', res);
    squareInfo.value = res.data?.info?.square;
  }
};

// 查询标签列表
const checkPostsByLabel = async () => {
  const apiParams = {
    'page.size': 1,
    video_label_id: props.tag.value,
    square_id: route.query.id,
  };
  const [err, res] = await to(getPostsByLabel(apiParams));
  if (err) return;
  const list = res.data.posts;
  const info:any = noTagVideo.value;
  info[props.tag || 'all'] = !!((list && list.length > 0));
  noTagVideo.value = info;
};

const itemInfo = (item) => {
  if (!props.isFromOuter && squareInfo.value) {
    return {
      ...item,
      square: squareInfo.value,
    };
  }
  return item;
};

const {
  promotionVisible,
  promotionKey,
  annualFeeVisible,
  upgrade,
  openAnnualFeeDialog,
  openPromoting,
} = usePromotion(detailId);
</script>

<template>
  <div ref="listRef" class="list">
    <div
      v-infinite-scroll="getList"
      :infinite-scroll-immediate-check="true"
      :infinite-scroll-distance="300"
      :infinite-scroll-disabled="loaded"
      infinite-scroll-watch-disabled="loaded"
      class="page-container"
    >
      <div class="page-content">
        <div class="post-list">
          <wc-waterfall gap="16" :cols="props.cols">
            <template v-for="(item, index) in dataList">
              <div v-if="item.post.video" :key="item.post.id" class="post-item">
                <Card
                  :id="`id-${item.post.id}`"
                  :key="item.post.id"
                  :item-width="itemWidth"
                  :data="itemInfo(item)"
                  go-detail
                  in-list
                  @click-content="(e) => clickContent(e, index)"
                  @toolbar-change="(type) => toolbarClick(type, item, index)"
                />
              </div>
            </template>
            <!-- 骨架屏 -->
            <template v-if="loading && dataList.length <= 0">
              <CardSkeleton :numbers="props.skeletonNum" />
            </template>
          </wc-waterfall>
        </div>

        <div v-if="loaded" class="w-full">
          <t-divider v-if="dataList.length" class="my-0! px-80 color-text-2">{{ $t('components.infiniteLoading.noMoreData') }}</t-divider>
          <div v-else class="noData">
            <template v-if="emptyType === 1 || !isSelf">
              <Empty tip="这里空空如也" />
            </template>
            <template v-else-if="emptyType === 2 ">
              <Empty tip="空空如也，快来添点精彩吧！" />
              <t-button
                v-if="noTagVideo[props.tag]"
                class="publishVideo"
                @click="addVideo"
              >
                + 添加视频
              </t-button>
              <t-button
                v-else
                class="publishVideo"
                @click="publishVideo"
              >
                + 发布视频
              </t-button>
            </template>
          </div>
        </div>
        <div v-else-if="loading" class="text-center w-full">
          <t-loading v-if="loading" :text="$t('components.infiniteLoading.loading')" size="small" />
        </div>
        <t-back-top
          container=".page-container"
          style="position: fixed"
          size="small"
          shape="circle"
          :visible-height="50"
          :offset="['40px', '64px']"
        >
          <div class="back-top">
            <t-icon name="arrow-up" class="icon" />
          </div>
        </t-back-top>

        <PostDetail
          v-if="detailVisible"
          :id="detailId"
          v-model="detailVisible"
          window-type="dialog"
          :default-toolbar="defaultToolbar"
          :article-directly="detailArticleDirectly"
          :card-directly="detailCardDirectly"
          :from-outer="isFromOuter"
          @promoting="openPromoting"
        />
      </div>
    </div>
    <LabelVideoSelector
      v-if="isAddVideo"
      v-model="isAddVideo"
      v-bind="videoSelectProps"
      @confirm="onVideoSuccess"
    />
    <PostPublishDialog
      v-model="isPublishVideo"
      :mode="PostType.Video"
      @submit="submitPost"
    />

    <PostPromotion
      :id="detailId"
      :key="promotionKey"
      v-model="promotionVisible"
      @upgrade="openAnnualFeeDialog()"
      @buy="openAnnualFeeDialog(false)"
      @refresh="emit('refresh')"
    />

    <AnnualFeeDialog
      v-if="annualFeeVisible"
      v-model="annualFeeVisible"
      :square-id="store.squareId"
      :team-id="store.teamId"
      :z-index="1600"
      :upgrade="upgrade"
      @success="promotionKey++"
    />
  </div>
</template>

<style scoped lang="less">
.page-container {
  background: none;
  .noData{
    text-align: center;
  }
}

.scrollbar(6px);

.page-content {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  overflow: auto;
  padding-bottom: 16px;
}

.post-list {
  width: 100%;
  &::-webkit-scrollbar-thumb{
    display: none;
  }
}

:deep(.t-back-top) {
  width: 48px;
  height: 48px;
  background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.50));
  border: none;
}
.back-top {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  cursor: pointer;
  .icon {
    color: #fff;
    font-size: 32px;
  }
}
</style>
