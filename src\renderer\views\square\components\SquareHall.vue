<script setup lang="ts">
  import { computed, onMounted, ref, watch, getCurrentInstance, onBeforeMount } from "vue";
  import to from "await-to-js";
  import Empty from "@renderer/components/common/Empty.vue";
  import { getDiscoveryListNew, marketClassify, marketClassifyDiscovery } from "@renderer/api/contacts/api/organize";
  import BizDetail from "@renderer/views/square/niche/components/detail.vue";
  import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import { useRouter } from "vue-router";
  import { useTabsStore } from "@renderer/components/page-header/store";
  import FindAddressHistory from "./FindAddressHistory.vue";
  import SquareAvatar from "@/views/square/components/SquareAvatar.vue";
  import { getSquareList } from "@/api/square/square";
  import { Square } from "@/api/square/models/square";
  import LikeButton from "@/views/square/components/LikeButton.vue";
  import useNavigate from "@/views/square/hooks/navigate";
  import { i18n } from "@/i18n";
  import "@amap/amap-jsapi-types";
  import OpenSquare from "@/views/square/components/OpenSquare.vue";
  import { useBaiduMap } from "@renderer/components/common/map/hooks";
  import LynkerSDK from '@renderer/_jssdk';

  const { ipcRenderer } = LynkerSDK;
  const openSquareVisible = ref(false);
  const { goHomePage } = useNavigate();
  const list = ref < { fans: number; square: Square }[] > ([]);
  const discoveryList = ref < [] > ([]);
  const loading = ref(false);
  const infiniteLoading = ref(false); // 无限滚动加载状态
  let listCount = 100;
  let discoveryListCount = ref(100);
  const { proxy } = getCurrentInstance() as any;
  const t = i18n.global.t;
  const addressData = ref(null);
  const isMapPageFlag = ref(false);
  const title = ref(t("identity.squareNo"));
  const isSearch = ref(false);
  // HACK: md5 is not defined
  // https://github.com/yue1123/vue3-baidu-map-gl/issues/27#issuecomment-2219696323
  let moduleObject;
  onBeforeMount(() => {
    moduleObject = module;
    // eslint-disable-next-line no-global-assign
    (module as unknown) = undefined;
  });
  const onBMapInitdHandler = () => {
    // eslint-disable-next-line no-global-assign
    (module as unknown) = moduleObject;
  };
  let headList = ref([]);
  let listForm = ref({
    "page.size": 10,
    "page.number": 1,
    "lat_lng.latitude": "",
    "lat_lng.longitude": "",
    keyword: "",
    market_classify_id: "",
  });
  let discoveryListForm = ref({
    page: 1,
    pageSize: 10,
    point: "",
    title: "",
    longitude: "",
    latitude: "",
    classify_id: "",
  });

  const restlistForm = () => {
    list.value = [];
    listForm.value = {
      "page.size": 10,
      "page.number": 1,
      "lat_lng.latitude": "",
      "lat_lng.longitude": "",
      keyword: "",
      market_classify_id: "",
    };
    getList();
  };


  const restlistForm2 = () => {
    discoveryList.value = [];
    discoveryListForm.value = {
      page: 1,
      pageSize: 10,
      point: "",
      title: "",
      longitude: "",
      latitude: "",
      classify_id: "",
    };
    getDiscoveryListFn();
  };

  const scrollDisabled = computed(() => list.value.length >= listCount || infiniteLoading.value);
  const scrollDisabled1 = computed(() => discoveryList.value.length >= discoveryListCount.value || infiniteLoading.value);

  const tapsIndex = ref(0);
  const addressText = ref("");
  const city = ref("全国");

  // 地图 IP 定位
  const { location, markerInfo, onMapInit } = useBaiduMap({
    onIPLocation() {
      city.value = location.value.name;
    },
    onPointGeocoder() {
      getAllList();
    },
  })



  /*

  // 定位插件

  const GeoLocationPlugin = () =>

    new Promise((resolve) => {

      // https://lbs.amap.com/api/javascript-api/reference/location#m_AMap.Geolocation

      AMap.plugin("AMap.Geolocation", () => {

        resolve(

          new AMap.Geolocation({

            timeout: 10000, // 超过10秒后停止定位，默认：无穷大

            zoomToAccuracy: true, // 定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false

          }),

        );

        // reject();

      });

    });

  */

  const editAddressText = (text) => {
    // console.log('editAddressText');
    addressText.value = text;
  };

  /*

  // IP定位获取当前城市信息

  const getCityInfo = () =>

    GeoLocationPlugin().then(

      (plugin) =>

        new Promise((resolve, reject) => {

          plugin.getCityInfo((status, result) => {

            if (status === "complete") {

              resolve(result);

              return;

            }

            reject();

          });

        }),

    );

  const getAddress = (lnglat, cb?) => {

    setTimeout(() => {

      AMap.plugin("AMap.Geocoder", () => {

        const geocoder = new AMap.Geocoder({});

        geocoder.getAddress(lnglat, (status, result) => {

          if (status === "complete" && result.info === "OK") {

            cb?.(result);

          } else {

            cb?.(null);

          }

        });

      });

    });

  };

  */

  const getDiscoveryListFn = async () => {
    await network();
    if (errNetwork.value) {
      // MessagePlugin.error("网络链接失败，请检查网络后重试");
      return false;
    }
    const [err, resresDiscoveryList] = await to(getDiscoveryListNew({ ...discoveryListForm.value }));
    if (resresDiscoveryList) {
      discoveryListCount.value = resresDiscoveryList.data.data.total;
      discoveryList.value = resresDiscoveryList.data.data.list;
    }
    kindFlag.value = false;

    /*

    getCityInfo()

      .then(async (res) => {

        // console.log(res, "qqqqqqqqqqqqqq");

        city.value = res.city ? res.city : "全国";

        const [err, resresDiscoveryList] = await to(getDiscoveryListNew({ ...discoveryListForm.value }));

        if (resresDiscoveryList) {

          discoveryListCount.value = resresDiscoveryList.data.data.total;

          discoveryList.value = resresDiscoveryList.data.data.list;

        }

        kindFlag.value = false;

      })

      .catch(async (errs) => {

        console.log("errs", errs);

        const [err, resresDiscoveryList] = await to(getDiscoveryListNew({ ...discoveryListForm.value }));

        if (resresDiscoveryList) {

          discoveryListCount.value = resresDiscoveryList.data.data.total;

          discoveryList.value = resresDiscoveryList.data.data.list;

        }

        kindFlag.value = false;

    */

  };

  const getList = async () => {
    const [errData, resData] = await to(
      marketClassifyDiscovery({
        longitude: discoveryListForm.value.longitude,
        latitude: discoveryListForm.value.latitude,
      }),
    );
    if (resData) {
      headList.value = resData?.data?.data?.list || [];
      localStorage.setItem("marketClassifyTemp", JSON.stringify(resData?.data?.data?.list));
    } else if (errData?.code === "ERR_NETWORK") {
      errNetwork.value = true;
      const marketClassifyTemp = JSON.parse(localStorage.getItem("marketClassifyTemp") || "[]");
      headList.value = marketClassifyTemp;
    }

    const [err, resSquareList] = await to(getSquareList({ ...listForm.value }));
    if (resSquareList) {
      resSquareList.data.items.forEach((e) => {
        if (typeof e.distance === "number") {
          e.distance = e.distance > 999 ? `${(e.distance / 1000).toFixed(1)}km` : `${e.distance}m`;
        }
      });
      listCount = resSquareList.data.total;
      list.value = resSquareList.data.items;
      localStorage.setItem("resSquareTemp", JSON.stringify(list.value));
    } else if (err?.code === "ERR_NETWORK") {
      // listCount = resSquareList.data.total;
      errNetwork.value = true;
      const resSquareTemp = JSON.parse(localStorage.getItem("resSquareTemp") || "[]");
      list.value = resSquareTemp;
    }
    kindFlag.value = false;
  };



  // 加载更多广场号数据
  const getMoreList = async () => {
    const [err, resSquareList] = await to(getSquareList({ ...listForm.value }));

    if (resSquareList) {
      const newItems = resSquareList.data.items || [];

      // 处理距离显示
      newItems.forEach((e) => {
        if (typeof e.distance === "number") {
          e.distance = e.distance > 999 ? `${(e.distance / 1000).toFixed(1)}km` : `${e.distance}m`;
        }
      });

      listCount = resSquareList.data.total;

      // 追加新数据到现有列表
      list.value = [...list.value, ...newItems];
      localStorage.setItem("resSquareTemp", JSON.stringify(list.value));

    } else if (err?.code === "ERR_NETWORK") {
      errNetwork.value = true;
    }
  };



  // 加载更多商机数据
  const getMoreDiscoveryList = async () => {
    const [err, resDiscoveryList] = await to(getDiscoveryListNew({ ...discoveryListForm.value }));

    if (resDiscoveryList) {
      const newItems = resDiscoveryList.data.data.list || [];

      discoveryListCount.value = resDiscoveryList.data.data.total;

      // 追加新数据到现有列表
      discoveryList.value = [...discoveryList.value, ...newItems];

    } else if (err?.code === "ERR_NETWORK") {
      errNetwork.value = true;
    }
  };



  const errNetwork = ref(false);

  const getAllList = async (positionData?, name?) => {
    // console.log('getAllList');
    let res = null;
    if (positionData) {
      res = positionData;
    }
    /*
    else {
      try {
        res = await getCityInfo();
      } catch (error) {
        console.log(error);
        console.log("网络延迟error", error);
      }
    }
    */

    const { address, location } = markerInfo.value;
    let lat = location.lat;
    let lng = location.lng;
    if (res?.position?.length) {
      lng = res.position[0];
      lat = res.position[1];
    }

    listForm.value["lat_lng.longitude"] = lng;
    listForm.value["lat_lng.latitude"] = lat;
    addressData.value = [lng, lat];
    // listForm.value["lat_lng.longitude"] = res && res?.position && res.position[0] ? res.position[0] : "";
    // listForm.value["lat_lng.latitude"] = res && res?.position && res.position[1] ? res.position[1] : "";
    // addressData.value = res?.position;
    if (name) {
      addressText.value = name;
    } else {
      addressText.value = address || "珠海市政府";
      localStorage.setItem("addressTextTemp", addressText.value);
      /*
      getAddress(res?.position ? res?.position : "", (result) => {
        // console.log("11012", result);
        if (result && result.regeocode) {
          addressText.value = result.regeocode?.formattedAddress || "珠海市政府";
          localStorage.setItem("addressTextTemp", addressText.value);
        } else {
          const address = localStorage.getItem("addressTextTemp");
          addressText.value = address;
        }
      });
      */
    }
    // console.log('addressText.value', addressText.value);
    if (lng) {
      discoveryListForm.value.point = `${lng},${lat}`;
      discoveryListForm.value.longitude = lng;
      discoveryListForm.value.latitude = lat;
    }
    /*
    discoveryListForm.value.point = res && res.position ? res.position.join(",") : null;
    discoveryListForm.value.longitude = res && res?.position && res.position[0] ? res.position[0] : "";
    discoveryListForm.value.latitude = res && res?.position && res.position[1] ? res.position[1] : "";
    */

    const [errSquareList, resSquareList] = await to(getSquareList({ ...listForm.value }));
    // console.log("resSquareList", resSquareList);
    // console.log("errSquareList", errSquareList);

    // const resData = await marketClassify();
    const [errData, resData] = await to(
      marketClassifyDiscovery({
        longitude: discoveryListForm.value.longitude,
        latitude: discoveryListForm.value.latitude,
      }),
    );
    // console.log("resData", errData, resData);
    if (resData) {
      headList.value = resData?.data?.data?.list || [];
      localStorage.setItem("marketClassifyTemp", JSON.stringify(resData?.data?.data?.list));
    } else if (errData?.code === "ERR_NETWORK") {
      errNetwork.value = true;
      const marketClassifyTemp = JSON.parse(localStorage.getItem("marketClassifyTemp") || "[]");
      headList.value = marketClassifyTemp;
    }

    if (resSquareList) {
      resSquareList.data.items.forEach((e) => {
        if (typeof e.distance === "number") {
          e.distance = e.distance > 999 ? `${(e.distance / 1000).toFixed(1)}km` : `${e.distance}m`;
        }
      });
      listCount = resSquareList.data.total;
      list.value = resSquareList.data.items;
      localStorage.setItem("resSquareTemp", JSON.stringify(list.value));
    } else if (errSquareList?.code === "ERR_NETWORK") {
      // listCount = resSquareList.data.total;
      errNetwork.value = true;
      const resSquareTemp = JSON.parse(localStorage.getItem("resSquareTemp") || "[]");
      list.value = resSquareTemp;
    }
    // const [errDiscoveryList, resDiscoveryList] = await to(getDiscoveryListNew({ ...discoveryListForm.value }));
    // if (resDiscoveryList) {
    //   discoveryListCount.value = resDiscoveryList.data.data.total;
    //   discoveryList.value = resDiscoveryList.data.data.list;
    //   localStorage.setItem("discoveryListTemp", JSON.stringify(discoveryList.value));
    // } else if (errNetwork.value) {
    //   const discoveryListTemp = JSON.parse(localStorage.getItem("discoveryListTemp") || "[]");
    //   discoveryList.value = discoveryListTemp;
    // }
    loading.value = false;
  };



  const onOpenSquare = () => {

    if (errNetwork.value) {

      MessagePlugin.error("网络链接失败，请检查网络后重试");

      return false;

    }

    openSquareVisible.value = true;

  };



  const onGoDigitalPlatform = () => {

    // if (!url) return;

    let params = {

      from: "square",

      // redirect: url,

      // t: +new Date(),

      // jumpPath: '/workBenchIndex/member_manage',

    };



    // ipcRenderer.invoke("delect-memberWinBV");

    ipcRenderer

      .invoke("click-menu-item", {

        url: "/digitalPlatformIndex/digital_platform_home",

        reload: true,

        query: params,

      })

      .then((res) => {

        res && ipcRenderer.send("update-nume-index", "digital_platform");

      });

  };



  const goHomePageRun = (square) => {

    if (errNetwork.value) {

      MessagePlugin.error("网络链接失败，请检查网络后重试");

      return false;

    }

    goHomePage(square);

  };



  const onSearch = () => {
    // 重置分页参数
    if (tapsIndex.value === 0) {
      listForm.value["page.number"] = 1;
      list.value = [];
      getList();
    } else {
      discoveryListForm.value.page = 1;
      discoveryListForm.value.title = listForm.value.keyword;
      discoveryList.value = [];
      getDiscoveryListFn();
    }
  };

  let kindFlag = ref(false);

  const showItemKind = async (val) => {
    await network();
    if (errNetwork.value) {
      // MessagePlugin.error("网络链接失败，请检查网络后重试");
      return false;
    }

    list.value = [];
    kindFlag.value = true;

    // 重置分页参数
    listForm.value["page.number"] = 1;
    listForm.value.market_classify_id = val.id;
    title.value = val.alias;
    isSearch.value = true;

    getList();
  };



  const showItemKind2 = async (val) => {
    await network();
    if (errNetwork.value) {
      // MessagePlugin.error("网络链接失败，请检查网络后重试");
      return false;
    }

    discoveryList.value = [];
    kindFlag.value = true;

    // 重置分页参数
    discoveryListForm.value.page = 1;
    discoveryListForm.value.classify_id = val.id;
    title.value = val.alias;
    isSearch.value = true;

    getDiscoveryListFn();
  };

  const iconFilter = (item) => {

    const squareType = item.square.squareType;

    switch (squareType) {

      case "ENTERPRISE":

        return "iconenterprise";

      case "INDIVIDUAL_BUSINESS":

        return "iconindividual";

      case "GOVERNMENT":

        return "icongov";

      case "OTHER":

        return "iconcheckone";

      default:

        return "iconbusiness";

    }

  };

  const highlightText = (text, searchTerm) => {

    // 如果搜索词为空，则直接返回原始文本

    if (!searchTerm) {

      return text;

    }



    text = text.replace(/<[^>]+>/g, "");



    // 使用正则表达式进行全局不区分大小写的文本匹配

    const regex = new RegExp(searchTerm, "gi");



    // 使用 <span> 标签包裹匹配到的文本，并添加样式

    const highlightedText = text.replace(regex, (match) => `<span class="highlight">${match}</span>`);



    return highlightedText;

  };

  const handleInfiniteOnLoad = async () => {
    // 防止并发请求
    if (infiniteLoading.value) {
      return;
    }

    // 异步加载数据等逻辑
    if (tapsIndex.value == 0) {
      if (scrollDisabled.value) {
        // 数据加载完毕
        return;
      } else {
        // 设置加载状态
        infiniteLoading.value = true;
        try {
          // 加载更多数据 - 页码加1
          listForm.value["page.number"] += 1;
          await getMoreList();
        } finally {
          // 无论成功失败都要重置加载状态
          infiniteLoading.value = false;
        }
      }
    }

    if (tapsIndex.value == 1) {
      if (scrollDisabled1.value) {
        return;
      } else {
        // 设置加载状态
        infiniteLoading.value = true;
        try {
          // 加载更多数据 - 页码加1
          discoveryListForm.value.page += 1;
          await getMoreDiscoveryList();
        } finally {
          // 无论成功失败都要重置加载状态
          infiniteLoading.value = false;
        }
      }
    }
  };







  const showAllKinds = () => {

    title.value = t("clouddisk.allKind");

  };



  const bizDetailReadonlyRef = ref(null);

  const bizOpportunityDetailVisible = ref(false);

  const router = useRouter();

  const tabStore = useTabsStore();

  const goDetailOnlyRead = async (item) => {

    await network();

    if (errNetwork.value) {

      // MessagePlugin.error("网络链接失败，请检查网络后重试");

      return false;

    }

    // console.log(item, "打印出什么");

    // bizOpportunityDetailVisible.value = true;

    // bizDetailReadonlyRef.value.detailsOpen(item.uuid);

    const fullPath = `/square/niche/nicheDetailReadOnly?uuid=${item.uuid}&from=square`;

    tabStore.addTab({ label: item.title, fullPath });

    router.push(fullPath);

  };

  const editIsMapPageFlag = (val, name?) => {

    if (val) {

      getAllList(

        {

          position: [val.location.lng, val.location.lat],

        },

        name,

      );

    }



    isMapPageFlag.value = false;

  };

  const editAddressData = (data) => {

    addressData.value = data;

  };



  const shareSpecialRun = (data) => {

    const id = data.id;

    const row = listData.value.find((item) => item.id === id);

    // console.log(row);

    shareRef.value.shareOpen(row);

  };



  const init = () => {

    title.value = t("identity.squareNo");

    isSearch.value = false;

    list.value = [];

    const longtemp = discoveryListForm.value.longitude;

    const lattemp = discoveryListForm.value.latitude;

    listForm.value = {

      "page.size": 10,

      "page.number": 1,

      "lat_lng.latitude": "",

      "lat_lng.longitude": "",

      keyword: "",

      market_classify_id: "",

    };

    discoveryList.value = [];

    discoveryListForm.value = {

      page: 1,

      pageSize: 10,

      point: "",

      title: "",

      longitude: "",

      latitude: "",

      classify_id: "",

    };

    getAllList({ position: [longtemp, lattemp] }, addressText.value);

  };



  const lineBodyRef = ref(null);

  const backTopRun = () => {

    lineBodyRef.value.scrollTo({ top: 0, behavior: "smooth" });

  };

  const scrollTopVal = ref(0);

  const handleScroll = (e) => {
    scrollTopVal.value = e.target.scrollTop;

    // 检查是否接近底部并触发无限滚动
    const distanceToBottom = e.target.scrollHeight - e.target.scrollTop - e.target.clientHeight;

    // 当距离底部小于等于20px且没有被禁用且没有正在加载时，触发无限滚动
    if (distanceToBottom <= 20 && !scrollDisabled.value && !infiniteLoading.value) {
      handleInfiniteOnLoad();
    }
  };



  const showmap = async () => {

    await network();

    if (errNetwork.value) {

      // MessagePlugin.error("网络链接失败，请检查网络后重试");

      return false;

    }

    isMapPageFlag.value = true;

  };



  const network = async () => {

    const [errData, resData] = await to(

      marketClassifyDiscovery({

        longitude: discoveryListForm.value.longitude,

        latitude: discoveryListForm.value.latitude,

      }),

    );

    if (errData?.code === "ERR_NETWORK") {

      errNetwork.value = true;

    } else {

      errNetwork.value = false;

    }

  };

</script>



<template>

  <div v-show="!isMapPageFlag" style="height: 100%">

    <div class="header" :class="isSearch ? 'p-t16l24' : ''">

      <div class="flex-a">

        <iconpark-icon v-if="title !== t('identity.squareNo')" style="cursor: pointer; padding-right: 4px"
          name="iconarrowlift" @click="init"></iconpark-icon>

        <div v-if="title === t('identity.squareNo')" class="title"
          :style="{ fontSize: title === t('identity.squareNo') ? '18px ' : '16px' }"
          :class="{ activeTitleBar: tapsIndex === 1, rem4: title.length === 4 }" @click="tapsIndex = 0">

          {{ t("identity.squareNo") }}

        </div>

        <div v-if="isSearch || title === t('clouddisk.allKind')" class="searchTitle">

          {{ title }}

        </div>

        <!-- <div

          v-if="title === t('identity.squareNo')"

          class="title"

          style="margin-left: 24px"

          :class="tapsIndex === 0 ? 'activeTitleBar' : ''"

          @click="tapsIndex = 1"

        >

          {{ t("application.niche") }}

        </div> -->

      </div>

      <iconpark-icon v-if="title === t('identity.squareNo')" name="iconsearch" class="icon-search"
        @click="(title = '搜索'), (isSearch = true)"></iconpark-icon>

      <!-- <iconpark-icon

        v-if="title !== t('identity.squareNo')"

        style="cursor: pointer; padding-right: 4px"

        name="iconarrowlift"

        @click="(title = t('identity.squareNo')), (isSearch = false), restlistForm(), restlistForm2()"

      ></iconpark-icon> -->

      <!-- <div

        v-if="title === t('identity.squareNo')"

        class="title"

        :style="{ fontSize: title === t('identity.squareNo') ? '18px ' : '16px' }"

        :class="{ activeTitleBar: tapsIndex === 1, rem4: title.length === 4 }"

        @click="tapsIndex = 0"

      >

        {{ t('identity.squareNo') }}111

      </div>

      <div

        v-if="isSearch"

        class="searchTitle"

      >

        {{ title }}

      </div>

      <div

        v-if="title === t('identity.squareNo')"

        name="iconsearch"

        class="icon-search"

        @click="(title = '搜索'), (isSearch = true)"

      ></div> -->

    </div>



    <!-- 搜索 -->

    <t-input v-if="isSearch" v-model="listForm.keyword" :maxlength="50" :placeholder="$t('square.search')"
      style="width: 312px; margin: 0 auto 16px" @enter="onSearch">

      <template #prefix-icon>

        <iconpark-icon name="iconsearch" />

      </template>

    </t-input>

    <div v-if="(tapsIndex === 0 && list.length > 0) || (tapsIndex === 1 && discoveryList.length > 0)"
      class="positioning-text" :class="isSearch ? 'plr24' : ''" @click="showmap">

      <iconpark-icon name="iconpositioning"></iconpark-icon>

      <t-tooltip :content="addressText" placement="bottom">

        <span>{{ addressText }}</span>

      </t-tooltip>

      <iconpark-icon name="iconarrowdwon"></iconpark-icon>

    </div>

    <!-- 分类 -->

    <!--  -->

    <div v-if="(tapsIndex === 0 && list.length > 0) || (tapsIndex === 1 && discoveryList.length > 0)" ref="lineBodyRef"
      class="head-menu-wrap-box" :style="{

        height:

          title === t('identity.squareNo')

            ? 'calc( 100% - 112px ) '

            : title === t('clouddisk.allKind')

            ? 'calc( 100% - 108px )'

            : 'calc( 100% - 140px )',

        overflow: 'overlay',

      }" :class="isSearch ? 'mt0' : ''" @scroll="handleScroll" v-infinite-scroll="handleInfiniteOnLoad"
      :infinite-scroll-immediate-check="false" :infinite-scroll-distance="50"
      :infinite-scroll-disabled="scrollDisabled">

      <!-- 这是广场号-->

      <div v-if="tapsIndex == 0" v-show="title === t('identity.squareNo') || title === t('clouddisk.allKind')"
        class="head-menu-wrap">

        <div v-for="item in headList.length < 10 || title === t('clouddisk.allKind') ? headList : headList.slice(0, 9)"
          :key="item.alias" class="head-menu" @click="showItemKind(item)">

          <div class="head-menu-img-box">

            <img :src="item.icon" />

          </div>

          <div class="head-menu-text">{{ item.alias }}</div>

        </div>

        <div v-if="headList.length > 9 && title !== t('clouddisk.allKind')" class="head-menu" @click="showAllKinds">

          <div class="head-menu-img-box">

            <img src="@/assets/alls.svg" />

          </div>

          <div class="head-menu-text">全部</div>

        </div>

      </div>



      <div v-if="tapsIndex == 1" v-show="title === t('identity.squareNo') || title === t('clouddisk.allKind')"
        class="head-menu-wrap">

        <div v-for="item in headList.length < 10 || title === t('clouddisk.allKind') ? headList : headList.slice(0, 9)"
          :key="item.alias" class="head-menu" @click="showItemKind2(item)">

          <div class="head-menu-img-box">

            <img :src="item.icon" />

          </div>

          <div class="head-menu-text">{{ item.alias }}</div>

        </div>

        <div v-if="headList.length > 9 && title !== t('clouddisk.allKind')" class="head-menu" @click="showAllKinds">

          <div class="head-menu-img-box">

            <img src="@/assets/alls.svg" />

          </div>

          <div class="head-menu-text">全部</div>

        </div>

      </div>

      <div v-show="tapsIndex == 1 && title !== t('clouddisk.allKind')" class="advert cursor"
        @click="onGoDigitalPlatform">

        <img v-if="proxy.$i18n.locale === 'zh-cn'" src="@/assets/digital/svg/square_digital_cn.svg" />

        <img v-else src="@/assets/digital/svg/square_digital_mo.svg" />

      </div>

      <!-- 这是商机-->

      <div v-show="tapsIndex == 1" class="head-menu-wrap-box" style="display: flex; flex-direction: column">

        <div v-if="title !== t('clouddisk.allKind') && discoveryList.length" v-loading="loading"
          v-infinite-scroll="handleInfiniteOnLoad" class="list-wrap" :class="isSearch ? 'border-0' : ''"
          :infinite-scroll-immediate-check="false" :infinite-scroll-distance="50"
          :infinite-scroll-disabled="scrollDisabled1">

          <div v-for="(item, index) in discoveryList" :key="index" class="item" @click="goDetailOnlyRead(item)">

            <!--          <SquareAvatar-->

            <!--            v-if="item?.goods_image.length"-->

            <!--            :square="item?.goods_image[0]"-->

            <!--            size="44px"-->

            <!--            class="avatar"-->

            <!--            :symbol="false"-->

            <!--          />-->

            <img v-if="item?.images?.length" style="

                width: 44px;

                border-radius: 4px;

                height: 44px;

                object-fit: cover;

                object-position: center;

                margin-bottom: 15px;

              " :src="item?.images[0]?.file_name" alt="" />

            <img v-else class="avatar" style="width: 44px; border-radius: 4px" src="@/assets/business/Rectangle.png"
              alt="" />



            <div class="right-content">

              <div class="flex-a">

                <div v-if="item.is_top === 1" class="tags-pzy tag3">置顶</div>

                <div v-if="item.type == 1" class="tags-pzy tags2">{{ t("niche.gy") }}</div>

                <div v-if="item.type == 2" class="tags-pzy tags1">{{ t("niche.xq") }}</div>

                <div class="squareTitle1" v-html="highlightText(item.title, discoveryListForm.title)"></div>

              </div>

              <div class="flex-a-js" style="position: relative">

                <div class="flex-a" style="width: 100%">

                  <div v-if="item?.classify.length > 0" class="class-type line-1" style="text-align: left">

                    {{ item?.classify[0]?.alias

                    }}<span v-if="item?.classify[1]?.alias">/{{ item?.classify[1]?.alias }}</span>

                  </div>

                  <div class="distance line-1"
                    style="color: #828da5; flex: 1; border-left: 1px solid #eceff5 !important"
                    :class="item.lastMarketClassifyName ? '' : 'hideboder'">

                    <img src="@/assets/svg/icon_local.svg" />

                    <div class="line-1">{{ item.address }}</div>

                  </div>

                </div>

              </div>

            </div>

          </div>

          <!-- <div v-show="scrollTopVal > 240" class="fixed-box">

            <div class="up curun" @click="backTopRun">

              <t-icon name="arrow-up" class="icon" />

            </div>

          </div> -->



          <t-back-top container=".head-menu-wrap-box" shape="circle" size="small" :visible-height="240"
            :offset="['32px', '40px']" style="position: fixed">

            <div class="back-top">

              <t-icon name="arrow-up" class="icon" />

            </div>

          </t-back-top>

        </div>

      </div>



      <div v-show="tapsIndex == 0 && title !== t('clouddisk.allKind')" class="advert cursor" @click="onOpenSquare">

        <img src="@renderer/assets/svg/member/friends.svg" />

      </div>



      <!-- 这是广场号-->

      <div v-if="title !== t('clouddisk.allKind') && list.length && tapsIndex == 0" v-loading="loading"
        class="list-wrap" :class="isSearch ? 'border-0' : ''">

        <div v-for="(item, index) in list" :key="item.square.name" class="item" @click="goHomePageRun(item.square)">

          <SquareAvatar :square="item.square" size="44px" class="avatar" :symbol="false" />

          <div class="right-content">

            <div class="flex-a">

              <iconpark-icon class="iconiconpark" :name="iconFilter(item)"></iconpark-icon>

              <div class="squareTitle" v-html="highlightText(item.square.name, listForm.keyword)"></div>

            </div>

            <div class="flex-a-js" style="position: relative">

              <div class="flex-a">

                <div v-if="item.lastMarketClassifyName" class="tags"
                  v-html="highlightText(item.lastMarketClassifyName, listForm.keyword)"></div>

                <!-- {{ item.lastMarketClassifyName }} -->

                <div class="distance" :class="item.lastMarketClassifyName ? '' : 'hideboder'">

                  <img src="@/assets/svg/icon_local.svg" />

                  <span>{{ item.distance }}</span>

                </div>

              </div>

              <LikeButton v-model="item.followed" style="height: 20px; position: absolute; top: -10px; right: 0"
                :be-followed="item.beFollowed" :square-id="item.square.squareId" :square-name="item.square.name"
                simple />

            </div>

          </div>

        </div>

        <!-- <div v-show="scrollTopVal > 240" class="fixed-box">

          <div class="up curun" @click="backTopRun">

            <iconpark-icon name="icontotop" style="font-size: 32px;">

            </iconpark-icon></div>

        </div> -->

        <t-back-top container=".head-menu-wrap-box" shape="circle" size="small" :visible-height="240"
          :offset="['32px', '40px']" style="position: fixed">

          <div class="back-top">

            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">

              <g clip-path="url(#clip0_352_1824)">

                <path d="M9.53516 3.33301L9.53516 19.3787" stroke="white" stroke-width="2" stroke-linecap="round" />

                <path d="M16.9996 8.68615L9.53516 1.22168L2.07069 8.68615" stroke="white" stroke-width="2"
                  stroke-linecap="round" />

              </g>

              <defs>

                <clipPath id="clip0_352_1824">

                  <rect width="20" height="20" fill="white" />

                </clipPath>

              </defs>

            </svg>

          </div>

        </t-back-top>

      </div>

    </div>

    <div v-if="!kindFlag && !list.length && title !== t('clouddisk.allKind') && title !== t('identity.squareNo')">

      <Empty name="no-result" />

    </div>



    <BMap :height="0" @initd="onMapInit" />

    <!--    商机缺省也-->

    <!-- <div

      v-if="!kindFlag && !discoveryList.length && title !== t('clouddisk.allKind') && title !== t('identity.squareNo')"

    >

      <Empty name="no-result" />

    </div> -->

  </div>

  <BizDetail ref="bizDetailReadonlyRef" @share="shareSpecialRun" />

  <FindAddressHistory v-if="isMapPageFlag" :city="city" :address-text="addressText" :address-data="addressData"
    @edit-is-map-page-flag="editIsMapPageFlag" @edit-address-data="editAddressData"
    @edit-address-text="editAddressText" />

  <OpenSquare v-if="openSquareVisible" v-model="openSquareVisible" @success="openSquareVisible = false" />

</template>



<style scoped lang="less">
  .hideboder {

    border: none !important;

  }



  .like-icon:hover {

    color: #707eff;

    font-size: 20px;

  }



  .like-icon {

    font-size: 20px !important;

  }



  .flex-a-js {

    display: flex;

    align-items: center;

    justify-content: space-between;

  }



  .tags {

    display: flex;

    max-width: 120px;

    overflow: hidden;

    text-overflow: ellipsis;

    white-space: nowrap;

    margin-top: 5px;

    width: max-content;

    justify-content: center;

    align-items: center;

    text-align: center;

    margin-right: 8px;

    font-style: normal;



    color: var(--text-kyy_color_text_3, #828da5);

    font-size: 14px;

    font-style: normal;

    font-weight: 400;

    line-height: 22px;

    /* 157.143% */

  }



  .tags-pzy {

    display: flex;

    max-width: 120px;

    overflow: hidden;

    text-overflow: ellipsis;

    white-space: nowrap;

    //margin-top: 5px;

    width: max-content;

    justify-content: center;

    align-items: center;

    text-align: center;

    margin-right: 4px;

    font-style: normal;



    color: var(--text-kyy_color_text_3, #828da5);

    font-size: 12px;

    font-style: normal;

    font-weight: 400;

    line-height: 22px;

    /* 157.143% */

  }



  .tags1 {

    color: var(--kyy_color_tag_text_warning, #fc7c14);

    border-radius: var(--kyy_radius_tag_s, 4px);

    background: var(--kyy_color_tag_bg_warning, #ffe5d1);

    padding: 0 4px;

  }



  .tag3 {

    display: flex;

    height: 20px;

    min-height: 20px;

    max-height: 20px;

    padding: 2px 4px;

    justify-content: center;

    align-items: center;

    gap: 4px;

    border-radius: var(--kyy_radius_tag_s, 4px);

    background: var(--kyy_color_tag_bg_brand, #eaecff);

    color: var(--kyy_color_tag_text_brand, #4d5eff);

    text-align: center;

    font-family: "PingFang SC";

    font-size: 12px;

    font-style: normal;

    font-weight: 400;

    line-height: 20px;

    /* 166.667% */

    margin-right: 4px;

    float: left;

  }



  .tags2 {

    display: flex;

    height: 20px;

    flex-shrink: 0;

    padding: 2px 4px;

    justify-content: center;

    align-items: center;

    margin-right: 4px;

    border-radius: var(--kyy_radius_tag_s, 4px);

    background: var(--kyy_color_tag_bg_brand, #eaecff);

    color: var(--kyy_color_tag_text_brand, #4d5eff);

    text-align: center;

    float: left;

    border-radius: var(--kyy_radius_tag_s, 4px);

    background: var(--kyy_color_tag_bg_cyan, #e6f9f8);

    color: var(--kyy_color_tag_text_cyan, #11bdb2);

    text-align: center;



    /* kyy_fontSize_1/regular */

    font-family: "PingFang SC";

    font-size: 12px;

    font-style: normal;

    font-weight: 400;

    line-height: 20px;

    /* 166.667% */

  }



  // .head-menu:nth-child(4) {

  //   margin-right: 0px !important;

  // }

  .icon-search {

    width: 20px;

    height: 20px;

    color: #516082;

    cursor: pointer;

  }



  // .head-menu:nth-child(8) {

  //   margin-right: 0px !important;

  // }

  .head-menu:nth-child(5n) {

    margin-right: 0px !important;

  }



  .head-menu-wrap {

    display: flex;

    max-height: 168px;

    justify-content: flex-start;

    flex-wrap: wrap;

    margin: 0 16px 0;

    z-index: 8;



    .head-menu {

      cursor: pointer;

      height: 70px;

      margin-right: 11px;

      margin-bottom: 16px;

    }



    .head-menu-img-box {

      width: 44px;

      height: 44px;

      border-radius: 12px;

      overflow: hidden;

      margin: 0 auto;



      img {

        width: 44px;

        height: 44px;

      }

    }



    .head-menu-text {

      color: var(--text-kyy-color-text-1, #1a2139);

      font-size: 14px;

      font-style: normal;

      text-align: center;

      width: 56px;

      font-weight: 400;

      margin-top: 4px;

    }

  }



  .header {

    display: flex;

    padding: 16px;

    justify-content: space-between;

    align-items: center;

    align-self: stretch;



    .title {

      color: var(--text-kyy-color-text-1, #1a2139);

      font-size: 18px;

      font-weight: 600;

      cursor: pointer;

      line-height: 26px;

      position: relative;

    }



    .searchTitle {

      color: var(--text-kyy-color-text-1, #1a2139);

      font-size: 16px;

      font-weight: 600;

      cursor: pointer;

      line-height: 26px;

      position: relative;

    }



    .rem4::after {

      left: 22px !important;

    }



    .title::after {

      content: "";

      position: absolute;

      bottom: -8px;

      left: 50%;

      transform: translateX(-50%);

      width: 20px;

      height: 3px;

      border-radius: 2px;

      background: var(--icon-kyy-color-icon-deepest, #1a2139);

    }



    .icon {

      color: var(--icon-kyy-color-icon-deep, "#516082");

      font-size: 20px;

    }

  }



  .head-menu-wrap-box {

    display: flex;

    flex-direction: column;

    /* overflow: auto; */

    margin-top: 12px;

    /* height: calc(100% - 105px); */

  }



  .border-0 {

    border-top: none !important;

  }



  .list-wrap {

    /* flex: 1;

  overflow: auto; */

    border-top: 1px solid #eceff5;

    margin: 0px 16px 0;

    display: flex;

    flex-direction: column;

    position: relative;



    .fixed-box {

      position: fixed;

      right: 10px;

      bottom: 28px;



      .curun {

        font-size: 20px;

        display: flex;

        width: 48px;

        height: 48px;

        padding: 14px;

        justify-content: center;

        align-items: center;

        flex-shrink: 0;

        color: #fff;

        cursor: pointer;

      }



      .add {

        font-size: 24px;

        border-radius: 100px;

        background: var(--brand-kyy-color-brand-default, #4d5eff);



        /* kyy_shadow_m */

        box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);

      }



      .up {

        border-radius: 100px;

        background: var(--divider-kyy-color-divider-deep, #d5dbe4);

      }

    }



    .item {

      display: flex;

      align-items: center;

      padding-top: 12px;



      align-self: stretch;

      cursor: pointer;

      content-visibility: auto;

      contain-intrinsic-size: 51px;

    }



    .avatar {

      flex-shrink: 0;



      padding-bottom: 15px;

    }



    .right-content {

      flex: 1;

      width: 0;

      margin-left: 12px;

      border-bottom: 1px solid #eceff5;

      padding-bottom: 12px;



      .name {

        color: var(--text-kyy-color-text-1, #1a2139);

        font-size: 16px;

        font-weight: 600;

        line-height: 24px;

      }



      .fans {

        color: var(--text-kyy-color-text-3, #828da5);

        font-size: 14px;

        line-height: 22px;

      }

    }

  }



  .activeTitleBar::after {

    display: none !important;

  }



  :deep(.highlight) {

    color: #4d5eff;

  }



  .business-box {

    //background-size: cover;

    //background-image: url('@/assets/svg/Vector.svg')  ;

    width: 100%;

    //height: 100%;

    position: relative;

    background-repeat: no-repeat;

    text-align: center;



    padding: 12px 16px;



    .business-box-list {

      display: flex;

      column-gap: 10px;

      row-gap: 16px;

      flex-wrap: wrap;



      .business-box-item {

        width: 56px;



        .img {

          width: 44px;

          height: 44px;

          margin-bottom: 4px;

        }

      }

    }



    .divider-8 {

      display: flex;

      min-height: 1px;

      max-height: 1px;

      padding: 0px 16px;

      flex-direction: column;

      align-items: flex-start;

      gap: 4px;

      align-self: stretch;

      background: #eceff5;

      margin: 12px 0;

    }

  }



  .mt0 {

    margin-top: 0 !important;

  }



  .p-t16l24 {

    padding: 16px 24px !important;

  }



  .plr24 {

    padding-left: 24px !important;

    padding-right: 24px !important;

  }



  .class-type {

    color: var(--text-kyy_color_text_3, #828da5);

    text-align: center;

    /* kyy_fontSize_1/regular */

    font-family: PingFang SC;

    font-size: 14px;

    font-style: normal;

    font-weight: 400;

    line-height: 20px;

    /* 166.667% */

    margin-right: 8px;

    margin-top: 5px;

    //min-width: 92px;

    max-width: 92px;

  }



  .pagination-wrap {

    display: flex;

    justify-content: center;

    align-items: center;

    gap: 8px;

    align-self: stretch;

    color: var(--text-kyy-color-text-3, #828da5);

    font-size: 14px;

    line-height: 22px;

  }



  .positioning-text {

    cursor: pointer;

    display: flex;

    align-items: center;

    color: var(--text-kyy_color_text_1, #1a2139);

    font-feature-settings: "clig" off, "liga" off;

    font-size: 14px;

    font-style: normal;



    padding: 0 16px 8px;

    font-weight: 400;

    line-height: 22px;



    /* 157.143% */

    span {

      margin-left: 4px;

      white-space: nowrap;

      text-overflow: ellipsis;

      overflow: hidden;

      word-break: break-all;

    }

  }



  .flex-a {

    display: flex;

    align-items: center;

  }



  .distance {

    display: flex;

    align-items: center;

    margin-top: 5px;

    padding-left: 8px;

    border-left: 1px solid #eceff5;



    img {

      padding-right: 4px;

    }



    span {

      color: var(--text-kyy_color_text_3, #828da5);

      font-size: 14px;

      font-style: normal;

      font-weight: 400;

      line-height: 22px;

      /* 157.143% */

    }

  }



  .activeTitleBar {

    color: #000 !important;

    font-size: 16px !important;

    font-weight: 400 !important;

    line-height: 24px;

    /* 150% */

  }



  .iconiconpark {

    width: 24px;

    height: 24px;

    font-size: 24px;

    margin-right: 4px;

  }



  .squareTitle {

    overflow: hidden;

    color: var(--text-kyy-color-text-1, #1a2139);

    text-overflow: ellipsis;

    white-space: nowrap;

    font-size: 16px;

    font-style: normal;

    font-weight: 600;

    line-height: 24px;

    /* 150% */

    max-width: 10rem;

  }



  .squareTitle1 {

    overflow: hidden;

    color: var(--text-kyy-color-text-1, #1a2139);

    text-overflow: ellipsis;

    white-space: nowrap;

    font-size: 16px;

    font-style: normal;

    font-weight: 600;

    line-height: 24px;

    /* 150% */

    width: 100%;

  }



  .advert {

    padding: 0 16px;

  }



  :deep(.t-back-top) {

    background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.5)) !important;

    border: none;

    box-shadow: none;

  }



  .back-top {

    //position: fixed;

    //right: 354px;

    //bottom: 30px;

    width: 48px;

    height: 48px;

    display: flex;

    align-items: center;

    justify-content: center;

    border-radius: 100px;

    cursor: pointer;



    .icon {

      color: #fff;

      font-size: 32px;

    }

  }
</style>