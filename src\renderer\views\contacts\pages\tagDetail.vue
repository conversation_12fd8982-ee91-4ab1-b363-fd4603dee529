<template>
  <div class="container">
    <div class="f-between" style="margin-bottom: 20px">
      <span class="title">{{ route.params.des }}</span>
      <iconpark-icon class="title-icon" name="iconaddcircle" @click="selectVisible = true"></iconpark-icon>
    </div>
    <div class="list">
      <div class="f-align" style="flex-shrink: 0; margin-bottom: 16px">
        <div :class="['tab-item', tabType === 1 ? 'active' : '']" @click="changeTab(1)">
          联系人（{{ personList.length }}）
        </div>
        <div :class="['tab-item', tabType === 2 ? 'active' : '']" @click="changeTab(2)">
          群组（{{ groupList.length }}）
        </div>
      </div>
      <div class="list-outer scrollbar" v-if="tabType === 1 && personList.length">
        <div
          :class="['list-item', 'f-between', item?.pin ? 'list-item-pin' : '']"
          v-for="item in personList"
          :key="item.card.card_id"
          @click="showCard(item)"
        >
          <!-- <SvgIcon v-if="item?.pin" name="top_mark" class="top-mark" /> -->
          <div v-if="item?.pin" class="i-svg:top_mark top-mark" />
          <div class="f-align">
            <avatar
              class="avatar-icon"
              roundRadius
              :imageUrl="item.card.avatar"
              :userName="item.card.card_name"
              avatarSize="44px"
            />
            <div class="user-info">
              <div class="user-name">
                <div>{{ item.card.card_name }}</div>
                <div v-if="cardIdType(myCardsInfo?.cardId) === 'personal' && cardIdType(item.card?.card_id) === 'personal' && item.type === 1" class="friend-mark">
                  {{ t("contacts.friends") }}
                </div>
              </div>
              <MultiIdTag
                :myCard="{ cardId: myCardsInfo.cardId, teamName: myCardsInfo.team }"
                :anotherCard="{ cardId: item.card.card_id, teamName: item.card.team_name }"
              ></MultiIdTag>
            </div>
          </div>
          <div class="f-align act-group">
            <t-popup placement="bottom-right">
              <iconpark-icon class="drag-icon" @click.capture.stop="() => {}" name="iconmore"></iconpark-icon>
              <template #content>
                <div>
                  <div class="f-align pop-item" @click="updateTag('pin', item)">
                    <iconpark-icon class="icon" name="icontotop"></iconpark-icon>
                    <span>{{ item?.pin ? "取消置顶" : "置顶" }}</span>
                  </div>
                  <div class="f-align pop-item" @click="updateTag('delete', item)">
                    <iconpark-icon class="icon" name="iconunsubscribe"></iconpark-icon>
                    <span>删除</span>
                  </div>
                </div>
              </template>
            </t-popup>
          </div>
        </div>
      </div>
      <div class="list-outer" v-else-if="tabType === 2 && groupList.length">
        <div
          :class="['list-item', 'f-between', item?.pin ? 'list-item-pin' : '']"
          v-for="item in groupList"
          :key="item.group.group"
          @click="goGroup(item)"
        >
          <!-- <SvgIcon v-if="item?.pin" name="top_mark" class="top-mark" /> -->
          <div v-if="item?.pin" class="i-svg:top_mark top-mark" />
          <groupItem :group="item.group"></groupItem>
          <div class="f-align act-group">
            <t-popup placement="bottom-right">
              <iconpark-icon class="drag-icon" name="iconmore" @click.capture.stop="() => {}"></iconpark-icon>
              <template #content>
                <div>
                  <div class="f-align pop-item" @click="updateTag('pin', item)">
                    <iconpark-icon class="icon" name="icontotop"></iconpark-icon>
                    <span>{{ item?.pin ? "取消置顶" : "置顶" }}</span>
                  </div>
                  <div class="f-align pop-item" @click="updateTag('delete', item)">
                    <iconpark-icon class="icon" name="iconunsubscribe"></iconpark-icon>
                    <span>删除</span>
                  </div>
                </div>
              </template>
            </t-popup>
          </div>
        </div>
      </div>
      <div v-else class="empty">
        <img src="@renderer/assets/tagEmpty.png" alt="" />
        <div>暂无联系人，可以去添加试试</div>
      </div>
    </div>

    <select-member
      v-model:visible="selectVisible"
      :show-my-group-menu="true"
      :change-menus="true"
      :active-card-id="[curTagCardId]"
      :select-list="selectListId"
      @confirm="selectList"
      @get-my-card="getCardId"
      :attach="'body'"
    />
  </div>
</template>

<script setup lang="ts">
// import selectMember from "@renderer/components/selectMember/audio-add-members.vue";
import selectMember from '@renderer/components/rk-business-component/select-member/common-add-members.vue';
import avatar from "@renderer/components/kyy-avatar/index.vue";
import SvgIcon from "@/components/SvgIcon.vue";
import groupItem from "../components/groupItem.vue";
import MultiIdTag from "@/components/contacts/MultiIdTag.vue";

import { ref, onMounted, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import { tagDetail, relationPin, editTag } from "@renderer/api/contacts/api/tag";
import { openChat } from "@/utils/share";
import { innerCardsDetails, outerCardsDetails, personalCardsDetails, platformCardsDetails } from "@renderer/api/contacts/api/follow";
import { _transPlatformDetail,cardIdType } from "@renderer/views/identitycard/data";

import { useI18n } from "vue-i18n";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();
const route = useRoute();

const tabType = ref(1);
const personList = ref([]);
const groupList = ref([]);
const selectVisible = ref(false);
const selectListId = ref([]);
const curTagCardId = ref("");
const tagSelectList = ref([]);
const myCardsInfo = ref({ team: "", cardId: '' });

const getTagDetail = () => {
  selectListId.value = [];
  personList.value = [];
  groupList.value = [];
  getCards();
  tagDetail(route.params.id).then(({ data }) => {
    console.log(data, "detail");
    if (data?.list && data.list.length) {
      selectListId.value = data.list.map((v) => v?.card?.card_id || v?.group?.group || "");
      personList.value = data.list.filter((v) => v.type === 1);
      groupList.value = data.list.filter((v) => v.type === 2);
    }
  });
};

const getCards = () => {
  let cardId = "",
    api = null;
  if (curTagCardId.value.slice(0, 1) === "$") {
    cardId = curTagCardId.value.slice(1);
    api = innerCardsDetails;
  } else if (curTagCardId.value.slice(0, 1) === "#") {
    cardId = curTagCardId.value.slice(1);
    api = outerCardsDetails;
  } else if (/^PT/.test(curTagCardId.value)) {
    cardId = curTagCardId.value.slice(2);
    api = platformCardsDetails;
  } else {
    myCardsInfo.value = {cardId:curTagCardId.value,team:''}
    return
  }
  api({ ids: [cardId] }).then((res) => {
    myCardsInfo.value = /^PT/.test(curTagCardId.value) ? _transPlatformDetail(res.data.data[0]) : res.data.data[0];
  });
};
const changeTab = (type) => {
  tabType.value = type;
};

const updateTag = (action, item) => {
  action === "pin" && actionPin(item);
  action === "delete" && actionDel(item);
};

const actionDel = (item) => {
  const list =
    tabType.value === 1
      ? personList.value.filter((v) => v.card.card_id !== item.card.card_id).concat(groupList.value)
      : groupList.value.filter((v) => v.group.group !== item.group.group).concat(personList.value);
  const relation = list.map((v) => {
    if (v.type === 1) {
      return {
        cardId: v.card.card_id,
        type: 1,
      };
    }
    return {
      groupId: v.group.group,
      type: 2,
    };
  });
  editTag(route.params.id, { des: route.params.des, cardId: curTagCardId.value, relation }).then((res) => {
    getTagDetail();
  });
};

const actionPin = (item) => {
  relationPin(route.params.id as string, tabType.value === 1 ? item.card.card_id : item.group.group, {
    type: tabType.value,
    action: item?.pin ? "unpin" : "pin",
  }).then((res) => {
    getTagDetail();
  });
};

const selectList = (list) => {
  selectVisible.value = false;
  tagSelectList.value = list.map((v) => {
    if (v.conversationType === 3) {
      return {
        groupId: v.targetId,
        type: 2,
      };
    }
    return {
      cardId: v.cardId,
      type: 1,
    };
  });
  editTag(route.params.id, { des: route.params.des, cardId: curTagCardId.value, relation: tagSelectList.value }).then(
    (res) => {
      getTagDetail();
    },
  );
};

const getCardId = (card) => {
  curTagCardId.value = card.cardId;
};

const showCard = (item) => {
  ipcRenderer.invoke("identity-card", { cardId: item.card.card_id, myId: curTagCardId.value });
};

const goGroup = (item) => {
  openChat({ main: curTagCardId.value, group: item.group.group });
};

// 多端更新数据更新同步，通讯录里的更新全部走update-contact-list
const updateContactListener = () => {
  getTagDetail();
};

onMounted(() => {
  curTagCardId.value = route.params.cardId as string;
  getTagDetail();
  ipcRenderer.on("update-contact-list", updateContactListener);
});

onUnmounted(() => {
  ipcRenderer.off("update-contact-list", updateContactListener);
});
</script>

<style lang="less" scoped>
@import url("../../zhixing/css/base.less");
.container {
  padding: 20px 24px;
  display: flex;
  flex-direction: column;

  .title {
    color: var(--text-kyy-color-text-1, #1a2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
  .title-icon {
    transform: scale(1.7);
    cursor: pointer;
  }

  .list {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow-y: auto;
    .tab-item {
      padding: 5px 16px;
      color: var(--text-kyy-color-text-2, #516082);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      border-radius: 100px;
      border: 1px solid var(--border-kyy-color-border-default, #d5dbe4);
      background: var(--bg-kyy-color-bg-light, #fff);
      margin-right: 12px;
      cursor: pointer;
    }
    .active {
      color: var(--brand-kyy-color-brand-default, #4d5eff);
      border: 1px solid var(--brand-kyy-color-brand-default, #4d5eff);
    }

    .list-outer {
      flex-grow: 1;
      overflow-y: auto;
    }
    .list-item {
      margin-bottom: 2px;
      padding: 12px 16px;
      align-items: center;
      color: var(--text-kyy-color-text-1, #1a2139);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      &:hover {
        border-radius: 8px;
        background: var(--bg-kyy-color-bg-list-hover, #f3f6fa);
        .act-group {
          visibility: visible;
        }
      }
      .act-group {
        visibility: hidden;
      }
    }

    .drag-icon {
      width: 20px;
      height: 20px;
      border-radius: 4px;
      background: var(--bg-kyy-color-bg-light, #fff);
      margin-right: 12px;
      color: #828da5;
      cursor: pointer;
      &:hover {
        background: var(--bg-kyy-color-bg-brand-foucs, #dbdfff);
        color: var(--lingke-brand-hover, #707eff);
      }
    }
    .list-item-pin {
      border-radius: 8px;
      background: var(--bg-kyy-color-bg-deep, #f5f8fe);
      position: relative;
    }
    .avatar-icon {
      margin-right: 9px;
    }
    .card_name {
      font-size: 14px;
      line-height: 22px;
    }
  }
}
.t-button {
  min-width: auto;
}
.t-button--shape-circle {
  border: none;
  background-color: transparent;
  border-radius: 50% !important;
  width: 36px;
  height: 36px;
  img {
    width: 36px;
    height: 36px;
  }
}
.pop-item {
  padding: 5px 20px 5px 16px;
  color: var(--kyy_color_dropdown_text_default, #1a2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  cursor: pointer;
  .icon {
    margin-right: 12px;
    color: #828da5;
  }
  &:hover {
    border-radius: var(--kyy_radius_dropdown_s, 4px);
    background: var(--bg-kyy-color-bg-list-hover, #f3f6fa);
  }
}
.empty {
  color: var(--text-kyy-color-text-2, #516082);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.top-mark {
  position: absolute;
  top: 4px;
  left: 4px;
  // width: 12px;
  // height: 12px;
  font-size: 12px;
}
.right-info {
  .name-text {
    font-size: 14px;
    line-height: 22px;
  }
  .post {
    display: flex;
    align-items: center;
    .person-num {
      margin-top: 2px;
      font-size: 12px;
      color: #717376;
      line-height: 16px;
    }
  }
}
.user-info {
  .user-name {
    display: flex;
    align-items: flex-start;
  }
  .post {
    font-size: 12px;
    margin-top: 2px;
    color: #717376;
    line-height: 16px;
  }
  .friend-mark {
    width: 28px;
    height: 22px;
    border-radius: 4px;
    background: var(--kyy_color_tag_bg_brand, #eaecff);
    color: var(--kyy_color_tag_text_brand, #4d5eff);
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    text-align: center;
    line-height: 22px;
    margin-left: 4px;
  }
  .temporary-mark {
      width: 28px;
      height: 22px;
      background-color:#FFE3F1 ;
      border-radius: 4px;
      font-size: 12px;
      text-align: center;
      color: #FF4AA1;
      line-height: 22px;
      margin-left: 4px;
    }
}
</style>
