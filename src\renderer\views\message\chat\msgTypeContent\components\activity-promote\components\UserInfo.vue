<template>
  <div class="content-section">
    <div class="user-info">
      <Avatar
        class="mr-4"
        :image-url="avatar"
        :user-name="companyName"
        avatar-size="24px"
        round-radius
      />
      <span class="company-name">{{ companyName }}</span>
    </div>
    <div class="apply-text">{{ applyText }}</div>
  </div>
</template>

<script setup lang="ts">
import Avatar from '@renderer/components/kyy-avatar/index.vue';

interface Props {
  avatar: string
  companyName: string
  applyText: string
}

defineProps<Props>();
</script>

<style scoped>
.content-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.company-name {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.571;
  color: #1a2139;
}

.apply-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.571;
  color: #1a2139;
  height: 22px;
}
</style>
