<!-- 党建列表页公用页面 -->
<template>
  <div class="wh-full flex flex-col relative pb_bg">
    <div style="height: 56px">
      <Top @on-change="onDataChange" />
    </div>
    <template v-if="props.datas.list?.length && props.datas?.count">
      <List :list="props.datas.list" :total="props.datas.count" />
      <template v-if="props.datas?.count && props.datas?.count > 10">
        <Pagination :current-props="params.page" :total="props.datas.count" @on-change="onDataChange" />
      </template>
    </template>
    <div v-else-if="props.datas?.count !== null" class="flex-1">
      <!-- <Empty class="wh-full mt-0!" /> -->
      <!-- <no-empty class="wh-full mt-0!" /> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent, onActivated } from 'vue';
const Top = defineAsyncComponent(() => import('./components/Top.vue'));
const List = defineAsyncComponent(() => import('./components/List.vue'));
const Pagination = defineAsyncComponent(() => import('./components/Pagination.vue'));
// import noEmpty from '../dj-management/common/no-empty.vue';

import { Params } from './constant';

const props = defineProps({
  datas: {
    type: Object,
    default: () => ({
      count: 0,
      list: [],
    }),
  },
});
const emits = defineEmits(['onParamsChange']);
const params = ref<Params>({
  page: 1,
  pageSize: 10,
  keyword: '',
});

const onDataChange = (transferData: Params) => {
  params.value = {
    ...params.value,
    ...transferData,
  };
  emits('onParamsChange', params.value);
};

onMounted(() => {
  emits('onParamsChange', params.value);
});
onActivated(() => {
  emits('onParamsChange', params.value);
});
</script>
<style scoped lang="less">
.pb_bg {
  background: url('@assets/pb/pb_bg.png') no-repeat top center;
  background-size: cover;
  overflow-y: auto;
  padding-bottom: 16px;
}
</style>
