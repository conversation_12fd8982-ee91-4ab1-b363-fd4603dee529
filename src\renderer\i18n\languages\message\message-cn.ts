export const im_CN = {
  public: {
    // 欢迎来到另可
    welcome: '欢迎来到另可',
    // 开启另一种可能
    welcomeTips: '开启另一种可能',
    confirm: '确定',
    save: '保存',
    savelocal: '保存到本地',
    cancel: '取消',
    remove: '移除',
    // 发送失败
    sendFail: '发送失败',
    // 有人@我
    atMe: "{'@'}我",
    tips: '提示',
    more: '更多',
    known: '知道了',
    goGroupAdmin: '前往组织管理后台',
    search: '搜索',
    detail: '查看详情',
    dynamic: '查看动态',
    unknown: '未知',
    unsupport: '消息类型暂不支持',
    vcard: '数字名片',
    recall_self: '你撤回了一条消息',
    recall_peer: '撤回了一条消息',
    recall_peer2: '撤回一条消息',
    text: '文本',
    img: '图片',
    longImg: '长图',
    emoji: '表情',
    voice: '语音',
    video: '视频',
    file: '文件',
    location: '位置',
    address: '地址',
    chat_record: '聊天记录',
    card: '身份卡',
    cloud_file: '云盘文件',
    remind: '提醒',
    note: '笔记',
    schedule: '日程',
    zxlist: '清单',
    approve: '审批',
    square: '广场',
    square_moment: '广场动态',
    square_id: '广场号',
    square_preview: '广场预览',
    album: '拾光相册',
    invite: '邀请你参加',
    day_weather: '每日天气',
    friend_notify: '新联系人',
    biz: '数字商协',
    government: '数字城市',
    notify: '通知',
    video_call: '视频通话',
    voice_call: '语音通话',
    send_vcard: '发送名片',
    you: '你',
    voiceToText: '转文字',
    fold: '收起',
    copy: '复制',
    forward: '转发',
    forwardSuc: '转发成功',
    reply: '回复',
    delete: '删除',
    recall: '撤回',
    select: '多选',
    download: '下载',
    save_disk: '存入云盘',
    add_remind: '添加提醒',
    save_common: '存到常用',
    add_common: '已添加至常用',
    add_common_fail: '添加失败',
    add_common_tolong: '添加常用语不能超过200字！',
    add_later_suc: '已添加为稍后处理',
    del_later_suc: '已移除稍后处理',
    top: '置顶',
    cancel_top: '取消置顶',
    mute: '消息免打扰',
    isMute: '消息提醒已关闭',
    notMute: '消息提醒已开启',
    unmute: '允许消息通知',
    move_into_common: '移入常用',
    remove_common: '移出常用',
    move_into_common_success: '移入常用成功',
    remove_common_success: '移出常用成功',
    move_into_common_error: '移入常用失败，请重试',
    remove_common_error: '移出常用失败，请重试',
    common_session_none: '可将您的重要会话添加至常用聊天',
    common_session_emty: '右键点击一个会话，选择【移入常用】，将您的重要会话加入常用聊天吧',
    quit_group: '退出群聊',
    session_del: '不显示聊天',
    auth_expired: '登录失效，请重新登录',
    group_baned: '你已不在当前群聊',
    quit_group_before_tips: '请将群主身份转让后，再退出群聊',
    load_retry: '加载失败，点击重试',
    load_retry1: '加载失败，请重试',
    load_error1: '内容加载失败',
    refresh: '刷新',
    loading: '加载中...',
    init_error: '初始化失败，请重试',
    syncing: '收取中...',
    linking: '连接中...',
    disconnect: '连接已断开',
    networkErr: '网络异常，请检查网络',
    includes: '包含: ',
    // 常用位置
    common_addr: '常用位置',
    // 举报
    report: '举报',
    // 截图
    screenshot: '截图',
    // 极速模式
    speed: '极速',
    speedMode: '使用常用信息快速回复',
    // 场景
    scene: '场景',
    // 逐条转发
    forward1: '逐条转发',
    // 合并转发
    forward2: '合并转发',
    // 下载图片
    downloadImg: '下载图片',
    // 常用语
    commonTxt: '常用语',
    // 常用图片
    commonImg: '常用图片',
    // 常用文件
    commonFile: '常用文件',
    addCommon: '添加常用语',
    addCommonTip: '添加常用语,快速回复消息',
    addImg: '添加常用图片',
    addImgTip: '添加常用图片,快速回复消息',
    addFile: '添加常用文件',
    addFileTip: '添加常用文件,快速回复消息',
    localCommon: '通过移动端或聊天信息中添加常用地址',
    // 所有人
    all: '所有人',
    // 请输入...'
    input: '请输入...',
    // 请先勾选内容项
    check: '请先勾选内容项',
    // 未知用户
    unknownUser: '未知用户',
    delMsg: '确认删除消息',
    delMsgTips: '删除的信息将从你的会话记录消失，但仍对话内其他人可见。',
    // 没有可下载的图片
    noImg: '没有可下载的图片',
    // 最多选择9个
    maxSelect: '最多选择9个',
    // 单个文件不能超过100MB
    maxFile: '单个文件不能超过100MB',
    // 已注销
    logout: '已注销',
    // 好友
    friend: '好友',
    // 咨询
    consult: '咨询',
    // 部门
    department: '部门',
    // 全员
    allMember: '全员',
    // 内部
    internal: '内部',
    // 外部
    external: '外部',
    // 家庭
    family: '亲友',
    // 平台群
    platform: '平台',
    groups: '分组',
    // 已添加到置顶
    topTip: '已添加到置顶',
    // 已取消置顶
    cancelTopTip: '已取消置顶',
    // 添加成员
    addMember: '添加群聊',
    // 选择人员
    selectMember: '选择人员',
    // 搜索群成员
    searchMember: '搜索群成员',
    // 已选
    selected: '已选',
    // 对方已注销账号
    logoutTip: '对方已注销账号',
    // 无法在退出的群聊中发消息
    exitTip: '无法在退出的群聊中发消息',
    addContact1: '你与',
    addContact2: '非联系人，请先',
    addContact3: '添加好友',
    // 当前群聊已解散
    dismissTip: '当前群聊已解散',
    // 你已被移除群聊
    removeTip: '你已被移除群聊',
    // 重新编辑
    editAgain: '重新编辑',
    // 名片
    card1: '名片',
    disk: '云盘',
    // 前往查看
    goCheck: '前往查看',
    // 预览
    preview: '预览',
    // 全部
    all1: '全部',
    // 已同步到【${setting.groupInfo.members?.[0]?.attachments?.teamName}】云盘
    syncTip: '已同步到【{0}】云盘',
    // 前往云盘
    goDisk: '前往云盘',
    // 群聊文件、图片可永久保存到云盘
    saveTip: '群聊文件、图片可永久保存到云盘',
    // 开启同步到云盘
    openSync: '开启同步到云盘',
    // 筛选
    filter: '筛选',
    // 群成员
    groupMember: '群成员',
    // 选择成员
    chooseMember: '选择成员',
    // 日期
    date: '日期',
    // 文件
    file1: '文件',
    // 图片与视频
    imgVideo: '图片与视频',
    // 本周
    thisWeek: '本周',
    // 链接
    link: '链接',
    // 你没有权限开启同步到云盘，可联系群主申请开启
    noAuth: '你没有权限开启同步到云盘，可联系群主申请开启',
    // 添加新成员
    addNewMember: '添加新成员',
    // 置顶聊天
    topChat: '置顶聊天',
    // 群备注
    groupRemark: '群备注',
    // 群备注仅自己可见
    groupRemarkTip: '请输入备注',
    // 我在本群的名称
    selfGroupRemark: '我在本群的名称',
    // 保存到通讯录
    saveContact: '保存到通讯录',
    // 群二维码
    groupQRCode: '群二维码',
    DogroupQRCode: '扫描二维码，加入群聊',
    QigroupQRCode: '此群是企业内部群聊，仅企业成员可扫码加入',
    // 解散该群
    dismissGroup: '解散该群',
    // 群管理
    groupManager: '群管理',
    // 添加群管理员
    addGroupManager: '添加群管理员',
    // 开启邀请成员需要管理员同意
    inviteTip: '开启邀请成员需要管理员同意',
    // 开启新成员可查看历史记录
    historyTip: '开启新成员可查看历史记录',
    // 开启群文件自动同步到云盘
    syncTip1: '开启群文件自动同步到云盘',
    // 开启后，本群文件将自动保存到云盘，群解散也不会丢失
    syncTip2: '开启后，本群文件将自动保存到云盘，群解散也不会丢失',
    // 开启群图片自动同步到云盘
    syncTip3: '开启群图片自动同步到云盘',
    // 开启后，本群图片将自动保存到云盘，群解散也不会丢失
    syncTip4: '开启后，本群图片将自动保存到云盘，群解散也不会丢失',
    // 群管理员
    groupAdmin: '群管理员',
    // 转让群主
    transferGroup: '转让群主',
    // 群主
    groupOwner: '群主',
    // 解散群组
    dismiss: '解散群组',
    // 解散该群后，所有成员都将被移除群聊
    dismissTip1: '解散该群后，所有成员都将被移除群聊',
    // 退出群组
    exit: '退出群组',
    // 退出后将不再接受此群组消息
    exitTip1: '退出后将不再接受此群组消息',
    // 清除聊天记录
    clearChat: '清除聊天记录',
    // 标签
    tag: '标签',
    // 确认移除?
    removeTip1: '确认移除?',
    // 是否移除该成员 ${member.attachments?.nickname || member.attachments?.staffName}
    removeTip2: '是否移除该成员 {0}',
    // 当前开启了入群验证，禁止生成二维码
    qrCodeTip: '当前开启了入群验证，禁止生成二维码',
    // 无搜索结果
    noResult: '无搜索结果',
    // 联系人
    contact: '联系人',
    // 我的助手
    assistant: '我的助手',
    // 查看更多我的助手
    moreAssistant: '查看更多相关助手',
    // 查看更多相关联系人
    moreContact: '查看更多相关联系人',
    // 我的群组
    myGroup: '我的群组',
    // 查看更多相关群组
    moreGroup: '查看更多相关群组',
    // 查看更多相关消息
    moreMsg: '查看更多相关消息',
    // 通过网络查找更多联系人
    moreContactTip: '通过网络查找更多联系人',
    group: '群组',
    // 引用
    quote: '引用',
    // 最近联系过
    recentContact: '最近联系过',
    // 我的工具栏
    myTools: '我的工具栏',
    // 暂无数据
    noData: '暂无数据',
    // 打开
    open: '打开',
    // 打开文件夹
    openFolder: '打开文件夹',
    // 另存为
    saveAs: '另存为',
    // 当前群已开启入群验证，请填写申请信息
    applyTip: '当前群已开启入群验证，请填写申请信息',
    // 请检查网络环境
    networkTip: '请检查网络',
    // 选择联系人
    selectContact: '选择联系人',
    // 创建群聊
    createGroup: '创建群聊',
    // 更多群场景
    moreScene: '更多群场景',
    // 适用于公司、组织或者团队内部的交流
    sceneTip: '适用于公司、组织或者团队内部的交流',
    // 适用于公司员工对外部客户、朋友之间的交流
    sceneTip1: '适用于公司员工对外部客户、朋友之间的交流',
    // 适用于个人朋友之间的日常交流
    sceneTip2: '适用于个人朋友之间的日常交流',
    // 适用于家庭成员、个人之间的日常交流
    sceneTip3: '适用于亲友、个人之间的日常交流',
    // 公司群
    companyGroup: '公司群',
    // 部门群
    departmentGroup: '部门群',
    // 分组群
    groupGroups: '分组群',
    // 内部群
    internalGroup: '内部群',
    // 外部群
    externalGroup: '外部群',
    // 普通群
    normalGroup: '普通群',
    // 家庭群
    familyGroup: '亲友群',
    platformGroup: '平台群',
    platformAllGroup: '平台全员群',
    groupType: '群类型',
    createGroup1: '创建群组',
    addContact4: '发起群聊、添加联系人',
    myCard: '我的身份卡',
    mute1: '静音',
    unmute1: '取消静音',
    download_suc: '下载成功',
    internalGroupTips: '用于组织\\团队内部沟通，所有群成员均来自于同一个团队。创建后仅支持邀请本组织\\团队内部成员入群。',
    externalGroupTips: '用于组织\\团队员工对外部沟通协作。群主必须拥有外部身份，群员可使用外部或个人身份加入。',
    normalGroupTips: '用于个人与朋友之间的日常交流。创建后仅支持个人身份成员入群。',
    familyGroupTips: '用于亲友之间的日常交流。创建后仅支持个人身份成员入群，根据家庭场景提供场景化工具。',
    platformGroupTips: '用于行业或政企成员之间的日常资源共享交流；支持根据平台群场景匹配商机、另可圈等工具。',
    creatByCard: '按身份创建',
    creatByCardTips: '（根据身份，创建不同类型群聊）',
    creatByType: '按类型创建',
    creatByTypeTips: '（根据群类型，提供更适合的场景化功能）',
    sceneTip_new: '适用企业或组织内部的沟通',
    sceneTip1_new: '适用企业或组织外部的沟通',
    sceneTip2_new: '适用个人与个人之间的日常沟通',
    sceneTip3_new: '适用亲友之间的日常沟通',
    sceneTip4_new: '适用行业或政企成员之间的资源共享、沟通',
    sceneTip_light: '亮点功能：[消息已读未读]',
    sceneTip1_light: '亮点功能：[数据归属组织所有] ',
    sceneTip2_light: '亮点功能：[公告]  [笔记]  [活动]  等',
    sceneTip3_light: '亮点功能：[运动排行]  [拾光相册]  等',
    sceneTip4_light: '亮点功能：[服务] [商机] [另可圈] 等',
    resend: '重新发送',
    verify: '检测到你当前账号未实名认证，为保证你可成功建立平台群， 请前往app设置->账号与安全->实名认证 完善信息。',
    setIdentity: '设置群身份',
    addIdentity: '新增群身份',
    editIdentity: '编辑群身份',
    nameLabel: '群身份名称',
    namePlaceholder: '请输入名称',
    none: '无',
    pravicyTips: '由于对方隐私设置，无法发起通话',
    deleteIdentity: '删除将会取消已设置身份的群成员。',
    deleIdentityHead: '确认删除群身份吗？',
    dialogHeader: '温馨提示',
    inherit: '当前群内另可圈尚无可继承人员，请联系成员进行实名认证',
    transGroup: '当前成员未通过实名认证，无法转让',
    dissolution: '当前群另可圈使用中，是否解散群聊？',
    circle_tips: '暂无权开通另可圈，请联系群主开通',
    contentRevoked: '内容已撤回',
    contentDeleted: '内容已删除',
    mediaAccessTitle: '设备访问受限',
    mediaAccessContentMicrophone: '应用未被授权麦克风的访问权限，请在系统偏好设置中授权访问权限',
    mediaAccessContentCamera: '应用未被授权摄像头的访问权限，请在系统偏好设置中授权访问权限',
    cannotCreateDigitalGroup: '你没有数字平台应用管理员权限，无法创建平台群',
    noFriends: '你还没有好友哦，添加好友一起互动吧',
    noInternalOrg: '暂无内部组织，马上加入或创建组织',
    joinOrgTip: '点击「加入组织」，创建商务身份卡，开启跨组织沟通协作通道',
    noDigitalPlatform: '暂无数字平台，马上开启或加入，构筑未来智慧生态',
    addFriend: '添加好友',
    joinOrg: '加入组织',
    createOrg: '创建组织',
    joinPlatform: '加入数字平台',
    ctRevoked: '發布審核已被發起人撤回',
    noCheckAuth: '您没有审核限',
  },
  vcard: {
    digital_card: '另可数字名片',
    send_card_tip: '是否向好友发送你的数字名片？',
    open_card: '打开数字名片',
    send: '发送',
    send_success: '已发送',
  },
  rtc: {
    call_video: '你发起视频通话',
    call_voice: '你发起语音通话',
    call_video1: '对方发起视频通话',
    call_voice1: '对方发起语音通话',
    timeout: '对方未接听通话',
    timeout1: '通话未接听',
    cancel: '你已取消通话',
    cancel1: '对方已取消通话',
    cancel2: '对方已取消',
    busy: '对方正在通话中',
    reject: '对方已拒绝通话',
    reject1: '你已拒绝通话',
    duration: '通话时长',
  },
  time: {
    once: '单次',
    daily: '每天',
    weekly: '每周',
    monthly: '每月',
    yearly: '每年',
    workday: '工作日',
    rang1: '周一到周六',
    rang2: '周日到周五',
    none: '无',
    after5min: '5分钟后',
    after15min: '15分钟后',
    after30min: '30分钟后',
    after1h: '1小时后',
    after2h: '2小时后',
    after1d: '1天后',
    after2d: '2天后',
    immediately: '即时',
    current: '刚刚',
    inOneMin: '1分钟内',
    before: '提前',
    beforemin: '分钟前',
    aftermin: '分钟后',
    before5min: '提前5分钟',
    before15min: '提前15分钟',
    before30min: '提前30分钟',
    before1h: '提前1小时',
    before2h: '提前2小时',
    before1d: '提前1天',
    haved: '已开始',
    now: '现在',
    day: '天',
    hour: '小时',
    minute: '分钟',
    today: '今天',
    yesterday: '昨天',
    dayBeforeYesterday: '前天',
    year: '年',
    month: '月',
    day1: '日',

  },
  msg: {
    album_share: '共享了一个相册节点',
    remind_time: '提醒时间',
    remind_period: '提醒频次',
    repeat_period: '重复周期',
    remind_frequency: '提醒频次',
    task_time: '任务时间',
    expire_remind: '到期提醒',
    renew: '立即续费',
    // 同意
    agree: '同意',
    // 已同意
    agreed: '已同意',
    // 拒绝
    refuse: '拒绝',
    // 已拒绝
    refused: '已拒绝',
    // 已申诉
    appealed: '已申诉',
    // 提交申诉
    appeal: '提交申诉',
    // 已被他人处理
    handled: '已被他人处理',
    // 已转交
    transfer: '已转交',
    // 已被转交
    transferred: '已被转交',
    // 已退回
    back: '已退回',
    // 已加签
    countersign: '已加签',
    // 发起人已撤销
    revoke: '发起人已撤销',
    // 评论了审批
    comment: '评论了审批',
    // 送祝福
    sendBlessing: '送祝福',
    // 禁言已结束，无法申诉
    appealTip: '禁言已结束，无法申诉',
    // 已超过申诉时间，无法申诉
    appealTip1: '已超过申诉时间，无法申诉',
    // 湿度
    humidity: '湿度',
    // 级
    level: '级',
    // 特别关注
    specialAttention: '特别关注',
    // 有人@你
    atYou: "有人{'@'}你",
    // 条新消息
    newMsg: '条新消息',
    // 入群申请
    inviteApply: '入群申请',
    // 邀请进群
    inviteGroup: '邀请进群',
    // 备注
    remark: '备注',
    // 通过
    pass: '通过',
    // 发送
    send: '发送',
    // 换行
    newline: '换行',
    // 操作成功
    success: '操作成功',
    // 已读未读列表
    readList: '已读未读列表',
    // 已读
    read: '已读',
    // 人已读
    read1: '人已读',
    // 全部已读
    readAll: '全部已读',
    // 未读
    unread: '未读',
    // 人未读
    unread1: '人未读',
    // 已复制
    copied: '已复制',
    // 加一个
    addOne: '加一个',
    // 身份卡信息错误
    cardError: '身份卡信息错误',
    // 日程分享
    scheduleShare: '日程分享',
    // 我已经将我的日程共享给你。打开日历勾选我的名字后，即可查看
    scheduleShareTip: '我已经将我的日程共享给你。打开日历勾选我的名字后，即可查看',
    // 查看日程
    checkSchedule: '查看日程',
    // 前往认证
    goAuth: '前往认证',
    // 组织认证通过
    checkAuth: '查看认证资料',
    // 管理员
    admin: '管理员',
    // 你的删除联系人申请，如有疑问，建议联系企业管理员
    deleteTip: '你的删除联系人申请，如有疑问，建议联系企业管理员',
    // 请在组织管理后台中查看订单详情
    orderTip: '请在组织管理后台中查看订单详情',
    // 请在组织管理后台中查看发票详情
    invoiceTip: '请在组织管理后台中查看发票详情',
    // 查看订单
    checkOrder: '查看订单',
    // 查看发票
    checkInvoice: '查看发票',
    // 拒绝原因
    refuseReason: '拒绝原因',
    // 这条动态已删除
    deleteMoment: '这条动态已删除',
    // 来自 ${data?.square?.name} 的动态
    momentFrom: '来自 {0} 的动态',
    // 广场号ID: ${data?.pid}
    momentId: '广场号ID: {0}',
    // 商机
    biz: '商机',
    // 来自${data?.square_name}的商机
    bizFrom: '来自{0}的商机',
    // 活动时间
    activityTime: '活动时间',
    // 活动地址
    activityAddr: '活动地址',
    // 帮助中心
    help: '帮助中心',
    // 举报详情
    reportDetail: '举报详情',
    postDetail: '动态详情',
    // 违规详情
    violationDetail: '违规详情',
    // 安全验证
    security: '安全验证',
    // 详情
    detail: '详情',
    // 暂定
    pending: '暂定',
    // 参与
    join: '参与',
    // 你不是活动参与人，无法查看或加入此活动
    joinTip: '你不是活动参与人，无法查看或加入此活动',
    // 无法加入
    joinTip1: '无法加入',
    // 删除历史记录
    deleteHistory: '删除历史记录',
    // 删除联系人
    deleteContact: '删除联系人',
    // 申请人
    applicant: '申请人',
    // 备注信息
    remarkInfo: '备注信息',
    // 待审核
    pendingReview: '待审核',
    // 审核
    review: '审核',
    // 签约成功
    contractSuccess: '签约成功',
    // 收益结算
    settlement: '收益结算',
    // 指定BD经理通知
    bdManager: '指定BD经理通知',
    // 提现通过
    cashPass: '提现通过',
    // 提现拒绝
    cashRefuse: '提现拒绝',
    // 合伙人等级调整
    partnerLevel: '合伙人等级调整',
    // 合伙人签约
    partnerSign: '合伙人签约',
    // 合伙人身份变动
    partnerChange: '另可联盟身份变动通知',
    // 注销成功澳门
    partnerCancelMO: '注销成功',
    // 注销成功
    partnerCancel: '注销成功',
    // 注销异常
    partnerCancelFail: '注销异常',
    // 合伙人解绑
    partnerUnBan: '合伙人解绑',
    // 查看收入明细
    checkIncome: '查看收入明细',
    // 立即签约
    contractNow: '立即签约',
    // 查看提现详情
    checkCash: '查看提现详情',
    // 查看等级权益
    checkLevel: '查看等级权益',
    // 请前往app操作查看
    appTip: '请前往app操作查看',
    // 请前往app操作查看收入明细
    appTip1: '请前往app操作查看收入明细',
    // 请前往app操作查看提现详情
    appTip2: '请前往app操作查看提现详情',
    // 请用app操作查看等级权限
    appTip3: '请用app操作查看等级权限',
    appTip4: '请用app操作查看等级权益',
    // 调整原因：
    adjustReason: '调整原因',
    // 解绑原因
    UnBanReason: '解绑原因',
    // 举报成功
    reportSuccess: '举报成功',
    // 举报不成立
    reportFail: '举报不成立',
    // 申述结果通知
    appealResult: '申述结果通知',
    // 服务链接违规
    serviceViolation: '服务链接违规',
    // 通过${data?.header?.staff?.from}进入
    enter: '通过{0}进入',
    enter1: '通过',
    enter2: '邀请进入',
    // 申请加入：
    applyJoin: '申请加入',
    // 加入部门：
    joinDepartment: '加入部门',
    // 新联系人通知
    newContact: '新联系人通知',
    none: '无',
    // 忽略
    ignore: '忽略',
    // 已忽略
    ignored: '已忽略',
    // 已过期
    expired: '已过期',
    // 暂无数据
    noData: '暂无数据',
    // 服务
    service: '服务',
    // 发送失败，点击重试
    sendFail: '发送失败，点击重试',
    // 参与了运动排行
    joinRank: '{0}参与了运动排行',
    joinRank1: '参与了',
    joinRank2: '运动排行',
    // ${data.top1.title}获得${getYearMonthDay(data.date * 1000)}运动排行榜冠军
    rank1: '{0}获得{1}运动排行榜冠军',
    // 运动要求
    rankRequire: '运动要求',
    // 转文字失败
    convertError: '转文字失败',
    enterNow: '立即进入',
    pravicyTips: '权限不足，请联系管理员添加权限',
  },
  tools: {
    later: '稍后处理',
    later1: '标记碎片信息，事事不遗漏！',
    remind: '提醒',
    remind1: '定时提醒，让你把握每个重要时刻！',
    cardtip: '数字化电子名片，快捷建立商务联系',
    weather: '每日天气',
    weather2: '天气',
    weather3: '成员天气',
    weatherSet: '天气设置',
    weather1: '同步异地家人天气，让牵挂稍加一些心安！',
    birthday: '生日祝福',
    birthday1: '家人生日到期提醒，暖心祝福语任意挑选！',
    albums: '拾光相册',
    albums1: '将美好经历记录在册，汇集家人的温暖故事！',
    setting: '设置',
    push_type: '选择模式',
    push_option1: '固定位置',
    push_option2: '位置实时同步',

    push_option_tip1: '可自定义不同地区天气',
    push_option_tip2: '开启同步后，获取当前位置天气',
    push_option_tip3: '请用手机版另可扫描二维码授权',
    push_option_tip4: '开启每日天气信息',
    lastUpTime: '最后更新时间',

    push_city: '推送城市',
    push_time: '推送时间',
    important: '重要日子',
    note: '笔记',
    noteTool: '笔记工具',
    activity: '活动',
    activityTool: '活动工具',
    sports: '运动排行',
    group: '群公告',
    groupTool: '群公告工具',
    discover: '发现另可',
    moreTool: '更多工具',
    circle: '另可圈',

    groupTips: '用于向群成员传达重要信息或提醒',
    albumsTips: '用于记录分享您的珍贵回忆',
    noDataDefault: '暂无',
  },
  later: {
    pending: '进行中',
    finish: '已完成',
    from: '来自',
  },
  birth: {
    title: '生日祝福',
    desc: '开启后，可为群成员配置生日祝福',
    title1: '生日送祝福',
    desc1: '随机祝福语',
    title2: '祝福卡片样式',
    desc2: '样式{type}',
    group: '群成员生日（{0}人）',
    setting: '群成员生日设置',
    setting1: '生日设置',
    setting2: '生日推送',
    style: '祝福卡片样式',
    styleChoose: '请选择祝福卡片的样式',
    warning1: '请联系群主或群管理员开启此功能',
    warning2: '仅群主或者群管理员才能关闭此功能',
    warning3: '请联系群主或群管理员切换卡片样式',
    calendar: '公历',
    lunar: '农历',
    style1: '样式一',
    style2: '样式二',
    style3: '样式三',
    notSet: '未设置',
    searchMember: '搜索成员名称',
  },
  groupNotice: {
    addNew: '新建群公告',
    set: '发布设置',
    isSession: '发送至会话',
    isTop: '在会话内置顶显示',
    deleteTip: '确认删除该群公告吗？',
    release: '发布于',
    releaseTip: '已发布',
    groupNotice: '群公告',
    from: '由',
    top: '置顶',
    toMy: '仅对自己取消置顶',
    toAll: '对所有群成员取消置顶',
    receive: '收到',
    inviteGroupApplyList: '条进群申请',
    clickView: '点击查看',
    inviteApply: '入群申请',
    inviteGroup: '邀请进群',
    refuse: '拒绝',
    pass: '通过',
    desc: '备注',
    authTip: '管理权限已变更，请重新操作',
  },
  // 排行问题
  rank: {
    // 今日
    rankToday: '今日',
    // 昨日
    rankYesterday: '昨日',
    // 本周
    rankThisWeek: '本周',
    // 本月
    rankThisMonth: '本月',
    // 全部
    rankAll: '全部',
    // 本週每日步數
    rankThisWeekStep: '本周步数',
    // 排行榜
    rankList: '排行榜',
    // 運動記錄
    rankRecord: '运动记录',
    // 運動要求
    rankRequire: '运动要求',
    // 標題
    title: '标题',
    // 運動時間
    step: '运动时间',
    // 步數要求
    stepRequire: '步数要求',
    // 讚我的家人
    likeFamily: '赞我的家人',
    // 請輸入具體步數，列如：1000
    stepRequireTip: '请输入具体步数，列如：1000',
    // 請輸入
    inputTip: '请输入',
    // 数
    number: '数',
  },
  // 重要日子
  important: {
    settings: '设置',
    createText: '为家人创建重要日子吧！',
    addList: {
      title: '标题',
      titleph: '请输入标题，例如：家人团聚日',
      comment: '备注',
      commentph: '请输入备注',
      dateph: '请选择',
      period: '重复',
      periodSelect: '不重复',
      reminder: '提醒时间',
      reminderSelect: {
        0: '当天（上午9点）', 1: '1天前（上午9点）', 2: '2天前（上午9点）', 3: '3天前（上午9点）', 7: '7天前（上午9点）',
      },
      albums: '图片',
      upload: '点击上传',
      fujian: '附件',
      creator: '创建人',
      createTime: '创建时间',
    },
    edit: '编辑',
    delete: '删除',
    havePeriod: '已过期',
    tipText: '该条重要日子已过期，无法编辑',
    delText: '确定删除该重要日子吗？',
    setTextOne: '显示已过期的日子',
    setTextTwo: '推送法定节假日',
    area: '区域',
    selectArea: '选择区域',
    selectAreaph: '請选择区域',
    areaOptionOne: '中国',
    areaOptionTwo: '中国澳门',
    detail: '详情',
  },
  /** 广告审核状态 */
  adStatus: {
    status_1: '未开始',
    status_2: '投放中',
    status_3: '结束',
    status_4: '终止',
    status_5: '取消',
    status_6: '待审核',
    status_7: '审核不通过',
    status_8: '超时未审核',
  },
  FeePackageCard: {
    体验套餐权益已到账: '体验套餐权益已到账',
    已获得: '已获得',
    体验套餐权益: '体验套餐权益',
    查看全部权益: '查看全部权益',
    权益内容: '权益内容',
    体验剩余: '体验剩余',
    立即购买: '立即购买',
    体验已结束: '体验已结束',
  },
};
