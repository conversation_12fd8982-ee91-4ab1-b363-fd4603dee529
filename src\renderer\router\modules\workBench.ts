const nicheRoute = [
  {
    path: 'nicheHome',
    name: 'nicheHome',
    component: () => import('@renderer/views/niche/index.vue'),
  },
  {
    path: 'nicheAdmin',
    name: 'nicheAdmin',
    component: () => import('@renderer/views/niche/admin.vue'),
  },
  {
    path: 'nicheExtend',
    name: 'nicheExtend',
    component: () => import('@renderer/views/niche/extend.vue'),
  },
  {
    path: 'nicheDraftList',
    name: 'nicheDraftList',
    component: () => import('@renderer/views/niche/draft.vue'),
  },
  {
    path: 'nicheExtendManage',
    name: 'nicheExtendManage',
    component: () => import('@renderer/views/niche/extendManage.vue'),
  },
  {
    path: 'nicheDetail',
    name: 'nicheDetail',
    component: () => import('@renderer/views/niche/nicheDetail.vue'),
  },
  {
    path: 'detailExtendPage',
    name: 'detailExtendPage',
    component: () => import('@renderer/views/niche/detailExtendPage.vue'),
  },
  {
    path: 'extendDetail',
    name: 'extendDetail',
    component: () => import('@renderer/views/niche/extendDetail.vue'),
  },

  {
    path: 'nicheCreate',
    name: 'nicheCreate',
    component: () => import('@renderer/views/niche/nicheCreate.vue'),
  },
  {
    path: 'nicheEdit',
    name: 'nicheEdit',
    component: () => import('@renderer/views/niche/nicheEdit.vue'),
  },
  {
    path: 'nicheDraft',
    name: 'nicheDraft',
    component: () => import('@renderer/views/niche/nicheDraft.vue'),
  },
  {
    path: 'nicheDetailReadOnly',
    name: 'nicheDetailReadOnly',
    component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
  },
];

const deviceRoute = [
  {
    path: 'device-list',
    name: 'bench_deviceList',
    component: () => import('@renderer/views/device-management/device-list.vue'),
  },
  {
    path: 'device-admin',
    name: 'bench_deviceAdmin',
    component: () => import('@renderer/views/device-management/device-admin.vue'),
  },
  {
    path: 'device-admin-employee',
    name: 'bench_deviceAdminEmployee',
    component: () => import('@renderer/views/device-management/device-admin-employee.vue'),
  },
  {
    path: 'device-admin-permission',
    name: 'bench_deviceAdminPermission',
    component: () => import('@renderer/views/device-management/device-admin-permission.vue'),
  },
  {
    path: 'device-form-design',
    name: 'bench_deviceFormDesign',
    component: () => import('@renderer/views/device-management/device-form-design.vue'),
  },
  {
    path: 'device-form-design',
    name: 'bench_deviceNoPermissions',
    component: () => import('@renderer/views/device-management/noPermissions.vue'),
  },
];

const partnerRoute = [
  {
    path: 'partner-list',
    name: 'bench_partnerList',
    component: () => import('@renderer/views/partner/partner-list.vue'),
  },
  {
    path: 'partner-contacts',
    name: 'bench_partnerContacts',
    component: () => import('@renderer/views/partner/partner-contacts.vue'),
  },
  {
    path: 'partner-admin',
    name: 'bench_partnerAdmin',
    component: () => import('@renderer/views/partner/partner-admin.vue'),
  },
  {
    path: 'partner-admin-contacts',
    name: 'bench_partnerAdminContacts',
    component: () => import('@renderer/views/partner/partner-admin-contacts.vue'),
  },
  {
    path: 'partner-admin-employee',
    name: 'bench_partnerAdminEmployee',
    component: () => import('@renderer/views/partner/partner-admin-employee.vue'),
  },
  {
    path: 'partner-admin-permission',
    name: 'bench_partnerAdminPermission',
    component: () => import('@renderer/views/partner/partner-admin-permission.vue'),
  },
  {
    path: 'partner-form-design',
    name: 'bench_partnerFormDesign',
    component: () => import('@renderer/views/partner/partner-form-design.vue'),
  },
  {
    path: 'contacts-form-design',
    name: 'bench_partnerContactsFormDesign',
    component: () => import('@renderer/views/partner/contacts-form-design.vue'),
  },
];

const supplierRoute = [
  {
    path: 'supplier-list',
    name: 'bench_supplierList',
    component: () => import('@renderer/views/supplier/supplier-list.vue'),
  },
  {
    path: 'supplier-admin',
    name: 'bench_supplierAdmin',
    component: () => import('@renderer/views/supplier/supplier-admin.vue'),
  },
  {
    path: 'supplier-admin-contact',
    name: 'bench_supplierAdminContact',
    component: () => import('@renderer/views/supplier/supplier-admin-contact.vue'),
  },
  {
    path: 'supplier-admin-employee',
    name: 'bench_supplierAdminEmployee',
    component: () => import('@renderer/views/supplier/supplier-admin-employee.vue'),
  },
  {
    path: 'supplier-admin-permission',
    name: 'bench_supplierAdminPermission',
    component: () => import('@renderer/views/supplier/supplier-admin-permission.vue'),
  },
  {
    path: 'supplier-form-design',
    name: 'bench_supplierFormDesign',
    component: () => import('@renderer/views/supplier/supplier-form-design.vue'),
  },
];

const customerRoute = [
  {
    path: 'customer-list',
    name: 'bench_customerList',
    component: () => import('@renderer/views/customer/customer-list.vue'),
  },
  {
    path: 'customer-admin',
    name: 'bench_customerAdmin',
    component: () => import('@renderer/views/customer/customer-admin.vue'),
  },
  {
    path: 'customer-admin-contact',
    name: 'bench_customerAdminContact',
    component: () => import('@renderer/views/customer/customer-admin-contact.vue'),
  },
  {
    path: 'customer-admin-employee',
    name: 'bench_customerAdminEmployee',
    component: () => import('@renderer/views/customer/customer-admin-employee.vue'),
  },
  {
    path: 'customer-admin-permission',
    name: 'bench_customerAdminPermission',
    component: () => import('@renderer/views/customer/customer-admin-permission.vue'),
  },
  {
    path: 'customer-form-design',
    name: 'bench_customerFormDesign',
    component: () => import('@renderer/views/customer/customer-form-design.vue'),
  },
];

const fengcaiRoutes = [
  {
    path: 'fengcai_list',
    name: 'bench_fengcai_list',
    component: () => import('@renderer/views/workBench/fengcai/list/index.vue'),
  },
  {
    path: 'fengcai_detail_view',
    name: 'fengcai_detail_view',
    component: () => import('@renderer/views/workBench/fengcai/detail/index.vue'),
  },
  {
    path: 'fengcai_admin_list',
    name: 'fengcai_admin_list',
    component: () => import('@renderer/views/workBench/fengcai/admin.vue'),
  },
  {
    path: 'fengcai_release_party',
    name: 'fengcai_release_party',
    component: () => import('@renderer/views/workBench/fengcai/release-party.vue'),
  },
  {
    path: 'fengcai_detail',
    name: 'fengcai_detail',
    component: () => import('@renderer/views/workBench/fengcai/details.vue'),
  },
];

const partnerAppRouters = [
  {
    path: 'partnerHome',
    name: 'partnerHome',
    component: () => import('@renderer/views/workBench/partner/partnerHome.vue'),
    children: [
      {
        path: 'index',
        name: 'partnerIndex',
        component: () => import('@renderer/views/workBench/partner/index.vue'),
      },
      {
        path: 'customer',
        name: 'partnerCustomer',
        component: () => import('@renderer/views/workBench/partner/customer.vue'),
      },
      {
        path: 'order',
        name: 'partnerOrder',
        component: () => import('@renderer/views/workBench/partner/order.vue'),
      },
      {
        path: 'statement',
        name: 'partnerStatement',
        component: () => import('@renderer/views/workBench/partner/statement.vue'),
      },
    ],
  },
];

export default [
  {
    path: '/workBenchIndex',
    name: 'workBenchIndex', // 工作台
    component: () => import('@renderer/views/workBench/index.vue'),
    children: [
      {
        path: 'promotionalpage',
        name: 'bench_promotionalpage', // 工作台
        component: () => import('@renderer/views/workBench/components/PromotionalpageShow.vue'),
      },
      {
        path: 'work_bench_album',
        name: 'work_bench_album',
        component: () => import('@renderer/views/square/friend-ablum/albumIndex.vue'),
      },
      {
        path: 'addAdvertSing',
        name: 'bench_add_advert_sing', // add广告工作台
        component: () => import('@renderer/views/workBench/advertSing/addAdvertSing.vue'),
      },
      {
        path: 'AdDetails',
        name: 'bench_ad_details', // 广告详情
        component: () => import('@renderer/views/workBench/advertSing/AdDetails.vue'),
      },
      {
        path: 'workBenchHome',
        name: 'bench_workBenchHome', // 工作台
        component: () => import('@renderer/views/workBench/workBenchHome/index.vue'),
      },
      {
        path: 'ringkolAdvertisement',
        name: 'ringkolAdvertisement', // 另可广告route-view
        component: () => import('@renderer/views/workBench/ringkol-advertisement/index.vue'),
      },
      {
        path: 'editRingkolAdvertisement',
        name: 'editRingkolAdvertisement', // 另可广告route-view
        component: () => import('@renderer/views/workBench/ringkol-advertisement/editRingkolAdvertisement.vue'),
      },
      {
        path: 'rkAdDetails',
        name: 'rkAdDetails', // 另可广告route-view
        component: () => import('@renderer/views/workBench/ringkol-advertisement/rkAdDetails.vue'),
      },

      {
        path: 'teamSting',
        name: 'bench_teamSting', // teamSting
        component: () => import('@renderer/views/workBench/teamSting/index.vue'),
      },
      {
        path: 'growth',
        name: 'Growth',
        component: () => import('@renderer/views/workBench/growth/index.vue'),
      },
      {
        path: 'growth-publish',
        name: 'GrowthPublish',
        component: () => import('@renderer/views/workBench/growth/publish.vue'),
      },
      {
        path: 'timeline',
        name: 'Timeline',
        component: () => import('@renderer/views/workBench/growth/timeline.vue'),
      },
      {
        path: 'introduce',
        name: 'Introduce',
        component: () => import('@renderer/views/workBench/introduce/index.vue'),
      },
      {
        path: 'introduce-publish',
        name: 'IntroducePublish',
        component: () => import('@renderer/views/workBench/introduce/publish.vue'),
      },
      {
        path: 'introduce-detail',
        name: 'IntroduceDetail',
        component: () => import('@renderer/views/workBench/introduce/detail.vue'),
      },
      {
        path: 'workBenchEnterprise',
        name: 'bench_workBenchEnterprise', // 管理工作台
        component: () => import('@renderer/views/workBench/enterprise/index.vue'),
      },
      {
        path: 'merchant-settlement-apply',
        name: 'MerchantSettlementJoinApply', // 商户入网申请
        component: () => import('@renderer/views/workBench/receipt/onboarding/JoinApply.vue'),
      },
      {
        path: 'notice',
        name: 'notice', // 公告
        component: () => import('@renderer/views/notice/index.vue'),
      },
      {
        path: 'policy-express',
        name: 'PolicyExpress', // 政策列车
        component: () => import('@renderer/views/policy-express/index.vue'),
      },
      {
        path: 'policy-express-setting',
        name: 'PolicyExpressSetting', // 直通车管理
        component: () => import('@renderer/views/policy-express/setting/PolicyExpressSetting.vue'),
      },
      {
        path: 'policy-express-create', // 政策创建
        name: 'policyExpressSettingCreate',
        component: () => import('@renderer/views/policy-express/setting/PolicyExpressCreate.vue'),
      },
      {
        path: 'policy-express-edit', // 政策再次发布
        name: 'policyExpressEdit',
        component: () => import('@renderer/views/policy-express/setting/PolicyExpressEdit.vue'),
      },
      {
        path: 'policy-express-draft-edit', // 政策草稿编辑
        name: 'policyExpressSettingDraftEdit',
        component: () => import('@renderer/views/policy-express/setting/PolicyExpressDraft.vue'),
      },
      {
        path: 'policy-express-review', // 政策草稿编辑
        name: 'policyExpressReview',
        component: () => import('@renderer/views/policy-express/setting/policyReview.vue'),
      },
      {
        path: 'policy-analysis-review', // 政策草稿编辑
        name: 'analysisReview',
        component: () => import('@renderer/views/policy-express/setting/analysisReview.vue'),
      },
      {
        path: 'policy-express-analysis', // 解读创建
        name: 'policyExpressSettingAnalysis',
        component: () => import('@renderer/views/policy-express/setting/PolicyExpressAnalysis.vue'),
      },
      {
        path: 'analysis-edit', // 解读编辑
        name: 'analysisEdit',
        component: () => import('@renderer/views/policy-express/setting/analysisEdit.vue'),
      },
      {
        path: 'analysis-draft-edit', // 解读草稿编辑
        name: 'analysisDraftEdit',
        component: () => import('@renderer/views/policy-express/setting/analysisDraftEdit.vue'),
      },
      {
        path: 'policy-express-detail',
        name: 'policyExpressDetail', // 直通车详情
        component: () => import('@renderer/views/policy-express/setting/PolicyExpressDetail.vue'),
      },
      {
        path: 'analysis-express-detail',
        name: 'analysisExpressDetail', // 解读详情
        component: () => import('@renderer/views/policy-express/setting/analysisExpressDetail.vue'),
      },
      {
        path: 'policy-analysis-list',
        name: 'PolicyAnalysisList', // 政策分析列表
        component: () => import('@renderer/views/policy-express/viewer/PolicyAnalysisList.vue'),
      },
      {
        path: 'policy-express-list',
        name: 'PolicyExpressList', // 政策速递列表
        component: () => import('@renderer/views/policy-express/viewer/PolicyExpressList.vue'),
      },
      {
        path: 'policy-express-info',
        name: 'PolicyExpressInfo', // 政策速递详情
        component: () => import('@renderer/views/policy-express/viewer/PolicyExpressInfo.vue'),
      },
      {
        path: 'policy-analysis-info',
        name: 'PolicyAnalysisInfo', // 政策分析详情
        component: () => import('@renderer/views/policy-express/viewer/PolicyAnalysisInfo.vue'),
      },
      {
        path: 'noticeAgainCreate',
        name: 'noticeAgainCreate', // 公告
        component: () => import('@renderer/views/notice/noticeSetting/noticeAgainCreate.vue'),
      },
      {
        path: 'noticeCreate',
        name: 'noticeCreate', // 公告
        component: () => import('@renderer/views/notice/noticeSetting/noticeCreate.vue'),
      },
      {
        path: 'noticeDraft',
        name: 'noticeDraft', // 公告
        component: () => import('@renderer/views/notice/noticeSetting/noticeDraft.vue'),
      },
      {
        path: 'noticeEdit',
        name: 'noticeEdit', // 公告
        component: () => import('@renderer/views/notice/noticeSetting/noticeEdit.vue'),
      },
      {
        path: 'noticeDetail',
        name: 'noticeDetail', // 公告
        component: () => import('@renderer/views/notice/noticeSetting/noticeDetail.vue'),
      },
      {
        path: 'noticeDetailRead',
        name: 'noticeDetailRead', // 公告
        component: () => import('@renderer/views/notice/noticeSetting/noticeDetailRead.vue'),
      },
      {
        path: 'noticeDraftDetail',
        name: 'noticeDraftDetail', // 公告
        component: () => import('@renderer/views/notice/noticeSetting/noticeDraftDetail.vue'),
      },
      {
        path: 'noticeAddAdministrator',
        name: 'NoticeAddAdministrator', // 公告管理
        component: () => import('@renderer/views/notice/noticeSetting/noticeSetting.vue'),
      },
      {
        path: 'addHonor',
        name: 'bench_addHonor', // 组织设置下的添加荣誉
        component: () => import('@renderer/views/workBench/teamSting/addHonor.vue'),
      },
      {
        path: 'honorDetail',
        name: 'bench_honorDetail', // 组织设置下的添加荣誉
        meta: {
          keepAlive: false,
        },
        component: () => import('@renderer/views/workBench/teamSting/honorDetail.vue'),
      },
      {
        path: 'aboutour_back',
        name: 'bench_aboutour', // 关于我们
        component: () => import('@renderer/views/workBench/aboutour/index.vue'),
      },
      {
        path: 'aboutour',
        name: 'bench_aboutour', // 关于我们
        component: () => import('@renderer/views/workBench/aboutour/index.vue'),
      },
      // {
      //   path: "groupInfo",/workBenchIndex/member_manage
      //   name: "bench_groupInfo", // 工作台
      //   component: () => import("@renderer/views/workBench/groupInfo/index.vue"),
      // },
      {
        path: 'init_apply',
        name: 'bench_init_apply',
        component: () => import('@renderer/views/approve/init_apply/index.vue'),
      },
      {
        path: 'approve_center',
        name: 'bench_approve_center',
        component: () => import('@renderer/views/approve/approve_center/index.vue'),
      },
      {
        path: 'admin/flow',
        name: 'bench_flow',
        component: () => import('@renderer/views/approval/admin/flow.vue'),
      },
      {
        path: 'approvalDesgin',
        name: 'bench_approvalDesgin',
        component: () => import('@renderer/views/approval/admin/approvalDesgin.vue'),
      },
      {
        path: 'admin/approvalData',
        name: 'bench_approvalData',
        component: () => import('@renderer/views/approval/admin/approvalData.vue'),
      },
      {
        path: 'admin/handover',
        name: 'bench_handover',
        component: () => import('@renderer/views/approval/admin/handover.vue'),
      },
      {
        path: 'admin/derived-record',
        name: 'bench_derived-record',
        component: () => import('@renderer/views/approval/admin/derived-record.vue'),
      },
      {
        path: 'admin/administrator',
        name: 'bench_administrator',
        component: () => import('@renderer/views/approval/admin/administrator.vue'),
      },
      {
        path: 'free-form/form-design',
        name: 'bench_freeForm',
        component: () => import('@renderer/views/free-form/form-design.vue'),
      },

      {
        path: 'free-form/form-runtime',
        name: 'bench_freeFormRuntime',
        component: () => import('@renderer/views/free-form/form-runtime.vue'),
      },
      {
        path: 'free-form/form-detail',
        name: 'bench_freeFormDetail',
        component: () => import('@renderer/views/free-form/form-detail.vue'),
      },
      {
        path: 'approve_home',
        name: 'bench_approve_home', // 工作台下的审批
        component: () => import('@renderer/views/approve/approve_home/index.vue'),
      },
      {
        path: 'engineer_home',
        name: 'bench_engineer_home',
        component: () => import('@renderer/views/engineer/engineer_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/engineerIndex/engineer_home',
          title: '工程',
          icon: '',
          role: 'personal',
          affix: true,
        },
      },
      {
        path: 'engineer_set',
        name: 'bench_engineer_set',
        component: () => import('@renderer/views/engineer/engineer_set/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/engineerIndex/engineer_set',
          title: '工程设置',
          icon: '',
          role: 'personal',
        },
      },
      {
        path: 'engineer_admin',
        name: 'bench_engineer_admin',
        component: () => import('@renderer/views/engineer/engineer_admin/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/engineerIndex/engineer_admin',
          title: '工程管理后台',
          icon: '',
          role: 'personal',
        },
      },
      {
        path: 'service_home',
        name: 'bench_service_home',
        component: () => import('@renderer/views/service/service_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/serviceIndex/service_home',
          title: '服务',
          icon: '',
          role: 'personal',
          affix: true,
        },
      },
      {
        path: 'service_admin',
        name: 'bench_service_admin',
        component: () => import('@renderer/views/service/service_admin/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/serviceIndex/service_admin',
          title: '服务管理后台',
          icon: '',
          role: 'personal',
        },
      },
      // {
      //   path: "nicheHome",
      //   name: "bench_nicheHome",
      //   component: () => import("@renderer/views/niche/index.vue"),
      // },
      // {
      //   path: "nicheAdmin",
      //   name: "bench_nicheAdmin",
      //   component: () => import("@renderer/views/niche/admin.vue"),
      // },
      // {
      //   path: "nicheExtend",
      //   name: "bench_nicheExtend",
      //   component: () => import("@renderer/views/niche/extend.vue"),
      // },
      // {
      //   path: "nicheDraft",
      //   name: "bench_nicheDraft",
      //   component: () => import("@renderer/views/niche/draft.vue"),
      // },
      ...nicheRoute,
      {
        path: 'customerServiceList', // 客服应用
        name: 'bench_customerServiceList',
        component: () => import('@renderer/views/customerService/content.vue'),
      },
      {
        path: 'admin',
        name: 'bench_admin',
        component: () => import('@renderer/views/business/admin.vue'),
      },
      {
        path: 'businessManage',
        name: 'bench_businessManage',
        component: () => import('@renderer/views/business/businessManage.vue'),
      },
      {
        path: 'businessExamine',
        name: 'bench_businessExamine',
        component: () => import('@renderer/views/business/businessExamine.vue'),
      },
      ...partnerRoute,
      ...deviceRoute,
      ...supplierRoute,
      ...customerRoute,
      ...fengcaiRoutes,
      ...partnerAppRouters,
      // cbd
      {
        path: 'cbd_number',
        name: 'bench_cbd_number',
        component: () => import('@renderer/views/cbd/member_number/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/cbdIndex/cbd_number',
          title: '数字CBD',
          icon: '',
          role: 'personal',
          affix: true,
        },
      },
      {
        path: 'cbd_leaflets',
        name: 'bench_cbd_leaflets',
        component: () => import('@renderer/views/cbd/member_number/panel/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/cbdIndex/cbd_leaflets',
          title: '数字CBD',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'cbd_home',
        name: 'bench_cbd_home',
        component: () => import('@renderer/views/cbd/member_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/cbdIndex/cbd_home',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'cbd_manage',
        name: 'bench_cbd_manage',
        component: () => import('@renderer/views/cbd/member_manage/form-design.vue'),
        meta: {
          hidden: true,
          parentPath: '/cbdIndex/cbd_manage',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'cbd_manage_unit',
        name: 'bench_cbd_manage_unit',
        component: () => import('@renderer/views/cbd/member_manage/form-design-unit.vue'),
        meta: {
          hidden: true,
          parentPath: '/cbdIndex/cbd_manage',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'cbd_ebook',
        name: 'bench_cbd_ebook',
        component: () => import('@renderer/views/cbd/member_number/components/ebook.vue'),
        meta: {
          hidden: true,
          parentPath: '/politicsIndex/cbd_ebook',
          title: '刊物',
          icon: 'document',
          role: 'personal',
          affix: false,
        },
      },

      // 政企
      {
        path: 'politics_number',
        name: 'bench_politics_number',
        component: () => import('@renderer/views/politics/member_number/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/politicsIndex/politics_number',
          title: '数字城市',
          icon: '',
          role: 'personal',
          affix: true,
        },
      },
      {
        path: 'politics_leaflets',
        name: 'bench_politics_leaflets',
        component: () => import('@renderer/views/politics/member_number/panel/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/politicsIndex/politics_leaflets',
          title: '数字城市',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'politics_home',
        name: 'bench_politics_home',
        component: () => import('@renderer/views/politics/member_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/politicsIndex/politics_home',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'politics_manage',
        name: 'bench_politics_manage',
        component: () => import('@renderer/views/politics/member_manage/form-design.vue'),
        meta: {
          hidden: true,
          parentPath: '/politicsIndex/politics_manage',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'politics_manage_unit',
        name: 'bench_politics_manage_unit',
        component: () => import('@renderer/views/politics/member_manage/form-design-unit.vue'),
        meta: {
          hidden: true,
          parentPath: '/politicsIndex/politics_manage',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },

      // 1.9迭代，新增商机、名录、活动、会刊
      {
        path: 'politics_rich',
        name: 'bench_politics_rich',
        component: () => import('@renderer/views/politics/member_number/components/rich-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/politicsIndex/politics_rich',
          title: '商机',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'politics_name',
        name: 'bench_politics_name',
        component: () => import('@renderer/views/politics/member_number/components/name-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/politicsIndex/politics_name',
          title: '名录',
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },

      // 社群
      {
        path: 'association_number',
        name: 'bench_association_number',
        component: () => import('@renderer/views/association/member_number/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/associationIndex/association_number',
          title: '数字城市',
          icon: '',
          role: 'personal',
          affix: true,
        },
      },
      {
        path: 'association_leaflets',
        name: 'bench_association_leaflets',
        component: () => import('@renderer/views/association/member_number/panel/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/associationIndex/association_leaflets',
          title: '数字城市',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'association_home',
        name: 'bench_association_home',
        component: () => import('@renderer/views/association/member_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/associationIndex/association_home',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'association_manage',
        name: 'bench_association_manage',
        component: () => import('@renderer/views/association/member_manage/form-design.vue'),
        meta: {
          hidden: true,
          parentPath: '/associationIndex/association_manage',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'association_manage_unit',
        name: 'bench_association_manage_unit',
        component: () => import('@renderer/views/association/member_manage/form-design-unit.vue'),
        meta: {
          hidden: true,
          parentPath: '/associationIndex/association_manage',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },

      // 1.9迭代，新增商机、名录、活动、会刊
      {
        path: 'association_rich',
        name: 'bench_association_rich',
        component: () => import('@renderer/views/association/member_number/components/rich-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/associationIndex/association_rich',
          title: '商机',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'association_name',
        name: 'bench_association_name',
        component: () => import('@renderer/views/association/member_number/components/name-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/associationIndex/association_name',
          title: '名录',
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'association_ebook',
        name: 'bench_association_ebook',
        component: () => import('@renderer/views/association/member_number/components/ebook.vue'),
        meta: {
          hidden: true,
          parentPath: '/associationIndex/association_ebook',
          title: '刊物',
          icon: 'document',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'association_active',
        name: 'bench_association_active',
        component: () => import('@renderer/views/association/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/associationIndex/association_active',
          title: '活动',
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      // 高校
      {
        path: 'uni_number',
        name: 'bench_uni_number',
        component: () => import('@renderer/views/uni/member_number/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/uniIndex/uni_number',
          title: '数字高校',
          icon: '',
          role: 'personal',
          affix: true,
        },
      },
      {
        path: 'uni_leaflets',
        name: 'bench_uni_leaflets',
        component: () => import('@renderer/views/uni/member_number/panel/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/uniIndex/uni_leaflets',
          title: '数字高校',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'uni_home',
        name: 'bench_uni_home',
        component: () => import('@renderer/views/uni/member_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/uniIndex/uni_home',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'uni_manage',
        name: 'bench_uni_manage',
        component: () => import('@renderer/views/uni/member_manage/form-design.vue'),
        meta: {
          hidden: true,
          parentPath: '/uniIndex/uni_manage',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'uni_manage_unit',
        name: 'bench_uni_manage_unit',
        component: () => import('@renderer/views/uni/member_manage/form-design-unit.vue'),
        meta: {
          hidden: true,
          parentPath: '/uniIndex/uni_manage',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },

      // 1.9迭代，新增商机、名录、活动、会刊
      {
        path: 'uni_rich',
        name: 'bench_uni_rich',
        component: () => import('@renderer/views/uni/member_number/components/rich-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/uniIndex/uni_rich',
          title: '商机',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'uni_name',
        name: 'bench_uni_name',
        component: () => import('@renderer/views/uni/member_number/components/name-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/uniIndex/uni_name',
          title: '名录',
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'uni_ebook',
        name: 'bench_uni_ebook',
        component: () => import('@renderer/views/uni/member_number/components/ebook.vue'),
        meta: {
          hidden: true,
          parentPath: '/uniIndex/uni_ebook',
          title: '刊物',
          icon: 'document',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'uni_active',
        name: 'bench_uni_active',
        component: () => import('@renderer/views/uni/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/uniIndex/uni_active',
          title: '活动',
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },

      {
        path: 'activityList',
        name: 'bench_activityList',
        component: () => import('@renderer/views/activity/activityListInvolved.vue'),
      },
      {
        path: 'activityAdmin',
        name: 'bench_activityAdmin',
        component: () => import('@renderer/views/activity/activityAdmin.vue'),
      },
      {
        path: 'activityAdminList',
        name: 'bench_activityAdminList',
        component: () => import('@renderer/views/activity/activityAdminList.vue'),
      },
      {
        path: 'activityListCreated',
        name: 'bench_activityListCreated',
        component: () => import('@renderer/views/activity/activityListCreated.vue'),
      },
      {
        path: 'activityListDraft',
        name: 'bench_activityListDraft',
        component: () => import('@renderer/views/activity/activityListDraft.vue'),
      },
      {
        path: 'activityDetail/:id',
        name: 'bench_activityDetail',
        component: () => import('@renderer/views/activity/activityDetail.vue'),
      },
      {
        path: 'activityParticipantDetail/:id',
        name: 'bench_activityParticipantDetail',
        component: () => import('@renderer/views/activity/activityParticipantDetail.vue'),
      },
      {
        path: 'ablum-view/ablum-detail',
        name: 'bench_ablumDetail',
        component: () => import('@renderer/views/activity/manage/album/albumViewDetail.vue'),
      },
      {
        path: 'activityEdit/:id?',
        name: 'bench_activityEdit',
        component: () => import('@renderer/views/activity/activityEdit.vue'),
      },
      {
        path: 'politics_ebook',
        name: 'bench_politics_ebook',
        component: () => import('@renderer/views/politics/member_number/components/ebook.vue'),
        meta: {
          hidden: true,
          parentPath: '/politicsIndex/politics_ebook',
          title: '刊物',
          icon: 'document',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'politics_active',
        name: 'bench_politics_active',
        component: () => import('@renderer/views/politics/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/politicsIndex/politics_active',
          title: '活动',
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'association_ebook',
        name: 'bench_association_ebook',
        component: () => import('@renderer/views/association/member_number/components/ebook.vue'),
        meta: {
          hidden: true,
          parentPath: '/associationIndex/association_ebook',
          title: '刊物',
          icon: 'document',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'association_active',
        name: 'bench_association_active',
        component: () => import('@renderer/views/association/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/associationIndex/association_active',
          title: '活动',
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      // {
      //   path: "member_activityDetailLayout/:id",
      //   name: "bench_member_activityDetailLayout",
      //   component: () => import("@renderer/views/activity/activityDetail.vue"),
      // },
      {
        path: 'member_number',
        name: 'bench_member_number',
        component: () => import('@renderer/views/member/member_number/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/memberIndex/member_number',
          title: '数字商协',
          icon: '',
          role: 'personal',
          affix: true,
        },
      },
      {
        path: 'member_leaflets',
        name: 'bench_member_leaflets',
        component: () => import('@renderer/views/member/member_number/panel/leaflets-panel.vue'),
        meta: {
          hidden: true,
          parentPath: '/memberIndex/member_leaflets',
          title: '用另可数字商协，带领全体会员狂奔数字时代',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'member_home',
        name: 'bench_member_home',
        component: () => import('@renderer/views/member/member_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/memberIndex/member_home',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'member_manage',
        name: 'bench_member_manage',
        component: () => import('@renderer/views/member/member_manage/form-design.vue'),
        meta: {
          hidden: true,
          parentPath: '/memberIndex/member_manage',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'member_manage_unit',
        name: 'bench_member_manage_unit',
        component: () => import('@renderer/views/member/member_manage/form-design-unit.vue'),
        meta: {
          hidden: true,
          parentPath: '/memberIndex/member_manage',
          title: '会员管理',
          icon: '',
          role: 'personal',
          affix: false,
        },
      },
      // 1.9迭代，新增商机、名录、活动、会刊
      {
        path: 'member_rich',
        name: 'bench_member_rich',
        component: () => import('@renderer/views/member/member_number/components/rich-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/memberIndex/member_rich',
          title: '会员商机',
          icon: 'rich',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'member_name',
        name: 'bench_member_name',
        component: () => import('@renderer/views/member/member_number/components/name-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/memberIndex/member_name',
          title: '会员名录',
          icon: 'name',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'member_ebook',
        name: 'bench_member_ebook',
        component: () => import('@renderer/views/member/member_number/components/ebook.vue'),
        meta: {
          hidden: true,
          parentPath: '/memberIndex/member_ebook',
          title: '会刊',
          icon: 'document',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'member_active',
        name: 'bench_member_active',
        component: () => import('@renderer/views/member/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/memberIndex/member_active',
          title: '活动',
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'activity_active_comp',
        name: 'activity_active_comp',
        component: () => import('@renderer/views/member/member_number/components/active-comp.vue'),
        meta: {
          hidden: true,
          parentPath: '/memberIndex/activity_active_comp',
          title: '活动',
          icon: 'active',
          role: 'personal',
          affix: false,
        },
      },
      {
        path: 'pb_list',
        name: 'bench_pb_list', // 云盘route-view
        component: () => import('@renderer/views/workBench/pb/list/index.vue'),
      },
      {
        path: 'pb_detail/:id',
        name: 'bench_pb_detail', // 云盘route-view
        component: () => import('@renderer/views/workBench/pb/detail/index.vue'),
      },
      // {
      //   path: "member_activityDetailLayout/:id",
      //   name: "bench_member_activityDetailLayout",
      //   component: () => import("@renderer/views/activity/activityDetail.vue"),
      // },
      {
        path: 'admin_list',
        name: 'admin_list',
        component: () => import('@renderer/views/workBench/pb/admin.vue'),
      },
      {
        path: 'release_party',
        name: 'release_party',
        component: () => import('@renderer/views/workBench/pb/release-party.vue'),
      },
      {
        path: 'pb_detail',
        name: 'pb_detail',
        component: () => import('@renderer/views/workBench/pb/details.vue'),
      },
      {
        path: 'work_bench_iframe/:iframeOnlyId',
        name: 'work_bench_iframe',
        component: () => import('@renderer/views/workBench/iframe/index.vue'),
      },
      {
        path: 'work_bench_webview/:webviewOnlyId',
        name: 'work_bench_webview',
        component: () => import('@renderer/views/workBench/webview/index.vue'),
      },
    ],
  },
  {
    path: '/im_policy',
    name: 'im_policy', // 工作台
    component: () => import('@renderer/views/policy-express/im-review/index.vue'),
    children: [
      {
        path: 'im_policy_review',
        name: 'IMPolicyReview',
        component: () => import('@renderer/views/policy-express/im-review/imPolicyReview.vue'),
      },
      {
        path: 'policy-express-info',
        name: 'IMPolicyExpressInfo', // 政策速递详情
        component: () => import('@renderer/views/policy-express/viewer/PolicyExpressInfo.vue'),
      },
      {
        path: 'policy-analysis-info',
        name: 'IMPolicyAnalysisInfo', // 政策分析详情
        component: () => import('@renderer/views/policy-express/viewer/PolicyAnalysisInfo.vue'),
      },
    ],
  },
];
