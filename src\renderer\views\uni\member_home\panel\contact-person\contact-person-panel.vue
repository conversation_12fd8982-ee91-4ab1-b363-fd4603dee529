<template>
  <div class="container">
    <div class="su-header">
      <div class="more-search">
        <div class="in-box">
          <t-input
            v-model="formData.contacts_name"
            :placeholder="t('ebook.ssllr')"
            :maxlength="50"
            clearable
            style="width: 304px"
            @change="onSearch"
          />
        </div>
        <div :class="{ 'f-icon': true, factive: paramsSuper }" @click="showFilter">
          <iconpark-icon name="iconscreen" class="iconscreen"></iconpark-icon>
        </div>
      </div>
      <div class="more-right">
        <t-button theme="default" variant="base" @click="onSetWay" class="fw600 default_icon">
          <template #icon>
            <iconpark-icon name="iconsetUp" class="iconset mr-4px" style="color: #828DA5; "></iconpark-icon>
          </template>
          设置联络方式
        </t-button>
        <t-button theme="primary" variant="base" @click="onAdd">
          <template #icon>
            <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
          </template>
          {{ t("ebook.alrr") }}
        </t-button>
      </div>
    </div>

    <div class="body">
      <div v-if="paramsSuper" class="filter-res">
        <div class="tit">{{ t("approval.approval_data.sures") }}</div>
        <div v-if="formData.group_id" class="stat te">
          <span> {{ t("ebook.fz2") }} {{ formData.group_text }}</span>
          <span class="close2" @click="clearStatus">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div class="icon" @click="clearFilters">
          <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
          <a>{{ t("approval.approval_data.clearFilters") }}</a>
        </div>
      </div>
      <div class="body-contents">
        <div class="table" :class="handClass">
          <t-table row-key="id" :columns="memberColumns" :pagination="null" :data="memberData">
            <template #operate="{ row }">
              <span class="operates">
                <t-link
                  theme="primary"

                  hover="color"
                  class="operates-item"
                  @click="onSetContactrow(row)"
                >
                  设置
                </t-link>
                <!-- <t-link
                  theme="primary"
                  hover="color"
                  class="operates-item"
                  @click="setGroupRun(row)"
                >
                  {{ t("ebook.grs") }}
                </t-link> -->
                <t-link
                  theme="primary"
                  hover="color"
                  class="operates-item"
                  style="color: #d54941"
                  @click="delItem(row)"
                >
                  {{ t("ebook.del") }}
                </t-link>
              </span>
            </template>
            <template #main="{ row }">
              <div class="main-box">
                <kyy-avatar
                  round-radius
                  avatar-size="32px"
                  :image-url="row.avatar"
                  :user-name="row.contacts_name"
                  style="margin-right: 12px"
                />
                <span>{{ row.contacts_name }}</span>
              </div>
            </template>
            <template #describe="{ row }">
              <t-tooltip v-if="row.describe" placement="top">
                <template #content>{{row.describe}}</template>
                <div  class="describe line-2">
                  {{row.describe}}
                </div>
              </t-tooltip>
              <div v-else>--</div>
            </template>

            <template #photo="{ row }">
              <t-image v-if="row.photo" class="logoContact" :src="getSrcThumbnail(row.photo)" fit="cover">
                <template #loading>
                  <img class="logoContact" src="@renderer/assets/digital/icon/avatar_default.png" />
                </template>
                <template #error>
                  <img class="logoContact" src="@renderer/assets/digital/icon/avatar_default.png" />
                </template>
              </t-image>
              <span v-else>--</span>
            </template>
            <template #group="{ row }">
              <div class="line-1">
                <MyTooltipComp :text="row.group_info?.map((item) => item.group_name).join('、') || '--'"></MyTooltipComp>
              </div>
            </template>

            <!-- <template #empty>
              <div class="empty">
                <noData :tip="t('ebook.nlllr')" :name="'no-friend-list'" />
              </div>
            </template> -->
          </t-table>
          <div class="empty" v-if="loaded && !memberData.length">
            <noData :tip="t('ebook.zug1')" :name="'no-friend-list'"> </noData>
          </div>

          <div class="empty" v-if="!loaded && loading">
            <t-loading />
          </div>
        </div>
      </div>
    </div>
  </div>

  <t-drawer v-model:visible="filterVisible" :close-btn="true" size="472px" :header="t('approval.approval_data.sur')">
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title">{{ t("ebook.fz") }}</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.group_id"
            clearable
            :options="statusOptions"
            style="width: 422px"
            :popupProps="{
              overlayInnerStyle: {
                width: '422px',
              },
            }"
            @change="statusChange"
            ><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="foot">
        <div class="btn1" @click="footerCla">
          {{ t("approval.approval_data.cl") }}
        </div>
        <div v-if="paramsSuperFoot" class="btn3" @click="getDataRunDr">
          {{ t("approval.approval_data.cm") }}
        </div>
        <div v-else class="btn2">
          {{ t("approval.approval_data.cm") }}
        </div>
      </div>
    </template>
  </t-drawer>
  <SetContactwayModal ref="setWayRef" :origin="originType.Uni" :teamId="currentTeamId"/>
  <SetContactrowModal ref="setContactrowRef" :origin="originType.Uni" :teamId="currentTeamId" @onReload="onSearch"/>

  <group-owner-select
    ref="groupOwnerSelectRef"
    :single="false"
    :header="t('ebook.alrr')"
    @send-user-data="sendUserData"
    :dis-data="disData"
  />
  <group-set :teamId="currentTeamId" ref="groupSetRef" :type="2" @change="onSearch" />
</template>

<script lang="ts" setup>
import noData from "@renderer/components/common/Empty.vue";
import { ref, reactive, watch, computed } from "vue";
import { useI18n } from "vue-i18n";
import { getResponseResult } from "@renderer/utils/myUtils";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import groupOwnerSelect from "@renderer/views/uni/member_home/panel/group-panel/group-owner-select.vue";
import {
  gcontactsdelete,
  getGovernmentcontactslist,
  getGovernmentlist,
  governmentcontactsCreate,
  getAppStaffAxios,
} from "@renderer/api/uni/api/businessApi";
import to from "await-to-js";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import groupSet from "./group-set.vue";

import SetContactwayModal from '@renderer/views/digital-platform/modal/set-contactway-modal.vue'
import SetContactrowModal from '@renderer/views/digital-platform/modal/set-contactrow-modal.vue'
import { originType } from '@renderer/views/digital-platform/utils/constant'; 
import { getSrcThumbnail } from "@renderer/views/message/service/msgUtils";
import MyTooltipComp from "@renderer/components/engineer/components/MyTooltipComp.vue";


const store = useUniStore();
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();

const { t } = useI18n();
const loading = ref(false);
const loaded = ref(false);
const statusOptions = ref([]);
const getGroupList = async () => {
  try {
    const [err, res] = await to(getGovernmentlist(currentTeamId.value));
    if (err) {
      return;
    }
    const { data }: any = res;
    statusOptions.value = data?.data?.list || [];
    for (const st of statusOptions.value) {
      st.label = st.group_name;
      st.value = st.id;
    }
    statusOptions.value.push({ label: t("ebook.wfz"), value: "0" });
  } catch (error) {}
};
const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
});
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => props.platform || route.query?.platform);


const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  }
  if (platformCpt.value === platform.digitalWorkbench) {
    return route.query?.teamId || 0;
  }
  return getUniTeamID();
});

const formData = reactive({
  contacts_name: "",
  group_id: undefined,
  group_text: undefined,
});
const memberColumns = ref([
  { colKey: "main", title: t("ebook.llr"), width: "200px", ellipsis: true },
  { colKey: "group", title: t("ebook.fz"), width: "240px", ellipsis: false },
  { colKey: "photo", title: '照片', width: "144px", ellipsis: false },
  { colKey: "describe", title: '职责描述', width: "240px" },
  { colKey: "operate", title: t("member.impm.app_7"), width: '120px' },
]);
const memberData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    onSearch();
  },
});

const onSearch = () => {
  const params = {
    ...formData,
  };
  getMemberList(params);
};

const getMemberList = async (params) => {
  loaded.value = false;
  loading.value = true;
  try {
    let result = await getGovernmentcontactslist(params, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    memberData.value = result.data.list;
    pagination.total = result.data.total;
    console.log(memberData.value);
    loaded.value = true;
    loading.value = false;
  } catch (error) {
    loaded.value = true;
    loading.value = false;
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};
const initData = () => {
  getMemberList({});
  getGroupList();
};

onMountedOrActivated(() => {
  initData();
});

const filterVisible = ref(false);
const paramsSuper = computed(() => formData.group_id);
const paramsSuperFoot = computed(() => paramsTemp.value.group_id);
const showFilter = () => {
  filterVisible.value = true;
};
const paramsTemp = ref({
  group_id: undefined,
  group_text: undefined,
});
const statusChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.group_text = ctx.option.label;
};

const clearFilters = () => {
  formData.group_id = undefined;
  formData.group_text = undefined;
  paramsTemp.value.group_id = undefined;
  paramsTemp.value.group_text = undefined;
  onSearch();
};
const clearStatus = () => {
  formData.group_id = undefined;
  paramsTemp.value.group_text = undefined;
  onSearch();
};

const getDataRunDr = () => {
  filterVisible.value = false;
  formData.group_id = paramsTemp.value.group_id;
  formData.group_text = paramsTemp.value.group_text;
  onSearch();
};
const footerCla = () => {
  filterVisible.value = false;
  paramsTemp.value.group_id = undefined;
  paramsTemp.value.group_text = undefined;
};

const handClass = computed(() => {
  let claName = "";
  if (paramsSuper.value) {
    claName = "paramsSuper";
  } else {
    claName = "no-paramsSuper";
  }
  return claName;
});

const delItem = (row) => {
  const confirmDia = DialogPlugin({
    header: t("ebook.dllr"),
    theme: "info",
    body: t("ebook.dllrt"),
    closeBtn: null,
    confirmBtn: t("ebook.del"),
    className: "delmode",
    onConfirm: async () => {
      // 删除字段操作
      let res = null;
      try {
        res = await gcontactsdelete(row.id, currentTeamId.value);
        res = getResponseResult(res);
        MessagePlugin.success(t("ebook.delsucc"));
        if (!res) return;
        getMemberList({});
      } catch (error) {
        MessagePlugin.error(error.message);
      }
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};
const optionsMembers = ref([]);
const getAppMemberList = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getAppStaffAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      optionsMembers.value = result.data;
      resolve("success");
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};
getAppMemberList();
const groupOwnerSelectRef = ref(null);
const disData = ref([]);
const onAdd = () => {
  disData.value = memberData.value.map((item) => item.related_id);
  groupOwnerSelectRef.value.ownerOpen(optionsMembers.value, []);
};
const sendUserData = (e) => {
  console.log(e);
  if (!e.length) {
    return;
  }
  const params = {
    main_contacts: [],
  };
  for (const item of e) {
    if (!disData.value.includes(item.idStaff)) {
      params.main_contacts.push({
        related_id: item.idStaff,
        card_id: `$${item.idStaff}`,
        openid: item.openId,
      });
    }
  }
  governmentcontactsCreate(params, currentTeamId.value).then(() => {
    MessagePlugin.success(t("ebook.asu"));
    onSearch();
  });
};
/**
 * 设置联络方式
 */
 const setWayRef = ref(null);
const onSetWay = () => {
  setWayRef.value.onOpen();
}


/**
 * 单个设置联络人的联络信息
 */
const setContactrowRef = ref(null);

const onSetContactrow = (row) => {
  setContactrowRef.value.onOpen(row);
}
const groupSetRef = ref(null);
const setGroupRun = (row) => {
  groupSetRef.value.setOpen(
    row.group_info?.map((item) => item.group_id),
    row.id,
  );
};
</script>

<style lang="less" scoped>
.iconadd {
  font-size: 20px;
}
.main-box {
  display: flex;
  align-items: center;
}
:deep(.no-paramsSuper .t-table__content) {
  max-height: calc(100vh - 126px) !important;
  overflow-x: hidden;
}

:deep(.paramsSuper .t-table__content) {
  max-height: calc(100vh - 176px) !important;
  overflow-x: hidden;
}

@import "../public.less";

.main_body {
  display: flex;
  flex-direction: column;
}
.body {
  // height: calc(100vh - 184px) !important;
  // &-content {
  //   background-color: @kyy_color_bg_light;
  //   height: fit-content;
  //   height: 100%;
  // }
}
.operates-item {
  display: flex;
  padding: 4px;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.operates-item:hover {
  border-radius: 4px;
  background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
}
.filter-res {
  align-items: center;
}
.te {
  margin-bottom: 0px !important;
}
:deep(.table .t-table__empty-row) {
  display: none;
}
.operates {
  gap: 8px;
}
:deep(.t-table tr:not(.t-table__empty-row):hover) {
  background-color: #f3f6fa !important;
}
</style>
