<template>
  <div class="content">
    <div v-show="isShowMenu" class="content-menu">
      <div class="expandbtn cursor" @click="onShowMenu">
        <!-- <iconpark-icon name="iconarrowright" class="icon"></iconpark-icon> -->
        <iconpark-icon name="iconarrowlift" class="icon"></iconpark-icon>
      </div>

      <t-tree
        :data="directorys"
        :on-active="onChangeTreeItem"
        expand-all
        :active-multiple="false"
        :actived="fistCkdValue"
        :keys="{
          label: 'directory_name',
          value: 'id',
          children: 'children',
        }"
        :hover="false"
        activable
        :transition="true"
        @change="onTreeChange"
      >
        <!-- <template #icon="{ node }">
          <t-icon
            v-if="node.getChildren() && !node.expanded"
            name="chevron-right"
          />
          <t-icon
            v-else-if="node.getChildren() && node.expanded"
            name="chevron-down"
          />

        </template> -->
      </t-tree>
    </div>
    <div v-show="!isShowMenu" class="content-unmenu">
      <div class="expandbtn cursor" @click="onShowMenu">
        <iconpark-icon name="iconarrowright" class="icon"></iconpark-icon>
        <!-- <iconpark-icon name="iconarrowlift" class="icon"></iconpark-icon> -->
      </div>
    </div>
    <div class="content-word">
      <WordComp
        v-if="currentArticleData"
        :data="currentArticleData"
        :directories="directoriesCpt"
        :module-id="props.id"
      />
      <HotComp v-else-if="[Flag.hotIssues].includes(flag)" :hots="hotIssuses" @select-item="onSelectHotItem" />

      <REmpty v-else />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { REmpty } from '@rk/unitPark';
import { otherDocumentListsAxios, getDocumentDetailAxios, getHotQuestionAxios, setClickDocumentAxios } from '../../api';
import WordComp from '../components/word-comp.vue';
import HotComp from '../components/hot-comp.vue';

// import { getResponseResult } from '../../utils';
import { MessagePlugin } from 'tdesign-vue-next';
import { isString } from 'lodash';
import { Flag } from '../common/enum';

const props = withDefaults(
  defineProps<{
    id: number;
    flag: string;
    wordId: number;
    directoryId: number;
    autoUpdate: string;
  }>(),
  {
    id: 0,
    flag: 'product', // product产品手册 hot_issues 热门问题 other 其它
    wordId: 0,
    directoryId: 0,
    autoUpdate: '',
  },
);

watch(
  () => props.autoUpdate,
  async (val) => {
    if (!val) return;
    console.log(val);
    console.log(props.wordId && props.directoryId);

    if (props.wordId && props.directoryId) {
      autoRequest();
    }
  },
);

watch(
  () => props.id,
  async (val: any) => {
    if (val && props.flag) {
      autoRequest();
    }
  },
);
const onSetContent = (res: any) => {
  const tags = res.detail?.tags_list;
  if (tags && tags.length > 0) {
    tags.map((v) => {
      try {
        v.content = JSON.parse(v.content)?.htmlContent;
      } catch (error) {
        console.log(error?.message);
      }
      return v;
    });
  }
};
const autoRequest = async () => {
  // getModuleList();
  currentArticleData.value = null;
  // fistCkdValue.value = [];

  await getModuleList();
  if (props.wordId && props.directoryId) {
    fistCkdValue.value = [`word-${props.wordId}-${props.directoryId}`];
    getDetailAxios(props.wordId).then((res: any) => {
      onSetContent(res);
      currentArticleData.value = {
        direct_id: Number(props.directoryId), // 直接目录id
        ...res.detail,
      };
    });
  } else {
    fistCkdValue.value = [];
  }

  if ([Flag.hotIssues].includes(props.flag)) {
    getHotList();
  }
};

const isShowMenu = ref(true);
const directorys = ref([]);

const fistCkdValue = ref([]);
// const directoryFlat = ref([]);

const currentArticleData = ref(null);
// const directories = ref([]);

const onSelectHotItem = (item) => {
  getDetailAxios(item.id).then((res: any) => {
    console.log(res.detail.directory_id, res.id);
    onSetContent(res);
    currentArticleData.value = {
      direct_id: res.detail.directory_id, // 直接目录id
      ...res.detail,
    };
    // 设置一下左侧栏菜单对焦
    fistCkdValue.value = [`word-${item.id}-${res.detail.directory_id}`];
  });
};
const onShowMenu = () => {
  isShowMenu.value = !isShowMenu.value;
};

const onTreeChange = (e) => {
  console.log(e);
};
const onChangeTreeItem = (val: any) => {
  console.log(fistCkdValue.value);
  console.log(val, 'valllllllllllll');
  if (val.length > 0) {
    if (isString(val[0]) && val[0].includes('word')) {
      fistCkdValue.value = val;
      const arr = val[0].split('-');
      if (arr && arr.length > 2) {
        setClickCountAxios(Number(arr[1]));
        getDetailAxios(Number(arr[1])).then((res: any) => {
          // const tags = res.detail?.tags_list;
          // if (tags && tags.length > 0) {
          //   tags.map((v) => {
          //     try {
          //       v.content = JSON.parse(v.content)?.htmlContent;
          //     } catch (error) {
          //       console.log(error?.message);
          //     }
          //     return v;
          //   });
          // }
          onSetContent(res);
          currentArticleData.value = {
            direct_id: Number(arr[2]), // 直接目录id
            ...res.detail,
          };
        });
      }
    }
  }
  // getNewJobsList(val[0]);

  // memberData.value = arr;
};

const setClickCountAxios = (id) =>
  // eslint-disable-next-line no-async-promise-executor
  new Promise(async (resolve, reject) => {
    let res = null;
    try {
      res = await setClickDocumentAxios({ id });
      // res = getResponseResult(res);
      if (!res) {
        reject();
        return;
      }

      resolve(res.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });

const getModuleList = () => {
  let res: any = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      res = await otherDocumentListsAxios({ flag: props.flag, id: props.id });
      // res = getResponseResult(res);
      if (!res) return;
      console.log(res);
      // modules.value = switchData(res?.data?.module_lists);
      onExchangeTree(res.data.list);

      directorys.value = res.data.list;
      console.log(directorys.value);
      resolve(res.data.list);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg) MessagePlugin.error(errMsg);
      reject();
    }
  });
};

// 获取热门问题前50条
const hotIssuses = ref([]);
const getHotList = async () => {
  let res: any = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      res = await getHotQuestionAxios();
      // res = getResponseResult(res);
      if (!res) {
        reject();
        return;
      }
      hotIssuses.value = res.data.hot_question_lists;
      resolve(res.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const getDetailAxios = (id) => {
  let res: any = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      res = await getDocumentDetailAxios({ id });
      // res = getResponseResult(res);
      if (!res) {
        reject();
        return;
      }

      resolve(res.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg) MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const onExchangeTree = (arr: Array<any>) => {
  arr.forEach((item) => {
    // item.id = item.id;
    if (item.document_lists && item.document_lists.length > 0) {
      item.children = [
        ...item.document_lists.map((doc) => ({
          id: `word-${doc.id}-${item.id}`, // 标识word文档类型，文档id, 该文档的直接上级目录id
          directory_name: `${doc.document_name}`,
        })),
        ...item.children,
      ];
    }
    if (item.children && item.children.length > 0) {
      onExchangeTree(item.children);
    }
  });
};

const directoriesCpt = computed(() => {
  if (currentArticleData.value) {
    console.log(currentArticleData.value.direct_id);
    const result = getParent(directorys.value, currentArticleData.value.direct_id);
    console.log(result);
    if (result) return result;
  }
  return [];
});

const getParent = (data2, nodeId2) => {
  let arrRes = [];
  if (data2.length === 0) {
    if (nodeId2) {
      arrRes.unshift(data2);
    }
    return arrRes;
  }
  let rev = (data, nodeId) => {
    for (let i = 0, length = data.length; i < length; i++) {
      let node = data[i];
      if (node.id === nodeId) {
        arrRes.unshift(node);
        // 查找到当前id,继续追随父级id
        rev(data2, node.parent_id); // 注意这里是传入的tree，不要写成data了，不然遍历的时候一直都是node.children,不是从最顶层开始遍历的
        break;
      } else {
        // 如果当前节点没有对应id,则追溯该子类是否有匹配项
        if (node.children) {
          rev(node.children, nodeId);
        }
      }
    }
    return arrRes;
  };
  arrRes = rev(data2, nodeId2);
  return arrRes;
};

onMounted(() => {
  console.log(props.wordId && props.directoryId);
  getModuleList().then(() => {
    if (props.wordId && props.directoryId) {
      fistCkdValue.value = [`word-${props.wordId}-${props.directoryId}`];
    } else {
      fistCkdValue.value = [];
    }

    if ([Flag.hotIssues].includes(props.flag)) {
      getHotList();
    }
  });
});
</script>

<style lang="less" scoped>
@import url('../common/public.less');
.content {
  display: flex;
  height: 100%;

  &-unmenu {
    flex: initial;
    width: 0;
    position: relative;
    .expandbtn {
      position: absolute;
      z-index: 2;
      left: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      width: 16px;
      height: 48px;
      border-radius: 24px;
      border: 1px solid var(--divider-kyy-color-divider-deep, #d5dbe4);
      background: var(--bg-kyy-color-bg-deepest, #eceff5);
      display: flex;
      align-items: center;
      .icon {
        color: #828da5;
        font-size: 16px;
      }
    }
  }
  &-menu {
    flex: initial;
    width: 256px;
    padding: 16px 8px;
    border-right: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
    background: var(--bg-kyy-color-bg-light, #fff);
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
    .expandbtn {
      visibility: hidden;
      position: sticky;
      z-index: 2;
      right: 0;
      top: 47%;
      bottom: 0;
      // margin: auto;
      margin-left: 96%;

      width: 16px;
      height: 48px;
      border-radius: 24px;
      border: 1px solid var(--divider-kyy-color-divider-deep, #d5dbe4);
      background: var(--bg-kyy-color-bg-deepest, #eceff5);
      display: flex;
      align-items: center;
      .icon {
        color: #828da5;
        font-size: 16px;
      }
    }
    &:hover {
      .expandbtn {
        visibility: visible;
      }
    }
  }
  &-word {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

:deep(.t-is-active) {
  border-radius: var(--kyy_radius_terr_item, 4px);
  background: var(--kyy_color_tree_bg_active, #e1eaff);
  color: var(--kyy_color_tree_text, #516082) !important;
}
:deep(.t-tree__item) {
  cursor: pointer;
  user-select: none;
  &:hover {
    background: var(--kyy_color_tree_bg_active, #e1eaff);
    border-radius: 4px;
    transition: all 0.25s linear;
  }
}
:deep(.t-tree) {
  margin-top: -44px;
}
:deep(.t-tree__icon) {
  margin-left: 4px;
}
// :deep(.t-tree--transition) .t-tree__label {
// 	transition: none;
// }

:deep(.t-tree--hoverable) .t-tree__label:not(.t-is-active):not(.t-is-checked):hover {
  // background-color: var(--td-bg-color-container-hover);
  background-color: none;
}

:deep(.t-tree__item) {
  display: flex;
}

:deep(.t-tree__label) {
  // max-width: 160px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
}
</style>
