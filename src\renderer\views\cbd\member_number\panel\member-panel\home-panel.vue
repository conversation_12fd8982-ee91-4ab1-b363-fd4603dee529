<template>
  <div class="home">
    <div class="bodyContent">
      <div class="bodyc">
        <div class="sticky">
          <div class="tabs">
            <div class="list">
              <div
                v-for="(tab, tabIndex) in tabs"
                :key="tabIndex"
                v-show="tab.isShow"
                :class="{ 'tabs-item': true, active: tab.value === 'home' }"
                @click="goTabNew(tab)"
              >
                <!-- <img :src="tab.icon" class="icon" /> -->

                <!-- 论坛 -->
                <template v-if="tab.uuid === 'digital_platform_forum'">
                  <img src="@renderer/assets/digital/svg/forum_icon.svg" class="w-25 h-20">
                  <img src="@renderer/assets/digital/svg/forum_text.svg" class="w-42 h-24">
                </template>

                <span v-else class="text line-1">{{ tab.label }}</span>

                <img v-if="tab.uuid && appUnreadCount[tab.uuid]" src="@/assets/redpoint.svg" class="redpoint" />
                <img
                  v-if="tab.page == 'member_about' && (visibleProgress || visibleIntroduce || visibleHonor)"
                  src="@/assets/redpoint.svg"
                  class="redpoint"
                />
              </div>
            </div>
            <div class="bot">
              <!-- v-show="isManage" -->
              <div theme="default" class="bot-item cursor" v-show="isManage" @click="goToAdmin(currentTeamId)">
                <!-- <t-badge style="line-height: 20px;" :count="store.getAllTeamApplyCount + store.getAllTeamActiveCount" :offset="[-12, -2]">
                  <img src="@renderer/assets/member/icon/icon_web.png" class="svg">
                  <span class="text">{{ $t('member.sv17.admin') }}</span>
                </t-badge> -->
                <!-- appCount -->
                <t-badge class="badge" style="line-height: 20px" :count="appCount" :offset="[-6, 4]">
                  <img src="@renderer/assets/member/icon/icon_web.png" class="svg" />
                  <span class="text">{{ $t("member.sv17.admin") }}</span>
                </t-badge>
              </div>
            </div>
          </div>
        </div>
        <div ref="containerFlets" class="scroll">
          <div class="area">
            <div class="info">
              <!-- <div class="swiper_one">
                <img class="default" src="@/assets/member/ringkol.svg">
              </div>
              <div class="swiper_two">
                <div class="top"><img class="default" src="@/assets/member/ringkol.svg"></div>
                <div class="bottom"><img class="default" src="@/assets/member/ringkol.svg"></div>
              </div> -->
              <member-advertising-space
                :currentMemberCard="currentMemberCard"
                :member="isAdminValue?.member"
                :teamId="currentTeamId"
                ref="advertisingSpaceRef"
              >
              </member-advertising-space>

              <div class="member" v-if="isAdminValue?.member">
                <div class="member_info">
                  <div class="member_info_title">
                    <div class="member_info_title_text">
                      {{ $t("member.eb.k") }}
                    </div>
                    <div class="member_info_title_btn cursor" @click="onGoMemberCenter({ tab: 'meterials' })">
                      {{ $t("member.eb.j") }}
                    </div>
                  </div>
                  <div class="member_info_content">
                    <CardsModal :accountLists="memberCards"  :originType="originType.CBD" :current-card="currentMemberCard" @onSetCard="onSetCard">
                      <div class="icon cursor" v-show="memberCards && memberCards.length > 1">
                        <iconpark-icon name="iconstanding" class="iconstanding"></iconpark-icon>
                      </div>
                    </CardsModal>
                    <div class="person">
                      <!-- 单位会员 -->
                      <div style="position: relative;cursor: pointer;" @click="callingOpen"
                  @mouseenter="showCalling = true"
                  @mouseleave="showCalling = false">
                        <template v-if="currentMemberCard">
                          <t-image class="logo" :src="currentMemberCard?.logo" fit="cover">
                            <template #loading>
                              <img src="@/assets/member/svg/avatar_default.svg" />
                            </template>
                            <template #error>
                              <img src="@/assets/member/svg/avatar_default.svg" />
                            </template>
                          </t-image>
                        </template>
                        <template v-else>
                          <t-image class="logo" src="@/assets/member/svg/avatar_default.svg"> </t-image>
                        </template>
                        <div v-show="showCalling">
                        <calling @confirm="confirmLogo" ref="callingRef" />
                       </div>
                      </div>

                      <div class="infos">
                        <span
                          class="name line-1"
                          :style="{ 'padding-right': memberCards && memberCards.length > 1 ? '10px' : 0 }"
                          >{{ currentMemberCard?.staff_name }}</span
                        >
                        <!-- <span class="level mt-2px line-1" v-if="currentMemberCard?.type === 1">{{currentMemberCard?.job}}</span> -->
                        <span class="level mt-2px line-1" v-if="currentMemberCard?.type === 1">
                          {{ currentMemberCard?.is_contact === 1 ? "代表人" : "负责人" }}
                        </span>
                        <span class="level mt-2px line-1" v-else>{{ t("order.geren")}}</span>
                        <span class="levelt mt-4px line-1" v-if="currentMemberCard?.type === 1">{{
                          currentMemberCard?.name
                        }}</span>
                        <span class="tag mt-8px">
                          <!-- <span class="tag-unit">{{currentMemberCard?.level_name}}</span> -->
                          <template v-if="currentMemberCard?.type === 1">
                            <span v-if="currentMemberCard?.is_connect" class="tag-connect">已连接</span>
                            <span v-else class="tag-unconnect cursor" @click="onConnect(currentMemberCard)"
                              >未连接</span
                            >
                          </template>
                        </span>
                      </div>
                    </div>
                    <div class="entry">
                      <div class="entry-item cursor" @click="onGoMemberCenter({ tab: 'meterials' })">
                        <img class="logo" src="@/assets/member/svg/member_info_icon.svg" />
                        <span class="text">租户资料</span>
                      </div>
                      <div class="entry-item cursor" @click="onInviteJobClub">
                        <img class="logo" src="@/assets/member/svg/invite_icon.svg" />
                        <span class="text">{{ $t("member.digital.o") }}</span>
                      </div>
                      <div class="entry-item cursor" @click="onGoMemberCenter({ tab: 'advertisement' })">
                        <img class="logo" src="@/assets/member/svg/product_icon.svg" />
                        <span class="text">{{ $t("member.eb.g") }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="member_produce mt-8px cursor" @click="onOpenSquareVisible">
                  <template v-if="proxy.$i18n.locale === 'zh-cn'">
                    <img src="@/assets/member/icon/upsvip_cn.jpg" />
                  </template>
                  <template v-else>
                    <img src="@/assets/member/icon/upsvip_hk.jpg" />
                  </template>
                </div>
              </div>
              <div class="member" v-else-if="memberVisitorCard?.detail">
                <div class="member_info">
                  <div class="member_info_title">
                    <div class="member_info_title_text">{{ $t("member.eb.k") }}</div>
                    <!-- <div class="member_info_title_btn cursor" @click="onGoMemberCenter({ tab: 'meterials' })">进入</div> -->
                  </div>
                  <div class="member_info_content">
                    <CardsModal :accountLists="memberCards" :current-card="currentMemberCard" @onSetCard="onSetCard">
                      <div class="icon cursor" v-show="memberCards && memberCards.length > 1">
                        <iconpark-icon name="iconstanding" class="iconstanding"></iconpark-icon>
                      </div>
                    </CardsModal>
                    <div class="person">
                      <!-- 单位会员 -->
                      <div style="position: relative;cursor: pointer;" @click="callingOpen"
                  @mouseenter="showCalling = true"
                  @mouseleave="showCalling = false">
                        <template v-if="currentMemberCard">
                          <t-image class="logo" :src="memberVisitorCard?.detail?.avatar" fit="cover">
                            <template #error>
                              <img src="@/assets/member/svg/avatar_default.svg" />
                            </template>
                            <template #loading>
                              <img src="@/assets/member/svg/avatar_default.svg" />
                            </template>
                          </t-image>
                        </template>
                        <template v-else>
                          <img class="logo" src="@/assets/member/svg/avatar_default.svg" />
                        </template>
                        <!-- <div v-show="showCalling">
                        <calling @confirm="confirmLogo" ref="callingRef" />
                       </div> -->
                      </div>

                      <div class="infos">
                        <span
                          class="name line-1"
                          :style="{ 'padding-right': memberCards && memberCards.length > 1 ? '10px' : 0 }"
                          >{{ memberVisitorCard?.detail?.name }}</span
                        >
                        <!-- <span class="level mt-2px line-1" v-if="currentMemberCard?.type === 1">{{
                          currentMemberCard?.job_name
                        }}</span> 政企-->
                        <span class="level mt-2px line-1">
                          {{ memberVisitorCard?.detail?.job_name }}
                        </span>
                         <!-- <span class="level mt-2px line-1" v-else>{{
                          t("order.geren")
                        }}</span>
                        <span class="levelt mt-4px line-1" v-if="currentMemberCard?.type === 1">{{
                          currentMemberCard?.name
                        }}</span> -->
                        <!-- <span class="tag mt-8px">
                          <template v-if="currentMemberCard?.type === 1">
                            <span v-if="currentMemberCard?.is_connect" class="tag-connect">已连接</span>
                            <span v-else class="tag-unconnect cursor" @click="onConnect(currentMemberCard)"
                              >未连接</span
                            >
                          </template>
                        </span> -->
                      </div>
                    </div>
                    <div class="entry">
                      <!-- <div class="entry-item cursor" @click="onGoMemberCenter({ tab: 'meterials' })">
                        <img class="logo" src="@/assets/member/svg/member_info_icon.svg" />
                        <span class="text">组织资料</span>
                      </div> -->
                      <t-tooltip placement="bottom">
                        <template #content>成为正式平台成员</template>
                        <div class="entry-item cursor" @click="onOpenInviteVisitorToNormal">
                          <img class="logo" src="@/assets/member/svg/invite_icon.svg" />
                          <span class="text">申请加入</span>
                        </div>
                      </t-tooltip>

                      <!-- <div class="entry-item cursor" @click="onGoMemberCenter({ tab: 'advertisement' })">
                        <img class="logo" src="@/assets/member/svg/product_icon.svg" />
                        <span class="text">{{ $t("member.eb.g") }}</span>
                      </div> -->
                    </div>
                  </div>
                </div>
                <div class="member_produce mt-8px cursor" @click="onOpenSquareVisible">
                  <template v-if="proxy.$i18n.locale === 'zh-cn'">
                    <img src="@/assets/member/icon/upsvip_cn.jpg" />
                  </template>
                  <template v-else>
                    <img src="@/assets/member/icon/upsvip_hk.jpg" />
                  </template>
                </div>
              </div>
              <div class="member" v-else-if="isAdminValue" @click="onOpenSquareVisible">
                <template v-if="proxy.$i18n.locale === 'zh-cn'">
                  <img class="defaultMember" src="@/assets/member/icon/upDefault_cn.jpg" />
                </template>
                <template v-else>
                  <img class="defaultMember" src="@/assets/member/icon/upDefault_hk.jpg" />
                </template>
              </div>
            </div>
            <div class="waterfall">
              <wc-waterfall gap="16" cols="2">
                <ex-apps :activationGroupItem="activeAccount" :key="'ex-apps'"/>

                <notice :activationGroupItem="activeAccount" />
                <dynamics :activationGroupItem="activeAccount" />
                <fengcai :activationGroupItem="activeAccount" />

                <!-- <div v-if="richArr.length > 0" class="waterfall-box min-h-272px">
                  <div class="top">
                    <div class="title">商机</div>
                    <div v-show="richArr.length > 8" class="more cursor" @click="goTabNew({ page: 'cbd_rich' })">
                      查看全部<iconpark-icon name="iconarrowright" class="icon"></iconpark-icon>
                    </div>
                  </div>
                  <div class="lists mt-12px">
                    <div
                      v-for="(rich, richIndex) in richArr.slice(0, 8)"
                      :key="richIndex"
                      class="lists-item cursor"
                      @click="onOpenRich(rich)"
                    >
                      <div class="left">
                        <img v-lazy="rich.images || ORG_DEFAULT_AVATAR" class="lo" />
                      </div>
                      <div class="right">
                        <div class="top">
                          <span class="name line-2">
                            <span v-show="rich.is_top === 1" class="tip blue">置顶</span>
                            <span v-show="rich.type === 2" class="tip yellow">{{ rich.type_text }}</span>
                            <span v-show="rich.type === 1" class="tip">{{ rich.type_text }}</span>
                            {{ rich.title }}
                          </span>
                        </div>
                        <div class="bottom">
                          <span class="company">
                            <kyy-avatar
                              class="av"
                              :avatar-size="'20px'"
                              :image-url="rich.avatar || ORG_DEFAULT_AVATAR"
                              :user-name="rich.name"
                              :shape="'circle'"
                            />
                            <span class="text line-1">
                              {{ rich.name }}
                            </span>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div> -->
                <div v-if="squareList.length > 0" class="waterfall-box min-h-272px">
                  <div class="top topSquare">
                    <div class="title">
                      <img src="@renderer/assets/member/svg/square.svg" />
                      {{ $t("member.eb.p") }}
                    </div>
                    <div
                      v-show="squareList.length > 0"
                      class="more cursor"
                      @click="goTabNew({ page: 'cbd_square' })"
                    >
                      全部<iconpark-icon name="iconarrowright" class="icon"></iconpark-icon>
                    </div>
                  </div>

                  <div class="conbox">
                    <wc-waterfall gap="8" cols="2">
                      <div
                        v-for="(menu, menuIndex) in squareList.slice(0, 8)"
                        :key="menuIndex"
                        class="item cursor"
                      >
                        <div class="public">
                        <span class="logo">
                          <t-image
                            v-if="menu?.square?.squareType === 'INDIVIDUAL'"
                            class="img"
                            fit="cover"
                            :src="getSrcThumbnail(menu?.square?.avatar)"
                          >
                            <template #error>
                              <KyyAvatar
                                class="avatar"
                                avatar-size="48px"
                                :image-url="''"
                                :user-name="menu?.square?.name"
                                shape="circle"
                              />
                            </template>
                            <template #loading>
                              <img :src="ORG_DEFAULT_AVATAR" />
                            </template>
                          </t-image>
                          <t-image
                            v-else
                            :src="menu?.square?.avatar ? getSrcThumbnail(menu?.square?.avatar) : ''"
                            class="img"
                            fit="cover"
                          >
                            <template #loading>
                              <img :src="ORG_DEFAULT_AVATAR" class="img" />
                            </template>
                            <template #error>
                              <img :src="ORG_DEFAULT_AVATAR" class="img" />
                            </template>
                          </t-image>
                        </span>
                          <div class="right">
                            <div class="flex justify-between items-center">
                           <span class="lin">
                            <span class="star">
                              <iconpark-icon
                                v-if="menu?.square?.squareType === SquareType.Enterprise"
                                name="iconenterprise"
                                class="star-icon"
                              ></iconpark-icon>
                              <iconpark-icon
                                v-else-if="menu?.square?.squareType === SquareType.BusinessAssociation"
                                name="iconbusiness"
                                class="star-icon"
                              ></iconpark-icon>
                              <iconpark-icon
                                v-else-if="menu?.square?.squareType === SquareType.IndividualBusiness"
                                name="iconindividual"
                                class="star-icon"
                              ></iconpark-icon>
                              <iconpark-icon
                                v-else-if="menu?.square?.squareType === SquareType.Other"
                                name="iconother"
                                class="star-icon"
                              ></iconpark-icon>
                              <iconpark-icon
                                v-else-if="menu?.square?.squareType === SquareType.Government"
                                name="icongov"
                                class="star-icon"
                              ></iconpark-icon>
                            </span>
                            <REllipsisTooltip class="name w-104 font-600 text-[#1A2139]" :text="menu?.square?.name" />
                          </span>
                            </div>

                            <div class="flex items-center gap-8">
                              <div class="inline-flex px-4 border border-solid border-[#FFBE16] rounded-4 shrink-0">
                                <span class="text-11 leading-16 text-[#D9A213]"><span class="mr-2">粉丝</span>{{ formatFansCount(menu?.square?.fansCount) }}</span>
                              </div>
                              <div v-if="menu?.square?.distance && menu?.square?.distance !== '0'" class="inline-flex items-center gap-2 px-4 border border-solid border-[#FFBE16] rounded-4 shrink-0">
                                <iconpark-icon name="iconlocal2" class="text-14 text-[#D9A213] mr-2"></iconpark-icon>
                                <span class="text-11 leading-16 text-[#D9A213]">{{ formatDistance(menu?.square?.distance) }}</span>
                              </div>
                            </div>
                          </div>
                          <t-button theme="default" class="square-link-button" @click="onGoSquarePage(menu)">
                            <span class="font-500 text-12">{{ $t("member.eb.j") }}</span>
                          </t-button>
                        </div>

                        <ProductThumbnail v-if="menu?.square?.promotionProducts?.length || menu?.square?.imgs?.length" class="mt-12 w-full" :products="menu?.square?.promotionProducts" :imgs="menu?.square?.imgs" />

                        <div class="visible" v-show="menu.square.squareType !== SquareType.Individual && !menu.connected">
                          <div class="tip">未展示到平台</div>
                          <t-button class="btn" theme="primary" @click="onGoSquarePage(menu)">
                            <iconpark-icon class="iconpreciewopen" name="iconpreciewopen"></iconpark-icon>
                            <span class="text">展示到平台</span>
                          </t-button>
                        </div>
                      </div>
                    </wc-waterfall>
                  </div>
                </div>

                <div v-if="richArrEbook.length > 0" class="waterfall-box">
                  <div class="top topEbook">
                    <div class="title">
                      <img src="@renderer/assets/member/svg/document.svg" />
                      刊物
                    </div>
                    <div v-show="richArrEbook.length > 0" class="more cursor" @click="goTabNew({ page: 'cbd_ebook' })">
                      全部<iconpark-icon name="iconarrowright" class="icon"></iconpark-icon>
                    </div>
                  </div>
                  <div class="listsEbook">
                    <div
                      v-for="(rich, richIndex) in richArrEbook.slice(0, 3)"
                      :key="richIndex"
                      class="listsEbook-item cursor"
                      @click="onOpenEbook(rich)"
                    >
                      <div class="cover">
                        <img :src="rich.cover" alt="" />
                      </div>
                      <div class="name">
                        {{ rich.name }}
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <img
                  v-if="proxy.$i18n.locale === 'zh-cn'"
                  src="@renderer/assets/member/svg/digital_platform_cn.svg"
                  class="entry cursor"
                  @click="goLeaf"
                />
                <img
                  v-else
                  class="entry cursor"
                  src="@renderer/assets/member/svg/digital_platform_mo.svg"
                  @click="goLeaf"
                /> -->
                <!-- <div class="leaft">
                </div> -->
              </wc-waterfall>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="backTop cursor"
    v-show="scrolledDistance > 50"
    :class="{ isOpacity: scrolledDistance > 70 }"
    @click="scrollToTop"
  >
    <!-- <iconpark-icon name="iconarrowup" class="iconarrowup"></iconpark-icon> -->
    <iconpark-icon class="iconarrowup" name="icontopinside"></iconpark-icon>
  </div>
  <!-- && (memberSettingInfo?.im_group || members) 根据用户是否有平台身份判断暂时注释这两个条件2.1.1-->
  <template v-if="isAdminValue?.member||memberVisitorCard?.detail">
    <div v-show="isExpandContact" class="backTopContact cursor isOpacity" @click="onLiaisonModal">
      <!-- <img class="iconarrowup" src="@renderer/assets/digital/icon/contacter.png"> -->
      <!-- <img  v-else class="iconarrowup" src="@renderer/assets/digital/svg/digtalk_hk.svg">  -->
      <img class="iconarrowup" src="@renderer/assets/digital/svg/digtalk.svg" />
      <div class="shadowBox" @click.stop="onExpandContact"></div>
    </div>

    <div class="backRight" v-show="!isExpandContact" @click="onExpandContact">
      <img class="iconarrowup cursor" src="@renderer/assets/digital/svg/expand.svg" />
    </div>
  </template>
  <div class="tricks cursor">
    <Tricks  uuid="数字CBD-首页"  :scene="2" :isDrag="false"/>
  </div>

  <detailsReadonly ref="detailsReadonlyRef" :new-jupm="true" />
  <!-- 套餐升级（传 upgrade 标识） -->
  <AnnualFeeDialog
    v-if="annualFeeDialogUpgradeVisible"
    v-model="annualFeeDialogUpgradeVisible"
    upgrade
    :team-id="squareTeamId"
    @success="upgradeLoaded"
  />
  <InviteQrcodeModal
    ref="inviteQrcodeModalRef"
    :header-text="$t('member.digital.o')"
    :way-tips="$t('member.kaxi.g')"
    :activeAccount="activeAccount"
    :member="currentMemberCard"
  />
  <!-- <CardsModal ref="cardsModalRef" @onSetCard="onSetCard" /> -->
  <!-- 组织未创建，先创建组织再购买套餐 -->
  <OpenSquare v-if="openSquareVisible" v-model="openSquareVisible" @success="openSquareVisible = false" />
  <liaisonModel ref="liaisonModelRef" :teamId="currentTeamId" :isManage="isManage"/>
  <AnnualConnect
    ref="annualConnectRef"
    :activeAccount="activeAccount"
    @backType="onBackType"
    @refresh="onRefreshAnual"
  ></AnnualConnect>

  <BMap :height="0" :width="0" @initd="onMapInit" />
</template>

<script setup lang="ts">
import { SquareType } from "@renderer/api/square/enums";
import business from "@renderer/assets/member/svg/business.svg";
import activity from "@renderer/assets/member/svg/activity.svg";
import directory from "@renderer/assets/member/svg/directory.svg";
import square from "@renderer/assets/member/svg/square.svg";
import about from "@renderer/assets/member/svg/about.svg";
// import business from "@renderer/assets/member/svg/business.svg";
import document from "@renderer/assets/member/svg/document.svg";
import {
  ref,
  reactive,
  onActivated,
  watch,
  Ref,
  toRaw,
  computed,
  onMounted,
  onBeforeUnmount,
  onUnmounted,
  getCurrentInstance,
  defineAsyncComponent,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import { useCbdStore } from "@renderer/views/cbd/store/cbd";
import {
  getMemberSquaresAxios,
  getMemberNichAxios,
  getSharedSquaresAxios,
  getIndividualSquareAxios,
  getMemberApplyLinkAxios,
  checkIsAdminAxios,
  getMemberCardsAxios,
  workShopAppAxios,
  getMemberSettingAxios,
  getMemberAdminAxios,
  getPlatformLiaisonsAxios,
  editSpecifiedFieldsCBR,
} from "@renderer/api/cbd/api/businessApi";
import { getResponseResult } from "@renderer/utils/myUtils";
import { getCbdTeamID, goToAdmin } from "@renderer/views/cbd/utils/auth";
import to from "await-to-js";
import { getEbookShareList } from "@renderer/api/cbd/api/ebookApi";
import { toSquareHome } from "@renderer/views/square/utils/ipcHelper";
import { getBaseUrl } from "@renderer/utils/apiRequest";
import detailsReadonly from "@renderer/views/square/niche/components/detail.vue";
import { getOpenid, setPersonSquareId } from "@renderer/utils/auth";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { useApi } from "@renderer/views/member/hooks/api";
import { useI18n } from "vue-i18n";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { ArticleChannel } from "@renderer/components/pb/constants";
import { getAppsState, getRedPoint, listSwitch } from "@renderer/api/workBench/index";
import { noticeRed } from "@renderer/api/notice/index";
import LikeButton from "@renderer/views/digital-platform/components/LikeButton.vue";
import { getSrcThumbnail, getSrcLogo } from "@renderer/views/message/service/msgUtils";
import { societyArticleChannelInfo } from "@/api/pb/manage";
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
import ggao from "@/assets/svg/ggaopt.svg";
import elegance from "@/assets/fengcai/elegance.svg";
import { getFrontGrowthList } from "@/api/workBench/growth";
import { getFrontIntroduceList } from "@/api/workBench/introduce";
import AnnualFeeDialog from "@/views/square/components/annual-fee/AnnualFeeDialog.vue";
import { getMemberCurrentCardID, setMemberCurrentCardID } from "@renderer/views/member/utils/auth";
import memberAdvertisingSpace from "@renderer/views/cbd/member_number/components/member-advertising-space.vue";

import CardsModal from "@renderer/views/member/member_number/modal/cards-modal.vue";
import InviteQrcodeModal from "@renderer/views/cbd/member_number/modal/invite-qrcode-modal.vue";
import KyyAvatar from "@renderer/components/kyy-avatar/index.vue";

import dynamics from "@renderer/views/cbd/member_number/panel/member-panel/module/dynamics.vue";
import notice from "@renderer/views/member/member_number/panel/member-panel/module/notice.vue";
import fengcai from "@renderer/views/member/member_number/panel/member-panel/module/fengcaiModule.vue";
import OpenSquare from "@renderer/views/square/components/OpenSquare.vue";
import liaisonModel from "@renderer/views/cbd/member_number/modal/liaison-modal.vue";
import calling from "@renderer/views/digital-platform/components/calling.vue";

import AnnualConnect from "@renderer/views/digital-platform/components/AnnualConnect.vue";
import party from "@/assets/pb/header_party.svg";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { onDisplayToPlatform, TeamAnnualTypeError } from "@renderer/views/digital-platform/utils/auth";
import { formatFansCount, formatDistance } from "@renderer/views/digital-platform/utils/format";
import { getAppEntries, getVisitorCardsApi } from "@renderer/api/digital-platform/api/businessApi";
import { inviteUrl } from "@renderer/utils/baseUrl";
import Qs from "qs";
import { useEventBus } from '@vueuse/core';
import { workAreaRefreshKey } from '@renderer/views/digital-platform/utils/eventBusKey';
import { societyArticleChannelInfo_fengcai } from "@/api/fengcai/manage";
import { EntryType } from "@renderer/api/square/models/square";
import {REllipsisTooltip, Tricks} from '@rk/unitPark';
import ExApps from "@renderer/views/member/member_number/panel/member-panel/module/ex-apps.vue";
import { originType }  from "@renderer/views/digital-platform/utils/constant"
import { policyChannelInfoAxios } from "@renderer/api/digital-platform/api/businessApi";
import LynkerSDK from "@renderer/_jssdk";
import ProductThumbnail from "@renderer/views/digital-platform/components/ProductThumbnail.vue";
import { useBaiduMap } from '@renderer/components/common/map/hooks';
import { BMap } from 'vue3-baidu-map-gl';
import { jumpH5WithLink } from "@renderer/views/contacts/utils";

const { ipcRenderer, shell } = LynkerSDK;

const bus = useEventBus(workAreaRefreshKey);
let appsStateData = null;
const squareResult = ref(null);
const digitalPlatformStore = useDigitalPlatformStore();
const digitalRouter = useRouterHelper("digitalPlatformIndex");
const { t } = useI18n();
const { menuList, routeList, roleFilter } = useRouterHelper("cbdIndex");
const { onActionSquare } = useApi();
const { proxy } = getCurrentInstance() as any;
const router = useRouter();
const isExpandContact = ref(true);
// const store = useCbdStore();
const emits = defineEmits(["selectPanel"]);
const route = useRoute();
const openId = getOpenid();

const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
});
const store: any = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore;
  }
  return useCbdStore();
});
const platformCpt = computed(() => props.platform || route.query?.platform);
const advertisingSpaceRef = ref(null);
ipcRenderer.on("edit-ad-swiper", (event, val) => {
  advertisingSpaceRef.value.editAdSwiper(val);
  // 把轮播暂停edit-ad-swiper
  console.log(val, "把组件暂停");
});
onBeforeUnmount(() => {
  ipcRenderer.removeAllListeners("edit-ad-swiper");
});
const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  }
  return getCbdTeamID();
});

const activeAccount = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query;
  } else {
    return store.value.activeAccount;
  }
});
const appUnreadCount = reactive({} as Record<string, number>);

const tabs = ref([
  {
    label: "首页",
    icon: business,
    value: "home",
    page: "cbd_home",
    isShow: true,
  },
  {
    label: t("member.squarek.b"),
    icon: business,
    value: "shop",
    page: "cbd_shop",
    isShow: true,
  },
  {
    label: t("application.forum"),
    value: "digital_platform_forum",
    page: "digital_platform_forum",
    uuid: "digital_platform_forum",
    isShow: true,
  },
  {
    label: t("member.squarek.t"),
    icon: square,
    value: "square",
    page: "member_square",
    // page: "cbd_square",

    isShow: false,
  },
  {
    label: t('policy.policy_express'),
    icon: square,
    value: "digital_platform_policy_express",
    page: "digital_platform_policy_express",
    uuid: "digital_platform_policy_express",
    isShow: true,
  },
  {
    label: t("member.squarek.u"),
    icon: directory,
    value: "directory",
    page: "cbd_name",
    isShow: true,
  },
  {
    label: t("member.squarek.w"),
    icon: activity,
    value: "activity",
    page: "cbd_active",
    isShow: true,
  },
  {
    label: t("member.squarek.v"),
    icon: document,
    value: "document",
    page: "cbd_ebook",
    isShow: false,
  },
  {
    label: t("square.partyBuild"),
    icon: party,
    value: "society-article",
    page: "digital_platform_pb_list",
    uuid: "society-article",
    isShow: true,
  },
  {
    label: t("member.squarek.l"),
    icon: about,
    value: "about",
    page: "member_about",
    isShow: true,
  },
  // {
  //   label: "公告",
  //   icon: ggao,
  //   value: "digital_platform_notice",
  //   page: "digital_platform_notice",
  //   uuid: "digital_platform_notice",
  //   isShow: false,
  // },
  {
    label: t("banch.ptfc"),
    icon: elegance,
    value: "digital_platform_fengcai",
    page: "digital_platform_fengcai",
    uuid: "digital_platform_fengcai",
    isShow: false,
  },
  {
    label: '另可圈',
    value: "circle",
    page: "digital_platform_circle",
    uuid: "digital_platform_circle",
    query: {},
    isShow: false,
  },
  {
    label: t("album.appName"),
    value: "album",
    page: "digital_platform_album",
    uuid: "digital_platform_album",
    query: {},
    isShow: false,
  },
  {
    label: '视频',
    value: "video_list",
    page: "video_list",
    uuid: "video_list",
    query: {},
    isShow: true,
  },
]);

// 跳宣传页
const goLeaf = () => {
  // router.push({
  //   path: "/cbdIndex/member_manage",
  //   query: {
  //     // projectId: props.projectId
  //   }
  // });
  // console.log(platformCpt.value)
  // return ;

  if (platformCpt.value === platform.digitalPlatform) {
    // 平台
    const searchMenu = digitalRouter.routeList.find((v) => v.name === "digital_platform_leaflets");
    searchMenu.query = { ...searchMenu.query, platform: platform.digitalPlatform };
    router.push({ path: searchMenu.fullPath, query: searchMenu.query });
    digitalPlatformStore.addTab(toRaw(searchMenu), true);
  } else {
    // const searchMenu = routeList.find((v) => v.name === "cbd_leaflets");
    // // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
    // router.push({ path: searchMenu.fullPath, query: { } });
    // store.addTab(toRaw(searchMenu));
  }
};

const onExpandContact = () => {
  isExpandContact.value = !isExpandContact.value;
};



const onOpenPersonSquare = async (goSquare?) => {
  console.log('onOpenPersonSquare')

  return new Promise(async (resolve, reject) => {
    const params = {};
    const [err, res] = await to(getIndividualSquareAxios(params));
    if (err) {
      // MessagePlugin.error(err?.message);
      console.log(err?.message)
      reject()
      return;
    }
    const { data } = res;
    // 启用成功后，继续跳广场
    const squareId = goSquare || data?.info?.square?.squareId
    console.log(goSquare, squareId)
    if(squareId) {
      // onGoSquarePage(squareId)
      // localStorage.setItem('personSquareId', squareId)
      setPersonSquareId(squareId)
      resolve();
    } else {
      // MessagePlugin.error('缺少广场ID')
      reject();
    }
  })
};


const isOpenSelfSqaure = () => {
  return new Promise(async (resolve, reject) => {
    const params = {
      // team_id: obj.team_id , // 组织 ID（获取组织广场号时使用）
      open_id: getOpenid(), // 获取个人广场号时使用
    };
    const [err, res] = await to(getSharedSquaresAxios(params));
    if (err) {
      console.log(err?.message);
      reject();
      return;
    }
    console.log(res);
    const { data } = res;

    if (!data?.selfOpened) {
      const confirmDia = DialogPlugin({
        header: "提示",
        theme: "info",
        body: "您当前未启用广场应用，确定启用？",
        closeBtn: null,
        confirmBtn: "确定",
        className: "delmode",
        onConfirm: async () => {
          // 删除字段操作
          confirmDia.hide();
          try {
            await onOpenPersonSquare();
            resolve();
          } catch (error) {
            reject();
          }
        },
        onClose: () => {
          confirmDia.hide();
          reject();
        },
      });
    } else {
      setPersonSquareId(data?.square?.squareId)
      resolve();
    }
  })
}

// 跳商机页
const goTabNew = async (val) => {
  if (val?.page === "cbd_home") return;
  // router.push({
  //   path: "/cbdIndex/member_manage",
  //   query: {
  //     // projectId: props.projectId
  //   }
  // });
  if(val?.value === 'album' || val?.value === 'circle') {
    try {
      await isOpenSelfSqaure();
    } catch (error) {
      console.log(error);
      return;
    }
  }

  // if(val.value === 'square') {

  //   onActionSquare(null, currentTeamId.value)
  //   return;
  // };

  // if(val.page === 'member_square') {
  //   emits('selectPanel', 'square');
  //   return;
  // }

  if (platformCpt.value === platform.digitalPlatform) {
    const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(val.page));
    // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
    searchMenu.query = { ...searchMenu.query, ...route.query, platform: platform.digitalPlatform, team_id: currentTeamId.value, ...val?.query };
    router.push({ path: searchMenu.fullPath, query: searchMenu.query });
    digitalPlatformStore.addTab(toRaw(searchMenu), false);
  } else {
    const searchMenu = routeList.find((v) => v.name === val.page);
    // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
    router.push({ path: searchMenu.fullPath, query: {} });
    store.value.addTab(toRaw(searchMenu));
  }
};

const isManage = computed(
  () =>
    store.value.activeAccount &&
    (isAdminValue.value?.super || isAdminValue.value?.isAdmin || isAdminValue.value?.superAdmin),
);

const isLoading = ref(false);
const isNetworkError = ref(false);
const squareList = ref([]);
const isAdminValue = ref(null);

const memberCards: Ref<any> = ref([]);
const currentMemberCard: Ref<any> = ref(null);
const memberVisitorCard: Ref<any> = ref(null);



const onGetVisitorCardsAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getVisitorCardsApi({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg !== "Network Error") MessagePlugin.error(errMsg);
    }
  });
};

const onGetMemberCardsAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberCardsAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg !== "Network Error") MessagePlugin.error(errMsg);
    }
  });
};

const liaisonModelRef = ref(null);
const memberSettingInfo = ref(null);
const onGetMemberSetting = () => {
  let result = null;
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberSettingAxios({}, currentTeamId.value);
      result = getResponseResult(result);

      if (!result) {
        reject();
        return;
      }
      result = result.data;
      memberSettingInfo.value = result;
      resolve(result);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const onLiaisonModal = () => {
  if (activeAccount.value?.user_ids?.platformStaff > 0) {
    liaisonModelRef.value?.onOpen({
      ...toRaw(memberSettingInfo.value),
      platformStaff: activeAccount.value?.user_ids?.platformStaff,
      teamId: activeAccount.value?.teamId,
      inCardID: activeAccount.value?.user_ids?.idStaff, // 内部身份
    });
  }
};

const openSquareVisible = ref(false);

// 购买套餐
const onOpenSquareVisible = () => {
  openSquareVisible.value = true;
};

const onSetCard = (row) => {
  currentMemberCard.value = row;
  digitalPlatformStore.setRecordTeamsSelectCardId({ teamId: currentTeamId.value, cardId: currentMemberCard.value?.id });
  if(currentMemberCard.value?.type === 1 && !currentMemberCard.value?.is_connect) {
    onConnect(currentMemberCard.value);
  }
};

const cardsModalRef = ref(null);
const onOpenCardsModal = () => {
  console.log(memberCards.value);
  console.log(cardsModalRef.value);
  // onGetMemberCardsAxios().then(
  //   (res: any) => {
  //     memberCards.value = res;
  //     currentMemberCard.value = res[0];
  //     cardsModalRef.value.onOpen();
  //   },
  //   () => {
  //     MessagePlugin.error('获取会员卡信息失败');
  //   }
  // );
  if (memberCards.value.length > 1) {
    cardsModalRef.value?.onOpen(memberCards.value, currentMemberCard.value);
  }
};

// 获取邀请链接
const getInviteLinkAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyLinkAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data.link);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg !== "Network Error") MessagePlugin.error(errMsg);
    }
  });
};

// 邀请入会
const inviteQrcodeModalRef = ref(null);
const onInviteJobClub = () => {
  getInviteLinkAxios().then((val) => {
    inviteQrcodeModalRef.value.onOpen(val);
  });
};

const onOpenInviteVisitorToNormal = () => {
  let url = ''
  let path = "/account/jump?to=cbdInvite";
  getInviteLinkAxios().then((res: any) => {
    if (res) {

      let params = {
        link: res,
      }
      url = `${inviteUrl}${path}&${Qs.stringify(params, { encode: true })}`
      console.log(url)
      jumpH5WithLink(url);
    }
  });
}


// 跳转到会员中心
const onGoMemberCenter = (query) => {
  setMemberCurrentCardID(currentMemberCard.value?.id);
  digitalPlatformStore.setInputeMemberCenter(true);
  const tab = {
    label: "租户中心",
    icon: activity,
    value: "activity",
    page: "cbd_my",
    isShow: true,
  };
  const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(tab.page));
  console.log(digitalRouter.routeList, searchMenu, platform, "platform-----------------platform");
  // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
  searchMenu.query = {
    ...searchMenu.query,
    ...query,
    platform: platform.digitalPlatform,
    team_id: currentTeamId.value,
  };
  router.push({ path: searchMenu.fullPath, query: searchMenu.query });
  digitalPlatformStore.addTab(toRaw(searchMenu), false);
};

const onInitData = (isCheckConnect = false) => {
  onGetMemberCardsAxios().then(
    (res: any) => {
      if (res && res.length > 0) {
        memberCards.value = res.map((v) => {
          // v.label = v.type === 1 ? v.team_name : v.name;
          v.logo = v.logo ? getSrcThumbnail(v.logo) : "";
          v.label = v.name;
          return v;
        });
        console.log(res);

        if (digitalPlatformStore.recordTeamsSelectCardIds && digitalPlatformStore.recordTeamsSelectCardIds.length > 0) {
          const result = digitalPlatformStore.recordTeamsSelectCardIds.find((v) => v.teamId === currentTeamId.value);
          if (result) currentMemberCard.value = memberCards.value.find((v) => v.id === result.cardId);
        }

        if (currentMemberCard.value) {
          console.log(currentMemberCard.value);
          currentMemberCard.value = memberCards.value.find((v) => v.id === currentMemberCard.value?.id);
          console.log(currentMemberCard.value);
        } else {
          currentMemberCard.value = res[0];
          console.log(currentMemberCard.value);
        }
        console.log(currentMemberCard.value);
        // currentMemberCard.value = currentMemberCard.value
        //   ? currentMemberCard.value
        //   : res[0];
        // onLookDetail(res[0]);
        if(isCheckConnect) {
          if(currentMemberCard.value?.type === 1 && !currentMemberCard.value?.is_connect) {
            onConnect(currentMemberCard.value);
          }
        }

        // onSearch();
      } else {
        currentMemberCard.value = null;
      }

      digitalPlatformStore.setRecordTeamsSelectCardId({
        teamId: currentTeamId.value,
        cardId: currentMemberCard.value?.id,
      });
    },
    (err) => {
      currentMemberCard.value = null;
    },
  );
};

// 判断当前用户是否为管理员
const onCheckIsAdmin = async (params, teamId?) => {
  let res: any = null;
  console.log("这啊啊4444");
  try {
    res = await checkIsAdminAxios(params, teamId);
    res = getResponseResult(res);
    if (!res) return;

    isAdminValue.value = res.data;
    // const my = tabsTop.value.find((v) => v.value === 'my');
    // if(my) {
    //   if (isAdminValue.value?.member) {
    //     my.isShow = true;
    //   } else {
    //     my.isShow = false;
    //   }
    // }
    if (isAdminValue.value?.member) {
      await onInitData(true);
    }

    onGetVisitorCardsAxios().then((res: any)=> {
      memberVisitorCard.value = res;
    })
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === "Network Error") {
    } else {
      MessagePlugin.error(errMsg);
    }
  }
};

const annualConnectRef = ref(null);
// const selectOrganizeModalRef = ref(null)
const onBackType = (res) => {
  if (res?.type === TeamAnnualTypeError.Success) {
    // MessagePlugin.success('连接成功')
    // 更新详情里面的数据
    // if(isShowNameDetail.value) {
    //   onGetMemberNameDetail(detailData.value).then((res:any) => {
    //     console.log(res)
    //     nameDetailModelRef.value?.onOpen(res.data);
    //   });
    // }

    // onSearch();
    onRefreshAnual();
  }
};

const onRefreshAnual = () => {
  onInitData();
  onGetMemberSquaresAxios(true); // 会员广场
};

const onConnect = async (row) => {
  annualConnectRef.value.onConnectPlatform(row);
};

/**
 *
 * @param idStaff 获取应用统计
 */
const appCount = ref(0);
const onWorkShopAppAxios = async () => {
  let res: any = null;
  try {
    res = await workShopAppAxios({ uuids: [] }, currentTeamId.value);
    res = getResponseResult(res);
    if (!res) return;
    console.log(res);
    const { data } = res;
    // if (data && data.length > 0) {
    //   const kvObject = data.reduce((acc, item) => {
    //     acc[item.uuid] = item.count;
    //     return acc;
    //   }, {});
    //   appCount.value = kvObject;
    // }
    appCount.value = data || 0;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === "Network Error") {
    } else {
      // MessagePlugin.error(errMsg);
      console.log(errMsg);
    }
  }
};

const members = ref(0);
// 获取成员列表
const onGetMemberGroup = () => {
  let result = null;
  return new Promise(async (resolve, reject) => {
    try {
      result = await getPlatformLiaisonsAxios({}, currentTeamId.value);
      result = getResponseResult(result);

      if (!result) {
        reject();
        return;
      }
      members.value = result?.data?.total || 0;
      resolve(result);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      console.log(errMsg);
      reject();
    }
  });
};

const initData = () => {
  onWorkShopAppAxios();
  onCheckIsAdmin({ ...store.value.activeAccount?.user_ids }, currentTeamId.value);
  onGetMemberSetting();
  onGetMemberGroup();
};

const getNoticeRed = () => {
  noticeRed(currentTeamId.value).then((res) => {
    appUnreadCount["digital_platform_notice"] = res.data.data.platform;
  });
  societyArticleChannelInfo_fengcai(2, {
    team_id: currentTeamId.value,
  }).then((res) => {
    appUnreadCount["digital_platform_fengcai"] = res.data.data.unread > 0;
    console.log(res, "红点平台风采1");
  });


  policyChannelInfoAxios({team_id: currentTeamId.value,}).then((res) => {
    appUnreadCount["digital_platform_policy_express"] = res?.data?.data?.unread > 0;
    console.log(res, "红点政策解读3");})
};


const onGetAppEntriesAxios = async (squareId) => {
  const circle = tabs.value.find((v) => v.value === "circle");
  const [err, res] = await to(getAppEntries(squareId, currentTeamId.value,));
  if (err) {
    circle.isShow = false;
    console.log(err?.message);
    return;
  }
  const { data } = res;
  const entry = data?.entries?.find(v=>v.entryType === EntryType.CircleRingkol)

  console.log('circle',entry)
  if(entry && entry.enable) {
    circle.isShow = true;
  } else {
    circle.isShow = false;
  }

  const policyExpress = tabs.value.find((v) => v.value === "digital_platform_policy_express");
  const policyEntry = data?.entries?.find(v=>v.entryType === EntryType.PolicyExpress)
  if(policyEntry && policyEntry.enable) {
    policyExpress.isShow = true;
  } else {
    policyExpress.isShow = false;
  }


}

// const squareInfo = null;
const onActionSquareAxios = async (val?) => {
  // const params = {
  //   team_id: currentTeamId.value,
  //   // open_id: getOpenid(),
  // };
  // const [err, res] = await to(getSharedSquaresAxios(params));
  const params = {
    team_id: currentTeamId.value,
    // open_id: getOpenid(),
  };
  // const [err, res] = await to(getMemberSquaresAxios({ "page.size": 12 }, currentTeamId.value));
  const [err, res] = await to(getSharedSquaresAxios(params));

  if (err) {
    console.log(err?.message);
    return;
  }
  console.log(res);
  const { data } = res;
  // squareInfo.value = data;
  const album = tabs.value.find((v) => v.value === "album");
  if (data.opened) {
    // const tab = {
    //   label: "单位广场号",
    //   icon: square,
    //   value: "square",
    //   page: "",
    // };

    // if (!tabs.value.find((v) => v.value === "square")) {
    //   tabs.value.push(tab);
    // }
    album.isShow = true;

    album.query = {
      squareId: data?.square?.squareId
    }


    // 广场中的应用是否开启
    const circle = tabs.value.find((v) => v.value === "circle");
    circle.query = {
      id: data?.square?.squareId
    };
    onGetAppEntriesAxios(data?.square?.squareId)

  } else {
    // const findIndex = tabs.value.findIndex((v) => v.value === "square");
    // if (findIndex > -1) {
    //   tabs.value.splice(findIndex, 1);
    // }
    album.isShow = false;
  }
};
const onReload = () => {
  onGetMemberSquaresAxios(true);
};

const addressData = ref({});

// 地图 IP 定位
const { location, markerInfo, onMapInit } = useBaiduMap({
  onIPLocation() {
    const { point } = location.value;
    addressData.value = [point.lng, point.lat];
  },
  onPointGeocoder() {
    onGetMemberSquaresAxios(true);
  }
});

const onGetMemberSquaresAxios = async (isCover = false) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor

  // 缓存信息存储
  // const caches = store.getStorageDatas;
  // const cache = caches.find(v=>v.teamId === getCbdTeamID());

  // if(cache) {
  //   // memberList.value = cache.memberSquare?.items || [];
  //   // page.value = cache.memberSquare?.page;
  // } else {
  //   isLoading.value = true;
  // }
  console.log("getStorageDatas");
  try {
    console.log('addressData.value', addressData.value)
    result = await getMemberSquaresAxios({ "page.size": 10, 'latLng.latitude': addressData.value[1], 'latLng.longitude': addressData.value[0] }, currentTeamId.value);
    result = result.data.data;
    isNetworkError.value = false;
    if (!result) {
      isLoading.value = false;
      return;
    }
    squareResult.value = result;
    if (isCover) {
      squareList.value = result.items;
    } else {
      squareList.value = squareList.value.concat(result.items);
    }

    // page.value = result.page;
    // 缓存处理 start
    // const memberSquare = {
    //   items: toRaw(memberList.value),
    //   page: result.page
    // };
    // if(cache) {
    //   cache.memberSquare = memberSquare
    // } else {
    //   caches.push({teamId: getCbdTeamID(), memberSquare});
    // }
    // console.log('caches: ', caches)
    // store.setStorageDatas(caches)
    // 缓存处理 end
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    //   MessagePlugin.error(errMsg);
    console.log("dong:", errMsg);
    if (errMsg === "Network Error") {
      isNetworkError.value = true;
    }
  }
  isLoading.value = false;
};

const richArr = ref([]);
const pagination = {
  pageSize: 10,
  page: 1,
  total: 0,
};
const onSetArr = async (isCover = false) => {
  // const obj = {
  //     tip: '供应',
  //     name: '一次性丁腈乳胶弹pvc手套餐饮厨一次性丁腈乳胶弹pvc手套餐饮厨一次性丁腈乳胶弹pvc手套餐饮厨',
  //     company: '就看见四点零分考虑到实际发',
  //     area: '中国相关',
  // };

  // for (let i = 0; i< 200; i++) {
  //     richArr.value.push(obj);
  // }

  const params = {
    promotion_type: 2,
    promotion_related: currentTeamId.value,
    is_top: "",
    // title: keyword.value,
    ...pagination,
  };
  // if (['1', '2'].includes(currentTab.value.key)) {
  //   params.type = Number(currentTab.value.key);
  // } else if (currentTab.value.key === '3') {
  //   params.is_top = 1;
  // }

  // 缓存信息存储
  // const caches = store.getStorageDatas;
  // const cache = caches.find(v=>v.teamId === getCbdTeamID());
  // if(!cache) {
  //   isLoading.value = true;
  // }
  const [err, res] = await to(getMemberNichAxios(params, currentTeamId.value));
  isLoading.value = false;
  if (err) {
    console.log(err, "rich-err");
    if (err?.message === "Network Error") {
      isNetworkError.value = true;
      // if(cache) {
      //   richArr.value = cache.memberRich?.items || [];
      //   pagination.total = cache.memberRich?.total || 0;
      // }
    }
    return;
  }
  isNetworkError.value = false;
  let { data } = res;
  data = data.data;
  console.log(data);
  const richs =
    data.total > 0
      ? data.list.map((v) => ({
          ...v.apply_data,
          uuid: v.uuid,
          images: v.images.length > 0 ? getSrcThumbnail(v.images[0]?.file_name) : "",
          is_top: v.is_top,
          type: v.type,
          title: v.title,
          type_text: useType(v.type),
        }))
      : [];
  pagination.total = data.total;
  if (isCover) {
    richArr.value = richs;
  } else {
    richArr.value = richArr.value.concat(richs);
  }
  // const memberRich = {
  //   items: toRaw(richArr.value),
  //   total: data.total
  // };
  // if(cache) {
  //   cache.memberRich = memberRich
  // } else {
  //   caches.push({teamId: getCbdTeamID(), memberRich});
  // }
};
const useType = (type) => {
  let msgType = "";
  if (type === 1) {
    msgType = "供应";
  } else if (type === 2) {
    msgType = "需求";
  }
  return msgType;
};

const richArrEbook = ref([]);
const onSetArrEbook = async (isCover = false) => {
  const params = {
    team_id: currentTeamId.value,
    // name: keyword.value,
    pageSize: 5,
    channel_type: "digital_platform",
    // page: pagination.page
  };
  // 缓存信息存储
  // const caches = store.getStorageDatas;
  // const cache = caches.find((v) => v.teamId === getCbdTeamID());
  isLoading.value = true;
  const [err, res] = await to(getEbookShareList(params));
  isLoading.value = false;
  // if (err) {
  //   console.log(err, 'rich-err');
  //   if (err?.message === 'Network Error' && cache) {
  //     isNetworkError.value = true;
  //     richArr.value = cache.memberRich?.items;
  //     pagination.total = cache.memberRich?.total;
  //   }
  //   return;
  // }
  isNetworkError.value = false;
  let { data } = res;
  data = data.data;
  console.log(data);
  const richs =
    data.total > 0
      ? data.list?.map((v) => {
          v.cover = getSrcThumbnail(v.cover);
          return v;
        })
      : [];
  pagination.total = data.total;
  if (isCover) {
    richArrEbook.value = richs;
  } else {
    richArrEbook.value = richArrEbook.value.concat(richs);
  }
  // const memberRich = {
  //   items: toRaw(richArr.value),
  //   total: data.total
  // };
  // if (cache) {
  //   cache.memberRich = memberRich;
  // } else {
  //   caches.push({ teamId: getCbdTeamID(), memberRich });
  // }
};

const annualFeeDialogUpgradeVisible: Ref<any> = ref(false);
const squareTeamId = ref("");
const onGoSquarePage = (square) => {
  if (square.connected) {
    // 如果已经链接
    console.log("如果已经链接");
    onActionSquare({}, square?.square?.squareId);
  } else {
    if (square?.square?.squareType === SquareType.Individual) {
      onActionSquare({}, square?.square?.squareId);
      return;
    }
    annualConnectRef.value.onConnectPlatform({
      relation_team_id: square?.square?.originId,
    });
    // const param = {
    //   squareId: square?.square?.squareId,
    //   belong_team_id: currentTeamId.value,
    //   consume_team_id: square?.square?.originId
    // };
    // onDisplayToPlatform(param, currentTeamId.value).then((res) => {
    //   if (res === 'update') {
    //     console.log('update');
    //     squareTeamId.value = square?.square?.originId;
    //     annualFeeDialogUpgradeVisible.value = true;
    //   } else if (res === 'success') {
    //     onGetMemberSquaresAxios(true); // 会员广场
    //   }
    // }).catch((err) => {
    //   console.log('err:', err);
    //   if (err && err.message) {
    //     MessagePlugin.error(err.message);
    //   }
    // });
  }
};

// 升级后的回调
const upgradeLoaded = () => {
  // console.log('upgradeLoaded')
  // MessagePlugin.success('购买成功')
  onGetMemberSquaresAxios(true); // 会员广场
  // 升级后重新触发流程
  // upgradePackageRef.value.onClose();
  // onRunTeamAnnualFee()
};

// const onOpenEbook = (data: any) => {
//   const url = `${getBaseUrl("h5")}/ebook/browse?id=${data.uuid}`;
//   openUrlByBrowser(url);
// };

const onOpenEbook = (data: any) => {
  let url = "";
  if (data.file.type === "pdf") {
    url = `${getBaseUrl("h5")}/ebook/browse?id=${data.uuid}`;
  } else {
    url = data.file.url;
  }
  openUrlByBrowser(url);
};

const openUrlByBrowser = (url: string) => {
  shell.openExternal(url);
};

const detailsReadonlyRef = ref(null);
const onOpenRich = (row) => {
  // detailsReadonlyRef.value.detailsOpen(row.uuid);
  const quParams = {
    uuid: row.uuid,
    from: "digital",
  };
  const pageKey = "cbd_rich_detail";
  if (platformCpt.value === platform.digitalPlatform) {
    const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(pageKey));
    // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
    searchMenu.query = { ...quParams, platform: platform.digitalPlatform, from: "cbd" };
    router.push({ path: searchMenu.fullPath, query: searchMenu.query });
    searchMenu.title = row.title;
    digitalPlatformStore.addTab(toRaw(searchMenu), true);
  } else {
    const searchMenu = routeList.find((v) => v.name === pageKey);
    // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
    router.push({ path: searchMenu.fullPath, query: quParams });
    searchMenu.title = row.title;
    store.value.addTab(toRaw(searchMenu));
  }
};

const jumpTo = () => {
  const jumpPage = route.query?.jumpPage;
  if (jumpPage) {
    const tab = tabs.value.find((v) => v.page === jumpPage);
    tab && goTabNew(tab);
  }
};

// 关于我们是否显示红点
const visibleProgress = ref(false); // 组织历程
const visibleIntroduce = ref(false); // 组织介绍
const visibleHonor = ref(false); // 荣誉榜
const redPoint = () => {
  getRedPoint(digitalPlatformStore.activeAccount.teamId)
    .then((res) => {
      console.log(res.data.data, "是否显示红点------------------------------");
      res.data.data.progress.platform != 0 ? (visibleProgress.value = true) : (visibleProgress.value = false);
      res.data.data.introduce.platform != 0 ? (visibleIntroduce.value = true) : (visibleIntroduce.value = false);
      res.data.data.honor.platform != 0 ? (visibleHonor.value = true) : (visibleHonor.value = false);
    })
    .catch((err) => {
      MessagePlugin.error(err.message);
    });
};
// 是否显示入口
const showAbout = ref(false);
const enter = () => {
  listSwitch(digitalPlatformStore.activeAccount.teamId, { channel_type: "digital_platform" }).then((res) => {
    if (res.data.data.honor_list_data || res.data.data.introduce_list_data || res.data.data.progress_list_data) {
      showAbout.value = true;
    }
    if (!res.data.data.honor_list_data && !res.data.data.introduce_list_data && !res.data.data.progress_list_data) {
      showAbout.value = false;
      // 如果都没有数据就不显示关于我们入口
      // tabs.value = tabs.value.filter((item) => item.page != "member_about");
      console.log(tabs.value, "如果都没有就不显示关于我们入口");
    }
  });
};
const getfengcai = async () => {
  const findIndexTabfengcai = tabs.value.find((v) => v.value === "digital_platform_fengcai");

  if (appsStateData && appsStateData.code === 0 && appsStateData.data["platform-view"] === false) {
    findIndexTabfengcai.isShow = false;
  } else {
    findIndexTabfengcai.isShow = true;
  }
};
watch(() => digitalPlatformStore.unreadData, (val) => {
  const { post = 0, likes = 0, systems = 0, comments = 0, atMe = 0 } = val;
  appUnreadCount["digital_platform_forum"] = atMe + post + comments + systems + likes;
});
onMountedOrActivated(async () => {
  digitalPlatformStore.getUnreadStats(currentTeamId.value);
  // onGetMemberSquaresAxios(true); // 会员广场
  onSetArr(true); // 商机
  onSetArrEbook(true); // 会刊
  redPoint(); // 关于我们的红点
  enter(); // 关于我们的入口
  getNoticeRed(); // 公告红点
  const { data } = await getAppsState(currentTeamId.value);
  appsStateData = data;
  getSocietyArticleChannelInfo();
  getfengcai();
  onActionSquareAxios();
  const isPlatformStaff = digitalPlatformStore.activeAccount.user_ids.platformStaff !== 0;
  // const index = tabs.value.findIndex((tab) => tab.label === "公告");
  // const noticeTab = tabs.value.find((tab) => tab.uuid === "digital_platform_notice");
  // if (isPlatformStaff) {
  //   noticeTab.isShow = true;
  // } else {
  //   noticeTab.isShow = false;
  // }

  if (currentTeamId.value) {
    initData();
  }
  jumpTo();
  // onActionSquareAxios(); // 用来判定有无本会广场
  ipcRenderer.invoke('IM-refresh');
  // bus.emit({ name: 'work-area-refresh' });
});
// onMountedOrActivated存在一定情况不刷新
onActivated(() => {});
// 获取数字城市党建状态以及未读信息
const getSocietyArticleChannelInfo = async () => {
  const findIndexTab = tabs.value.find((v) => v.value === "society-article");
  const findIndexTabfengcai = tabs.value.find((v) => v.value === "digital_platform_fengcai");
  console.log(findIndexTabfengcai, "findIndexTabfengcaifindIndexTabfengcai");
  const currentTeamIdVal = currentTeamId.value;
  // const { data: appsStateData } = await getAppsState(currentTeamIdVal);
  console.log(appsStateData.data, "appsStateData.dataappsStateData.data");
  if (appsStateData && appsStateData.code === 0 && appsStateData.data["society-article"] === false) {
    // if (findIndex > -1) {
    //   tabs.value.splice(findIndex, 1);
    // }
    findIndexTab.isShow = false;

    console.log("society-article", findIndexTab);
    return;
  } else {
    findIndexTab.isShow = true;
  }
  if (appsStateData.code === 0 && appsStateData.data["platform-view"] === false) {
    // if (findIndex > -1) {
    //   tabs.value.splice(findIndex, 1);
    // }
    findIndexTabfengcai.isShow = false;
    return;
  } else {
    findIndexTabfengcai.isShow = true;
  }
  const { data: channelInfoData } = await societyArticleChannelInfo(ArticleChannel.SOCIETY_ARTICLE_CHANNEL_SP, {
    team_id: currentTeamIdVal,
  });
  if (channelInfoData.code === 0) {
    // if (channelInfoData.data.empty) {
    //   if (findIndex > -1) {
    //     tabs.value.splice(findIndex, 1);
    //   }
    // } else

    if (channelInfoData.data.empty === false) {
      appUnreadCount["society-article"] = channelInfoData.data.unread;
    }
  }
};

const scrolledDistance = ref(0); // 滚动距离
const containerFlets = ref(null);
const handleScroll = (event) => {
  console.log(event.target.scrollTop, "e");
  scrolledDistance.value = event.target.scrollTop;
  console.log(scrolledDistance.value, "few");
  // scrolledDistance.value = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
};
let animationId = null;
const scrollToTop = () => {
  console.log(containerFlets.value.scrollTop);
  // containerFlets.value.scrollTop = 0; // 滚动容器到顶部
  cancelAnimationFrame(animationId); // 取消之前的动画

  const scrollTop = containerFlets.value.scrollTop;
  console.log(containerFlets.value.scrollTop);
  const step = Math.ceil(scrollTop / 6); // 每帧滚动的步长
  console.log(step);
  const animate = () => {
    if (containerFlets.value.scrollTop > 0) {
      containerFlets.value.scrollTop -= step;
      animationId = requestAnimationFrame(animate); // 请求下一帧动画
    } else {
      cancelAnimationFrame(animationId); // 动画结束，取消请求
    }
  };

  animationId = requestAnimationFrame(animate); // 开始动画
};

onMounted(() => {
  containerFlets.value?.addEventListener("scroll", handleScroll); // 监听滚动事件
});
onUnmounted(() => {
  containerFlets.value?.removeEventListener("scroll", handleScroll);
});

const showCalling = ref(false);
const callingRef = ref(null);
const callingOpen = () => {
  callingRef.value.some();
};
const confirmLogo = (data) => {
  const params = {
    id: currentMemberCard.value?.id,
    directory_image_values: [data],
    is_contact: currentMemberCard.value?.is_contact,
  };
  editSpecifiedFieldsCBR(params, currentTeamId.value)
    .then((res) => {
      if (res) {
        MessagePlugin.success("修改成功");
        onInitData();
      }
    })
    .catch((err) => {
      console.log(err);
      MessagePlugin.error(err?.message);
    });
};
</script>

<style lang="less" scoped>
// lss 加个滚动条样式
@import "@renderer/views/member/member_number/panel/member-panel/less/home-panel.less";
// src\renderer\assets\cbd\svg\cbd_bg.svg

:deep(.body) {
  background: url("@renderer/assets/cbd/svg/cbd_bg.svg") !important;
}
.tabs {
  .active {
    // border-radius: 16px;
    background: var(--kyy_blue-kyy_color_kyyBlue_default, #21acfa) !important;
  }
}
</style>
