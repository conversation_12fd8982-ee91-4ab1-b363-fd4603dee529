<template>
  <div class="trends-item">
    <!-- <img
      v-if="postItem.post.pin && !hideTop"
      src="@/assets/square/top-banner.png"
      alt=""
      class="top-banner"
    > -->

    <div class="right-content" :class="{ 'mt-16': data.stickOnTop }">
      <template v-if="goDetail">
        <div class="flex flex-center gap-12">
          <Avatar
            avatar-size="40px"
            :image-url="owner?.avatar ?? ''"
            :user-name="owner?.remark || owner?.name"
            round-radius
            :class="['avatar', { cursor: canHeadClick }]"
            @click.stop="clickHead"
          />

          <div class="flex-1 flex">
            <div class="flex flex-col">
              <div class="top">
                <div class="flex-y-center flex-1">
                  <div :class="['name', { cursor: canHeadClick }]" @click.stop="clickHead">
                    {{ owner?.remark || owner?.name }}
                  </div>
                  <span v-if="showFlag" class="tag">{{ flagText }}</span>
                </div>
              </div>

              <div class="time">{{ postedAtFormat }}</div>
            </div>

            <t-button
              v-if="BtnStatusMap[data.post.friendStatus]?.show"
              :disabled="BtnStatusMap[data.post.friendStatus]?.disabled"
              class="add-friend-btn ml-auto px-8! h-24!"
              variant="outline"
              theme="primary"
              @click="applyVisible = true"
              >{{ BtnStatusMap[data.post.friendStatus]?.text }}</t-button
            >
          </div>

          <div v-if="isSelfPost && !hideSetting" class="setting" @click.stop>
            <PostSetting
              :key="settingKey"
              :hide-top="hideTop"
              :default-value="postItem"
              :has-top-post="hasTopPost"
              :post-id="postItem.post.id"
              :self="self"
              @removed="emit('removed')"
              @toggle-top="emit('toggle-top', $event)"
            />
          </div>
        </div>
      </template>
      <div class="pl-[52px] w-full">
        <PostItemContent
          v-bind="$attrs"
          :data="postItem"
          :preview="preview"
          in-list
          @click-content="(e) => emit('click-content', e)"
          @toolbar-change="emit('toolbar-change', $event)"
        />
      </div>
    </div>
  </div>
  <apply-dialog
    v-model:visible="applyVisible"
    showOverlay
    :cardInfo="cardInfo"
    :myId="cardInfo?.myId"
    @onApplyContact="onConfirm"
    @onconfirm="onConfirm"
  />
</template>

<script setup lang="ts">
import { computed, ref, watch, inject } from 'vue';
import Avatar from '@renderer/components/kyy-avatar/index.vue';
import { timeAgo } from '@/views/square/utils/time';
import PostItemContent from './PostItemContent.vue';
import applyDialog from '@renderer/views/identitycard/dialog/applyContact.vue';
import PostSetting from './PostSetting.vue';
import { useForumStore } from '../../store';
import { flagMap, BtnStatusMap } from '../../constant';
import { getOpenid } from '@renderer/utils/auth';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  goDetail: Boolean,
  prevTime: String,
  inList: Boolean,
  preview: Boolean,
  // 是否隐藏更多按钮
  hideMoreNav: Boolean,
  hideTop: Boolean,
  // 是否有置顶动态
  hasTopPost: {
    type: Boolean,
    default: false,
  },
  showFollow: Boolean,
  canHeadClick: {
    type: Boolean,
    default: true,
  },
  showFlag: Boolean,
  // 隐藏设置
  hideSetting: Boolean,
  // 是否是自已的帖子
  self: Boolean,
});
const emit = defineEmits([
  'click-head',
  'click-more',
  'click-content',
  'toolbar-change',
  'init',
  'removed',
  'toggle-top',
]);

const forumStore = useForumStore();
const data = ref<any>({
  ...props.data,
  owner: props.data.owner || forumStore.currCard,
});

// 数据更新
watch(
  () => props.data,
  () => {
    data.value = {
      ...props.data,
      owner: props.data.owner || forumStore.currCard,
    };
  },
  { deep: true },
);

const postItem = computed<Record<string, any>>({
  get() {
    return data.value;
  },
  set(val) {
    data.value = val;
  },
});
const owner = computed(() => postItem.value.owner || forumStore.currCard);
const postedAtFormat = computed(() => timeAgo(props.data?.post?.postedAt));

// 申请加好友
const applyVisible = ref(false);
const cardInfo = ref({
  myId: getOpenid() || '',
  openid: owner.value.openid,
  cardId: owner.value.openid,
  staffOpenId: owner.value.openid,
  name: owner.value.name,
  avatar: owner.value.avatar,
});
const onConfirm = () => {
  emit('init');
};

// 更新备注
watch(
  () => forumStore.refreshRemark,
  (val) => {
    if (!val || val.cardId !== owner.value.cardId) return;
    data.value.owner.remark = val.remark;
  },
  { deep: true },
);

const clickHead = () => {
  if (!props.canHeadClick) return;
  emit('click-head', postItem.value);
};

const isSelfPost = computed(() => forumStore.currCard?.openid === owner.value?.openid);
const settingKey = ref(1);
const flagText = computed(() => {
  return flagMap[postItem.value.owner.digitalPlatformFlag];
});
</script>

<style lang="less" scoped>
.trends-item {
  position: relative;
  display: flex;
  padding: 16px;
  padding-bottom: 12px;
  width: 100%;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  background: var(--bg-kyy-color-bg-light, #fff);
  overflow: hidden;

  .top-banner {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    z-index: 1;
  }

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 6px;
  }

  .setting {
    z-index: 9;
  }

  .right-content {
    flex: 1;
    max-width: 100%;
    .add-friend-btn {
      :deep(.t-button__text) {
        font-size: 12px !important;
      }
    }
    .top {
      display: flex;
      align-items: center;
      .name {
        margin-right: 4px;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        text-align: left;
        color: var(--text-kyy-color-text-1, #1a2139);
        line-height: 24px;
        display: inline-block;
        max-width: 100%;
        .ellipsis();
        &:hover {
          color: var(--brand-kyy_color_brand_default, #4d5eff);
        }
      }
      .tag {
        display: flex;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: var(--checkbox-kyy_radius_checkbox, 2px) 4px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_brand, #eaecff);
        color: var(--kyy_color_tag_text_brand, #4d5eff);
        text-align: center;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
      .nav {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: -24px;
        margin-bottom: -24px;
        margin-right: -24px;
        padding: 14px 24px;
        cursor: pointer;
        .follow-btn {
          height: 26px;
          border-radius: 20px;
          background-color: rgb(31, 109, 250);
          margin-right: 0;
          padding: 2px 12px;
        }

        .count-wrap {
          height: 48px;
          border-radius: 4px;
          display: flex;
          padding: 4px 0px;
          align-items: center;
          gap: 4px;
          align-self: stretch;
          &:hover {
            background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
          }
          &.viewed {
            &:hover {
              color: var(--brand-kyy_color_brand_default, #4d5eff);
              background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
            }
            .count {
              color: var(--brand-kyy_color_brand_default, #4d5eff);
              background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
            }
            .icon {
              color: var(--brand-kyy_color_brand_default, #4d5eff);
            }
            .count-inner {
              background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
              .count {
                padding-right: 0;
                background-color: transparent;
              }
            }
          }
        }

        .count-inner {
          display: flex;
          border-radius: 99px;
          .count {
            background: var(--kyy_color_badge_bg, #ff4aa1);
          }
        }

        .count {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 500;
          padding: 0 6px;
          min-width: 20px;
          height: 20px;
          line-height: 20px;
          color: #fff;
          border-radius: var(--kyy_radius_badge_full, 999px);
          background: var(--kyy_color_badge_bg, #ff4aa1);
          cursor: pointer;
        }
        .icon {
          color: #a1a2a4;
          font-size: 20px;
          border-radius: 999px;
        }
      }
    }
    .time {
      display: flex;
      font-size: 14px;
      color: var(--text-kyy_color_text_3, #828da5);
    }
  }
}

.time-wrap {
  width: 36px;
  margin-right: 18px;
  text-align: center;
  z-index: 9;
  .month {
    font-weight: bold;
    font-size: 16px;
    color: #13161b;
  }
  .day {
    font-size: 12px;
    color: #717376;
  }
}
</style>
