import { getCards, getStaff, getProfilesInfo,getPlatform } from '@/utils/auth'
import { cardIdType } from "@/views/identitycard/data";

import { i18nt } from "@/i18n";

export interface SelectMemberInfo {
    openId: string
    cardId: string
    avatar?: string
    comment?: string
    nickname?: string
    staffName?: string
    departmentId?: string,
    departmentName?: string
    jobId?: string
    jobName?: string
    stayOn?: number
    teamId?: string
    teamName?: string
    type?:string
}


export const getAllCards = () => {
    const list: SelectMemberInfo[] = [];
    const info = getProfilesInfo()
    info && list.push({ openId: info.openid, cardId: info.openid, nickname: info.title, avatar: info?.avatar});

    const convertCardHandler = (item) => list.push({
        openId: info.openid,
        cardId: item.uuid,
        avatar: item.avatar,
        nickname: item.name,
        teamId: item.teamId,
        teamName: item.team,
        type:item.type || ''
    });
    getStaff()?.forEach(convertCardHandler);
    getCards()?.forEach(convertCardHandler);
    getPlatform()?.forEach(convertCardHandler);
    return list;
}

// * NORMAL = 0; // 普通群
// * INNER = 3; // 内部群
// * OUT = 10; // 外部群
// * FAMILY = 15; // 亲友群现在可以拉外部身份
// * PLATFORM  = 20; // 平台群
export const getAllCardsByGroupType = (type?: number) => {
    const info = getProfilesInfo()

    if (type === 0) {
        return [{ openId: info.openid, cardId: info.openid, nickname: info.title, avatar: info?.avatar }];
    }
    if (type === 15) {
      const cards = [{ openId: info.openid, cardId: info.openid, nickname: info.title, avatar: info?.avatar }];
         getCards().forEach((item) => {
          cards.push({
            openId: info.openid,
            cardId: item.uuid,
            avatar: item.avatar,
            nickname: item.name,
            teamId: item.teamId,
            teamName: item.team,
            type: item.type
          });
        });
      return cards;
    }
    if (type === 3) {
       const inner = getStaff()?.map(item => ({
            openId: info.openid,
            cardId: item.uuid,
            avatar: item.avatar,
            nickname: item.name,
            teamId: item.teamId,
            teamName: item.team,
            type:item.type
        })) || []
      return [...inner]
    }
    if (type === 20) {
       const inner = getStaff()?.map(item => ({
            openId: info.openid,
            cardId: item.uuid,
            avatar: item.avatar,
            nickname: item.name,
            teamId: item.teamId,
            teamName: item.team,
            type:item.type
        })) || []
      const plat = getPlatform()?.map(item => ({
          openId: info.openid,
          cardId: item.uuid,
          avatar: item.avatar,
          nickname: item.name,
          teamId: item.teamId,
          teamName: item.team,
          type:item.type
      })) || []

      return [...inner,...plat]
    }

    if (type === 10) {
        return getCards().map( item => ({
            openId: info.openid,
            cardId: item.uuid,
            avatar: item.avatar,
            nickname: item.name,
            teamId: item.teamId,
            teamName: item.team,
            type: item.type
        }));
    }
    return getAllCards();
}


export const getAllGroupCardsByGroupType = (type) => {
  const info = getProfilesInfo();
  // 抽取公共逻辑到单独的函数
  const createCardItem = (item) => ({
    openId: info.openid,
    cardId: item.uuid,
    avatar: item.avatar,
    nickname: item.name,
    teamId: item.teamId,
    teamName: item.team,
    type: item.type
  });

  const createCardGroup = (groupName, itemsGetter) => {
    const items = itemsGetter();
    return {
      group: i18nt(`contacts.${groupName}`),
      expand: false,
      children: items?.map((v) => createCardItem(v))
    };
  };
  // 个人身份
  const guide_profile_card = createCardGroup('guide_profile_card', () => ([{
    openId: info.openid, cardId: info.openid, nickname: info.title, avatar: info?.avatar || '', uuid: info.openid || ''
  }]));
  // 内部身份
  const guide_inner_card = createCardGroup('guide_inner_card', getStaff);
  // 外部身份
  const guide_out_card = createCardGroup('guide_out_card', getCards);
  // 平台身份
  const guide_platform_card = createCardGroup('guide_platform_card', getPlatform); 
  // 根据type返回不同的卡片组
  switch (type) {
    case 0:
    case 15:
      return [guide_profile_card];

    case 3:
      return [getStaff()?.length ? guide_inner_card : []];

    case 10:
      return [guide_out_card];

    case 20:
      return [getStaff()?.length ? guide_inner_card : null, getPlatform()?.length ? guide_platform_card : null].filter(Boolean);

    case 30:
      return [getStaff()?.length ? guide_inner_card : null,
        getCards()?.length ? guide_out_card : null]

    default:
      return [guide_profile_card,
        getStaff()?.length ? guide_inner_card : null,
        getCards()?.length ? guide_out_card : null,
        getPlatform()?.length ? guide_platform_card : null
      ].filter(Boolean);
  }
};

// export const getAllGroupCardsByGroupType = (type?: number) => {
//     const info = getProfilesInfo()
//     if (type === 0 || type == 15) {
//         return [
//             {
//               group: i18nt('contacts.guide_profile_card'),
//               expand:false,
//               children: [{ openId: info.openid, cardId: info.openid, nickname: info.title, avatar: info?.avatar}]
//             }
//         ]
//     }

//     if (type === 3) {
//       const cardList = []
//       cardList.push({
//         group: i18nt('contacts.guide_inner_card'),
//         expand:false,
//         children: getStaff().map(item => ({
//               openId: info.openid,
//               cardId: item.uuid,
//               avatar: item.avatar,
//               nickname: item.name,
//               teamId: item.teamId,
//               teamName: item.team,
//               type:item.type

//           }))
//         })
//       // if(getPlatform()?.length){
//       //   cardList.push({
//       //       group: i18nt('contacts.guide_platform_card'),
//       //       expand:false,
//       //       children: getPlatform()?.map(item => ({
//       //           openId: info.openid,
//       //           cardId: item.uuid,
//       //           avatar: item.avatar,
//       //           nickname: item.name,
//       //           teamId: item.teamId,
//       //           teamName: item.team,
//       //           type:item.type
//       //       }))
//       //   })
//       // }
//       return cardList
//     }

//     if(type === 10) {
//         return [
//             {
//               group: i18nt('contacts.guide_out_card'),
//               expand:false,
//               children: getCards().map(item => ({
//                     openId: info.openid,
//                     cardId: item.uuid,
//                     avatar: item.avatar,
//                     nickname: item.name,
//                     teamId: item.teamId,
//                     teamName: item.team,
//                     type:item.type

//                 }))
//             }
//         ]
//     }

//     const cardList = []
//     cardList.push({
//         group: i18nt('contacts.guide_profile_card'),
//         expand:false,
//         children: [{ openId: info.openid, cardId: info.openid, nickname: info.title, avatar: info?.avatar}]
//     })
//     if(getStaff()?.length){
//         cardList.push({
//             group: i18nt('contacts.guide_inner_card'),
//             expand:false,
//             children: getStaff()?.map(item => ({
//                 openId: info.openid,
//                 cardId: item.uuid,
//                 avatar: item.avatar,
//                 nickname: item.name,
//                 teamId: item.teamId,
//                 teamName: item.team,
//                 type:item.type
//             }))
//         })
//     }
//     if(getCards()?.length){
//         cardList.push({
//             group: i18nt('contacts.guide_out_card'),
//             expand:false,
//             children: getCards().map(item => ({
//                 openId: info.openid,
//                 cardId: item.uuid,
//                 avatar: item.avatar,
//                 nickname: item.name,
//                 teamId: item.teamId,
//                 teamName: item.team,
//                 type:item.type
//             }))
//         })
//     }
//     if(getPlatform()?.length){
//         cardList.push({
//             group: i18nt('contacts.guide_platform_card'),
//             expand:false,
//             children: getPlatform()?.map(item => ({
//                 openId: info.openid,
//                 cardId: item.uuid,
//                 avatar: item.avatar,
//                 nickname: item.name,
//                 teamId: item.teamId,
//                 teamName: item.team,
//                 type:item.type
//             }))
//         })
//     }
//     return cardList
// }

export const getDepartment = (curCard: SelectMemberInfo) => {
    let positions: Partial<SelectMemberInfo>[];
    let MemberCardType = cardIdType(curCard.cardId)
    if(MemberCardType === 'outer') {
        positions = getCards()?.find(item => item.uuid === curCard.cardId)?.position;
    } else if(MemberCardType === 'inner') {
        positions = getStaff()?.find(item => item.uuid === curCard.cardId)?.position;
    }else if(MemberCardType === 'platform'){
        positions = getPlatform()?.find(item => item.uuid === curCard.cardId)?.position;
    }

    return positions?.[0] ?? {
        departmentId: null ,
        departmentName: null,
        jobId: null,
        jobName: null,
        comment: null,
        staffName: null,
        stayOn: 0,
    }
}

export const getGroupType = (list: SelectMemberInfo[], myTeamId?: string, extend?: { groupSearchType?: string }) => {
  // 如果是单聊，并且任意一方是平台身份，则建立平台群，因为只要你能和一个平台成员聊天，那么你必定有内部或者平台身份
  // 单聊并且选择的群聊类型是平台，建平台群
  if (list.some((it) => cardIdType(it.cardId) === 'platform') || extend.groupSearchType === 'platform') {
    return 20; // 平台群
  }
  if (myTeamId && list.every((it) => it.teamId === myTeamId)) {
    return 3; // 内部群
  }
  if (!myTeamId && list.every((it) => !it.teamId)) {
    return 0; // 普通群
  }
  return 10; // 外部群
};
// 组织选人左侧菜单分类
export const getMenusObj = ()=> {
  return {
    'inner': ['organize','recent', 'tags'],
    'outer': ['recent', 'orgcontacts', 'external', 'tags'],
    'personal': ['friend', 'recent', 'orgcontacts', 'tags'],
    'platform': ['platform', 'receiver','recent', 'tags'],
  }
}
