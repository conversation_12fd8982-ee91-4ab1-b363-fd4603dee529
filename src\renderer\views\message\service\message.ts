import { MsgShareType } from "@renderer/utils/share";
import { safeParseJson } from "@renderer/utils/assist";
import { isMsgCanMerge, isMsgContentSame, appContentType } from "./msgUtils";

// 消息包装类
export default class MsgWrapper {
	private static messages: MessageToSave[] = [];

	// 消息
	msg: MessageToSave;

	// 被合并的消息
	merged?: MsgWrapper[];

	selected = false;

	uploadProgress = 0;

	constructor(msg: MessageToSave) {
		this.msg = msg;
	}

	get canSelect() {
		return (this.msg.messageType === 'text' || this.msg.messageType === 114) && !this.isMerged &&
			![MsgShareType.consult, MsgShareType.consult_order].includes(this.msg.contentExtra?.contentType);
	}

	get isMerged() {
		return this.merged?.length > 0;
	}

	get id() {
		return this.msg.id;
	}

	get messageUId() {
		return this.msg.messageUId;
	}

	get targetId() {
		return this.msg.targetId;
	}

	get sentTime() {
		return this.msg.sentTime;
	}

	get receiptTime() {
		return this.msg.receiptTime;
	}

	get receipts() {
		return this.msg.receipts;
	}

	static onMessages(list: MessageToSave[], sort = true, options?) {
		list.forEach((item) => {
			// 不能查看历史，加入群之前的历史过滤掉
			// if (options && !options.viewHistory && options.joined) {
			//     if (options.joined.toString.length < 11 ? item.sentTime < options.joined * 1000 : item.sentTime < options.joined) return;
			// }
			const index = MsgWrapper.messages.findIndex((msg) => {
				return (msg.messageUId && msg.messageUId === item.messageUId) || (msg.tempId && msg.tempId === item.tempId);
			});
			if (['RC:ReferenceMsg', 'RC:TxtMsg', 'RC:RCNtf'].includes(item.messageType)) {
				item.messageType = { 'RC:ReferenceMsg': 114, 'RC:TxtMsg': 'text', 'RC:RCNtf': 2101 }[item.messageType];
			}

			// 移动端上报的融云历史数据传了移动端的结构需要做兼容
			if (appContentType(item.contentExtra)) {
				if (!item.contentExtra?.content) {
					item.contentExtra = { contentType: item.contentExtra.data?.type, data: safeParseJson(item.contentExtra.data?.content), scene: item.contentExtra.data?.scene };
				}
			}
			if (index === -1) {
				MsgWrapper.messages.unshift(item);

			}
		});
		if (!sort) return;
		this.sortMessages();
	}

	static sortMessages = () => {
		MsgWrapper.messages = MsgWrapper.messages.sort((a, b) => {
			// 拉历史消息撤回消息，原撤回消息的时间排序
			// const aRCNSent = a.content?.sentTime || '';
			// const bRCNSent = b.content?.sentTime || '';

			// const aSent = aRCNSent || a.sentTime;
			// const bSent = bRCNSent || b.sentTime;
			return b.sentTime - a.sentTime;
		});
	}
	// 消息发送后，修改消息的messageUId
	static changeMsg(tempId:string, messageUId: string,  sentStatus: number,) {
		const msg = MsgWrapper.messages.find((msg) => msg.tempId === tempId);
		msg.sentStatus = sentStatus;
 		if(messageUId){
			msg.messageUId = messageUId
			msg.clientMsgID = messageUId;	
		};
	}
	static changeMsgSendStatus(messageUId: string, sentStatus: number,) {
		
		const msg = MsgWrapper.messages.find((msg) => msg.messageUId === messageUId || msg.tempId === messageUId);
		msg && (msg.sentStatus = sentStatus);
	}
	/**
	 * 从数据库加载数据
	 * @param list 消息列表，顺序为 id 降序
	 */
	static onLoadDbMessages(list: MessageToSave[], options?) {
		this.onMessages(list, true, options);
	}

	/**
	 * 从服务器接受消息，由于撤回消息的存在，所以需要检查消息是否存在
	 * @param list 接受的消息列表
	 */
	static onReceiveRemoteMessages(list: MessageToSave[]) {
		this.onMessages(list, false);
	}

	/**
	 * 撤回操作
	 * @param msg 撤回的消息
	 */
	static recallMessage(msg: MessageToSave) {
			MsgWrapper.messages.map((item) => {
				if (msg.messageUId === item.messageUId) {
					item.messageType = 2101;
				}
				// 自己引用自己和自己引用别人的数据结构不一样
				if (item.messageType === 114 && ((item.content?.referMsgUid && item.content.referMsgUid === msg.messageUId) || item.quoteElem?.quoteMessage?.clientMsgID === msg.messageUId)) {
					console.log('===>recallMessage', item);
					item.content && (item.content = { ...item.content, contentType: 2101 })
					item.quoteElem?.quoteMessage && (item.quoteElem.quoteMessage.contentType = 2101);
				}
			})			
	}

	static onDeleteMsg(messageUId: string) {
		const index = MsgWrapper.messages.findIndex((msg) => msg.messageUId === messageUId);
		if (index !== -1) {
			MsgWrapper.messages.splice(index, 1);
		}
	}

	/**
	 * 清除消息
	 */
	static cleanMessages() {
		MsgWrapper.messages = [];
	}

	/**
	 * 获取通过计算后的消息
	 * @returns
	 */
	static getComputeMessages(): MsgWrapper[] {
		return getMessagesComputed(MsgWrapper.messages, 'msgWrapper');
	}
}

/**
 * 整理是否消息内容相同合并+1
 * @param messages
 * @returns
 */
export const getMessagesComputed = (messages: MessageToSave[], from?:string) => {
	if (!messages.length) return [];
	if (messages[0].conversationType !== 3) return messages.sort((a, b) => a.sentTime - b.sentTime).map((item) => new MsgWrapper(item));
	let mergedMsg: MessageToSave[] = [];
	const results: MsgWrapper[] = [];
	for (let index = messages.length; index > 0; index--) {
		const curMsg = messages[index - 1];
		if(curMsg.sentStatus === 20){
				results.push(new MsgWrapper(curMsg));
				if(index > 1){
					continue;
				}
		}
		let merged = false;
		// 判断是否可以合并
		const canMerge = isMsgCanMerge(curMsg);		
		
		if ( canMerge && (!mergedMsg.length || isMsgContentSame(curMsg, mergedMsg[0]))) {
			// 建国：主要是针对这种 +1 失败的消息不展示，不过其他正常发送失败的消息确实要展示
			merged = true;
			mergedMsg.push(curMsg);
			// 循环未结束，继续合并，循环到最后一条消息，则结束合并
			if (index > 1) {
				continue;
			}
		}
		
		// 合并结束，相同消息大于等于3条，合并
		let len = mergedMsg.length 
		if (len >= 3) {
			const item = new MsgWrapper(mergedMsg[len-1]);
			if(from === 'msgWrapper'){// 合并消息，取第一条合并消息的atInfo
				item.msg.contentExtra.data = mergedMsg[0].contentExtra.data
			}
			item.merged = mergedMsg.slice(0,-1).map((item) => new MsgWrapper(item));
			results.push(item);
		} else if (mergedMsg.length) {
			mergedMsg.forEach((item) => results.push(new MsgWrapper(item)));
		}

		if (index > 1 && canMerge && !merged) {
			mergedMsg = [curMsg];

		} else if (!merged && curMsg.sentStatus !== 20) {
			results.push(new MsgWrapper(curMsg));
			mergedMsg = [];
		}
	}
	return results.sort((a, b) => a.sentTime - b.sentTime);
};
