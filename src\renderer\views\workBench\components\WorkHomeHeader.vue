<template>
  <div class="work-home-header-box">
    <div class="head-title" :class="{'flexBetween': activationGroupItem?.teamAuth !== 1}">
      <span class="head-title-name line-1">
        <MyTooltipComp :text="activationGroupItem?.teamFullName"></MyTooltipComp>
      </span>
      <span v-if="activationGroupItem?.teamAuth === 1" class="certified head-title-tag">
        <iconpark-icon name="iconsecure" style="font-size: 16px;"></iconpark-icon> 已认证
      </span>
      <span v-else class="head-title-untag cursor" @click="goCertifiedPage">
         去认证
        <iconpark-icon name="iconarrowright"  class="arrowRight"></iconpark-icon>
      </span>
    </div>
    <div class="head-info-box">
      <div class="avatar-box">
        <avatar avatarSize="72px" :image-url="myCardInfo?.avatar" :user-name="myCardInfo?.name" :round-radius="true" />
        <!-- <img class="user-logo" :src="activationGroupItem.teamLogo ? activationGroupItem.teamLogo : organization_icon" /> -->

        <div class="info-boxs-text">
          <div class="info-boxs-text-name">{{ checkIsAdminData?.name }}</div>
          <!-- jobNameFn(checkIsAdminData)&& -->
          <div v-if="checkIsAdminData?.position[0]?.departmentName" class="info-boxs-text-job">{{ checkIsAdminData?.position[0]?.departmentName }}
          </div>
          <!-- jobNameFn(checkIsAdminData)&& -->
          <div v-if="checkIsAdminData?.position[0]?.jobName" class="info-boxs-text-job">{{ checkIsAdminData?.position[0]?.jobName }}</div>
          <!-- <div v-if="isAdminData?.isAdmin === 1" class="info-boxs-text-job">{{ t("banch.admin1") }}</div> -->
          <!-- <div v-if="isAdminData?.isManagement === 1" class="info-boxs-text-job">{{ t("banch.admin") }}</div> -->
        </div>
      </div>
      <div class="group-admin-btn-box" style="margin-bottom: 10px;" v-if="showOrgSetting||allowInvite||(checkIsAdminData?.isAdmin || checkIsAdminData?.super || checkIsAdminData?.superAdmin)">
        <div class="right-head-box">
          <div v-if="showOrgSetting" class="right-head-item" @click="teamStingFn">
            <img src="@/assets/bench/zzsz.svg" />
            <div class="right-head-item-text">{{ t("banch.zzsz") }}</div>
          </div>
          <div v-if="allowInvite" class="right-head-item" @click="showInvite">
            <img src="@/assets/bench/yqcy.svg" />
            <div class="right-head-item-text">{{ t("banch.yqcy") }}</div>
          </div>
          <div v-if="checkIsAdminData?.isAdmin || checkIsAdminData?.super || checkIsAdminData?.superAdmin"
            class="right-head-item" @click="goEnterprise">
            <img src="@/assets/bench/icon2.svg" />
            <div class="right-head-item-text">{{ t("banch.glht") }}</div>
          </div>
        </div>
      </div>
      <div v-else>
        <img v-if="langs === 'zh-cn'" src="@/assets/workbench/PlatformDetails.svg" class="bench-img"
        @click="goPath()" />
        <img v-else src="@/assets/workbench/ft.svg" class="bench-img" @click="goPath()" />
      </div>
    </div>
    <div class="vip-box" v-if="!isMas">
      <!-- 非商店 -->
      <span class="vip-name">{{ packageName() }}</span>
      <div class="by" @click="by(teamAnnualFeeData)">
        {{ gmtext() }}
      </div>
    </div>
    <div class="vip-box" v-else>
      <!-- 商店 -->
      <span class="vip-name">{{ packageName() }}</span>
      <div v-if="
      isMAcFee()&&
     ( !teamAnnualFeeData?.opened ||
     teamAnnualFeeData?.annualFeeDetail?.trial ||
     checkExpiration(teamAnnualFeeData?.annualFeeExpiredAt))
    " class="by" @click="by(teamAnnualFeeData)">
        {{
          isMAcFee()
        }}
      </div>
    </div>
    <invite-dialog ref="invite" :setting="teamSettingDetail" :team="checkIsAdminData" :my-card-info="myCardInfo"
      :position="myCardInfo.position" :invite="inviteType" />
    <!-- 套餐升级upgrade -->
    <!-- 套餐购买open -->
    <AnnualFeeDialog v-if="openVisible" v-model="openVisible" :open="payFlag?.includes(['open'])"
      :upgrade="payFlag?.includes(['upgrade'])" :team-id="activationGroupItem.teamId" @success="buySuccess" />
  </div>
</template>

<script setup lang="ts">
  import inviteDialog from "@renderer/components/contacts/dialog/invite.vue";
  import avatar from "@renderer/components/kyy-avatar/index.vue";
  import { getInnerCardInfo } from "@renderer/api/contacts/api/common";
  import {
    getDepartmentList,
    applyReviewList,
    staffsReview,
    teamSetting,
    authorityCheck,
  } from "@renderer/api/contacts/api/organize";
  import { ref, onMounted, computed, watch, nextTick, onUnmounted, getCurrentInstance } from "vue";
  import { useI18n } from "vue-i18n";
  import { useContactsStore } from "@renderer/store/modules/contacts";
  import { useRouter, useRoute } from "vue-router";
  import { checkIsAdmin, getRedPoint, listSwitch, getHonorMoudleList } from "@renderer/api/workBench/index";
  import { MessagePlugin } from "tdesign-vue-next";
  import { getTeamAnnualFee } from "@renderer/api/business/manage";
  import AnnualFeeDialog from "@/views/square/components/annual-fee/AnnualFeeDialog.vue";
  const payFlag = ref("");
  import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
  import { annualFeePackage } from "@/api/customerService";
  import { getLang } from "@/utils/auth";
  import organization_icon from "@/assets/contacts/organization_icon.svg";
  import { getFrontGrowthList } from "@/api/workBench/growth";
  import { getFrontIntroduceList } from "@/api/workBench/introduce";
  import { authTeamsCheck } from "@renderer/api/workBench/index.ts";
  import to from "await-to-js";
  import LynkerSDK from '@renderer/_jssdk';

  const { ipcRenderer, shell } = LynkerSDK;
  const { proxy } = getCurrentInstance();
  const tabStore = useTabStore();
  const isMas = ref(__APP_ENV__.VITE_APP_MAS);
  // const isMas = ref(true);

  const packageType = ref(false);
  const props = defineProps({
    activationGroupItem: {
      type: Object,
    },
    tabList: {
      type: Object,
    },
  });
  const route = useRoute();
  const router = useRouter();
  const langs = computed(() => proxy.$i18n.locale);
  const isMAcFee=()=>{
    if (!teamAnnualFeeData.value?.opened || teamAnnualFeeData.value?.annualFeeDetail?.trial) {
      return t("order.by")
    }else if (!checkExpiration(teamAnnualFeeData.value?.annualFeeExpiredAt)) {
      return false
    } else {
      return false
    }
  }
  const contactsStore = useContactsStore();
  // const visible = ref(false);
  const teamSettingDetail = ref();
  const invite = ref(null);
  const myCardInfo = ref({
    avatar: "",
    openId: "",
    name: "",
    position: [
      {
        partmentId: "",
      },
    ],
  });
  const { t } = useI18n();
  const inviteType = ref(["teamQrcode", "teamLink", "teamCode", "phone"]);
  const allowInvite = ref(false);
  const emits = defineEmits(["parentSetWorkBenchTabItem",'updisPromotionalpage']);
  const openVisible = ref(false);
  const showOrgSetting = ref(false);
  const by = (teamAnnualFeeData) => {
    if (!haveApp.value) {
      MessagePlugin.error(t("banch.noadmin"));
      return;
    }
    if (teamAnnualFeeData?.annualFeeDetail?.trial) {
      payFlag.value = "open";
      openVisible.value = true;
      return;
    }
    if (!teamAnnualFeeData?.opened) {
      payFlag.value = "open";
    } else if (!checkExpiration(teamAnnualFeeData?.annualFeeExpiredAt)) {
      payFlag.value = "upgrade";
    } else {
      payFlag.value = "";
    }
    openVisible.value = true;
  };
  const buySuccess = () => {
    getData();
  };
  const goAbout = () => {
    router.push({
      path: "/workBenchIndex/aboutour",
      query: {
        platform: "workBench",
        fromType: 2,
      },
    });

    emits("parentSetWorkBenchTabItem", {
      activeIcon: "workshop",
      icon: "workshop",
      path: "/workBenchIndex/aboutour",
      path_uuid: "workBench",
      name: "bench_aboutour",
      title: t("member.squarek.l"),
      type: 17,
    });
  };
  const checkExpiration = (expiredAt) => {
    const now = new Date();
    const expirationDate = new Date(expiredAt);
    return now > expirationDate;
  };
  const gmtext = () => {
    if (!teamAnnualFeeData.value) {
      return;
    }
    if (!teamAnnualFeeData?.value.opened || teamAnnualFeeData?.value?.annualFeeDetail?.trial) {
      return t("order.by");
    }
    if (!checkExpiration(teamAnnualFeeData.value?.annualFeeExpiredAt)) {
      return t("square.annualFee.upgrade");
    } else {
      return t("square.action.renew");
    }
  };
  function formatTimeDifference(isoString) {
    const targetDate = new Date(isoString);
    const currentDate = new Date();
    const diffMs = Math.abs(targetDate - currentDate);
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays > 0) {
      return `${diffDays}天`;
    } else if (diffHours > 0) {
      return `${diffHours}小时`;
    } else if (diffMins > 0) {
      return `${diffMins < 1 ? 1 : diffMins}分钟`;
    } else {
      return `1分钟`;
    }
  }
  const packageName = () => {
    if (!teamAnnualFeeData.value) {
      return;
    }
    console.log(teamAnnualFeeData.value, "teamAnnualFeeDatateamAnnualFeeData");
    if (teamAnnualFeeData.value?.annualFeeDetail?.trial) {
      if (!checkExpiration(teamAnnualFeeData?.value.annualFeeExpiredAt)) {
        return `${t("banch.tysy")}${formatTimeDifference(teamAnnualFeeData?.value.annualFeeExpiredAt)}`;
      } else {
        return t("banch.tyjs");
      }
    }
    if (!teamAnnualFeeData.value?.opened) {
      return t("banch.mfb");
    } else if (teamAnnualFeeData.value?.annualFeeDetail) {
      return teamAnnualFeeData.value?.annualFeeDetail?.package?.name;
    } else {
      return t("square.annualFee.basicVersion");
    }
  };

  const goCertifiedPage = () => {

    router.push("/workBenchIndex/teamSting");
    emits("parentSetWorkBenchTabItem", {
      activeIcon: "workshop",
      icon: "workshop",
      path: "/workBenchIndex/teamSting",
      path_uuid: "workBench",
      name: "bench_teamSting",
      title: t("banch.zzsz"),
      type: 17,
    });
    tabStore.setCurrentTab(1);
    tabStore.setOpenCertifyModal(true);
  }

  const teamStingFn = () => {
    router.push("/workBenchIndex/teamSting");
    emits("parentSetWorkBenchTabItem", {
      activeIcon: "workshop",
      icon: "workshop",
      path: "/workBenchIndex/teamSting",
      path_uuid: "workBench",
      name: "bench_teamSting",
      title: t("banch.zzsz"),
      type: 17,
    });
  };
  const jobNameFn = (val) => {
    if (!val || !val.position) {
      return false;
    }
    const jobNames = val.position.filter((item) => item.jobName !== "").map((item) => item.jobName);
    return jobNames.length > 0 ? jobNames.join(",") : false;
  };
  const goPath = () => {
    router.push("/workBenchIndex/promotionalpage");
    emits("parentSetWorkBenchTabItem", {
      activeIcon: "workshop",
      icon: "workshop",
      path: "/workBenchIndex/promotionalpage",
      path_uuid: "workBench",
      name: "bench_promotionalpage",
      // title: t("banch.glht"),
      title: "另可数智工场",
      type: 17,
    });
    // console.log("啊啊啊啊啊啊啊啊啊啊啊");
  };
  const showInvite = () => {
    invite.value.show();
  };

  const goEnterprise = () => {
    router.push("/workBenchIndex/workBenchEnterprise");
    emits("parentSetWorkBenchTabItem", {
      activeIcon: "workshop",
      icon: "workshop",
      path: "/workBenchIndex/workBenchEnterprise",
      path_uuid: "workBench",
      name: "workBenchEnterprise",
      title: t("banch.glht"),
      type: 17,
    });
    console.log("添加管理后台");
  };
  const checkIsAdminData = ref(null);
  const showTeamInfo = ref(true);
  const showAbout = ref(false)
  const haveApp = ref(false);
  const teamAnnualFeeData = ref(null);
  const isAdminData = ref(null);
  const getData = async () => {
    const admin = await getInnerCardInfo(props.activationGroupItem.staffId, props.activationGroupItem.teamId);
    const res = await checkIsAdmin({ ...props.activationGroupItem.user_ids }, props.activationGroupItem.teamId);
    isAdminData.value = admin.data.data;
    checkIsAdminData.value = { ...admin.data.data, ...res.data.data, team: props.activationGroupItem.teamFullName };
    authorityCheck(
      { items: ["organization_setting_info", "application_setting"] },
      props.activationGroupItem.teamId,
    ).then(({ data }) => {
      if (data.code === 0) {
        showTeamInfo.value = Boolean(data.data["organization_setting_info"]);
        haveApp.value = Boolean(data.data["application_setting"]);
      }
    });
    listSwitch(props.activationGroupItem.teamId, { channel_type: "digital_factory" }).then((res) => {
      if (res.data.data.honor_list_data || res.data.data.introduce_list_data || res.data.data.progress_list_data) {
        showAbout.value = true;
      }
      if (!res.data.data.honor_list_data && !res.data.data.introduce_list_data && !res.data.data.progress_list_data) {
        showAbout.value = false;
      }
    });
    authorityCheck(
      { items: ["organization_setting_info", "application_setting"] },
      props.activationGroupItem.teamId,
    ).then(({ data }) => {
      if (data.code === 0) {
        showTeamInfo.value = Boolean(data.data["organization_setting_info"]);
        haveApp.value = Boolean(data.data["application_setting"]);
      }
    });
    const { data } = await teamSetting({ type: 1 }, props.activationGroupItem.teamId);
    if (data.code === 0) {
      allowInvite.value = data.data.enable && (checkIsAdminData.value.isAdmin || data.data.notManagerInviteEnable);
      if (data.data.notManagerInviteEnable) {
        inviteType.value = data.data.notManagerInvite
          ? ["teamQrcode", "teamLink", "teamCode", "phone"].filter((v) => data.data.notManagerInvite.includes(v))
          : [];
      }
      if (checkIsAdminData.value.isAdmin) {
        inviteType.value = ["teamQrcode", "teamLink", "teamCode", "phone"];
      }
      teamSettingDetail.value = data.data;
    }
    const cardInfo = await getInnerCardInfo(+checkIsAdminData.value.cardId.slice(1));
    if (cardInfo.data.code === 0) {
      console.log(cardInfo, "cardInfocardInfocardInfocardInfo");
      myCardInfo.value = cardInfo.data.data;
    }
    initData();
    const teamAnnualFee = await getTeamAnnualFee();
    // teamAnnualFeeData.value = teamAnnualFee.data;

    // //套餐1.2
    teamAnnualFeeData.value = teamAnnualFee.data;
    // teamAnnualFee.data.annualFeeDetail.trial=true
    // teamAnnualFee.data.annualFeeExpiredAt="2023-05-24T03:59:17Z"
  };

  watch(
    () => props.activationGroupItem.teamId,
    (newValue) => {
      getData();
    },
  );
  const initData = async () => {
    // 获取组织设置
    const { teamId, fromType } = route.query;
    console.log("🚀 ~ getData ~ route.query:", route.query);
    const isSquare = Number(fromType) === 1;

    const [err, res] = await to(
      authTeamsCheck(
        {
          items: [
            "organization_introduce",
            "organization_setting_info",
            "organization_honor",
            "organization_progress",
            "organization_manage",
          ],
        },
        isSquare ? teamId : localStorage.getItem("honorteamid"),
      ),
    );
    if (err) {
      const errData = err?.response?.data;
      MessagePlugin.warning(errData?.message);
      return;
    }
    const limitData = res.data.data;
    let visible = false;
    for (const key in limitData) {
      if (limitData[key] === 1) {
        visible = true;
        break;
      }
    }
    showOrgSetting.value = visible;
    if (showOrgSetting.value===false&&allowInvite.value===false&&!checkIsAdminData?.isAdmin&&!checkIsAdminData?.super&&!checkIsAdminData?.superAdmin) {
      updisPromotionalpage(true)
    }


  };

  //是否显示入口
  // const enterProgress=ref(false)//组织历程
  // const enterIntroduce=ref(false)//组织介绍
  // const enterHonor=ref(false)//荣誉榜
  // const enter=async()=>{
  //   getHonorMoudleList({"channel_type":"digital_factory","team_id":localStorage.getItem('honorteamid')},localStorage.getItem('honorteamid')).then((res)=>{
  //     enterHonor.value=res.data.data.list!=0?true:false
  //     console.log(enterHonor.value,res.data.data.list,'获取列表数据--------------6666666666666-------------')
  //   }).catch((err)=>{
  //     MessagePlugin.error(err.message)
  //   })
  //   let resGrowth = await getFrontGrowthList({"channel_type":"2", team_id: localStorage.getItem('honorteamid')})
  //   if (resGrowth.data.code === 0) {
  //     enterProgress.value=resGrowth.data.data.list!=0?true:false
  //   }
  //   let resIntroduce = await getFrontIntroduceList({"channel_type":"2", team_id: localStorage.getItem('honorteamid') })
  //   if (resIntroduce.data.code === 0) {
  //     enterIntroduce.value=resIntroduce.data.data.list!=0?true:false
  //   }
  // }

  onMountedOrActivated(() => {
    console.log("props.activationGroupItem.teamId", props.activationGroupItem.teamId);
    if (props.activationGroupItem.teamId) {
      getData();
      // redPoint();
      // enter()
    }
  });
</script>

<style lang="less" scoped>

  .certified {
    color: var(--kyy_color_tag_text_success, #499D60);
    font-size: 12px;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_success, #E0F2E5);
    padding: 2px 4px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }

  .info-box {
    width: 280px;
    height: 308px;
    border-radius: 8px;
    /* background: #EFF3FF; */
    margin-left: 12px;
    background: url('@/assets/img/workbg.png') no-repeat;

  }

  .avatar-box {
    display: flex;
    align-items: center;
  }

  .info-boxs-text {
    margin-left: 16px;
  }

  .info-boxs-text-name {
    color:  #1A2139;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    max-width: 175px;
    /* 150% */
  }

  .info-boxs-text-job {
    max-width: 175px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    font-size: 14px;
    color: #516082;
  }

  .info-boxs-year {
    width: 280px;
    height: 44px;
    border-radius: 8px;
  }

  .head-info-box {
    padding: 16px 12px;
    border-radius: 8px 8px 0px 0px;
    height: 218px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .head-title {
    color: #1A2139;
    font-size: 14px;
    font-style: normal;
    padding: 12px;
    font-weight: 600;
    line-height: 22px;
    display: flex;
    width: 280px;
    height: 46px;
    // flex-direction: column;
    // justify-content: space-between;
    align-items: flex-start;
    flex-shrink: 0;
    gap: 4px;
    &-name {
      // flex: 1;
      // width: 100%;
    }
    &-tag {
      flex:none;
    }
    &-untag {
      flex:none;
      color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      display: flex;
 
      .arrowRight {
        font-size: 22px;
      }
      &:hover {
        color: var(--color-button_text_brand-kyy_color_button_text_brand_font_hover, #707EFF);
        .arrowRight {
          color: var(--color-button_text_brand-kyy_color_button_text_brand_font_hover, #707EFF);
        }
      }
    }
  }

  .flexBetween {
    justify-content: space-between;
    align-items: center;
  }

  .img-boxs {
    display: flex;
    width: 224px;
    flex-wrap: wrap;
    gap: 12px;
    margin-left: 12px;

    img {
      width: 224px;
      height: 148px;
    }
  }

  .cultureWall {
    width: 616px;
    padding: 0 !important;
  }

  .content-box {
    width: 1168px;
  }

  .header-boxs {
    height: 332px;
    width: 100%;
    border-radius: 16px;
    background: #FFF;
    display: flex;
    align-items: center;
    padding: 12px;
  }

  .tab-boxs {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #1A2139;
    font-size: 16px;
    font-weight: 600;
    width: 100%;
    padding: 16px 0 16px 16px;
  }

  .tab-item {
    padding: 4px 12px;
    cursor: pointer;
  }

  .tab-red {
    position: relative;
  }

  .tab-red::after {
    position: absolute;
    top: 0;
    width: 8px;
    height: 8px;
    left: 0;
    border-radius: 50%;
    border: 1px solid #FFF;
    background: #FF4AA1;
  }

  .tab-active {
    border-radius: 16px;
    background: #1D63E9;
    color: #fff;
  }


  .about-box {
    display: flex;
    height: 24px;
    min-width: 72px;
    padding: 0px 12px 0px 8px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy_radius_button_full, 999px);
    border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_brand, #4d5eff);
    margin-right: 8px;
    color: var(--color-button_border-kyy_color_buttonBorder_text_brand, #4d5eff);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    cursor: pointer;
    line-height: 22px;
    /* 157.143% */
    position: relative;

    .redpoint {
      position: absolute;
      top: -7px;
      right: -4px;
    }
  }

  .about-vip {
    display: flex;
    align-items: center;
    margin-top: 8px;
  }

  .experience {
    color: #944711 !important;
    background: linear-gradient(125deg, #fff6e5 14.27%, #fdd2c1 98.59%) !important;

    .by {
      color: #fffaf7 !important;
    }
  }

  .avatar-box {
    display: flex;
    align-items: center;
  }

  .info-boxs-text {
    margin-left: 16px;
  }
  .info-boxs-year {
    width: 280px;
    height: 44px;
    border-radius: 8px;
  }

  .head-info-box {
    padding: 16px 12px;
    border-radius: 8px 8px 0px 0px;
    height: 218px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  // .head-title {
  //   color: #1A2139;
  //   font-size: 14px;
  //   font-style: normal;
  //   padding: 12px;
  //   font-weight: 600;
  //   line-height: 22px;
  //   display: flex;
  //   width: 280px;
  //   height: 46px;
  //   flex-direction: column;
  //   justify-content: space-between;
  //   align-items: flex-start;
  //   flex-shrink: 0;
  // }

  .img-boxs {
    display: flex;
    width: 224px;
    flex-wrap: wrap;
    gap: 12px;
    margin-left: 12px;

    img {
      width: 224px;
      height: 148px;
    }
  }

  .cultureWall {
    width: 616px;
    padding: 0 !important;
  }

  .content-box {
    width: 1168px;
  }

  .header-boxs {
    height: 332px;
    width: 100%;
    border-radius: 16px;
    background: #FFF;
    display: flex;
    align-items: center;
    padding: 12px;
  }

  .tab-boxs {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #1A2139;
    font-size: 16px;
    font-weight: 600;
    width: 100%;
    padding: 16px 0 16px 16px;
  }

  .tab-item {
    padding: 4px 12px;
    cursor: pointer;
  }

  .tab-red {
    position: relative;
  }

  .tab-red::after {
    position: absolute;
    top: 0;
    width: 8px;
    height: 8px;
    left: 0;
    border-radius: 50%;
    border: 1px solid #FFF;
    background: #FF4AA1;
  }

  .tab-active {
    border-radius: 16px;
    background: #1D63E9;
    color: #fff;
  }


  .head-info-box {
    padding: 16px 12px;
    border-radius: 8px 8px 0px 0px;
    height: 218px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  // .head-title {
  //   color: #1A2139;
  //   font-size: 14px;
  //   font-style: normal;
  //   padding: 12px;
  //   font-weight: 600;
  //   line-height: 22px;
  //   display: flex;
  //   width: 280px;
  //   height: 46px;
  //   flex-direction: column;
  //   justify-content: space-between;
  //   align-items: flex-start;
  //   flex-shrink: 0;
  // }

  .img-boxs {
    display: flex;
    width: 224px;
    flex-wrap: wrap;
    gap: 12px;
    margin-left: 12px;

    img {
      width: 224px;
      height: 148px;
    }
  }

  .vip-box {
    width: 280px;
    height: 44px;
    padding: 4px 8px;
    background: url('@/assets/workbench/leav.svg');
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-repeat: no-repeat;
    border-radius: 8px;

    .vip-name {
      margin-left: 32px;
      color: #6A4A42;

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .right-img {
      position: absolute;
      top: 1px;
      right: -3px;
      margin-left: 8px;
      width: 50px;
      height: 22px;
    }

    .by {
      height: 24px;
      cursor: pointer;
      line-height: 24px;
      text-align: center;
      padding: 0 12px;
      border-radius: 999px;
      border: 1px solid #FFF;
      background: linear-gradient(100deg, #FFE7D0 2.09%, #FFD4B6 94.65%);
    }
  }

  .right-head-box {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-around;
  }

  .bench-img {
    width: 256px;
    height: 90px;
  }

  .mgl0 {
    margin-left: 0 !important;
    border-right: none !important;
  }

  .work-home-header-box {
    // background-color: #f5f8fe;
    // padding: 16px;
    padding-bottom: 16px;

    .left-box {
      border-radius: 8px;
      border: 1px solid var(--border-kyy_color_border_white, #fff);

      background-image: url("@/assets/bench/homehead.svg");
      background-repeat: no-repeat;
      background-size: 100%;
      height: 136px;
      padding: 24px;
      display: flex;
      align-items: center;
    }

    .user-logo {
      width: 72px;
      height: 72px;
      border-radius: 50%;
    }

    .user-info {
      .group-name {
        color: var(--text-kyy_color_text_3, #828da5);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        margin-bottom: 8px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        max-width: 430px;
      }

      .user-name {
        color: #1a2139;
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px;
        /* 144.444% */
        margin-right: 8px;
        max-width: 9rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
      }

      .user-post {
        color: #516082;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        margin-right: 8px;
      }

      .user-admin {
        display: flex;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_brand, #eaecff);
        color: var(--kyy_color_tag_text_brand, #4d5eff);
        text-align: center;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 166.667% */
      }
    }
  }

  .flex-a {
    display: flex;
    align-items: center;
  }

  .borderleftnone {
    border-right: none !important;
  }

  .left-box-item {
    width: 100%;
    border-right: 1px solid #fff;
    margin-right: 24px;
  }

  .right-head-item:hover {
    /* background-color: #f5f8fe; */
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_default, #FFF);

    /* kyy_shadow_s */
    box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
  }

  .vip-mac {
    padding-right: 0 !important;
  }

  .right-head-item {
    cursor: pointer;
    padding: 8px;
    width: 72px;
    height: 74px;
    display: flex;
    border-radius: 8px;
    flex-direction: column;
    text-align: center;
    justify-content: space-between;
    align-items: center;

    img {
      width: 32px;
      height: 32px;
      margin-bottom: 4px;
    }

    .right-head-item-text {
      color: var(--text-kyy_color_text_1, #1a2139);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .right-head {
    justify-content: space-between;
    /* width: 50%; */
  }

  .by-info {
    text-align: center;
    margin-top: -16px;
    margin-bottom: -16px;

    img {
      width: 48px;
      height: 48px;
    }

    .by-info-text {
      color: var(--text-kyy_color_text_1, #1a2139);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
      margin-top: 12px;
      margin-bottom: 8px;
    }

    .by-info-btn {
      display: flex;
      gap: 8px;
      justify-content: center;

      .minw83 {
        width: 83px;
      }
    }

    .by-info-text-2 {
      color: #828da5;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      margin-bottom: 24px;
    }
  }
</style>
