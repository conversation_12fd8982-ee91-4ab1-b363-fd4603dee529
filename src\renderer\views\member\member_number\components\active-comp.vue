<template>
  <div class="page">
    <div v-if="route.path.includes('/digitalPlatformIndex')" class="change-box">
      <img class="logo" src="/assets/member/acvive.png" alt="" />
    </div>

    <div
      class="square"
      :class="{ '!h-398': memberList.activities.length < 1 }"
      :style="`height: ${route.path.includes('/digitalPlatformIndex') ? 'calc(100% - 48px)' : '100%'}`"
    >
      <div class="between">
        <div v-if="route.path.includes('/digitalPlatformIndex')" class="app-name">活动</div>
        <div class="search">
          <t-input
            v-model="keyword"
            :placeholder="$t('activity.activity.keywords_placeholder')"
            :maxlength="50"
            clearable
            style="width: 304px"
            @change="onSearch"
          >
            <template #prefix-icon>
              <iconpark-icon name="iconsearch-a961a3le" class="iconsearch"></iconpark-icon>
            </template>
          </t-input>

          <div
            class="iconscreen-div"
            :class="[paramsSuper ? 'iconscreen-div-active' : '']"
            @click="filterVisible = true"
          >
            <icon class="menu-icons" name="iconscreen" :url="iconUrl" size="20" />
          </div>
        </div>
      </div>

      <div v-if="paramsSuper" class="filter-res">
        <div class="tit">{{ t('approval.approval_data.sures') }}</div>
        <div v-if="params['duration.startTime'] || params['duration.endTime']" class="ov-time te">
          <span
            >{{ t('activity.activity.durationObj_label') }}：
            {{ moment.unix(params['duration.startTime']).format('YYYY-MM-DD HH:mm') }} ~
            {{ moment.unix(params['duration.endTime']).format('YYYY-MM-DD HH:mm') }}</span
          >
          <span class="close2" @click="clearFilterDuration">
            <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
          </span>
        </div>
        <div v-if="params.categories.length" class="stat te">
          <span
            >{{ t('activity.activity.categories') }}：{{
              activityTypeOption.find((option) => option.value === params.categories[0])?.label
            }}</span
          >
          <span class="close2" @click="clearFilterCategories">
            <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
          </span>
        </div>
        <div v-if="params.status" class="kword te">
          <span class="line-1" style="display: inline-block; max-width: 100%"
            >{{ t('activity.activity.status') }}: {{ formatStatusText(params.status) }}</span
          >
          <span class="close2" @click="clearFilterStatus">
            <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
          </span>
        </div>
        <div class="icon clearFiltersBox" @click="clearFilters">
          <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
          <span class="clearFilters">{{ t('approval.approval_data.clearFilters') }}</span>
        </div>
      </div>

      <div class="conbox mt-24px" :class="conhei">
        <div
          v-for="(menu, menuIndex) in memberList.activities"
          :key="menu.id"
          class="item cursor"
          @click="onGoSquarePage(menu)"
        >
          <div class="public">
            <div class="av-box">
              <div class="static-tag" v-if="menu?.stickedAt !== '0'">
                <div class="static-tag-text">置顶</div>
              </div>

              <span class="logo" :class="{'with-static-tag': menu?.stickedAt !== '0'}">
                <img v-lazy="menu?.assetUrl" class="img" />
              </span>
              <div class="av-name">
                <span class="av-name-span">{{ menu?.subject }}</span>
              </div>
            </div>

            <div class="right">
              <div class="lin" style="padding-bottom: 4px">
                <iconpark-icon name="icontag" size="20" style="padding-right: 8px; color: #828da5" />
                <span class="type person line-2" style="margin-right: 8px">
                  {{ activityCategoryMap[menu.categoryId] }}</span
                >
                <!--进行中-->
                <span v-if="menu?.status || menu?.timeStatus == 'TimeUnderway'" class="status-tag-2" style="margin-right: 8px">{{
                  t('activity.activity.ongoing')
                }}</span>
                <!--未开始-->
                <span v-if="menu?.status || menu?.timeStatus == 'TimeFuture'" class="status-tag-1" style="margin-right: 8px">{{
                  t('activity.activity.noStart')
                }}</span>
                <span v-if="menu?.status || menu?.timeStatus == 'TimeOver'" class="status-tag-2" style="margin-right: 8px">{{
                  t('activity.activity.ended')
                }}</span>
                <span v-if="menu?.status || menu?.timeStatus == 'TimeCancelled'" class="status-tag-3" style="margin-right: 8px">{{
                  t('activity.activity.canceled')
                }}</span>

                <t-tooltip
                  v-if="menu.chat && menu.creator === getOpenid()"
                  :content="`${t('activity.activity.from')}${
                    menu.chat.type === 'GroupChat'
                      ? t('activity.activity.groupChat')
                      : t('activity.activity.singleChat')
                  }：${menu.chat.type === 'GroupChat' ? menu.chat.group?.name : menu.chat.name}`"
                >
                  <t-tag
                    class="mr-8"
                    style="background-color: #faedfd; color: #ca48eb; cursor: pointer"
                    @click="(context) => openIM(context, menu)"
                    >{{ t('activity.activity.scene') }}</t-tag
                  >
                </t-tooltip>
                <t-tag v-if="menu.actorScope === 'Publish'" style="background-color: #e8f0fb; color: #4093e0">公开报名</t-tag>
              </div>
              <div class="lin" style="padding-bottom: 4px">
                <iconpark-icon name="icondate" size="22" style="padding-right: 8px; color: #828da5" />
                <span class="line-2" style="font-size: 14px; color: #828da5">
                  {{ moment.unix(menu?.duration?.startTime || menu.durationStart).format('MM-DD HH:mm') }} ~
                  {{ moment.unix(menu?.duration?.endTime || menu.durationEnd).format('MM-DD HH:mm') }}</span
                >
              </div>

              <div class="lin" style="padding-bottom: 4px">
                <iconpark-icon name="iconpositioning" size="22" style="padding-right: 8px; color: #828da5" />
                <span class="line-1" style="font-size: 14px; color: #828da5">{{ menu?.location ?? menu?.address }}</span>
              </div>
            </div>
          </div>
        </div>

        <div v-if="!memberList?.activities?.length" style="display: flex; justify-content: center; width: 100%">
          <div v-show="!memberList?.activities?.length" class="noEmpty">
            <Empty :tip="keyword ? '未找到匹配活动' : '暂无活动'" :name="keyword ? 'no-result' : 'no-data-activity'" />
          </div>
        </div>
      </div>

      <div>
        <t-pagination
          v-if="memberList?.summary?.totalOfItems > 12"
          v-model="params['page.number']"
          v-model:pageSize="params['page.size']"
          style="margin-top: 16px"
          :total="memberList?.summary?.totalOfItems"
          show-jumper
          :page-size-options="pageSizeOptions"
          @change="onChange"
          @page-size-change="onPageSizeChange"
          @current-change="onCurrentChange"
        >
          <template #totalContent>
            <!--          <div style="margin-right: 24px;"> {{ t("approval.handover.count_tip", { count:memberList?.summary?.totalOfItems }) }}</div>-->
            <div style="margin-right: 24px">
              {{ t('member.manager.count_tip', { count: memberList?.summary?.totalOfItems }) }}
            </div>
          </template>
        </t-pagination>
      </div>

      <t-drawer
        v-model:visible="filterVisible"
        class="activity_screen_drawer"
        :close-btn="true"
        size="472px"
        :header="t('approval.approval_data.sur')"
      >
        <div class="form-boxxx">
          <div class="fitem">
            <div class="title">{{ t('activity.activity.categories') }}</div>
            <div class="ctl">
              <t-select v-model="drawerForm.categories" clearable :placeholder="t('approval.operation.select')">
                <t-option
                  v-for="item in activityTypeOption"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                ></t-option>
                <template #suffixIcon>
                  <img src="@/assets/svg/icon_arrow_down.svg" />
                </template>
              </t-select>
            </div>
          </div>
          <div class="fitem">
            <div class="title">{{ t('activity.activity.status') }}</div>
            <div class="ctl">
              <t-select v-model="drawerForm.status" clearable :placeholder="t('approval.operation.select')">
                <t-option
                  v-for="item in activityStatusOption"
                  :key="item.id"
                  :value="item.id"
                  :label="item.label"
                ></t-option>
                <template #suffixIcon>
                  <img src="@/assets/svg/icon_arrow_down.svg" />
                </template>
              </t-select>
            </div>
          </div>

          <div class="fitem">
            <div class="title">{{ t('activity.activity.durationObj_label') }}</div>
            <div class="ctl">
              <t-date-range-picker
                v-model="drawerForm.duration"
                style="width: 100%"
                :placeholder="[t('approval.approval_data.start_time'), t('approval.approval_data.end_time')]"
                clearable
              />
            </div>
          </div>
        </div>
        <template #footer>
          <div class="foot">
            <div class="btn1" @click="footerCla">
              {{ t('member.regular.reset') }}
            </div>
            <div class="btn3" @click="getDataRunDr">
              {{ t('approval.approval_data.cm') }}
            </div>
            <!-- <div v-else class="btn2" @click="getDataRunDr">
              {{ t("approval.approval_data.cm") }}
            </div> -->
          </div>
        </template>
      </t-drawer>
    </div>

    <Tricks :offset="{ x: '-32', y: '-40' }" :uuid="`${route.path.includes('/workBenchIndex') ? '数智工场' : '数字商协'}-活动列表`" />
  </div>
</template>

<script setup lang="ts">
import { computed, onActivated, reactive, ref, toRaw, watch } from 'vue';
import { eventsTeamId, getMemberSquaresAxios } from '@renderer/api/member/api/businessApi';
import { getResponseResult } from '@renderer/utils/myUtils';
import Empty from '@renderer/components/common/Empty.vue';
// import { useDigitalPlatformStore } from "@renderer/views/association/store/association";
import { useI18n } from 'vue-i18n';
import { iconUrl } from '@renderer/plugins/KyyComponents';
import { Icon } from 'tdesign-icons-vue-next';
import moment from 'moment';
import { getActivityListByTeam, getCategories } from '@/api/activity';
// import { getAssociationTeamID } from "@renderer/views/association/utils/auth";
import useNavigate from '@renderer/views/member/hooks/navigate';
import { useRoute, useRouter } from 'vue-router';
import { platform } from '@renderer/views/digital-platform/utils/constant';
import { useDigitalPlatformStore } from '@renderer/views/digital-platform/store/digital-platform-store';
import { getStaffsPlatform } from '@renderer/api/association/api/businessApi';
import to from 'await-to-js';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { MessagePlugin } from 'tdesign-vue-next';
import useRouterHelper from '@renderer/views/square/hooks/routerHelper';
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const router = useRouter();
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();
const digitalRouter = useRouterHelper('digitalPlatformIndex');

const { t } = useI18n();
const keyword = ref('');
// const store = useDigitalPlatformStore();
const { goActivePage } = useNavigate();
const isLoading = ref(false);
const memberList = ref({});
const page = ref(null);

const selelctApproveData = ref(false);
const filterVisible = ref(false);
const drawerForm = ref({
  categories: '',
  duration: [],
  status: 0,
});

const params = reactive({
  keywords: '',
  status: '',
  location: '',
  categories: [],
  openid: '',
  teamId: '',
  'duration.startTime': '',
  'duration.endTime': '',
  'page.number': 1,
  'page.size': 12,
  kind: 'Unspecified',
});

const pageSizeOptions = [
  { label: `12${t('approve.num_page_numbers')}`, value: 12 },
  { label: `24${t('approve.num_page_numbers')}`, value: 24 },
  { label: `48${t('approve.num_page_numbers')}`, value: 48 },
  { label: `96${t('approve.num_page_numbers')}`, value: 96 },
];

const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
});
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform;
});

const currentTeamId = computed(() => {
  // if (platformCpt.value === platform.digitalPlatform) {
  //   return digitalPlatformStore.activeAccount?.teamId;
  // } else {
  //   return getAssociationTeamID();
  // 朱红的冲突暂时保留1.6的逻辑
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } else if (route.query?.teamId || route.query?.team_id) {
    return route.query?.teamId || route.query?.team_id;
  } else {
    return null;
  }
});

const store: any = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore;
  }
  return useDigitalPlatformStore();
});

const onGetMemberSquaresAxios = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  isLoading.value = true;
  let stage = null;

  const teamInfo = JSON.parse(localStorage.getItem('honorteam'));
  // 查询活动时的身份卡信息 数字平台使用平台身份查询 数智工厂使用组织内部身份查询 广场使用个人身份查询
  let cardId = null;

  if (route.path.includes('/workBenchIndex')) {
    stage = 'WORKSHOP';
    cardId = teamInfo.cardId;
  }
  if (route.path.includes('square')) {
    stage = 'SQUARE';
    cardId = '';
  }
  if (route.path.includes('/digitalPlatformIndex')) {
    stage = 'PLATFORM';
    cardId = getPlatform().find((item) => item.teamId === currentTeamId.value)?.uuid || '';
  }
  try {
    if(stage === 'PLATFORM'){
      // 活动2.4.4开始：数字平台使用单独接口查询
      result = await listDPActivitiesForUser({
        keywords: keyword.value,
        'pagination.size': params['page.size'],
        'pagination.number': params['page.number'],
        stage,
        categories: params.categories,
        status: params.status,
        'duration.startTime': params['duration.startTime'],
        'duration.endTime': params['duration.endTime'],
        teamId: currentTeamId.value,
        cardId,
        openId: getOpenid(),
      });
    } else {
      console.log('走这里活动工厂');

      result = await getActivityListByTeam({
        keywords: keyword.value,
        'page.size': params['page.size'],
        'page.number': params['page.number'],
        stage,
        categories: params.categories,
        status: params.status,
        'duration.startTime': params['duration.startTime'],
        'duration.endTime': params['duration.endTime'],
        teamId: currentTeamId.value,
        cardId,
        openId: getOpenid(),
      });
    }

    result = getResponseResult(result);
    if (!result) {
      isLoading.value = false;
      return;
    }
    console.log('resultresultresult', result);
    memberList.value = result.data;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    //   MessagePlugin.error(errMsg);
    console.log(errMsg);
  }
  isLoading.value = false;
};
const staffs = ref([]);
const staff = computed(() => {
  return staffs.value.find((v) => v.flag === 'staff_platform');
});
const onGetStaffsPlatform = async () => {
  const [err, res] = await to(getStaffsPlatform(currentTeamId.value));
  if (err) {
    console.log(err);
    return;
  }
  if (res) {
    const { data } = res;
    console.log(data);
    if (data && data.data.length > 0) {
      staffs.value = data.data;
    }
  }
  console.log(staffs.value);
};

const onMore = () => {
  onGetMemberSquaresAxios();
};
import { getOpenid, getPlatform } from '@renderer/utils/auth';
import { ClientSide } from '@renderer/types/enumer';
import { activityCategoryMap } from '@/views/activity/utils';
import { openChat } from '@/utils/share';
import {listDPActivitiesForUser} from "@/api/activity/platform";

const onGoSquarePage = (row) => {
  if (route.path.includes('/workBenchIndex')) {
    // 数智工厂使用组织内部身份进入
    const teamInfo = JSON.parse(localStorage.getItem('honorteam'));

    router.push({
      path: `/workBenchIndex/activityParticipantDetail/${row.id}`,
      query: {
        subject: row.subject,
        teamId: teamInfo.teamId,
        cardId: teamInfo.cardId,
      },
    });
    ipcRenderer.invoke('set-work-bench-tab-item', {
      path: `/workBenchIndex/activityParticipantDetail/${row.id}`,
      path_uuid: 'bench_activityParticipantDetail',
      title: row.subject,
      activeIcon: 'workshop',
      icon: 'workshop',
      name: `/workBenchIndex/activityParticipantDetail/${row.id}`,
      type: ClientSide.ACTIVITY,
    });
    return;
  }

  // 数字平台使用平台身份进入
  const platformCardId = getPlatform().find((item) => item.teamId === currentTeamId.value)?.uuid;

  if (!platformCardId) {
    MessagePlugin.warning('无对应平台身份');
    return;
  }

  digitalPlatformStore.addTab(
    toRaw({
      path: `/digitalPlatformIndex/digital_platform_member_activityParticipantDetail/${row.id}?notTab=true`,
      fullPath: `/digitalPlatformIndex/digital_platform_member_activityParticipantDetail/${row.id}?notTab=true`,
      name: 'noticeDetailRead',
      title: row.subject,
      icon: 'active',
      query: {
        id: row.id,
        title: row.subject,
        notTab: true,
        team_id: currentTeamId.value,
        teamId: currentTeamId.value,
        cardId: platformCardId,
      },
    }),
    true,
  );

  router.push({
    path: `/digitalPlatformIndex/digital_platform_member_activityParticipantDetail/${row.id}?notTab=true`,
    query: {
      title: row.subject,
      teamId: currentTeamId.value,
      cardId: platformCardId,
    },
  });
};
const onSearch = () => {
  console.log('onSearch');
  page.value = null;
  params['page.number'] = 1;
  memberList.value = {};
  onGetMemberSquaresAxios();
};

onMountedOrActivated(() => {
  onSearch(); // 会员名录列表
  onGetStaffsPlatform();
});

// watch(
//   () => store.activeAccount,
//   async (val) => {
//     if (val) {
//       onSearch();

//     }
//   },
//   // {
//   //   // deep: true,
//   //   immediate: true
//   // }
// );

const paramsSuper = computed(
  () => params.categories?.length || params.status || params['duration.startTime'] || params['duration.endTime'],
);

const getDataRun = () => {
  reqParamsHandle();
  onGetMemberSquaresAxios();
};

const reqParamsHandle = () => {
  params['duration.startTime'] = drawerForm.value.duration?.[0] ? moment(drawerForm.value.duration?.[0]).unix() : '';
  params['duration.endTime'] = drawerForm.value.duration?.[1]
    ? moment(drawerForm.value.duration?.[1])
        .add(24 * 60 * 60 - 1, 'seconds')
        .unix()
    : '';
  params.status = drawerForm.value.status || '';
  params.categories = drawerForm.value.categories ? [drawerForm.value.categories] : [];
};

const getDataRunDr = () => {
  filterVisible.value = false;
  params['page.number'] = 1;
  getDataRun();
};

const footerCla = () => {
  // filterVisible.value = false;
  // paramsTemp.value.job_id = undefined;
  // paramsTemp.value.department_id = undefined;
  // paramsTemp.value.role_id = undefined;
  drawerForm.value.categories = '';
  drawerForm.value.duration = [];
  drawerForm.value.status = 0;
};

const activityTypeOption = Object.entries(activityCategoryMap).map(([key, value]) => ({
  label: value,
  value: Number(key),
}));

const activityStatusOption = ref([
  { id: 0, label: t("activity.activity.all") },
  { id: 'TimeFuture', label: t('activity.activity.noStart') },
  {
    id: 'TimeUnderway',
    label: t('activity.activity.ongoing'),
  },
  { id: 'TimeOver', label: t('activity.activity.ended') },
]);

const formatStatusText = (val) => activityStatusOption.value?.find((item) => item.id === val)?.label || '';

const clearFilterCategories = () => {
  drawerForm.value.categories = '';
  params.categories = [];
  getDataRun();
};

const clearFilterStatus = () => {
  drawerForm.value.status = 0;
  params.location = '';
  getDataRun();
};
const clearFilterDuration = () => {
  drawerForm.value.duration = [];
  params['duration.startTime'] = '';
  params['duration.endTime'] = '';
  getDataRun();
};

const clearFilters = () => {
  params.categories = [];
  params['duration.startTime'] = '';
  params['duration.endTime'] = '';
  params.openid = '';
  params.location = '';
  drawerForm.value.categories = '';
  drawerForm.value.duration = [];
  drawerForm.value.status = 0;
  getDataRun();
};

// 排序、分页、过滤等发生变化时会出发 change 事件
const onChange = (info, context) => {
  console.log('change', info, context);
};

const onPageSizeChange = (size) => {
  console.log('page-size:', size);
  params['page.size'] = size;
  // approvalCenterCopyData.value.pageSize = pageSize.value;
  getDataRun();
};

const onCurrentChange = (index, pageInfo) => {
  // page.value = index;
  params['page.number'] = index;
  // approvalCenterCopyData.value.page = page.value;
  console.log(pageInfo, 'onCurrentChange');
  getDataRun();
};

const conhei = computed(() => {
  // if (memberList.value?.summary?.totalOfItems > 12 && paramsSuper.value) {
  //   return `conbox-height3`;
  // }
  // if (memberList.value?.summary?.totalOfItems > 12 && !paramsSuper.value) {
  //   return `conbox-height2`;
  // }
  // if (memberList.value?.summary?.totalOfItems < 12 && paramsSuper.value) {
  //   return `conbox-height4`;
  // }
  return `conbox-height1`;
});

// 打开场景会话
const openIM = ({ e }, row) => {
  e.stopPropagation();

  let cardId;

  if (route.path.includes('/workBenchIndex')) {
    // 数智工厂使用组织内部身份
    const teamInfo = JSON.parse(localStorage.getItem('honorteam'));

    cardId = teamInfo.cardId;
  } else {
    // 数字平台使用在数字平台中的内部身份
    cardId = digitalPlatformStore.activeAccount.cardId;
  }

  const params = row.chatId?.groupId
    ? { main: cardId, group: row.chatId?.groupId }
    : { main: cardId, peer: row.chatId.cardId };
  openChat(params);
};
</script>

<style lang="less" scoped>
// lss 加个滚动条样式
@import '@renderer/views/engineer/less/common.less';
.page {
  display: flex;
  justify-content: start;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: inherit;
  padding: 16px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  height: 100%;
  background: url('https://image.ringkol.com/activity/4aace50/bg_img.png');
  background-repeat: no-repeat;
  background-size: cover;
}
.between {
  display: flex;
  justify-content: space-between;
  padding-right: 12px;
  .app-name {
    color: var(--text-kyy_color_text_1, #1a2139);
    font-family: 'PingFang SC';
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px;
    text-indent: 144px;
  }
}
.square {
  padding: 16px 24px;
  width: 100%;
  border-radius: 16px;
  background: var(--bg-kyy_color_bg_light, #fff);
  overflow-y: hidden;
  padding-right: 12px;
  max-width: 1168px;
  .search {
    display: flex;

    .iconscreen-div {
      top: 16px;
      right: 24px;
      width: 32px;
      height: 32px;
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      border: 1px solid var(--color-button-border-kyy-color-button-border-border-active, #d5dbe4);
      line-height: 28px;
      text-align: center;
      margin-left: 8px;
      cursor: pointer;
      svg {
        color: #828da5;
      }
    }

    .iconscreen-div:hover {
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      border: 1px solid var(--color-button-border-kyy-color-button-border-border-hover, #707eff);
      background: var(--color-button-border-kyy-color-button-border-bg-hover, #eaecff);

      .t-icon {
        color: #707eff !important;
      }
      svg {
        color: #707eff;
      }
    }
  }

  .conbox {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    width: 100%;
    overflow-y: auto;

    .item {
      width: 268px;
      height: 248px;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);
      border: 1px solid #f5f8fe;
      position: relative;
      transition: all 0.15s linear;

      &:hover {
        /*.hover {
          opacity: 1;
        }*/
        border-radius: 8px;
        // background: var(--bg-kyy_color_bg_default, #fff);
        /* kyy_shadow_m */
        // box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
        // transition: all 0.15s linear;
        border-radius: 8px;
        border: 1px solid var(--brand-kyy_color_brand_hover, #707eff);
        background: var(--bg-kyy_color_bg_default, #fff);
      }

      .public {
        display: flex;
        flex-direction: column;
        padding: 12px;
        align-items: center;
        height: 100%;
        gap: 8px;
        .av-box {
          position: relative;
          display: flex;

          .static-tag{
            position: absolute;
            left: 0;
            top: 0;
            background: transparent url("@/assets/activity/static-tag-img.svg") no-repeat center / 100%;
            width: 40px;
            height: 40px;
            z-index: 10;
            border-top-left-radius: 8px;

            .static-tag-text {
              color: #fff;
              font-size: 12px;
              transform: rotate(-45deg);
              position: absolute;
              left: 0;
              top: 3px;
              height: 20px;
              line-height: 20px;
              width: 24px;
            }
          }

          .logo {
            width: 244px;
            height: 144px;
            position: relative;

            &.with-static-tag{
              img{
                border-radius: 12px;
              }
            }

            .img {
              width: 100%;
              height: 100%;
              border-radius: 8px;
              object-fit: cover;
            }
          }
          .av-name {
            display: flex;
            width: 244px;
            padding: 8px 12px;
            align-items: flex-start;
            gap: 10px;
            position: absolute;
            bottom: 0px;
            border-radius: 0px 0px 8px 8px;
            background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.5));

            color: var(--text-kyy_color_text_white, #fff);
            text-overflow: ellipsis;
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 157.143% */

            .av-name-span {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              word-wrap: break-word;
              word-break: break-all;
            }
          }
        }

        .right {
          flex: 1;
          display: flex;
          flex-direction: column;
          width: 100%;
          // justify-content: space-between;
          // height: 100%;

          .lin {
            display: flex;
            align-items: center;
            padding-bottom: 8px;
            //max-width: 194px;
            .star {
              // position: absolute;
              // bottom: -2px;
              // right: -3px;
              display: flex;
              align-items: center;

              &-icon {
                font-size: 24px;
              }
            }

            .name {
              color: var(--text-kyy_color_text_1, #1a2139);
              text-align: center;
              font-size: 14px;
              font-style: normal;
              font-weight: 600;
              line-height: 22px; /* 157.143% */
            }

            .status-tag-1 {
              display: flex;
              height: 20px;
              min-height: 20px;
              max-height: 20px;
              padding: 0px 4px;
              justify-content: center;
              align-items: center;
              gap: 4px;
              border-radius: var(--kyy_radius_tag_s, 4px);
              background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
              color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
              text-align: center;

              /* kyy_fontSize_1/regular */
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px; /* 166.667% */
            }

            .status-tag-2 {
              display: flex;
              height: 20px;
              min-height: 20px;
              max-height: 20px;
              padding: 0px 4px;
              justify-content: center;
              align-items: center;
              gap: 4px;
              border-radius: var(--kyy_radius_tag_s, 4px);
              background: var(--kyy_color_tag_bg_warning, #ffe5d1);
              color: var(--kyy_color_tag_text_warning, #fc7c14);
              text-align: center;

              /* kyy_fontSize_1/regular */
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px; /* 166.667% */
            }

            .status-tag-3 {
              display: flex;
              height: 20px;
              min-height: 20px;
              max-height: 20px;
              padding: 0px 4px;
              justify-content: center;
              align-items: center;
              gap: 4px;
              border-radius: var(--kyy_radius_tag_s, 4px);
              background: var(--kyy_color_tag_bg_warning, #eceff5);
              color: var(--kyy_color_tag_text_warning, #516082);
              text-align: center;

              /* kyy_fontSize_1/regular */
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px; /* 166.667% */
            }
          }

          .type {
            border-radius: var(--kyy_radius_tag_s, 4px);
            background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
            padding: 0 4px;
            color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
            text-align: center;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
            width: fit-content;
          }

          .person {
            background: var(--kyy_color_tag_bg_brand, #eaecff);
            color: var(--kyy_color_tag_text_brand, #4d5eff);
          }

          .bottom {
            color: var(--text-kyy_color_text_3, #828da5);
            // text-align: center;
            max-width: 194px;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
          }
        }
      }
    }
  }

  .conbox-height1 {
    max-height: calc(100% - 52px);
  }
  .conbox-height2 {
    height: calc(100% - 100px);
  }
  .conbox-height3 {
    height: calc(100% - 136px);
  }
  .conbox-height4 {
    height: calc(100% - 48px);
  }
  .example-more {
    display: flex;
    align-items: center;
    justify-content: center;

    .more {
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #d5dbe4);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
      color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      padding: 4px 16px;
    }

    .noempty {
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      display: flex;
      align-items: center;
      width: 100%;
      gap: 12px;
      padding-bottom: 16px;

      .toText {
        flex: none;
      }

      .line {
        height: 1px;
        background: var(--divider-kyy_color_divider_deep, #d5dbe4);
        width: 100%;
      }
    }
  }
}

:deep(.activity_screen_drawer) {
  .t-drawer__header {
    padding: 0 24px;
    color: var(--text-kyy-color-text-1, #1a2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }

  .t-drawer__close-btn {
    right: 24px;
  }

  .t-drawer__body {
    padding: 12px 24px;
  }

  input::-webkit-input-placeholder {
    color: var(--lingke-body-tips, #acb3c0);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }

  input::input-placeholder {
    color: var(--lingke-body-tips, #acb3c0);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

.filter-res {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
  margin-top: 8px;

  .tit {
    color: var(--text-kyy-color-text-2, #516082);

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }

  .ov-time {
    display: flex;
    min-width: 290px;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }

  .close2 {
    margin-left: 8px;

    img {
      width: 12px;
      height: 12px;
    }
  }

  .te {
    color: var(--kyy-color-tag-text-black, #1a2139);
    cursor: pointer;
    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    border-radius: 4px;
    background: var(--kyy-color-tag-bg-gray, #eceff5);
    margin-right: 8px;
  }

  .stat {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }

  .kword {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
    max-width: calc(100% - 100px);
  }

  .icon {
    display: flex;
    margin-left: 4px;
    cursor: pointer;

    img {
      width: 14px;
      height: 14px;
      margin-top: 4px;
      margin-right: 4px;
    }
  }
}

.form-boxxx {
  .fitem {
    margin-bottom: 24px;

    .title {
      color: var(--text-kyy-color-text-3, #828da5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }

    .ctl {
      margin-top: 8px;
    }
  }
}

.foot {
  width: 100%;
  display: flex;
  justify-content: end;

  .btn1 {
    display: flex;
    cursor: pointer;
    height: 32px;
    min-width: 80px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
    text-align: center;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
    background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    margin-right: 8px;
  }

  .btn2 {
    display: flex;
    cursor: pointer;
    width: 88px;
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    background: var(--color-button-primary-kyy-color-button-primary-bg-disabled, #c9cfff);
    color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
    text-align: center;

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }

  .btn3 {
    display: flex;
    cursor: pointer;
    width: 88px;
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);

    color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
    text-align: center;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }
}

.iconscreen-div-active {
  background: var(--color-button-border-kyy-color-button-border-bg-hover, #eaecff);
  border: 1px solid var(--color-button-border-kyy-color-button-border-border-active, #4d5eff) !important;
}

.change-box {
  width: 100%;
  min-width: 1088px;
  display: flex;
  justify-content: flex-end;
  position: relative;
  min-height: 48px;
  max-width: 1168px;
  .logo {
    position: absolute;
    top: 0;
    left: 24px;
    z-index: 20;
    border-radius: 8px;
  }
}
</style>
