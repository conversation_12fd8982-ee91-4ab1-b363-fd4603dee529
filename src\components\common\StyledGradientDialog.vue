<template>
  <t-dialog
    v-bind="$attrs"
    v-model:visible="dialogVisible"
    class="style-gradient-dialog"
    :header="false"
    :footer="footer"
    :close-btn="false"
    placement="center"
  >
    <div
      :style="{
        padding: pd,
      }"
      class="relative rounded-t-16 overflow-hidden"
      :class="!footer ? 'rounded-16' : ''"
    >
      <div class="title-box p-12 flex relative z-10">
        <div class="flex-1 flex flex-col gap-2">
          <template v-if="title">
            <div class="text-16 font-600 text-#1A2139">{{ title }}</div>
            <p v-if="description" class="sub-title text-#516082 text-14">{{ description }}</p>
          </template>
        </div>
        <div class="close flex-shrink-0">
          <div class="cursor" @click="handleClose">
            <iconpark-icon class="text-24 text-#1A2139" name="iconerror"></iconpark-icon>
          </div>
        </div>
      </div>

      <div class="relative z-10">
        <slot></slot>
      </div>

      <div
        v-if="backgroundImage"
        class="absolute top-0 left-0 right-0 w-full bottom-0"
        :style="typeof backgroundImage === 'object' ? backgroundImage?.style : undefined"
      >
        <img
          :src="
            typeof backgroundImage === 'object' && backgroundImage.src
              ? backgroundImage.src
              : 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/h5/culture/dialog_bg.png?t=1'

            // 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/h5/dialog-default-bg.png'
          "
          class="img-box w-full h-full"
          :style="{
            objectFit: typeof backgroundImage === 'object' ? backgroundImage.objectFit : undefined,
            backgroundColor: typeof backgroundImage === 'object' ? backgroundImage.backgroundColor : '#f5f8fe',
          }"
        />
        <div class="bg-color"></div>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import type { CSSProperties } from 'vue';
import { computed } from 'vue';
const props = withDefaults(
  defineProps<{
    // 标题
    title?: string;
    // 描述
    description?: string;
    // 仅需要的 Dialog 属性
    visible?: boolean;
    footer?: boolean | Function;
    padding?: string;
    // 背景图相关配置
    backgroundImage?:
      | {
          // 背景图地址
          src?: string;
          // 背景图样式(主要用于背景图容器高度等定制)
          style?: CSSProperties;
          // 背景样颜色
          backgroundColor?: string;
          // 背景图是否需要裁剪
          objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
        }
      | boolean;
  }>(),
  {
    backgroundImage: true,
    footer: true,
  },
);

const emit = defineEmits(['update:visible', 'close']);

const pd = computed(() => {
  return typeof props.padding !== 'undefined' ? props.padding : '12px';
});

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => {
    emit('update:visible', val);
    if (!val) {
      emit('close');
    }
  },
});

const handleClose = () => {
  dialogVisible.value = false;
};

defineExpose({
  show: () => {
    dialogVisible.value = true;
  },
  hide: () => {
    handleClose();
  },
});
</script>

<style lang="less">
.style-gradient-dialog {
  .t-dialog {
    border: 0;
  }
  .img-box {
    position: absolute;
    z-index: 1;
    height: 100%;
    width: 100%;
    background-color: #f5f8fe;
  }
  .bg-color {
    position: absolute;
    height: 100%;
    width: 100%;
  }
  .close {
    position: absolute;
    top: 12px;
    right: 12px;
    height: 24px;
    z-index: 100000;
  }
}
.style-gradient-dialog .t-dialog--default,
.style-gradient-dialog .t-dialog__body {
  padding: 0;
}

.style-gradient-dialog .t-dialog__footer {
  padding-top: 0px;
  padding-left: 24px;
  padding-right: 24px;
  display: flex;
  align-items: center;
  justify-content: end;
  width: 100%;
  height: 80px;
}
.style-gradient-dialog .t-input__inner {
  order: 1;
}

.style-gradient-dialog .t-input__suffix {
  order: 3;
}

.style-gradient-dialog .t-input__clear {
  order: 2;
}
</style>
