const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始最小化配置构建测试...');

// 使用vite直接构建
function startMinimalBuild() {
  console.log('📦 使用最小化配置构建...');
  
  const buildProcess = spawn('npx', ['vite', 'build', '--config', '.electron-vite/vite.config.minimal.ts'], {
    stdio: 'pipe',
    shell: true,
    cwd: process.cwd()
  });
  
  let output = '';
  let hasStarted = false;
  
  buildProcess.stdout.on('data', (data) => {
    const text = data.toString();
    output += text;
    process.stdout.write(text);
    
    if (text.includes('building for production') || text.includes('✓')) {
      hasStarted = true;
      console.log('📊 检测到构建开始...');
    }
  });
  
  buildProcess.stderr.on('data', (data) => {
    const text = data.toString();
    output += text;
    process.stderr.write(text);
  });
  
  // 设置超时
  const timeout = setTimeout(() => {
    if (!hasStarted) {
      console.log('⏰ 构建似乎卡住了，终止进程...');
      buildProcess.kill('SIGTERM');
    }
  }, 60000); // 1分钟超时
  
  buildProcess.on('close', (code) => {
    clearTimeout(timeout);
    if (code === 0) {
      console.log('✅ 最小化配置构建成功完成');
      checkBuildResults();
    } else {
      console.error(`❌ 构建失败，退出码: ${code}`);
      console.log('输出:', output);
    }
  });
  
  buildProcess.on('error', (error) => {
    clearTimeout(timeout);
    console.error('❌ 构建过程出错:', error.message);
  });
}

function checkBuildResults() {
  try {
    const distPath = path.join(__dirname, 'dist/electron/renderer');
    const assetsPath = path.join(distPath, 'assets');
    
    if (fs.existsSync(assetsPath)) {
      const files = fs.readdirSync(assetsPath);
      const jsFiles = files.filter(f => f.endsWith('.js'));
      const cssFiles = files.filter(f => f.endsWith('.css'));
      const assetFiles = files.filter(f => !f.endsWith('.js') && !f.endsWith('.css'));
      
      console.log('\n📊 构建结果统计:');
      console.log(`JS 文件: ${jsFiles.length} 个`);
      console.log(`CSS 文件: ${cssFiles.length} 个`);
      console.log(`其他资源文件: ${assetFiles.length} 个`);
      
      // 检查文件大小
      let totalSize = 0;
      jsFiles.forEach(file => {
        const filePath = path.join(assetsPath, file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
        totalSize += stats.size;
        console.log(`  ${file}: ${sizeInMB}MB`);
      });
      
      console.log(`\n📈 总JS大小: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
      
      if (assetFiles.length === 0) {
        console.log('✅ 成功！所有静态资源已内联到JS文件中');
      } else {
        console.log('⚠️  仍有部分资源文件未内联:');
        assetFiles.slice(0, 10).forEach(file => { // 只显示前10个
          const filePath = path.join(assetsPath, file);
          const stats = fs.statSync(filePath);
          const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
          console.log(`  ${file}: ${sizeInMB}MB`);
        });
        if (assetFiles.length > 10) {
          console.log(`  ... 还有 ${assetFiles.length - 10} 个文件`);
        }
      }
      
      // 检查资源内联效果
      const inlinedAssets = jsFiles.filter(file => {
        const filePath = path.join(assetsPath, file);
        const content = fs.readFileSync(filePath, 'utf8');
        return content.includes('data:image/') || content.includes('data:font/');
      });
      
      if (inlinedAssets.length > 0) {
        console.log(`\n🎯 检测到 ${inlinedAssets.length} 个JS文件包含内联资源`);
      }
      
    } else {
      console.log('❌ 构建输出目录不存在');
    }
  } catch (error) {
    console.error('❌ 检查构建结果时出错:', error.message);
  }
}

// 先清理，然后开始构建
console.log('🧹 清理构建目录...');
const distPath = path.join(__dirname, 'dist/electron/renderer');
if (fs.existsSync(distPath)) {
  fs.rmSync(distPath, { recursive: true, force: true });
  console.log('✅ 清理完成');
}

startMinimalBuild();
