<template>
  <section class="tool-section" v-if="!isLoading && toolStore.weatherOpen">
    <div class="tool-header">
      <div class="header-left">
        <t-input
          v-model="searchKey"
          clearable
          class="input"
          placeholder="搜索"
          @keyup="onListenSearch"
          @clear="clearInput"
        >
          <template #prefix-icon>
            <!-- <SvgIcon name="im-history" class="svg-size20" /> -->
            <i class="i-svg:im-history text-20 color-text-3" />
          </template>
        </t-input>
      </div>
      <div class="right-icon hover-click-class-1" @click="openSetting">
        <iconpark-icon name="iconsetUp" class="iconset"></iconpark-icon>
      </div>
    </div>
    <div class="content">
      <div class="top-item">
        <div class="top-label">{{ t("im.tools.weather3") }}</div>
        <div class="up-time">
          {{ t("im.tools.lastUpTime") }}: {{ weatherAllList?.[0]?.weather?.condition.updatetime || "" }}
        </div>
      </div>
      <div class="content-info">
        <template v-for="item in weatherList" :key="item.card_id">
          <weatherCard class="weather-card" v-if="item.weather?.city?.cityId" :info="item" />
        </template>
        <Empty name="no-result" v-if="weatherAllList.length > 0 && weatherList.length === 0"/>

      </div>
    </div>
  </section>
  <div class="weather-introduce" v-else-if="!isLoading">
    <img src="@renderer/assets/im/weather/weatherbg.png" alt="" />
    <div class="btn" @click="openSetting"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, nextTick, onMounted, onUnmounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import weatherCard from "./weatherCard.vue";
import Empty from "@renderer/components/common/Empty.vue";

import SvgIcon from "@/components/SvgIcon.vue";
import { useMessageStore } from "../../service/store";
import { useImToolStore } from "@renderer/views/message/tool/service/tool";

import { useGroupTools } from "../../service/extend/chatTools";
import { msgEmit } from "@renderer/views/message/service/msgEmit";

const { t } = useI18n();

const msgStore = useMessageStore();
const toolStore = useImToolStore();

const { loadData, getWeatherList } = useGroupTools();

const isLoading = ref(true);
const weatherList = ref([]);
const weatherAllList = ref([]);
// 获取是否开通天气
const check = () => {
  loadData(msgStore.chatingSession.targetId, msgStore.chatingSession.myCardId)
  .then(async (res) => {
    const setting = res?.members?.[0];
    console.log("dgz =====>", res);
    isLoading.value = false;
    if (setting) {
      toolStore.setWeatherOpen(Boolean(setting.weather));
      if (setting.weather) {
        getMemberWeatherList();
      }
    }
  })
  .catch((err) => {
    isLoading.value = false;
  });
};
// 获取成员天气列表
const getMemberWeatherList = async () => {
  const list = await getWeatherList();
  weatherList.value = list;
  weatherAllList.value = list;
};
const openSetting = () => {
  toolStore.showWeatherSetting(true);
};
const searchKey = ref("");
const onListenSearch = (e) => {
  weatherList.value = weatherAllList.value.filter((v) => {
    if (v.userInfo?.nickname?.toLowerCase().includes(e) || v.userInfo?.staffName?.toLowerCase().includes(e)) {
      return v;
    }
  });
};
const clearInput = (e) => {
  weatherList.value = weatherAllList.value;
};

watch(() => toolStore.weatherSettingVisible, () => {
  check();
});

onMounted(() => {
  check();
  msgEmit.on("loadWeather", () => {
    getMemberWeatherList();
  });
});
onUnmounted(() => {
  msgEmit.off("loadWeather");
});
</script>

<style lang="less">
@import "../../style/tools.less";
</style>

<style lang="less" scoped>
.tool-section {
  display: flex;
  flex-direction: column;
  padding:0;
  .tool-header{
    padding:12px 16px;
  }
  .content {
    flex: 1;
    overflow:hidden;
    display:flex;
    flex-direction: column;
  }
  .content-info{
    flex:1;
    overflow-y:auto;
    overflow-x:hidden;
  }
}
.weather-introduce {
  position: relative;
  width: 376px;
  height: 100%;
  overflow: hidden;
  flex-shrink: 0;
  img {
    width: 100%;
  }
  .btn {
    position: absolute;
    top: 185px;
    left: 30px;
    width: 138px;
    height: 42px;
    cursor: pointer;
  }
}
.top-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-kyy-color-text-2, #516082);
  padding:0 16px;
  margin: 12px 0;
}
.up-time {
  color: var(--text-kyy_color_text_3, #828da5);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.weather-card {
  margin: 0 16px 12px;
}
.header-left {
  flex: 1;
}
.right-icon {
  height: 32px;
  width: 32px;
  border: 1px solid #d5dbe4;
  border-radius: 4px;
  margin-left: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  &:hover {
    border-color: #707eff;
  }
}
</style>
