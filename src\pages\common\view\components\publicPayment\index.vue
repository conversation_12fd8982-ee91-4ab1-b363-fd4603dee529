<template>
  <StyledGradientDialog
    v-model:visible="qrcodeDialogFlag"
    :close-btn="false"
    :header="true"
    :cancel-btn="null"
    :confirm-btn="null"
    :footer="false"
    width="464"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
    class="qrcodeDialog"
    placement="center"
    :title="t('payment.smzf')"
    :background-image="{
      src: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/common_pay_bg.png',
    }"
    @close="qrcodeDialogClose"
  >
    <div class="qrcodes">
      <div class="headM">
        <!-- rowData.pay_amount -->
        <div class="money">
          <span class="money-unit">{{ rowData.pay === 'CN' ? '￥' : 'MOP' }}</span>
          <span class="money-num">
            {{ addCommasToNumber(parseFloat(rowData.pay_amount).toFixed(2)) }}
          </span>
        </div>

        <div class="money-tips">{{ t('payment.payableAmount') }}</div>
      </div>

      <div class="body-box">
        <div class="qrcode-img-box" :class="times === 0 ? 'expires' : ''" @click="refsqrCode">
          <qrcode-vue style="width: 128px; height: 128px; border-radius: 8px" :value="link" :size="128" level="H" />
          <div v-if="times === 0" class="fail-tips">获取失败，点击重新获取二维码</div>
        </div>

        <div v-if="times === 0" class="tips">
          <span>{{ t('payment.qrCodeHasExpired') }}</span>
          <span class="cxhq" @click="refsqrCode">{{ t('payment.cxhq') }}</span>
        </div>

        <div v-else class="tips">
          <i18n-t keypath="payment.qrCodeExTime" tag="span">
            <template #num>
              <span class="time-num">{{ times }}</span>
            </template>
          </i18n-t>
        </div>

        <div class="line"></div>

        <div v-if="payWayStr === '云闪付'" class="ysf-box">
          <div>
            <div>{{ t('order.plaisit') }}{{ payWayStr }}{{ t('order.saocode') }}</div>
            <div>{{ t('payment.scanQRCodePaymentForPayment') }}</div>
          </div>
        </div>

        <div v-else>
          <div class="zf-box">
            <div v-if="resPayRegionDataFlag.includes(3)" class="zfb-box">
              <img src="@assets/img/mypay.svg" />
            </div>
            <div v-if="resPayRegionDataFlag.includes(2)" class="zfb-box">
              <img src="@assets/img/zfb.svg" />
            </div>
            <div v-if="resPayRegionDataFlag.includes(1)" class="zfb-box">
              <img src="@assets/img/wx.svg" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </StyledGradientDialog>
  <t-dialog
    v-model:visible="tipDialogPayOkFlag"
    :close-btn="false"
    :header="true"
    :cancel-btn="null"
    :confirm-btn="null"
    :footer="true"
    width="384"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
    class="tipQrcodeDialog"
  >
    <div style="text-align: center">
      <img
        v-if="paySteta === '支付成功'"
        style="width: 48px; height: 48px; margin-bottom: 16px"
        src="@assets/svg/clouddisk/icon_success.svg"
      />
      <img
        v-if="paySteta === t('payment.paymentErr')"
        style="width: 48px; height: 48px; margin-bottom: 16px"
        src="@assets/img/icon_error.svg"
      />
      <div class="paysess">
        {{ paySteta }}
      </div>
      <div v-if="paySteta === '支付成功'" class="paynum">
        {{ rowData.pay === 'CN' ? '￥' : 'MOP' }}{{ addCommasToNumber(rowData?.pay_amount?.toFixed(2)) }}
      </div>

      <div v-if="paySteta === t('payment.paymentErr')" class="play-customer-service-two">
        {{ rowData.pay === 'CN' ? '￥' : 'MOP' }}{{ addCommasToNumber(rowData?.pay_amount?.toFixed(2)) }}
      </div>
      <div class="flex gap-[20px] flex-row justify-center items-center">
        <template v-if="customBtns?.success">
          <template v-if="paySteta === '支付成功'">
            <t-button
              v-for="customBtn in customBtns?.success"
              :key="customBtn.text"
              :theme="customBtn.theme"
              :variant="customBtn.variant"
              class="min-w-[80px]"
              @click="
                () => {
                  customBtn.onClick(() => {
                    tipDialogPayOkFlag = false;
                  });
                }
              "
            >
              {{ customBtn.text }}
            </t-button>
          </template>
          <template v-else>
            <t-button
              v-for="customBtn in customBtns?.cancel"
              :key="customBtn.text"
              :theme="customBtn.theme"
              @click="
                () => {
                  customBtn.onClick(() => {
                    tipDialogPayOkFlag = false;
                  });
                }
              "
            >
              {{ customBtn.text }}
            </t-button>
            <t-button @click="againPay">重新支付</t-button>
          </template>
        </template>
        <template v-else>
          <t-button v-if="paySteta === '支付成功'" @click="isOk">{{ t('payment.close') }}</t-button>
          <t-button v-else @click="againPay">重新支付</t-button>
        </template>
      </div>
    </div>
  </t-dialog>
  <t-dialog
    v-model:visible="PayPaddingOkFlag"
    :close-btn="false"
    :header="true"
    :cancel-btn="null"
    :confirm-btn="null"
    :footer="true"
    width="384"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
    class="tipQrcodeDialog"
  >
    <div style="text-align: center; height: 170px">
      <img class="rotate" src="@assets/loading.png" />
      <div class="pay-text">{{ t('order.paycximg') }}</div>
      <t-button style="margin-top: 20px" @click="((PayPaddingOkFlag = false), (qrcodeDialogFlag = false))">
        {{ t('payment.close') }}
      </t-button>
    </div>
  </t-dialog>
</template>
<script setup lang="ts" name="paymentDialog">
import { ref, watch, onBeforeUnmount } from 'vue';
import QrcodeVue from 'qrcode.vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import { getPay, getPayStatus } from '../../../api';
import StyledGradientDialog from '@components/common/StyledGradientDialog.vue';
// import mypay from '@assets/img/mypay.svg';
// import zfb from '@assets/img/zfb.svg';
// import wx from '@assets/img/wx.svg';

const link = ref('');
const { t } = useI18n();
const emits = defineEmits(['paymentCallback', 'payclose', 'getDataList']);
let timeData = null;
let downTimer = null;

const qrcodeDialogFlag = ref(false);
const PayPaddingOkFlag = ref(false);
const tipDialogPayOkFlag = ref(false);
const payWayStr = ref(t('payment.scanCodePayment'));
const time180or300 = ref(180);
const times = ref(180);
const paySteta = ref('支付成功');
// 自定义按钮
const customBtns = ref<any>();
const addCommasToNumber = (str) => {
  let [integerPart, decimalPart] = str.split('.');
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  if (decimalPart) {
    decimalPart = decimalPart.length === 1 ? `${decimalPart}0` : decimalPart.slice(0, 2);
  } else {
    decimalPart = '00';
  }
  console.log(`${integerPart}.${decimalPart}`, '222222222222');

  return `${integerPart}.${decimalPart}`;
};
const qrcodeDialogClose = () => {
  const confirmDia = DialogPlugin({
    header: '提示',
    theme: 'info',
    class: 'delmode',
    body: t('payment.orderTip'),
    closeBtn: null,
    zIndex: 9999999999999,

    confirmBtn: t('payment.continuePayment'),
    cancelBtn: t('payment.confirmDeparture'),
    onClose: () => {
      confirmDia.hide();
      qrcodeDialogFlag.value = false;
      emits('payclose');
    },
    onConfirm: () => {
      confirmDia.hide();
    },
  });
};
const rowData = ref({
  sn: '',
  region: 0,
  pay: '',
  pay_amount: 0,
});

const againPayFunc = () => {
  qrcodeDialogFlag.value = true;
  tipDialogPayOkFlag.value = false;
  // getLink(rowData.value.sn);
  times.value = time180or300.value;
  clearTimeout(downTimer);
  clearTimeout(timeData);
  getLinkStart();
  handleCountDown();
};

const isOk = () => {
  emits('paymentCallback', paymentStatus.value);

  tipDialogPayOkFlag.value = false;
};
const outOrderNo = ref('');
const handleCountDown = () => {
  console.log('触发这里handleCountDown');

  times.value--;

  downTimer = setTimeout(() => {
    if (times.value > 0) {
      handleCountDown();
    } else {
      clearTimeout(downTimer);
    }
  }, 1000);
};

const againPay = () => {
  tipDialogPayOkFlag.value = false;
  try {
    getPay({
      order_sn: rowData.value.sn,
      client_type: 0,
      payment: 0,
    })
      .then(async (res) => {
        console.error(res);
        if (res.code !== 0) {
          ToBeConfirmedTwo();
          qrcodeDialogFlag.value = false;
        } else {
          outOrderNo.value = res.data.sn;
          if (res.data.channel === 4) {
            // 拉卡拉
            link.value = res.data.params.qr_code;
          } else if (res.data.channel === 3) {
            // mypay
            link.value = res.data.params.counter_url;
          }
          againPayFunc();
        }
      })
      .catch(() => {
        ToBeConfirmedTwo();
      });
  } catch {
    ToBeConfirmedTwo();
  }
};

// OTHER = 0; //其他类型
// WXPAY = 1; //微信支付
// ALIPAY = 2; //支付宝支付
// MPAY = 3; //mpay支付
// PAY_PUBLIC = 4; //对公支付
// IAP = 5; //苹果应用内购买
const getLink = (sn) =>
  new Promise((resolve, reject) => {
    getPay({
      order_sn: sn,
      client_type: 0,
      payment: 0,
    })
      .then((res) => {
        console.log(res, 'resressssss');
        if (res.code !== 0) {
          reject(res);
          emits('getDataList', sn);

          qrcodeDialogFlag.value = false;
        } else {
          outOrderNo.value = res.data.sn;
          if (res.data.channel === 4) {
            // 拉卡拉
            link.value = res.data.params.qr_code;
          } else if (res.data.channel === 3) {
            // mypay
            link.value = res.data.params.counter_url;
          }
          resolve(res);
        }
      })
      .catch((err) => {
        if (err.response.data.code === 60002) {
          ToBeConfirmedTwo(err.response.data.message);
        }
      });
  });

let clickFlag = false;
const resPayRegionDataFlag = ref([1, 2]);
const openWin = async (row) => {
  if (clickFlag) {
    return;
  }
  clickFlag = true;
  console.log(row, 'rowwwwwwwwwww');

  rowData.value = row;
  rowData.value.pay = row.region;
  customBtns.value = row.customBtns || undefined;
  clearTimeout(downTimer);
  clearTimeout(timeData);
  qrcodeDialogFlag.value = true;
  try {
    // row.region
    const res = await getLink(row.sn, row.region);
    console.log(res, 'resresress');
    if (res.data.channel === 4) {
      time180or300.value = 300;
    } else {
      time180or300.value = 180;
    }
    times.value = time180or300.value;

    console.log(resPayRegionDataFlag.value, 'resPayRegionDataresPayRegionData');
    handleCountDown();
    qrcodeDialogFlag.value = true;
    clickFlag = false;
  } catch (error: any) {
    console.log(error, 'reererer');
    console.log(error.message, 'reererererror.data.message');
    if (error.message !== 'Network Error') {
      MessagePlugin.error(error.response?.data?.message);
    }
    clickFlag = false;
  }
};
defineExpose({
  openWin,
});
onBeforeUnmount(() => {
  clearTimeout(timeData);
  clearTimeout(downTimer);
});
const refsqrCode = async () => {
  if (times.value === 0) {
    await getLink(rowData.value.sn, rowData.value.pay);
    // times.value = 180;
    times.value = time180or300.value;

    clearTimeout(downTimer);
    clearTimeout(timeData);

    getLinkStart();
    handleCountDown();
    console.log(
      timeEnd.value - new Date().getTime(),
      'timeEnd.value-new Date().getTime()timeEnd.value-new Date().getTime()',
    );
  }
};

// const payStetaTip = ref('');
const paymentStatus = ref(0); // 0待支付 1支付成功 2支付渠道已关闭 3转入退款 4扫码支付已取消 5支付中 6支付失败
const timeEnd = ref(null);
let isRequesting = false;

const getLinkStart = () => {
  if (times.value < 1) {
    clearTimeout(timeData);
    return;
  }
  if (isRequesting) return; // 防止并发
  isRequesting = true;
  // if (!outOrderNo.value) {
  //   isRequesting = false;
  //   return;
  // }
  getPayStatus(outOrderNo.value)
    .then((res: any) => {
      const { status } = res.data;
      paymentStatus.value = status;
      if (status === 0) {
        timeData = setTimeout(() => {
          getLinkStart();
        }, 3000);
      } else if ([1, 2, 4, 6].includes(status)) {
        if (status === 1) {
          paySteta.value = '支付成功';
          qrcodeDialogFlag.value = false;
          tipDialogPayOkFlag.value = true;
        } else if (status === 4) {
          paySteta.value = t('payment.paymentErr');
          qrcodeDialogFlag.value = false;
          tipDialogPayOkFlag.value = true;
        } else {
          paySteta.value = t('payment.paymentErr');
          qrcodeDialogFlag.value = false;
          tipDialogPayOkFlag.value = true;
          times.value = 0;
        }
        PayPaddingOkFlag.value = false;
        clearTimeout(timeData);
      } else if (status === 5) {
        if (qrcodeDialogFlag.value) {
          closeModalContinue.value = true;
          qrcodeDialogFlag.value = false;
          PayPaddingOkFlag.value = true; // 状态5
        }
        timeData = setTimeout(() => {
          // times.value = 180;
          times.value = time180or300.value;
          getLinkStart();
        }, 3000);
      } else {
        timeData = setTimeout(() => {
          getLinkStart();
        }, 3000);
      }
    })
    .catch((err: any) => {
      if (err.response.data.code === 60002) {
        ToBeConfirmedTwo();
      } else {
        timeData = setTimeout(() => {
          getLinkStart();
        }, 3000);
      }
    })
    .finally(() => {
      isRequesting = false; // 请求结束，释放锁
    });
};

const ToBeConfirmedTwo = () => {
  // 待确认
  const ToBeConfirmedDia = DialogPlugin({
    header: '提示',
    body: `订单状态已变更，请重新确认`,
    theme: 'info',
    confirmBtn: {
      content: '知道了',
    },
    cancelBtn: null,
    onConfirm: () => {
      ToBeConfirmedDia.hide();
      emits('paymentCallback', paymentStatus.value);
    },
  });
};

const closeModalContinue = ref(false);

watch(
  () => qrcodeDialogFlag.value,
  (newvalue) => {
    console.log(newvalue, 'newvaluenewvaluenewvalue');

    if (newvalue) {
      clearTimeout(timeData);
      getLinkStart();
    } else {
      console.log(closeModalContinue.value, 'closeModalContinue.value');
      if (!closeModalContinue.value) {
        console.log(33333333);
        clearTimeout(timeData);
        clearTimeout(downTimer);
        // times.value = 180;
        timeData = null;
        downTimer = null;
        // times.value = 180;
        times.value = time180or300.value;
      }
    }
  },
);
</script>
<style scoped lang="less">
.ysf-box {
  margin: 16px auto 0;
  width: 200px;
  height: 58px;
  background: #eeffe8;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    width: 40px;
    height: 40px;
    margin-right: 8px;
  }
  div {
    font-size: 14px;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei-Regular;
    font-weight: 400;
    color: #1c8710;
    line-height: 22px;
  }
}
.pay-text {
  text-align: center;
  width: 100%;
  height: 24px;
  font-size: 16px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Bold;
  font-weight: 700;
  text-align: center;
  color: #1a2139;
  line-height: 24px;
}
.rotate {
  margin-top: 28px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  animation: rotate 2s infinite linear;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.play-customer-service {
  height: 22px;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: center;
  color: #717376;
  line-height: 22px;
  margin-bottom: 24px;
}
.play-customer-service-two {
  color: #da2d19;
  font-size: 20px;
  font-weight: 800;
  margin-bottom: 8px;
}
.back-box {
  padding-left: 16px;
  display: flex;
  height: 22px;
  margin-top: 50px;
  cursor: pointer;
  font-size: 14px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Regular;
  font-weight: 400;
  color: #4d5eff;
  line-height: 22px;
  align-items: center;
  img {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}

.paynum {
  height: 24px;
  font-size: 16px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Bold;
  font-weight: 700;
  text-align: center;
  color: #da2d19;
  line-height: 24px;
  margin-bottom: 24px;
}
.paysess {
  height: 24px;
  font-size: 16px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei-Bold;
  font-weight: 700;
  text-align: center;
  color: #1a2139;
  line-height: 24px;
  margin-bottom: 8px;
}
.qrcodes {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #fff;
  overflow: hidden;

  .foot-boxs {
    width: 300px;
    cursor: pointer;
    height: 72px;
    border: 1px solid #eceff5;
    border-radius: 4px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
    margin-left: 16px;
    img {
      width: 32px;
      height: 32px;
      margin-right: 8px;
    }
    div {
      height: 22px;
      font-size: 14px;
      font-family:
        Microsoft YaHei,
        Microsoft YaHei-Regular;
      font-weight: 400;
      color: #1a2139;
      line-height: 22px;
    }
  }
  .zf-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .zfb-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 112px;
      height: 24px;

      .labzfb {
        height: 12px;
        font-size: 8px;
        font-family:
          Microsoft YaHei,
          Microsoft YaHei-Regular;
        font-weight: 400;
        text-align: left;
        color: #1a2139;
        line-height: 12px;
      }
    }
  }
  .tips {
    text-align: center;
    height: 22px;
    font-size: 14px;

    font-family: PingFang SC;
    font-weight: 400;
    color: #516082;
    line-height: 22px;
  }

  .time-num {
    margin: 0 4px;
    color: #d54941;
  }
  .expires {
    background: rgba(#141a42, 0.8);
    position: relative;
  }
  .expires::after {
    content: '';
    width: 144px;
    height: 144px;
    background: rgba(#141a42, 0.8);

    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 22px;
    border-radius: 8px;
    transform: translate(-50%, -50%);
  }
  .fail-tips {
    width: 98px;
    position: absolute;
    top: 50%;
    color: #ffff;
    z-index: 9;
    left: 50%;
    margin-left: -49px;
    margin-top: -22px;
  }
  .qrcode-img-box {
    margin: 0 auto;
    width: 144px;
    height: 144px;
    padding: 8px;
    border-radius: 8px;
    position: relative;
    margin-bottom: 8px;
    img {
      width: 200px;
      height: 200px;
    }
  }
}
.zfbs {
  width: 112px;
  height: 32px;
}

.foot-boxs:last-child {
  margin-left: 0;
}
.foot-boxs:first-child {
  margin-left: 0;
}
.foots {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0 16px;
}

.body-box {
  padding: 16px 32px;
  background: #ffffff;
  border-radius: 0 0 4px 4px;
  position: relative;
}

.headM {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 88px;
  width: 100%;
  text-align: center;
  overflow: hidden;
  background: url(http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/common_money_bg.png);
  padding: 16px 24px;

  .money {
    text-align: center;
    font-family: PingFang SC;
    color: #d54941;
    font-weight: 600;

    .money-unit {
      margin-right: 4px;
      font-size: 16px;
      line-height: 24px;
    }

    .money-num {
      font-size: 28px;
      line-height: 32px;
    }
  }

  .money-tips {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #d54941;
    line-height: 22px;
  }
}

.line {
  width: 100%;
  height: 1px;
  background: #eceff5;
  margin-top: 16px;
  margin-bottom: 16px;
}

.cxhq {
  margin-left: 4px;
  font-size: 14px;
  line-height: 22px;
  color: #4d5eff;
  cursor: pointer;
  opacity: 1;
  transition: opacity 0.3s;

  &:hover {
    opacity: 0.8;
  }
}
</style>
