<template>
  <t-loading size="medium" :showOverlay="false" class="memberLoading" :loading="isLoading" text="加载中...">
    <div class="page" @click.stop="onScroll">
      <div class="change-box">
        <img class="logos" src="@renderer/assets/member/svg/square_logo.svg" />
      </div>
      <div class="square">
        <div
          class="scroll"
          v-infinite-scroll="handleInfiniteOnLoad"
          :infinite-scroll-immediate-check="false"
          :infinite-scroll-disabled="scrollDisabled"
          infinite-scroll-watch-disabled="scrollDisabled"
          :infinite-scroll-distance="20"
          @scroll="onScroll"
        >
          <div class="search">
            <div class="search-box">
              <div class="titleName">
                <span class="name"> {{ resourceLabels.title }} </span>
                <locationWidget
                  ref="latWidgetRef"
                  @click.stop="some"
                  :address-text="addressText"
                  :address-data="addressData"
                  @edit-is-map-page-flag="editIsMapPageFlag"
                  @edit-address-data="editAddressData"
                  @edit-address-text="editAddressText"
                />
              </div>
              <div class="in-box">
                <t-input
                  v-model="keyword"
                  :placeholder="t('niche.sssq')"
                  :maxlength="50"
                  clearable
                  style="width: 304px"
                  @change="onSearch"
                >
                  <template #prefix-icon>
                    <iconpark-icon name="iconsearch-a961a3le" class="iconsearch"></iconpark-icon>
                  </template>
                </t-input>
              </div>
            </div>
            <div class="banner mt-24px">
              <div class="tabox">
                <div class="tbox" ref="taboxContainer">
                  <div class="tabs" :class="{ setH: !!isExpand }" ref="tabsContainer">
                    <div
                      class="tab cursor"
                      v-for="(tabItem, tabItemIndex) in tabsList"
                      @click="onSetTab(tabItem)"
                      :class="{ activeTab: currentTabKey === tabItem?.value }"
                      :key="tabItemIndex"
                    >
                      {{ tabItem.label }}
                    </div>
                  </div>
                </div>
                <template v-if="showExpand">
                  <div class="expand cursor" @click="isExpand = !isExpand">
                    {{ isExpand ? '收起' : '展开' }}
                    <iconpark-icon v-show="!isExpand" class="iarrow" name="iconarrowdown"></iconpark-icon>
                    <iconpark-icon v-show="isExpand" class="iarrow" name="iconarrowup-a960jjb9"></iconpark-icon>
                  </div>
                </template>
              </div>
              <div class="guodu">
                <div class="line"></div>
              </div>
              <div class="area">
                <t-popup placement="bottom-right" v-model:visible="currentSortVisible" overlay-class-name="lssPop">
                  <template #content>
                    <div>
                      <t-list class="list">
                        <t-list-item
                          v-for="(item, itemIndex) in optionAreas"
                          :key="itemIndex"
                          class="list-item cursor"
                          :class="{
                            lactive: item?.value === currentSortItem.value,
                          }"
                          style="text-align: left"
                          @click="radioChangeSort(item)"
                        >
                          {{ item.label }}
                        </t-list-item>
                      </t-list>
                    </div>
                  </template>
                  <div class="btnarea cursor">
                    {{ currentSortItem?.label }}<iconpark-icon class="iarrow" name="iconarrowdown"></iconpark-icon>
                  </div>
                </t-popup>
              </div>
            </div>
          </div>

          <div v-if="memberList.length > 0" class="conbox">
            <wc-waterfall gap="12" cols="3">
              <div
                v-for="(menu, menuIndex) in memberList"
                :key="menuIndex"
                class="item cursor"
              >
                <div class="public">
                <span class="logo">
                  <t-image
                    v-if="menu?.square?.squareType === 'INDIVIDUAL'"
                    class="img"
                    fit="cover"
                    :src="getSrcThumbnail(menu?.square?.avatar)">
                            <template #error>
                              <KyyAvatar
                                class="avatar"
                                avatar-size="48px"
                                :image-url="''"
                                :user-name="menu?.square?.name"
                                shape="circle"
                              />
                            </template>
                            <template #loading>
                              <img :src="ORG_DEFAULT_AVATAR" />
                            </template>
                          </t-image>
                          <t-image
                            v-else
                            :src="menu?.square?.avatar ? getSrcThumbnail(menu?.square?.avatar) : ''"
                            class="img"
                            fit="cover"
                          >
                            <template #loading>
                              <img :src="ORG_DEFAULT_AVATAR" class="img" />
                            </template>
                            <template #error>
                              <img :src="ORG_DEFAULT_AVATAR" class="img" />
                            </template>
                          </t-image>
                        </span>
                  <div class="right">
                    <div class="flex justify-between items-center">
                           <span class="lin">
                            <span class="star">
                              <iconpark-icon
                                v-if="menu?.square?.squareType === SquareType.Enterprise"
                                name="iconenterprise"
                                class="star-icon"
                              ></iconpark-icon>
                              <iconpark-icon
                                v-else-if="menu?.square?.squareType === SquareType.BusinessAssociation"
                                name="iconbusiness"
                                class="star-icon"
                              ></iconpark-icon>
                              <iconpark-icon
                                v-else-if="menu?.square?.squareType === SquareType.IndividualBusiness"
                                name="iconindividual"
                                class="star-icon"
                              ></iconpark-icon>
                              <iconpark-icon
                                v-else-if="menu?.square?.squareType === SquareType.Other"
                                name="iconother"
                                class="star-icon"
                              ></iconpark-icon>
                              <iconpark-icon
                                v-else-if="menu?.square?.squareType === SquareType.Government"
                                name="icongov"
                                class="star-icon"
                              ></iconpark-icon>
                            </span>
                             <REllipsisTooltip class="name w-192 font-600 text-[#1A2139]" :text="menu?.square?.name" />
                          </span>
                    </div>

                    <div class="flex items-center gap-8">
                      <div class="inline-flex px-4 border border-solid border-[#FFBE16] rounded-4 shrink-0">
                        <span class="text-11 leading-16 text-[#D9A213]"><span class="mr-2">粉丝</span>{{ formatFansCount(menu?.square?.fansCount) }}</span>
                      </div>
                      <div v-if="menu?.square?.distance && menu?.square?.distance !== '0'" class="inline-flex items-center gap-2 px-4 border border-solid border-[#FFBE16] rounded-4 shrink-0">
                        <iconpark-icon name="iconlocal2" class="text-14 text-[#D9A213] mr-2"></iconpark-icon>
                        <span class="text-11 leading-16 text-[#D9A213]">{{ formatDistance(menu?.square?.distance) }}</span>
                      </div>
                    </div>
                  </div>
                  <t-button theme="default" class="square-link-button" @click="onGoSquarePage(menu)">
                    <span class="font-500 text-12">{{ $t("member.eb.j") }}</span>
                  </t-button>
                </div>

                <ProductThumbnail v-if="menu?.square?.promotionProducts?.length || menu?.square?.imgs?.length" class="mt-12 w-full" :products="menu?.square?.promotionProducts" :imgs="menu?.square?.imgs" :size="92" />

                <div class="visible" v-show="menu.square.squareType !== SquareType.Individual && !menu.connected">
                  <div class="tip">{{t('niche.wzs1')}}</div>
                  <t-button class="btn" theme="primary" @click="onGoSquarePage(menu)">
                    <iconpark-icon class="iconpreciewopen" name="iconpreciewopen"></iconpark-icon>
                    <span class="text ml-4px">{{t('niche.wzs2')}}</span>
                  </t-button>
                </div>

                <!-- <div class="hover">
                  <div class="top">
                    <span class="logo">
                      <img v-lazy="menu?.square?.avatar" class="img">
                      <span class="star">
                        <iconpark-icon v-if="menu?.square?.squareType === 'ENTERPRISE'" name="iconenterprise" class="star-icon"></iconpark-icon>
                        <iconpark-icon v-else-if="menu?.square?.squareType === 'BUSINESS_ASSOCIATION'" name="iconbusiness" class="star-icon"></iconpark-icon>
                        <iconpark-icon v-else-if="menu?.square?.squareType === 'INDIVIDUAL_BUSINESS'" name="iconother" class="star-icon"></iconpark-icon>
                        <iconpark-icon v-else-if="menu?.square?.squareType === 'OTHER'" name="iconother" class="star-icon"></iconpark-icon>
                      </span>
                    </span>
                    <span class="name line-2 mt-12px">{{ menu?.square?.name }}</span>
                    <span v-if="menu?.square?.squareType === 'INDIVIDUAL'" class="type person line-2 mt-8px">个人广场号</span>
                    <span v-else class="type line-2 mt-8px">{{ menu?.certInfo?.industry }}</span>
                  </div>
                  <t-button
                    theme="default"
                    variant="outline"
                    class="bottom mt-12px"
                    @click="onGoSquarePage(menu)"
                  >
                    {{ $t('member.long.org_2') }}
                  </t-button>
                </div> -->
              </div>
            </wc-waterfall>
          </div>
          <template v-if="!isLoading">
            <div v-if="memberList.length < 1" class="empty">
              <Empty :tip="keyword ? t('niche.nossreq') : t('niche.no_data')" :name="keyword ? 'no-result' : 'no-friend-list'" />
            </div>
            <div class="noMore" v-else-if="memberList.length > 0 && !page?.nextPageToken">
              <span class="noMore-line"></span> <span class="noMore-text">没有更多了</span> <span class="noMore-line"></span>
            </div>
          </template>
        </div>
      </div>
      <!-- <template v-if="!isNetworkError">
          <div v-show="!isLoading && memberList.length < 1" class="noEmpty">
            <Empty :tip="$t('member.second.r')" />
          </div>
        </template>
        <template v-else>
          <div v-show="!isLoading && memberList.length < 1" class="noEmpty">
            <Empty name="offline">
              <template #tip>
                <div class="tipEmpty">
                  <span class="text">网络链接失败，请检查网络后重试</span>
                  <t-button theme="primary" class="btn" @click="onSearch">点击重试</t-button>
                </div>
              </template>
            </Empty>
          </div>
        </template> -->
    </div>
  <BMap :height="0" @initd="onMapInit" />
  <AnnualConnect ref="annualConnectRef" :activeAccount="activeAccount" @backType="onBackType"></AnnualConnect>
  <Tricks :offset="{ x: '-32', y: '-40' }" :uuid="uuidCpt" />
  </t-loading>
</template>

<script setup lang="ts">
import { Ref, computed, onActivated, reactive, ref, toRaw, watch } from 'vue';
import { getMemberSquaresAxios } from '@renderer/api/member/api/businessApi';
import { getResponseResult } from '@renderer/utils/myUtils';
import Empty from '@renderer/components/common/Empty.vue';
import { useMemberStore } from '@renderer/views/member/store/member';
import { toSquareHome } from '@/views/square/utils/ipcHelper';
import { useI18n } from 'vue-i18n';
import { ORG_DEFAULT_AVATAR } from '@/views/square/constant';
import { getMemberTeamID } from '@renderer/views/member/utils/auth';
import { useApi } from '@renderer/views/member/hooks/api';
import { useRoute } from 'vue-router';
import { useDigitalPlatformStore } from '@renderer/views/digital-platform/store/digital-platform-store';
import { DigitalPlatformTypeSquare, platform } from '@renderer/views/digital-platform/utils/constant';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import LikeButton from '@renderer/views/digital-platform/components/LikeButton.vue';
import {getSrcLogo, getSrcThumbnail} from '@renderer/views/message/service/msgUtils';
import { SquareType } from '@renderer/api/square/enums';
import { getOpenid } from '@renderer/utils/auth';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import AnnualConnect from '@renderer/views/digital-platform/components/AnnualConnect.vue';
import { TeamAnnualTypeError } from '@renderer/views/digital-platform/utils/auth';

import locationWidget from '@/views/big-market/components/location-widget.vue';
import { useBaiduMap } from '@renderer/components/common/map/hooks';

import { getListSquareIndustriesAxios } from '@renderer/api/digital-platform/api/businessApi';
import { to } from 'await-to-js';
import KyyAvatar from '@/components/kyy-avatar/index.vue';
import {formatDistance, formatFansCount} from "@/views/digital-platform/utils/format";
import ProductThumbnail from "@/views/digital-platform/components/ProductThumbnail.vue";

import { BMap } from 'vue3-baidu-map-gl';
import {REllipsisTooltip} from "@rk/unitPark";

// import { Tricks } from '@rk/unitPark';
const digitalPlatformStore = useDigitalPlatformStore();
const openId = getOpenid();
const { t } = useI18n();
const route = useRoute();
const keyword = ref('');
const store = useMemberStore();
const { onActionSquare } = useApi();
const isLoading = ref(false);
const isNetworkError = ref(false);
const memberList = ref([]);
const page = ref(null);
const pageSize = ref(24);

const annualFeeDialogUpgradeVisible: Ref<any> = ref(false);
// const generateRandomLabel = () => {
//   const words = ['科技','创新','健康','教育','金融','智能','生态','未来','发展','数据','安全','绿色','能源','城市','数字'];
//   const length = Math.floor(Math.random() * 3) + 2; // 生成2-4字组合
//   return Array.from({length}, () => words[Math.floor(Math.random() * words.length)]).join('');
// };

// const tabsList = Array.from({length:10}, () => {
//   const label = generateRandomLabel();
//   return { label, value: label };
// });
const tabsList = ref([]);
// const currentTabKey = ref(tabsList?.length > 0 ? tabsList[0].value : '');
const currentTabKey: any = ref('');

/**
 * ORDER_BY_TYPE_UNKNOWN
  ORDER_BY_TYPE_NEARBY
  ORDER_BY_TYPE_VISITS
  ORDER_BY_TYPE_FANS
  ORDER_BY_TYPE_LIKES
 */
const optionAreas = [
  { label: t('square.df'), value: 'ORDER_BY_TYPE_NEARBY' },
  { label: t('square.vf'), value: 'ORDER_BY_TYPE_VISITS' },
  { label: t('square.ff'), value: 'ORDER_BY_TYPE_FANS' },
  { label: t('square.lf'), value: 'ORDER_BY_TYPE_LIKES' },
];
const currentSortVisible = ref(false);
const currentSortItem = ref(optionAreas[0]);

const radioChangeSort = (item) => {
  currentSortItem.value = item;
  // isLoading.value = true;
  onSearch();
};

const uuidCpt = computed(() => {
  let msg = '数字城市-广场号';
  switch (digitalPlatformStore.platformType) {
    case 'member':
      msg = '数字商协-广场号';
      break;
    case 'government':
      msg = '数字城市-广场号';
      break;
    case 'cbd':
      msg = '数字CBD-广场号';
      break;
    case 'association':
      msg = '数字社群-广场号';
      break;
  }
  return msg;
});

const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
});
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform;
});

const currentTeamId = computed(() => {
  if(route.name === 'square_politics_square') {
    return route.query?.teamId
  }
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } else {
    return getMemberTeamID();
  }
});

const activeAccount = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query;
  } else if (route.name === 'square_politics_square') {
    return route.query;
  } else {
    return store.activeAccount;
  }
});

// 获取行业分类
const onGetListSquareIndustriesAxios = async () => {
  const [err, res] = await to(getListSquareIndustriesAxios(currentTeamId.value));
  if (err) {
    throw err?.message;
    // return;
  }
  const { data } = res;
  console.log('onGetListSquareIndustriesAxios', data);
  tabsList.value =
    data?.data?.Industries?.map((v) => {
      return {
        label: v.name,
        value: v.id,
      };
    }) || [];
  // tabsList.value.unshift({
  //   label: '个人',
  //   value: 0
  // });
  // 添加'全部'选项
  tabsList.value.unshift({
    label: t('square.allKind'),
    value: '',
  });
};

// let listCount = 0;
const scrollDisabled = computed(() => {
  // console.log(memberList.value.length, listCount, memberList.value.length >= listCount);
  // console.log(listCount, "dd", listCount > 0 ? memberList.value.length >= listCount : false);
  // return listCount > 0 ? memberList.value.length >= listCount : false;
  console.log('ddd', page.value, page.value && page.value?.nextPageToken);
  return !(!page.value || (page.value && page.value?.nextPageToken));

  // return (!(page.value && page.value?.nextPageToken)) || !!page.value
  // return memberLevels.value.length >= listCount ;
});

const handleInfiniteOnLoad = () => {
  console.log('handleInfiniteOnLoad', scrollDisabled.value);
  // 异步加载数据等逻辑
  if (scrollDisabled.value) {
    // 数据加载完毕
    isLoading.value = false;

  } else {
    // 加载数据列表
    if(isLocationLoaded.value){
      isLoading.value = true;
      onGetMemberSquaresAxios(false);
    }
  }
};

const squareResult = ref(null);
const isSetLoading = ref(false);
const onGetMemberSquaresAxios = async (isCover = false) => {
  if(isSetLoading.value) return;
  let result = null;
  // eslint-disable-next-line no-async-promise-executor

  // 缓存信息存储
  const caches = store.getStorageDatas;
  const cache = caches.find((v) => v.teamId === currentTeamId.value);

  if (cache) {
    // memberList.value = cache.memberSquare?.items || [];
    // page.value = cache.memberSquare?.page;
  } else {
    isLoading.value = true;
  }
  console.log('getStorageDatas');

  let digital_platform_type = DigitalPlatformTypeSquare.Member;
  switch (route?.name) {
    case 'digital_platform_association_square':
      digital_platform_type = DigitalPlatformTypeSquare.Association;
      break;
        case 'digital_platform_uni_square':
      digital_platform_type = DigitalPlatformTypeSquare.Uni;
      break;
    case 'digital_platform_member_square':
      digital_platform_type = DigitalPlatformTypeSquare.Member;
      break;
    case 'digital_platform_politics_square':
      digital_platform_type = DigitalPlatformTypeSquare.Government;
      break;
    case 'square_politics_square':
      digital_platform_type = DigitalPlatformTypeSquare.Government;
      break;
    case 'digital_platform_cbd_square':
      digital_platform_type = DigitalPlatformTypeSquare.CBD;
      break;
    default:
      break;
  }
  isSetLoading.value = true;
  
  try {
    result = await getMemberSquaresAxios(
      {
        keyword: keyword.value,
        'page.size': pageSize.value,
        'page.next_page_token': page.value ? page.value.nextPageToken : '',
        digital_platform_type,
        'latLng.latitude': addressData.value?.length > 1 ? addressData.value[1] : 0,
        'latLng.longitude': addressData.value?.length > 1 ? addressData.value[0] : 0,
        orderBy: currentSortItem.value?.value,
        industryId: currentTabKey.value,
        queryIndividualOnly: currentTabKey.value === -1 ? true : false,
      },
      currentTeamId.value,
    );
    isSetLoading.value = false;
    result = getResponseResult(result);
    result = result?.data;
    isNetworkError.value = false;
    if (!result) {
      isLoading.value = false;
      return;
    }
    result.items.forEach((v) => {
      v.square.avatar = v.square.avatar ? getSrcLogo(v.square.avatar) : '';
    });
    squareResult.value = result;
    if (isCover) {
      memberList.value = result.items;
    } else {
      memberList.value = memberList.value.concat(result.items);
    }

    page.value = result.page;
    // 缓存处理 start
    const memberSquare = {
      items: toRaw(memberList.value),
      page: result.page,
    };
    if (cache) {
      cache.memberSquare = memberSquare;
    } else {
      caches.push({ teamId: currentTeamId.value, memberSquare });
    }
    console.log('caches: ', caches);
    store.setStorageDatas(caches);
    // 缓存处理 end
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    //   MessagePlugin.error(errMsg);
    console.log('dong:', errMsg);
    if (errMsg === 'Network Error') {
      isNetworkError.value = true;
    }
  }
  isLoading.value = false;
};

const annualConnectRef = ref(null);
// const selectOrganizeModalRef = ref(null)
const onBackType = (res) => {
  if (res?.type === TeamAnnualTypeError.Success) {
    // MessagePlugin.success('连接成功')
    // 更新详情里面的数据
    // if(isShowNameDetail.value) {
    //   onGetMemberNameDetail(detailData.value).then((res:any) => {
    //     console.log(res)
    //     nameDetailModelRef.value?.onOpen(res.data);
    //   });
    // }

    onSearch();
  }
};

const squareTeamId = ref('');
const onGoSquarePage = (square) => {
  // ipcRenderer.invoke("create-dialog", {
  //   url: `square-single/info?id=${square?.square?.squareId}`,
  //   opts: {
  //     width: 656,
  //   },
  // });
  if (square.connected) {
    // 如果已经链接
    onActionSquare({}, square?.square?.squareId);
  } else {
    if (square?.square?.squareType === SquareType.Individual) {
      onActionSquare({}, square?.square?.squareId);
      return;
    }
    annualConnectRef.value.onConnectPlatform({
      relation_team_id: square?.square?.originId,
      from: 'square',
    });

    // const param = {
    //   squareId: square?.square?.squareId,
    //   belong_team_id: currentTeamId.value,
    //   consume_team_id: square?.square?.originId
    // }
    // onDisplayToPlatform(param, currentTeamId.value).then((res) => {
    //   if (res === 'update') {
    //     console.log('update')
    //     squareTeamId.value = square?.square?.originId;
    //     annualFeeDialogUpgradeVisible.value = true;
    //   } else if (res === 'success') {
    //     MessagePlugin.success('连接成功')
    //     onSearch();
    //   }
    // }).catch((err) => {
    //   console.log('err:', err)
    //   if (err && err.message) {
    //     MessagePlugin.error(err.message);
    //   }
    // })
  }
};

// 升级后的回调
const upgradeLoaded = () => {
  // console.log('upgradeLoaded')
  // MessagePlugin.success('购买成功')
  onSearch();
  // 升级后重新触发流程
  // upgradePackageRef.value.onClose();
  // onRunTeamAnnualFee()
};

const onSetTab = (tabItem) => {
  if (currentTabKey.value === tabItem?.value) {
    return;
  }
  currentTabKey.value = tabItem?.value;
  // isLoading.value = true;
  onSearch();
};

const addressData = ref(null);
const addressText = ref(null);
const isLocationLoaded = ref(false);
// 地图 IP 定位
const { location, markerInfo, onMapInit } = useBaiduMap({
  onIPLocation() {
    const { point } = location.value;
    addressData.value = [point.lng, point.lat];
  },
  onPointGeocoder() {
    addressText.value = markerInfo.value.address;
    console.log('dfjkfjkj ', addressText.value);
    // homeDataReq();

    isLocationLoaded.value = true;
    onSearch();
  },
});

const onSearch = () => {
  page.value = null;
  memberList.value = [];
  isLoading.value = true;
  onGetMemberSquaresAxios(true);
};

const some = () => {
  console.log('方法有用，请勿删除');
};
const editIsMapPageFlag = (val, name?) => {
  if (val) {
    console.log(val, name);
    addressText.value = name;
    addressData.value = [val.location.lng, val.location.lat];
    // nicheStore.position = addressData.value;
    // nicheStore.positionText = addressText.value;
    // homeDataReq();
    onSearch();
  }
};

const editAddressText = (text) => {
  addressText.value = text;
};

const editAddressData = (data) => {
  addressData.value = data;
};

// watch(
//   () => store.activeAccount,
//   async (val) => {
//     if (val) {
//       onSearch();
//     }
//   },

// );

const tabsContainer = ref(null);
const taboxContainer = ref(null);
const showExpand = ref(false);
const isExpand = ref(false);

const checkOverflow = () => {
  console.log('scrollWidth', taboxContainer.value?.clientWidth, tabsContainer.value?.clientWidth);
  // if (tabsContainer.value) {
  //   console.log('scrollWidth',tabsContainer.value.scrollWidth, tabsContainer.value.clientWidth)
  //   showExpand.value = tabsContainer.value.scrollWidth > tabsContainer.value.clientWidth;
  // }
  if (tabsContainer.value && taboxContainer.value) {
    showExpand.value = taboxContainer.value?.clientWidth === tabsContainer.value?.scrollWidth;
  }
};
const latWidgetRef = ref(null);
const onScroll = (e) => {
  console.log('dddfff', latWidgetRef.value);
  latWidgetRef.value?.close();
};
onMountedOrActivated(async () => {
  // onSearch();
  // onLoadCache();
  // if(memberList.length < 1) {
  //   isLoading.value = true;
  // }
  console.log('route', route, route.name);
  await onGetListSquareIndustriesAxios();
  // onSearch();
  const observer = new ResizeObserver(checkOverflow);
  isExpand.value = false;
  if (tabsContainer.value) {
    observer.observe(tabsContainer.value);
  }
  checkOverflow();
  return () => observer.disconnect();

});

const resourceLabels = computed(() => {
  const isRegional = ['digital_platform_politics_square', 'square_politics_square'].includes(route.name);
  return {
    title: isRegional ? t('niche.qyzy') : t('niche.gch'),
    placeholder: isRegional ? '搜索区域资源名称' : '搜索广场号名称',
  };
});
</script>

<style lang="less" scoped>
@import '@renderer/views/engineer/less/common.less';
.noMore {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  &-line {
    width: 100%;
    height: 1px;
    background: var(--divider-kyy_color_divider_light, #ECEFF5);
  }
  &-text {
    flex: none;
  }
}
:deep(.address-list) {
  height: 492px !important;
  left: 0;
  right: auto !important;
}

:deep(.lat-box) {
  justify-content: flex-start !important;
  width: fit-content !important;
}

.empty {
  height: 60vh;
}
.list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.list-item {
  width: 84px;
  padding: 0 8px;
  height: 32px;
  min-width: 136px;
  display: flex;
  align-items: center;

  &:hover {
    border-radius: var(--kyy_radius_dropdown_s, 4px);
    background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
  }
}
.lactive {
  color: var(--kyy_color_dropdown_text_active, #4d5eff);

  /* kyy_fontSize_2/regular */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  border-radius: var(--kyy_radius_dropdown_s, 4px);
  background: var(--kyy_color_dropdown_bg_active, #e1eaff);
}

.filter-res {
  padding: 0;
  margin-top: 24px;

  .te {
    margin-bottom: 0;
  }

  .tit {
    line-height: 23px;
  }

  .limit {
    max-width: 250px;
  }
}

.banner {
  display: flex;
  gap: 12px;
  .tabox {
    display: flex;
    flex: 1;
    .expand {
      flex: none;
      width: 72px;
      height: 32px;
      color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.15s linear;
      .iarrow {
        font-size: 20px;
      }

      &:hover {
        transition: all 0.15s linear;
        background: var(--bg-kyy_color_bg_list_foucs, #e1eaff);
      }
    }
  }
  .tbox {
    flex: 1;
    width: 100%;
    display: flex;
  }
  .setH {
    height: auto !important;
  }
  .tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    overflow-y: hidden;
    height: 32px;

    .activeTab {
      border-radius: 8px;
      background: linear-gradient(135deg, #5efce8 0%, #736efe 100%) !important;
      color: var(--text-kyy_color_text_white, #fff) !important;
      font-weight: 600 !important;
    }
    .tab {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);
      color: var(--text-kyy_color_text_2, #516082);

      /* kyy_fontSize_1/regular */
      font-family: 'PingFang SC';
      font-size: 12px;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
      padding: 6px 12px;
      transition: all 0.15s linear;
      &:hover {
        transition: all 0.15s linear;
        border-radius: 8px;
        background: var(--bg-kyy_color_bg_list_foucs, #e1eaff);
      }
    }
  }
  .guodu {
    display: flex;
    flex: none;
    padding-top: 8px;
    .line {
      width: 1px;
      background: var(--divider-kyy_color_divider_light, #eceff5);
      height: 16px;
    }
  }

  .area {
    flex: none;
    .btnarea {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px; /* 157.143% */

      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #d5dbe4);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);

      height: 32px;
      padding-left: 12px;
      padding-right: 8px;
      transition: all 0.15s linear;
      .iarrow {
        font-size: 20px;
      }
      &:hover {
        transition: all 0.15s linear;
        color: var(--brand-kyy_color_brand_hover, #707eff);
        font-size: 14px;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        border: 1px solid var(--brand-kyy_color_brand_hover, #707eff);
        background: var(--bg-kyy_color_bgBrand_foucs, #dbdfff);
        .iarrow {
          color: var(--brand-kyy_color_brand_hover, #707eff);
        }
      }
    }
  }
}

.in-box {
  display: flex;

  .f-icon {
    display: flex;
    width: 32px;
    height: 32px;
    cursor: pointer;
    min-height: 32px;
    max-height: 32px;
    padding: 6px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-left: 8px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
    background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);

    .iconscreen {
      font-size: 20px;
      color: #828da5;
    }

    &:hover {
      border: 1px solid var(--color-button-secondary-brand-kyy-color-button-secondary-brand-border-dedault, #4d5eff) !important;
      background: var(--color-button-secondary-brand-kyy-color-button-secondray-brand-bg-default, #eaecff) !important;
      transition: all 0.25s linear;

      .iconscreen {
        color: #4d5eff;
      }
    }
  }

  .factive {
    border: 1px solid var(--color-button-secondary-brand-kyy-color-button-secondray-brand-border-active, #3e4cd1) !important;
    background: var(--color-button-secondary-brand-kyy-color-button-secondray-brand-bg-active, #dbdfff) !important;

    .iconscreen {
      color: #4d5eff;
    }
  }
}

.page {
  display: flex;
  justify-content: center;
  width: 100%;
  height: inherit;
  padding: 16px 24px;
  padding-top: 64px;
  // background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  height: 100%;
  background: url('@/assets/member/svg/square_bg.svg');
  background-position: top;
  align-items: center;
  flex-direction: column;
  justify-content: flex-start;

  .change-box {
    width: 100%;
    max-width: 1184px;
    min-width: 1088px;
    display: flex;
    justify-content: flex-end;
    position: relative;

    .logos {
      position: absolute;
      top: -48px;
      left: 24px;
      z-index: 20;
    }
  }
}

.memberLoading {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.comp {
  // padding-top: 16px;
  // padding-bottom: 16px;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.square {
  padding-bottom: 24px;
  // height: inherit;
  max-width: 1184px;
  min-width: 1088px;
  width: 100%;
  padding: 0 8px;
  background-color: #fff;
  border-radius: 16px;
  overflow-y: hidden;

  .scroll {
    width: 100%;
    // height: inherit;
    // max-height: calc(100vh - 120px);
    // min-height: calc(100vh - 120px);
    height: calc(100vh - 120px);
    // height: 500px;
    overflow-y: auto;
    padding: 0 16px;
    position: relative;
  }

  .scroll::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    // background-color: red;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  .scroll::-webkit-scrollbar-track {
    // box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    // border-radius: 10px;
    background-color: #fff;
  }

  /*定义滑块 内阴影+圆角*/
  .scroll::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #d5dbe4;
  }

  .search {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 12;
    padding: 16px 0;
    // padding-bottom: 8px;
    display: flex;
    flex-direction: column;
  }

  .search-box {
    display: flex;
    justify-content: space-between;

    .name {
      padding-left: 144px;
      color: #169993;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px; /* 144.444% */
      .count {
        color: var(--text-kyy_color_text_2, #516082);

        /* kyy_fontSize_2/regular */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
    .titleName {
      // position: relative;

      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .conbox {
    padding-bottom: 16px;
    .item {
      // flex: 1;
      width: calc((100% - 12px) / 2);
      // height: 208px;
      //height: 96px;
      border-radius: 12px;
      border: 1px solid #BEE2FF;
      // background: var(--bg-kyy_color_bg_deep, #F5F8FE);
      background: linear-gradient(180deg, rgba(227, 249, 254, 0.50) 0%, rgba(245, 250, 253, 0.50) 100%), #FFF;
      transition: none;
      padding: 12px;

      .visible {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 10;
        border-radius: 8px;
        background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.50));
        display: flex;
        align-items: center;
        justify-content: center;

        .btn {
          display: flex;
          gap: 4px;
          width: 110px;

          .iconpreciewopen {
            font-size: 20px;
          }

          .text {
            color: var(--text-kyy_color_text_white, #FFF);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
          }
        }

        .tip {
          border-radius: 8px 0px;
          background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));

          position: absolute;
          left: 0;
          top: 0;
          padding: 0 4px;
          color: var(--text-kyy_color_text_white, #FFF);
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          /* 166.667% */
        }
      }

      &:hover {

        /*.hover {
          opacity: 1;
        }*/
        border-radius: 12px;
        border-color: #4D5EFF;

        transition: all 0.15s linear;
      }

      .public {
        display: flex;
        // flex-direction: column;
        // justify-content: space-between;
        align-items: center;
        gap: 12px;


        .logo {
          width: 44px;
          height: 44px;
          // height: 48px;
          position: relative;
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          gap: 4px;

          .img {
            width: 44px;
            height: 44px;
            border-radius: 50%;

          }
          .like {
            :deep(.t-button--theme-default) {
              border: 0 !important;
            }
            :deep(.t-button--variant-outline) {
              border: 0;
              height: 20px !important;
              padding: 0 4px;
              padding-right: 8px;
              &:hover {
                border: 0px !important;
              }
            }
            :deep(.t-button__text) {
              font-size: 12px;
            }
            :deep(.icon) {
              font-size: 16px;
            }
          }

        }

        .right {

          flex: 1;
          display: flex;
          flex-direction: column;
          // justify-content: space-between;
          // height: 100%;
          gap: 4px;
          //width: calc(100% - 66px);

          .lin {
            display: flex;
            align-items: center;
            // max-width: 160px;

            .star {
              // position: absolute;
              // bottom: -2px;
              // right: -3px;
              display: flex;
              align-items: center;

              &-icon {
                font-size: 24px;
              }
            }

            .name {

              color: var(--text-kyy_color_text_1, #1A2139);
              text-align: center;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
              /* 157.143% */

            }
          }

          .type {
            border-radius: var(--kyy_radius_tag_s, 4px);
            background: var(--kyy_color_tag_bg_kyyBlue, #E4F5FE);
            padding: 0 4px;
            color: var(--kyy_color_tag_text_kyyBlue, #21ACFA);
            text-align: center;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            /* 166.667% */
            width: fit-content;
          }

          .person {
            background: var(--kyy_color_tag_bg_success, #E0F2E5);
            color: var(--kyy_color_tag_text_success, #499D60);
          }

          .bottom {
            color: var(--text-kyy_color_text_3, #828DA5);
            // text-align: center;
            // max-width: 160px;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
          }
        }

        .square-link-button{
          height: 24px;
          width: 48px;

          &:hover{
            background: #EAECFF !important;
          }
        }
      }
    }

  }

  .example-more {
    display: flex;
    align-items: center;
    justify-content: center;

    .more {
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #d5dbe4);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
      color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      padding: 4px 16px;
    }

    .noempty {
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      display: flex;
      align-items: center;
      width: 100%;
      gap: 12px;
      padding-bottom: 16px;

      .toText {
        flex: none;
      }

      .line {
        height: 1px;
        background: var(--divider-kyy_color_divider_deep, #d5dbe4);
        width: 100%;
      }
    }
  }
}
</style>
