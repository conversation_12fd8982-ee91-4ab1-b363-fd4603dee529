<template>
  <div class="editor-box">
    <div class="edit-content">
      <div class="niche-form-box form-box">
        <div v-if="alertShow" class="alert">
          <iconpark-icon name="iconinfo-b7dcijg4" class="iconerror" />
          <div class="text">{{ t("notice.sftip") }}</div>
        </div>

        <div class="form-one">
          <div class="from niche-edit-form">
            <t-form ref="form" :rules="FORM_RULES" :data="formData" :colon="false" scroll-to-first-error="smooth"
              label-align="top" @submit="onSubmit">
              <t-form-item :label="t('notice.tit')" class="t-form-item-titles" name="title">
                <!-- <div
                  ref="stackTextareaRef"
                  class="stack-textarea"
                  contenteditable="true"
                  :placeholder="t('notice.pin')"
                  plaintext-only="true"
                  :class="{'placeholder-visible': isEmpty}"
                  @input="updateContent($event)"
                  v-html="formData.title"
                >
                </div> -->
                <!-- <textarea
                  v-model="formData.title"
                  class="stack-textarea"
                  maxlength="200"
                  style="width: 416px"
                  :autosize="{ minRows: 1, maxRows: 5 }"
                  :placeholder="t('notice.pin')"
                /> -->
                <t-textarea ref="stackTextareaRef" v-model="formData.title" class="textareaPadd"
                  :class="{ pod: podHas }" :maxlength="200" autofocus
                  style="width: 416px; padding: 4px 8px; height: 32px" :autosize="{ minRows: 1, maxRows: 5 }" clearable
                  :placeholder="t('notice.pin')" />
                <t-select v-model="formData.type" style="width: 160px; margin-left: 8px">
                  <t-option :key="1" :label="t('notice.tz')" :value="1" />
                  <t-option :key="2" :label="t('notice.tg')" :value="2" />
                  <t-option :key="3" :label="t('notice.tb')" :value="3" />
                  <t-option :key="4" :label="t('notice.gg')" :value="4" />
                  <t-option :key="5" :label="t('notice.gs')" :value="5" />
                </t-select>
                <t-popup show-arrow placement="bottom-right">
                  <iconpark-icon name="iconhelp" class="icon"
                    style="font-size: 20px; color: #828da5; position: relative; top: 7px; left: 4px" />
                  <template #content>
                    <div class="tipc">
                      <div class="ite">
                        <div class="title">{{ t("notice.tz") }}</div>
                        <div class="cont">{{ t("notice.tzc") }}</div>
                      </div>
                      <div class="ite">
                        <div class="title">{{ t("notice.tg") }}</div>
                        <div class="cont">{{ t("notice.tgc") }}</div>
                      </div>
                      <div class="ite">
                        <div class="title">{{ t("notice.tb") }}</div>
                        <div class="cont">{{ t("notice.tbc") }}</div>
                      </div>
                      <div class="ite">
                        <div class="title">{{ t("notice.gg") }}</div>
                        <div class="cont">{{ t("notice.ggc") }}</div>
                      </div>
                      <div class="ite">
                        <div class="title">{{ t("notice.gs") }}</div>
                        <div class="cont">
                          {{ t("notice.gsc") }}
                        </div>
                      </div>
                    </div>
                  </template>
                </t-popup>
              </t-form-item>

              <t-form-item :label="t('notice.signature')" name="signature">
                <t-input v-model="formData.signature" :maxlength="20" show-limit-number clearable
                  :placeholder="t('notice.pin')" />
              </t-form-item>

              <t-form-item :label="t('notice.zw')" class="content" name="content" :rules="[
                  {
                    validator: contentValidator,
                    required: true,
                    trigger: 'change',
                  },
                ]">
                <Editor :editor-type="'B'" class="notice-lk-editors" ref="editorRef" type="B" root-dir="notice"
                  :options="{ toolbar: ['annex', 'link'], height: 320, placeholder: t('notice.pin') }"
                  @update="handleContentChange($event)" @img-insert="imgInsert" />
              </t-form-item>

              <FFileUpload :attrs="attrsImage" />

              <t-form-item class="channel" name="channel" :rules="[
                  {
                    validator: channelValidator,
                    required: true,
                    trigger: 'change',
                  },
                ]">
                <template #label>
                  <span style="flex: 1">{{ t('notice.fsz') }}</span>
                  <t-popup placement="top">
                    <iconpark-icon name="iconhelp" class="icon"
                      style="font-size: 20px; color: #828da5; position: relative; top: 5px;left: 4px;" />
                    <template #content>
                      <div class="fsz-tip">
                        <div class="row"> <span class="title">{{t('notice.fsztip1')}}</span> <span
                            class="con">{{t('notice.fsztip2')}}</span> </div>
                        <div class="row"> <span class="title">{{t('notice.fsztip3')}}</span> <span
                            class="con">{{t('notice.fsztip4')}}</span> </div>
                        <div class="row"> <span class="title">{{t('notice.fsztip5')}}</span> <span
                            class="con">{{t('notice.fsztip6')}}</span> </div>
                      </div>
                    </template>
                  </t-popup>
                </template>

                <channels ref="channelsRef" :type="2" :chdata="formData" @change="channelsChange" />
              </t-form-item>

              <t-form-item :label="t('notice.qtsz')" class="channel" name="set">
                <t-checkbox v-model="formData.need_confirm"></t-checkbox>
                <span>{{ t("notice.xycm") }}</span>
              </t-form-item>
            </t-form>
          </div>
        </div>
        <div style="height: 16px; width: 100%; opacity: 0">1</div>
      </div>
    </div>
    <div class="footer family btn-font-weight-600">
      <div style="width: 608px; display: flex; gap: 8px" class="not-family">
        <template v-if="props.etype !== 2">
          <t-button theme="default" style="width: 80px" @click="draftBtnRun"> {{ t("notice.ccg") }} </t-button>
          <t-button theme="default" style="min-width: 80px" @click="viewRun"> {{ t("notice.ylxg") }} </t-button>
          <t-button theme="default" :loading="isLoading && actionKey === 4" style="min-width: 80px"
            @click="timePublishRun">
            {{ t("notice.dsfb") }}
          </t-button>
          <t-button style="min-width: 80px" :loading="isLoading && actionKey === 2" @click="publishRun">
            {{ t("notice.ljfb") }}
          </t-button>
        </template>
        <template v-else>
          <t-button theme="default" style="min-width: 80px" @click="viewRun"> {{ t("notice.ylxg") }} </t-button>
          <t-button :loading="isLoading && actionKey === 4" style="min-width: 80px" @click="timePublishRun">
            {{ t("notice.bc") }}
          </t-button>
        </template>
      </div>
    </div>
  </div>

  <review ref="reviewRef" />

  <timeSet ref="timeSetRef" :mode="2" @time-res="timeRes" />
  <releaseCheck ref="releaseCheckRef" @check-succ="checkSucc" @review="viewRun" @check-cancel="checkCancel" />
</template>

<script setup lang="ts">
  import { computed, nextTick, onActivated, onMounted, reactive, ref } from "vue";
  import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import { useI18n } from "vue-i18n";
  import { useRoute, useRouter } from "vue-router";
  import _ from "lodash";
  import to from "await-to-js";
  import { configDataStore } from "@renderer/store/modules/configData";
  import uploadImage, { blobToPngFile } from "@renderer/views/square/utils/upload";
  import upload from "@renderer/views/zhixing/components/upload.vue";
  import { getAppsState } from "@renderer/api/workBench";
  import {
    authMember,
    draftGet,
    getDateil,
    noteDraftAdd,
    noteDraftPut,
    noteReleaseAdd,
    noteReleasePut,
    safeSetInfo,
  } from "@renderer/api/notice/manage";
  import { checkContentLegality } from "@renderer/api/pb/manage";
  import review from "./review.vue";
  import FFileUpload from "./FFileUpload.vue";
  import timeSet from "./timeSet.vue";
  import releaseCheck from "./releaseCheck.vue";
  import channels from "./channels.vue"; 
  import Editor from "@/components/editor/index.vue";
  import LynkerSDK from '@renderer/_jssdk';

  const { ipcRenderer } = LynkerSDK;
  const { t } = useI18n();
  const channelsRef = ref(null);
  const configData = configDataStore();
  const emits = defineEmits(["deltabItem"]);

  const channelsChange = (val) => {
    console.log("channelsChange", val);
    formData.to_square = val.to_square;
    formData.work_shop_type = val.work_shop_type;
    formData.platform_type = val.platform_type;
    formData.work_shop_receiver = val.work_shop_receiver;
    formData.work_shop_list = val.work_shop_list;
    formData.platform_list = val.platform_list;
    formData.platform_receiver = val.platform_receiver;
    form.value.clearValidate(["channel"]);
  };

  const getContentText = () => {
    let text = "";
    try {
      if (formData.content) {
        const content = JSON.parse(formData.content);
        for (const item of content) {
          if (typeof item.insert !== "object") {
            const str = item.insert.replace(/\s*/g, "");
            if (str !== "\n" && str !== "\n\n" && str.length) {
              text += item.insert;
            }
          }
          if (typeof item.insert === "object" && item.insert.image) {
            text += `[图片]`;
          }
        }
      } else {
        text = "";
      }
    } catch (err) {
      text = "";
    }
    return text;
  };

  const contentValidator = () => {
    if (!formData.content?.length) {
      return {
        message: t("notice.qsrnr"),
        required: true,
        trigger: "change",
      };
    }
    if (!getContentText().length) {
      return {
        message: t("notice.qsrnr"),
        required: true,
        trigger: "change",
      };
    }
    return { result: true, message: "", type: "success" };
  };
  const channelValidator = () => {
    if (!formData.to_square && !formData.work_shop_type && !formData.platform_type) {
      return {
        message: t("notice.qxzcn"),
        required: true,
        trigger: "change",
      };
    }
    if (formData.work_shop_type === 2 && !formData.work_shop_receiver.length) {
      return {
        message: t("notice.qxzcy"),
        required: true,
        trigger: "change",
      };
    }
    if (formData.platform_type === 2 && !formData.platform_receiver.length) {
      return {
        message: t("notice.qxzcy"),
        required: true,
        trigger: "change",
      };
    }
    return { result: true, message: "", type: "success" };
  };
  const FORM_RULES = {
    type: [{ required: true, message: t("niche.type_rq") }],
    title: [{ required: true, message: t("niche.title_rq") }],
  };
  const formData = reactive({
    type: 1,
    title: "",
    draft_id: undefined,
    reviewer: undefined,
    signature: undefined,
    file: [],
    need_confirm: false,
    timing_at: undefined,
    content: "",
    notice_id: 0,
    to_square: 0,
    work_shop_type: 0,
    work_shop_receiver: [],
    work_shop_list: [],
    platform_list: [],
    platform_type: 0,
    platform_receiver: [],
  });
  const form = ref(null);

  const closePage = () => {
    emits("deltabItem", true);
  };

  const closePageDraftRun = () => {
    if (!formData.title || !formData.title.length) {
      const myDialog = DialogPlugin({
        header: t("niche.tip"),
        theme: "info",
        body: t("notice.titem"),
        className: "dialog-classp32",
        cancelBtn: null,
        onConfirm: () => {
          myDialog.hide();
        },
      });
    } else {
      draftReq();
    }
  };

  const closeOrSaveFn = () => {
    if (havaValue.value) {
      const myDialog = DialogPlugin({
        header: t("niche.tip"),
        theme: "info",
        body: t("notice.ccgtip"),
        className: "dialog-classp32",
        confirmBtn: t("notice.bc"),
        cancelBtn: t("notice.bbc"),
        onConfirm: () => {
          myDialog.hide();
          actionKey.value = 3;
          form.value.submit({ showErrorMessage: true });
        },
        onCancel: () => {
          closePage();
          myDialog.hide();
        },
      });
    } else {
      closePage();
    }
  };

  const props = defineProps({
    activationGroupItem: {
      type: Object,
      default: null,
    },
    etype: {
      type: Number,
      default: null,
    },
  });

  defineExpose({
    closeOrSaveFn,
  });

  onActivated(() => {
    getAppAuth();
  });
  const teamId = ref(localStorage.getItem("workBenchTeamid") || "");
  const figureAuth = ref(false);
  const auth = ref({
    verify_auth: 0,
    manage_auth: 0,
    notice_super: 0,
  });
  const alertShow = ref(true);
  const getAppAuth = async () => {
    const [err, res] = await to(getAppsState(teamId.value));
    if (err) {
      return;
    }
    const { data } = res;
    if (data.data?.government || data.data?.member || data.data?.cbd || data.data?.association|| data.data?.uni) {
      figureAuth.value = true;
    } else {
      figureAuth.value = false;
    }

    safeSetInfo().then((res) => {
      if (res.data) {
        alertShow.value = res.data.data.status;
        console.log("alertShow.value", alertShow.value);
      }
    });
    authMember().then((res) => {
      if (res.data) {
        auth.value = res.data.data;
        console.log("auth.value ", auth.value);
      }
    });
  };

  const setDataRun = (data) => {
    formData.type = data.type;
    formData.title = data.title;
    formData.file = data.file;
    formData.reviewer = data.reviewer;

    formData.notice_id = data.notice_id;
    attrsImage.value.value = data.file;
    formData.signature = data.signature;
    formData.timing_at = data.timing_at;
    formData.content = data.content;
    formData.need_confirm = !!data.need_confirm;
    formData.to_square = data.to_square;
    formData.work_shop_type = data.work_shop_type;
    formData.work_shop_receiver = [];
    formData.work_shop_list = data.work_shop_list;
    formData.platform_type = data.platform_type;
    formData.platform_receiver = [];
    formData.platform_list = data.platform_list;
    formData.platform_list.map((item) => (item.cardId = item.card_id));
    formData.work_shop_list.map((item) => (item.cardId = item.card_id));
    editorRef.value.renderContent({ ops: JSON.parse(data.content) });
    formData.work_shop_receiver = data.work_shop_list.map((item) => item.cardId.replace(/\$/g, ""));
    formData.platform_receiver = data.platform_list.map((item) => item.cardId.replace(/\PT/g, ""));
    channelsRef.value.setData(formData);
    setTimeout(() => {
      form.value.clearValidate();
    }, 500);
  };
  const getDetailRun = (id) => {
    getDateil(id).then((res) => {
      if (res.data) {
        setDataRun(res.data.data);
      }
    });
  };

  const getDratfDetailRun = (id) => {
    draftGet(id).then((res) => {
      if (res.data) {
        setDataRun(res.data.data);
      }
    });
  };

  const isDraft: any = ref(0);
  const idDraft: any = ref(0);
  const podHas: any = ref(true);
  const initRun = async () => {
    console.log("initRun", route);
    console.log("route", route.query.id);
    const id: any = route.query?.id || 0;
    formData.notice_id = id;
    isDraft.value = route.query?.isDraft || 0;
    idDraft.value = route.query?.idDraft || 0;
    formData.draft_id = route.query?.idDraft || undefined;
    channelsRef.value.setData(formData);
    if (id) {
      await getDetailRun(id);
    } else if (idDraft.value) {
      await getDratfDetailRun(idDraft.value);
    }
    if (isDraft.value) {
      formData.notice_id = 0;
    }
    console.log("initRundom.setTimeout()");
    setTimeout(() => {
      const dom: any = document.querySelector(".t-textarea__inner");
      if (dom) {
        console.log("initRundom.focus()");
        dom.focus();
        podHas.value = false;
        setTimeout(() => {
          dom.blur();
        }, 10);
      }
    }, 200);
  };

  const route = useRoute();

  onMounted(() => {
    initRun();
    getAppAuth();
  });

  const loading = ref(false);
  const noteReleaseAddReq = (timing_id?) => {
    const params = dataHandle();
    params.id = 0;
    params.timing_id = timing_id;
    params.notice_id = 0;
    console.log("params", params);
    noteReleaseAdd(params).then((res: any) => {
      loading.value = false;
      if (res.data?.code === 0) {
        subSucc();
      }
    });
  };

  const dataHandle = () => {
    const params: any = { ...formData };
    params.file = attrsImage.value.value;
    params.need_confirm = formData.need_confirm ? 1 : 0;
    params.value = undefined;
    params.reviewer = formData.reviewer?.staff_id;
    return params;
  };

  const noteReleasePutReq = () => {
    const params = dataHandle();
    noteReleasePut(params).then((res: any) => {
      loading.value = false;
      if (res.data?.code === 0) {
        subSucc(t("notice.edsucc2"));
      }
    });
  };
  const subSucc = (msg?) => {
    let tip = t("notice.fbcg");
    if (alertShow.value && auth.value.verify_auth !== 1) {
      tip = t("notice.fbap");
    }
    MessagePlugin.success(msg || tip);
    closePage();
  };

  const draftSucc = () => {
    MessagePlugin.success(t("notice.cgcg"));
    // MessagePlugin.success("已保存至草稿箱");
    setTimeout(() => {
      closePage();
    }, 200);
  };

  const draftReq = () => {
    const params = dataHandle();
    if (isDraft.value) {
      const temp = _.cloneDeep(params);
      temp.draft_id = idDraft.value;
      noteDraftPut(params).then((res: any) => {
        if (res.data.code === 0) {
          draftSucc();
        }
      });
    } else {
      params.id = undefined;
      noteDraftAdd(params).then((res: any) => {
        if (res.data.code === 0) {
          draftSucc();
        }
      });
    }
  };

  const draftBtnRun = () => {
    draftRun();
  };

  const draftRun = () => {
    const myDialog = DialogPlugin({
      header: t("niche.tip"),
      theme: "info",
      body: t("notice.cgbct"),
      className: "dialog-classp32",
      confirmBtn: t("notice.bc"),
      cancelBtn: t("notice.bbc"),
      onConfirm: () => {
        myDialog.hide();
        actionKey.value = 3;
        form.value.submit({ showErrorMessage: true });
      },
      onCancel: () => {
        closePage();
        myDialog.hide();
      },
    });
  };

  const havaValue = computed(() => formData.title);

  const viewTip = () => {
    // MessagePlugin.warning("请填写完必填项后再预览");
    const myDialog = DialogPlugin({
      header: t("niche.tip"),
      theme: "info",
      body: t("notice.yltip"),
      className: "dialog-classp32",
      confirmBtn: t("notice.wzdl"),
      cancelBtn: null,
      onConfirm: () => {
        myDialog.hide();
      },
    });
  };

  const reviewRef = ref(null);
  const actionKey = ref(1);
  const viewRun = () => {
    actionKey.value = 1;
    form.value.submit({ showErrorMessage: true });
  };
  const clearValidateRun = (id) => {
    form.value.clearValidate([id]);
  };

  const isLoading = ref(false);
  const shumei = async () => {
    isLoading.value = true;
    if (actionKey.value === 1 || actionKey.value === 3) {
      prepare();
      return;
    }
    try {
      if (formData.value) {
        formData.value = {};
        console.log("formData2", formData);
      }
      const params = dataHandle();
      const { data } = await checkContentLegality(JSON.stringify(params));
      if (data.code !== 0) {
        isLoading.value = false;
        return;
      }
      prepare();
      isLoading.value = false;
    } catch (error) {
      console.log(error, "error");
      isLoading.value = false;
      const { response } = error;
      if (response.status === 418) return;
      MessagePlugin.warning(response.data.message);
    }
  };

  const prepare = () => {
    if (actionKey.value === 1) {
      const data: any = dataHandle();
      reviewRef.value.reviewOpen(data);
    } else if (actionKey.value === 2) {
      // 立即发布
      checkStart();
    } else if (actionKey.value === 3) {
      // 草稿
      draftReq();
    } else {
      // 定时发布
      if (props.etype === 2) {
        checkStart();
      } else {
        timePublishOkRun();
      }
    }
  };

  const onSubmit = ({ validateResult, firstError }) => {
    console.log("formData", formData);
    console.log("attrsImage", attrsImage);
    if (validateResult === true) {
      shumei();
    } else {
      console.log("Validate Errors: ", firstError, validateResult);
      // const editContent = document.querySelector(".edit-content");
      // const firstErrorDom = editContent.querySelector(".t-is-error");
      // firstErrorDom.scrollIntoView({ behavior: "smooth", block: "start" });
      if (actionKey.value === 1) {
        viewTip();
      } else {
        MessagePlugin.warning(t("notice.btxtip"));
      }
    }
  };
  const attrsImage = ref({
    id: "productImage",
    name: t("notice.fj"),
    value: [],
    editable: true,
    required: false,
    max: 10,
  });

  const formStep = ref(1);

  const publishReqRun = () => {
    console.log("publishReqRun");
    if (props.etype === 2) {
      // 1新建2编辑3草稿4再次
      if (actionKey.value === 2) {
        // 定时公告但是点的立即发布，走新建流程
        noteReleaseAddReq(formData.notice_id);
      } else {
        noteReleasePutReq();
      }
    } else {
      noteReleaseAddReq();
    }
  };

  const checkExpiration = (expiredAt) => {
    const now = new Date();
    const expirationDate = new Date(expiredAt);
    return now > expirationDate;
  };

  const publishRun = async () => {
    actionKey.value = 2;
    form.value.submit({ showErrorMessage: true });
    // const myDialog = DialogPlugin({
    //     header: t("niche.tip"),
    //     theme: "info",
    //     body: "确认是否立即发布",
    //     className: "dialog-classp32",
    //     confirmBtn: "发布",
    //     onConfirm: () => {
    //       myDialog.hide();
    //       publishReqRun();
    //     },
    //     onCancel: () => {
    //       myDialog.hide();
    //     },
    //   });
  };
  const publishRunDebounce = _.debounce(publishRun, 500, {
    leading: true, // 延长开始后调用
    trailing: false, // 延长结束前调用
  });

  const titleblur = () => {
    const regex = /([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2694-\u2697]|\uD83E[\uDD10-\uDD5D])/g;
    formData.title = formData.title.replace(regex, "");
  };
  const contentblur = () => {
    const regex = /([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2694-\u2697]|\uD83E[\uDD10-\uDD5D])/g;
    formData.content = formData.content.replace(regex, "");
  };

  // 正文内容（富文本）
  const noteData = ref({
    content: {
      images: [],
    },
    user_id: "",
    openid: "",
  });

  /**
   * 处理富文本中正文内容
   */
  const handleContentChange = (contents) => {
    const content = {
      imageUrl: "",
      description: "",
      images: [],
      attachments: [],
      delta: "",
    };
    /**
     * 处理content里的数据
     * description： 所有文本信息加起来
     * imageUrl：第一张图片
     * images：所有的图片集合
     * attachments：所有的文件集合
     * delta：富文本内容
     */
    const delta = [];
    // 附件大小
    let size = 0;
    contents.forEach((v) => {
      let deltaItem = _.cloneDeepWith(v);
      if (typeof v.insert === "string") {
        content.description += v.insert.trim();
      }
      if (v.insert?.image) {
        !content.imageUrl && (content.imageUrl = v.insert.image);
        const imgItem = noteData.value.content.images.find((img) => img.url === v.insert.image);
        content.images.push(imgItem);
        imgItem?.size && (size += imgItem.size);
      }
      if (v.insert?.custom) {
        size += v.insert.custom.size;
        content.attachments.push(v.insert.custom);
        const atta = { attachment: JSON.stringify(v.insert.custom) };
        const custom = { custom: JSON.stringify(atta) };
        deltaItem.insert = custom;
      }
      delta.push(deltaItem);
    });
    content.delta = JSON.stringify(delta);
    if (content.delta === '[{"insert":"\\n"}]') {
      formData.content = "";
    } else {
      formData.content = content.delta;
    }
  };
  const editorRef = ref(null);

  // 上传图片
  const imgInsert = (val) => {
    val.forEach((img) => {
      const imgObj = {
        name: img.name,
        size: img.size,
        type: img.name?.substring(img.name.lastIndexOf(".") + 1),
        url: img.url,
      };
      noteData.value.content.images.push(imgObj);
    });
  };

  const onPreviewImage = (imageData) => {
    const temp = imageData.images.map((item) => ({
      url: item,
      imgIndex: imageData.index,
    }));
    ipcRenderer.invoke("view-img", JSON.stringify(temp));
  };

  const timeSetRef = ref(null);

  const addFiveMinutesToStringInBeijingTime = () => {
    const now = new Date();
    now.setMinutes(now.getMinutes() + 5);
    // 设置为东八区时间（北京）
    const options = {
      timeZone: "Asia/Shanghai",
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false, // 使用24小时制
    };
    // 使用Intl.DateTimeFormat进行格式化，注意这个方法在某些旧浏览器中可能不支持
    const formatter = new Intl.DateTimeFormat("zh-CN", options);
    const formattedDate = formatter.format(now);

    return formattedDate.replace(/\//g, "-"); // 将可能出现的斜杠替换为短横线，以符合YYYY-MM-DD格式
  };

  const timePublishOkRun = () => {
    if (!formData.timing_at) {
      formData.timing_at = addFiveMinutesToStringInBeijingTime();
    }
    console.log("formData.timing_at", formData.timing_at);
    timeSetRef.value.chanTimeOpen({ timing_at: formData.timing_at });
  };

  const timePublishRun = () => {
    actionKey.value = 4;
    form.value.submit({ showErrorMessage: true });
  };
  const timeRes = (val) => {
    formData.timing_at = val;
    checkStart();
    // publishRun();
  };

  const publishDirectly = (type) => {
    const myDialog = DialogPlugin({
      header: t("niche.tip"),
      theme: "info",
      body: type === 1 ? t("notice.directly1") : t("notice.directly2"),
      className: "dialog-classp32",
      confirmBtn: t("notice.qd"),
      onConfirm: () => {
        myDialog.hide();
        publishReqRun();
      },
      onCancel: () => {
        myDialog.hide();
      },
    });
  }

  const releaseCheckRef = ref(null);
  const checkStart = () => {
    safeSetInfo().then((res) => {
      if (res.data) {
        alertShow.value = res.data.data.status;
        authMember().then((res) => {
          if (res.data) {
            auth.value = res.data.data;
            // let type = 1;
            if (alertShow.value) {
              if (auth.value.verify_auth === 1) {
                publishDirectly(1)
              } else {
                releaseCheckRef.value.checkOpen(2, formData.timing_at);
              }
              // type = auth.value.verify_auth === 1 ? 1 : 2;
            } else {
              // type = 1;
              publishDirectly(2)
            }
            // console.log("auth.value.verify_auth", auth.value.verify_auth);
            // console.log("detail.formDatavalue", formData);
          }
        });
      }
    });
  };
  const checkCancel = (val) => {
    // formData.timing_at = undefined;
  };
  const checkSucc = (val) => {
    formData.reviewer = val;
    console.log("checkSucc", val);
    publishReqRun();
  };
  const stackTextareaRef = ref(null);
  const isEmpty = computed(() => formData.title.trim() === "");
  const updateContent = (e) => {
    const val = e.target.innerHTML;
    formData.title = val;
  };
</script>

<style lang="less" scoped>
  .editor-box {
    height: 100%;
    width: 100%;

    .edit-content {
      height: calc(100vh - 100px);
      overflow-y: auto;
      width: 100%;
      padding: 16px 0px;
      display: flex;
      justify-content: center;

      .form-box {
        width: 608px;

        .alert {
          display: flex;
          padding: 8px 24px;
          justify-content: center;
          align-items: center;
          gap: 8px;
          align-self: stretch;
          border-radius: 8px;
          background: var(--kyy_color_alert_bg_bule, #eaecff);
          margin-bottom: 16px;

          .iconerror {
            font-size: 20px;
          }

          .iconc {
            cursor: pointer;
          }

          .text {
            color: var(--kyy_color_alert_text, #1a2139);
            width: 504px;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
          }
        }

        .step-header {
          margin: 16px 0;
          display: flex;

          .one {
            width: 304px;
            position: relative;

            // .conbg{
            //   background-color: #fff;
            // }
            .con {
              position: absolute;
              top: 0px;
              padding: 12px 24px;
              width: 100%;
              display: flex;
              gap: 12px;

              .index {
                display: flex;
                text-align: center;
                margin-top: 2px;
                font-family: "PingFang SC";
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px;
                /* 150% */
                width: 40px;
                height: 40px;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 10px;
                border-radius: 20px;
                border: 2px solid var(--border-kyy_color_border_default, #d5dbe4);
                color: var(--text-kyy_color_text_3, #828da5);
              }

              .index-active {
                border: 2px solid var(--border-kyy_color_border_white, #fff);
                color: var(--text-kyy_color_text_white, #fff);
              }

              .title {
                font-family: "PingFang SC";
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                color: var(--text-kyy_color_text_3, #828da5);
              }

              .title-active {
                color: var(--text-kyy_color_text_white, #fff);
              }
            }
          }
        }

        .form-one {
          .title-tag {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;

            .tag {
              width: 3px;
              height: 16px;
              border-radius: 8px;
              background: var(--brand-kyy_color_brand_default, #4d5eff);
            }

            .title-text {
              color: var(--text-kyy_color_text_1, #1a2139);
              font-family: "PingFang SC";
              font-size: 16px;
              font-style: normal;
              font-weight: 600;
              line-height: 24px;
              /* 150% */
            }
          }

          .price-input {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-right: 24px;
          }

          .quantity-box {
            display: flex;
            flex-direction: column;

            .quantity-top {
              display: flex;
              gap: 8px;
            }

            .quantity-btm {
              display: flex;
              justify-content: end;
              // height: 46px;
              margin-top: 8px;

              .hoit {
                width: 300px;
                display: flex;
                gap: 8px;

                .item {
                  cursor: pointer;
                  display: flex;
                  height: 32px;
                  padding: 0px 16px;
                  justify-content: center;
                  align-items: center;
                  border-radius: 100px;
                  border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);
                  background: var(--bg-kyy_color_bg_light, #fff);
                  color: var(--text-kyy_color_text_2, #516082);
                  text-align: center;

                  /* kyy_fontSize_2/regular */
                  font-family: "PingFang SC";
                  font-size: 14px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 22px;
                  /* 157.143% */
                }
              }
            }
          }
        }
      }
    }

    .edit-content::-webkit-scrollbar {
      width: 0px;
    }

    .footer {
      position: fixed;
      bottom: 0;
      display: flex;
      height: 64px;
      padding: 16px 0;
      align-items: center;
      gap: 8px;
      align-self: stretch;
      background: var(--bg-kyy_color_bg_light, #fff);
      box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.08);
      width: 100vw;
      justify-content: center;

      .mock-abtn:hover {
        border-radius: 4px;
        background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
      }

      .mock-abtn {
        display: flex;
        width: 64px;
        height: 32px;
        min-height: 32px;
        max-height: 32px;
        justify-content: center;
        align-items: center;
        padding: 4px;
      }

      .def {
        color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        cursor: pointer;
        line-height: 22px;
        /* 157.143% */
      }

      .disabt {
        color: var(--color-button_text_brand-kyy_color_button_text_brand_font_disabled, #c9cfff);
        text-align: center;
        cursor: no-drop;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }
    }
  }

  .iconorientation {
    font-size: 20px;
    margin-right: 4px;
  }

  .map-container {
    position: relative;
    display: flex;
    width: 100%;
    height: 228px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    background: var(--bg-kyy_color_bg_light, #fff);

    .position-map {
      display: flex;
      height: 64px;
      padding: 8px 12px;
      align-items: center;
      justify-content: space-between;
      gap: 16px;
      align-self: stretch;

      .position-map-left-icon {
        display: flex;
        height: 22px;
      }

      .position-map-left {
        overflow: hidden;
        color: var(--text-kyy_color_text_2, #516082);
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .address_name {
          width: 500px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .detail-address {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        align-self: stretch;
        overflow: hidden;
        color: var(--text-kyy_color_text_3, #828da5);
        text-overflow: ellipsis;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        overflow: hidden;
        text-overflow: ellipsis;
        width: 500px;
      }
    }
  }

  .el-vue-amap-container {
    height: 164px;
    width: 100%;
  }

  .el-vue-amap-container .el-vue-amap {
    border-bottom-right-radius: 8px;
  }

  :deep(.t-cascader__item:hover:not(.t-is-expanded):not(.t-is-disabled)) {
    border-radius: 4px;
    background: var(--select-kyy_color_select_cascade_item_bg_hover, #f3f6fa) !important;
  }

  .util-box {
    display: flex;
    // width: 300px;
    // max-height: 362px;
    // min-height: 260px;
    padding: 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    flex-shrink: 0;
    border-radius: var(--addressSelector-kyy-radius_AddressSelector_option, 8px);
    background: var(--addressSelector-kyy_color_addressSelector_bg_default, #fff);

    .util-class::-webkit-scrollbar {
      width: 0px;
      height: 1px;
    }

    .util-class {
      display: flex;
      align-items: flex-start;
      gap: 24px;
      width: 284px;
      overflow-x: auto;
      height: 36px;
      align-self: stretch;
      border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
      padding-bottom: 0;

      .cla-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: -3px;
        height: 36px;
        cursor: pointer;

        .text {
          color: var(--text-kyy_color_text_1, #1a2139);
          text-align: center;
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
          /* 150% */
        }

        .text-act {
          color: var(--brand-kyy_color_brand_default, #4d5eff);

          /* kyy_fontSize_3/bold */
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px;
          /* 150% */
        }

        .dot {
          width: 16px;
          height: 3px;
          border-radius: 1.5px;
          margin-top: 8px;
          background: var(--brand-kyy_color_brand_default, #4d5eff);
        }
      }
    }

    .util-list {
      .u-item {
        color: var(--addressSelector-kyy_color_addressSelector_option_text_hover, #1a2139);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 142.857% */
        display: flex;
        width: 274px;
        height: 36px;
        min-height: 36px;
        max-height: 36px;
        padding: 0px 12px;
        align-items: center;
        gap: 12px;
        color: var(--addressSelector-kyy_color_addressSelector_option_text_hover, #1a2139);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 142.857% */
        border-radius: var(--addressSelector-kyy-radius_AddressSelector, 4px);
        cursor: pointer;
      }

      .uact {
        color: var(--addressSelector-kyy_color_addressSelector_option_text_active, #4d5eff);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 142.857% */
        background: var(--addressSelector-kyy_color_addressSelector_bg_active, #e1eaff);
      }

      .u-item:hover {
        background: var(--addressSelector-kyy_color_addressSelector_bg_active, #e1eaff);
      }
    }
  }

  :deep(.t-textarea) {
    position: relative;
  }

  :deep(.content .t-textarea__inner) {
    padding-bottom: 20px;
  }

  :deep(.pod .t-textarea__inner) {
    height: 32px !important;
  }

  .textareaPadd {
    width: 416px;
  }

  :deep(.textareaPadd .t-textarea__inner) {
    min-height: 32px !important;
    height: 32px;
    border-color: var(--input-kyy_color_input_border_default, #d5dbe4);
  }

  :deep(.textareaPadd .t-textarea__inner) {
    // padding-right: 40px;
  }

  :deep(.textareaPadd .t-textarea__info_wrapper) {
    display: none;
  }

  :deep(.t-form-item-titles .t-form__controls-content) {
    align-items: start;
  }

  :deep(.title .t-textarea__inner) {
    padding-right: 40px;
  }

  :deep(.title .t-textarea__info_wrapper) {
    position: absolute;
    right: 10px;
    bottom: 7px;
  }

  :deep(.content .t-textarea__info_wrapper) {
    position: absolute;
    right: 4px;
    bottom: 1px;
    width: 99%;
    background: #fff;
  }

  :deep(.t-textarea__limit) {
    color: var(--text-kyy_color_text_5, #acb3c0);
    text-align: right;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
  }

  .step-tables {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;

    .lable-se {
      display: flex;
      gap: 4px;
      color: var(--warning-kyy_color_warning_default, #fc7c14);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      align-items: center;
    }

    .table-box {
      width: 100%;

      .table-header {
        display: flex;
        padding-bottom: 12px;
        align-items: center;
        gap: 12px;
        align-self: stretch;

        .table-icon {
          font-size: 20px;
          color: #828da5;
          cursor: pointer;
        }

        .left {
          display: flex;
          align-items: center;
          gap: 4px;
          flex: 1 0 0;
          color: var(--checkbox-kyy_color_checkbox_text_default, #1a2139);

          /* kyy_fontSize_3/bold */
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px;
          /* 150% */
        }

        .right {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 4px;

          .a-icon {
            color: #4d5eff;
          }
        }
      }

      .table-content {
        .self-tag {
          display: flex;
          height: 20px;
          width: 20px;
          padding: 2px 4px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: var(--kyy_radius_tag_s, 4px);
          background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
          color: #21acfa;
          text-align: center;
          font-family: PingFang SC;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          /* 166.667% */
          // float: left;
          margin-right: 4px;
        }

        .related-box {
          display: flex;
          align-items: center;
          // gap: 8px;
          flex-shrink: 0;
          align-self: stretch;

          .text {
            overflow: hidden;
            color: var(--checkbox-kyy_color_checkbox_text_active, #1a2139);
            text-overflow: ellipsis;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            width: 100%;
            height: 22px;
            white-space: nowrap;
          }
        }
      }

      .table-check {
        display: flex;
        padding: 12px;
        align-items: center;
        gap: 8px;
        flex: 1 0 0;
        align-self: stretch;
        border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
        background: var(--kyy_color_table_bg_default, #fff);
      }
    }
  }

  :deep(.t-tabs__bar.t-is-top) {
    width: 16px !important;
    //   // height: 3px!important;
    //   // margin: 0 23px!important;
    //   border-radius: 1.5px!important;
    //   background:#4D5EFF !important;
  }

  :deep(.t-input__limit-number) {
    color: var(--text-kyy_color_text_5, #acb3c0);
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  :deep(.unitlabel .t-form__label) {
    opacity: 0;
  }

  :deep(.quantitylabel) {
    margin-right: 0;
  }

  :deep(.t-select-option:not(.t-is-disabled):not(.t-is-selected):hover) {
    border-radius: var(--addressSelector-kyy-radius_AddressSelector, 4px);
    background: var(--addressSelector-kyy_color_addressSelector_bg_hover, #f3f6fa);
  }

  .bbr {
    border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  }

  .type-tag {
    margin-right: 4px;
  }

  .tableDisabled .type-tag {
    opacity: 0.6;
  }

  .tableDisabled .self-tag {
    background: #f0f9fe !important;
    color: var(--kyy_blue-kyy_color_kyyBlue_disabled, #a0dbfd) !important;
  }

  .t-select__dropdown .t-tabs__btn--right {
    border-left: none;
    background: #fff;
    width: 28px;
  }

  .t-select__dropdown .t-tabs__btn--left {
    border-right: none;
    background: #fff;
    width: 28px;
  }

  .unit-saerch-pan {}

  .ckdom {
    // display: none;
  }

  .type-re {
    display: flex;
    padding: 12px 0px;
    align-items: center;
    align-self: stretch;
    gap: 12px;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4);

    .mock-type-btn-a {
      display: flex;
      height: 32px;
      padding: 0px 16px;
      justify-content: center;
      align-items: center;
      border-radius: 100px;
      border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
      background: var(--bg-kyy_color_bg_light, #fff);
      color: var(--brand-kyy_color_brand_default, #4d5eff);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      cursor: pointer;
    }

    .mock-type-btn {
      display: flex;
      height: 32px;
      padding: 0px 16px;
      justify-content: center;
      align-items: center;
      color: var(--text-kyy_color_text_2, #516082);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      border-radius: 100px;
      border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);
      background: var(--bg-kyy_color_bg_light, #fff);
      cursor: pointer;
    }

    .mock-type-btn:hover {
      border: 1px solid var(--lingke-brand-hover, #707eff) !important;
      background: var(--lingke-brand-12, rgba(76, 94, 255, 0.12)) !important;
      color: var(--color-button-border-kyy-color-button-border-text-hover, #707eff) !important;
    }
  }

  .tip-box {
    display: flex;
    align-items: center;
    margin-left: 8px;

    .icon {
      width: 20px;
      height: 20px;
    }

    .tip-text {
      overflow: hidden;
      color: var(--warning-kyy_color_warning_default, #fc7c14);
      text-overflow: ellipsis;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      margin-left: 4px;
      margin-right: 12px;
    }

    .open-btn {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0px 12px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4d5eff);
      background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #eaecff);
      color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4d5eff);
      cursor: pointer;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .notice-lk-editors {
    width: 100%;
    /* min-height: 270px !important; */
    border: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4) !important;
    border-radius: 4px;

    .ql-container {
      border-radius: 4px;
      border-top: none !important;
      // padding:0 12px;
      // text-indent: 12px;
    }
  }

  .tipc {
    display: flex;
    width: 328px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    flex-shrink: 0;
    align-self: stretch;
    background-color: #fff;
    padding: 8px;

    .ite {
      display: flex;
      gap: 2px;

      .title {
        color: var(--text-kyy_color_text_1, #1a2139);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        /* 157.143% */
        width: 28px;
      }

      .cont {
        color: #828da5;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        width: 274px;
      }
    }
  }

  .stack-textarea {
    /* 设置初始高度为32px */
    min-height: 32px;
    /* 设置最大高度，超过这个高度会出现滚动条 */
    max-height: 160px;
    /* 5行 * 32px */
    /* 内容超出后显示垂直滚动条 */
    overflow-y: auto;
    /* 设置圆角为4px */
    border-radius: 4px;
    /* 设置边框颜色 */
    border: 1px solid #d5dbe4;
    /* 可以添加一些内边距，使文本不会贴着边框 */
    padding: 4px 8px;
    /* 其他样式，如字体大小等，根据需要添加 */
    font-size: 16px;
    /* 其他样式... */
    width: 416px;
    resize: none;
  }

  .stack-textarea:hover {
    border: 1px solid var(--input-kyy-color-input-border-hover, #707eff) !important;
  }

  .stack-textarea:focus-visible {
    border: 1px solid var(--input-kyy-color-input-border-hover, #707eff) !important;
  }

  textarea[class="stack-textarea"]::-webkit-input-placeholder {
    overflow: hidden;
    color: var(--input-kyy_color_input_text_default, #acb3c0);
    text-overflow: ellipsis;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .stack-textarea:placeholder {
    overflow: hidden;
    color: var(--input-kyy_color_input_text_default, #acb3c0);
    text-overflow: ellipsis;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .placeholder-visible::before {
    content: attr(placeholder);
    display: block;
    color: #acb3c0;
    /* Placeholder颜色 */
    pointer-events: none;
    /* 防止点击事件触发 */
    overflow: hidden;
    color: var(--input-kyy_color_input_text_default, #acb3c0);
    text-overflow: ellipsis;
    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .fsz-tip {
    display: flex;
    flex-direction: column;
    width: 360px;
    padding: 8px;
    align-items: flex-start;
    gap: 16px;
    border-radius: 8px;

    .row {
      display: flex;
      gap: 8px;

      .title {
        color: var(--text-kyy_color_text_1, #1A2139);

        /* kyy_fontSize_2/bold */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        /* 157.143% */
        min-width: 56px;
      }

      .con {
        color: #828DA5;

        /* kyy_fontSize_2/regular */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }
    }
  }
</style>