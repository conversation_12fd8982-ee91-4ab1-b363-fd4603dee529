 
export const treeJson =  {
  "id": "Modeling",
  "title": "集团总部",
  "children": [
    {
      "id": "Classification",
      "title": "分类决策中心",
      "children": [
        // 原有节点保持不变...
        {
          "id": "Decision trees",
          "title": "决策树研究部",
          "children": [  // 新增三级节点
            {
              "id": "CART",
              "title": "分类回归树委员会"
            },
            {
              "id": "ID3",
              "title": "迭代决策算法组",
              "children": [  // 新增四级节点
                {
                  "id": "Entropy",
                  "title": "信息熵研究办公室"
                },
                {
                  "id": "Gain",
                  "title": "信息增益计算组"
                }
              ]
            }
          ]
        },
        // 新增平行节点
        {
          "id": "Ensemble Learning",
          "title": "集成学习事业部",
          "children": [
            {
              "id": "Random Forest",
              "title": "随机森林算法中心"
            }
          ]
        }
      ]
    },
    // 新增顶级节点
    {
      "id": "Deep Learning",
      "title": "深度学习研究院",
      "children": [
        {
          "id": "CNN",
          "title": "卷积神经网络实验室"
        },
        {
          "id": "RNN",
          "title": "循环神经网络项目组"
        }
      ]
    }
    // ... 其他原有节点保持不变并添加类似扩展 ...
  ]
}
