<template>
  <div class="page-content">
    <template v-for="(item, index) in dataList" :key="index">
      <CategoryTitle :count="count" :index="index" />
      <div class="card-item" @click="itemClick(item)">
        <KyyAvatar
          class="avatar"
          avatar-size="40px"
          shape="circle"
          :image-url="item.square.avatar"
          :user-name="item.square.name"
          @click.stop="goHomePage(item.square)"
        />

        <div class="main">
          <div class="name-wrap">
            <div class="name line-1" @click.stop="goHomePage(item.square)">
              {{ item.square.remark || item.square.name }}
            </div>
            <div class="time">{{ item.createTime }}</div>
          </div>

          <!-- TODO -->
          <div v-if="item.res.comment" class="desc line-1">
            <div class="v-line" />
            <template v-if="item.res.comment.deleted">{{ $t('square.post.commentDeleted') }}</template>
            <template v-else>
              <div class="name line-1">{{ item.res.comment.squareName }}</div>
              <div class="line-1">{{ item.res.comment.content }}</div>
            </template>
          </div>

          <div class="desc">{{ item.resourceTypeText }}</div>
          <!--评论-->
          <template v-if="item.res.resourceType === ResourceType.Comment">
            <div v-if="item.res.comment?.deleted" class="content">{{ $t('square.post.commentDeleted') }}</div>
            <template v-if="item.res.resourceType === 'TOP_COMMENT'">
              <div v-if="item.res.comment.content" class="content" v-html="item.res.comment.content" />
            </template>
            <div v-if="item.res.resourceType === 'REPLY'" class="desc line-1">
              <div class="v-line" />
              <template v-if="item.res.comment.deleted">{{ $t('square.post.commentDeleted') }}</template>
              <template v-else>
                <div class="name line-1">{{ item.square.name }}</div>：
                <div class="line-1">{{ item.res.comment.content }}</div>
              </template>
            </div>
          </template>
        </div>

        <!--动态-->
        <template v-if="showPost(item)">
          <div v-if="item.res.post.firstPicture || item.videoUrl" class="img-wrap">
            <t-image :src="getOSSImageResize(item.res.post.firstPicture) || item.videoUrl" fit="cover" class="img" />
            <t-icon
              v-if="item.videoUrl"
              name="play-circle"
              size="30"
              class="play-icon"
            />
          </div>

          <!--平台风采 -->
          <div v-else-if="item.res.post.postType === PostType.Fengcai" class="img-wrap fcbox">
            <t-image
              :src="item.res?.post.fengcai?.fields?.img"
              fit="cover"
              class="img"
              lazy
            />
            <div class="fc-tag">
              <iconpark-icon name="icon16elegance" class="icon text-20" />
            </div>
          </div>

          <!--党建 -->
          <div v-else-if="item.res.post.postType === PostType.PartyBuilding" class="img-wrap fcbox">
            <t-image
              :src="item.res?.post.partyBuilding?.fields?.img"
              fit="cover"
              class="img"
              lazy
            />
            <div class="fc-tag">
              <iconpark-icon name="icon-party" class="icon text-20" />
            </div>
          </div>

          <!--组织介绍 -->
          <div v-else-if="item.res.post.postType === PostType.TeamIntro" class="img-wrap fcbox">
            <t-image
              v-if="item.res?.post.teamIntro?.fields?.img"
              :src="item.res?.post.teamIntro?.fields?.img"
              fit="cover"
              class="img"
              lazy
            />
            <iconpark-icon v-else name="icon-logo" class="cl-logo" />

            <div class="fc-tag">
              <iconpark-icon name="icon-about-us" class="icon text-20" />
            </div>
          </div>

          <!--荣誉 -->
          <div v-else-if="item.res.post.postType === PostType.TeamHonorRoll" class="img-wrap fcbox">
            <t-image
              :src="item.res?.post.teamHonorRoll?.fields?.img"
              fit="cover"
              class="img"
              lazy
            />
            <div class="fc-tag">
              <iconpark-icon name="icon-about-us" class="icon text-20" />
            </div>
          </div>

          <!-- 历程 -->
          <div v-else-if="item.res.post.postType === PostType.TeamHistory" class="img-wrap fcbox">
            <t-image
              v-if="item.res?.post.teamHistory?.fields?.img"
              :src="item.res?.post.teamHistory?.fields?.img"
              fit="cover"
              class="img"
              lazy
            />
            <iconpark-icon v-else name="icon-logo" class="cl-logo" />
            <div class="fc-tag">
              <iconpark-icon name="icon-about-us" class="icon text-20" />
            </div>
          </div>

          <div v-else-if="!item.res.post.deleted" class="text-wrap">
            <div class="inner">
              <template v-if="item.res.post.postType === PostType.AlbumNode">
                <template v-if="item.res.post.text">拾光相册:</template>
                <template v-else>[拾光相册]</template>
              </template>

              <iconpark-icon
                v-if="item.res.post.postType === PostType.Article"
                name="iconlink"
                class="text-16! color-text-3"
                style="vertical-align: -3px"
              />
              {{ item.res.post.text }}
              <template v-if="item.res.post.postType === PostType.Forward && !item.res.post.text">
                {{
                  item.res.post.forwardPostSquareName }}:
              </template>
            </div>
          </div>
        </template>

        <!--时光相册-->
        <template v-if="showAlbumNode(item)">
          <div
            v-if="item.res.albumNode?.images?.length || item.res.albumNode?.cover"
            class="img-wrap album"
            @click="goAlbum(item)"
          >
            <template v-if="item.res.albumNode?.nodeType === 1">
              <t-image
                v-for="(img, idx) in item.res.albumNode?.images.slice(0, 3)"
                :key="idx"
                :src="getOSSImageResize(img.url)"
                fit="cover"
                class="img"
                :style="`z-index: ${idx + 1};right: ${idx * 6}px;top: ${idx * 6}px`"
              />
            </template>
            <template v-else>
              <t-image :src="getOSSImageResize(item.res.albumNode?.cover)" fit="cover" class="img" />
              <iconpark-icon name="zhiboanniu" class="live-icon" />
            </template>
          </div>
        </template>

        <template v-if="showAlbumNodeImage(item)">
          <div v-if="item.res.albumNodeImage.url" class="img-wrap album" @click="goAlbum(item)">
            <t-image :src="getOSSImageResize(item.res.albumNodeImage.url)" fit="cover" class="img" />
          </div>
        </template>
      </div>

      <!--<t-divider class="m-0" />-->
    </template>
  </div>

  <PostDetail
    v-if="detailVisible"
    :id="detailId"
    v-model="detailVisible"
    :include-visibility-options="store.isPersonal ? [] : [Visibility.PUBLIC, Visibility.PRIVATE]"
    :force-top-comment-id="forceTopCommentId"
    :default-toolbar="defaultToolbar"
  />

  <InfiniteLoading :class="{ 'empty-wrap': !dataList.length }" @infinite="loadMore">
    <template #complete>
      <div v-if="!dataList.length" class="is-empty">
        <Empty name="no-like" />
      </div>
    </template>
  </InfiniteLoading>
</template>

<script setup lang="ts">
import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';
import to from 'await-to-js';
import { useI18n } from 'vue-i18n';
import Empty from '@/components/common/Empty.vue';
import InfiniteLoading from '@/components/InfiniteLoading/index.vue';
import { getNewsLikesList } from '@/api/square/news';
import { PostType, SNAPSHOT, Visibility } from '@/views/square/constant';
import { timeAgo } from '@/views/square/utils/time';
import useInfiniteLoad from '@/hooks/infiniteLoad';
import PostDetail from '@/views/square/components/post/PostDetail.vue';
import { checkPostInvisible, getPost } from '@/api/square/post';
import useNavigate from '@/views/square/hooks/navigate';
import KyyAvatar from '@/components/kyy-avatar/index.vue';
import CategoryTitle from '@/views/square/notifications/components/CategoryTitle.vue';
import { useSquareStore } from '@/views/square/store/square';
import { LikeNews } from '@/api/square/models/news';
import { ResourceType } from '@/api/square/models/comment';
import { useGoAlbum } from '@/views/square/notifications/utils';
import { getOSSImageResize } from '@/views/square/utils/OSSHelpler';

const { t } = useI18n();
const store = useSquareStore();
const { goHomePage } = useNavigate();

const count = store.newsStats.likes;

type LikeNewsExt = LikeNews & {
  createTime: string;
  resourceTypeText: string;
  videoUrl: string;
  targetText: string;
}

// 创建包装函数来传递teamId
const getListWithTeamId = (params: any) => getNewsLikesList(params, store.isPersonal ? '-1' : store.teamId || '');

const { dataList, loadMore } = useInfiniteLoad<LikeNewsExt>(getListWithTeamId, {
  dataHandler: ((v) => {
    const { resourceType, post } = v.res;
    const resourceTypeMap: Partial<Record<ResourceType, string>> = {
      [ResourceType.Post]: t('square.post.post'),
      [ResourceType.Comment]: t('square.post.comment'),
      [ResourceType.AlbumNodeImage]: '拾光相册',
      [ResourceType.AlbumNode]: '拾光相册',
      [ResourceType.Square]: '广场',
    };
    const result = {
      ...v,
      createTime: timeAgo(v.likedAt),
      resourceTypeText: `${t('square.post.likeYou')}${resourceTypeMap[resourceType]}`,
    };

    switch (resourceType) {
      case ResourceType.AlbumNode:
      case ResourceType.AlbumNodeImage:
        break;
      case ResourceType.Post:
        result.videoUrl = post.video ? `${post.video}${SNAPSHOT}` : '';
        break;
      default:
        break;
    }

    return result;
  }),
});

const detailId = ref('');
const detailVisible = ref(false);
const defaultToolbar = ref('like');
const forceTopCommentId = ref('');

const { goAlbum } = useGoAlbum();

const checkPost = (post) => {
  if (post.deleted) {
    MessagePlugin.error(t('square.post.deleted'));
    return false;
  }

  if (post.invisible) {
    MessagePlugin.error(t('square.post.invisible'));
    return false;
  }

  return true;
};

const showPost = (item) => {
  const { resourceType, comment, post } = item.res;
  const likePost = resourceType === ResourceType.Post && !post.deleted;
  const likeComment = resourceType === ResourceType.Comment && comment.resourceType === ResourceType.Post && !comment.deleted;
  return likePost || likeComment;
};

const showAlbumNode = (item) => {
  const { resourceType, comment } = item.res;
  // 相册节点点赞
  return resourceType === ResourceType.AlbumNode
    // 相册节点评论点赞
    || (resourceType === ResourceType.Comment && comment.resourceType === ResourceType.AlbumNode);
};

const showAlbumNodeImage = (item) => {
  const { resourceType, comment } = item.res;
  // 相册图片点赞
  return resourceType === ResourceType.AlbumNodeImage
    // 相册图片评论点赞
    || (resourceType === ResourceType.Comment && comment.resourceType === ResourceType.AlbumNodeImage);
};

const itemClick = async (item: LikeNewsExt) => {
  const isSelfPost = store.isSelfSquare(item.square.squareId);
  const squareId = isSelfPost ? item.square.squareId : store.squareId;

  let [err, res] = [null, null];
  const resourceType = item.res.resourceType;

  console.log(item, resourceType);

  // 动态
  if (resourceType === ResourceType.Post) {
    const { post } = item.res;
    if (!checkPost(post)) return;

    // 检测动态是否可见
    [err, res] = await to(checkPostInvisible(post.postId, { square_id: squareId }));
    if (!err) {
      if (!checkPost(res.data)) return;
    }

    const isComment = item.res.comment?.resourceType === ResourceType.Comment;
    defaultToolbar.value = isComment ? 'comment' : 'like';

    // 预先请求接口，有异常不打开详情
    [err] = await to(getPost({ post_id: post.postId }));
    if (err) return;

    if (isComment) {
      forceTopCommentId.value = item.res.comment?.commentId;
    }

    detailVisible.value = true;
    detailId.value = post.postId;
    return;
  }

  if (resourceType === ResourceType.Comment) {
    const commentType = item.res.comment.resourceType;
    if (commentType === ResourceType.Post) {
      // 他人的动态：点击“回复评论”“点赞”按钮，或整个评论卡片，toast提示：这条动态已设置不可见
      // 本人的动态：点击“回复评论”按钮，提示：这条动态已设置不可见，回复后对方不会收到通知
      const [err, res] = await to(checkPostInvisible(item.res.post.postId, { square_id: squareId }));
      if (!err) {
        const { invisible, deleted } = res.data;
        if (deleted) {
          await MessagePlugin.error(t('square.post.deleted'));
          return;
        }
        if (invisible) {
          await MessagePlugin.error(isSelfPost ? t('square.post.tip11') : t('square.post.invisible'));
          return;
        }
      }

      defaultToolbar.value = 'comment';
      if (item.res.post.allowComment === false) {
        await MessagePlugin.warning(t('square.errReason.postCommentClosed'));
      }
      forceTopCommentId.value = item.res.comment.commentId;

      detailId.value = item.res.post.postId;
      detailVisible.value = true;
    }

    // 跳转拾光相册
    if ([ResourceType.AlbumNode, ResourceType.AlbumNodeImage].includes(commentType)) {
      goAlbum(item);
      return;
    }
  }

  // 跳转拾光相册
  if ([ResourceType.AlbumNode, ResourceType.AlbumNodeImage].includes(resourceType)) {
    goAlbum(item);
  }
};
</script>

<style lang="less" scoped>
@import "list";

.fcbox {
  position: relative;
  border-radius: 8px;
  color: var(--lingke-contain-fonts, #516082);
  background: var(--bg-kyy-color-bg-deep, #f5f8fe);

  .cl-logo {
    font-size: 47px;
    color: #e2e6f5;
  }
}

.fc-tag {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  border-radius: 0px 4px;
  background: rgba(21, 21, 21, 0.30);
  position: absolute;
  right: 0px;
  top: 0px;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  .icon {
    color: #fff;
  }
}

.live-icon {
  position: absolute;
  top: 40%;
  left: 40%;
  font-size: 20px;
  z-index: 100;
}
</style>
