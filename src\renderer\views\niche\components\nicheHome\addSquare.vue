<template>
  <div class="eid">
    <t-drawer
      :header="params.promotion_type === 1 ? t('niche.tianjiasq') : t('niche.tianjiazz')"
      :close-btn="true"
      placement="right"
      :visible="deVisible"
      :destroy-on-close="true"
      class="drawer-addsq"
      size="472px"
      :footer="null"
      @close="handleClose"
    >
      <div class="add-sq">
        <div v-if="params.promotion_type === 2" class="join-boxx">
          <join-box ref="joinRef" />
        </div>
        <div class="search-bar" @keyup.enter="getDetailRun">
          <t-input
            v-if="params.promotion_type === 1"
            ref="inputRef"
            v-model="params.keyword"
            style="width: 350px"
            autofocus
            :placeholder="t('niche.gchss')"
            clearable
            @blur="getDetailRun"
          >
            <template #prefix-icon>
              <iconpark-icon name="iconsearch" class="name-icon" />
            </template>
          </t-input>

          <!-- <t-input-group v-else>
            <t-select v-replace-svg  v-model="params.digital_type" style="width: 120px" @change="getDetailRun">
              <t-option key="1" label="数字商协" :value="1" />
              <t-option key="2" label="数字城市" :value="2" />
            </t-select>
            <t-input
              ref="inputRef"
              v-model="params.keyword"
              style="width: 230px"
              :placeholder="'请输入组织名称、ID搜索'"
              clearable
            >
              <template #prefix-icon>
                <iconpark-icon name="iconsearch" class="name-icon" />
              </template>
            </t-input>
          </t-input-group> -->

          <div v-else class="addsq-search" @keyup.enter="getDetailRun">
            <!-- <div class="line"></div>
            <t-select v-replace-svg  v-model="params.digital_type" style="width: 94px" @change="getDetailRun">
              <t-option key="1" label="数字商协" :value="1" />
              <t-option key="2" label="数字城市" :value="2" />
            </t-select> -->
            <t-input
              ref="inputRef"
              v-model="params.keyword"
              style="width: 350px"
              :placeholder="t('niche.szss')"
              clearable
              @blur="getDetailRun"
            >
              <template #prefix-icon>
                <iconpark-icon name="iconsearch" class="name-icon" />
              </template>
            </t-input>
          </div>
          <t-button style="width: 80px" @click="getDetailRun">{{ t("niche.ss") }} </t-button>
        </div>

        <div v-if="params.promotion_type === 2" class="tabs">
          <div class="item" :class="{ itemacv: params.digital_type === 1 }" @click="changeType(1)">
            {{ t("niche.szsxx") }}
          </div>
          <div class="item" :class="{ itemacv: params.digital_type === 2 }" @click="changeType(2)">
            {{ t("niche.szzqx") }}
          </div>
          <div class="item" :class="{ itemacv: params.digital_type === 3 }" @click="changeType(3)">
            {{ t("niche.szcbdx") }}
          </div>
          <div class="item" :class="{ itemacv: params.digital_type === 4 }" @click="changeType(4)">
            {{ t("niche.szsq") }}
          </div>
              <div class="item" :class="{ itemacv: params.digital_type === 5 }" @click="changeType(5)">
           数字高校
          </div>
          <div :class="`tag${params.digital_type}`"></div>
        </div>

        <div class="res-box" :class="`res-box-hei${params.promotion_type}`">
          <div v-for="item in listData" :key="item.id" class="item">
            <avatar avatar-size="64px" :image-url="item.avatar || null" :user-name="item.name"></avatar>
            <div class="text">
              <div class="name" :title="item.name">
                <div v-if="params.promotion_type === 2 && item.is_connect" class="connect-tag">
                  {{ t("niche.yilian") }}
                </div>
                <div v-html="getNameText(item.name)"></div>
              </div>
              <div class="id">{{ item.uuid }}</div>
            </div>
            <t-button
              style="width: 80px"
              :disabled="selectedId.includes(item.promotion_related)"
              @click="handleClick(item)"
            >
              <span v-if="selectedId.includes(item.promotion_related) && !item.selected">{{ t("niche.yitianj") }}</span>

              <template v-if="params.promotion_type === 1">
                <span v-if="!selectedId.includes(item.promotion_related) && !item.selected">{{
                  t("niche.tianjia")
                }}</span>
              </template>
              <template v-else>
                <span v-if="!selectedId.includes(item.promotion_related) && !item.selected">
                  {{ item.is_connect ? t("niche.tianjia") : t("niche.cad") }}
                </span>
              </template>

              <template v-if="item.selected">
                <template v-if="item.process_state === 0">
                  <span>{{ t("niche.status_pending") }}</span>
                </template>
                <template v-if="item.process_state === 1">
                  <span>{{ releaseStateText[item.release_state] }}</span>
                </template>
                <template v-if="item.process_state === 2">
                  <span>{{ t("niche.status_rejected") }}</span>
                </template>
              </template>
            </t-button>
          </div>

          <template v-if="params.promotion_type === 1">
            <div v-if="reqOk && listData.length === 0 && params.keyword">
              <Empty />
            </div>
            <div v-if="listData.length === 0 && !params.keyword" class="logo-box">
              <img class="logo" src="@renderer/assets/niche/Vector.svg" alt="" />
            </div>
          </template>
          <template v-else>
            <div v-if="listData.length === 0" style="margin-top: 140px">
              <Empty />
            </div>
          </template>
        </div>
      </div>
    </t-drawer>

    <RenewalDialog
      v-if="renewalDialogVisible"
      v-model="renewalDialogVisible"
      :square-id="props.squareId"
      :upgrade="upgrade"
      :team-id="teamId"
      @success="renewalSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
// import Empty from "@renderer/components/common/Empty.vue";
import Empty from "@renderer/views/niche/components/nicheHome/noData.vue";
import joinBox from "@renderer/views/niche/components/nicheHome/join-box.vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";

import { businessCount } from "@renderer/api/business";
import { useApprovalStore } from "@renderer/store/modules/approval";
import RenewalDialog from "@/views/square/components/annual-fee/AnnualFeeDialog.vue";
import avatar from "@/components/kyy-avatar/index.vue";
import { promotionSearch, digitalConnectApi, digitalConnectGet } from "../../apis/create";
import { getTeamAnnualFee } from "@renderer/api/business/manage";
const { t } = useI18n();
const selectedId = ref([]);
const reqOk = ref(false);
const joinRef = ref(null);

const inputRef = ref(null);
const params = ref({
  keyword: "",
  id: "",
  promotion_type: 1,
  is_draft: 0,
  digital_type: 1,
});
const releaseStateText = ref([
  t("niche.status_not_active"),
  t("niche.status_in_progress"),
  t("niche.status_off_shelf"),
  t("niche.status_expired"),
  t("niche.status_deleted"),
]);
const props = defineProps({
  squareId: {
    type: String,
    default: null,
  },
});
const listData = ref([]);
const status_text = [
  t("niche.re_st_0"),
  t("niche.re_st_1"),
  t("niche.re_st_2"),
  t("niche.re_st_3"),
  t("niche.re_st_4"),
];
const process_state_text = [t("niche.pro0"), t("niche.pro1"), t("niche.pro2")];
const deVisible = ref(false);
const handleClose = () => {
  deVisible.value = false;
};
const digitalConnect = ref({
  total: 0,
  consume: 0,
  usable: 0,
});
const teamId = ref(localStorage.getItem("businessTeamId") || "");

const sqOpen = (type, id, chData, is_draft) => {
  params.value.id = id;
  params.value.keyword = null;
  params.value.digital_type = 1;
  params.value.is_draft = is_draft || 0;
  params.value.promotion_type = type;
  reqOk.value = false;
  listData.value = [];
  dataTemp.value = [];
  deVisible.value = true;
  selectedId.value = chData.map((item) => item.promotion_related);
  setTimeout(() => {
    inputRef.value.focus();
  }, 1000);
  if (type === 2) {
    getDetailRun();
  }
  digitalConnectGetRun();
  getTeamAnnualFeeRun();
};

const changeType = (type) => {
  params.value.digital_type = type;
  getDetailRun();
};

const related_ids: any = ref([]);
const releaseDetail: any = ref({});
releaseDetail.value.release_state = 1;
const getDetailRun = () => {
  promotionSearch(params.value).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      listData.value = res.data.data;
      for (const item of listData.value) {
        if (item.selected) {
          selectedId.value.push(item.promotion_related);
        }
      }
      reqOk.value = true;
    }
  });
};

const productDetailRef = ref(null);
const showData = ref({});
const productDetailRun = (row) => {
  showData.value = row;
  productDetailRef.value.productDetailOpen(null, null, row);
};

const emits = defineEmits(["update-package", "addEvent"]);

const dataTemp = ref([]);

const approvalStore = useApprovalStore();
const nicheCountFn = async () => {
  const res = await businessCount();
  if (res.data.code === 0) {
    approvalStore.updateNew = +new Date();
    approvalStore.nicheCountData = res.data.data.audit;
  }
};
const handleClick = (rowData) => {
  if (params.value.promotion_type === 1) {
    emits("addEvent", rowData);
    selectedId.value.push(rowData.promotion_related);
  } else if (rowData.is_connect) {
    emits("addEvent", rowData);
    selectedId.value.push(rowData.promotion_related);
  } else {
    updatev2(rowData);
  }
};

// 体验
const trialPackage = (rowData) => {
  if (checkExpiration(squareData.value.annualFeeExpiredAt)) {
    pageShow.value = true;
    tipsData.value.tipHeader = t("niche.vip_open_tiph");
    tipsData.value.con = t("niche.vip_open_tip9");
    tipsData.value.btn = t("niche.vip_open_btn5");
  } else {
    const item = squareData.value?.annualFeeDetail?.package?.items.find((item) => item.itemType === "NICHE");
    if (!item) {
      tipsData.value.con = `体验套餐权益不足，请先购买套餐，再连接添加`;
      tipsData.value.btn = t("niche.vip_open_btn5");
      return;
    }
    if (digitalConnect.value.usable === 0) {
      pageShow.value = true;
      tipsData.value.con = t("niche.vip_open_tip10");
      tipsData.value.btn = t("niche.vip_open_btn5");
    } else {
      addConnect(rowData);
      return;
    }
  }
  update();
};

// 非体验
const defaultPackage = (rowData) => {
  if (checkExpiration(squareData.value.annualFeeExpiredAt)) {
    pageShow.value = true;
    tipsData.value.con = t("niche.vip_open_tip10");
    tipsData.value.btn = t("niche.vip_open_btn6");
  } else {
    // 进行中
    const item = squareData.value?.annualFeeDetail?.package?.items.find((item) => item.itemType === "NICHE");
    if (!item) {
      tipsData.value.con = t("niche.vip_open_tip2");
      tipsData.value.btn = t("niche.vip_open_btn5");
      return;
    }
    // eslint-disable-next-line no-lonely-if
    if (digitalConnect.value.usable === 0) {
      pageShow.value = true;
      tipsData.value.con = t("niche.vip_open_tip9");
      tipsData.value.btn = t("niche.vip_open_btn5");
      upgrade.value = true;
    } else {
      addConnect(rowData);
      return;
    }
  }
  update();
};

const updatev2 = (rowData) => {
  if (squareData.value?.annualFeeDetail?.package) {
    if (squareData.value?.annualFeeDetail?.trial) {
      trialPackage(rowData);
    } else {
      defaultPackage(rowData);
    }
  } else {
    noEquity();
  }
};

const noEquity = () => {
  if (isMac.value) {
    mactip(2);
    return;
  }
  tipsData.value.con = t("niche.vip_open_tip2");
  tipsData.value.btn = t("niche.vip_open_btn2");
  upgrade.value = true;
  pageShow.value = true;
  update();
};

const update = () => {
  if (upgrade.value && isMac.value) {
    mactip(1);
    return;
  }
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: tipsData.value.con,
    className: "dialog-classp32",
    confirmBtn: tipsData.value.btn,
    cancelBtn: "取消",
    onConfirm: () => {
      myDialog.hide();
      updateRun();
      // emits("update-package", {});
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const addConnectReq = (rowData) => {
  const params = {
    consume_team_id: teamId.value,
    belong_team_id: rowData.team_id,
  };
  digitalConnectApi(params).then((res) => {
    if (res.data?.code === 0) {
      getDetailRun();
      emits("addEvent", rowData);
      selectedId.value.push(rowData.promotion_related);
      digitalConnectGetRun();
    }
  });
};
const addConnect = (rowData) => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("niche.cntip"),
    className: "dialog-classp32",
    confirmBtn: t("niche.tianjia"),
    cancelBtn: "取消",
    onConfirm: () => {
      myDialog.hide();
      addConnectReq(rowData);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const isMac = ref(process.platform === "darwin");
const mactip = (type) => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: type === 1 ? `连接数不足` : t("niche.vip_open_tip2"),
    className: "dialog-classp32",
    cancelBtn: null,
    confirmBtn: "知道了",
    onConfirm: () => {
      myDialog.hide();
    },
  });
};
const updateRun = () => {
  renewalDialogVisible.value = true;
};

const digitalConnectGetRun = () => {
  digitalConnectGet(teamId.value).then((res) => {
    if (res.data?.data) {
      digitalConnect.value = res.data.data;
      joinRef.value?.connectReData(res.data.data);
      emits("update-package", res.data.data);
    }
  });
};

const renewalDialogVisible = ref(false);
const upgrade = ref(false);
const renewalSuccess = (e) => {
  renewalDialogVisible.value = false;
  digitalConnectGetRun();
};
const checkExpiration = (expiredAt) => {
  const now = new Date();
  const expirationDate = new Date(expiredAt);
  return now > expirationDate;
};
const tipsData = ref({
  con: "",
  btn: "",
  tipHeader: null,
});
const squareData: any = ref({});
const squareShow: any = ref(false);
const pageShow: any = ref(false);
const getTeamAnnualFeeRun = async () => {
  const res = await getTeamAnnualFee(teamId.value);
  if (res.data) {
    squareData.value = res.data;
  }
};

const getNameText = (val) => {
  if (val) {
   return val.replace(params.value.keyword, `<span style="color:#4D5EFF;">${params.value.keyword}</span>`);
  }
};

defineExpose({
  sqOpen,
});
</script>

<style lang="less" scoped>
.add-sq {
  width: 100%;
  height: 100%;
}
.search-bar {
  display: flex;
  padding: 8px 16px 12px 16px;
  align-items: flex-start;
  gap: 10px;
  align-self: stretch;
  background: var(--bg-kyy_color_bg_light, #fff);
}
:deep(.drawer-addsq .t-drawer__body) {
  padding: 0px !important;
}
// :deep(.t-drawer__body) {
//   padding: 0px !important;
// }
.res-box::-webkit-scrollbar {
  width: 0px;
}

.res-box {
  display: flex;
  padding: 12px 16px;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  flex: 1 0 0;
  align-self: stretch;
  overflow-y: auto;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  overflow-x: hidden;
  border-top: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4);
  .logo-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    .logo {
      width: 250px;
      height: 56px;
    }
  }
  .item {
    display: flex;
    width: 440px;
    padding: 16px;
    align-items: center;
    gap: 12px;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #fff);

    .text {
      width: 248px;
      .name {
        width: 100%;
        overflow: hidden;
        color: var(--text-kyy_color_text_1, #1a2139);
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        flex: 1 0 0;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
      .id {
        width: 100%;
        overflow: hidden;
        color: var(--text-kyy_color_text_3, #828da5);
        text-overflow: ellipsis;

        /* kyy_fontSize_1/regular */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
    }
  }
}
.res-box-hei1 {
  height: calc(100% - 56px);
}
.res-box-hei2 {
  height: calc(100% - 139px);
}
.name-icon {
  font-size: 20px;
  color: #828da5;
}
.addsq-search {
  display: flex;
  position: relative;
  .line {
    position: absolute;
    top: 8px;
    left: 92px;
    height: 16px;
    width: 1px;
    max-width: 1px;
    /* color: red; */
    /* background: red; */
    z-index: 1;
    background: var(--divider-kyy_color_divider_light, #eceff5);
  }
}
.tabs {
  display: flex;
  padding: 0px 16px;
  align-items: center;
  align-self: stretch;
  gap: 24px;
  position: relative;
  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--text-kyy_color_text_1, #1a2139);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 36px; /* 150% */
    height: 36px;
    cursor: pointer;
  }
  .itemacv {
    color: var(--brand-kyy_color_brand_default, #4d5eff);
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
  }
  .tag1 {
    width: 16px;
    height: 3px;
    border-radius: 1.5px;
    background: var(--brand-kyy_color_brand_default, #4d5eff);
    position: absolute;
    bottom: 0px;
    left: 40px;
  }
  .tag2 {
    width: 16px;
    height: 3px;
    border-radius: 1.5px;
    background: var(--brand-kyy_color_brand_default, #4d5eff);
    position: absolute;
    bottom: 0px;
    left: 130px;
  }
  .tag3 {
    width: 16px;
    height: 3px;
    border-radius: 1.5px;
    background: var(--brand-kyy_color_brand_default, #4d5eff);
    position: absolute;
    bottom: 0px;
    left: 216px;
  }
  .tag4 {
    width: 16px;
    height: 3px;
    border-radius: 1.5px;
    background: var(--brand-kyy_color_brand_default, #4d5eff);
    position: absolute;
    bottom: 0px;
    left: 304px;
  }
}
.join-boxx {
  padding: 8px 16px 4px 16px;
}

.connect-tag {
  display: flex;
  height: 20px;
  width: 32px;
  padding: 2px 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_success, #e0f2e5);
  color: var(--kyy_color_tag_text_success, #499d60);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  float: left;
  margin-right: 4px;
}
</style>
