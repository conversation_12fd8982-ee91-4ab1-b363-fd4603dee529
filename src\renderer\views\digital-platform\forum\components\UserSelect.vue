<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { CardInfo } from '@renderer/api/forum/models/user';
import to from 'await-to-js';
import { getOwnerId } from '@renderer/api/forum/user';
import { OwnerType } from '@renderer/api/forum/models/forum';
import { MessagePlugin } from 'tdesign-vue-next';
import Avatar from '@/components/kyy-avatar/index.vue';
import { useForumStore } from '../store';

const props = defineProps<{
  // 局部使用选择的用户，不影响全局
  isolation?: boolean;
  disabled?: boolean;
  defaultValue?: CardInfo;
  triggerCls?: string;
}>();
const emit = defineEmits(['change']);

const visible = ref(false);
const loading = ref(false);
const onlyOne = computed(() => forumStore.cardList.length === 1);

// 格式化数据（因：从草稿列表编辑，传入的数据结构不一致）
const mapper = (val) => {
  val._flagText =
    { staff_platform: '平台', staff: '员工', visitor: '访客', anonymous: '匿名' }[
      val.flag || val.digitalPlatformFlag
    ] || '访客';
  val.card = val.card || val.cardId;
};

// 当前用户
const forumStore = useForumStore();
const currCard = ref(forumStore.currCard);
watch(
  () => props.defaultValue,
  (val) => {
    if (!val) return;

    mapper(val);
    currCard.value = val;
    emit('change', val);
  },
  { immediate: true },
);

// 切换时获取 ownerId
const ownerId = ref('');
const getOwner = async (cardId: string) => {
  const [err, res] = await to(
    getOwnerId({
      ownerType: OwnerType.DigitalPlatform,
      teamId: forumStore.teamId,
      cardId,
      appUuid: forumStore.platformType,
    }),
  );

  if (err) {
    console.log(err);
    const { code, message } = err.data;
    if (code === 2001) {
      MessagePlugin.error('切换失败，暂无相关权限');
      return false;
    }
    MessagePlugin.error(message);
    return false;
  }

  ownerId.value = res.data.data.ownerId;
  return true;
};

// 选择用户
const itemClick = async (item: CardInfo) => {
  visible.value = false;
  console.log('item', item);

  // 局部使用
  if (props.isolation) {
    const valid = await getOwner(item.card);
    if (!valid) return;

    mapper(item);
    currCard.value = item;
    item.id = ownerId.value;

    forumStore.updateOwnerIdToList(ownerId.value, item);

    emit('change', item);
    return;
  }

  forumStore.switchCard(item.card);
};
</script>

<template>
  <t-popup
    v-model="visible"
    :disabled="disabled || onlyOne"
    placement="bottom-right"
    overlay-inner-class-name="popup-forum-users popup-com"
  >
    <slot>
      <div :class="['user-select', triggerCls]">
        <div v-if="loading" class="text-center w-full"><t-loading size="small" /></div>
        <template v-else-if="currCard">
          <div class="name">{{ currCard.name }}</div>
          <t-tag theme="primary" variant="light" class="border-0!">{{ currCard._flagText }}</t-tag>
          <iconpark-icon v-if="!onlyOne" name="iconarrowdown" class="icon" />
        </template>
      </div>
    </slot>

    <template #content>
      <div class="user-list">
        <div
          v-for="(item, index) in forumStore.cardList"
          :key="index"
          :class="['user-item', { selected: currCard?.card === item.card }]"
          @click="itemClick(item)"
        >
          <Avatar
            avatar-size="24px"
            :image-url="item.avatar ?? ''"
            :user-name="item.name"
            round-radius
            class="avatar"
          />
          <div class="name">{{ item.name }}</div>
          <t-tag theme="primary" variant="light">{{ item._flagText }}</t-tag>
          <iconpark-icon name="iconcheckone" class="icon" />
        </div>
      </div>
    </template>
  </t-popup>
</template>

<style lang="less" scoped>
.user-select {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  max-width: 100%;
  &:hover {
    background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
  }
  .name {
    color: var(--text-kyy_color_text_1, #1a2139);
    font-size: 16px;
    font-weight: 600;
    line-height: 24px; /* 150% */
    max-width: 132px;
    .ellipsis();
  }
  .icon {
    font-size: 20px;
    color: #828da5;
  }
}
</style>

<style lang="less">
.popup-forum-users {
  width: 224px;
  padding: 4px;
  .user-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--checkbox-kyy_radius_checkbox, 2px);
  }

  .user-item {
    width: 100%;
    display: flex;
    height: 32px;
    min-width: 136px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px var(--kyy_radius_dropdown_m, 8px);
    align-items: center;
    border-radius: var(--kyy_radius_dropdown_s, 4px);
    background: var(--bg-kyy_color_bg_default, #fff);
    cursor: pointer;
    .avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      flex-shrink: 0;
      margin-right: var(--kyy_radius_dropdown_m, 8px);
    }
    .name {
      color: var(--text-kyy_color_text_1, #1a2139);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      margin-right: 4px;
      .ellipsis();
    }
    .icon {
      display: none;
      font-size: 20px;
      height: 20px;
      margin-left: 4px;
    }
    &:hover,
    &.selected {
      background: var(--bg-kyy_color_bg_list_foucs, #e1eaff);
    }
    &.selected .icon {
      display: initial;
    }
  }
}
</style>
