<template>
  <div v-if="!isDelete" class="addhonor">
    <!-- 是否开启发布验证 -->
    <div v-if="openFlag" class="chatBox">
      <img src="@renderer/assets/bench/icon_info.svg" alt="">
      <div>{{ `${t('banch.publishverify')}` }}</div>
    </div>
    <t-form
      ref="activityForm"
      class="honorForm"
      scroll-to-first-error="auto"
      :label-width="600"
      :data="formData"
      :rules="rules"
      @submit="onSubmit"
    >
      <t-form-item
        :label="t('banch.growthTimeTitle')"
        name="progress_at"
        label-align="top"
        style="flex: 1;"
      >
        <t-date-picker
          id="durationRef"
          v-model="formData.progress_at"
          clearable
          format="YYYY-MM-DD"
          :disable-date="allTime"
          style="width: 100%;"
          :placeholder="t('banch.honorTimeph')"
          :default-time="[moment().format('HH:mm:ss'), moment().format('HH:mm:ss')]"
          @change="onChangeDate"
          @click="onClickDatePicker"
        />
      </t-form-item>
      <t-form-item
        :label="t('banch.growthTitleTip')"
        name="content"
        label-align="top"
        style="flex: 1;"
      >
        <t-textarea
          :key="textareaKey"
          v-model="formData.content"
          class="title textarea-wrapper"
          :maxlength="200"
          :autosize="{ minRows: 1, maxRows: 5 }"
          :placeholder="t('banch.honorTitleph')"
          @keydown="preventEnter"
        />
      </t-form-item>
      <t-form-item :label="t('banch.growthCover')" label-align="top" name="image_arr">
        <ImageUpload v-model="formData.image_arr"></ImageUpload>
      </t-form-item>
      <!-- 发布至 -->
      <t-form-item
        name="channelType"
        label-align="top"
        class="sendTo"
        :label="t('banch.sendTo')"
      >
        <t-checkbox-group v-model="formData.channelType">
          <t-checkbox
            v-if="showSwitchList.includes('square')"
            key="0"
            value="to_square"
            :label="t('banch.square')"
          ></t-checkbox>
          <t-checkbox
            v-if="showSwitchList.includes('workshop')"
            key="1"
            value="to_workshop"
            :label="t('banch.banch')"
          ></t-checkbox>
          <t-checkbox
            v-if="showSwitchList.includes('member') || showSwitchList.includes('government')|| showSwitchList.includes('uni')|| showSwitchList.includes('association') || showSwitchList.includes('cbd')"
            key="2"
            value="to_platform"
            :label="t('banch.member')"
          ></t-checkbox>
        </t-checkbox-group>
      </t-form-item>
      <!-- 底部按钮 -->
      <t-form-item :label-width="0" label-align="left" class="bottomButtons">
        <div>
          <t-button
            class="activityDraft"
            style="margin-right: 8px;"
            theme="default"
            @click="saveDraft"
          >{{ t('activity.activity.saveDraft')
          }}</t-button>
          <t-button
            class="activityDraft"
            style="margin-right: 8px;"
            theme="default"
            @click="handlePreview"
          >{{ t('banch.publishPreviewTIitle') }}</t-button>
          <t-button
            :class="activityDetail?.details?.status=='Released'?'activityDraft':'activitySubmit'"
            :theme="activityDetail?.details?.status=='Released'?'default':'primary'"
            :disabled="draftLoading"
            :loading="submitLoading"
            type="submit"
          >{{ t('banch.publish') }}</t-button>
        </div>
      </t-form-item>
    </t-form>
    <!-- 点击存草稿的弹窗 -->
    <t-dialog
      v-model:visible="saveDraftFlag"
      class="appAuthTipDialog"
      theme="info"
      :cancel-btn="$t('address.cancel')"
      :close-btn="false"
      :confirm-btn="$t('address.sure')"
      :header="$t('banch.saveDraft')"
      :body="$t('banch.saveDraftTip')"
      @confirm="saveDraftConfirm"
    >
    </t-dialog>
    <AboutDialog
      v-model="showAboutDialog"
      type="timeline"
      :data="formData"
    ></AboutDialog>
    <select-body-dialog
      v-if="selectVisible"
      v-model:visible="selectVisible"
      options-type="process"
      :id-team="activationGroupItem.teamId"
      @confirm="selectConfirm"
    />
  </div>
  <noData v-if="isDelete" class="no-data" text="内容已被删除" />
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted, onUnmounted, onDeactivated } from 'vue';
import { useI18n } from "vue-i18n";
import moment from "moment";
import _ from "lodash";
import to from 'await-to-js';
import { useRouter, useRoute } from 'vue-router';
import { editHonorAPI, auditHonorAPI, auditAPI } from "@renderer/api/workBench/index.ts";
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import SelectBodyDialog from "@renderer/views/workBench/components/select-body-dialog.vue";
import AboutDialog from '@renderer/views/workBench/components/AboutDialog.vue';
import ImageUpload from '../components/ImageUpload.vue';
import { publishGrowth, saveGrowthAsDraft, editGrowthAsDraft, getGrowthDetailById, getGrowthAllTime } from '@/api/workBench/growth';
import { onMountedOrActivated } from '../../../hooks/onMountedOrActivated';
import { getAppsState } from '@/api/workBench/index';
import { useTabStore } from '../teamSting/honorStore/tabStore';
import noData from "../components/noData.vue";
import { preventEnter } from '@renderer/views/workBench/utils';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const tabStore = useTabStore();

// 表单数据
const formData = ref({
  image_arr: [],
  progress_at: '',
  content: '',
  channelType: []
});
const textareaKey = ref(0);
const submitLoading = ref(false);
const publishDialogVisible = ref(false);
const activityForm = ref();
const activationGroupItem = ref({});
activationGroupItem.value = JSON.parse(localStorage.getItem('honorteam'));
const selectVisible = ref(false);
const showSwitchList = ref([]);
const allTime = ref([]);
const isDelete = ref(false);
const needChangeTime = ref(false);
const showAboutDialog = ref(false);
// 表单校验规则
const rules = ref({
  progress_at: [
    { required: true, message: t('banch.addHonorTip1') },
    {
      validator: () => !needChangeTime.value,
      message: t('banch.growthTimeRepeat'),
    },
  ],
  image_arr: [{ required: true, message: t('banch.addHonorTip1') }],
  content: [{ required: true, message: t('banch.addHonorTip1') }],
  channelType: [{ required: true, message: t('banch.addHonorTip1') }],
});

/**
 * 初始化发布平台的开关信息
 */
const initAppState = async () => {
  showSwitchList.value = [];
  const [err, res] = await to(getAppsState(localStorage.getItem('honorteamid')));
  if (err) {
    const errData = err?.response?.data;
    MessagePlugin.warning(errData?.message);
    return;
  }
  const list = res.data.data;
  if (list.square) {
    showSwitchList.value.push('square');
  }
  if (list.workshop) {
    showSwitchList.value.push('workshop');
  }
  if (list.member) {
    showSwitchList.value.push('member');
  }
  if (list.government) {
    showSwitchList.value.push('government');
  }
  if (list.association) {
    showSwitchList.value.push('association');
  }
  if (list.cbd) {
    showSwitchList.value.push('cbd');
  }
  if (list.uni) {
    showSwitchList.value.push('uni');
  }
};

const onChangeDate = () => {
  needChangeTime.value = false;
};

/**
 * 初始化数据
 */
const initData = async () => {
  if (!route.query.id) {
    return;
  }
  const [err, res] = await to(getGrowthDetailById(route.query.id, localStorage.getItem('honorteamid')));
  if (err) {
    isDelete.value = true;
    MessagePlugin.warning(t('banch.contentIsDeleted'));
    return;
  }
  isDelete.value = false;
  const data = res.data.data;
  // needChangeTime.value = data.need_change_time;
  // if (data.status === 4 && needChangeTime.value) {
  //   activityForm.value.validate('progress_at');
  // }

  formData.value = data;
  formData.value.channelType = [];
  formData.value.image_arr = formData.value.image_arr.map((item) => ({ status: 'success', url: item }));
  // 重新发布
  if (formData.value.status === 4) {
    formData.value.reviewer = '';
    formData.value.isRepublish = true;
  }

  data.to_square === 1 && formData.value.channelType.push('to_square');
  data.to_workshop === 1 && formData.value.channelType.push('to_workshop');
  data.to_platform === 1 && formData.value.channelType.push('to_platform');
};

const getAllTime = async () => {
  const [err, res] = await to(getGrowthAllTime({}, localStorage.getItem('honorteamid')));
  if (err) {
    const errData = err?.response?.data;
    MessagePlugin.warning(errData?.message);
    return;
  }
  allTime.value = res.data.data.all_time;
  if (route.query.id) {
    allTime.value = allTime.value.filter((item) => item !== formData.value.progress_at);
  }
};

const getFormData = () => {
  const data = {};
  data.to_square = formData.value.channelType.includes('to_square') ? 1 : 0;
  data.to_workshop = formData.value.channelType.includes('to_workshop') ? 1 : 0;
  data.to_platform = formData.value.channelType.includes('to_platform') ? 1 : 0;
  data.reviewer = formData.value.reviewer || '';
  if (route.query.type === 'draft') {
    data.draft_id = route.query.id;
  } else {
    data.draft_id = '';
  }
  data.content = formData.value.content;
  data.progress_at = formData.value.progress_at;
  data.image_arr = formData.value.image_arr.map((item) => item.url);
  return data;
};

const openPersonal = ref(false);// 当前人是否有审核权限
const openFlag = ref(false);
const open = async () => {
  const res = await auditHonorAPI(localStorage.getItem('honorteamid'));
  openFlag.value = res.data.data.process_status === 1;
  openPersonal.value = res.data.data.progress_auth === 1;
};

/**
 * 选择审核人的事件
 */
const selectConfirm = async (list) => {
  formData.value.reviewer = parseInt(list[0].cardId.substring(1), 10);
  const err = await handleSubmit(false);
  if (!err) {
    MessagePlugin.success(t('banch.auditSubmitTip'));
  }
};
/**
 * 移动到有错误的表单项位置
 */
 const moveFirstInvalidElement = (res) => {
  const keys = Object.keys(res);
  const dom = document.querySelector('.addhonor');
  let top = 0;
  if (!dom) {
    return;
  }
  const items = document.querySelectorAll('.t-form__item');
  if (keys.includes('progress_at')) {
    top = items[0].offsetTop;
  } else if (keys.includes('content')) {
    top = items[1].offsetTop;
  } else if (keys.includes('channelType')) {
    top = items[2].offsetTop;
  }
  dom.scrollTo({
    top: top - 32,
    behavior: 'smooth'
  });
};
/**
 * 点击发布
 */
const onSubmit = async () => {
  const res = await activityForm.value.validate();
  if (typeof res !== 'boolean') {
    moveFirstInvalidElement(res);
    console.log('resres', res);
    if (Object.keys(res).length > 1) {
      MessagePlugin.warning(t('banch.addHonorTip'));
    } else if (res.channelType || res.content) {
      MessagePlugin.warning(t('banch.addHonorTip'));
    } else {
      const errTips = res.progress_at;
      if (!errTips[0].result) {
        MessagePlugin.warning(t('banch.addHonorTip'));
      }
    }
    return;
  }
  submitLoading.value = true;
  auditAPI({
    params: formData.value.content
  }).then(async (res) => {
    submitLoading.value = false;
    activationGroupItem.value = JSON.parse(localStorage.getItem('honorteam'));
    if (res.data.message === "success") { // 审核通过
      await open();
      // 当前人有审核权限或者未开启安全认证弹出弹窗
      if (openPersonal.value || !openFlag.value) {
        handleDialog();
      } else {
        // 非审核人弹出选人弹窗
        selectVisible.value = true;
      }
    } else {
      MessagePlugin.warning('你输入的信息包含敏感内容，请修改后重试');
    }
  }).catch((err) => {
    submitLoading.value = false;
    if (err?.response?.status !== 418) {
      MessagePlugin.warning(err?.response?.data?.message);
    }
  });
};
/**
 * 发布弹框确认
 */
 const handleDialog = () => {
  const dia = DialogPlugin.confirm({
    header: t('account.tip'),
    theme: 'info',
    body: openPersonal.value && openFlag.value ? t('banch.publishNeedAuth', [t('banch.growthTitle')]) : t('banch.publishNotNeedAuth', [t('banch.growthTitle')]),
    onConfirm: () => {
      handleSubmit();
      dia.destroy();
    },
    onCancel: () => {
      dia.hide();
    }
  });
};
// 提交表单
const handleSubmit = async (tip = true) => {
  const data = getFormData();
  const [err, _] = await to(publishGrowth(data, localStorage.getItem('honorteamid')));
  if (err) {
    const errData = err?.response?.data;
    if (errData.code === 90008) {
      needChangeTime.value = true;
      activityForm.value.validate('progress_at');
      await getAllTime();
    }
    MessagePlugin.warning(errData?.message);
    return err;
  }
  publishDialogVisible.value = false;
  activityForm.value?.reset();
  tip && MessagePlugin.success('发布成功');
  router.replace({
    path: '/workBenchIndex/teamSting',
    query: {
      activeMenu: 'growth',
      closeTab: '/workBenchIndex/growth-publish'
    }
  });
};
// 点击存草稿按钮
const saveDraftFlag = ref(false);
const saveDraft = async () => {
  // const res = await activityForm.value.validate();
  // if (typeof res !== 'boolean') {
  //   MessagePlugin.warning('请填写必填项');
  //   return;
  // }
  if (!formData.value.progress_at) {
    MessagePlugin.warning(t('banch.growthTimeNotEmpty'));
    return;
  }
  saveDraftFlag.value = true;
};
// 保存草稿
const saveDraftConfirm = async () => {
  const data = getFormData();
  let draftApi;
  if (route.query.id) {
    data.id = route.query.id;
    draftApi = editGrowthAsDraft;
  } else {
    draftApi = saveGrowthAsDraft;
  }

  const [err, _] = await to(draftApi(data, localStorage.getItem('honorteamid')));
  if (err) {
    const errData = err?.response?.data;
    if (errData.code === 90008) {
      needChangeTime.value = true;
      activityForm.value.validate('progress_at');
      await getAllTime();
    }
    MessagePlugin.warning(errData?.message);
    return;
  }
  saveDraftFlag.value = false;
  activityForm.value?.reset();
  MessagePlugin.success('保存成功');
  router.replace({
    path: '/workBenchIndex/teamSting',
    query: {
      activeMenu: 'growth',
      closeTab: '/workBenchIndex/growth-publish'
    }
  });
};

/**
 * 监听表单内容是否变化
 */
const contentWatcher = ref();
const listenContentChange = () => {
  contentWatcher.value = watch(() => formData.value, () => {
    if (!route.query.id) {
      const hasContent = formData.value.progress_at && formData.value.content.trim();
      if (hasContent) {
        tabStore.setSaveDraftWhenCloseTab(true);
      } else {
        tabStore.setSaveDraftWhenCloseTab(false);
      }
    }
  }, { immediate: true, deep: true });
};

/**
 * 预览效果
 */
 const handlePreview = async () => {
  // const inValid = !formData.value.progress_at || !formData.value.content.trim();
  const res = await activityForm.value.validate();
  if (typeof res !== 'boolean') {
    const dia = DialogPlugin.confirm({
      header: t('account.tip'),
      theme: 'info',
      confirmBtn: t('banch.iknow'),
      cancelBtn: null,
      body: t('banch.inpuAllMustContent'),
      onConfirm: async () => {
        moveFirstInvalidElement(res);
        dia.destroy();
      },
      onCancel: () => {
        dia.hide();
      }
    });
    return;
  }
  showAboutDialog.value = true;
};

const onClickDatePicker = async () => {
  await getAllTime();
};

watch(() => tabStore.isSaveDraft.growth, (val) => {
  if (val) {
    saveDraftConfirm();
  }
});

/**
 * 重置表单
 */
watch(() => tabStore.resetGrowthFormTag, () => {
  console.log(' tabStore.resetGrowthFormTag,', tabStore.resetGrowthFormTag);
  nextTick(() => {
    activityForm.value?.reset();
  });
}, { immediate: true });

onMountedOrActivated(async () => {
  textareaKey.value++;
  isDelete.value = false;
  needChangeTime.value = false;
  tabStore.isSaveDraft.growth = false;
  tabStore.setSaveDraftWhenCloseTab(false);
  await initAppState();
  await open();
  await initData();
  await getAllTime();
  listenContentChange();
});

onUnmounted(() => {
  contentWatcher.value?.();
});
onDeactivated(() => {
  contentWatcher.value?.();
});

</script>

<style lang="less" scoped>
.t-form__item.bottomButtons button {
  font-weight: bold;
}
.textarea-wrapper {
  transition: none;
  :deep(.t-textarea__inner) {
    transition: none;
  }
}
.addhonor{
  display: flex;
  width: 100%;
  height: 100%;
  padding: 16px 304px;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  background: var(--bg-kyy_color_bg_light, #FFF);
  overflow-y:auto;
}
// 发布验证
.chatBox {
  width:608px;
  text-align: center;
  display: flex;
  padding: 8px 24px;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  background: var(--kyy_color_alert_bg_bule, #EAECFF);
  color: var(--kyy_color_alert_text, #1A2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  // margin-top: 16px;
  /* 157.143% */
  .overHiddenName {
    max-width: calc(100% - 140px);
    padding-left: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .clickViewButton {
    height: 22px;
    padding: 0;
    color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4D5EFF) !important;
    background-color: transparent !important;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;

    /* 157.143% */
    :deep(.t-button__suffix) {
      margin-left: 0;
    }
  }
  >img{
    width:20px;
    height:20px;
  }
}
// 表单
.honorForm {
  width:608px;
  // 荣誉封面
  .upload-box {
    cursor: pointer;
    display: flex;
    width: 128px;
    height: 96px;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 4px;
    border-radius: 8px;
    border: 1px solid var(--kyy_color_upload_border_default, #D5DBE4);
    background: var(--kyy_color_upload_bg, #FFF);
    color: var(--kyy_color_upload_text_default, #516082);
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;

    /* 157.143% */
    img {
      width: 48px;
      height: 48px;
    }
  }
  .upload-box-tips {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    gap: 8px;
    margin-top: 0;
    margin-left: 16px;
    color: var(--text-kyy-color-text-5, #ACB3C0);

    /* kyy_fontSize_1/regular */
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
    >div:nth-child(1){
      height:23px;
    }
    .t-button {
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #D5DBE4);
      color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
      text-align: center;

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }
  :deep(.textarea) {
    .t-textarea__inner {
      height: 138px;
      resize: none;
    }
    .t-textarea__info_wrapper{
      position: absolute;
      bottom:4px;
      right: 4px;
    }
  }
  //富文本
  :deep(.QuillBox) {
    min-height: 303px;

    .t-form__controls-content {
      flex-direction: column;
    }

    .upload-input {
      display: none;
    }

    .ql-toolbar {
      width: 100%;
      border-radius: 4px 4px 0 0;
      background: var(--bg-kyy-color-bg-deep, #F5F8FE);
      border: 1px solid var(--divider-kyy-color-divider-deep, #D5DBE4);
      border-bottom: none;
    }

    .ql-container {
      height: 230px;
      width: 100%;
      border-radius: 0 0 4px 4px;
      border: 1px solid var(--divider-kyy-color-divider-deep, #D5DBE4);
      border-top: none;
    }
  }
  :deep(.t-form__label){
    margin-bottom: 8px;
    line-height: 22px;
  }

  :deep(.t-form__label--top) {
    min-height: unset;
  }

  //發送至
  .sendTo{
    margin-bottom:80px;
  }
  //底部按鈕
  .bottomButtons{
    position:fixed;
    bottom:0;
    left:0;
    width:100%;
    height: 64px;
    padding: 16px 0;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
    background:#fff;
    box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.08);
    :deep(.t-button){
      min-width:88px;
    }
    :deep(.t-form__controls){
      width: 608px;
      margin:auto;
    }
  }
}

:deep(.t-textarea__limit) {
  display: none;
}
</style>
<style lang="less">
.activity-lk-editor {
  width: 100%;
  min-height: 270px !important;

  .ql-toolbar {
    border: 1px solid var(--divider-kyy_color_divider_deep, #D5DBE4) !important;
    border-bottom: none !important;
  }

  .ql-container {
    border: 1px solid var(--divider-kyy_color_divider_deep, #D5DBE4) !important;
    border-top: none !important;
  }
}
.addhonor .t-dialog{
  width:480px;
  padding:32px;
}
.no-data {
  margin-top: 120px;
}
</style>
