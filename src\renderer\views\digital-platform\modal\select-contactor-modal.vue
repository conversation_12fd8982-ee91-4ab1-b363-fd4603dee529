<template>
  <t-dialog
    v-model:visible="visible"
    :z-index="2501"
    :header="props.header"
    attach="body"
    width="672px"
  >
    <template #body>
      <div class="toBody">
        <div class="toBody-left">
          <t-input v-model="searchKey" class="input" placeholder="搜索">
            <template #prefix-icon>
              <iconpark-icon name="iconsearch"  style="font-size:20px; "></iconpark-icon>
            </template>
          </t-input>
          <div class="content">
            <t-checkbox-group v-model="rightValue">
              <div
                class="group"
                style="margin-top: 0; overflow: auto; height: 312px"
              >
                <span
                  v-for="op in optionsData"
                  :key="op.id"
                  class="group-item"
                >
                  <t-checkbox
                    :value="op.id"
                    :disabled="
                      (isOnly && rightValue.length > 0) ||
                        disabledArr.includes(op.id)
                    "
                  >
                    <template #label>
                      <div class="selectLabel">
                        <kyyAvatar
                          style="padding: 0 4px"
                          :image-url="op.avatar"
                          avatar-size="24px"
                          :user-name="op.name"
                        />
                        <span class="text"> {{ op.name }} </span>
                      </div>
                    </template>
                  </t-checkbox>
                </span>
              </div>
            </t-checkbox-group>
          </div>
        </div>
        <div class="toBody-line" />
        <div class="toBody-right">
          <div class="behaver">{{ $t('member.impm.sys_6') }}：{{ rightValue.length }}人</div>
          <div class="selectGroup">
            <span
              v-for="(item, index) in rightValueComputed"
              :key="index"
              class="selectGroup-item"
            >
              <div class="selectLabel">
                <!-- {{ options.filter((e) => e.staffId === item) }}1111111111 -->
                <!-- {{ item }} -->
                <kyyAvatar
                  style="padding: 0 4px"
                  :image-url="item.avatar"
                  avatar-size="24px"
                  :user-name="item.name"
                />
                <span class="text">
                  {{ item.name }}
                </span>
              </div>

              <div class="close">
                <!-- <svg
                  class="iconpark-icon"
                  style="width: 16px; height: 16px"
                  @click="delItem(item)"
                >
                  <use href="#close" />
                </svg> -->
                <iconpark-icon
                  name="iconerror"
                  style="font-size: 16px"
                  @click="delItem(item)"
                ></iconpark-icon>
              </div>
            </span>
          </div>
        </div>
      </div>
      <!-- <div class="tips">{{ $t('member.impm.sys_3') }}</div> -->
    </template>
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >{{ $t('member.impm.sys_4') }}</t-button>
        <t-button theme="primary" @click="subForm" :disabled="rightValue?.length < 1">{{ $t('member.impm.sys_5') }}</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, computed, toRaw, Ref } from "vue";
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { originType }  from "@renderer/views/digital-platform/utils/constant"
const visible = ref(false);
const searchKey = ref("");
const emits = defineEmits(["subForm"]);
const rightValue: Ref<any[]> = ref([]);
const props = defineProps({
  header: {
    type: String,
    default: "转移负责人"
  },
  options: {
    type: Array,
    default: () => []
  },
  isOnly: {
    type: Boolean,
    default: false
  },
  originType: {
    type: String,
    default: originType.Member
  }
});
const optionsData = computed(() =>
  props.options.filter((e: any) => e.name.indexOf(searchKey.value) !== -1));
const rightValueComputed = computed(() =>
  rightValue.value.map((v) => props.options.find((op: any) => op.id === v)));

const onSetSelectedValue = (arr) => {
  rightValue.value = arr;
};
const delItem = (val) => {
  rightValue.value = rightValue.value.filter((e) => e !== val.id);
};

const subForm = () => {
  emits("subForm", toRaw(rightValue.value));
  onClose();
};

const disabledArr = ref([]);
const onSetDisableArr = (arr) => {
  disabledArr.value = arr;
};

const onOpen = () => {
  rightValue.value = [];
  visible.value = true;
};

const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose,
  onSetSelectedValue,
  onSetDisableArr
});
</script>

<style lang="less" scoped>
.t-alert--info {
  padding: 8px 16px;
}
.t-checkbox-group {
  width: 100%;
}
.toBody {
  border: 1px solid @kyy_color_divder_deep;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  height: 372px;

  &-left {
    flex: 1;

    .input {
      margin: 12px 16px;
      width: auto;
    }
    .bcrumb {
      margin: 0 16px;
      display: flex;
      flex-wrap: wrap;
      &-item {
        display: flex;
        align-items: center;
        max-width: 140px;
        .name {
          font-size: 14px;

          font-weight: 400;
          color: #13161b;
          max-width: 100px;
        }
        .icon {
          margin-left: 2px;
        }
        &:last-child {
          .name {
            color: #a1a2a4;
          }
          .icon {
            display: none;
          }
        }
      }
    }
    .group {
      margin: 0 8px;
      margin-top: 4px;
      display: flex;
      flex-direction: column;

      width: 100%;
      &-item {
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.25s linear;
        display: flex;
        justify-content: space-between;
        &:hover {
          background: #f0f8ff;
        }
        .selectLabel {
          display: flex;
          align-items: center;

          .image {
            width: 24px;
            height: 24px;
            border-radius: 5px;
          }
          .text {
            font-size: 14px;

            font-weight: 400;
            width: 200px;
            color: #13161b;
            margin-left: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .tips {
          font-size: 14px;

          font-weight: 400;

          color: #2069e3;
        }
      }
    }
  }
  &-line {
    background: #e3e6eb;
    border-radius: 8px;
    width: 1px;
    margin: 8px 0;
  }
  &-right {
    flex: 1;
    .behaver {
      font-size: 14px;

      font-weight: 400;
      color: #13161b;
      margin: 16px;
      // background-color: @kyy_color_icon_orange;
    }
    .selectGroup {
      margin: 0 8px;
      margin-top: 4px;
      &-item {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.25s linear;

        justify-content: space-between;
        &:hover {
          background: #f0f8ff;
        }
        .selectLabel {
          display: flex;
          align-items: center;

          .icon {
            background-color: @kyy_color_icon_orange;
            width: 24px;
            height: 24px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            .iconpark {
            }
          }
          .image {
            width: 24px;
            height: 24px;
            border-radius: 5px;
          }
          .text {
            font-size: 14px;

            font-weight: 400;
            color: #13161b;
            width: 200px;
            margin-left: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .close {
          cursor: pointer;
          user-select: none;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}

.tips {
  font-size: 14px;

  font-weight: 400;
  color: #717376;
  line-height: 22px;
  margin-top: 8px;
}
:deep(.t-dialog) {
  height: 554px;
}
</style>
