<template>
  <div v-if="readonly">
    <template v-if="computedRow !== null">
      <span>{{ computedRow }}</span>
      <span>{{ seatType === 0 ? '排' : '桌' }}</span>
    </template>

    <template v-if="computedNumber !== null">
      <span>{{ computedNumber }}</span>
      <span>{{ seatType === 0 ? '座' : '号' }}</span>
    </template>

    <template v-if="computedRow === null && computedNumber === null">
      <span>--</span>
    </template>
  </div>

  <div v-else class="activity-seat-number-input flex items-center gap-16">
    <div class="flex items-center gap-8">
      <t-input-number
        v-model="computedRow"
        theme="normal"
        :class="`!w-${width}`"
        :placeholder="placeholder"
        @blur="handleChange"
      />
      <span>{{ seatType === 0 ? '排' : '桌' }}</span>
    </div>
    <div class="flex items-center gap-8">
      <t-input-number
        v-model="computedNumber"
        theme="normal"
        :class="`!w-${width}`"
        :placeholder="placeholder"
        @blur="handleChange"
      />
      <span>{{ seatType === 0 ? '座' : '号' }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';

const props = defineProps({
  modelValue: {
    type: [String, null, undefined],
    required: true,
  },
  change: {
    type: Function,
  },
  width: {
    type: Number,
    default: 120,
  },
  placeholder: {
    type: String,
    default: '请输入',
  },
  seatType: {
    type: Number,
    default: 0,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  repeatCheckList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:modelValue']);

const computedRow = ref(null);

const computedNumber = ref(null);

const handleChange = () => {
  // 座位号行列最大值均为99
  const maxNum = 99;
  computedRow.value = !isEmpty(computedRow.value) ? Math.min(maxNum, computedRow.value) : null;
  computedNumber.value = !isEmpty(computedNumber.value) ? Math.min(maxNum, computedNumber.value) : null;

  if (!isEmpty(computedRow.value) && !isEmpty(computedNumber.value)) {
    const seatNumberString = `${computedRow.value}-${computedNumber.value}`;

    // 检测是否有重复的座位号（活动详情还包括参与人）
    const isRepeat = props.repeatCheckList.some((actor) => actor.seatNumber === seatNumberString);
    if (isRepeat) {
      MessagePlugin.warning('座位号重复，请重新输入');
      computedRow.value = null;
      computedNumber.value = null;
    }
  }

  const formatSeatNumber = `${computedRow.value !== null ? computedRow.value : ''}-${computedNumber.value !== null ? computedNumber.value : ''}`;
  emit('update:modelValue', formatSeatNumber);
  if (props.change) {
    props.change(formatSeatNumber);
  }
};

const isEmpty = (value) => value === null || value === undefined || value === '';

watchEffect(() => {
  if (props.modelValue) {
    const [rowValue, numberValue] = props.modelValue.split('-');
    computedRow.value = !isEmpty(rowValue) ? rowValue : null;
    computedNumber.value = !isEmpty(numberValue) ? numberValue : null;
  } else {
    computedRow.value = null;
    computedNumber.value = null;
  }
});
</script>

<style lang="less" scoped>
.activity-seat-number-input{
  :deep(.t-input){
    padding: 0 12px;
  }
}
</style>
