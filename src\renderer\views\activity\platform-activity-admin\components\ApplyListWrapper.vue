<template>
  <div>
    <div class="px-16">
      <div class="py-10 wrapper-header">
        <div class="py-4 flex items-center">
          <div class="flex items-center gap-4 cursor-pointer" @click="emit('back')">
            <iconpark-icon class="text-[#828DA5] text-20" name="iconarrowlift" />
            <span class="text-[#828DA5]">返回</span>
          </div>

          <div class="flex-1 text-center font-600 text-18 leading-26 text-[#1A2139]">
            申请列表
          </div>
        </div>
      </div>
    </div>

    <div class="pb-16 pl-16 pr-2">
      <div class="pr-14 py-16 flex gap-8">
        <t-button
          v-for="option in statusOptions"
          :key="option.value"
          :theme="filterParams.status === option.value ? 'primary' : 'default'"
          class="status-option-box"
          :class="{ active: filterParams.status === option.value }"
          @click="onChangeStatus(option.value)"
        >
          {{ option.label }}
        </t-button>
      </div>
      <RTable
        class="platform-activity-apply-list-table"
        :table="table"
        @change="onTableChange"
      >
        <template #empty>
          <REmpty />
        </template>
      </RTable>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, reactive, ref } from 'vue';
import { RTable, REllipsisTooltip, REmpty } from '@rk/unitPark';
import dayjs from 'dayjs';
import to from 'await-to-js';
import { getPlatform } from '@/utils/auth';
import { listDPActivities } from '@/api/activity/platform';
import { useNavigate } from '@/views/activity/hooks/useNavigate';
import ApplyListOperate from '@/views/activity/platform-activity-admin/components/ApplyListOperate.vue';
import emitter from '@/utils/MittBus';

const emit = defineEmits(['back', 'change']);

const isLoading = ref(true);

const teamId = localStorage.getItem('honorteamid');

const table = ref({
  attrs: {
    loading: isLoading,
    rowKey: 'id',
    onRowClick: ({ row }) => openActivityDetail(row),
  },
  columns: [{
    title: '活动',
    width: 456,
    cell: (h, { row }) => <div class="flex gap-12">
      <img class="w-72 h-72 rounded-8" src={row.assetUrl} alt=""/>
      <div class="flex flex-col gap-4">
        <REllipsisTooltip line-number={1} text={row.subject} />
        <div class="flex items-center gap-8 text-[#828DA5]">
          <iconpark-icon className="text-20" name="icondate"></iconpark-icon>
          <span>{dayjs(row.durationStart * 1000).format('MM-DD HH:mm')} ~ {dayjs(row.durationEnd * 1000).format('MM-DD HH:mm')}</span>
        </div>
        <div class="flex items-center gap-8 text-[#828DA5]">
          <iconpark-icon className="text-20" name="iconpositioning"></iconpark-icon>
          <REllipsisTooltip line-number={1} text={row.address} />
        </div>
      </div>
    </div>,
  }, {
    title: '申请人',
    width: 198,
    cell: (h, { row }) => <div class="flex items-center gap-8">
      <div class="px-4 rounded-4 bg-[#E0F2E5] text-[#499D60] text-12 leading-20">个人</div>
      <div>{row.promoterName}</div>
    </div>,
  }, {
    title: '申请时间',
    width: 162,
    cell: (h, { row }) => <div>{dayjs(row.appliedAt * 1000).format('YYYY-MM-DD HH:mm')}</div>,
  }, {
    title: '操作',
    width: 128,
    cell: (h, { row }) => <ApplyListOperate row={row} onChange={() => onDataChange()} />,
  }],
  list: [],
  pagination: {
    pageSize: 10,
    current: 1,
    total: 0,
  },
});

const statusOptions = [
  {
    label: '全部',
    value: 'PLATFORM_STATUS_DEFAULT',
  },
  {
    label: '待审核',
    value: 'PLATFORM_STATUS_PENDING_REVIEW',
  },
  {
    label: '已同意',
    value: 'PLATFORM_STATUS_AGREED',
  },
  {
    label: '已拒绝',
    value: 'PLATFORM_STATUS_REJECTED',
  },
  {
    label: '已取消',
    value: 'PLATFORM_STATUS_CANCELLED',
  },
  {
    label: '已失效',
    value: 'PLATFORM_STATUS_INVALIDED',
  },
];

const filterParams = reactive({
  status: 'PLATFORM_STATUS_DEFAULT',
});

const onTableChange = async ({ pageInfo }, name) => {
  if (name === 'table') {
    table.value.pagination.current = pageInfo.current;
    table.value.pagination.pageSize = pageInfo.pageSize;
    table.value.pagination.total = pageInfo.total;
  } else {
    table.value.pagination.current = 1;
    table.value.pagination.pageSize = 10;
  }

  loadData();
};

const onChangeStatus = (status) => {
  filterParams.status = status;

  loadData();
};

const onDataChange = () => {
  loadData();

  emit('change');
};

const loadData = async () => {
  isLoading.value = true;

  const [error, res] = await to(listDPActivities({
    'pagination.size': table.value.pagination.pageSize,
    'pagination.number': table.value.pagination.current,
    teamId,
    fromPage: 1,
    ...filterParams,
  }));

  if (error) {
    isLoading.value = false;
    return;
  }

  isLoading.value = false;

  table.value.list = res.data.data.activities;

  // 加载申请列表数据时刷新活动红点，保证红点数量和列表数据同步
  emitter.emit('refreshActivityRedNum');
};

const { openParticipantDetailTab } = useNavigate();

// 打开活动详情tab
const openActivityDetail = (row) => {
  // 使用平台身份进入
  const platformCardId = getPlatform().find((item) => item.teamId === teamId)?.uuid;

  openParticipantDetailTab({
    activity: row,
    type: 'WORKBENCH',
    teamId,
    cardId: platformCardId,
  });
};

onMounted(() => {
  loadData();
});
</script>

<style lang="less" scoped>
.wrapper-header{
 border-bottom: 1px solid #ECEFF5;
}

.status-option-box{
  height: 32px;
  width: 80px;
  font-weight: 600;

  &.active{
    pointer-events: none;
  }
}

:deep(.platform-activity-apply-list-table){
  .header {
    padding-right: 14px;
  }

  .RK-Table {
    height: calc(100vh - 175px);
    overflow: overlay;
    padding-right: 14px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }

  .t-table {
    .t-table__body tr:not(.t-table__empty-row) {
      &:hover {
        background-color: #f3f6fa !important;
        cursor: pointer;
      }
    }

    td:last-child {
      padding: 8px !important;
    }
  }

  .t-table__pagination{
    padding-bottom: 0 !important;
  }
}
</style>
