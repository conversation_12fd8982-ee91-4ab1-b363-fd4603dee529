/* eslint-disable import/no-mutable-exports */
import { ringkolRequestApi, client_orgRequest } from "@/utils/apiRequest";
import { recentSearch } from "../model/recent";

/**
 * 根据openId获取内部成员和外部身份卡数据
 * @param params openId
 * @returns
 */
export function staffOpenid(params = {openId:''}) {
  return client_orgRequest({
    method: "get",
    url: "/common/staff-openId",
    params,
  });
}

export function recentContactList(data: recentSearch) {
  return ringkolRequestApi({
    method: "post",
    url: "/im/v1/pair/listPairs",
    data,
  });
}
/**
 * 获取当前用户的平台用户数据
 * @param data type string 可选  成员所属类型，使用应用标识区分，如商协：member，城市：government
              team_id string  组织标识 可选
 * @returns
 */
export function getPlatformWithTeams(data?:{type?:string,team_id?:string}) {
  return client_orgRequest({
    method: "post",
    url: "/member/platform/getPlatformWithTeams",
    data,
  });
}
