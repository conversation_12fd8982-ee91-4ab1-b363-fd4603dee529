<script setup lang="ts">
import { useSquareStore } from '@/views/square/store/square';
import AnnualFeeDialog from '@/views/square/components/annual-fee/AnnualFeeDialog.vue';
import useUpgradeEntry from './useUpgradeEntry';

const store = useSquareStore();
const {
  feeInfo,
  showUpgradeBtn,
  isExpired,
  adventDays,
  isTrial,
  showEntry,
  renewalVisible,
  upgradeLoaded,
  checkIsExpired,
  fetchFeeInfo,
} = useUpgradeEntry();

defineExpose({ refresh: fetchFeeInfo });
</script>

<template>
  <div v-if="showEntry" class="upgrade-entry" :class="{ trial: isTrial, expired: isExpired }">
    <template v-if="isTrial">
      <div v-if="isExpired" class="expired"><iconpark-icon name="icon-vip-fill" class="icon" /> 体验已结束</div>
      <template v-else>体验剩余 {{ adventDays }}</template>
    </template>
    <template v-else>
      <template v-if="isExpired">
        <iconpark-icon name="icon-vip-fill" class="w-24 h-24 mr-4" />
        <span class="max-w-70 line-1 text-[#944711]">
          {{ feeInfo.annualFeeDetail?.package?.name || $t('square.annualFee.basicVersion') }}{{ $t('album.expired') }}
        </span>
        <span class="text-[#944711]">
          ({{ $t('album.expired') }})
        </span>
      </template>
      <span v-else class="max-w-140 line-1">{{ feeInfo.annualFeeDetail?.package?.name || $t('square.annualFee.basicVersion') }}</span>
    </template>

    <template v-if="showUpgradeBtn">
      <div v-if="isTrial" class="flex items-center w-46 h-full" @click="renewalVisible = true">
        <div class="btn-upgrade">购买</div>
        <!-- <SvgIcon name="square-trapezium" class="trapezium" /> -->
        <i class="i-svg:trapezium trapezium" />
      </div>
      <div v-else-if="isExpired" class="flex items-center w-46 h-full" @click="renewalVisible = true">
        <div class="btn-upgrade">续费</div>
        <!-- <SvgIcon name="square-trapezium" class="trapezium" /> -->
        <i class="i-svg:trapezium trapezium" />
      </div>
      <t-link
        v-else
        theme="primary"
        hover="color"
        class="font-600"
        @click="checkIsExpired"
      >
        {{ isTrial ? '购买' : $t('square.annualFee.upgrade') }}
      </t-link>
    </template>

    <AnnualFeeDialog
      v-if="renewalVisible"
      v-model="renewalVisible"
      :square-id="store.squareId"
      :team-id="store.squareInfo?.organizationProfile?.teamId"
      :upgrade="!isTrial && !isExpired"
      @upgrade-loaded="upgradeLoaded"
    />
  </div>
</template>

<style scoped lang="less">
.upgrade-entry {
  display: flex;
  width: 208px;
  padding: var(--select-kyy-radius-select-option, 8px) 16px;
  justify-content: space-between;
  align-items: flex-start;
  border-radius: var(--select-kyy-radius-select-option, 8px);
  background: var(--bg-kyy-color-bg-deep, #F5F8FE);
  color: var(--text-kyy-color-text-2, #516082);
  margin: 0 auto 8px;
  font-size: 14px;
  font-weight: 600;

  &.trial, &.expired {
    position: relative;
    height: 40px;
    padding: 0px var(--checkbox-kyy_radius_checkbox, 2px) 0px 8px;
    justify-content: flex-start !important;
    align-items: center;
    border-radius: 6px;
    background: linear-gradient(125deg, #FFF6E5 14.27%, #FDD2C1 98.59%);
    .icon {
      font-size: 24px;
    }
    .expired {
      color: #944711;
    }
    .btn-upgrade {
      position: absolute;
      right: 0;
      color: #FFFAF7;
      font-size: 14px;
      line-height: 22px; /* 157.143% */
      z-index: 2;
      margin-right: 6px;
      cursor: pointer;
    }
    .trapezium {
      position: absolute;
      right: 2px;
      z-index: 1;
      // width: 46px;
      // height: 36px;
      font-size: 46px;
      cursor: pointer;
    }
  }
}
</style>
