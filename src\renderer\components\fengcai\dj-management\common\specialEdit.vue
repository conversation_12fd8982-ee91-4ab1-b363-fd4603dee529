<template>
  <div class="detail-review">
    <t-dialog
      v-model:visible="visible"
      :header="etype === 1 ? t('banch.zltj') : '编辑专栏'"
      :on-close="onCancel"
      :destroy-on-close="true"
      width="400px"
      :confirm-btn="{
        disabled: submitFormDisabled,
        content: t('notice.qd'),
      }"
      @confirm="onConfirm"
    >
      <div class="detail-review-hex">
        <t-form
          ref="form"
          :rules="FORM_RULES"
          :data="formData"
          :colon="false"
          scroll-to-first-error="smooth"
          label-align="top"
          @submit="onSubmit"
        >
          <t-form-item :label="t('banch.zlname')" name="name" requiredMark>
            <t-input
              v-model="formData.title"
              :maxlength="8"
              show-limit-number
              clearable
              :placeholder="t('notice.pin')"
            />
          </t-form-item>
          <t-form-item :label="t('banch.zsqd')" name="columns" requiredMark>
            <div style="display: flex; gap: 24px">
              <t-checkbox
                v-for="item in formData.channels.filter((item) => item.show)"
                :key="item.name"
                v-model="item.checked"
                >{{ item.name }}</t-checkbox
              >
            </div>
          </t-form-item>
        </t-form>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import { MessagePlugin } from "tdesign-vue-next";
import { columnsCreate, columnsModify } from "@renderer/api/fengcai/manage";
import { getAppsState } from "@renderer/views/niche/apis/create";
const { t } = useI18n();
const visible = ref(false);
const channelsRef = ref(null);

const nicheDetail = ref(null);
const etype = ref(1);
const formData = reactive({
  id: 0,
  title: "",
  channels: [
    {
      name: t("banch.square"),
      value: 0,
      checked: false,
      show: false,
    },
    {
      name: t("banch.banch"),
      checked: false,
      value: 1,
      show: true,
    },
    {
      name: t("banch.member"),
      checked: false,
      value: 2,
      show: false,
    },
  ],
});
const FORM_RULES = {
  title: [{ required: true, message: t("niche.type_rq") }],
  channels: [{ required: true, message: t("niche.title_rq") }],
};
const onSubmit = () => {};
const submitFormDisabled = computed(
  () => !formData.title || !formData.channels.filter((item) => item.show).some((che) => che.checked),
);
const chanOpen = (type, data?) => {
  etype.value = type;
  visible.value = true;
  formData.title = "";
  getAppAuth(data?.channels || []);
  if (type === 2) {
    formData.title = data.title;
    formData.id = data.id;
  }
};

const teamId = ref(localStorage.getItem("workBenchTeamid"));
const getAppAuth = async (cdata?) => {
  const res = await getAppsState(teamId.value);
  const { data } = res;
  formData.channels[0].show = data.data?.square;
  formData.channels[0].checked = cdata.includes(0);
  formData.channels[1].checked = cdata.includes(1);
  formData.channels[2].show = data.data?.government || data.data?.member || data.data?.cbd || data.data?.association|| data.data?.uni;
  formData.channels[2].checked = cdata.includes(2);
};

const onConfirm = () => {
  console.log("onConfirm");
  editChannelReq();
};
const onCancel = () => {
  visible.value = false;
};

const cruscc = () => {
  MessagePlugin.success(etype.value === 2 ? t("banch.bianjic") : "新建成功");
  visible.value = false;
  emits("delist-succ");
};
const editChannelReq = () => {
  const params = {
    id: formData.id,
    title: formData.title,
    channels: formData.channels
      .filter((item) => item.checked)
      .map((item) => item.value)
      .toString(),
  };
  if (etype.value === 2) {
    columnsModify(params).then((res: any) => {
      console.log(res);
      if (res.data?.code === 0) {
        cruscc();
      }
    });
  } else {
    columnsCreate(params).then((res: any) => {
      console.log(res);
      if (res.data?.code === 0) {
        cruscc();
      }
    });
  }
};

const emits = defineEmits(["delist-succ"]);

defineExpose({
  chanOpen,
});
</script>

<style lang="less" scoped>
.in {
  margin-top: 12px;
  :deep(.t-textarea__inner) {
    border-color: #d5dbe4 !important;
  }
  :deep(.t-textarea__info_wrapper) {
    display: none !important;
  }
}
.detail-review-hex::-webkit-scrollbar {
  width: 4px;
}
.detail-review-hex {
  width: 100%;
  max-height: 520px;
  overflow-y: auto;
  padding-right: 6px;
}
.detail-review {
  :deep(.t-dialog__body__icon) {
    margin-right: 0 !important;
  }
}

.detail-review-hex {
  padding-right: 12px;
}
</style>

<style lang="less">
.in {
  .t-textarea__info_wrapper {
    display: none !important;
  }
}
</style>
