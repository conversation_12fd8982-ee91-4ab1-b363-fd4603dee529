import { digitalPlatformRequest, ringkolRequest as request } from '@renderer/utils/apiRequest';
import { OwnerData, OwnerRequest, PlatformType } from './models/forum';
import { CardInfo, UserInfo, Perms, CoverParams, PermRequest, AnonDetail } from './models/user';
import { AxiosResponseData } from '../model';

const Api = {
  selfCards: '/bbs_extend/platform-user/self-cards/',
  setCover: '/bbs_extend/platform-user/set-cover/',
  ownerId: '/bbs/v1/user/getOwnerId',
  perms: '/bbs/v1/user/getPermissions',
  getConfig: '/bbs/v1/user/getConfig',
  updateConfig: '/bbs/v1/user/updateConfig',
  setMyDetail: '/bbs_extend/person-info/edit',
  setRemark: '/bbs_extend/remark/set', 
  getUser: '/bbs_extend/person-info/detail',
  editUser: (platformType: string) => `/bbs_extend/person-info/edit/${platformType}`,
  getTeamsMember: '/teams/all-member',
  getStats: '/bbs/v1/user/getStats',
  anonSetup: '/bbs_extend/anonymous/create-or-update/',
  anonDetail: '/bbs_extend/anonymous/get/',
};

// 获取个人身份列表
export const getSelfCards = (
  platformType: PlatformType,
): AxiosResponseData<{
  count: number;
  has_anonymous: boolean;
  items: CardInfo[];
}> => digitalPlatformRequest.get(`${Api.selfCards}${platformType}`);

// 设置身份卡背景封面
export const setCover = (platformType: PlatformType, params: CoverParams) =>
  digitalPlatformRequest.post(`${Api.setCover}${platformType}`, params);

// 个人资料详情
export const getUserInfo = (data: {
  /** 身份卡 */
  card_id: string;
  /** 查看他人资料的时候 传他人的openid */
  openid?: string;
}) => digitalPlatformRequest.post(Api.getUser, data);

// 个人资料编辑
export const updateUserInfo = (type: string, data: UserInfo): AxiosResponseData<UserInfo> =>
  digitalPlatformRequest.post(Api.editUser(type), data);

// 设置他人备注
export const setRemark = (data: {
  /** 被设置的身份卡 */
  card_id: string;
  /** 备注 */
  remark: string;
}) => digitalPlatformRequest.post(Api.setRemark, data);

// 获取所有者 ID
export const getOwnerId = (data: OwnerRequest): AxiosResponseData<OwnerData> =>
  request.post(Api.ownerId, data, { hideMessage: true });

// 获取权限信息
export const getPerms = (params: PermRequest): AxiosResponseData<{ perms: Perms }> =>
  request.get(Api.perms, { params });

// 获取论坛配置
export const getConfig = (teamId: string) => request.get(Api.getConfig, { headers: { teamId } });

// 更新论坛配置
export const updateConfig = (data) => request.post(Api.updateConfig, data);

// 更新个人详情
export const setMyDetail = (platformType: PlatformType, data) =>
  digitalPlatformRequest.post(`${Api.setMyDetail}/${platformType}`, data);
// 获取内部员工/平台成员
export const getTeamsMember = (teamId) =>
  digitalPlatformRequest.get(Api.getTeamsMember, {
    headers: {
      teamId,
    },
  });

// 获取用户统计
export const getUserStats = (owner_id: string) => request.get(Api.getStats, { params: { owner_id } });

// 创建或者更新匿名身份
export const anonSetup = (
  platformType: PlatformType,
  data: {
    /** 匿名昵称 */
    name: string;
    /** 匿名头像 */
    avatar: string;
  },
) => digitalPlatformRequest.post(`${Api.anonSetup}${platformType}`, data);

// 获取匿名身份详情
export const getAnonDetail = (platformType: PlatformType): AxiosResponseData<AnonDetail> =>
  digitalPlatformRequest.get(`${Api.anonDetail}${platformType}`);
