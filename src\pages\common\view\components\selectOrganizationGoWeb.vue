<template>
  <t-dialog
    v-model:visible="dialog"
    class="dialogHeight560 dialogSetHeader dialogSet20240801 dialogSetFooter24 dialogSDing"
    :close-btn="false"
    :header="true"
    width="560"
  >
    <template #header>
      <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
        <div>{{ t('contacts.selectOrg') }}</div>
        <img
          style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
          src="@assets/<EMAIL>"
          @click="dialog = false"
        />
      </div>
    </template>
    <div class="outer">
      <div class="box">
        <div class="groupInfo">
          <img class="imgs" src="@assets/svg/clouddisk/icon_info.svg" />
          {{ typeFlag === 'order' ? t('banch.ddtzht') : t('banch.fptzht') }}
        </div>

        <div class="groupList">
          <div
            v-for="(item, index) in list"
            :key="index"
            class="group-item"
            :class="activeIndex === index ? 'group-item-active' : ''"
            @click="activeIndex = index"
          >
            <div class="group-title">
              <img v-if="item.logo" class="groupimgs" :src="item.logo" />
              <img v-else class="groupimgs" src="@assets/svg/clouddisk/temaavatar.svg" />
              <span>{{ item.full_name }}</span>
            </div>
            <img v-show="activeIndex === index" class="activeimg" src="@assets/img/radioButton&checkbox.svg" />
          </div>
          <REmpty v-if="list && list.length === 0" style="width: 100%" />
        </div>
      </div>
    </div>
    <template #footer>
      <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
        <t-button theme="default" class="min-80" variant="outline" @click="dialog = false">取消</t-button>
        <t-button class="min-80" @click="openWeb">{{ t('banch.qwzzht') }}</t-button>
      </div>
    </template>
  </t-dialog>
</template>
<script setup lang="ts">
// import { jumpWeb } from "@renderer/views/contacts/utils";
import lodash from 'lodash';
import { REmpty } from '@rk/unitPark';
import sdk from '@lynker-desktop/web';
import qs from 'qs';
import { teamsauth } from '../../api';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { getApiConfig } from '@axios/baseUrl';

const { t } = useI18n();
const dialog = ref(false);
const list = ref(null);
const activeIndex = ref(0);
let typeFlag: string | null = null;
let orderId: any = null;
const openWin = (id, flag) => {
  if (id) {
    orderId = id;
  }
  activeIndex.value = 0;
  typeFlag = flag;
  teamsauth(flag).then((res) => {
    list.value = res.data;
  });
  dialog.value = true;
};
const openWeb = async () => {
  const { token, env } = await sdk.getConfig();
  const openid = await sdk.getLocalStorage('openid');
  const baseUrl = getApiConfig(env)['org-web'];
  const teamId = lodash.get(list.value, `${activeIndex.value}.team_id`, undefined);
  const query = {
    token,
    openid,
    teamId,
    orderId,
  };
  const pathname = `${typeFlag}` === 'order' ? '/#/orderManagement/myOrder' : '/#/invoice/index';
  const url = `${baseUrl}${pathname}?${qs.stringify(query)}`;
  console.log(url, query);
  sdk.openBrowser(url);
  // jumpWeb(typeFlag === "order" ? "/#/orderManagement/myOrder" : "/#/invoice/index", {
  //   teamId:list.value[activeIndex.value].team_id,
  //   orderId,
  // });
};
defineExpose({
  openWin,
});
</script>
<style lang="less" scoped>
.groupList {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}
::-webkit-scrollbar {
  width: 6px;
  // background-color: #f5f5f5;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  // background-color: #e3e6eb;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  // background-color: #c8c8c8;
  background: var(--divider-kyy_color_divider_deep, #d5dbe4);
}
.outer {
  padding: 0 6px;
}
.box {
  padding: 0 18px;
  max-height: 408px;
  overflow-y: overlay;
  .groupInfo {
    display: flex;
    padding: 8px 24px;
    justify-content: start;
    align-items: center;
    border-radius: 8px;
    background: var(--kyy_color_alert_bg_bule, #eaecff);
    color: var(--kyy_color_alert_text, #1a2139);
    margin-bottom: 12px;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  .imgs {
    margin-right: 8px;
  }
}
.group-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
  height: 40px;
  padding: 8px;
  width: 100%;
}
.group-item:hover {
  background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
}
.group-item-active {
  background: var(--bg-kyy_color_bg_list_foucs, #e1eaff);
}
.group-title {
  display: flex;
  align-items: center;
  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.groupimgs {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 50%;
  overflow: hidden;
}
.activeimg {
  width: 20px;
  height: 20px;
}
</style>
