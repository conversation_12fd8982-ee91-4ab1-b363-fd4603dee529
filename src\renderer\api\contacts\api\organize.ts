import { client_orgRequest, ringkolRequestApi } from "@/utils/apiRequest";
import {
  teamId,
  pairsSearch,
  sms,
  createTeam,
  joinTeam,
  emailCode,
  checkEmailCode,
  smsApplyModel,
  CheckDepartment,
  PlatformUserParam
} from "../model/organize";
import { followSearch } from "../model/follow";

export function marketClassifytree(data) {
  let area = JSON.parse(window.localStorage.getItem("profile")).area;
  if (data) {
    area = data;
  }
  return client_orgRequest({
    method: "get",
    url: `/market-classify/tree?region=${area}`,
  });
}
export function marketClassify(data) {
  let area = JSON.parse(window.localStorage.getItem("profile")).area;
  if (data) {
    area = data;
  }
  return client_orgRequest({
    method: "get",
    // url: `/market-classify?region=${area}`,
    url: `/market-classify?region=${area}`,
  });
}
export function marketClassifyDiscovery(params) {
  return client_orgRequest({
    method: "get",
    url: `/market-classify/discovery`,
    params,
  });
}
export function getOrganizeList() {
  return client_orgRequest({
    method: "get",
    url: "/addressbook/organize",
  });
}

export function getExternalList(params: { teamId: string }) {
  return client_orgRequest({
    method: "get",
    url: "/external/team",
    params,
  });
}

export function getExternalTreeList(params: { teamId: string }) {
  return client_orgRequest({
    method: "get",
    url: "/external/staffs/treeList",
    params,
  });
}

export function getApplyList() {
  return client_orgRequest({
    method: "get",
    url: "/staffs-apply/list",
  });
}

export function getDepartmentList(params: teamId) {
  return client_orgRequest({
    method: "get",
    url: "/addressbook/organize/departments",
    params,
  });
}

export function getPlatformUser(params: PlatformUserParam) {
  return client_orgRequest({
    method: "get",
    url: "/addressbook/platform/user",
    params,
  });
}
export function getPlatformAdmin(params: PlatformUserParam) {
  return client_orgRequest({
    method: "get",
    url: "/addressbook/platform/admin",
    params,
  });
}
export function getPlatformAdminList(params?) {
  return client_orgRequest({
    method: "get",
    url: "/platform/admin/list",
    params,
  });
}

export function getAllDepartmentList(params: {
  teamId: string;
  defaultParent: number;
}) {
  return client_orgRequest({
    method: "get",
    url: "/staffs/staffs-with-position",
    params,
  });
}

export function pairsList(params?: followSearch) {
  return ringkolRequestApi({
    method: "get",
    url: "/im/v1/friend/listFriends",
    params:{'page.size': 999,'page.number': 1,...params},
  });
}

export function getSms(data: sms) {
  return client_orgRequest({
    method: "post",
    url: "/validator/sms/create",
    data,
  });
}

export function createOrg(data: createTeam) {
  return client_orgRequest({
    method: "post",
    url: "/teams/create",
    data,
  });
}

export function checkCreate() {
  return client_orgRequest({
    method: "get",
    url: "/teams/check-create",
  });
}

export function teamCount() {
  return client_orgRequest({
    method: "get",
    url: "/teams/owner-count",
  });
}

export function searchOrg(params: { keyword: string }) {
  return client_orgRequest({
    method: "get",
    url: "/teams/search",
    params,
  });
}

export function searchOrgList(params: { keyword: string }) {
  return client_orgRequest({
    method: "get",
    url: "/teams/search-list",
    params,
  });
}

export function checkJoinOrg(params: { teamId?: string; keyword?: string }) {
  return client_orgRequest({
    method: "get",
    url: "/teams/check-join",
    params,
  });
}

export function joinOrg(data: joinTeam) {
  return client_orgRequest({
    method: "post",
    url: "/teams/join",
    data,
  });
}

export function ocrBankcard(url) {
  return client_orgRequest({
    method: "post",
    url: "/ocr/bankcard",
    data: {
      url,
    },
  });
}

export function getEmailSms(data: emailCode) {
  return client_orgRequest({
    method: "post",
    url: "/validator/mail/create",
    data,
  });
}

export function checkEmailSms(data: checkEmailCode) {
  return client_orgRequest({
    method: "post",
    url: "/validator/mail/check",
    data,
  });
}

export function staffCancelApply(data: { id: number }) {
  return client_orgRequest({
    method: "post",
    url: "/staffs-apply/cancel",
    data,
  });
}

export function applyReviewList(params: { page: number; pageSize: number; teamId: string }) {
  return client_orgRequest({
    method: "get",
    url: "/staffs-apply/review-list",
    params,
  });
}

export function staffsReview(data: { ids: Array<number>; agree: boolean }) {
  return client_orgRequest({
    method: "post",
    url: "/staffs-apply/review",
    data,
  });
}

export function teamSetting(params: { type: number; departmentId?: number }, teamId: string) {
  return client_orgRequest({
    method: "get",
    url: "/teams/setting",
    params,
    headers: {
      teamId,
    },
  });
}

export function authorityCheck(params: { items: Array<string> }, teamId: string) {
  return client_orgRequest({
    method: "get",
    url: "/teams/authority/check",
    params,
    headers: {
      teamId,
    },
  });
}

export function smsApply(data: smsApplyModel, teamId: string) {
  return client_orgRequest({
    method: "post",
    url: "/staffs-apply/sms-apply",
    data,
    headers: {
      teamId,
    },
  });
}

export function refreshLink(params: { departmentId?: number }, teamId: string) {
  return client_orgRequest({
    method: "get",
    url: "/staffs-apply/refresh-link",
    params,
    headers: {
      teamId,
    },
  });
}

export function ready() {
  return client_orgRequest({
    method: "get",
    url: "/ready",
  });
}

export function teamAuth(data, teamId: string) {
  return client_orgRequest({
    method: "post",
    url: "/teams/auth",
    data,
    headers: {
      teamId,
    },
  });
}

export function getTeamAuth(teamId: string) {
  return client_orgRequest({
    method: "get",
    url: "/teams/auth",
    headers: {
      teamId,
    },
  });
}

export function getTeams(params: { teamId: string }) {
  return client_orgRequest({
    method: "get",
    url: "/teams",
    params
  });
}

// 政府认证
export function teamAuthGovernment(data, teamId: string) {
  return client_orgRequest({
    method: "post",
    url: "/teams/authGovernment",
    data,
    headers: {
      teamId,
    },
  });
}

export function getTeamLicenceText(teamId: string, imgUrl: string) {
  return client_orgRequest.get(`/teams/licence`, { params: { url: imgUrl }, headers: { teamId } });
}

export function getDiscoveryList(params) {
  return client_orgRequest({
    method: "get",
    // url: `/market-classify?region=${area}`,
    url: `/niche/business/discovery/list`,
    params,
  });
}
export function getDiscoveryListNew(params) {
  return client_orgRequest({
    method: "get",
    // url: `/market-classify?region=${area}`,
    url: `/niche/discovery/list`,
    params
  });
}
// 获取是否有权限访问组织岗位
export function getDepartmentPermission(params: CheckDepartment) {
  return client_orgRequest({
    method: "get",
    url: "/addressbook/organize/check/department",
    params,
  });
}

// 获取认证书下载
export function getTeamsRegionTemplate() {
  return client_orgRequest({
    method: "post",
    url: "/teams/getTeamsRegionTemplate",
  });
}
