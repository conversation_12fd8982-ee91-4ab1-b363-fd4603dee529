<template>
  <div class="order-page promotionChannels">
    <!-- 状态标签 -->
    <orderTabs ref="orderTabsRef" :tabs="tabs" :tab-current-index="tabCurrentIndex" @tab-change="tabChange">
      <template v-if="allcount" #btns>
        <t-button variant="outline" class="weight600" theme="primary" @click="addMuChannelRun">批量添加渠道</t-button>
        <t-button theme="primary" class="weight600" style="margin-left: 8px" @click="addGoodsRun">
          添加推广商品
        </t-button>
      </template>
    </orderTabs>
    <!-- 搜索框 -->
    <div class="search-bar">
      <div class="left">
        <t-input v-model="title" style="width: 304px" clearable placeholder="搜索商品名称" @change="titleChange">
          <template #prefix-icon>
            <iconpark-icon name="iconsearch" class="icon20" />
          </template>
        </t-input>
        <superSearch
          :filter-params="filterParams"
          :active="filterParamsValue.length > 0"
          @search="search"
          @reset="reset"
        />
      </div>
      <div class="right">
        <template v-if="selectedRowKeys.length">
          <div>已选择 {{ selectedRowKeys.length }} 条</div>
          <div class="line"></div>
        </template>
        <template v-else>
          <template v-if="totalItems">
            <div>共 {{ totalItems }} 条</div>
            <div class="line"></div>
          </template>
        </template>
        <div class="tipb">
          <t-tooltip
            :show-arrow="true"
            theme="light"
            placement="bottom-right"
            overlay-class-name="custom-tooltip-class"
          >
            <template #content>
              <div style="width: 304px; padding: 8px; color: #516082">
                <p>
                  1、商品添加后会发送到对应的数字平台上进行审核，审核通过后才会展示在其【数字平台-市场】上，商品在其数字平台上售卖后，对应分佣指定金额给其作为报酬；
                </p>
                <p style="margin-top: 12px">2、商品状态为“已售卖、部分售罄”才会展示在其数字平台中，其他状态均不展示</p>
              </div>
            </template>
            推广说明
            <iconpark-icon name="iconhelp" class="icon" style="font-size: 20px; color: #828da5" />
          </t-tooltip>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-box">
      <div v-if="filterParamsValue.length" class="filter-bar">
        <div class="filter-res">
          <div class="tit">高级筛选结果：</div>
          <div v-for="item in filterParamsValue" :key="item.key" class="stat te">
            <span>
              <span>{{ item.label }}：</span>
              <span v-if="item.type === 'input'">{{ item.value }}</span>
              <span v-if="item.type === 'date'">{{ item.value[0] }} ~ {{ item.value[1] }}</span>
              <span v-if="item.type === 'select'">{{ getValueByOpt(item.options, item.value) }}</span>
            </span>
            <span class="close2" @click="clearFilter(item.key)">
              <iconpark-icon name="iconerror" style="font-size: 20px" />
            </span>
          </div>
          <div class="icon" @click="clearAllFilters">
            <iconpark-icon name="icondelete" style="font-size: 20px; color: #4d5eff" />
            <a>清空</a>
          </div>
        </div>
      </div>

      <t-table
        :data="tableData"
        :columns="columns"
        row-key="spuId"
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
        @row-click="onRowClick"
      >
        <template #orderNumber="{ row }">
          <div class="order-info">
            <img :src="row.imgUrls[0]" alt="商品图片" class="goods-image" @click="viewImage(row)" />
            <t-tooltip :show-arrow="true" :content="row.title">
              <div class="goods-name">{{ row.title }}</div>
            </t-tooltip>
          </div>
        </template>
        <template #status="{ row }">
          <div class="status-box">
            <div class="status-tag" :class="`status-tag${statusText2(row.viewState).value}`"></div>
            <span>{{ statusText2(row.viewState)?.label }}</span>
          </div>
        </template>
        <template #channel="{ row }">
          <a v-if="row.channelCount" @click.stop="showChannel(row)">
            {{ row.channelCount ? row.channelCount + '个' : '请添加商品渠道' }}
          </a>
          <span v-else style="color: #828da5">请添加商品渠道</span>
        </template>
        <template #type="{ row }">
          <span>{{ typeText[row.typ] }}</span>
        </template>
        <template #operation="{ row }">
          <template v-if="row.channelCount">
            <div class="abtn" @click.stop="manch(row)">管理渠道</div>
          </template>
          <div v-else style="display: flex; gap: 8px">
            <div
              v-if="['PRODUCT_VIEW_STATE_PARTIAL_SOLD_OUT', 'PRODUCT_VIEW_STATE_SALE'].includes(row.viewState)"
              class="abtn"
              @click.stop="addChannelRun(row)"
            >
              添加渠道
            </div>
            <t-popconfirm placement="bottom-right" @confirm="delcom(row)">
              <template #icon>
                <iconpark-icon name="iconinfo-g4lj3da7" style="font-size: 20px; margin-right: 8px; margin-top: 4px" />
              </template>
              <template #content>
                <div class="del-box">
                  <div class="del-box-t">
                    <!-- <iconpark-icon name="iconwarning" style="font-size: 20px" /> -->
                    <span>是否删除该渠道推广，确定移除？</span>
                  </div>
                  <!-- <div class="del-box-btns">
                    <t-button theme="defauel" variant="outline" style="width: 52px; height: 24px" @click="delcal">
                      取消
                    </t-button>
                    <t-button theme="primary" style="width: 52px; height: 24px" @click="delcom(row)">确定</t-button>
                  </div> -->
                </div>
              </template>
              <div class="abtn" @click.stop="delRun2">移除</div>
            </t-popconfirm>
            <!-- <t-popup placement="bottom-right"  v-model:visible="popupvisible " trigger="click">
              <template #content>
                <div class="del-box">
                  <div class="del-box-t">
                    <iconpark-icon name="iconwarning" style="font-size: 20px" />
                    <span>是否删除该渠道推广，确定移除？</span>
                  </div>
                  <div class="del-box-btns">
                    <t-button theme="defauel" variant="outline" style="width: 52px; height: 24px" @click="delcal">
                      取消
                    </t-button>
                    <t-button theme="primary" style="width: 52px; height: 24px" @click="delcom(row)">确定</t-button>
                  </div>
                </div>
              </template>
              <div class="abtn" @click.stop="delRun2(row)">移除</div>
            </t-popup> -->
          </div>
        </template>
        <template #empty>
          <div class="empty-box">
            <REmpty v-if="!allcount || allcount === 0" name="no-goods" tip="还没有添加推广商品，快去添加吧~" />
            <REmpty
              v-else
              :name="filterParamsValue.length || title ? 'no-result' : 'no-data'"
              :tip="filterParamsValue.length || title ? '搜索无结果' : '暂无数据'"
            />
            <t-button v-if="!allcount || allcount === 0" theme="primary" style="margin-left: 45px" @click="addGoodsRun">
              添加推广商品
            </t-button>
          </div>
        </template>
      </t-table>
      <div v-if="totalItems && totalItems > 10" class="pagination">
        <t-pagination
          :total="totalItems"
          :page-size="pageSize"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          show-jumper
          @change="handlePageChange"
        />
      </div>
    </div>
    <!-- 分页 -->

    <channel ref="channelRef" />
    <addChannel ref="addChannelRef" @succ="refdataRun" />

    <adSelectShop
      ref="adadSelectShopRef"
      :only-promotion-enabled="false"
      :store-id="storeId"
      table-type="shop"
      change-type="multiple"
      :disable-ids="disableIds"
      @change-shop="changeShop"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import orderTabs from '../orders/components/orderTabs.vue';
import superSearch from '../orders/components/superSearch.vue';
import { MessagePlugin } from 'tdesign-vue-next';
import channel from './components/channel.vue';
import addChannel from './components/addChannel.vue';
import { deletePromotionProduct, listPromotionProducts, batchAddPromotionProducts } from './apis';
import sdk from '@lynker-desktop/web';
import { useShopStore } from '@pages/shop/store/index';
import adSelectShop from '@pages/shop/components/adSelectShop.vue';
import { REmpty } from '@rk/unitPark';

// const apiUrl = '/api/orders';
const tabs = ref([
  { value: 9999, label: '全部', count: 0, viewState: 'PRODUCT_VIEW_STATE_UNKNOWN' },
  { value: 1, label: '已售卖', count: 0, viewState: 'PRODUCT_VIEW_STATE_SALE' },
  { value: 2, label: '部分售罄', count: 0, viewState: 'PRODUCT_VIEW_STATE_PARTIAL_SOLD_OUT' },
  { value: 3, label: '已售罄', count: 0, viewState: 'PRODUCT_VIEW_STATE_SOLD_OUT' },
  { value: 4, label: '已下架', count: 0, viewState: 'PRODUCT_VIEW_STATE_OFF_SHELF' },
  { value: 5, label: '已删除', count: 0, viewState: 'PRODUCT_VIEW_STATE_DELETED' },
]);
const tabCurrentIndex = ref(0);
const filterParams = ref([
  {
    key: 'productType',
    label: '商品类型',
    type: 'select',
    placeholder: '请选择商品类型',
    value: '',
    options: [
      { label: '核销型', value: 'PRODUCT_TYPE_CHECK' },
      { label: '零售型', value: 'PRODUCT_TYPE_RETAIL' },
    ],
  },
]);
const shopStore = useShopStore();
const selectedRowKeys = ref([]);
const disStateArr = ['PRODUCT_STATE_SOLD_OUT', 'PRODUCT_STATE_OFF_SHELF', 'PRODUCT_STATE_DELETED'];
const columns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    width: 48,
    disabled: ({ row }) => disStateArr.includes(row.state),
  },
  { title: '商品信息', colKey: 'orderNumber', width: 360 },
  { title: '商品类型', colKey: 'type', width: 120, ellipsis: true },
  { title: '商品状态', colKey: 'status', width: 120, ellipsis: true },
  { title: '推广渠道', colKey: 'channel', width: 120, ellipsis: true },
  { title: '操作', colKey: 'operation', width: 136 },
];
const typeText = {
  PRODUCT_TYPE_CHECK: '核销型',
  PRODUCT_TYPE_RETAIL: '零售型',
};
const title = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);
const allcount = ref(0);
const tableData = ref([]);

const rehandleSelectChange = (keys) => {
  console.log('Selected row keys:', keys);
  selectedRowKeys.value = keys;
};

onMounted(() => {
  fetchData();
});

const refdataRun = () => {
  selectedRowKeys.value = [];
  fetchData();
};
const tabChange = (index, value) => {
  console.log('Selected tab:', index, value);
  tabCurrentIndex.value = index;
  fetchData();
};
const orderTabsRef = ref(null);
const storeId = ref(shopStore?.store?.id);
const disableIds = ref([]);
const fetchData = async () => {
  const temp = {};
  filterParams.value.map((item) => {
    temp[item.key] = item.value;
  });
  const reqData = {
    'page.number': currentPage.value,
    'page.size': pageSize.value,
    title: title.value,
    viewState: tabCurrentIndex.value === 0 ? undefined : tabs.value[tabCurrentIndex.value].viewState,
    storeId: shopStore?.store?.id,
    onlyPromotionEnabled: true,
    ...temp,
  };
  console.log(reqData, 'reqData');
  try {
    const res = await listPromotionProducts(reqData);
    console.log(res, 'res');
    if (res?.data) {
      tableData.value = res?.data?.products || [];
      totalItems.value = res?.data?.total || 0;
      disableIds.value = res?.data?.products.promotionEnabledAt;
    }
    // const stParams = {
    //   ...reqData,
    //   storeId: 2,
    // };
    // const statistics = await statPromotionProducts(stParams);
    // console.log(statistics, 'statistics');
    // console.log(statistics?.data, 'statistics');
    const statistics = res?.data?.stateStats;
    if (statistics) {
      console.log(tabs.value, 'tabs.value');
      tabs.value.forEach((item) => {
        statistics.forEach((stat) => {
          if (item.viewState === stat.viewState) {
            item.count = stat.count;
            allcount.value += item.count;
          }
        });
      });
      console.log(tabs.value, 'tabs.value');
      orderTabsRef.value.updateTagPosition();
    }
  } catch (error) {
    MessagePlugin.error(error.response?.data?.message || '请求失败');
  }
};
const titleChange = () => {
  currentPage.value = 1;
  fetchData();
};
// const statusText = (status) => {
//   const statusMap = {
//     1: { label: '已售卖', value: 1, enum: 'PRODUCT_STATE_SALE' },
//     2: { label: '已下架', value: 2, enum: 'PRODUCT_STATE_OFF_SHELF' },
//     3: { label: '部分售罄', value: 3, enum: 'PRODUCT_STATE_PARTIAL_SOLD_OUT' },
//     4: { label: '已售罄', value: 4, enum: 'PRODUCT_STATE_SOLD_OUT' },
//     5: { label: '已删除', value: 5, enum: 'PRODUCT_STATE_DELETED' },
//   };
//   const enumToNumberMap = {
//     PRODUCT_STATE_SALE: 1,
//     PRODUCT_STATE_OFF_SHELF: 2,
//     PRODUCT_STATE_PARTIAL_SOLD_OUT: 3,
//     PRODUCT_STATE_SOLD_OUT: 4,
//     PRODUCT_STATE_DELETED: 5,
//   };
//   if (typeof status === 'number') {
//     return statusMap[status] || { label: '', value: null, enum: null };
//   } else if (typeof status === 'string' && enumToNumberMap[status]) {
//     const num = enumToNumberMap[status];
//     return statusMap[num];
//   } else {
//     return { label: '', value: null, enum: null };
//   }
// };
const statusText2 = (status) => {
  const statusMap = {
    1: { label: '已售卖', value: 1, enum: 'PRODUCT_VIEW_STATE_SALE' },
    2: { label: '已下架', value: 2, enum: 'PRODUCT_VIEW_STATE_OFF_SHELF' },
    3: { label: '部分售罄', value: 3, enum: 'PRODUCT_VIEW_STATE_PARTIAL_SOLD_OUT' },
    4: { label: '已售罄', value: 4, enum: 'PRODUCT_VIEW_STATE_SOLD_OUT' },
    5: { label: '已删除', value: 5, enum: 'PRODUCT_VIEW_STATE_DELETED' },
  };
  const enumToNumberMap = {
    PRODUCT_VIEW_STATE_SALE: 1,
    PRODUCT_VIEW_STATE_OFF_SHELF: 2,
    PRODUCT_VIEW_STATE_PARTIAL_SOLD_OUT: 3,
    PRODUCT_VIEW_STATE_SOLD_OUT: 4,
    PRODUCT_VIEW_STATE_DELETED: 5,
  };
  if (typeof status === 'number') {
    return statusMap[status] || { label: '', value: null, enum: null };
  } else if (typeof status === 'string' && enumToNumberMap[status]) {
    const num = enumToNumberMap[status];
    return statusMap[num];
  } else {
    return { label: '', value: null, enum: null };
  }
};
// 分页
const handlePageChange = (newPage) => {
  console.log('Current page:', newPage);
  currentPage.value = newPage.current;
  pageSize.value = newPage.pageSize;
  selectedRowKeys.value = [];
  fetchData();
};

/**
 * 筛选 start
 */
// 定义一个计算属性，返回filterParams中所有有值的的数组，需要判断类型input和select的就是'',date的就是[]
const filterParamsValue = computed(() => {
  return filterParams.value.filter((item) => {
    if (item.type === 'input' || item.type === 'select') {
      return item.value !== '' && item.value !== undefined;
    } else if (item.type === 'date') {
      return item.value.length > 0;
    }
  });
});

// 定义一个映射对象，用于存储不同类型的默认值，避免重复判断
const defaultValueMap = {
  date: [],
  input: '',
  select: '',
};
const search = (params) => {
  console.log('Search params:', params);
  filterParams.value = params;
  fetchData();
};
const reset = (params) => {
  console.log('Reset search', params);
  filterParams.value.forEach((item) => {
    if (defaultValueMap[item.type] !== undefined) {
      item.value = defaultValueMap[item.type];
    }
  });
  fetchData();
};

const clearFilter = (key) => {
  filterParams.value.forEach((item) => {
    if (item.key === key) {
      item.value = defaultValueMap[item.type];
    }
  });
  fetchData();
};

const clearAllFilters = () => {
  console.log('Clear all filters');
  filterParams.value.forEach((item) => {
    if (defaultValueMap[item.type] !== undefined) {
      item.value = defaultValueMap[item.type];
    }
  });
  fetchData();
};
const getValueByOpt = (options, value) => {
  const option = options.find((opt) => opt.value === value);
  return option ? option.label : '';
};
/**
 * 筛选 end
 */

const manch = async (row) => {
  console.log('manch', row);
  const promotion = row.viewState;
  const url = `${location.origin}/shop/index.html#/workBench/managementChannel?spuId=${row.spuId}&spuName=${row.title}&promotionViewState=${promotion}`;
  await sdk.workBench_openTabForWebview({
    title: '管理渠道',
    path_uuid: `managementChannel`,
    icon: 'https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/04aae9e/shop.png?x-oss-process=image%2Fresize%2Cs_400%2Fquality%2Cq_90',
    url,
  });
};

const delSucc = () => {
  MessagePlugin.success('删除成功');
};
const deletePromotionProductReq = async (spuId) => {
  try {
    const res = await deletePromotionProduct({ id: spuId });
    console.log(res, 'res');
    if (res?.data) {
      delSucc();
      fetchData();
    }
  } catch (error) {
    MessagePlugin.error(error.response?.data?.message || '请求失败');
  }
};
// const delRun = (row) => {
//   console.log('delRun', row);
//   const tip = DialogPlugin({
//     header: '提示',
//     body: '是否删除该渠道推广，确认移除？',
//     theme: 'info',
//     confirmBtn: {
//       content: '删除',
//     },
//     cancelBtn: '取消',
//     onConfirm: () => {
//       deletePromotionProductReq(row.spuId);
//       tip.hide();
//     },
//     onCancel: () => {
//       tip.hide();
//     },
//   });
// };
const delcom = (row) => {
  deletePromotionProductReq(row.spuId);
};
const channelRef = ref(null);
const showChannel = (row) => {
  console.log('showChannel');
  channelRef.value.openDrwer(row.spuId);
};
const addChannelRef = ref(null);
const addChannelRun = (row) => {
  console.log('addChannel');
  addChannelRef.value.openDrwer(row.spuId, false);
};
const addMuChannelRun = () => {
  console.log('addMuChannel');
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('至少勾选一个商品');
    return;
  }
  addChannelRef.value.openDrwer(selectedRowKeys.value, true);
};

const viewImage = async (row) => {
  console.log('viewImage', row);
  await sdk.previewImage({
    images: row.imgUrls,
    index: 0,
    url: row.imgUrls[0],
  });
};
const onRowClick = (e) => {
  console.log('onRowClick', e);
  viewDetail(e.row);
};
const viewDetail = async (row) => {
  const teamId = shopStore?.store?.teamId;
  const token = localStorage.getItem('main_token');
  sdk.workBench_closeTab({
    path_uuid: 'product-admin-detail',
  });
  await sdk.workBench_openTabForWebview({
    title: row.title,
    path_uuid: `product-admin-detail`,
    url: location.origin + '/shop/index.html#/product-admin-detail/' + row.spuId + `?teamId=${teamId}&token=${token}`,
    icon: 'https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/04aae9e/shop.png?x-oss-process=image%2Fresize%2Cs_400%2Fquality%2Cq_90',
  });
};

const adadSelectShopRef = ref(null);
const addGoodsRun = async () => {
  adadSelectShopRef.value?.openWin();
};
const changeShop = async (keys) => {
  try {
    const res = await batchAddPromotionProducts({ spuIds: keys, storeId: storeId.value });
    console.log(res, 'res');
    if (res?.data) {
      MessagePlugin.success('添加成功');
      fetchData();
    }
  } catch (error) {
    MessagePlugin.error(error.response?.data?.message || '请求失败');
  }
};
const delid = ref(null);
const delRun2 = (row) => {
  console.log('delRun2');
  delid.value = row.spuId;
};
</script>

<style scoped>
.order-page {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  flex: 1 0 0;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #fff);
}

.status-tabs {
}

.abtn {
  display: flex;
  padding: 4px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  border-radius: 4px;
  cursor: pointer;
  width: fit-content;
}
.abtn:hover {
  border-radius: 4px;
  background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
}
.icon20 {
  font-size: 20px;
}
.search-bar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  .left {
    display: flex;
    align-items: center;
    gap: 8px;
    .filter-box {
      display: flex;
      width: 32px;
      height: 32px;
      min-height: 32px;
      max-height: 32px;
      padding: 6px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      cursor: pointer;
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #d5dbe4);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
    }
    .filter-box:hover {
      background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
      .iconfi {
        color: #4d5eff;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    color: var(--text-kyy_color_text_1, #1a2139);
    .line {
      width: 1px;
      display: flex;
      height: 16px;
      min-width: 1px;
      max-width: 1px;
      gap: 4px;
      margin: 0 8px;
      background: var(--divider-kyy_color_divider_light, #eceff5);
    }
    .tipb {
      display: flex;
      gap: 4px;
      align-items: center;
      span {
        display: flex;
        align-items: center;
      }
    }
  }
}
.table-box {
  padding: 0 16px;
  height: calc(100vh - 146px);
  overflow-y: auto;
}
.table-box::-webkit-scrollbar {
  width: 0px;
}
.filter-bar {
  margin-bottom: 16px;
}
.filter-res {
  display: flex;
  flex-wrap: wrap;
  row-gap: 8px;
  .tit {
    color: var(--text-kyy-color-text-2, #516082);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }
  .ov-time {
    display: flex;
    min-width: 290px;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }
  .close2 {
    display: flex;
    align-items: center;
    color: #828da5;
    img {
      width: 10px;
      height: 10px;
    }
  }
  .te {
    color: var(--kyy-color-tag-text-black, #1a2139);
    cursor: pointer;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    border-radius: 4px;
    background: var(--kyy-color-tag-bg-gray, #eceff5);
    margin-right: 8px;
  }
  .stat {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }
  .kword {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }
  .icon {
    display: flex;
    margin-left: 4px;
    cursor: pointer;
    img {
      width: 14px;
      height: 14px;
      margin-top: 4px;
      margin-right: 4px;
    }
  }
}
.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 16px;
}
:deep(.t-pagination__total) {
  flex: none;
  margin-right: 16px;
  color: var(--kyy_color_pagination_text_illustrate, #516082);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.order-number {
  position: relative;
  .status-rd {
    position: absolute;
    top: -22px;
    left: -11px;
    display: flex;
    padding: 0px 4px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 0px 0px 8px 0px;
    background: #fc7c14;
    color: #fff;
    font-family: 'PingFang SC';
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 200% */
  }
}
.status-text1 {
  color: var(--warning-kyy_color_warning_default, #fc7c14);

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.status-text2 {
  color: var(--success-kyy_color_success_active, #499d60);

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.status-text3 {
  color: var(--blue-kyy_color_blue_default, #4093e0);

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.status-text4 {
  color: var(--text-kyy_color_text_2, #516082);

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.status-text5 {
}

.text-box {
  color: var(--kyy_color_modal_content, #516082);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.sele-box {
  display: flex;
  align-items: center;
  align-self: stretch;
  margin-top: 12px;
}

.order-info {
  display: flex;
  gap: 12px;
  align-items: center;
  .goods-image {
    width: 48px;
    height: 48px;
    border-radius: 4px;
    object-fit: cover;
    cursor: pointer;
  }
  .goods-name {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    flex: 1 0 0;
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    text-overflow: ellipsis;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.status-box {
  display: flex;
  align-items: center;
  gap: 12px;
  .status-tag {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
  .status-tag1 {
    background: #62bf7c;
  }
  .status-tag2 {
    background: #828da5;
  }
  .status-tag3 {
    background: #4d5eff;
  }
  .status-tag4 {
    background: #d54941;
  }
  .status-tag5 {
    background: #d54941;
  }
  .status-tag6 {
    background: #4d5eff;
  }
}
.icon20 {
  font-size: 20px;
  color: #828da5;
}
:deep(.tipb span) {
  display: flex;
  gap: 4px;
}
:deep(.t-table tr:hover) {
  background-color: #f3f6fa !important;
}
:deep(.t-table .t-table__empty-row:hover) {
  background-color: #fff !important;
}
:deep(.t-table tr) {
  cursor: pointer;
}
.del-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
  border-radius: 8px;
  /* border: 1px solid var(--kyy_color_popcomfirm_border, #d5dbe4); */
  background: var(--kyy_color_popcomfirm_bg, #fff);
  .del-box-t {
    display: flex;
    padding-top: 1px;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    color: var(--kyy_color_popcomfirm_title, #1a2139);
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
  .del-box-btns {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    gap: 8px;
    align-self: stretch;
  }
}
:deep(.t-table__th-cell-inner .t-checkbox.t-is-indeterminate .t-checkbox__input) {
  border-color: #4d5eff !important;
  color: #4d5eff !important;
  background-color: #4d5eff !important;
}
:global(.weight600) {
  font-weight: 600 !important;
}
:global(.custom-tooltip-class .t-popup__content) {
  margin-top: 8px !important;
}
.empty-box {
  margin-top: 80px;
}
</style>
