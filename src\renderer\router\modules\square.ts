import { retryRequest } from '@renderer/utils/async';
import { useSquareStore } from '@/views/square/store/square';
import { SquareType } from '@/api/square/enums';
import { i18n } from '@/i18n';

// @ts-ignore
const t = i18n.global.t;

const beforeEnter = async (to, from, next) => {
  const store = useSquareStore();

  const openid = localStorage.getItem('openid');
  const lastLoginInfo = store.lastLoginInfo[openid];
  store.needRedirectLastLogin = lastLoginInfo && lastLoginInfo.type !== SquareType.Individual;

  const timeout = 3000; // 超时时间
  const retries = 3; // 重试次数

  try {
    // 直接进入上次登录的组织广场
    if (store.needRedirectLastLogin) {
      await retryRequest(() => store.getSquareInfo(lastLoginInfo.squareId), retries, timeout);
      next();
      return;
    }

    await retryRequest(() => store.getIndividualInfo(), retries, timeout);
    next();
  } catch {
    console.error('广场信息获取失败');
    next();
  }
};

export default [
  // 广场
  {
    path: '/square',
    name: 'squareIndex',
    component: () => import('@renderer/views/square/index.vue'),
    beforeEnter,
    children: [
      {
        path: 'friend-circle',
        name: 'FriendCircle',
        component: () => import('@renderer/views/square/friend-circle/index.vue'),
        meta: {
          affix: true,
          title: t('square.square.friendCircle'),
          icon: 'iconsquare',
          // role: 'personal',
          sort: 10,
          keepAlive: true,
        },
      },
      {
        path: 'nearby-list',
        name: 'SquareNearbyList',
        component: () => import('@renderer/views/square/friend-circle/NearbyList.vue'),
        meta: {
          affix: true,
          title: t('square.square.nearby'),
          icon: 'iconpositioning',
          // role: 'personal',
          sort: 11,
          keepAlive: true,
        },
      },
      {
        path: 'friend-detail',
        name: 'SquareFriendDetail',
        component: () => import('@renderer/views/square/friend-detail/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/square/friend-circle',
          title: '',
          icon: '',
          keepAlive: true,
        },
      },
      {
        path: 'search',
        name: 'search',
        component: () => import('@renderer/views/square/search/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/square/friend-circle',
          title: t('square.search'),
          icon: '',
        },
      },
      {
        path: 'notifications',
        name: 'SquareNotification',
        component: () => import('@renderer/views/square/notifications/index.vue'),
        meta: {
          title: t('square.route.notification'),
          icon: 'iconnotice',
          sort: 20,
          keepAlive: true,
        },
      },
      {
        path: 'homepage',
        name: 'SquareHome',
        component: () => import('@renderer/views/square/homepage/index.vue'),
        meta: {
          title: t('square.route.myHome'),
          icon: 'iconhome',
          sort: 30,
          keepAlive: true,
        },
      },
      {
        path: 'ringkol-circle-preview-my',
        name: 'ringkol-circle-preview-my',
        redirect: '/square/ringkol-circle-preview',
        meta: {
          title: t('square.ringkol.circle'),
          icon: 'icon-ringkol-circle',
          // role: 'personal',
          sort: 40,
          keepAlive: true,
        },
      },
      {
        path: 'ringkol-circle-setting',
        name: 'ringkol-circle-setting',
        component: () => import('@renderer/views/square/ringkol-circle/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/square/ringkol-circle-preview-my',
          title: t('square.ringkol.circle'),
          icon: '',
        },
      },
      {
        path: 'phone-album/time-line',
        name: 'timeLine',
        component: () => import('@renderer/views/square/phone-album/timeLine.vue'),
        meta: {
          affix: true,
          title: t('square.route.album'),
          icon: 'icon-album',
          // role: 'personal',
          sort: 50,
        },
      },
      {
        path: 'info',
        name: 'SquareInfo',
        component: () => import('@renderer/views/square/homepage/SquareInfo.vue'),
        meta: {
          hidden: true,
          parentPath: '/square/homepage',
          title: t('square.route.home'),
          icon: '',
          // role: "personal",
        },
      },

      {
        path: 'drafts',
        name: 'drafts',
        component: () => import('@renderer/views/square/drafts/index.vue'),
        meta: {
          title: t('square.post.draftBox'),
          icon: 'icon20drafts',
          sort: 60,
        },
      },
      {
        path: 'setting',
        name: 'setting',
        component: () => import('@renderer/views/square/setting/index.vue'),
        meta: {
          // hidden: true,
          title: t('square.setting'),
          icon: 'iconsetting',
          role: 'personal',
          sort: 70,
        },
      },
      {
        path: 'fans',
        name: 'fans',
        component: () => import('@renderer/views/square/fans/index.vue'),
        meta: {
          title: t('square.route.fansManage'),
          icon: 'iconcategory',
          role: 'office',
          hidden: true,
        },
      },
      {
        path: 'ringkol-circle-preview',
        name: 'ringkol-circle-preview',
        component: () => import('@renderer/views/square/components/ringkol-circle/Preview.vue'),
        meta: {
          title: t('square.ringkol.setting'),
          icon: 'square-ringkol-circle',
          hidden: true,
        },
      },
      {
        path: 'publish-records',
        name: 'publish-records',
        component: () => import('@renderer/views/square/publish-records/index.vue'),
        meta: {
          affix: true,
          title: t('square.route.publishRecord'),
          icon: 'iconrelease',
          role: 'office',
        },
      },
      {
        path: 'square-promote',
        name: 'square-promote',
        component: () => import('@renderer/views/square/promote/index.vue'),
        meta: {
          affix: true,
          title: t('square.route.promote'),
          icon: 'iconadvertisement',
          role: 'office',
        },
      },
      {
        path: 'publish-article',
        name: 'publish-article',
        component: () => import('@renderer/views/square/publish-article/index.vue'),
        meta: {
          parentPath: '/square/publish-records',
          title: t('square.post.postArticle'),
          hidden: true,
          icon: '',
          role: 'office',
        },
      },
      {
        path: 'assemble',
        name: 'assemble',
        component: () => import('@renderer/views/square/assemble/SimpleIndex.vue'),
        meta: {
          title: t('square.page.assemble'),
          icon: 'iconnetwork',
          role: 'office',
        },
      },
      {
        path: 'account-info',
        name: 'account-info',
        component: () => import('@renderer/views/square/account-info/index.vue'),
        meta: {
          title: t('square.setting'),
          icon: 'iconsetting',
          role: 'office',
        },
      },
      {
        path: 'video',
        name: 'video',
        component: () => import('@renderer/views/square/video/index.vue'),
        meta: {
          title: '视频',
          hidden: true,
          role: 'personal',
        },
      },
      // {
      //   path: "demo",
      //   name: "demo",
      //   component: () => import("@renderer/views/square/Demo.vue"),
      //   meta: {
      //     title: 'demo',
      //   }
      // }
      {
        path: 'square_webview/:webviewOnlyId',
        name: 'square_webview',
        component: () => import('@renderer/views/square/webview/index.vue'),
      },
    ],
  },

  {
    path: '/square-alone',
    name: 'SquareAlone',
    component: () => import('@renderer/views/square/Alone.vue'),
    children: [
      {
        path: 'aboutour',
        name: 'aquareAboutour',
        component: () => import('@renderer/views/workBench/aboutour/index.vue'),
        meta: {
          title: '关于我们',
          icon: 'icon-album',
          role: 'office',
          keepAlive: true,
        },
      },
      {
        path: 'publication',
        name: 'Publication',
        component: () => import('@renderer/views/politics/member_number/components/ebook.vue'),
        meta: {
          title: '会刊',
          icon: 'icon-album',
          role: 'office',
          keepAlive: true,
        },
      },
      {
        path: 'honorDetail',
        name: 'aquareHonorDetail',
        component: () => import('@renderer/views/workBench/teamSting/honorDetail.vue'),
        meta: {
          title: '关于我们',
          icon: 'icon-album',
          role: 'office',
          keepAlive: false,
        },
      },
      {
        path: 'post-promotion',
        name: 'PostPromotion',
        component: () => import('@renderer/views/square/post-promotion/index.vue'),
        meta: {
          title: '推广',
          role: 'office',
          hidden: true,
          keepAlive: false,
        },
      },

      // 政策列车
      {
        path: 'square-policy-express',
        name: 'SquarePolicyExpress',
        component: () => import('@renderer/views/policy-express/index.vue'),
      },
      {
        path: 'square-policy-express-list',
        name: 'SquarePolicyExpressList',
        component: () => import('@renderer/views/policy-express/viewer/PolicyExpressList.vue'),
      },
      {
        path: 'square-policy-express-info',
        name: 'SquarePolicyExpressInfo',
        component: () => import('@renderer/views/policy-express/viewer/PolicyExpressInfo.vue'),
      },
      {
        path: 'square-policy-analysis-list',
        name: 'SquarePolicyAnalysisList',
        component: () => import('@renderer/views/policy-express/viewer/PolicyAnalysisList.vue'),
      },
      {
        path: 'square-policy-analysis-info',
        name: 'SquarePolicyAnalysisInfo',
        component: () => import('@renderer/views/policy-express/viewer/PolicyAnalysisInfo.vue'),
      },

      // 店铺首页
      {
        path: 'shop-home',
        name: 'ShopHome',
        component: () => import('@renderer/views/square/external/ShopHome.vue'),
      },

      // 1.9迭代，新增商机、名录、活动、会刊
      {
        path: 'square_digital_platform_member_rich',
        name: 'square_digital_platform_member_rich',
        component: () => import('@renderer/views/digital-platform/marketplace/home.vue'),
        // component: () => import("@renderer/views/member/member_number/components/rich-comp.vue"),
        // meta: {
        //   hidden: true,
        //   parentPath: '/digitalPlatformIndex/member_rich',
        //   // title: "会员商机",
        //   title: t('member.squarek.b'),
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_member_shop',
        name: 'square_digital_platform_member_shop',
        component: () => import('@renderer/views/digital-platform/marketplace/shophome.vue'),
        // component: () => import("@renderer/views/member/member_number/components/rich-comp.vue"),
        // meta: {
        //   hidden: true,
        //   title: '店铺',
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_member_supply',
        name: 'square_digital_platform_member_supply',
        component: () => import('@renderer/views/digital-platform/marketplace/supply.vue'),
        // meta: {
        //   hidden: true,
        //   title: '供应',
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_member_desired',
        name: 'square_digital_platform_member_desired',
        component: () => import('@renderer/views/digital-platform/marketplace/desired.vue'),
        // meta: {
        //   hidden: true,
        //   title: '需求',
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_member_rich_detail',
        name: 'square_digital_platform_member_rich_detail',
        component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
        // meta: {
        //   hidden: true,
        //   parentPath: '/digitalPlatformIndex/digital_platform_member_rich_detail',
        //   // title: "会员商机详情",
        //   title: t('member.squarek.q'),
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },

      {
        path: 'square_digital_platform_politics_shop',
        name: 'square_digital_platform_politics_shop',
        component: () => import('@renderer/views/digital-platform/marketplace/shophome.vue'),
      },
      {
        path: 'square_digital_platform_politics_rich',
        name: 'square_digital_platform_politics_rich',
        component: () => import('@renderer/views/digital-platform/marketplace/home.vue'),
      },
      {
        path: 'square_digital_platform_politics_supply',
        name: 'square_digital_platform_politics_supply',
        component: () => import('@renderer/views/digital-platform/marketplace/supply.vue'),
        // meta: {
        //   hidden: true,
        //   title: '供应',
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_politics_desired',
        name: 'square_digital_platform_politics_desired',
        component: () => import('@renderer/views/digital-platform/marketplace/desired.vue'),
        // meta: {
        //   hidden: true,
        //   title: '需求',
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_politics_rich_detail',
        name: 'square_digital_platform_politics_rich_detail',
        component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
        // meta: {
        //   hidden: true,
        //   parentPath: '/digitalPlatformIndex/digital_platform_politics_rich_detail',
        //   // title: "商机详情",
        //   title: t('member.squarek.s'),
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },

      {
        path: 'square_digital_platform_cbd_rich',
        name: 'square_digital_platform_cbd_rich',
        // component: () => import("@renderer/views/cbd/member_number/components/rich-comp.vue"),
        component: () => import('@renderer/views/digital-platform/marketplace/home.vue'),
        // meta: {
        //   hidden: true,
        //   parentPath: '/digitalPlatformIndex/cbd_rich',
        //   // title: "商机",
        //   title: t('member.squarek.b'),
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_cbd_shop',
        name: 'square_digital_platform_cbd_shop',
        component: () => import('@renderer/views/digital-platform/marketplace/shophome.vue'),
        // meta: {
        //   hidden: true,
        //   parentPath: '/digitalPlatformIndex/member_rich',
        //   // title: "会员商机",
        //   title: '店铺',
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_cbd_supply',
        name: 'square_digital_platform_cbd_supply',
        component: () => import('@renderer/views/digital-platform/marketplace/supply.vue'),
        // meta: {
        //   hidden: true,
        //   title: '供应',
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_cbd_desired',
        name: 'square_digital_platform_cbd_desired',
        component: () => import('@renderer/views/digital-platform/marketplace/desired.vue'),
        // meta: {
        //   hidden: true,
        //   title: '需求',
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_cbd_rich_detail',
        name: 'square_digital_platform_cbd_rich_detail',
        component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
        // meta: {
        //   hidden: true,
        //   parentPath: '/digitalPlatformIndex/digital_platform_cbd_rich_detail',
        //   // title: "商机详情",
        //   title: t('member.squarek.s'),
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_association_rich',
        name: 'square_digital_platform_association_rich',
        component: () => import('@renderer/views/digital-platform/marketplace/home.vue'),
        // meta: {
        //   hidden: true,
        //   parentPath: '/digitalPlatformIndex/association_rich',
        //   // title: "商机",
        //   title: t('member.squarek.b'),
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_association_shop',
        name: 'square_digital_platform_association_shop',
        component: () => import('@renderer/views/digital-platform/marketplace/shophome.vue'),
        // meta: {
        //   hidden: true,
        //   parentPath: '/digitalPlatformIndex/member_rich',
        //   // title: "会员商机",
        //   title: '店铺',
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_association_supply',
        name: 'square_digital_platform_association_supply',
        component: () => import('@renderer/views/digital-platform/marketplace/supply.vue'),
        // meta: {
        //   hidden: true,
        //   title: '供应',
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_association_desired',
        name: 'square_digital_platform_association_desired',
        component: () => import('@renderer/views/digital-platform/marketplace/desired.vue'),
        // meta: {
        //   hidden: true,
        //   title: '需求',
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_digital_platform_association_rich_detail',
        name: 'square_digital_platform_association_rich_detail',
        component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
        // meta: {
        //   hidden: true,
        //   parentPath: '/digitalPlatformIndex/digital_platform_association_rich_detail',
        //   // title: "商机详情",
        //   title: t('member.squarek.s'),
        //   icon: 'rich',
        //   role: 'personal',
        //   affix: false,
        // },
      },
      {
        path: 'square_politics_square',
        name: 'square_politics_square',
        component: () => import('@renderer/views/member/member_number/components/square-comp.vue'),
      },
    ],
  },

  // 无导航的页面
  {
    path: '/square-single',
    name: 'square-single',
    component: () => import('@renderer/views/square/layout/SingleLayout.vue'),
    beforeEnter,
    children: [
      {
        path: 'info',
        name: 'single-info',
        component: () => import('@renderer/views/square/homepage/SquareInfo.vue'),
        meta: {
          hidden: true,
          title: t('square.route.home'),
          icon: '',
        },
      },
      {
        path: 'ringkol-circle-setting',
        name: 'single-ringkol-circle-setting',
        component: () => import('@renderer/views/square/components/im/RingkolCircleGroupSetting.vue'),
        meta: {
          hidden: true,
          title: t('square.ringkol.setting'),
          icon: '',
        },
      },
    ],
  },
  // 平台风采
  {
    path: '/square-fengcai',
    name: 'square-fengcai',
    component: () => import('@renderer/views/square/friend-ablum/noticeIndex.vue'),
    children: [
      {
        path: 'list',
        name: 'square-fengcai-list',
        component: () => import('@renderer/views/square/fengcai/list/index.vue'),
        meta: {
          title: t('banch.ptfc'),
          icon: 'elegance',
          role: 'office',
          keepAlive: true,
        },
      },
      {
        path: 'item',
        name: 'square-fengcai-item',
        // component: () => import("@renderer/views/square/fengcai/detail/index.vue"),
        component: () => import('@renderer/views/workBench/fengcai/detail/index.vue'),

        meta: {
          title: '风采详情',
          icon: 'elegance',
          role: 'office',
          keepAlive: false,
        },
      },
    ],
  },
  // 公告
  {
    path: '/square-notice',
    name: 'square-notice',
    component: () => import('@renderer/views/square/friend-ablum/noticeIndex.vue'),
    children: [
      {
        path: 'list',
        name: 'square-notice-list',
        component: () => import('@renderer/views/square/notice/index.vue'),
        meta: {
          title: '公告',
          icon: 'iconwaitingtrigon',
          role: 'office',
          keepAlive: true,
        },
      },
      {
        path: 'item',
        name: 'square-notice-item',
        component: () => import('@renderer/views/notice/noticeSetting/noticeDetailRead.vue'),
        meta: {
          title: '公告详情',
          icon: 'iconwaitingtrigon',
          role: 'office',
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: '/square/niche',
    name: 'niche',
    component: () => import('@renderer/views/square/niche/index.vue'),
    children: [
      {
        path: 'list',
        name: 'nicheList',
        component: () => import('@renderer/views/square/niche/list.vue'),
        meta: {
          affix: true,
          title: '商机列表',
          role: 'office',
          keepAlive: true,
        },
      },
      {
        path: 'nicheDetailReadOnly',
        name: 'squareNicheDetailReadOnly',
        component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
        meta: {
          affix: true,
          title: '商机详情',
          role: 'office',
          keepAlive: true,
        },
      },
    ],
  },
  // 广场管理后台
  {
    path: '/square-admin',
    name: 'squareAdmin',
    component: () => import('@renderer/views/square/admin/index.vue'),
    children: [
      {
        path: 'square-manager',
        name: 'SquareManager',
        component: () => import('@renderer/views/square/admin/SquareManager.vue'),
        meta: {
          title: t('square.admin.title2'),
          icon: 'icon20attendant',
          role: 'office',
        },
      },
      {
        path: 'safety-setting',
        name: 'SafetySetting',
        component: () => import('@renderer/views/square/admin/SafetySetting.vue'),
        meta: {
          title: t('square.admin.safeSetting'),
          icon: 'iconsetting',
          role: 'office',
        },
      },
    ],
  },

  // 拾光相册
  {
    path: '/square/phone-album',
    name: 'squareAlbum',
    component: () => import('@renderer/views/square/phone-album/index.vue'),
    children: [
      {
        path: 'time-line',
        name: 'timeLine',
        component: () => import('@renderer/views/square/phone-album/timeLine.vue'),
        meta: {
          affix: true,
          title: t('square.route.album'),
          tabName: t('square.route.timeLine'),
          icon: '../assets/square/timeLine.png',
        },
      },
      {
        path: 'live-house',
        name: 'liveHouse',
        component: () => import('@renderer/views/square/phone-album/timeLine.vue'),
        meta: {
          affix: true,
          title: t('square.route.album'),
          tabName: t('square.route.timeLine'),
          icon: '../assets/square/timeLine.png',
        },
      },
      {
        path: 'album-detail',
        name: 'albumDetail',
        component: () => import('@renderer/views/square/phone-album/albumDetail.vue'),
        meta: {
          title: t('square.route.nodeDetail'),
          hidden: true,
        },
      },
      {
        path: 'photo-detail',
        name: 'photoDetail',
        component: () => import('@renderer/views/square/phone-album/photoDetail.vue'),
        meta: {
          title: t('square.route.albumDetail'),
          hidden: true,
        },
      },
      {
        path: 'recycle-bin',
        name: 'recycleBin',
        component: () => import('@renderer/views/square/phone-album/recycleBin.vue'),
        meta: {
          title: t('square.route.recycleBin'),
          tabName: t('square.route.recycleBin'),
          icon: '../assets/square/recycle.png',
        },
      },
      {
        path: 'photo-detail-recycle',
        name: 'photoDetailRecycle',
        component: () => import('@renderer/views/square/phone-album/photoDetailRecycle.vue'),
        meta: {
          title: '',
          hidden: true,
        },
      },
      {
        path: 'photo-detail-draft',
        name: 'photoDetailDraft',
        component: () => import('@renderer/views/square/phone-album/photoDetailDraft.vue'),
        meta: {
          title: '',
          hidden: true,
        },
      },
      {
        path: 'upload-img-index',
        name: 'uploadImgIndex',
        component: () => import('@renderer/views/square/phone-album/uploadImgIndex.vue'),
        meta: {
          title: '',
          hidden: true,
        },
      },
      {
        path: 'album-detail-today',
        name: 'albumDetailToday',
        component: () => import('@renderer/views/square/phone-album/albumDetailToday.vue'),
        meta: {
          title: t('square.route.nodeDetail'),
          hidden: true,
        },
      },
      {
        path: 'album-detail-to-city',
        name: 'albumDetailToCity',
        component: () => import('@renderer/views/square/phone-album/albumDetailToCity.vue'),
        meta: {
          title: t('square.route.nodeDetail'),
          hidden: true,
        },
      },
      {
        path: 'album-detail-to-city-detail',
        name: 'albumItemToCityDetail',
        component: () => import('@renderer/views/square/phone-album/albumDetailToCityDetail.vue'),
        meta: {
          title: t('square.route.nodeDetail'),
          hidden: true,
        },
      },
      {
        path: 'album-detail-to-day-detail',
        name: 'albumDetailTodayDetail',
        component: () => import('@renderer/views/square/phone-album/albumDetailTodayDetail.vue'),
        meta: {
          title: t('square.route.nodeDetail'),
          hidden: true,
        },
      },
    ],
  },

  // 拾光相册查看者
  {
    path: '/square/friend-ablum',
    name: 'friendAlbum',
    component: () => import('@renderer/views/square/friend-ablum/index.vue'),
    children: [
      {
        path: 'album-index',
        name: 'albumList',
        component: () => import('@renderer/views/square/friend-ablum/albumIndex.vue'),
        meta: {
          affix: true,
          title: '时光相册2',
          tabName: '时光相册2',
          icon: '',
        },
      },
      {
        path: 'ablum-detail',
        name: 'ablumDetail',
        component: () => import('@renderer/views/square/friend-ablum/ablumDetail.vue'),
        meta: {
          hidden: true,
          title: '',
          icon: '',
          role: 'personal',
        },
      },
      {
        path: 'photo-detail',
        name: 'photo-detail',
        component: () => import('@renderer/views/square/friend-ablum/photoDetail.vue'),
        meta: {
          hidden: true,
          title: '',
          icon: '',
          role: 'personal',
        },
      },
    ],
  },

  // 合伙人名录
  {
    path: '/square-partner',
    name: 'square-partner',
    component: () => import('@renderer/views/square/directory/index.vue'),
    children: [
      {
        path: 'list',
        name: 'square-partner-list',
        component: () => import('@renderer/views/square/directory/partnerList.vue'),
        meta: {
          affix: true,
          title: '合伙人',
          tabName: '合伙人',
          icon: '',
        },
      },
    ],
  },

  // 服务模块
  {
    path: '/square/shome',
    name: 'shome',

    component: () => import('@renderer/views/square/service/index.vue'),
    children: [
      {
        path: 'service',
        name: 'service',
        component: () => import('@renderer/views/square/service/home.vue'),
        meta: {
          // hidden: true,
          // parentPath: "/square/homepage",
          // affix: true,
          title: t('square.route.home'),
          icon: '',
          role: 'personal',
        },
      },
    ],
  },

  // 广场官网搭建
  // {
  //   path: "/assemble",
  //   name: "assembleIndex",
  //   component: () => import("@renderer/views/square/assemble/Layout.vue"),
  //   children: [
  //     {
  //       path: "index",
  //       name: "assemble",
  //       component: () => import("@renderer/views/square/assemble/index.vue"),
  //       meta: {
  //         title: t("square.page.assemble"),
  //         icon: "iconnetwork",
  //         role: "office",
  //       },
  //     },
  //     {
  //       path: "list",
  //       name: "assemble-list",
  //       component: () => import("@renderer/views/square/assemble/List.vue"),
  //       meta: {
  //         title: t("square.page.pageManage"),
  //         role: "office",
  //         hidden: true,
  //       },
  //     },
  //     {
  //       path: "template",
  //       name: "assemble-template",
  //       component: () => import("@renderer/views/square/assemble/Template.vue"),
  //       meta: {
  //         title: t("square.page.tplLib"),
  //         role: "office",
  //         hidden: true,
  //       },
  //     },
  //   ],
  // },
  {
    path: '/square-pb',
    name: 'square-pb', // 云盘route-view
    component: () => import('@renderer/views/square/pb/index.vue'),
    children: [
      {
        path: 'list',
        name: 'square-pb-list',
        component: () => import('@renderer/views/square/pb/list/index.vue'),
        meta: {
          title: t('square.partyBuild'),
          hidden: true,
        },
      },
      {
        path: 'detail/:id',
        name: 'square-pb-detail',
        component: () => import('@renderer/views/square/pb/detail/index.vue'),
        meta: {
          title: t('square.partyBuild'),
          hidden: true,
        },
      },
    ],
  },

  // 活动
  {
    path: '/square-activity',
    name: 'squareActivity',
    component: () => import('@renderer/views/square/activity/index.vue'),
    children: [
      {
        path: 'list',
        name: 'square-activity-list',
        component: () => import('@renderer/views/square/activity/list.vue'),
        meta: {
          title: '活动列表',
          hidden: true,
        },
      },
      {
        path: 'activityParticipantDetail/:id',
        name: 'square_activityParticipantDetail',
        component: () => import('@renderer/views/activity/activityParticipantDetail.vue'),
      },
    ],
  },
];
