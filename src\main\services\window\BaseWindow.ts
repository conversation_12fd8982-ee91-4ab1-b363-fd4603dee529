import { BrowserWindow, BrowserWindowConstructorOptions } from "electron";
import { showDevTool } from "@main/env";
import { getSDK, getRandomUUID } from '@lynker-desktop/electron-sdk/main';
import { handleError } from "@main/services/window/utils";
import { IsUseSysTitle } from "@main/config/const";
import winPosition from "@main/services/window/getWinPosition";

// 窗口默认配置
const defaultConfig: () => BrowserWindowConstructorOptions = () => ({
  width: 1296,
  height: 720,
  minWidth: 1296,
  minHeight: 720,
  useContentSize: true,
  autoHideMenuBar: true,
  frame: IsUseSysTitle,
  transparent: false,
  resizable: true,
  movable: true,
  show: false,
  trafficLightPosition: {
    x: 8,
    y: 12,
  },
  webPreferences: {
    preload: getSDK()?.getPreload?.(),
    // 解决跨越问题
    webSecurity: false,
    // 允许渲染进程使用 node 模块
    nodeIntegration: true,
    contextIsolation: false,
    // 如果是开发模式可以使用devTools
    // devTools: showDevTool,
    devTools: false,
    // 在macos中启用橡皮动画
    scrollBounce: process.platform === "darwin",
  },
  titleBarStyle: IsUseSysTitle ? "default" : "hidden",
});

const winPools = new Map<string, BrowserWindow>();

export class BaseWindow {
  winName: string;

  url: string;

  config: BrowserWindowConstructorOptions;

  constructor(winName: string, url: string, options: BrowserWindowConstructorOptions) {
    this.close();
    this.url = url;
    this.winName = winName;
    this.config = options;
  }

  async init() {
    const winName = this.winName;
    const options = this.config;
    let win = winPools.get(winName);
    if (!win) {
      win = await getSDK().windowManager.create({
        name: `BaseWindow-${winName}-${getRandomUUID()}`,
        url: ``,
        browserWindow: {
          ...defaultConfig(),
          ...options
        }
      });
      winPools.set(winName, win);
      win.menuBarVisible = false;

      const { x, y } = winPosition.getPosition();
      win.setPosition(x, y);
    }

    if (showDevTool) {
      // win.webContents.openDevTools({
      //   mode: "undocked",
      //   activate: true
      // });
    }

    // win.once("ready-to-show", () => {
      win.show();
    // });
    win.on("closed", () => {
      winPools.delete(this.winName);
    });

    handleError(win, winName);
  }

  async open() {
    await this.init();
    const win = winPools.get(this.winName);
    if (win) {
      win.loadURL(this.url);
      win.show();
      return;
    }
    win.loadURL(this.url);
  }

  close() {
    const win = winPools.get(this.winName);
    if (win?.isDestroyed() === false) {
      win.destroy();
    }
    winPools.delete(this.winName);
  }
}
