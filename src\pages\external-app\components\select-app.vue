<template>
  <t-dialog
    v-model:visible="visible"
    :width="600"
    class="select-app-dialog"
    placement="center"
    header="添加渠道应用"
    :footer="false"
    @close="handleClose"
  >
    <template #close-btn>
      <div class="w-full h-full flex justify-center items-center" @click="handleClose">
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
          <path d="M5.5 5.5L16.4999 16.4999" stroke="#1A2139" stroke-width="1.4" stroke-linecap="round" />
          <path d="M16.5 5.5L5.50007 16.4999" stroke="#1A2139" stroke-width="1.4" stroke-linecap="round" />
        </svg>
      </div>
    </template>
    <div>
      <div class="px-[12px] mb-[12px]">
        <div
          class="h-[400px] overflow-y-auto bg-white rounded-[8px] p-[12px] select-app-list relative"
          @scroll="handleScroll"
        >
          <div v-if="appListError" class="flex items-center justify-center h-full text-red-500">
            {{ appListError }}
          </div>
          <div
            v-else-if="filteredAppList.length === 0 && !appListLoading"
            class="flex items-center justify-center h-full text-gray-400"
          >
            <r-empty />
          </div>
          <div v-else class="">
            <!-- 骨架屏 - 只在第一次加载时显示 -->
            <div
              v-if="appListLoading && appList.length === 0"
              class="absolute top-[0] left-[0] w-full h-full overflow-hidden p-[12px]"
            >
              <div v-for="i in 6" :key="i" class="">
                <div class="flex items-center gap-12 px-12 py-4 rounded-8">
                  <div class="w-18 h-18 bg-white border-1 border-solid border-[#999] rounded-4"></div>
                  <div class="w-40 h-40 bg-[#ECEFF5] rounded-8"></div>
                  <div class="flex flex-col justify-start items-start gap[4px]">
                    <div class="w-260 h-22 bg-[#ECEFF5] rounded-4"></div>
                    <div class="w-120 h-22 bg-[#ECEFF5] rounded-4"></div>
                  </div>
                </div>
                <div v-if="i < 6" class="h-1 bg-gray-100 my-[4px]"></div>
              </div>
            </div>

            <!-- 实际应用列表 -->
            <div v-else>
              <div v-for="app in filteredAppList" :key="app.app_id">
                <div
                  class="flex items-center gap-12 p-12 rounded-8 hover:bg-[#f3f6fa] cursor-pointer"
                  @click="toggleAppSelection(app)"
                >
                  <t-checkbox :checked="isAppSelected(app)" @change="() => toggleAppSelection(app)" @click.stop />
                  <img
                    class="w-40 h-40 object-cover rounded-[12px]"
                    :src="app.picture_linking || externalAppIcon"
                    alt=""
                  />
                  <div class="flex flex-col justify-start items-start">
                    <div class="text-14 font-medium text-gray-900 truncate">{{ app.name || '' }}</div>
                    <div class="text-12 text-gray-500 mt-4 app-type-label">{{ getAppTypeLabel(app.type || '') }}</div>
                  </div>
                </div>
                <div class="h-1 bg-gray-100 my-[4px]"></div>
              </div>
            </div>

            <!-- 加载更多时的简单加载提示 -->
            <div v-if="appListLoading && appList.length > 0" class="flex items-center justify-center py-16">
              <t-loading size="small" />
              <span class="ml-8 text-12 text-gray-500">加载中...</span>
            </div>
          </div>
        </div>
      </div>

      <div v-if="filteredAppList.length > 0" class="flex justify-end p-[24px] bg-white gap-[8px]">
        <t-button class="w-[80px]" theme="default" @click="handleCancel">取消</t-button>
        <t-button class="w-[80px]" theme="primary" @click="handleConfirm">确定</t-button>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue';
import { getExternalAppList } from '@pages/external-app/api';
import type { ExternalAppListDataItem } from '@pages/external-app/api';
import type { IOptions } from '@axios/types';
import { REmpty } from '@rk/unitPark';
import externalAppIcon from '@pages/external-app/assets/external_app.svg';

import './styles.less';

interface Props {
  modelValue: boolean;
  channelType: string;
  options?: IOptions;
  existingApps?: ExternalAppListDataItem[];
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm', apps: ExternalAppListDataItem[]): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  existingApps: () => [],
});

const emit = defineEmits<Emits>();

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const appList = ref<ExternalAppListDataItem[]>([]);
const appListLoading = ref(false);
const appListError = ref<string | null>(null);
const appListTotal = ref(0);
const appListPage = ref(1);
const appListPageSize = ref(10);
const selectedApps = ref<ExternalAppListDataItem[]>([]);
const hasMore = ref(true);

const filteredAppList = computed(() => {
  if (!props.existingApps.length) return appList.value;

  const existingAppIds = new Set(props.existingApps.map((app) => app.app_id));
  return appList.value.filter((app) => !existingAppIds.has(app.app_id));
});

const loadAppList = async (isLoadMore = false) => {
  if (appListLoading.value || (!isLoadMore && !hasMore.value)) return;

  appListLoading.value = true;
  appListError.value = null;

  try {
    const response = await getExternalAppList(
      {
        channel_type_not: props.channelType,
        page: appListPage.value,
        pageSize: appListPageSize.value,
        bond_status_in: '0,2',
      },
      props.options,
    );

    if (response?.data?.items) {
      if (isLoadMore) {
        appList.value.push(...response.data.items);
      } else {
        appList.value = response.data.items;
      }

      appListTotal.value = response.data.total || 0;
      hasMore.value = appList.value.length < appListTotal.value;

      if (hasMore.value) {
        appListPage.value++;
      }
    }
  } catch (error) {
    console.error('获取应用列表失败:', error);
    appListError.value = '获取应用列表失败';
  } finally {
    appListLoading.value = false;
  }
};

const handleScroll = (e: Event) => {
  const target = e.target as HTMLElement;
  const { scrollTop, scrollHeight, clientHeight } = target;

  if (scrollHeight - scrollTop <= clientHeight + 50 && !appListLoading.value && hasMore.value) {
    loadAppList(true);
  }
};

const toggleAppSelection = (app: ExternalAppListDataItem) => {
  const index = selectedApps.value.findIndex((item) => item.app_id === app.app_id);
  if (index > -1) {
    selectedApps.value.splice(index, 1);
  } else {
    selectedApps.value.push(app);
  }
};

const isAppSelected = (app: ExternalAppListDataItem) => {
  return selectedApps.value.some((item) => item.app_id === app.app_id);
};

const getAppTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    app: 'APP跳转',
    h5: '网页H5跳转',
    wechat_official: '微信公众号',
    mini_program: '微信小程序',
  };
  return typeMap[type] || type;
};

const handleConfirm = () => {
  emit('confirm', selectedApps.value);
  visible.value = false;
};

const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};

const handleClose = () => {
  emit('cancel');
};

watch(
  () => visible.value,
  (newVal) => {
    if (newVal) {
      appList.value = [];
      appListLoading.value = false;
      appListError.value = null;
      appListTotal.value = 0;
      appListPage.value = 1;
      selectedApps.value = [];
      hasMore.value = true;

      nextTick(() => {
        loadAppList();
      });
    }
  },
  { immediate: true },
);
</script>

<style lang="less">
.select-app-dialog {
  .t-dialog {
    padding: 0;
    background-image: url('@pages/external-app/assets/drawer-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 0;
    background-color: #eaecff;
    overflow: hidden;
    .t-dialog__header {
      padding: 24px;
    }
    .t-dialog__body {
      padding: 0;
    }
    .t-dialog__footer {
      padding: 0;
      background-color: #fff;
    }
    .app-type-label {
      display: flex;
      height: 20px;
      min-height: 20px;
      max-height: 20px;
      padding: 2px 4px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: 4px;
      background: #eaecff;
      padding: 4px;
      color: var(--kyy_color_tag_text_brand, #4d5eff);
      text-align: center;

      /* kyy_fontSize_1/regular */
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
    }
  }
  .select-app-list {
    &::-webkit-scrollbar {
      width: 6px;
      background-color: transparent;
    }

    /*定义滚动条轨道 内阴影+圆角*/
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    /*定义滑块 内阴影+圆角*/
    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background: var(--divider-kyy_color_divider_deep, #d5dbe4);
    }
  }
}
</style>
