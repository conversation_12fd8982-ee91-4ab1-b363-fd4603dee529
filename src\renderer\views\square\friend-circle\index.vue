<template>
  <div
    ref="pageContent"
    v-infinite-scroll="onLoading"
    class="page-content"
    :infinite-scroll-immediate-check="false"
    :infinite-scroll-distance="1800"
    :infinite-scroll-disabled="scrollDisabled"
    infinite-scroll-watch-disabled="scrollDisabled"
    @scroll="throttlePageScroll"
  >
    <!-- <PostPublish
      v-if="!offlineAddNoCached"
      :key="refreshKey"
      source="friendCircle"
      @submit="refreshKey++"
      @error="store.hasPostFailed = true"
    /> -->
    <PostPublishDialog v-model="postDialogVisible" :show-article="!store.isPersonal" @submit="submitPost" />
    <PostPublishBlock
      @publish="onPublish"
      @home="onGoHome"
    />

    <t-alert
      v-if="store.hasPostFailed && !offlineAddNoCached"
      theme="error"
      close
      class="mt-16 mx-16"
      @close="store.hasPostFailed = false"
    >
      <template #icon>
        <!-- <svg-icon name="square-error-fill" style="width: 16px; height: 16px;color: #fff" /> -->
        <i class="i-svg:error-fill text-16 text-white" />
      </template>
      {{ $t('square.post.err2Draft') }} <t-link theme="primary" @click="router.push('/square/drafts'); store.hasPostFailed = false">{{ $t('square.clickSee') }}</t-link>
    </t-alert>
    <PostList
      ref="postListRef"
      :key="refreshKey"
      go-detail
      class="trends-list"
      view-observer="TIMELINE"
      :cache-key="SQUARE_CACHE.timeline"
      hide-top
      from="friend-circle"
      @removed="refreshKey++"
      @load="loaded"
      @publish="postDialogVisible = true"
      @offline-empty="offlineEmpty"
    />

    <t-back-top
      ref="backTopRef"
      container=".page-content"
      shape="circle"
      size="small"
      :visible-height="50"
      :offset="['385px', '40px']"
      style="position: fixed"
    >
      <div class="back-top">
        <!-- <t-icon name="arrow-up" class="icon" /> -->
        <img src="@renderer/assets/square/icon-top.svg" alt="" class="icon">
      </div>
    </t-back-top>

    <Tricks
      :key="backTopIsVisible"
      :offset="{ x: '-385', y: backTopIsVisible ? '-96' : '-40' }"
      :uuid="store.isPersonal ? '个人广场-好友圈' : '组织广场-好友圈'"
    />

    <!-- 外部跳转查看动态详情-->
    <PostDetail v-if="detailVisible" :id="detailId" v-model="detailVisible" />
  </div>
</template>

<script setup lang="ts" name="FriendCircle">
import { inject, onActivated, provide, ref, watch, computed, nextTick, reactive, onMounted } from 'vue';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import LynkerSDK from '@renderer/_jssdk';
import throttle from 'lodash/throttle';
import PostList from '../components/post/PostList.vue';
import PostPublishBlock from '@/views/square/components/post/PostPublishBlock.vue';
import PostPublishDialog from '@/views/square/components/post/PostPublishDialog.vue';
import { useSquareStore } from '@/views/square/store/square';
import PostDetail from '@/views/square/components/post/PostDetail.vue';
import { POST_REFRESH_KEY, SQUARE_CACHE } from '@/views/square/constant';
import useNavigate from '../hooks/navigate';
import { useSquareAccount } from '@/views/square/hooks/squareAccount';
import { useTabRemoveRepeatGuard } from '../utils/business';

const { ipcRenderer } = LynkerSDK;

const { redirectFromOuter } = useNavigate();
const store = useSquareStore();
const router = useRouter();

const refreshKey = ref(0);
const pageKey = ref(1);
const sidebarKey = ref(1);

const refresh = async (isRefresh = false) => {
  if (isRefresh) {
    await store.getSquaresList();

    // 管理后台从组织中删除当前账号，跳转个人广场
    if (store.isPersonal && route.fullPath === '/square/publish-records') {
      router.push('/square/friend-circle');
    }
  }

  sidebarKey.value++;
  doRefresh();
  // store.getIp(true);
};

const doRefresh = () => {
  refreshView();
  pageKey.value++;
  store.getSquareInfo();

  if (['/square/friend-circle', '/square/notifications'].includes(route.fullPath)) {
    store.getSquareStats();
    store.fetchNewsStats();
  }
};
// 刷新当前页面
// TODO 默认所有页面不缓存
const defaultExclude = ['SquareSearch', 'RingkolPreview', 'FriendDetail'];
const componentTick = reactive({ exclude: [...defaultExclude], showComponent: true });
const refreshView = () => {
  const match = route.matched.find((v) => v.path === route.path);
  if (match) {
    const { name, __name } = match.components.default;
    const componentName = name || __name;
    if (componentName) {
      componentTick.exclude = [...defaultExclude, componentName];
      componentTick.showComponent = false;
    }
  }
  nextTick(() => {
    componentTick.exclude = [...defaultExclude];
    componentTick.showComponent = true;
  });
};

const { accountChange } = useSquareAccount(refresh, { autoRoute: true });

// 外部跳转查看动态详情
const route = useRoute();
const detailVisible = ref(false);
const detailId = ref('');
const postDialogVisible = ref(false);
const postSubmitKey = ref(1);
provide(POST_REFRESH_KEY, postSubmitKey);
const submitPost = () => {
  postDialogVisible.value = false;
  postSubmitKey.value++;
  refreshKey.value++;
};

watch(
  () => route.query.postId,
  async (id) => {
    if (!id) return;
    detailId.value = id as string;
    detailVisible.value = true;
  },
  { immediate: true },
);

const postPublishKey = inject(POST_REFRESH_KEY, ref(0));
watch(() => postPublishKey.value, () => {
  refreshKey.value++;
});

const loaded = () => {
  // setTimeout(() => {
  //   store.unreadPostCount = 0;
  //   unReadPost();
  // }, 1000);
};

const onPublish = () => {
  postDialogVisible.value = true;
};
const onGoHome = () => {
  router.push('/square/homepage');
};

const offlineAddNoCached = ref(false);
const offlineEmpty = (flag: boolean) => {
  offlineAddNoCached.value = flag;
};

const pageContent = ref(null);
const scroll = ref(0);
onBeforeRouteLeave((to, from, next) => {
  scroll.value = pageContent.value?.scrollTop;
  next();
});

const postListRef = ref(null);
const scrollDisabled = computed(() => postListRef.value?.status === 'finished');
const onLoading = () => {
  postListRef.value?.loading();
};

const newsFresh = ref(true);
const backTopIsVisible = ref(false);

const pageScroll = (e) => {
  backTopIsVisible.value = e.target.scrollTop >= 50;

  if (!newsFresh.value) return;

  newsFresh.value = false;
  store.unreadPostCount = 0;
  setTimeout(() => store.fetchNewsStats(), 400);
};

const throttlePageScroll = throttle(pageScroll, 300);

onActivated(() => {
  // refreshKey.value++;
  nextTick(() => {
    if (scroll.value && pageContent.value) {
      pageContent.value.scrollTop = scroll.value;
    }
  });
});

const redirectPromise = ref(null);

onMounted(async () => {
  ipcRenderer.invoke('square-loaded');

  if (redirectPromise.value) {
    await redirectPromise.value();
    redirectPromise.value = null;
  }

  // HACK 用闭包避免 setTimeout 内获取不到 route.query
  // 检查是否是由于关闭页签触发的，如果是则不执行重定向
  ((query) => {
    setTimeout(() => {
      useTabRemoveRepeatGuard(() => {
        redirectFromOuter(query, accountChange);
      });
    }, 500);
  })(route.query);
});
</script>

<style lang="less" scoped>
.scrollbar(6px);

.page-content {
  position: relative;
  overflow-y: auto;
  padding: 16px 8px 16px 16px;
  margin-right: 6px;
}

:deep(.t-upload__card) {
  max-width: 350px;
  .t-upload__card-item {
    margin-right: 5px;
  }
}

:deep(.t-alert--error) {
  padding: 8px 16px;
  align-items: center;
  background-color: #FFEEE8;
  .t-alert__description {
    color: #13161B;
  }
}

:deep(.trends-list) {
  overflow-y: inherit;
  height: inherit;
}

:deep(.t-back-top) {
  background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.50)) !important;
  border: none;
  box-shadow: none;
}

.back-top {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  cursor: pointer;
  .icon {
    color: #fff;
    width: 20px;
    height: 20px;
  }
}

.album-title{
  color: #333333;
  font-size: 16px;
  margin: 0 16px 16px 16px;
  cursor: pointer;
  padding-top: 8px;
  .album-title-icon{
    font-size: 24px;
    vertical-align: middle;
  }
  .album-title-span{
    margin-left: 8px;
    vertical-align: middle;
  }
}
</style>
