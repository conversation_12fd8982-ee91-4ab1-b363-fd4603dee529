<template>
  <!-- 使用 REmpty 组件，此组件已废弃；此为兼容现有代码，后续需移除 -->
  <REmpty v-bind="bindProps" class="wrap">
    <template #default>
      <img
        v-if="shouldUseCustomImage"
        :src="getUrl(name as string)"
        alt=""
        :style="iconStyles"
      >
      <slot />
    </template>
    <template #tip>
      <slot name="tip" />
    </template>
    <template #bottom>
      <slot name="bottom" />
    </template>
  </REmpty>
</template>

<script setup lang="ts">
import { computed, useAttrs, CSSProperties } from 'vue';
import { REmpty } from '@rk/unitPark';

const props = defineProps({
  name: {
    type: String,
    default: 'no-data',
  },
  tip: {
    type: String,
    default: '暂无数据',
  },
});

const { name, tip } = props;
const attrs = useAttrs();

const getUrl = (tagIcon: string) => new URL(`../../assets/empty/${tagIcon}.svg`, import.meta.url).href;

// 兼容 REmpty 组件的变动，将移除的 key 映射到新的 key
// 注意：下面 3 个 key 已废弃，后续需移除，请使用 no-data 或 no-result 替代
const nameMapping = {
  'no-data-new': 'no-data',
  'no-search-contact': 'no-result',
  'no-friend-list': 'no-data',
} as const;

const computedName = computed(() => nameMapping[name as keyof typeof nameMapping] || name);
const computedTip = computed(() => {
  // 特殊处理，使用自定义文案
  if (name === 'no-friend-list') return tip || '马上发一个动态试试吧';
  if (name === 'slogan') return tip || '';
  if (name === 'no_detail') return tip || '详情未设置';
  return tip;
});

const bindProps = computed(() => ({ ...attrs, name: computedName.value, tip: computedTip.value }));

// 判断是否需要使用自定义图片（slogan 和 no_detail 不在 figma 中定义）
const shouldUseCustomImage = computed(() => ['slogan', 'no_detail'].includes(name));

const iconStyles = computed<CSSProperties>(() => {
  const defaultWidth = name === 'slogan' ? '338.414px' : '200px';
  const defaultHeight = name === 'slogan' ? '76.046px' : '200px';

  return {
    width: attrs.width || defaultWidth,
    height: attrs.height || defaultHeight,
  };
});
</script>

<style scoped lang="less">
.wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 16px 0;
}

:deep(.tip) {
  margin-top: 12px;
  text-align: center;
  color: var(--lingke-contain-fonts, #516082);
}
</style>
