import { join } from "path";
import fs from "fs";
// import path from "path";
const path = require("path");
import fse from 'fs-extra';
import { defineConfig, loadEnv } from "vite";
import vuePlugin from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import VueSetupExtend from "vite-plugin-vue-setup-extend";
// import { VitePWA } from 'vite-plugin-pwa'
import { getConfig, isMas } from "./env";
// import eslintPlugin from "vite-plugin-eslint";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import UnoCSS from "unocss/vite";
import { setAppConfigInfo } from './setConfig'
import pkg from '../package.json';
import { generateAllHtml } from "./generate-all-html";
// import AutoImport from 'unplugin-auto-import/vite';
// import Components from 'unplugin-vue-components/vite';// import htmlPurge from "vite-plugin-purgecss";
// import { TDesignResolver } from 'unplugin-vue-components/resolvers';
function resolve(dir: string) {
  return join(__dirname, "..", dir);
}
const config = getConfig();

console.log('process', isMas())
// 如果是苹果商店类型
if (isMas()) {
  config.VITE_APP_MAS = true
}

setAppConfigInfo(config)

/**  ===== fix: 配置变更后，vite重启端口恢复成默认 ===== */
let port = undefined;
try {
  port = fse.readJSONSync(path.join(__dirname, './.run.config.json')).port;
} catch (error) {
}
/**  ===== fix: 配置变更后，vite重启端口恢复成默认 ===== */

console.log("vite启动", port, process.env.NODE_ENV, { config });

const root = resolve("src/renderer");
// fix: Failed to resolve component: iconpark-icon
const isCustomElement = (tag: string) => ['iconpark-icon', 'wc-waterfall'].includes(tag);
const wasmFile = path.resolve(__dirname, "MediaInfoModule.wasm");

export default defineConfig({
  optimizeDeps: {
    exclude: ['@rk/editor']
  },
  mode: process.env.NODE_ENV,
  root,
  publicDir: '../../public',
  define: {
    __APP_ENV__: config,
  },
  resolve: {
    alias: {
      "@": root,
      "@renderer": root,
      "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
      "customTypes": resolve('customTypes'),
    },
  },
  assetsInclude: [
    'svga'
  ],
  base: "./",
  build: {
    /** 减少资源base64内联 */
    // assetsInlineLimit: 0,
    outDir: config && config.target ? resolve("dist/web") : resolve("dist/electron/renderer"),
    emptyOutDir: true,
    target: "esnext",
    reportCompressedSize: false,
    minify: false, //取消代码压缩
    cssCodeSplit: true,
    rollupOptions: {
      input: {
        index: join(__dirname, "../src/renderer/index.html"),
        internal: join(__dirname, "../src/renderer/windows/internal/index.html"),
        injoin: join(__dirname, "../src/renderer/windows/injoin/index.html"),
        wps: join(__dirname, "../src/renderer/windows/wps/index.html"),
        mergedmsg: join(__dirname, "../src/renderer/windows/merged/index.html"),
        RKIM: join(__dirname, "../src/renderer/windows/RKIM/index.html"),
        popBv: join(__dirname, "../src/renderer/windows/popBv/index.html"),
        square: join(__dirname, "../src/renderer/windows/square/index.html"),
        loading: join(__dirname, "../src/renderer/windows/loading/index.html"),
        webmodal: join(__dirname, "../src/renderer/windows/webmodal/index.html"),
        vcard: join(__dirname, "../src/renderer/windows/vcard/index.html"),
        activationCode: join(__dirname, "../src/renderer/windows/activationCode/index.html"),
        error: join(__dirname, "../src/renderer/windows/error/index.html"),
        /** SDK 选人组件 */
        SelectMember: join(__dirname, "../src/renderer/_jssdk/components/SelectMember/index.html"),
        /** SDK 加入数字平台组件 */
        JoinDigitalPlatformDrawer: join(__dirname, "../src/renderer/_jssdk/components/join-digital-platform-drawer/index.html"),
        /** SDK 组织认证组件 */
        OrgAuthDrawer: join(__dirname, "../src/renderer/_jssdk/components/org-auth-drawer/index.html"),
        /** SDK 添加联系人组件 */
        AddContactsDialog: join(__dirname, "../src/renderer/_jssdk/components/add-contacts-dialog/index.html"),
        /** SDK 年费组件 */
        AnnualFeeDrawer: join(__dirname, "../src/renderer/_jssdk/components/annual-fee-drawer/index.html"),
        /** SDK 窗口组件 for tabs */
        windows_tabs: join(__dirname, "../src/renderer/_jssdk/components/windows-tabs/index.html"),
        debugtools: join(__dirname, "../src/renderer/_jssdk/components/debugtools/index.html"),
      },
      output: {
        manualChunks(id) {
          // 控制拆包逻辑
          // 所有依赖独立一个js
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        },
        dir: "dist/electron/renderer",
      },
    },
  },

  server: {
    port: port,
    // host: "0.0.0.0",
    // https:{
    //   key: fs.readFileSync("src/renderer/cert/localhost+1-key.pem"),
    //   cert: fs.readFileSync("src/renderer/cert/localhost+1.pem"),
    // }
  },
  plugins: [
    /**
     * PWA 配置
     * 由于electron 的限制，无法使用pwa
     */
    // VitePWA({
    //   registerType: 'autoUpdate',
    //   workbox: {
    //     globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
    //     sourcemap: true,
    //     maximumFileSizeToCacheInBytes: 2097152 * 20,
    //   },
    //   devOptions: {
    //     enabled: true
    //   }
    // }),

    // AutoImport({
    //   resolvers: [TDesignResolver({
    //     library: 'vue-next'
    //   })],
    // }),
    // Components({
    //   resolvers: [TDesignResolver({
    //     library: 'vue-next'
    //   })],
    // }),
    // htmlPurge({
    //   content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
    //   defaultExtractor: (content) => content.match(/[\w-/:]+(?<!:)/g) || [],
    // }),
    vueJsx({
      isCustomElement,
    }),
    vuePlugin({
      template: {
        compilerOptions: {
          isCustomElement,
        },
      },
    }),
    createSvgIconsPlugin({
      // 指定要缓存的图标文件夹
      iconDirs: [path.resolve(process.cwd(), "src/renderer/assets/svg")],
      // 执行icon name的格式
      symbolId: "icon-[dir]-[name]",
      svgoOptions: false,
    }),

    VueSetupExtend(),
    UnoCSS(),
    {
      name: 'conditional-console-log',
      apply: 'build',
      transformIndexHtml(html) {
        // 在 HTML 中插入全局方法
        const consoleCode = `
          <script>
            // 添加全局条件性控制台方法
            window.__conditionalConsole__ = {
              log: (...args) => {
                if (localStorage.getItem('__IS_LOG__') === 'true') {
                  console.log(...args);
                }
              },
              error: (...args) => {
                if (localStorage.getItem('__IS_LOG__') === 'true') {
                  console.error(...args);
                }
              },
              warn: (...args) => {
                if (localStorage.getItem('__IS_LOG__') === 'true') {
                  console.warn(...args);
                }
              },
              info: (...args) => {
                if (localStorage.getItem('__IS_LOG__') === 'true') {
                  console.info(...args);
                }
              },
            };
          </script>
        `;
        // 在 head 标签结束前插入代码
        return html.replace(/<\/head>/i, `${consoleCode}</head>`);
      },
      transform(code, id) {
        // 检查是否是 JavaScript 或 TypeScript 文件
        if (!id.includes('node_modules') && (id.endsWith('.js') || id.endsWith('.ts') || id.endsWith('.vue'))) {
          // 使用正则表达式匹配所有 console 调用
          return code.replace(/console\.(log|error|warn|info);?/g, (match, method) => {
            return `(window.__conditionalConsole__ || console).${method}`;
          });
        }
        return code;
      },
    },
    {
      name: 'generate-all-scripts-html',
      apply: 'build',
      closeBundle() {
        try {
          const distPath = config && config.target ? resolve("dist/web") : resolve("dist/electron/renderer/assets")
          const files = fs.readdirSync(distPath);
          const jsFiles = files.filter(f => f.endsWith('.js'));
          const cssFiles = files.filter(f => f.endsWith('.css'));

          const htmlContent = generateAllHtml(jsFiles, cssFiles);

          fs.writeFileSync(join(distPath, '../__proload_assets__.html'), htmlContent);
          console.log('✅ __proload_assets__.html generated');
        } catch (error) {
          console.error('✅ __proload_assets__.html generated error', error)
        }
      },
    }
  ],
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          hack: `true; @import (reference) "${resolve("src/renderer/style/index.less")}";`,
        },
        javascriptEnabled: true,
      },
    },
  },
});

