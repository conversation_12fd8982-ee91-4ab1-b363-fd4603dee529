import { BasePageRequest, PageResponse } from "@renderer/api/model";
import { FriendStatus } from "@renderer/views/digital-platform/forum/constant";

/**
 * 帖子列表入参
 */
export interface PostListRequest extends BasePageRequest {
  /**
   * 1: 论坛，2: 我的帖子，3: 特定所有者，4: 话题帖子列表
   */
  from?: string;
  /**
   * true 时只查询置顶帖子
   */
  pin?: string;
  /**
   * from 为 3 时特定所有者 ID
   */
  target_owner_id?: string;
}

/**
 * 话题帖子列表入参
 */
export interface TopicPostListRequest extends BasePageRequest {
  /**
   * from 为 4 时话题 ID
   */
  topic?: string;
  /**
   * 排序方式，1：热门，2：最新
   */
  sort?: string;
}

/**
 * 数据
 */
export interface PostListResponse {
  page?: PageResponse;
  posts?: PostDetail[];
}

/**
 * 发布帖子/草稿
 */
export interface PostAdd {
  /**
   * 内容
   */
  content?: Content;
  /**
   * 开放加好友
   */
  friendFlag?: boolean;
  /**
   * 帖子 ID
   */
  id?: string;
  /**
   * IP 属地
   */
  ipRegion?: string;
  /**
   * 所有者 ID
   */
  ownerId?: string;
  /**
   * true 时置顶
   */
  pin?: boolean;
  /**
   * 帖子类型
   */
  postType?: PostType;
  /**
   * 文字内容
   */
  text?: string;
  /**
   * 话题
   */
  topics?: string[];
  /**
   * @提及人
   */
  at?: [];
}

/**
 * 内容
 */
export interface Content {
  /**
   * oss 对象列表
   */
  objects?: Object[];
}

/**
 * oss 对象
 */
export interface Object {
  /**
   * 元数据（map，key、value 均为字符串）
   */
  metadata?: { [key: string]: any };
  /**
   * 地址
   */
  url?: string;
}

/**
 * 帖子类型
 */
export enum PostType {
  Picture = "PICTURE",
  Text = "TEXT",
  Video = "VIDEO",
}

/**
 * 帖子详情
 */
export interface PostDetail {
  /**
   * 自己是否点赞
   */
  liked?: boolean;
  /**
   * 所有者
   */
  owner?: Owner;
  /**
   * 帖子
   */
  post?: Post;
  /**
   * 话题
   */
  topics?: Topic[];
}

/**
 * 所有者
 */
export interface Owner {
  /**
   * 应用标识
   */
  appUuid?: string;
  /**
   * 头像
   */
  avatar?: string;
  /**
   * 身份卡 ID
   */
  cardId?: string;
  /**
   * 数字平台身份类型
   */
  flag?: Flag;
  /**
   * 所有者 ID
   */
  id?: string;
  /**
   * 名称
   */
  name?: string;
  /**
   * 用户 openid
   */
  openid?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 组织 ID
   */
  teamId?: string;
}

/**
 * 数字平台身份类型
 */
export enum Flag {
  Staff = "staff",
  StaffPlatform = "staff_platform",
  Visitor = "visitor",
  Anonymous = "anonymous",
}

/**
 * 帖子
 */
export interface Post {
  /**
   * 评论数
   */
  comments?: number;
  friendFlag: boolean;
  friendStatus: FriendStatus;
  /**
   * 内容
   */
  content?: Content;
  /**
   * 帖子 ID
   */
  id?: string;
  /**
   * IP 属地
   */
  ipRegion?: string;
  /**
   * 点赞数
   */
  likes?: number;
  /**
   * 所有者 ID
   */
  ownerId?: string;
  /**
   * true 时置顶
   */
  pin?: boolean;
  /**
   * 发布时间
   */
  postedAt?: string;
  updatedAt?: string;
  /**
   * 帖子类型
   */
  postType?: PostType;
  /**
   * 帖子状态
   */
  status?: Status;
  /**
   * 文字内容
   */
  text?: string;
  /**
   * 话题
   */
  topics?: string[];
}

/**
 * 帖子状态
 */
export enum Status {
  Block = "BLOCK",
  Deleted = "DELETED",
  Draft = "DRAFT",
  Posted = "POSTED",
}

export interface Topic {
  /**
   * 话题 ID
   */
  id?: string;
  /**
   * 名称
   */
  name?: string;
}
