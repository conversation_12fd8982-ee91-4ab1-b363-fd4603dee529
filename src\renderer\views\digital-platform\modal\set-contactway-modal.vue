<template>
  <t-dialog v-model:visible="visible" class="dialogSet dialogNoDefault dialogSet-noBodyPadding dialogSetHeader drawerSetForm" :z-index="2500"   :close-btn="true" :size="'456px'">
    <template #header>
      <div class="header">
        <span class="title">设置联络方式</span>
        <!-- <span v-show="!props.isMember" class="tip cursor" @click="onShowMemberFlow">
          <iconpark-icon name="iconhelp" class="iconhelp"></iconpark-icon>{{ $t('member.winter_column.know_add_flow_1') }} </span> -->
      </div>
    </template>
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px; color: #516082" />
    </template>
    <div class="toBody">
      <div class="scroll">
        <div class="tip mb-24px">
          <iconpark-icon name="iconinfo-b7dcijg4" class="icon"></iconpark-icon>
          <span class="text">联络人信息将会在【数字平台-平台交流】中进行展示</span>
        </div>
        <div class="form">
          <t-form ref="form" :data="formData" :label-align="formData.labelAlign" :rules="rules" :label-width="60">
            <t-form-item :label="'服务电话'+(formData.phone_data?.length > 1 ? phoneIndex+1: '')"   v-for="(ph, phoneIndex) in formData.phone_data" :key="phoneIndex">
              <t-input v-model="ph.phone" :maxlength="50" :placeholder="'请输入服务电话/手机'"></t-input>
              <iconpark-icon name="iconaddcircle" v-show="phoneIndex === 0" class="iconaddcircle ml-8px cursor" @click="onAddPhone(formData.phone_data)"></iconpark-icon>
              <iconpark-icon name="iconremove" v-show="phoneIndex > 0" @click="onDelPhone(formData.phone_data, phoneIndex)" class="iconremove ml-8px cursor"></iconpark-icon>
            </t-form-item>
            <t-form-item :label="'联络地址'">
              <t-input v-model="formData.address" :maxlength="50" :placeholder="'请输入联络地址'"></t-input>
            </t-form-item>
            <t-form-item :label="'邮编'">
              <t-input v-model="formData.zip_code" :maxlength="50" :placeholder="'请输入邮编'"></t-input>
            </t-form-item>
            <t-form-item :label="'传真'">
              <t-input v-model="formData.fax" :maxlength="50" :placeholder="'请输入传真'"></t-input>
            </t-form-item>
            <t-form-item :label="'邮箱'">
              <t-input v-model="formData.email" :maxlength="50" :placeholder="'请输入邮箱'"></t-input>
            </t-form-item>
          </t-form>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="footer" style="display: flex; justify-content: flex-end">
        <t-button theme="default" variant="outline" @click="onClose">
          {{ $t('member.impm.select_11') }}
        </t-button>
        <t-button theme="primary" @click="onSubmit">确定</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script lang="ts" setup>
/**
 * @description 批量调整角色弹层
 * <AUTHOR>
 */
import lodash, { debounce } from 'lodash';

import { ref, reactive, Ref, Reactive } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import formRuntime from '@renderer/components/free-from/runtime/index.vue';
import { useI18n } from 'vue-i18n';
import {getContactInformationDetailAxios, setContactInformationAxios} from '@renderer/api/digital-platform/api/businessApi'
// import {

// } from '@renderer/api/politics/api/businessApi';

// import {

// } from '@renderer/api/member/api/businessApi';


// import {

// } from '@renderer/api/cbd/api/businessApi';


// import {

// } from '@renderer/api/association/api/businessApi';

import { to } from 'await-to-js';
import { getChidlren, getResponseResult } from '@/utils/myUtils';
import { originType } from '@renderer/views/digital-platform/utils/constant';


const currentTab = ref('');
const { t } = useI18n();
const type = ref(0); // 0创建 1编辑
const props = defineProps({
  teamId: {
    type: String,
    default: '',
  },
  origin: {
    type: String,
    default: originType.Member,
  },
  // orData: {
  //   type: Object,
  //   default: ()=> null
  // }
});

const emits = defineEmits(['onReload']);


const handleObj = {
  getLabelFunc: null,
  channel_type: ''
};
const handleFunc = () => {
  switch (props.origin) {
    case originType.Member:
      handleObj.channel_type = 'member';
      break;
    case originType.Politics:
      handleObj.channel_type = 'government';
      break;
    case originType.CBD:
      handleObj.channel_type = 'cbd';
      // handleObj.getLabelFunc = onGetLabelSettingCBDAxios;
      break;
    case originType.Association:
      handleObj.channel_type = 'association';
      // handleObj.getLabelFunc = onGetLabelSettingAssociationAxios;
      break;
          case originType.Uni:
      handleObj.channel_type = 'uni';
      // handleObj.getLabelFunc = onGetLabelSettingAssociationAxios;
      break;
    default:

      break;
  }
};
handleFunc();

const datas = ref(null);
const visible = ref(false);

interface ContactWay {
  /**
   * 地址
   */
  address?: string;
  /**
   * // cbd  //government 数字城市 member：数字商协
   */
  channel_type: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * 传真
   */
  fax?: string;
  /**
   * 电话信息
   */
  phone_data: any;
  /**
   * 邮编
   */
  zip_code?: string;
}
const initWayData: ContactWay = {
  address: '',
  channel_type: '',
  email: '',
  fax: '',
  phone_data: [{phone: ''}],
  zip_code: '',
}

const formData = reactive({
	labelAlign: 'top',
	...initWayData,
});

const onGetSettingInfo = async () => new Promise(async (resolve, reject) => {
  const [err, res] = await to(getContactInformationDetailAxios({
    channel_type: handleObj.channel_type
  }, props.teamId));
  if (err) return reject();
  if (res) {
    const { data } = res;
    console.log(data);
    resolve(data?.data);
  } else {
    reject();
  }
});

const onAddPhone = (arr) => {
  if(arr.length < 5) {
    arr.push({phone: ''})
  } else {
    MessagePlugin.error('最多能添加5个服务电话')
  }
}

const onDelPhone = (arr, i) => {
  arr?.splice(i, 1)
}

const onSubmit = debounce(() => {
  const params = {
    ...formData,
    phone_data: formData.phone_data?.length > 0 ? formData.phone_data?.filter(v=>v.phone)?.map(v=> v.phone): []
  }

  setContactInformationAxios(params, props.teamId).then(()=> {
    MessagePlugin.success('保存成功')
    onClose();
  }).catch((err)=> {
    MessagePlugin.error(err?.message)
  })


});

const onOpen = (data?: any) => {
  formData.address =  '';
  formData.channel_type =  handleObj.channel_type;
  formData.phone_data = [{phone: ''}];
  formData.zip_code = '';
  formData.fax = '';
  formData.email = '';

  onGetSettingInfo().then((srcData: any)=> {

    // srcData =
    if(srcData) {
      formData.address = srcData?.address || '';
      formData.channel_type = srcData?.channel_type || '';
      formData.phone_data = srcData?.phone_data?.length > 0 ? srcData.phone_data.map(v=>({phone:v})) : [{phone: ''}];
      formData.zip_code = srcData?.zip_code || '';
      formData.fax = srcData?.fax || '';
      formData.email = srcData?.email || '';
    }
    visible.value = true;
  }).catch(()=> {

    visible.value = true;
  })
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose,
});
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";
.iconaddcircle {
  color: #4D5EFF;
  font-size: 20px;
}
.iconremove {
  color: #D54941;
  font-size: 20px;
}
// :deep(.t-form__item) {
//   margin-right: 0;
//   margin-bottom: 0 !important;
// }

.footer {
  padding: 24px;
  padding-top: 16px;
}
// :deep(.t-dialog__footer) {
//   padding-top: 0 !important;
// }
:deep(.detail-control) {
  margin-bottom: 16px !important;
  margin-top: 0 !important;
}

:deep(.t-form__label) {
  white-space: wrap !important;
}

:deep(.t-form__label--top) {
  user-select:none;
}


:deep(.t-popup) {
  z-index: 2501;
}

.searchForm {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  // gap: 24px;
  &-item {
    min-width: 324px;
    width: 100%;
  }
}

.t-alert--info {
  padding: 8px 16px;
}

.form {
  // margin-top: 10px;
}

// :deep(.t-dialog__body) {
//   overflow: hidden !important;
//   padding-bottom: 0;
// }

.inputFlex {
  display: flex;
  flex-direction: column;
  width: 100%;

  .tips {
    font-size: 14px;

    font-weight: 400;
    color: #717376;
  }
}

.toBody {
  //   max-height: 70vh;
  //   overflow: auto;
  // padding: 0 24px;
  padding: 0 2px;
  .scroll {
    padding: 0 22px;
    overflow-y: overlay;
    height: calc(560px - 72px - 80px);

    .tip {
      border-radius: 8px;
      background: var(--kyy_color_alert_bg_bule, #EAECFF);
      padding: 8px 24px;
      display: flex;
      align-items: center;
      gap: 8px;
      .icon {
        font-size: 20px;

      }
      .text {
        color: var(--kyy_color_alert_text, #1A2139);
        font-size: 14px;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
  }
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}


</style>
