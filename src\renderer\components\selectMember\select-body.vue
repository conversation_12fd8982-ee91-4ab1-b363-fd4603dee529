<!--
*     props {
*       visible =>   Boolean 默认false，支持v-model语法糖
*
*       menu => 左侧显示的菜单，数组['recent','friend','orgcontacts','organize','groups','external','tags','rank','partyBuild']，默认全部显示
*      （recent：最近100个联系人,friend：我的好友,orgcontacts：组织联系人,organize：我的组织列表，groups：我的群组,external：外部组织架构,tags：标签,rank:排行,partyBuild:党建）
*
*       max => 最大选择人数，默认不限制
*
*       cardId => 数组 当前身份的cardIds，不传默认取全部身份卡id
*
*       selectList => 已经选择的人员，数组[cardId],匹配cardId
*
*       disableList => 禁止选择的人员, 数据规则同selectList
*
*       teamId => 数组，查询组织时显示对应的组织信息，用于过滤组织显示
*
*       diyData => 允许传进来的自己的数据源,需要遵照如下格式传进来,否则会报错.如{rank: array<{格式内如如下}>}
*           Array<{
*           argetId: cur.group.group,  身份卡id
*           cardId: cur.group.group,   同上,构建消息需要
*           avatar: '',   头像
*           team: '',     组织名,没有为空
*           teamId: '',   组织id,没有为空
*           openId: '',   个人openid
*           name: '',     当前身份卡名字
*           from: 'tags', 来源,用于搜索相关人员,跟menu里的菜单保持同步
*           conversationType: 3,   群聊还是单聊  1:单聊 3:群聊
*           select: false,     是否选中,默认false
*           }
*          >
*
*     }
*
*     events {
*       confirm => 点击确定按钮之后会把选择的人员返回  格式为
*        {
*         targetId: '', 同cardId,业务场景需要，后续考虑删除cardId
*         cardId: '',
*         avatar: '',
*         team: '',
*         teamId: '',
*         openId: '',
*         name: '',
*         from: '',
*         conversationType: 1, 单聊1群聊3
*         select: false,
*         attachment: {     单聊附加信息
*           member: []
*           creatorCardId: '',
*           relation: '',
*         }
*       }[]
*    }
-->
<template>
  <div class="card">
    <div class="members">
      <div ref="searchDom" style="padding: 12px 16px 12px 16px">
        <Input v-model="search" :placeholder="t('account.search')" @change="searchPerson">
          <template #prefix-icon>
            <!-- <SvgIcon name="im-history" class="svg-size20" /> -->
            <i class="i-svg:im-history text-20 color-text-3" />
          </template>
        </Input>
      </div>
      <div
        v-show="selectType && selectType !== 'search'"
        v-if="breadOptions.length"
        ref="breadcrumbDom"
        style="padding: 0 8px"
      >
        <Breadcrumb v-if="breadOptions.length">
          <div class="back flex-center cursor" @click="back">
            <img src="@renderer/assets/svg/icon_arrowLeft.svg" alt="" />{{ t("account.back") }}
          </div>
          <BreadcrumbItem v-for="item in breadOptions" :key="item.name" @click="changeDepartment(item)">
            <t-tooltip :content="item.name" :show-arrow="false" placement="top">
              <div class="line-1">{{ item.name }}</div>
            </t-tooltip>
          </BreadcrumbItem>
        </Breadcrumb>
        <Checkbox
          v-show="showCheckAll"
          class="selectAll"
          :disabled="disabledCheckAll"
          :checked="checkAll"
          :indeterminate="indeterminate"
          :on-change="handleSelectAll"
        >{{ t('zx.contacts.selectAll') }}</Checkbox>
      </div>
      <div ref="listDom" class="list scrollbar">
        <div v-lkloading="{ show:loadingData, height:false, opacity:false }">
          <List v-if="selectType">
            <div v-if="selectType === 'organize'">
              <ListItem
                v-for="item in renderDepartmentList"
                :key="item.departmentId"
                class="list-item"
                @click="nextDepartment(item)"
              >
                <Checkbox
                  v-model="item.select"
                  class=""
                  @click.stop
                  @change="(v) => selectDepartment(v, item)"
                />
                <img
                  style="width: 24px; height: 24px; border-radius: 50%"
                  class="list-icon mr8"
                  src="@renderer/assets/svg/people_notSelected.svg"
                  alt=""
                />
                <div
                  style="flex: 1; margin-right: 30px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden"
                >
                  {{ item.name }} ({{ item.staffsCount }})
                </div>
                <div :class="['next-level', item.select ? 'gray' : '']">
                  {{ t("contacts.sub") }}
                </div>
              </ListItem>
            </div>
            <div v-if="selectType === 'tags'">
              <ListItem
                v-for="item in tagsList"
                :key="item.id"
                class="list-item"
                @click="viewTagPerson(item)"
              >
                <Checkbox
                  v-model="item.select"
                  class=""
                  disabled
                  @click.stop
                />
                <img
                  style="width: 24px; height: 24px; border-radius: 50%"
                  class="list-icon mr8"
                  :src="icontag"
                  alt=""
                />
                <div
                  style="flex: 1; margin-right: 30px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden"
                >
                  {{ item.des }} ({{ item?.count || 0 }})
                </div>
                <div :class="['next-level', item.select ? 'gray' : '']">
                  {{ t("contacts.sub") }}
                </div>
              </ListItem>
            </div>
            <div v-if="['platform', 'receiver'].includes(selectType)">
              <ListItem
                v-for="item in (selectType === 'platform' ? renderPlatformList : renderReceiverList)"
                :key="item.id"
                class="list-item"
                @click="viewPRPerson(item)"
              >
                <Checkbox
                  v-model="item.select"
                  class=""
                  @click.stop
                  @change="(v) => selectDepartment(v, item)"
                />
                <img
                  style="width: 24px; height: 24px; border-radius: 50%"
                  class="list-icon mr8"
                  src="@renderer/assets/svg/people_notSelected.svg"
                  alt=""
                />
                <div
                  style="flex: 1; margin-right: 30px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden"
                >
                  {{ item.name }} ({{ item.staffsCount }})
                </div>
                <div :class="['next-level', item.select ? 'gray' : '']">
                  {{ t("contacts.sub") }}
                </div>
              </ListItem>
            </div>
            <ListItem
              v-for="item in personList[selectType]"
              :key="item.targetId"
              class="list-item"
              :class="{
                'list-item-disabled': (props.max > 0 && selectPersons.length >= props.max) || item?.disabled,
              }"
              @click="selectPerson(item)"
            >
              <span class="f-align" @click.stop.prevent="selectPerson(item)">
                <Checkbox
                  v-model="item.select"
                  :disabled="(props.max > 0 && selectPersons.length >= props.max) || item?.disabled"
                  class=""
                />
              </span>
              <groupAvatar
                v-if="selectType === 'groups'"
                style="border-radius: 50%; transform: scale(0.6); margin: 0 0 0 -8px"
                :avatars="item.avatar.split('|')"
              ></groupAvatar>
              <avatar
                v-else
                class="mr8"
                :image-url="item.avatar"
                :user-name="item.name"
                avatar-size="24px"
                round-radius
              />
              <div style="white-space: nowrap" class="companyBox">
                <span class="companyName">{{ item.name }}</span>
                <span
                  v-if="item.team && selectType !== 'organize' && item.targetId?.[0] === '#'"
                  class="company changeCardOrgStyle"
                >{{ item.team }}</span>
                <!-- <span v-if="selectType === 'search'">({{ getFrom(item.from) }})</span> -->
              </div>
            </ListItem>
          </List>
          <List v-else>
            <template v-if="props.isLevel && props.menu.length === 1">
              <ListItem
                v-for="item in personList[props.isLevel]"
                :key="item.targetId"
                class="list-item"
                :class="{
                  'list-item-disabled': (props.max > 0 && selectPersons.length >= props.max) || item?.disabled,
                }"
                @click="selectPerson(item)"
              >
                <span class="f-align" @click.stop.prevent="selectPerson(item)">
                  <Checkbox
                    v-model="item.select"
                    :disabled="(props.max > 0 && selectPersons.length >= props.max) || item?.disabled"
                    class=""
                  />
                </span>
                <avatar
                  class="mr8"
                  :image-url="item.avatar"
                  :user-name="item.name"
                  avatar-size="24px"
                  round-radius
                />
                <div style="white-space: nowrap" class="companyBox">
                  <span class="companyName">{{ item.name }}</span>
                  <span
                    v-if="item.team && selectType !== 'organize' && item.targetId[0] === '#'"
                    class="company changeCardOrgStyle"
                  >{{ item.team }}</span>
                  <!-- <span v-if="selectType === 'search'">({{ getFrom(item.from) }})</span> -->
                </div>
              </ListItem>
            </template>
            <template v-else>
              <ListItem
                v-for="item in options"
                :key="item.name"
                class="list-item"
                @click="showPerson(item)"
              >
                <img class="list-icon mr8" :src="item.icon" alt="" />
                <div
                  style="flex: 1; margin-right: 30px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden"
                >
                  {{ item.name }}
                  <!-- 3333333333333{{item}} -->
                </div>
                <img style="position: absolute; right: 8px" src="@renderer/assets/svg/icon_arrowRight.svg" alt="" />
              </ListItem>
            </template>
          </List>
          <!-- <Tloading :loading="loadingData" /> -->
        </div>
      </div>
    </div>
    <div class="split-line"></div>
    <div class="select-person">
      <div class="select-count">
        <span @click="getAllSelectPerson">
          {{ isShowText ? t("member.impm.sys_6") : t("contacts.selectMember") }}：{{ getSelectCount() }}</span>
        <iconpark-icon name="icondelete" style="font-size: 17px; color: #4D5EFF" @click="removeAll"></iconpark-icon>
      </div>
      <List class="list">
        <ListItem v-for="item in selectDepartmentList" :key="item.openid" class="list-item">
          <img
            style="width: 24px; height: 24px; border-radius: 50%"
            class="list-icon mr8"
            src="@renderer/assets/svg/people_notSelected.svg"
            alt=""
          />
          <div>{{ `${item.name} (${item.staffsCount})` }}</div>
          <img
            style="position: absolute; right: 8px"
            src="@renderer/assets/svg/icon_close.svg"
            alt=""
            @click="selectDepartment(false, item)"
          />
        </ListItem>
        <ListItem v-for="item in selectTagsList" :key="item.id" class="list-item">
          <img
            style="width: 24px; height: 24px;border-radius: 50%;"
            class="list-icon mr8"
            :src="icontag"
            alt=""
          />
          <div>{{ `${item.des} (${tagCount(item)})` }}</div>
          <img
            style="position: absolute; right: 8px"
            src="@renderer/assets/svg/icon_close.svg"
            alt=""
            @click="selectTags(false, item)"
          />
        </ListItem>
        <ListItem v-for="item in selectPersons" :key="item.openid" class="list-item">
          <groupAvatar
            v-if="item.conversationType === 3"
            style="border-radius: 50%; transform: scale(0.6); margin: 0 0 0 -8px"
            :avatars="item.avatar.split('|')"
            class="mr8"
          ></groupAvatar>
          <avatar
            v-else
            class="mr8"
            :image-url="item.avatar"
            :user-name="item.name"
            avatar-size="24px"
            round-radius
          />
          <div class="companyBox">
            <span class="companyName">{{ item.name }}</span>
            <span v-if="item.team && item.targetId[0] === '#'" class="company changeCardOrgStyle">{{ item.team }}</span>
          </div>
          <img
            style="position: absolute; right: 8px"
            src="@renderer/assets/svg/icon_close.svg"
            alt=""
            @click="removePerson(item)"
          />
        </ListItem>
      </List>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, nextTick, onMounted, computed, inject, watchEffect, watch } from "vue";
import { List, ListItem, Breadcrumb, BreadcrumbItem, Checkbox, Input, Loading } from "tdesign-vue-next";
import { getPlatformUser, getPlatformAdmin, getOrganizeList } from "@renderer/api/contacts/api/organize";
import groupAvatar from "@renderer/views/contacts/components/groupAvatar.vue";

import recentContact from "@renderer/assets/svg/recentContact1.svg";
import chartGraph from "@renderer/assets/svg/icon_organize.svg";
import iconpeople from "@renderer/assets/svg/icon-people.svg";
import iconpeoples from "@renderer/assets/svg/iconpeoples.svg";
import iconpeopless from "@renderer/assets/svg/icon-group.svg";
import icontag from "@renderer/assets/svg/icontag.svg";
import iconReceiver from "@renderer/assets/svg/icon_receiver.svg";
import iconPlatuser from "@renderer/assets/svg/icon_platuser.svg";
import iconmenutag from "@renderer/assets/svg/icon_menu_tag.svg";
import avatar from "@renderer/components/kyy-avatar/index.vue";
import { getGroupMembers } from '@renderer/views/message/service/request';
import { useI18n } from "vue-i18n";
import { getImCardIds, getProfilesInfo } from "@renderer/utils/auth";
import _ from "lodash";
import { cardIdType } from "@renderer/views/identitycard/data";
import { removeDuplicates } from "@renderer/utils/myUtils";
import selectData from "./data";

import SvgIcon from "@/components/SvgIcon.vue";

const { t } = useI18n();
const props = defineProps({
  // recent,follow,friend,orgcontacts,organize,groups
  menu: {
    type: Array,
    default: () => ["recent", "friend", "orgcontacts", "organize", "groups", "external", "tags", "rank", "partyBuild"],
  },
  max: {
    type: Number,
    default: 0,
  },
  cardId: {
    type: Array<string>,
    default: () => getImCardIds(),
  },
  selectList: {
    type: Array,
    default: () => [],
  },
  disableList: {
    type: Array,
    default: () => [],
  },
  teamId: {
    type: Array<string>,
    default: () => [],
  },
  // 语音通话增加成员需要群成员选项
  groupID: {
    type: String,
    default: '',
  },
  // group群组类型，增加groupTypeObj选项
  groupType: {
    type: String,
    default: "",
  },
  // 组织类型
  type: {
    type: String,
    default: "",
  },
  diyData: {
    type: Object,
    default: {},
  },
  // 根据不同业务模块场景，进行特殊处理，现在活动模块有特殊需求，传递['activity']
  extendFrom: {
    type: Array<string>,
    default: () => [],
  },
  // 不展示menu列表-->直接选择二级列表--:比如组织下面的所有人(需要条件: menu必须只能是一个)
  isLevel: {
    type: String,
    default: "",
  },
  emptyCardId: { // 平台需要不受分组权限影响，传true
    type: Boolean,
    default: false,
  },
});
const groupTypeObj = {
  family: { family: true },
};
const search = ref("");
const searchDom = ref(null);
const breadcrumbDom = ref(null);
const listDom = ref(null);
const breadOptions = ref([]);
const options = ref([]);
const defaultOptions = ref([
  {
    // 平台成员
    name: t("contacts.platformMember"),
    type: "platform",
    icon: iconPlatuser,
  },
  {
    // 对接人
    name: t("contacts.receiver"),
    type: "receiver",
    icon: iconReceiver,
  },
  {
    name: t("contacts.recent"),
    type: "recent",
    icon: recentContact,
  },
  // {
  //     name: t('contacts.follow'),
  //     type: 'follow',
  //     icon: iconstar,
  // },
  {
    name: t("contacts.friend"),
    type: "friend",
    icon: iconpeople,
  },
  {
    name: t("contacts.groups"),
    type: "groups",
    icon: iconpeoples,
  },
  {
    name: t("im.public.groupMember"),
    type: "groupMember",
    icon: iconpeoples
  },
  {
    name: t("contacts.organizePerson"),
    type: "orgcontacts",
    icon: iconpeopless,
  },
  {
    name: t("zx.contacts.menuTagTitle"),
    type: "tags",
    icon: iconmenutag,
  },
]);
const loadingData = ref(false);
const disabledCheckAll = computed(() => props.max === 1);
const personList = ref({
  recent: [],
  follow: [],
  friend: [],
  orgcontacts: [],
  organize: [], // 不包含部门
  groups: [],
  groupMember: [],
  tags: [],
  rank: [],
  partyBuild: [], // 党建
  activityExternal: [], // 党建
  search: [], // 搜索出来的人
  platform: [], // 平台成员
  receiver: [], // 对接人
  gv: [], // 政企代表人
});
const tagsList = ref([]); // 保存标签列表
let tagsListDefault = []; // 缓存标签列表数据
const renderDepartmentList = ref([]);
const renderPlatformList = ref([]);
const renderReceiverList = ref([]);
const organizeDetail = ref([]); // 保存所有组织的相关信息
const selectTeamId = ref(); // 选择的组织id
const selectTeamName = ref(); // 选择的组织name
const selectType = ref("");
const selectPersons = ref([]); // 选中的人
const allPerson = ref([]);
let selectOrg;
let departmentInfo;
const selectDepartmentList = ref([]); // 选中的部门
let selectDepartmentStaffs = {}; // 选中的部门下的人{ departmentId: staffs[] }
const selectTagsList = ref([]); // 选中的标签
let selectTagsStaffs = {}; // 选中的标签下的人{ departmentId: staffs[] }
const tagDetails = {}; // 标签详情数据
let _allOrgList = [];
const externalParams = inject("views.customerService.content.externalParams");
const showPerson = (item) => {
  if (item.type === "organize") {
    selectOrg = organizeDetail.value.find((v) => v.teamId === item.teamId);
    // selectOrg = await selectData.departmentList(item.teamId, item.name);
    renderDepartmentList.value = selectOrg.children;
    personList.value.organize = selectOrg.staffs;
    selectTeamId.value = item.teamId;
    selectTeamName.value = item.name;
    breadOptions.value = [{ name: item.name }];
  } else if (item.type === "tags") {
    tagsList.value = _.cloneDeep(tagsListDefault);
    initTagsSelectStatus();
    personList.value.tags = [];
    breadOptions.value = [{ name: t("zx.contacts.menuTagTitle") }];
  } else if (item.type === "platform") {
    renderPlatformList.value = globalPdata.value[0];
    personList.value.platform = globalPdata.value[1];
    breadOptions.value = [{ name: t("contacts.platformMember") }];
  } else if (item.type === "receiver") {
    renderReceiverList.value = globalRdata.value[0];
    personList.value.receiver = globalRdata.value[1];
    breadOptions.value = [{ name: t("contacts.receiver") }];
  } else {
    const bread = options.value.find((v) => v.type === item.type);
    breadOptions.value = [{ name: bread.name }];
  }
  selectType.value = item.type;
  calcListHeight();
};
const back = () => {
  if (search.value) {
    searchPerson("");
  }
  selectType.value = "";
  search.value = "";
  calcListHeight();
};
const calcListHeight = () => {
  nextTick(() => {
    const searchHeight = searchDom.value?.clientHeight;
    const breadHeight = breadcrumbDom.value?.clientHeight;
    listDom.value.style.height = `${354 - searchHeight - breadHeight - 8}px`;
  });
};
const getSelectCount = () => {
  const selectDepartmentCount = selectDepartmentList.value.reduce((acc, cur) => {
    acc += cur.staffsCount;
    return acc;
  }, 0);
  return selectDepartmentCount + selectPersons.value.length;
};
const selectPerson = (item) => {
  if ((props.max > 0 && selectPersons.value.length >= props.max) || item?.disabled) return;
  item.select = !item.select;
  item.select ? addSelectPerson(item) : removePerson(item);
};
const addSelectPerson = (item) => {
  let person = item;
  if (['organize', 'platform', 'receiver'].includes(selectType.value)) {
    person = allPerson.value.find((v) => v.targetId === item.targetId);
  }
  personSelectStatus(true, item.targetId);
  selectPersons.value.push(person);
  console.log("selectPerson", selectPersons.value);
  emits("selectPersons", selectPersons.value);
};
const removePerson = (item) => {
  if (item.disabled) return;
  personSelectStatus(false, item.targetId);
  selectPersons.value = selectPersons.value.filter((v) => v.targetId !== item.targetId);
  emits("selectPersons", selectPersons.value);
};
const removeAll = () => {
  // if (item.disabled) return;
  // personSelectStatus(false, item.targetId);
  // selectPersons.value = selectPersons.value.filter((v) => v.targetId !== item.targetId);
  // emits("selectPersons", selectPersons.value);
  selectPersons.value = selectPersons.value.filter((v) => {
    if (v.disabled) {
      return true;
    }
    personSelectStatus(false, v.targetId);
    return false;
  });
  emits("selectPersons", selectPersons.value);
};
const personSelectStatus = (selected, id) => {
  // 标签详情数据没进入allPerson，单独处理
  if (selectType.value === 'tags') {
    const persons = personList.value.tags?.filter((v) => v.targetId === id);
    persons.forEach((v) => {
      v.select = selected;
    });
  } else if (['organize', 'platform', 'receiver'].includes(selectType.value)) {
    const persons = personList.value[selectType.value].filter((v) => v.targetId === id);
    persons.forEach((v) => {
      v.select = selected;
    });
  }
  // const personsTypes = personList.value[selectType.value].filter((v) => v.targetId === id);
  // console.error('personsTypes', personsTypes);
  // personsTypes.forEach((v) => {
  //   v.select = selected;
  // });
  const persons = allPerson.value.filter((v) => v.targetId === id);

  persons.forEach((v) => {
    v.select = selected;
  });
};
const getDepartmentInfo = async (list) => {
  // 获取当前用户组织的详细信息
  if (!list.length) return;
  const team = list.shift();
  const res = await selectData.departmentList(team.teamId, team.name);
  organizeDetail.value.push({
    teamId: res.teamId,
    children: res.departmentInfoList,
    staffs: res.staffList,
  });
  allPerson.value = [...allPerson.value, ...res.staffList];
  await depthFind(res.departmentInfoList, -1, res.teamId, res.name);
  await getDepartmentInfo(list);
};
const globalPdata = ref([[], []]);
const globalRdata = ref([[], []]);
// 获取平台成员
const getPlatformUserList = async (data) => {
  try {
    const list = await selectData.getPlatformUserList(data);
    if (['government', 'association'].includes(data?.type)) {
      globalPdata.value = list;
      if (list[0].length) {
        const staffs = list[0].reduce((acc, cur) => [...acc, ...cur.staffs], []);
        allPerson.value = [...allPerson.value, ...staffs];
      }
      allPerson.value = [...allPerson.value, ...list[1]];
      renderPlatformList.value = globalPdata.value[0];
      personList.value.platform = globalPdata.value[1];
      return;
    }
    globalPdata.value = [[], list];
    renderPlatformList.value = globalPdata.value[0];
    personList.value.platform = globalPdata.value[1];
    allPerson.value = [...allPerson.value, ...list];
  } catch (err) {
    console.log("=====>", err);
  }
};
// 获取平台对接人
const getPlatformAdminList = async (data) => {
  try {
    const list = await selectData.getPlatformAdmin(data);
    if (['government', 'association'].includes(data?.type)) {
      globalRdata.value = list;
      if (list[0].length) {
        const staffs = list[0].reduce((acc, cur) => [...acc, ...cur.staffs], []);
        allPerson.value = [...allPerson.value, ...staffs];
      }
      allPerson.value = [...allPerson.value, ...list[1]];
      renderReceiverList.value = globalRdata.value[0];
      personList.value.receiver = globalRdata.value[1];
      return;
    }
    globalRdata.value = [[], list];
    renderReceiverList.value = globalRdata.value[0];
    personList.value.receiver = globalRdata.value[1];
    allPerson.value = [...allPerson.value, ...list];
  } catch (err) {
    console.log("=====>", err);
  }

  // let type = parma?.type;
  // const { data } = await getPlatformAdmin({ team_id: parma.teamId, type });
  // if (data.code === 0) {
  //   const staffList = data.data?.length
  //     ? await selectData.combPlatData(data.data, { teamId: parma.teamId, myCardId: parma.myCardId })
  //     : [];
  //   personList.value.receiver = staffList;
  //   allPerson.value = [...allPerson.value, ...staffList];
  // }
};
const getExternalInfo = async (list) => {
  // 获取当前用户组织的详细信息
  if (!list.length) return;
  const team = list.shift();
  const res = await selectData.externalCardList(team);
  personList.value[team.type] = res;
  allPerson.value = [...allPerson.value, ...res];
  getExternalInfo(list);
};
const viewPRPerson = (department) => {
  if (department.select) return;
  console.log(department);
  if (['platform', 'receiver'].includes(selectType.value)) {
    renderPlatformList.value = [];
  } else {
    renderReceiverList.value = [];
  }
  allPerson.value = [...allPerson.value, ...department.staffs];
  personList.value[selectType.value] = searchAllperson(department.staffs);
  breadOptions.value.push({
    name: department.name,
    departmentId: department.departmentId,
  });
  departmentInfo = department;
  calcListHeight();
  console.log(personList.value.organize, allPerson.value);
};
const nextDepartment = (department) => {
  if (department.select) return;
  console.log('===>nextDepartment', department);
  renderDepartmentList.value = department.children;
  setTimeout(() => {
    console.log('====>222', 222);
  }, 1000);
  personList.value.organize = searchAllperson(department.staffs);
  console.log('====>personList.value.organize', personList.value.organize);
  breadOptions.value.push({
    name: department.name,
    departmentId: department.departmentId,
  });
  departmentInfo = department;
  calcListHeight();
  console.log(personList.value.organize, allPerson.value);
};
const selectDepartment = (v, department) => {
  if (v) {
    // 添加选择的部门
    let staffsList = [];
    if (['platform', 'receiver'].includes(selectType.value)) {
      staffsList = getDepartmentPRPerson(department);
    } else {
      staffsList = getDepartmentPerson(department);
    }

    allPerson.value = [...allPerson.value, ...staffsList];
    department.select = true;
    selectDepartmentList.value.push(department);
    selectDepartmentStaffs[department.departmentId] = staffsList;
  } else {
    // 取消选择的部门
    const ind = selectDepartmentList.value.findIndex((item) => item.departmentId === department.departmentId);
    department.select = false;
    selectDepartmentList.value.splice(ind, 1);
    selectDepartmentStaffs[department.departmentId] = [];
  }
  emits("selectPersons");
};
const selectTags = (v, tag) => {
  if (v) {
    // 添加选择的标签
    const staffsList = getTagsPerson(tag);
    tag.select = true;
    selectTagsList.value.push(tag);
    selectTagsStaffs[tag.id] = staffsList;
  } else {
    // 取消选择的标签
    const ind = selectTagsList.value.findIndex(
      (item) => item.id === tag.id
    );
    tag.select = false;
    selectTagsList.value.splice(ind, 1);
    selectTagsStaffs[tag.id] = [];
  }
  initTagsSelectStatus();
};
const getDepartmentPerson = (department) => {
  let departmentInfo = _.cloneDeepWith(department);
  let staffs = departmentInfo.staffs;
  let stack = [];
  stack = departmentInfo.children;
  // 控制是否跳出循环
  while (stack?.length) {
    let item = stack.shift();
    let children = item.children;
    staffs = [...staffs, ...item.staffs];
    if (children && children.length) {
      for (let i = children.length - 1; i >= 0; i--) {
        stack.unshift(children[i]);
      }
    }
  }
  return staffs;
};
const getDepartmentPRPerson = (department) => department.staffs;

const getTagsPerson = (tag) => tagDetails[tag.id];
const changeDepartment = (item) => {
  if (selectType.value === "organize") {
    search.value = "";
    const teamInfo = organizeDetail.value.find((v) => v.teamId === selectTeamId.value);
    breadOptions.value = [{ name: selectTeamName.value }];
    if (item?.departmentId) {
      departmentInfo = item;
      depthFind(teamInfo.children, item.departmentId);
    } else {
      departmentInfo = teamInfo;
      renderDepartmentList.value = teamInfo.children;
      personList.value.organize = teamInfo.staffs;
    }
  }
  if (selectType.value === "tags") {
    if (item?.id) {
    } else {
      tagsList.value = _.cloneDeep(tagsListDefault);
      initTagsSelectStatus();
      personList.value.tags = [];
      breadOptions.value = [{ name: t("zx.contacts.menuTagTitle") }];
    }
  }
  if (['platform'].includes(selectType.value)) {
    if (item?.departmentId) {
      renderPlatformList.value = [];
      personList.value[selectType.value] = item.staffs;
    } else {
      if (breadOptions.value.length > 1) {
        breadOptions.value.pop();
      }
      departmentInfo = null;
      renderPlatformList.value = globalPdata.value[0];
      personList.value[selectType.value] = globalPdata.value[1];
    }
  }
  if (['receiver'].includes(selectType.value)) {
    if (item?.departmentId) {
      renderReceiverList.value = [];
      personList.value[selectType.value] = item.staffs;
    } else {
      if (breadOptions.value.length > 1) {
        breadOptions.value.pop();
      }
      departmentInfo = null;
      renderReceiverList.value = globalRdata.value[0];
      personList.value[selectType.value] = globalRdata.value[1];
    }
  }
  calcListHeight();
};
const searchAllperson = (list): any =>
  list.map((v) => {
    // allPerson数据是一直push，很多重复数据，但数据不全，保险匹配最后一个
    let info = allPerson.value.find((p) => p.targetId === v.cardId) ?? {};
    let lastIndex = allPerson.value.findLastIndex((p) => p.targetId === v.cardId);
    let lastInfo = allPerson.value[lastIndex] ?? {};
    return {
      ...info,
      ...lastInfo,
    };
  });
// 深度查找优先 DFS
const depthFind = async (data, departmentId, teamId?, teamName?) => {
  let stack = [];
  stack = _.cloneDeepWith(data);
  // 控制是否跳出循环
  let loop = !!stack.length;
  while (loop) {
    let item = stack.shift();
    let children = item.children;
    if (!item.bread) {
      item.bread = [
        {
          departmentId: item.departmentId,
          name: item.name,
        },
      ];
    }
    if (departmentId === -1) {
      const res = await selectData.combOrgData(item.staffs, {
        name: teamName,
        teamId,
      });
      allPerson.value = [...allPerson.value, ...res];
    }
    if (+item.departmentId === +departmentId) {
      renderDepartmentList.value = item.children;
      personList.value.organize = searchAllperson(item.staffs);
      breadOptions.value = [...breadOptions.value, ...item.bread];
      loop = false;
    }
    if (children && children.length) {
      for (let i = children.length - 1; i >= 0; i--) {
        children[i].bread = [...item.bread, { departmentId: children[i].departmentId, name: children[i].name }];
        stack.unshift(children[i]);
      }
    } else {
      stack.length === 0 && (loop = false);
    }
  }
};
const searchPerson = _.debounce(
  (v) => {
    // 查看是否在一级目录，一级目录查询所有人，非一级目录只查对应目录的人
    if (!selectType.value || selectType.value === "search") {
      selectType.value = v ? "search" : "";
      personList.value.search = [];
      // breadOptions.value = [];
    }
    console.log('====>selectType.value', v, selectType.value);
    let filterMenu = selectType.value;
    if (selectType.value === "organize") {
      filterMenu = selectOrg.teamId;
      renderDepartmentList.value = [];
    }
    if (selectType.value === "organize" && !v) {
      changeDepartment(departmentInfo);
      return;
    }
    if (['platform'].includes(selectType.value)) {
      filterMenu = props.teamId[0];
      renderPlatformList.value = [];
    }
    if (['platform'].includes(selectType.value)) {
      filterMenu = props.teamId[0];
      renderReceiverList.value = [];
    }
    if (['platform', 'receiver'].includes(selectType.value) && !v) {
      changeDepartment(departmentInfo);
      return;
    }
    if (selectType.value === "tags") {
      tagsList.value = [];
    }
    if (selectType.value === "tags" && !v) {
      changeDepartment({});
      return;
    }
    if (filterMenu) {
      const filters = allPerson.value.filter((person) => {
        if (selectType.value === "search") {
          return ~person.name.indexOf(v);
        }
        if (['organize', 'platform', 'receiver'].includes(selectType.value)) {
          return (
            ~person.name.indexOf(v) &&
            !["recent", "friend", "orgcontacts", "groups"].includes(person.from) &&
            person.teamId === filterMenu
          );
        }
        if (selectType.value === "tags") {
          return ~person.name.indexOf(v);
        }
        return ~person.name.indexOf(v) && person.from === filterMenu;
      });
      console.log('====>filters', filters, selectType.value);
      const finalList = [];
      if (selectType.value === "search") {
        personList.value[selectType.value] = removeDuplicates(filters, 'targetId');
        return;
      }
      // 搜索组织下的成员
      if (["organize"].includes(selectType.value)) {
        const staffsList = getDepartmentPerson(departmentInfo);
        console.log('====>staffsList', staffsList);
        filters.forEach((v) => {
          const index = staffsList.findIndex((p) => p.cardId === v.targetId);
          index > -1 && finalList.push(v);
        });
      } else {
        filters.forEach((v) => {
          const index = personList.value[selectType.value].findIndex((p) => p.targetId === v.targetId);
          index > -1 && finalList.push(v);
        });
      }
      console.log('====>finalList', finalList);
      personList.value[selectType.value] = removeDuplicates(finalList, 'targetId');
    }
  },
  50,
  {
    leading: true,
    trailing: true,
  },
);

const checkAll = computed(() => {
  let personList_checkAll = personList.value[selectType.value]?.filter((v) => !v.disabled)?.every((item) => item.select);
  // 空数组上述结果为true，加下判断
  // if (!personList.value[selectType.value]?.length) {
  //   personList_checkAll = false;
  // }

  let organize_checkAll = true;
  let tags_checkAll = true;
  let platform_checkAll = true;
  let receiver_checkAll = true;
  if (['organize', 'platform', 'receiver'].includes(selectType.value)) {
    organize_checkAll = renderDepartmentList.value?.every((item) => item.select);
  }
  if (selectType.value === 'tags') {
    tags_checkAll = tagsList.value?.every((item) => item.select);
  }
  if (selectType.value === 'platform') {
    platform_checkAll = renderPlatformList.value?.every((item) => item.select);
  }
  if (selectType.value === 'receiver') {
    receiver_checkAll = renderReceiverList.value?.every((item) => item.select);
  }
  return personList_checkAll && organize_checkAll && tags_checkAll && receiver_checkAll && platform_checkAll;
});
const showCheckAll = computed(() => {
  let personList_checkAll = !!personList.value[selectType.value]?.filter((v) => !v.disabled)?.length;
  let organize_checkAll = false;
  let tags_checkAll = false;
  let platform_checkAll = false;
  let receiver_checkAll = false;
  if (['organize'].includes(selectType.value)) {
    organize_checkAll = !!renderDepartmentList.value?.length;
  }
  if (['platform'].includes(selectType.value)) {
    platform_checkAll = !!renderPlatformList.value?.length;
  }
  if (['receiver'].includes(selectType.value)) {
    receiver_checkAll = !!renderReceiverList.value?.length;
  }
  if (selectType.value === 'tags') {
    tags_checkAll = !!tagsList.value?.length;
  }
  return personList_checkAll || organize_checkAll || tags_checkAll || platform_checkAll || receiver_checkAll;
});
const checkSelect = () => {
  if (props.selectList.length) {
    allPerson.value = allPerson.value.map((v) => {
      if (props.selectList.includes(v.targetId)) {
        v.select = true;
        !~selectPersons.value.findIndex((s) => s.targetId === v.targetId) && selectPersons.value.push(v);
      }
      return v;
    });
  }
};
// 标签打开是直接赋值的深拷贝数据源，覆盖了原来的select数值，不修改原有赋值逻辑，新增初始化select逻辑
const initTagsSelectStatus = () => {
  tagsList.value?.forEach((item) => {
    item.select = selectTagsList.value?.some((v) => v.id === item.id);
  });
  personList.value.tags?.forEach((item) => {
    item.select = selectPersons.value?.some((v) => v.targetId === item.targetId);
  });
};





const tagCount = (item) => tagDetails[item.id]?.length || 0;

const checkDisableList = () => {
  if (props.disableList.length) {
    allPerson.value = allPerson.value.map((v) => {
      if (props.disableList.includes(v.targetId)) {
        v.disabled = true;
      }
      return v;
    });
  }
};

const viewTagPerson = async (item) => {
  tagsList.value = [];
  breadOptions.value.push({ name: item.des, id: item.id });
  // const res = await selectData.tagDetailList(item.id, item.cardId);
  // let datas =  searchAllperson(res);
  // if (props.extendFrom?.includes('activity')) {
  //     // 活动联系人过滤掉平台成员
  //     datas = res.filter(v=>v.attachment.relation !== 'PLATFORM_FRIEND')
  //   }
  personList.value.tags = tagDetails?.[item.id];
};

// 当前组织经过selectData.organizeList的信息，需要新的字段自行添加，可以获取选择组织的信息
const curOrgInfo = ref({});
const loadData = async () => {
  console.log("------------------------>\n", props, "\n------------------------>");
  loadingData.value = true;
  back();
  selectDepartmentList.value = [];
  selectTagsList.value = [];
  selectPersons.value = [];
  allPerson.value = [];
  personList.value = {
    recent: [],
    follow: [],
    friend: [],
    orgcontacts: [],
    organize: [], // 不包含部门
    groups: [],
    groupMember: [],
    tags: [],
    rank: [],
    partyBuild: [],
    activityExternal: [],
    search: [], // 搜索出来的人
    platform: [], // 平台成员
    receiver: [], // 对接人
    gv: [], // 政企代表人
  };
  const showMenu = defaultOptions.value.filter((v) => props.menu.includes(v.type));
  await organizeList();
  console.log("=====>organizeList", props.teamId, _allOrgList);
  const organizeDetails = props.teamId.length
    ? _allOrgList.filter((v) => props.teamId.includes(v.teamId))
    : _allOrgList;
  const res = await selectData.organizeList(organizeDetails); // 从判断中拿出来，否则在menu只有一个，非organize且依赖organizeDetails的情况下就会报错，如data.ts的getPlatformUserList
  curOrgInfo.value = res?.[0] || {};
  if (props.menu.includes("organize")) {
    options.value = [...res, ...showMenu];
    organizeDetail.value = [];
    await getDepartmentInfo(res);
  } else {
    options.value = showMenu;
  }
  if (props.menu.includes("external")) {
    // 添加客服需要单独处理
    if (externalParams) {
      const res = await selectData.externalkefuCardList(externalParams);
      console.log(res,'resssssssssss2323');
      // 客服功能当前用户无身份卡且无身份卡成员列表显示错误出现一条没有名字的数据加if (res[0]?.team) {判断
      if (res[0]?.team) {
        options.value = [
          ...options.value,
          {
            name: res[0]?.team,
            type: `exorganize${externalParams.internal_teamId}`,
            icon: chartGraph,
          },
        ];
      }
      personList.value[`exorganize${externalParams.internal_teamId}`] = res;
      allPerson.value = [...allPerson.value, ...res];
      console.log(allPerson.value,'resssssssssss2323allPerson');
      console.log(personList.value,'resssssssssss2323personList');

    } else {
      const res = await selectData.externalList(props.cardId);
      console.log(res,'resssssssssss4545');

      options.value = [...res, ...options.value];
      console.log(options.value,'resssssssssssoptions.value');

      await getExternalInfo(res);
    }
  }
  // props.menu.includes('follow') && selectData.followListData().then(res => {
  //     personList.value.follow = res;
  //     allPerson.value = [...allPerson.value, ...res];
  // });
  if (props.menu.includes("friend")) {
    const res = await selectData.friendList();
    // 活动模块需要特殊处理，好友列表添加本人个人信息供选择
    if (props.extendFrom?.includes("activity")) {
      const peerInfo = getProfilesInfo();
      const profile = {
        targetId: peerInfo.openid,
        cardId: peerInfo.openid,
        avatar: peerInfo.avatar || "",
        team: "",
        teamId: "",
        openId: peerInfo.openid || "",
        name: peerInfo.title || "",
        from: "friend",
        conversationType: 1,
        select: false,
        attachment: {
          member: [
            {
              avatar: peerInfo.avatar || "",
              cardId: peerInfo.openid || "",
              openId: peerInfo.openid || "",
              staffName: peerInfo.title || "",
              comment: "",
              nickname: "",
              teamId: "",
              teamName: "",
              departmentId: "",
              departmentName: "",
              jobId: "",
              jobName: "",
              noDisturb: 0,
              stayOn: 0,
              viewHistory: false,
            },
            {
              avatar: peerInfo.avatar || "",
              cardId: peerInfo.openid || "",
              openId: peerInfo.openid || "",
              staffName: peerInfo.title || "",
              comment: "",
              nickname: "",
              teamId: "",
              teamName: "",
              departmentId: "",
              departmentName: "",
              jobId: "",
              jobName: "",
              noDisturb: 0,
              stayOn: 0,
              viewHistory: false,
            },
          ],
          creatorCardId: peerInfo.openid,
          relation: "",
        },
      };
      res.unshift(profile);
    }
    personList.value.friend = res;
    allPerson.value = [...allPerson.value, ...res];
  }
  if (props.menu.includes("recent")) {
    let res = await selectData.recentList(props.cardId);
    if (props.extendFrom?.includes("activity_inner")) {
      personList.value.recent = res.filter((v) => cardIdType(v.cardId) === "inner");
    } else if (props.extendFrom?.includes("activity_outer")) {
      personList.value.recent = res.filter((v) => cardIdType(v.cardId) === "outer");
    } else if (props.extendFrom?.includes("activity") || !props.menu.includes("platform")) {
      // 活动联系人，没有开通平台身份过滤掉平台成员
      personList.value.recent = res.filter((v) => v.attachment.relation !== "PLATFORM_FRIEND");
    } else {
      personList.value.recent = res;
    }
    allPerson.value = [...allPerson.value, ...res];
  }
  if (props.menu.includes("groups")) {
    let groupOption = props.groupType ? groupTypeObj[props.groupType] ?? {} : {};
    const res = await selectData.groupsList(props.cardId, groupOption);
    personList.value.groups = res;
    allPerson.value = [...allPerson.value, ...res];
  }
  if (props.menu.includes("groupMember")) {
  const members = await getGroupMembers(props.groupID);
  const groupMember = [];
    members.forEach(val => {
      groupMember.push({
        avatar: val.attachments.avatar,
        cardId: val.attachments.cardId,
        conversationType: 1,
        from: 'groupMember',
        name: val.attachments.staffName,
        openId: val.attachments.openId,
        select: false,
        targetId: val.attachments.cardId,
       });
    });
    personList.value.groupMember = groupMember;
    allPerson.value = [...allPerson.value, ...groupMember];
  }
  if (props.menu.includes("orgcontacts")) {
    const res = await selectData.getPairsList(props.cardId);
    personList.value.orgcontacts = res;
    allPerson.value = [...allPerson.value, ...res];
  }
  loadingData.value = false;
  console.log('====>personList.value', personList.value);
  if (props.menu.includes("tags")) {
    // 目前的业务标签只需要查询全部身份下的标签或者单个身份卡下的标签
    const cardId = props.cardId.length > 1 ? null : props.cardId[0];
    const res = await selectData.tagList(cardId);
    const length = res.length;
    // 活动标签过滤掉平台成员,先获取列表详情修改标签分组数量
    res.forEach(async (val, index) => {
      if (val.id) {
        const data = await selectData.tagDetailList(val.id, val.cardId);
        let datas = data;
        if (props.extendFrom?.includes("activity_inner")) {
          // 活动联系人,没有开通数字平台过滤掉平台成员
          datas = data.filter((v) => cardIdType(v.cardId) === "inner");
        } else if (props.extendFrom?.includes("activity_outer")) {
          datas = data.filter((v) => cardIdType(v.cardId) === "outer");
        } else if (props.extendFrom?.includes("activity") || !props.menu.includes("platform")) {
          // 活动联系人,没有开通数字平台过滤掉平台成员
          datas = data.filter((v) => cardIdType(v.cardId) !== "platform");
        }
        tagDetails[val.id] = datas;
        val.count = datas.length;
      }
      if (index === length - 1) {
        tagsList.value = res;
        tagsListDefault = _.cloneDeep(tagsList.value);
      }
    });
  }

  // 如果是等级，并且只有一个身份，则直接赋值
  if (props.isLevel && props.menu.length === 1) {
    personList.value[props.isLevel] = _.uniqBy(allPerson.value, (obj: any) => obj?.cardId || obj?.targetId);
  }

  if (props.menu.includes("rank") && props.diyData?.rank) {
    // rank 使用自定义进来的数据
    personList.value.rank = props.diyData.rank;
    selectType.value = "rank";
    allPerson.value = [...allPerson.value, ...props.diyData.rank];
  }

  if (props.menu.includes("partyBuild") && props.diyData?.partyBuild) {
    // pb 使用自定义进来的数据
    personList.value.partyBuild = props.diyData.partyBuild;
    selectType.value = "partyBuild";
    allPerson.value = [...allPerson.value, ...props.diyData.partyBuild];
  }
  if (props.menu.includes("activityExternal") && props.diyData?.activityExternal) {
    // pb 使用自定义进来的数据
    personList.value.activityExternal = props.diyData.activityExternal;
    selectType.value = "activityExternal";
    allPerson.value = [...allPerson.value, ...props.diyData.activityExternal];
  }

  if (props.menu.includes("platform")) {
    // 平台用户
    await getPlatformUserList({
      teamId: props.teamId[0],
      type: curOrgInfo.value?.platType,
      myCardId: props.emptyCardId ? '' : props.cardId[0],
    });
  }
  if (props.menu.includes("receiver")) {
    // 对接人
    await getPlatformAdminList({
      teamId: props.teamId[0],
      type: curOrgInfo.value?.platType,
      myCardId: props.cardId[0],
    });
  }
  if (props.menu.includes("governmentGroup")) {
    // 对接人
    const data = await selectData.getGovernmentStaffsPlatform(props.cardId[0], props.groupID);
    if (data) {
      personList.value.gv = data;
      selectType.value = "gv";
      allPerson.value = [...allPerson.value, ...data];
    }
  }
  checkSelect();
  checkDisableList();
};
const indeterminate = computed(() => false);

const handleSelectAll = (checked) => {
  console.log(checked);

  personList.value[selectType.value]?.filter((v) => !v.disabled)?.forEach((item) => {
    if (
      (props.max > 0 && selectPersons.value.length >= props.max) ||
      item?.disabled
    ) return;
    personSelectStatus(checked, item.targetId);
    if (checked) {
      if (!selectPersons.value?.some((v) => v.targetId === item.targetId)) {
        selectPersons.value.push(item);
      }
    } else {
      selectPersons.value = selectPersons.value.filter(
        (v) => v.targetId !== item.targetId
      );
    }
  });
  if (['organize', 'platform', 'receiver'].includes(selectType.value)) {
    renderDepartmentList.value?.filter((v) => !v.disabled)?.forEach((item) => {
      if (
        (props.max > 0 && selectPersons.value.length >= props.max) ||
        item?.disabled
      ) return;
      if (checked) {
        if (!selectDepartmentList.value?.some((v) => v.departmentId === item.departmentId)) {
          selectDepartment(checked, item);
        }
      } else {
        selectDepartment(checked, item);
      }
    });
  }
  if (['receiver'].includes(selectType.value)) {
    renderReceiverList.value?.filter((v) => !v.disabled)?.forEach((item) => {
      if (
        (props.max > 0 && selectPersons.value.length >= props.max) ||
        item?.disabled
      ) return;
      if (checked) {
        if (!selectDepartmentList.value?.some((v) => v.departmentId === item.departmentId)) {
          selectDepartment(checked, item);
        }
      } else {
        selectDepartment(checked, item);
      }
    });
  }
  if (['platform'].includes(selectType.value)) {
    renderPlatformList.value?.filter((v) => !v.disabled)?.forEach((item) => {
      if (
        (props.max > 0 && selectPersons.value.length >= props.max) ||
        item?.disabled
      ) return;
      if (checked) {
        if (!selectDepartmentList.value?.some((v) => v.departmentId === item.departmentId)) {
          selectDepartment(checked, item);
        }
      } else {
        selectDepartment(checked, item);
      }
    });
  }
  if (selectType.value === 'tags') {
    tagsList.value?.forEach((item) => {
      if (
        (props.max > 0 && selectPersons.value.length >= props.max) ||
        item?.disabled
      ) return;
      if (checked) {
        if (!selectTagsList.value?.some((v) => v.id === item.id)) {
          selectTags(checked, item);
        }
      } else {
        selectTags(checked, item);
      }
    });
  }
  emits("selectPersons", selectPersons.value);
};
const getAllSelectPerson = () => {
  let departmentStaffs = [];
  const keys = Object.keys(selectDepartmentStaffs);

  keys.forEach((key) => {
    departmentStaffs = [...departmentStaffs, ...selectDepartmentStaffs[key]].reduce((acc, cur) => {
      // 过滤掉同一个人，同一个人可能会出现在不同的部门
      if (!~acc.findIndex((v) => v?.targetId === cur.cardId)) {
        const msgPerson = allPerson.value.find((v) => v.targetId === cur.cardId);
        msgPerson && acc.push(msgPerson);
      }
      return acc;
    }, []);
  });
  const filterStaffs = departmentStaffs.reduce((acc, cur) => {
    // 过滤掉禁止选择的人和已经选择的人
    if (
      !~props.disableList.findIndex((d) => d === cur.cardId) &&
      !~selectPersons.value.findIndex((s) => s?.targetId === cur.cardId)
    ) {
      const msgPerson = allPerson.value.find((v) => v.targetId === cur.cardId);
      msgPerson && acc.push(msgPerson);
    }
    return acc;
  }, []);
  // 单独处理标签数据
  let tagsStaffs = [];
  const tagsKeys = Object.keys(selectTagsStaffs);
  tagsKeys.forEach((key) => {
    selectTagsStaffs[key]?.forEach((v) => {
      if (
        !~props.disableList.findIndex((d) => d === v.cardId) &&
        !~selectPersons.value.findIndex((s) => s.targetId === v.cardId) &&
        !~tagsStaffs.findIndex((s) => s.targetId === v.cardId)
      ) {
        tagsStaffs.push(v);
      }
    });
  });
  console.log('====>getAllSelectPerson', JSON.stringify([...tagsStaffs, ...filterStaffs, ...selectPersons.value]));
  return [...tagsStaffs, ...filterStaffs, ...selectPersons.value];
};

const emits = defineEmits(["selectPersons"]);
defineExpose({
  selectPersons,
  loadData,
  getAllSelectPerson,
});
// watch(() => JSON.stringify(props.cardId), loadData)
// watch(() => JSON.stringify(props.teamId), loadData)
const organizeList = async () => {
  if (_allOrgList.length === 0) {
    const { data } = await getOrganizeList();
    // 过滤平台身份
    // _allOrgList = data.data?.filter(item => cardIdType(item.cardId) !== 'platform');
    _allOrgList = data.data?.filter((item) => item.cardId);
  }
  return _allOrgList;
};
onMounted(async () => {
  if (props.menu.length) {
    loadData('onMounted');
  }
});

// 判断文案显示
const isShowText = () => props.isLevel || props.menu.includes("partyBuild");
</script>

<style lang="less" scoped>
@import "../../style/mixins.less";

.selectAll {
  padding: 8px 8px 0;
  height: 40px;
}

.list-item-disabled {
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.5);

  :deep(.t-checkbox.t-is-disabled .t-checkbox__input) {
    background-color: var(--td-bg-color-component-disabled) !important;
  }
  :deep(.t-checkbox.t-is-disabled.t-is-checked .t-checkbox__input) {
    background-color: #c9cfff !important;
  }
}

.flex {
  display: flex;
}

.f-align {
  display: flex;
  align-items: center;
}

.cursor {
  cursor: pointer;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.mr4 {
  margin-right: 4px;
}

.ml8 {
  margin-left: 8px;
}

.mr8 {
  margin-right: 8px;
}

.Breadcrumb {
  flex-wrap: wrap;
}

.card {
  width: 100%;
  height: 356px;
  border: 1px solid #e3e6eb;
  border-radius: 8px;
  display: flex;
}

.members {
  width: 316px;
  height: 100%;

  .back {
    margin-right: 4px;
    flex-shrink: 0;
    gap: 4px;
    color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    z-index: 999;
  }

  .t-breadcrumb {
    gap: 8px;
  }
  .t-breadcrumb__item .line-1 {
    padding: 0px 4px;
    color: var(--kyy_color_breadcrumb_text_default, #828da5);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .t-breadcrumb__item:last-child .line-1 {
    color: var(--kyy_color_breadcrumb_text_active, #1a2139);
    font-weight: 600;
  }
  .t-breadcrumb__item .line-1:hover {
    border-radius: 4px;
    background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
  }
}

.select-person {
  width: 316px;
  height: 100%;
  box-sizing: border-box;

  .select-count {
    height: 52px;
    padding-left: 16px;
    padding-right: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.list {
  width: 100%;
  height: calc(100% - 52px);
  overflow-y: auto;
  padding: 0 8px 8px 8px;
  position: relative;

  .list-item {
    display: flex;
    min-height: 40px;
    padding: 0px 28px 0px 8px;

    color: var(--text-kyy_color_text_1, #1a2139);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */

    :deep(.ListItem -main) {
      height: 100%;
      justify-content: flex-start;
      align-items: center;
    }

    &:hover {
      background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
      border-radius: 4px;
    }
  }
  .list-item-group {
    height: 46px;
  }
  .list-icon {
    width: 16px;
    height: 16px;
  }

  .next-level {
    position: absolute;
    right: 8px;
    color: #2069e3;
  }

  .gray {
    color: #ccc;
  }
}

.companyBox {
  width: 0;
  flex: 1;
  display: flex;
  gap: 4px;

  .companyName {
    vertical-align: middle;
    display: inline-block;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .changeCardOrgStyle {
    vertical-align: middle;
    max-width: calc(100% - 80px);
  }
}

.company {
  font-size: 12px;

  color: #e66800;
  line-height: 20px;
}

.select-person {
  width: 316px;
  height: 100%;
}

.split-line {
  width: 1px;
  height: 100%;
  background: #e3e6eb;
  align-self: center;
}

:deep(.select-person .t-list-item-main) {
  justify-content: flex-start;
  align-items: center;
}
:deep(.select-person .t-list) {
  background: none;
}

:deep(.t-breadcrumb) {
  flex-wrap: wrap;
}

// :deep(.t-breadcrumb__inner-text:hover) {
//   background: red;
// }
</style>
