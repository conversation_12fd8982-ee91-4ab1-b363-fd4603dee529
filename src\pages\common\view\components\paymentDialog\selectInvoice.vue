<template>
  <t-dialog
    :visible="selectInvoiceFlag"
    :close-btn="false"
    :header="true"
    :cancel-btn="null"
    :z-index="5003"
    :confirm-btn="null"
    attach="body"
    class="dialogSetForm-noPadding-450px"
    width="480"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
  >
    <!-- class="samlovf" -->
    <!-- class-name="samlovf" -->
    <template #header>
      <div class="flexboxs">
        <div>{{ t('banch.xzfp') }}</div>
        <img
          style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
          src="@assets/<EMAIL>"
          @click="resubmit()"
        />
      </div>
    </template>
    <div class="content-box">
      <div style="min-height: 250px">
        <div class="form-box">
          <t-form
            ref="selectFormRef"
            :rules="FORM_RULES"
            :data="selectFormData"
            label-align="top"
            :label-width="60"
            layout="verticalt"
            style="padding-top: 8px; width: 100%"
          >
            <t-form-item :label="t('order.selethead')" name="headerType">
              <div class="flexboxs">
                <t-select v-model="selectFormData.headerId" @change="onChange">
                  <t-option
                    v-for="(item, index) in invoiceListData"
                    :key="index"
                    :label="item.title"
                    :value="item.id"
                  />
                </t-select>
                <span
                  style="color: rgb(77, 94, 255); font-size: 14px; width: 130px; padding-left: 16px; cursor: pointer"
                  @click="addInvoiceHeaderFn"
                >
                  {{ t('banch.tjfpxx') }}
                </span>
              </div>
            </t-form-item>
            <div v-if="selectFormData.headerId" class="item-box">
              <div v-if="area === 'CN'" class="label-box">
                <div class="label-item">{{ t('banch.fplx') }}</div>
                <div class="value-item">
                  {{ selectFormData.invoiceType === 0 ? t('banch.ptfp') : t('banch.zzszyfp') }}
                </div>
              </div>
              <div v-if="area === 'CN' && selectFormData.invoiceType !== 1" class="label-box">
                <div class="label-item">{{ t('banch.ttlx') }}</div>
                <div class="value-item">
                  {{ selectFormData.headerType === 1 ? t('order.geren') : t('order.unit') }}
                </div>
              </div>
            </div>
            <t-form-item v-if="selectFormData.headerId" :label="t('order.invoiceHeader')" name="title">
              <t-input
                :value="selectFormData?.title"
                disabled
                :maxlength="30"
                :placeholder="'请输入' + t('order.invoiceHeader')"
                style="width: 100%"
              />
            </t-form-item>
            <t-form-item
              v-if="
                selectFormData.headerId &&
                area === 'CN' &&
                (selectFormData.headerType === 2 || selectFormData.headerType === 0)
              "
              :label="t('order.Unittaxnumber')"
              name="taxNumber"
            >
              <t-input
                :maxlength="18"
                :value="selectFormData.taxNumber"
                disabled
                :placeholder="'请输入' + t('order.Unittaxnumber')"
                style="width: 100%"
              />
            </t-form-item>
            <t-form-item
              v-if="
                selectFormData.headerId &&
                area === 'CN' &&
                selectFormData.headerType !== 1 &&
                selectFormData.invoiceType === 1
              "
              :label="t('order.Registeredaddress')"
              name="regAddress"
              :rules="[{ required: true, message: t('banch.zcdzbt') }]"
            >
              <t-input
                v-model="selectFormData.regAddress"
                :maxlength="100"
                :placeholder="'请输入' + t('order.Registeredaddress')"
                style="width: 100%"
              />
            </t-form-item>
            <t-form-item
              v-if="
                selectFormData.headerId &&
                selectFormData.headerType !== 1 &&
                area === 'CN' &&
                selectFormData.invoiceType !== 1
              "
              :label="t('order.Registeredaddress')"
              name="regAddress"
            >
              <t-input
                v-model="selectFormData.regAddress"
                :maxlength="100"
                :placeholder="'请输入' + t('order.Registeredaddress')"
                style="width: 100%"
              />
            </t-form-item>
            <t-form-item
              v-if="
                selectFormData.headerId &&
                area === 'CN' &&
                selectFormData.headerType === 2 &&
                selectFormData.invoiceType !== 1
              "
              :rules="[
                {
                  required: false,
                  message: t('order.Pleaseenterisokphone'),
                },

                {
                  pattern: /^[0-9]+$/,
                  message: t('order.Pleaseenterisokphone'),
                },
              ]"
              :label="t('order.RegistrationPhoneNumber')"
              name="regPhone"
            >
              <t-input
                v-model="selectFormData.regPhone"
                :maxlength="11"
                :placeholder="t('order.PleaseenterRegistrationPhoneNumber')"
                style="width: 100%"
              />
            </t-form-item>
            <t-form-item
              v-if="
                selectFormData.headerId &&
                area === 'CN' &&
                selectFormData.headerType !== 1 &&
                selectFormData.invoiceType === 1
              "
              :rules="[
                {
                  required: true,
                  message: t('order.Pleaseenterisokphone'),
                },

                {
                  pattern: /^[0-9]+$/,
                  message: t('order.Pleaseenterisokphone'),
                },
              ]"
              :label="t('order.RegistrationPhoneNumber')"
              name="regPhone"
            >
              <t-input
                v-model="selectFormData.regPhone"
                :maxlength="11"
                :placeholder="t('order.PleaseenterRegistrationPhoneNumber')"
                style="width: 100%"
              />
            </t-form-item>
            <t-form-item
              v-if="
                selectFormData.headerId &&
                area === 'CN' &&
                selectFormData.headerType !== 1 &&
                selectFormData.invoiceType === 1
              "
              :label="t('order.OpeningBank')"
              :rules="[{ required: true, message: t('banch.khyhbt') }]"
              name="bank"
            >
              <t-input
                v-model="selectFormData.bank"
                :maxlength="50"
                :placeholder="t('order.PleaseenterRegistrationPhoneNumberOpeningBank')"
                style="width: 100%"
              />
            </t-form-item>
            <t-form-item
              v-if="
                selectFormData.headerId &&
                area === 'CN' &&
                selectFormData.headerType !== 1 &&
                selectFormData.invoiceType !== 1
              "
              :label="t('order.OpeningBank')"
              name="bank"
            >
              <t-input
                v-model="selectFormData.bank"
                :maxlength="50"
                :placeholder="t('order.PleaseenterRegistrationPhoneNumberOpeningBank')"
                style="width: 100%"
              />
            </t-form-item>
            <t-form-item
              v-if="area === 'CN' && selectFormData.headerType !== 1 && selectFormData.invoiceType === 1"
              :rules="[
                { required: true, message: t('banch.yhzhbt') },
                {
                  pattern: /^[0-9]+$/,
                  message: t('order.pleaseenteranumber'),
                },
                {
                  pattern: /^.{10,30}$/,
                  message: t('order.Pleasenternumbersorletters3'),
                },
              ]"
              :label="t('order.Bankaccount')"
              name="bankAccount"
            >
              <t-input
                v-model="selectFormData.bankAccount"
                :maxlength="30"
                :placeholder="t('order.Pleasebankaccountnumber')"
                style="width: 100%"
              />
            </t-form-item>
            <t-form-item
              v-if="
                area === 'CN' &&
                selectFormData.headerId &&
                selectFormData.headerType !== 1 &&
                selectFormData.invoiceType !== 1
              "
              :label="t('order.Bankaccount')"
              :rules="[
                {
                  pattern: /^[0-9]+$/,
                  message: t('order.pleaseenteranumber'),
                },
                {
                  pattern: /^.{10,30}$/,
                  message: t('order.Pleasenternumbersorletters3'),
                },
              ]"
              name="bankAccount"
            >
              <t-input
                v-model="selectFormData.bankAccount"
                :maxlength="30"
                :placeholder="t('order.Pleasebankaccountnumber')"
                style="width: 100%"
              />
            </t-form-item>
            <t-form-item v-if="selectFormData.headerId" :label="t('order.Emile')" name="mail">
              <t-input v-model="selectFormData.mail" :placeholder="t('order.Pleaseenteranemailaddress')" />
            </t-form-item>
          </t-form>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex-end">
        <div>
          <t-button style="width: 83px" theme="default" variant="outline" @click="resubmit()">取消</t-button>
          <t-button style="width: 83px" @click="submitAddFormData">确认</t-button>
        </div>
      </div>
    </template>
  </t-dialog>
  <!-- 添加发票抬头 -->
  <t-dialog
    v-model:visible="addInvoiceHeader"
    :close-btn="false"
    :header="true"
    attach="body"
    :z-index="5004"
    class="dialogSetForm-noPadding-450px addloading"
    width="464"
    @close="closInvoiceHeader()"
  >
    <template #header>
      <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
        <div>{{ t('order.addinvoiceHeader') }}</div>

        <img
          style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
          src="@assets/<EMAIL>"
          @click="((addInvoiceHeader = false), closInvoiceHeader())"
        />
      </div>
    </template>
    <div style="min-height: 250px">
      <div class="form-box">
        <t-form
          ref="addForm"
          :data="addFormData"
          :rules="FORM_RULES"
          label-align="top"
          :label-width="60"
          layout="verticalt"
          style="padding-top: 8px; width: 100%"
        >
          <t-form-item v-if="area === 'CN'" :label="t('order.InvoiceType')" name="invoiceType">
            <t-radio-group v-model="addFormData.invoiceType" :default-value="0" @change="changeRadio">
              <t-radio :value="0">{{ t('order.ordinaryInvoice') }}</t-radio>
              <t-radio v-if="dataFlag !== '个人'" :value="1">{{ t('order.VATinvoice') }}</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item v-if="area === 'CN' && addFormData.invoiceType !== 1" label="抬头类型" name="headerType">
            <t-radio-group v-model="addFormData.headerType">
              <t-radio v-if="dataFlag === '个人'" :value="1">{{ t('order.geren') }}</t-radio>
              <t-radio :value="2">{{ t('order.unit') }}</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item :label="t('order.invoiceHeader')" name="title">
            <!-- <t-input
              :maxlength="30"
              :placeholder="'请输入' + t('order.invoiceHeader')"
              style="width: 100%"
              v-model="addFormData.title"
            /> -->

            <t-select-input
              v-if="(addFormData.invoiceType === 0 && addFormData.headerType === 2) || addFormData.invoiceType === 1"
              :input-value="addFormData.title"
              :value="addFormData.title"
              :disabled="rowData?.teamId"
              :popup-visible="options.length > 0 && addFormData.title ? addpopupVisible : false"
              :popup-props="{ overlayInnerStyle: { padding: '6px' } }"
              :placeholder="'请输入' + t('order.invoiceHeader')"
              clearable
              class="lk-t-select-input"
              class-name="lk-t-select-input-name"
              allow-input
              @popup-visible-change="onPopupVisibleChange"
              @clear="onClear"
              @input-change="onInputChange"
            >
              <template #panel>
                <ul class="tdesign-demo__select-input-ul-single invul">
                  <li v-for="(item, index) in options" :key="index" @click="() => onOptionClick(item)">
                    {{ item }}
                  </li>
                </ul>
              </template>
              <template #suffixIcon>
                <chevron-down-icon />
              </template>
            </t-select-input>
            <t-input
              v-else
              v-model="addFormData.title"
              :maxlength="30"
              :placeholder="'请输入' + t('order.invoiceHeader')"
              style="width: 100%"
            />
          </t-form-item>
          <t-form-item
            v-if="area === 'CN' && addFormData.headerType === 2"
            :label="t('order.Unittaxnumber')"
            name="taxNumber"
          >
            <t-input
              v-model="addFormData.taxNumber"
              :maxlength="18"
              :disabled="rowData?.teamId"
              :placeholder="'请输入' + t('order.Unittaxnumber')"
              style="width: 100%"
            />
          </t-form-item>
          <t-form-item
            v-if="area === 'CN' && addFormData.headerType !== 1 && addFormData.invoiceType === 1"
            :label="t('order.Registeredaddress')"
            name="regAddress"
            :rules="[{ required: true, message: t('banch.zcdzbt') }]"
          >
            <t-input
              v-model="addFormData.regAddress"
              :maxlength="100"
              :placeholder="'请输入' + t('order.Registeredaddress')"
              style="width: 100%"
            />
          </t-form-item>
          <t-form-item
            v-if="area === 'CN' && addFormData.headerType !== 1 && addFormData.invoiceType !== 1"
            :label="t('order.Registeredaddress')"
            name="regAddress"
          >
            <t-input
              v-model="addFormData.regAddress"
              :maxlength="100"
              :placeholder="'请输入' + t('order.Registeredaddress')"
              style="width: 100%"
            />
          </t-form-item>
          <t-form-item
            v-if="area === 'CN' && addFormData.headerType === 2 && addFormData.invoiceType !== 1"
            :rules="[
              {
                required: false,
                message: t('order.Pleaseenterisokphone'),
              },

              {
                pattern: /^[0-9]+$/,
                message: t('order.Pleaseenterisokphone'),
              },
            ]"
            :label="t('order.RegistrationPhoneNumber')"
            name="regPhone"
          >
            <t-input
              v-model="addFormData.regPhone"
              :maxlength="11"
              :placeholder="t('order.PleaseenterRegistrationPhoneNumber')"
              style="width: 100%"
            />
          </t-form-item>
          <t-form-item
            v-if="area === 'CN' && addFormData.headerType === 2 && addFormData.invoiceType === 1"
            :rules="[
              {
                required: true,
                message: t('order.Pleaseenterisokphone'),
              },
              {
                pattern: /^[0-9]+$/,
                message: t('order.Pleaseenterisokphone'),
              },
            ]"
            :label="t('order.RegistrationPhoneNumber')"
            name="regPhone"
          >
            <t-input
              v-model="addFormData.regPhone"
              :maxlength="11"
              :placeholder="t('order.PleaseenterRegistrationPhoneNumber')"
              style="width: 100%"
            />
          </t-form-item>
          <t-form-item
            v-if="area === 'CN' && addFormData.headerType !== 1 && addFormData.invoiceType === 1"
            :label="t('order.OpeningBank')"
            name="bank"
            :rules="[{ required: true, message: t('banch.khyhbt') }]"
          >
            <t-input
              v-model="addFormData.bank"
              :maxlength="20"
              :placeholder="t('order.PleaseenterRegistrationPhoneNumberOpeningBank')"
              style="width: 100%"
            />
          </t-form-item>
          <t-form-item
            v-if="area === 'CN' && addFormData.headerType === 2 && addFormData.invoiceType !== 1"
            :label="t('order.OpeningBank')"
            name="bank"
          >
            <t-input
              v-model="addFormData.bank"
              :maxlength="50"
              :placeholder="t('order.PleaseenterRegistrationPhoneNumberOpeningBank')"
              style="width: 100%"
            />
          </t-form-item>
          <t-form-item
            v-if="area === 'CN' && addFormData.headerType !== 1 && addFormData.invoiceType === 1"
            :rules="[
              { required: true, message: t('banch.yhzhbt') },
              {
                pattern: /^[0-9]+$/,
                message: t('order.pleaseenteranumber'),
              },
              {
                pattern: /^.{10,30}$/,
                message: t('order.Pleasenternumbersorletters3'),
              },
            ]"
            :label="t('order.Bankaccount')"
            name="bankAccount"
          >
            <t-input
              v-model="addFormData.bankAccount"
              :maxlength="30"
              :placeholder="t('order.Pleasebankaccountnumber')"
              style="width: 100%"
            />
          </t-form-item>

          <t-form-item
            v-if="area === 'CN' && addFormData.headerType === 2 && addFormData.invoiceType !== 1"
            :label="t('order.Bankaccount')"
            name="bankAccount"
            :rules="[
              {
                pattern: /^[0-9]+$/,
                message: t('order.pleaseenteranumber'),
              },
              {
                pattern: /^.{10,30}$/,
                message: t('order.Pleasenternumbersorletters3'),
              },
            ]"
          >
            <t-input
              v-model="addFormData.bankAccount"
              :maxlength="30"
              :placeholder="t('order.Pleasebankaccountnumber')"
              style="width: 100%"
            />
          </t-form-item>
          <t-form-item :label="t('order.Emile')" name="mail">
            <t-input v-model="addFormData.mail" :placeholder="t('order.Pleaseenteranemailaddress')" />
          </t-form-item>
          <t-form-item :label="t('order.morem')">
            <t-switch :value="addFormData.default" :default-value="true" @change="onChangeAdd" />
          </t-form-item>

          <t-form-item v-if="area === 'CN' && addFormData.invoiceType === 1">
            <div style="display: flex; align-items: center">
              <t-checkbox v-model="isCkd">{{ t('order.IreadAgree') }}</t-checkbox>
              <span style="color: #4d5eff; cursor: pointer" @click="invoiceHeader = true">
                《{{ t('order.book') }}》
              </span>
            </div>
          </t-form-item>
          <!-- <t-form-item :label="t('order.morem')">
					<t-switch @change="onChangeAdd" :value="addFormData.default" :default-value="true" />
				</t-form-item> -->
        </t-form>
      </div>
    </div>
    <template #footer>
      <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
        <t-button
          class="minw83"
          theme="default"
          variant="outline"
          @click="((addInvoiceHeader = false), closInvoiceHeader())"
        >
          {{ t('address.cancel') }}
        </t-button>
        <t-button class="minw83" @click="addFormSubmit">提交</t-button>
      </div>
    </template>
  </t-dialog>
  <!-- 增值税专用发票确认书 -->
  <t-dialog
    v-model:visible="invoiceHeader"
    attach="body"
    :close-btn="false"
    :header="true"
    z-index="9999999"
    width="464"
  >
    <template #header>
      <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
        <div>{{ t('order.confirmationVATSpecialInvoice') }}</div>

        <img
          style="width: 16px; cursor: pointer; height: 16px; -webkit-app-region: no-drag"
          src="@assets/<EMAIL>"
          @click="invoiceHeader = false"
        />
      </div>
    </template>
    <div style="color: rgb(19, 22, 27); white-space: pre-line">
      {{ t('order.text') }}
    </div>
    <template #footer>
      <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
        <t-button class="minw83" @click="((invoiceHeader = false), (isCkd = true))">知道了</t-button>
      </div>
    </template>
  </t-dialog>

  <BusinessAssociation
    v-if="businessIndexVisible"
    v-model:visible="businessIndexVisible"
    :team-id="rowData?.teamId"
    :region="area"
    @back="onVertifyBack"
    @success="verifySuccess"
  />
  <BusinessNormal
    v-if="businessVisible"
    v-model:visible="businessVisible"
    :team-id="rowData?.teamId"
    :region="area"
    @back="onVertifyBack"
    @success="verifySuccess"
  />
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  invoiceList,
  groupInvoiceList,
  createexport,
  creatgroupInvoiceList,
  squareTeamSearch,
} from '@/api/myOrder/api/myInvoice';
import { MessagePlugin } from 'tdesign-vue-next';
// import { getTeamAuthAxios } from "@/api/setting/api/settingApi";
// import { useSettingStore } from "@/store";
// import to from 'await-to-js';

const addInvoiceHeader = ref(false);
// import { getTeamCertStatus } from '@renderer/api/square/common';
const isCkd = ref(false);
const { t } = useI18n();
const emits = defineEmits(['update:selectInvoiceFlag', 'setDefaultInvoice']);
const area = ref('CN');
const props = defineProps({
  selectInvoiceFlag: {
    type: Boolean,
    default: false,
  },
  rowData: {
    type: Object,
    default: () => {},
  },
  payInfo: {
    type: Object,
    default: () => {},
  },

  defaultInvoice: {
    type: String,
    default: '',
  },
});
// const certStatus = ref(false);
const invoiceHeader = ref(false);
const dataFlag = ref(props.rowData?.teamId ? '组织' : '个人');
const invoiceListData = ref([]);
const selectFormRef = ref(null);
const selectFormData = ref({});
const addFormData = ref({
  bankAccount: '',
  invoiceType: 0,
  headerType: dataFlag.value === '组织' ? 2 : 1,
  title: '',
  default: true,
  taxNumber: '',
  regAddress: '',
  regPhone: '',
  bank: '',
  mail: '',
});
const FORM_RULES = ref({
  taxNumber: [
    { required: true, message: t('order.Unittaxnumberisrequired') },
    {
      pattern: /^[a-zA-Z0-9]+$/,
      message: t('order.Pleasenternumbersorletters'),
    },
    {
      pattern: /^.{10,20}$/,
      message: t('order.Pleasenternumbersorletters1'),
    },
  ],
  title: [{ required: true, message: t('order.Invoiceheaderisrequired') }],
  regPhone: [
    {
      pattern: /^[0-9]+$/,
      message: t('user.please_input_true_phone'),
    },
  ],
  bankAccount: [
    {
      pattern: /^[0-9]+$/,
      message: t('order.pleaseenteranumber'),
    },
  ],
  mail: [{ email: { ignore_max_length: true }, message: '请输入正确的邮箱地址' }],
});
const changeRadio = () => {
  addFormData.value.headerType = dataFlag.value === '组织' ? 2 : 1;
};
let options = ref([]);
const onClear = () => {
  if (addInvoiceHeader.value) {
    addFormData.value.title = '';
  }
};
let optionsdata = ref([]);
const addpopupVisible = ref(false);
const getTeam = () => {
  squareTeamSearch(addFormData.value?.title || '').then((res) => {
    optionsdata.value = JSON.parse(JSON.stringify(res.data.list));
    options.value = res.data.list.map((e) => e.name);
    if (options.value.length > 0 && addInvoiceHeader.value) {
      addpopupVisible.value = true;
    } else {
      popupVisible.value = true;
    }
  });
};
const popupVisible = ref(false);

const onOptionClick = (item) => {
  const isAddInvoiceHeader = addInvoiceHeader.value;
  const formData = addFormData.value;
  if (isAddInvoiceHeader) {
    addpopupVisible.value = false;
  } else {
    popupVisible.value = false;
  }
  if ((formData.invoiceType === 0 && formData.headerType === 2) || formData.invoiceType === 1) {
    const selectedOption = optionsdata.value.find((e) => e.name === item);
    formData.taxNumber = selectedOption ? selectedOption.code : '';
  }
  formData.title = item;
};
const onPopupVisibleChange = async (val) => {
  if (val) {
    await getTeam();
  } else if (addInvoiceHeader.value) {
    addpopupVisible.value = val;
  } else {
    popupVisible.value = val;
  }
};
const addForm = ref(null);

const onVertifyBack = (isEdit) => {
  console.log(isEdit, 'onVertifyBackonVertifyBack');
};
const verifySuccess = () => {
  console.log('successsuccess');
  // MessagePlugin.success(t('square.square.openTip4'));
};
// verifySuccess
import { getTeamAuth } from '@/api/contacts/api/organize';

const addInvoiceHeaderFn = async () => {
  if (props.rowData?.teamId) {
    getTeamAuth(props.rowData?.teamId).then((res) => {
      console.log(res.data.team_name, '啊实打实打撒');
      console.log(res.data.code, '啊实打实打撒');
      addFormData.value.title = res.data.team_name;
      addFormData.value.taxNumber = res.data.code;
      addInvoiceHeader.value = true;
      dataFlag.value = props.rowData?.teamId ? '组织' : '个人';
      addFormData.value.headerType = dataFlag.value === '组织' ? 2 : 1;
    });
  } else {
    addInvoiceHeader.value = true;
    dataFlag.value = props.rowData?.teamId ? '组织' : '个人';
    addFormData.value.headerType = dataFlag.value === '组织' ? 2 : 1;
  }
  //   const [err, res] = await to(getTeamCertStatus(props.rowData?.teamId));
  //   if (err) return;
  //   certStatus.value = res.data.certStatus;

  //   if (certStatus.value === "CERTIFIED") {
  //     addInvoiceHeader.value = true;
  //   } else {
  //     tipFlag.value = true;
  //   }
  // } else {

  // }
};
const onChangeAdd = (val) => {
  // console.log(val, '巴拉啊啊啊啊啊啊');
  // console.log(tableData.value, '啊实打实大22');
  // const hasDefaultOne = tableData.value.some((obj) => obj.default);

  // if (!hasDefaultOne) {
  // 	addFormData.value.default = true;
  // 	MessagePlugin.error(t('order.zsszygmrtt'));

  // 	return;
  // }
  addFormData.value.default = val;
};
// 添加抬头
// const addFormSub = () => {
// 	addForm.value.validate().then((res) => {
// 		if (res === true) {
// 			// 正确参数
// 			if (area.value === 'MO') {
// 				createexport({
// 					title: addFormData.value.title,
// 					mail: addFormData.value.mail,
// 					headerType: 1,
// 					region: area.value,
// 					invoiceType: 0,
// 					default: addFormData.value.default,
// 				}).then(async (res) => {
// 					if (res.data.code === 0) {
// 						await getList(res.data.id);
// 						if (!props.selectInvoiceFlag) {
// 							submitAddFormData();
// 						}
// 						MessagePlugin.success('操作成功');
// 						addInvoiceHeader.value = false;
// 						closInvoiceHeader();
// 					} else {
// 						MessagePlugin.error(res.data.message);
// 					}
// 				});
// 			} else {
// 				if (addFormData.value.invoiceType === 1) {
// 					invoiceHeader.value = true;
// 					return;
// 				}
// 				const objs = JSON.parse(JSON.stringify(addFormData.value));
// 				objs.headerType = addFormData.value.invoiceType === 1 ? 0 : addFormData.value.headerType;

// 				if (objs.invoiceType === 1 && objs.headerType === 1) {
// 					objs.taxNumber = '';
// 				}
// 				objs.region = area.value;
// 				createexport({
// 					...objs,
// 				}).then(async(res) => {
// 					if (res.data.code === 0) {
// 						await getList(res.data.id);
// 						if (!props.selectInvoiceFlag) {
// 							submitAddFormData();
// 						}
// 						MessagePlugin.success('操作成功');
// 						addInvoiceHeader.value = false;

// 						closInvoiceHeader();
// 					} else {
// 						MessagePlugin.error(res.data.message);
// 					}
// 				});
// 			}
// 		}
// 	});
// };
const addFormSubmit = async () => {
  try {
    const isValid = await addForm.value.validate();
    if (isValid !== true) return;
    let formData;
    if (area.value === 'MO') {
      formData = {
        title: addFormData.value.title,
        mail: addFormData.value.mail,
        headerType: 1,
        region: area.value,
        invoiceType: 0,
        default: addFormData.value.default,
      };
    } else {
      if (isCkd.value === false && addFormData.value.invoiceType === 1) {
        invoiceHeader.value = true;
        return;
      }
      formData = { ...addFormData.value };
      // 根据invoiceType调整headerType并清空taxNumber（如果适用）
      formData.headerType = formData.invoiceType === 1 ? 0 : formData.headerType;
      if (formData.invoiceType === 1 && formData.headerType === 1) {
        formData.taxNumber = '';
      }
      formData.region = area.value;
    }

    let creatUrl = props.rowData?.teamId ? creatgroupInvoiceList : createexport;
    const response = await creatUrl(formData, props.rowData?.teamId || null);
    if (response.data.code === 0) {
      await getList(response.data.id);
      if (!props.selectInvoiceFlag) {
        submitAddFormData();
      }
      MessagePlugin.success('操作成功');
      addInvoiceHeader.value = false;
      closInvoiceHeader();
    } else {
      MessagePlugin.error(response.data.message);
    }
  } catch (error) {
    console.error('添加抬头时发生错误:', error);
  }
};
const getList = async (val) => {
  return new Promise((resolve, reject) => {
    console.log(props.rowData, 'props.rowDataprops.rowDataprops.rowData');
    try {
      const queryParams = { page: 1, pageSize: 99999, teamId: props.rowData?.teamId };
      const getInvoiceList = queryParams.teamId ? groupInvoiceList : invoiceList;
      getInvoiceList(queryParams)
        .then((res) => {
          invoiceListData.value = res.data.list;
          if (val) {
            const defaultElement = invoiceListData.value.find((element) => element.id === val);
            if (defaultElement) {
              selectFormData.value = { ...defaultElement, headerId: defaultElement.id };
            }
            resolve('');
            return;
          }
          if (props.defaultInvoice) {
            selectFormData.value = { ...props.defaultInvoice, headerId: props.defaultInvoice.headerId };
          } else {
            const defaultElement = invoiceListData.value.find((element) => element.default);
            if (defaultElement) {
              selectFormData.value = { ...defaultElement, headerId: defaultElement.id };
            }
          }
          resolve('');
        })
        .catch((error) => {
          reject();
          console.error(error);
        });
    } catch (error) {
      reject();
      console.error(error);
    }
  });
};

const onInputChange = async (val) => {
  if (addInvoiceHeader.value) {
    addFormData.value.title = val;
  }
  await getTeam();
  console.log(optionsdata.value, 'optionsdataoptionsdataoptionsdata');
  const objs = JSON.parse(JSON.stringify(optionsdata.value.map((e) => e.name)));
  options.value = objs.filter((e) => e.includes(val));
};
const closInvoiceHeader = () => {
  addForm.value.reset({ type: 'initial' });

  addFormData.value = {
    bankAccount: '',
    invoiceType: 0,
    headerType: dataFlag.value === '组织' ? 2 : 1,
    title: '',
    default: true,
    taxNumber: '',
    regAddress: '',
    regPhone: '',
    bank: '',
    mail: '',
  };
  addForm.value.clearValidate();
};
const submitAddFormData = async () => {
  const isValid = await selectFormRef.value.validate();
  console.log(isValid, 'isValidisValidisValidisValid');
  if (isValid !== true) return;
  emits('setDefaultInvoice', selectFormData.value);
  emits('update:selectInvoiceFlag', false);
};
// const groupItem = computed(() => useSettingStore().groupItem);
watch(
  () => props.selectInvoiceFlag,
  (newvalue) => {
    if (newvalue) {
      getList();
      const region = props.rowData ? props.rowData?.region : props.payInfo?.region;
      console.log(props, '阿萨大大是的');
      if (region) {
        area.value = region;
        console.log(area.value, 'area.valuearea.value');
        return;
      }
    }
  },
);
// watch(
// 	() => addInvoiceHeader.value,
// 	(newvalue) => {
// 		if (newvalue) {
// 			// 切换地区
// 			area.value = groupItem.value.teamRegion;
// 			// area.value = 'CN'

// 			if (groupItem.value.teamAuth === 1 || groupItem.value.teamAuth === 3) {
// 				getcode();
// 			}
// 		}
// 	},
// );

// const getcode = () => {
// 	getTeamAuthAxios().then((res) => {
// 		console.log(res, '啊实打实大师大多');
// 		if (res.status === 200) {
// 			addFormData.value.title = res.data.team_name;
// 			if (area.value === 'CN') {
// 				addFormData.value.taxNumber = res.data.code;
// 			}
// 		}
// 	});
// };
const openAddInvoiceHeader = () => {
  addInvoiceHeader.value = true;
  dataFlag.value = props.rowData?.teamId ? '组织' : '个人';
  addFormData.value.headerType = dataFlag.value === '组织' ? 2 : 1;
};
const onChange = (val) => {
  const element = invoiceListData.value.find((e) => e.id === val);
  const { id, title, invoiceType, headerType, regAddress, taxNumber, regPhone, bank, bankAccount, mail } = element;
  selectFormData.value = {
    ...selectFormData.value,
    headerId: id,
    invoiceType,
    title,
    headerType,
    regAddress,
    taxNumber,
    regPhone,
    bank,
    bankAccount,
    mail,
  };
  console.log(selectFormData.value, 'selectFormData.valueselectFormData.value');
};
const resubmit = () => {
  selectFormRef.value.reset({ type: 'initial' });
  emits('update:selectInvoiceFlag', false);
};
defineExpose({
  openAddInvoiceHeader,
});
</script>
<style lang="less" scoped>
.flexboxs {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}
.item-box {
  display: flex;
  align-items: center;
  /* margin-bottom: 24px; */
}
.label-box {
  width: 244px;
  margin-bottom: 24px;
}
.flexboxs {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}
.invul {
  padding: 8px;
  max-height: 200px;
  overflow: auto;
  li {
    cursor: pointer;
    line-height: 32px;
    height: 32px;
    padding: 0 8px;
    width: 100%;
    border-radius: 4px !important;
  }
  li:hover {
    background-color: var(--select-kyy-color-select-multiple-option-item-bg-active, #e1eaff) !important;
    color: var(--select-kyy-color-select-multiple-option-item-text, #1a2139) !important;
  }
}
.content-box-tip {
  color: var(--text-kyy_color_text_2, #516082);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  display: flex;
  align-items: center;
  flex-direction: column;
  img {
    width: 48px;
    height: 48px;
    margin-bottom: 24px;
  }
}
.label-item {
  color: var(--text-kyy_color_text_3, #828da5);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  width: 84px;
  height: 22px;
}
.value-item {
  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 14px;
  font-style: normal;
  margin-top: 4px;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
:deep(.t-popup) {
  z-index: 99999999999999;
}
.t-popup {
  z-index: 99999999999999;
}
</style>
<style lang="less">
.invul::-webkit-scrollbar {
  width: 4px;
  background-color: #f5f5f5;
}
.invul::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}
.dialogSetForm-noPadding-450px {
  .t-dialog__body {
    overflow: overlay;
    max-height: 450px;
  }
}
:deep(.addloading) {
  .t-popup {
    z-index: 99999999999999;
  }
}
</style>
