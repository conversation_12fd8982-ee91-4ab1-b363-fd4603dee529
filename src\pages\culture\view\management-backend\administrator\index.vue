<template>
  <div class="box-flex">
    <div class="right-box">
      <div class="right-box-head">
        <span class="addzj">激活另可文旅应用的人为超级管理员，添加的管理员具有进入另可文旅的权限。</span>
        <div class="flex-end">
          <t-button
            v-if="myRole === 'RoleSuperAdmin'"
            variant="outline"
            style="margin-right: 8px; font-weight: bold"
            @click="openPushSetting"
          >
            <template #icon>
              <iconpark-icon name="iconsetUp" class="add-icon mr-4px setup-icon"></iconpark-icon>
            </template>
            {{ t('banch.newsSetting') }}
          </t-button>
          <t-button style="font-weight: bold" @click="addAdminMember">
            <template #icon><t-icon name="add" class="add-icon" /></template>
            {{ t('banch.tjgly') }}
          </t-button>
        </div>
      </div>
      <div class="right-box-content">
        <t-config-provider>
          <t-table
            row-key="index"
            :columns="columns"
            :data="adminList"
            style="position: relative; overflow: auto; height: 100%"
          >
            <template #name="{ row }">
              <div class="ellipsis-text" style="display: flex; align-items: center">
                <kyyAvatar
                  style="padding: 0 4px"
                  :round-radius="true"
                  :image-url="row.avatar"
                  avatar-size="32px"
                  :user-name="row.name"
                />
                <div class="ellipsis-text" style="padding-left: 12px">{{ row.name }}</div>
                <div v-if="row?.removed" class="lzbox" style="margin-left: 4px">{{ t('banch.ylz') }}</div>
              </div>
            </template>
            <template #type="{ row }">
              <div style="display: flex; align-items: center">
                {{ RoleMap[row.role] }}
              </div>
            </template>

            <template #operate="{ row }">
              <!-- v-if="isAdmin(row)" -->
              <div v-if="['RoleOrganizeAdmin', 'RoleSuperAdmin'].includes(myRole)" class="btn" @click="chengAdmin(row)">
                <span>
                  {{
                    row.role === 'RoleAdmin'
                      ? '移除'
                      : row.role === 'RoleSuperAdmin'
                        ? t('clouddisk.transferSuperAdministrator')
                        : ''
                  }}
                </span>
              </div>
            </template>
            <template #empty>
              <div>
                <REmpty :tip="t('banch.zwsj')" name="no-data" />
              </div>
            </template>
          </t-table>
        </t-config-provider>
      </div>
    </div>
    <!-- 转移弹窗 -->
    <transfer-dialog
      ref="transferDialogRef"
      :list-data="listData"
      @choose-transfer="chooseTransfer"
      @confirm-transter-admin="confirmTransterAdmin"
    />
    <select-personnel
      ref="selectPersonnelsRef"
      :max-num="isAddAdmin ? 5000 : 1"
      :disabled-array="disabledArray"
      :header="isAddAdmin ? '添加管理员' : '转移超级管理员'"
      :options="addAdminData"
      :tipes="'200'"
      @sub-form="onSelectItems"
    />
    <pushSetting ref="pushSettingRef" :my-info="myInfo" />
  </div>
</template>
<script setup lang="jsx">
import { ref, onMounted } from 'vue';
import {
  adminTourism,
  addAdmin,
  delAdmin,
  changeAdmin,
  getMyRole,
  getAdminAppUser,
} from '@pages/culture/api/management';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { REmpty } from '@rk/unitPark';
import KyyAvatar from '@components/kyy-avatar/index.vue';
import selectPersonnel from '@pages/shop/components/select-personnel/index.vue';
import transferDialog from '@pages/shop/components/transferDialog.vue';
import pushSetting from './pushSetting.vue';

const { t } = useI18n();
// 路由相关
const route = useRoute();
// http://localhost:8080/culture/index.html#/management-backend?teamId=605352471480246272&cardId=$1937&openId=2aiv40001dx1szyk92&token=
const teamId = route.query.teamId;
const openId = route.query.openId;
const cardId = route.query.cardId;
const myInfo = ref({
  openId,
  cardId,
  teamId,
  superAdmin: '',
});
const columns = ref([
  { colKey: 'name', title: '姓名', ellipsis: true, width: 400 },
  { colKey: 'type', title: '类型' },
  { colKey: 'operate', title: '操作' },
]);
const RoleMap = {
  RoleSuperAdmin: '超级管理员',
  RoleAdmin: '管理员',
  RoleDefault: '普通用户',
};
const myRole = ref('');
const adminList = ref([]);
let listData = ref(null);
const selectPersonnelsRef = ref(null);
const disabledArray = ref([]);
const addAdminData = ref([]);
const isAddAdmin = ref(true);
onMounted(() => {
  if (teamId) {
    getMyRoleData();
    getList();
  }
});

const pushSettingRef = ref(null);
const openPushSetting = () => {
  pushSettingRef.value.openWindow();
};
const addAdminMember = async () => {
  disabledArray.value = adminList.value.map((item) => item.cardId || item.openId);
  isAddAdmin.value = true;
  getSelectUser();
};
const getSelectUser = () => {
  const data = { 'me.openId': openId, 'me.cardId': cardId, 'me.teamId': teamId };
  getAdminAppUser(data)
    .then((res) => {
      console.log(res, 'resssssssssss', disabledArray);
      if (res.data?.users?.length === 0) {
        return;
      }
      for (let index = 0; index < res.data.users.length; index++) {
        const element = res.data.users[index];
        element.staffId = element.cardId || element.openId;
        !element.teamId && (element.teamId = teamId);
      }
      addAdminData.value = res.data.users;
      selectPersonnelsRef.value.openWindow();
    })
    .catch((error) => {
      console.log(error, 'errorerrorerror');
      // MessagePlugin.error(error.response.data.message);
    });
};
// 选人回调
const onSelectItems = async (val) => {
  console.log(val, 'valvalvalval');
  if (val.length === 0) {
    return;
  }
  if (isAddAdmin.value) {
    // const choseList = getUserObj(val);
    const data = { me: { openId, cardId, teamId }, admins: val };
    try {
      const res = await addAdmin(data, teamId);
      if (res.code === 0) {
        MessagePlugin.success('操作成功');
        getList();
      } else {
        MessagePlugin.error(res.message);
      }
    } catch (err) {
      MessagePlugin.error(err.message);
      getList();
    }
  } else {
    listData.value = val[0];
  }
};
const getMyRoleData = () => {
  const data = { 'me.openId': openId, 'me.cardId': cardId, 'me.teamId': teamId };
  getMyRole(data, teamId).then((res) => {
    myRole.value = res.data.role;
  });
};
const getList = async () => {
  try {
    const data = { 'me.openId': openId, 'me.cardId': cardId, 'me.teamId': teamId };
    const res = await adminTourism(data, teamId);
    if (res.code === 0) {
      adminList.value = res.data.admins;
      myInfo.value.superAdmin = res.data.admins.filter((item) => item.role === 'RoleSuperAdmin')[0]?.cardId;
    } else {
      adminList.value = [];
      MessagePlugin.error(res.message);
    }
  } catch (err) {
    console.log(err, '呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃');
    adminList.value = [];
    MessagePlugin.error(err.response.data.message);
  }
};
const transferDialogRef = ref(null);
const rowData = ref(null);
const chengAdmin = (row) => {
  rowData.value = row;
  if (row.role === 'RoleSuperAdmin') {
    listData.value = null;
    transferDialogRef.value.openWindow();
  } else {
    const confirmDia = DialogPlugin({
      header: '删除',
      theme: 'info',
      class: 'delmode',
      body: '是否确定删除该管理员？',
      closeBtn: null,
      confirmBtn: '确定',
      cancelBtn: '取消',
      onClose: () => {
        confirmDia.hide();
      },
      onConfirm: async () => {
        const data = { me: { openId, cardId, teamId }, deleteCardId: row.cardId };
        const res = await delAdmin(data, teamId);
        if (res.code === 0) {
          MessagePlugin.success('操作成功');
          getList();
        } else {
          MessagePlugin.error(res.message);
        }
        confirmDia.hide();
      },
    });
  }
};
const chooseTransfer = async () => {
  console.log('disabledTransferArray.value', rowData.value);
  disabledArray.value = [rowData.value.cardId || rowData.value.openId];
  isAddAdmin.value = false;
  getSelectUser();
};
// 转移管理员接口
const confirmTransterAdmin = async (removeOrig) => {
  try {
    const data = {
      me: { openId: rowData.value.openId, cardId: rowData.value.cardId, teamId: rowData.value.teamId },
      newSuperAdmin: listData.value,
      removeOrig,
    };
    const res = await changeAdmin(data, teamId);
    if (res.code === 0) {
      MessagePlugin.success('操作成功');
      getList();
      getMyRoleData();
      listData.value = null;
      transferDialogRef.value.close();
    } else {
      MessagePlugin.error(res.message);
    }
  } catch (err) {
    MessagePlugin.error(err.message);
    getList();
  }
};
</script>

<style lang="less" scoped>
.right-box-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 16px;
  span {
    color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    padding: 4px;
    cursor: pointer;
  }
  span:hover {
    color: #707eff;
    border-radius: 4px;
    background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
  }
}

.lzbox {
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_gray, #eceff5);
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 4px;
  color: var(--kyy_color_tag_text_gray, #516082);
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  justify-content: center;
  align-items: center;
  gap: 4px;
}
.add-icon {
  width: 16px;
  font-size: 16px;
  height: 16px;
}
.setup-icon {
  width: 18px;
  height: 18px;
  font-size: 18px;
}
.addzj {
  color: var(--text-kyy_color_text_2, #516082);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.right-box {
  padding: 16px 16px 0;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.right-box-content {
  flex: 1;
  overflow: hidden;
}
.box-flex {
  display: flex;
  flex: 1;
  width: 100%;
  background: #fff;
  height: 100%;
}
</style>
