<template>
  <div class="widget-wrap">
    <template v-if="!data.hiddenTitle && data.title">
      <div class="flex-center h-[46px]">— {{ data.title }} —</div>
      <t-divider class="my-0" />
    </template>

    <template v-if="variant === 'left-large'">
      <template v-for="(row, rowIdx) in chunkedList" :key="rowIdx">
<!--        <div v-if="rowIdx !== 0" class="px-[16px]">-->
<!--          <t-divider class="my-0" />-->
<!--        </div>-->

        <div class="flex justify-between px-[16px]">
          <div
            v-for="(item, index) in row"
            :key="index"
            :class="[
              'article-item px-0!',
              variant,
              index === 0 ? 'w-[196px]' : 'w-[131px]',
            ]"
            @click="goArticle(item)"
          >
            <t-image
              :src="item.img"
              fit="cover"
              class="img mb-[12px] h-[80px] b-rd-[4px]"
            >
              <template #error>
                <t-icon name="image-error" class="text-25!" />
              </template>
            </t-image>

            <div class="content">
              <div class="mb-[3px] font-bold">
                <img
                  v-if="item.top"
                  src="../assets/fire-fill.svg"
                  alt=""
                  class="h-[16px] w-[16px] v-middle"
                />
                {{ item.title }}
              </div>
              <div class="line-2">{{ item.content }}</div>
            </div>
          </div>
        </div>
      </template>
    </template>

    <template v-else>
      <template v-for="(item, index) in list" :key="index">
        <div v-if="index !== 0" class="px-[16px]">
          <t-divider class="my-0" />
        </div>

        <div :class="['article-item', variant]" @click="goArticle(item)">
          <t-image
            v-if="variant !== 'only-content'"
            :src="item.img"
            fit="cover"
            class="img mb-[12px] h-[80px] b-rd-[4px]"
          >
            <template #error>
              <t-icon name="image-error" class="text-25!" />
            </template>
          </t-image>

          <div class="content">
            <div class="mb-[3px] font-bold">
              <img
                v-if="item.top"
                src="../assets/fire-fill.svg"
                alt=""
                class="h-[16px] w-[16px] v-middle"
              />
              {{ item.title }}
            </div>
            <div class="line-2">{{ item.content }}</div>
          </div>
        </div>
      </template>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import chunk from "lodash/chunk";
import type {
  ArticleComponent,
} from "@/views/square/website/types";
import { toRefs } from "vue";

const props = defineProps<{
  data: ArticleComponent["formModel"];
}>();

// eslint-disable-next-line vue/no-setup-props-destructure
const { variant } = toRefs(props.data);
const list = computed(() => props.data.list.map(v => ({
  ...v,
  img: v.img.replace(/http:\/\//g, 'https://'),
})));

// const length = computed(() => list?.value?.length ?? 0);
const chunkedList = computed(() => chunk(list.value || [], 2));

const goArticle = (item: { id: string }) => {
  window.appSquare?.postMessage(
    JSON.stringify({
      type: "articleSquare",
      id: item.id,
    })
  );
};
</script>

<style lang="less" scoped>
.widget-wrap {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.article-item {
  padding: 8px 16px;
  transition: all 0.6s linear;

  &.left,
  &.right {
    display: flex;
    .img {
      width: 120px;
      flex-shrink: 0;
      margin-right: 12px;
      margin-bottom: 0;
    }
    .content {
      flex: 1;
    }
  }

  &.right {
    flex-direction: row-reverse;
  }

  &.mask {
    position: relative;
    .img {
      height: 120px;
      margin-bottom: 0;
    }
    .content {
      position: absolute;
      width: 100%;
      top: 0;
      left: 0;
      z-index: 1;
      padding: 32px;
      color: #fff;
      text-align: center;
    }
  }
}
</style>
