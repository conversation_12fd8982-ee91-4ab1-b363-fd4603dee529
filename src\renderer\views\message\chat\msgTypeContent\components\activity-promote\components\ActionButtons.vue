<template>
  <div class="bottom-buttons">
    <t-button
      v-for="button in buttons"
      :key="button.key"
      :variant="button.variant"
      :theme="button.theme"
      :disabled="button.disabled"
      class="btn"
      @click="handleClick(button.key)"
    >
      {{ button.text }}
    </t-button>
  </div>
</template>

<script setup lang="ts">
interface ButtonConfig {
  key: string
  text: string
  variant: 'outline' | 'base' | 'dashed' | 'text'
  disabled?: boolean
  theme: 'default' | 'primary' | 'success' | 'warning' | 'danger'
}

interface Props {
  buttons: ButtonConfig[]
}

defineProps<Props>();

const emit = defineEmits(['buttonClick']);

const handleClick = (key: string) => {
  emit('buttonClick', key);
};
</script>

<style scoped>
.bottom-buttons {
  display: flex;
  gap: 8px;
  padding: 16px;
}

.btn {
  flex: 1;
  height: 32px;
  font-weight: 600;
}
</style>
