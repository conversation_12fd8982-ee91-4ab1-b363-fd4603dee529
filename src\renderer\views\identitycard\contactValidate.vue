<template>
  <div v-if="loading" class="load">
    <t-loading />
  </div>
  <div v-if="!loading">
    <div class="main-card">
      <div id="main-content" ref="mainContent">
        <div class="top-bg-box">
        </div>
        <div :class="['w-header', noDrag ? 'no-drag' : '']">
          <t-space style="align-items: center;gap: 12px;">
            <div class="box-avatar">
              <kyy-avatar
                avatar-size="44px"
                :image-url="cardInfo.avatar"
                :user-name="cardInfo.name"
                roundRadius
              />
            </div>
            <div class="w-1">
              <div class="nicknameBox">
                <div class="nickname">{{ cardInfo.name }}</div>

                <img
                  v-if="genderValue.value === 1"
                  src="@renderer/assets/identity/icon_man.png"
                  alt=""
                />
                <img
                  v-else-if="genderValue.value === 2"
                  src="@renderer/assets/identity/icon_woman.png"
                  alt=""
                />
              </div>
            </div>
          </t-space>
        </div>
        <div :class="['content', isNotMac ? 'scrollbar' : '']">
          <div class="info-box">
            <div class="w-info">
              <div class="box-info">
                <div class="t-item flex">
                  <span class="t-title">{{ t("identity.source") }}</span>
                  <span class="t-text ellipsis">{{ t("identity.pass")
                  }}{{ sourceMap.get(+applyInfo?.source || 2) }}</span>
                </div>
                <div class="flex t-item">
                  <span class="t-title">{{ t("identity.notesInfo") }}</span>
                  <span class="t-text">{{ applyInfo?.remark || '- -' }}</span>
                </div>
                <!-- <div class="flex t-item">
                  <span class="t-title">{{ t("identity.introduce") }}</span>
                  <span class="t-text">{{ applyInfo?.introduce || '- -' }}</span>
                </div> -->
                <div class="flex t-item" style="flex-direction: column;" v-if="applyInfo?.businessCard?.length">
                  <div class="t-title" style="align-self: flex-start;margin-bottom: 8px;">名片</div>
                  <t-image @click="imagePreview(applyInfo.businessCard, index)" fit="cover" style="cursor: pointer;" v-for="(item, index) in applyInfo.businessCard" :key="item.id" :src="item" class="w-312 h-176 mb-4 border-rd-8">
                    <template #error><t-icon name="image-error" class="text-25!" /></template>
                    <template #loading><t-loading size="small" /></template>
                  </t-image>
                </div>
                <div class="flex t-item" v-else>
                  <span class="t-title">名片</span>
                  <span class="t-text">{{ '- -' }}</span>
                </div>



                <!-- <div class="flex t-item">
                  <div class="t-title" style="align-self: center;">{{ t('identity.businessCard') }}</div>
                  <div class="flex" style="flex-grow: 1;align-items: center;">
                    <div class="flex flex-1" style="flex-wrap: wrap;max-height: 56px;overflow:hidden;gap:8px">
                      <t-image @click="imagePreview(applyInfo.businessCard, index)" fit="cover" style="cursor: pointer;" v-for="(item, index) in applyInfo.businessCard" :key="item.id" :src="item" class="w-40 h-40 mr-4 border-rd-8">
                        <template #error><t-icon name="image-error" class="text-25!" /></template>
                        <template #loading><t-loading size="small" /></template>
                      </t-image>
                    </div>
                    <div v-if="applyInfo.businessCard?.length" style="flex-shrink: 0;cursor: pointer;" @click="imagePreview(applyInfo.businessCard, 0)">
                      <t-icon name="chevron-right" size="18" class="color-text-3" />
                    </div>
                  </div>

                </div> -->
              </div>
            </div>

          </div>
        </div>

        <div class="w-footer flex">
          <div class="flex flex-1" v-if="applyInfo.status === 0">
            <t-button :class="['flex-1']" theme="primary" @click.stop="agreeApply(applyId, true)">
              {{ t("identity.agree") }}
            </t-button>
          </div>
          <div class="flex flex-1" v-else>
            <t-button :class="['flex-1']" theme="primary" disabled>
              {{ statusMap.get(+applyInfo.status || 1) }}
            </t-button>
          </div>
        </div>


        <apply-dialog
          v-model:visible="applyVisible"
          :card-info="cardInfo"
          :my-id="myId"
          @onconfirm="agreeApply"
          @onApplyContact="onApplyContact"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { watch, ref, computed } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { isNotMac } from "../zhixing/util";
import {
  getApplyDetail,
} from "../../api/identity/api/card";
import { acceptFriend } from '@renderer/api/im/api';
import {
  cardData,
} from "@renderer/views/identitycard/data";
import applyDialog from "./dialog/applyContact.vue";

import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();

const route = useRoute();
const router = useRouter();

const cardId = ref("");
const myId = ref("");
const applyId = ref("");

const cardInfo: any = ref({});
const applyInfo: any = ref({});
const applyVisible = ref(false);
const loading = ref(true);

const sourceMap = new Map([
  [1, t("identity.withinGroup")],
  [2, t("identity.mobileSearch")],
  [3, t("identity.recommend")],
  [4, t("identity.consult")],
  [5, t("identity.scanCode")],
  [6, t("identity.shareCard")]
]);

const statusMap = new Map([
  [0, t("im.msg.agree")],
  [1, t("im.msg.agreed")],
  [2, t("im.msg.ignored")],
  [3, t("im.msg.expired")],
  [4, t("im.msg.agreed")]
]);

// 图片预览
const imagePreview = (url: string | string[], index: number = 0) => {
  const mapper = (v: string) => ({
    title: v?.split('/')?.pop(),
    url: v,
    type: 'image',
    imgIndex: index
  });
  const data = Array.isArray(url) ? url.map(mapper) : mapper(url);
  console.log(data);

  ipcRenderer.invoke('preview-file', JSON.stringify(data));
};

// 刷新添加状态,刷新弹框列表
const onApplyContact = () => {
  ipcRenderer.invoke("change-add-contacts");
};

const genderValue = computed(() => {
  let val = cardInfo.value?.options?.find((item) => item.type === "gender");
  return val ? {
    ...val,
    value: Number(val?.value),
  } : {
    value: 0,
  };
});


const agreeApply = async (id, hide = false) => {
  acceptFriend({id})
    .then(() => {
      hide && ipcRenderer.invoke("hide-identWin");
      ipcRenderer.invoke("update-contact", { page: 'newContacts', applyId: applyId.value });
    })
    .catch((err) => {
      MessagePlugin.error(
        err.response.data?.message || t("identity.addContactFailed")
      );
    });
};

const initData = async () => {
  applyVisible.value = false;
  cardInfo.value = await cardData(cardId.value);
  const res = await getApplyDetail({ applyId: applyId.value });
  if (res?.status === 200) {
    applyInfo.value = res?.data?.data;
  }
  const windSize = { width: 352, height: 560 };
  !route.query.fromIdentityPage && ipcRenderer.invoke("set-identWin-pos", windSize);
  ipcRenderer.invoke("set-window-sise", {
      window: "identWin",
      width: 352,
      height: 500,
      smooth: false
    });
  loading.value = false;
};

ipcRenderer.on("load-card", (e, val) => {
  loading.value = true;
});

ipcRenderer.removeAllListeners('show-card');
ipcRenderer.on("show-card", (e, val) => {
  loading.value = true;
  cardId.value = decodeURIComponent(val.cardId);
  myId.value = decodeURIComponent(val.myId);
  applyId.value = decodeURIComponent(val.applyId);
  if (val.route_path) {
    initData();
  } else {
    router.replace({
      path: `/identitycard/view/${encodeURIComponent(cardId.value)}/${encodeURIComponent(myId.value)}`,
      query: {
        showMoreCard: val.showMoreCard,
      }
    });
  }

});

watch(
  () => route.path,
  async (newValue, oldValue) => {
    if (~route.path.indexOf("identitycard/contactValidate")) {
      loading.value = true;
      cardId.value = decodeURIComponent(route.params.cardId as string);
      myId.value = decodeURIComponent(route.params.myId as string);
      applyId.value = decodeURIComponent(route.query.applyId as string);
      initData();
    }
  },
  { immediate: true }
);

const noDrag = ref(false);
</script>

<style lang="less" scoped>
@import "../../style/mixins.less";

::-webkit-scrollbar {
  width: 0 !important;
}
.top-bg-box{
  position: absolute;
  top:0;
  width: 100%;
  height: 243px;
  background: url('../../assets/identity/topBg_v2.png')  no-repeat;
  background-size: cover;
  .top-bg2{
    background: url('../../assets/identity/topBg2.png')  no-repeat;
    background-size: cover;
    height: 80px;
    width: 100%;
    position: absolute;
    bottom:0;
  }
}
.no-drag{
  -webkit-app-region: no-drag !important;
}

.load {
  height: 100vh;
  width: 100%;
  background-color: #f1f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mt8 {
  margin-top: 8px;
}
.main-card {
  width: 352px;
  height: 100vh;
  position: relative;
  overflow: hidden;
  z-index: 1;
  padding-bottom: 12px;
  background: var(--cyan-kyy_color_cyan_hover, #3CC9C0);
  #main-content{
    height: 100%;
    overflow-y:scroll;
  }
  .w-header {
    padding: 24px 12px 8px 12px;
    -webkit-app-region: drag;
    position: relative;
    .box-avatar {
      width: 44px;
      height: 44px;
      position: relative;
      cursor: pointer;
    }

    .w-1 {
      width: 100%;
      display: flex;
      flex-direction: column;

      .nicknameBox {
        display: flex;
        align-items: center;
        img {
          margin-left: 4px;
          width: 16px;
          height: 16px;
        }
      }

      .nickname {
        max-width: 140px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-kyy-color-text-white, #fff);
        line-height: 24px;
        cursor: text;
        -webkit-app-region: no-drag;
      }


    }
  }
  .info-box{
    width: 100%;
    padding:8px 4px;
    border-radius: 8px;
    margin-top: 8px;
    background-color: #fff;
    .t-item{
      padding:0 8px;
      line-height: 28px;
    }
  }

  .box-info{
    overflow-y: scroll;
    max-height: calc(100vh - 150px);
  }
  .content {
    width: 336px;
    margin: auto;
    overflow-x: hidden;
    position: relative;
    z-index: 1;
  }

  .w-info {
    -webkit-app-region: no-drag;
  }

  .w-footer {
    width: 100%;
    padding: 8px 20px;
    position: absolute;
    bottom: 0;
    z-index: 9;
    background-color: #fff;
  }
}

.flex {
  display: flex;
}

.flex-1 {
  flex-grow: 1;
}


.flex-column {
  display: flex;
  flex-direction: column;
}

.t-item {
  & + .t-item {
    margin-top: 4px;
  }

  .t-title {
    display: inline-block;
    width: 80px;
    white-space: nowrap;
    flex-shrink: 0;

    color: var(--text-kyy-color-text-3, #828da5);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }

  .t-text {
    display: inline-block;
    max-width: 176px;

    color: var(--text-kyy-color-text-1, #1a2139);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    flex-grow: 1;
  }

  .t-arrow {
    width: 16px;
    height: 16px;
    justify-self: flex-end;
    cursor: pointer;
  }
}

</style>
