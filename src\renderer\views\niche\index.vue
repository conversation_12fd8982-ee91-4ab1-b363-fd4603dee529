<template>
  <div v-if="allPermissions.allow_niche" class="bus">
    <leftMenu @refresh="renewalSuccess" :all-permissions="allPermissions" @gu="guideHandle" />
    <div class="right-box">
      <div class="right-content">
        <div class="data-box-header">
          <div class="f-btns">
            <t-button
              class="f-btn"
              :theme="params.operation_state === 0 ? 'primary' : 'default'"
              @click="setOperationState(0)"
            >
              {{ t('niche.all') }}
            </t-button>
            <t-button :theme="params.operation_state === 4 ? 'primary' : 'default'" @click="setOperationState(4)">
              {{ t('niche.status_in_progress') }}
            </t-button>
            <t-button :theme="params.operation_state === 2 ? 'primary' : 'default'" @click="setOperationState(2)">
              {{ t('niche.dsx') }}
            </t-button>
            <t-button :theme="params.operation_state === 5 ? 'primary' : 'default'" @click="setOperationState(5)">
              {{ t('niche.status_off_shelf') }}
            </t-button>
            <t-button :theme="params.operation_state === 1 ? 'primary' : 'default'" @click="setOperationState(1)">
              {{ t('niche.wfb') }}
            </t-button>
            <t-button :theme="params.operation_state === 3 ? 'primary' : 'default'" @click="setOperationState(3)">
              {{ t('niche.status_expired') }}
            </t-button>
            <t-button :theme="params.operation_state === 6 ? 'primary' : 'default'" @click="setOperationState(6)">
              {{ t('niche.status_deleted') }}
            </t-button>
          </div>
          <div class="top">
            <div class="approval-time">
              <div @keyup.enter="getDataRunDr">
                <t-input
                  v-model="params.title"
                  style="width: 304px"
                  :placeholder="t('niche.sbar')"
                  clearable
                  @clear="getDataRunDr"
                  @blur="getDataRunDr"
                >
                  <template #prefix-icon>
                    <iconpark-icon name="iconsearch" class="name-icon" />
                  </template>
                </t-input>
              </div>
              <div v-if="paramsSuper" class="af-icon" @click="showFilter">
                <img src="@renderer/assets/approval/icons/fbutton.svg" style="width: 32px; height: 32px" alt="" />
              </div>
              <t-button
                v-else
                style="width: 32px; margin-left: 8px; height: 32px"
                type="button"
                theme="default"
                variant="outline"
                @click="showFilter"
              >
                <iconpark-icon name="iconscreen" class="icon iconscreen" />
              </t-button>
              <t-divider layout="vertical"></t-divider>
              <t-checkbox v-model="showDay">显示距离时间</t-checkbox>
            </div>
            <div class="adds niche-steps-extend" style="margin-left: 145px">
              <t-button @click="toExtend"> {{ t('niche.tggl') }} </t-button>
            </div>
          </div>
        </div>

        <div class="data-box-con">
          <div v-if="paramsSuper" class="filter-res" style="margin-bottom: 24px">
            <div class="tit">{{ t('approval.approval_data.sures') }}</div>
            <div v-if="params.type" class="stat te">
              <span>{{ t('niche.res_type') }}{{ params.type === 1 ? t('niche.gy') : t('niche.xq') }}</span>
              <span class="close2" @click="clearFilterStatus">
                <iconpark-icon name="iconerror" style="font-size: 20px" />
              </span>
            </div>
            <div v-if="params.created_at_begin || params.created_at_end" class="ov-time te">
              <span>{{ t('niche.cr_time') }}： {{ params.created_at_begin }} ~ {{ params.created_at_end }}</span>
              <span class="close2" @click="clearFilterFinish">
                <iconpark-icon name="iconerror" style="font-size: 20px" />
              </span>
            </div>

            <div v-if="params.release_channel" class="stat te">
              <span>{{ t('niche.res_ch_a') }}{{ optionsLabel() }}</span>
              <span class="close2" @click="clearFilterChannel">
                <iconpark-icon name="iconerror" style="font-size: 20px" />
              </span>
            </div>
            <div v-if="params.staff_name" class="kword te">
              <span>{{ t('niche.cjz2') }}{{ params.staff_name }}</span>
              <span class="close2" @click="clearFilterKeyword">
                <iconpark-icon name="iconerror" style="font-size: 20px" />
              </span>
            </div>
            <div v-if="params.classify_id" class="kword te">
              <span>{{ t('niche.yhfl') }}{{ classify_name }}</span>
              <span class="close2" @click="clearClassifytree">
                <iconpark-icon name="iconerror" style="font-size: 20px" />
              </span>
            </div>

            <div class="icon" @click="clearFilters">
              <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
              <a>{{ t('approval.approval_data.clearFilters') }}</a>
            </div>
          </div>
          <t-table :row-key="'id'" :data="listData" cell-empty-content="--" :columns="columns" @row-click="rowClick">
            <template #title-slot-channelType>
              {{ t('niche.wdqd') }}
              <t-tooltip :show-arrow="false">
                <template #content>
                  <div style="width: 280px">
                    {{ t('niche.wdqdinfo') }}
                  </div>
                </template>
                <iconpark-icon
                  name="iconhelp"
                  class="icon"
                  style="font-size: 20px; color: #828da5; position: relative; top: 5px"
                />
              </t-tooltip>
            </template>

            <template #title-slot-actions>
              操作
              <t-tooltip :show-arrow="false" content="编辑：需要将所有渠道下架才可编辑" placement="bottom-right">
                <iconpark-icon
                  name="iconhelp"
                  class="icon"
                  style="font-size: 20px; color: #828da5; position: relative; top: 5px"
                />
                <!-- <template #content>
                  <p>1、推广：需要发布到自身广场号才能推广</p>
                  <p>2、编辑：需要将所有渠道下架才可编辑</p>
                </template> -->
              </t-tooltip>
            </template>

            <template #team_name="{ row }">
              <div class="g-box" @click="onRowClick(row)">
                <div class="main-img">
                  <img
                    v-if="row.images"
                    :src="row.images[0].file_name + '?x-oss-process=image/resize,m_fill,w_200,quality,q_60'"
                    alt=""
                    @click.stop="preview(row.images)"
                  />
                  <img v-else src="/assets/business/Rectangle.png" alt="" />
                  <div v-if="row.is_external_link" class="taglink">外部</div>
                </div>
                <div class="good-info">
                  <div class="good-title">
                    <t-tooltip :content="row.title">
                      <div class="tc" :class="`tct${row.is_top}`">
                        <div v-if="row.is_top === 1" class="tag">{{ t('niche.top') }}</div>
                        <div v-if="row.type === 1" class="tag1">
                          {{ t('niche.gy') }}
                        </div>
                        <div v-else class="tag2">{{ t('niche.xq') }}</div>
                        {{ row.title }}
                      </div>
                    </t-tooltip>
                    <!-- <div class="tc" :class="`tct${row.is_top}`">
                      {{ spString(row.title) }}

                    </div> -->
                  </div>
                  <div class="good-time"><iconpark-icon name="icondate" class="name-icon" /> {{ row.created_at }}</div>
                  <!-- <div class="good-time">
                    <iconpark-icon name="iconplatformmember" class="name-icon" />
                    {{ row.staff_name }}
                  </div> -->
                </div>
              </div>
            </template>
            <template #approval_name="{ row }">
              <div v-if="row.effective_unlimited" @click="onRowClick(row)">长期</div>
              <div v-else @click="onRowClick(row)">
                <p>{{ row.effective_begin }} ～ {{ row.effective_end }}</p>
                <template v-if="showDay">
                  <p v-if="row.effective_state === 0" style="color: #499d60; margin-top: 2px">
                    {{ t('niche.jsxiao') }} {{ row.effective_day }} 天
                  </p>
                  <p v-if="row.effective_state === 1" style="color: #d54941; margin-top: 2px">
                    {{ t('niche.jsx') }} {{ row.effective_day }} 天
                  </p>
                </template>
              </div>
            </template>
            <template #channel_type="{ row }">
              <div class="channel-box">
                <div v-for="item in row.channel_data" :key="item.id" class="item">
                  <div class="name">{{ item.promotion_type === 1 ? t('niche.gch') : t('niche.szpt') }}</div>
                  <template v-if="item.id === 0 && item.release_state !== 4">
                    <div class="status status00">{{ t('niche.wfb') }}</div>
                    <div class="opt" @click.stop="channelRelease(row, item)">
                      <iconpark-icon name="iconcheckcorrect" class="name-icon" />
                      {{ t('niche.re_run') }}
                    </div>
                  </template>
                  <template v-else-if="item.release_state === 4">
                    <div class="status status4">
                      {{ releaseStateText[item.release_state] }}
                    </div>
                  </template>
                  <template v-else>
                    <div v-if="item.id" class="status" :class="`status${item.release_state}`">
                      {{ releaseStateText[item.release_state] }}
                    </div>

                    <div v-if="item.release_state === 1" class="opt" @click.stop="delistOne(item, item.promotion_type)">
                      <iconpark-icon name="iconcommodity-reduce" class="name-icon" />
                      {{ t('niche.delist') }}
                    </div>
                    <div
                      v-if="item.release_state === 2"
                      class="opt"
                      @click.stop="publishOne(item, item.promotion_type)"
                    >
                      <iconpark-icon name="iconListing" class="name-icon" />
                      {{ t('niche.sj') }}
                    </div>
                  </template>
                </div>
              </div>
            </template>
            <template #approve_status="{ row }">
              <div :class="'status-box' + row.approve_status">
                {{ approve_status_text[row.approve_status] }}
              </div>
            </template>
            <template #actions="{ row }">
              <div style="display: flex; gap: 8px; align-items: center">
                <div class="mbtna">
                  <a @click.stop="featureRun('extend', row)">推广</a>
                </div>
                <div class="mbtna">
                  <a @click.stop="featureRun(row?.func[0].id, row)">{{ row?.func[0].title }}</a>
                </div>
                <t-popup
                  v-if="row?.func?.length > 1"
                  overlay-class-name="niche-popup"
                  placement="left-top"
                  :z-index="1000"
                  destroy-on-close
                >
                  <template #content>
                    <div class="actions-boxs">
                      <div v-for="(item, index) in row?.func" v-show="index !== 0" :key="item.id">
                        <div class="item" @click.stop="featureRun(item.id, row)">
                          <div class="icon">
                            <iconpark-icon :name="item.icon" class="name-icon" />
                          </div>
                          <div class="text">{{ item.title }}</div>
                        </div>
                      </div>
                    </div>
                  </template>
                  <div class="mbtn" @click.stop="onRowClick(row)">
                    <iconpark-icon name="iconmore" class="icon" />
                  </div>
                </t-popup>
              </div>
            </template>
          </t-table>
          <noData
            v-if="!listData.length"
            :show-step="!listData.length && params.operation_state === 0 && !params.title && !paramsSuper"
            :text="t('approval.no_data')"
            :style="{
              marginTop:
                !listData.length && params.operation_state === 0 && !params.title && !paramsSuper ? '0px' : '88px',
            }"
            @create="toCreate"
          />
          <div v-if="total && total > 10" class="pagination">
            <t-pagination
              :total="total"
              :total-content="false"
              show-previous-and-next-btn
              :show-page-size="false"
              :current="params.page"
              @change="pageChange"
            />
          </div>
        </div>
      </div>
    </div>

    <t-drawer
      v-model:visible="filterVisible"
      :close-btn="true"
      size="472px"
      :header="t('approval.approval_data.sur')"
      class="filterDrawer"
    >
      <div class="form-boxxx">
        <div class="fitem">
          <div class="title">{{ t('niche.re_type') }}</div>
          <div class="ctl">
            <t-select
              v-model="drawerForm.type"
              :options="optionsType"
              clearable
              :placeholder="t('approval.operation.select')"
              ><template #suffixIcon>
                <img src="@/assets/svg/icon_arrow_down.svg" />
              </template>
            </t-select>
          </div>
        </div>
        <div class="fitem">
          <div class="title">创建者</div>
          <div class="ctl">
            <t-input v-model="drawerForm.staff_name" :maxlength="20" :placeholder="t('niche.res_peo_i')" />
          </div>
        </div>
        <div class="fitem">
          <div class="title">{{ t('niche.cr_time') }}</div>
          <div class="ctl">
            <t-date-range-picker
              v-model="drawerForm.finish"
              style="width: 100%"
              :placeholder="[t('approval.approval_data.start_time'), t('approval.approval_data.end_time')]"
              clearable
            >
              <template #suffixIcon>
                <iconpark-icon name="icondate" class="iconorientation" />
              </template>
            </t-date-range-picker>
          </div>
        </div>
        <!-- <div class="fitem">
          <div class="title">{{ t("niche.res_ch") }}</div>
          <div class="ctl">
            <t-select v-replace-svg
              v-model="drawerForm.release_channel"
              :options="options"
              clearable
              :placeholder="t('niche.res_ch_se')"
            />
          </div>
        </div> -->

        <div class="fitem">
          <div class="title">{{ t('niche.hyfl2') }}</div>
          <div class="ctl">
            <t-cascader
              v-model="drawerForm.classifytree"
              :options="classifytree"
              :keys="{ label: 'name', value: 'id', children: 'children' }"
              clearable
              :placeholder="t('niche.cla_tip')"
              @change="classifytreeChange"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <div class="foot">
          <t-button type="button" style="width: 80px" theme="default" @click="initF">
            {{ t('niche.rest') }}
          </t-button>

          <t-button style="width: 80px" type="button" @click="getDataRunDr">
            {{ t('niche.ss') }}
          </t-button>
        </div>
      </template>
    </t-drawer>

    <t-image-viewer v-model:visible="viewer" :images="imageFiles" />

    <delist ref="delistRef" :edit="delistEdit" @delist-succ="getDataRunDr" />

    <guide ref="guideRef" />
  </div>
  <div v-else class="fill">
    <no-permissions />
  </div>

  <RenewalDialog
    v-if="renewalDialogVisible"
    v-model="renewalDialogVisible"
    :square-id="squareData?.square?.squareId"
    :upgrade="upgrade"
    :team-id="teamId"
    @success="renewalSuccess"
  />
  <OpenSquare v-if="openSquareVisible" v-model="openSquareVisible" @success="renewalSuccess" />
  <Tricks :offset="{ x: '-32', y: '-40' }" uuid="商机-我的商机" />
</template>

<script setup lang="ts" name="nicheHome">
import { computed, onActivated, onMounted, reactive, ref } from 'vue';
import leftMenu from '@renderer/views/niche/components/leftMenu.vue';
import delist from '@renderer/views/niche/components/nicheHome/delist.vue';
import noData from '@renderer/views/niche/components/nicheHome/noData.vue';
import guide from '@renderer/views/niche/components/nicheHome/guide.vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import noPermissions from '@renderer/views/device-management/noPermissions.vue';
import {
  businessReleaseCopy,
  businessReleaseDel,
  businessReleaseList,
  hotSet,
  manageOn,
  marketClassifytree,
  nicheAuth,
  releaseRelease,
  releaseSelf,
} from '@renderer/views/niche/apis/index';
import { AprListResponse } from '@renderer/api/approval/model/approvalData';
import { AxiosResponse } from 'axios';
import { useI18n } from 'vue-i18n';
import { getTeamAnnualFee } from '@renderer/api/business/manage';
import { ClientSide } from '@renderer/types/enumer';
import { useRoute, useRouter } from 'vue-router';
import { getCommonAppAuthAxios } from '@renderer/api/digital-platform/api/businessApi';
import to from 'await-to-js';
import OpenSquare from '@/views/square/components/OpenSquare.vue';
import RenewalDialog from '@/views/square/components/annual-fee/AnnualFeeDialog.vue';
import { getAppsState } from './apis/create';
import { goToDigitalPlatform_member } from '../member/utils/auth';
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;
const { t } = useI18n();
const optionsType = computed(() => [
  { label: t('niche.gy'), value: 1 },
  { label: t('niche.xq'), value: 2 },
]);
const options = computed(() => [
  { label: t('niche.sq1'), value: '1' },
  { label: t('niche.sq2'), value: '2' },
  { label: t('niche.sq3'), value: '3' },
  { label: t('niche.sq4'), value: '4' },
  { label: t('niche.sq5'), value: '5' },
  { label: t('niche.sq6'), value: '6' },
  { label: t('niche.sq7'), value: '7' },
  { label: t('niche.sq8'), value: '8' },
]);
const releaseStateText = ref([
  t('niche.status_not_active'),
  t('niche.status_in_progress'),
  t('niche.status_off_shelf'),
  t('niche.status_expired'),
  t('niche.status_deleted'),
]);

const optionsLabel = () => {
  const opt = options.value.filter((item) => item.value === params.release_channel);
  return opt[0].label;
};
const approve_status_text = [
  '',
  t('approval.approval_data.under_approval'),
  t('approval.approval_data.pass'),
  t('approval.approval_data.refuse'),
  t('approval.approval_data.repeal'),
];

const emits = defineEmits(['uptWorkBenchTabItem', 'setActiveIndexAndName', 'settabItem', 'setActiveIndex']);

const allPermissions = ref({
  allow_niche: 0,
  allow_admin: 0,
});
const showDay = ref(true);

onActivated(() => {
  init();
});
const props = defineProps({
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
  activationGroupItem: {
    type: Object,
    default: null,
  },
  cloudDiskType: {
    type: Object,
    default: null,
  },
});
const route = useRoute();
const settabItem = () => {
  const res = props.tabList.some((root) => root.updateKey === 'nicheHome');
  if (res) {
    emits('uptWorkBenchTabItem', {
      path: `/workBenchIndex/nicheHome`,
      path_uuid: 'niche',
      title: t('niche.wdsji'),
      name: 'nicheHome,nicheExtend,nicheDraftList,nicheAdmin',
      updateKey: 'nicheHome',
      type: ClientSide.NICHE,
    });
  } else {
    ipcRenderer.invoke('set-work-bench-tab-item', {
      path: `/workBenchIndex/nicheHome`,
      path_uuid: 'niche',
      title: t('niche.wdsji'),
      name: 'nicheHome,nicheExtend,nicheDraftList,nicheAdmin',
      updateKey: 'nicheHome',
      type: ClientSide.NICHE,
    });
  }
};

const init = (tip?) => {
  settabItem();
  nicheAuth(teamId.value).then((res) => {
    if (res.data) {
      allPermissions.value = res.data.data;
      if (allPermissions.value?.allow_niche) {
        getDataRun();
        getTeamAnnualFeeRun();
        getMarketClassifytree();
        getAppAuth();
      } else if (tip) {
        MessagePlugin.error('该用户没有可用权限');
      }
    }
  });
};

onMounted(() => {
  init(true);
});

const total = ref(0);
const params = reactive({
  title: '',
  release_channel: '',
  type: undefined,
  staff_name: '',
  classify_name: null,
  created_at_begin: '',
  created_at_end: '',
  classify_id: null,
  page: 1,
  operation_state: 0,
  pageSize: 10,
});

const drawerForm = reactive({
  staff_name: '',
  release_channel: '',
  classifytree: null,
  finish: [],
  type: '',
});

const selectedRowKeys = ref([]);
const columns = [
  {
    colKey: 'team_name',
    title: t('niche.ni_info'),
    width: '344',
  },
  {
    colKey: 'approval_name',
    title: t('niche.yxsj'),
    width: '232',
  },
  {
    colKey: 'channel_type',
    title: 'title-slot-channelType',
    width: '224',
  },
  {
    colKey: 'actions',
    title: 'title-slot-actions',
    width: '144',
  },
];

const guideHandle = () => {
  console.log('guideHandle');

  setTimeout(() => {
    if (listData.value.length === 0 && !localStorage.getItem(`nicheFirst-${teamId.value}`)) {
      guideRef.value.start();
    }
  }, 200);
};

const listData = ref([]);

const getData = () => {
  businessReleaseList(params).then((res: AxiosResponse<AprListResponse>) => {
    console.log(res);
    if (res.data) {
      listData.value = res.data.data.list;
      // guideHandle();
      total.value = res.data.data.total;
      listDataHandle(listData.value);
    }
  });
};
const listDataHandle = (data) => {
  for (const item of data) {
    item.func = getFeature(item);
    if(item.is_external_link === 1) {
      item.channel_data = item.channel_data.filter((ch) => ch.promotion_type !== 1)
    }
    // getChannelFeature(item.channel_type);
  }
};

const viewer = ref(false);
const imageFiles = ref([]);
// const preview = (imgs) => {
//   console.log(imgs);
//   imageFiles.value = imgs;
//   viewer.value = true;
// };

const preview = (imgs) => {
  const temp = imgs.map((item) => ({ url: item.file_name }));
  console.log('preview', temp);
  ipcRenderer.invoke('view-img', JSON.stringify(temp));
};

const pageChange = (e) => {
  params.page = e.current;
  params.pageSize = e.pageSize;
  getData();
};

const reqParamsHandle = () => {
  params.created_at_begin = drawerForm.finish[0];
  params.created_at_end = drawerForm.finish[1];
  params.staff_name = drawerForm.staff_name;
  params.release_channel = drawerForm.release_channel;
  params.type = drawerForm.type;
  params.classify_id = drawerForm.classifytree;
};

const getDataRun = () => {
  getTeamAnnualFeeRun();
  reqParamsHandle();
  getData();
};

const filterVisible = ref(false);
const showFilter = () => {
  filterVisible.value = true;
};
const getDataRunDr = () => {
  filterVisible.value = false;
  delistEdit.value = false;
  params.page = 1;
  params.pageSize = 10;
  getDataRun();
};
const clearFilters = () => {
  params.created_at_begin = null;
  params.created_at_end = null;
  params.staff_name = '';
  params.release_channel = null;
  params.type = null;
  params.classify_id = null;
  drawerForm.type = null;
  drawerForm.release_channel = null;
  drawerForm.classifytree = null;
  drawerForm.staff_name = '';
  drawerForm.finish = [];
  params.page = 1;
  params.pageSize = 10;
  getDataRun();
};
const clearFilterStatus = () => {
  params.type = undefined;
  drawerForm.type = undefined;
  getDataRun();
};
const clearFilterChannel = () => {
  drawerForm.release_channel = null;
  params.release_channel = null;
  getDataRun();
};
const clearFilterKeyword = () => {
  drawerForm.staff_name = null;
  params.staff_name = null;
  getDataRun();
};
const clearFilterFinish = () => {
  drawerForm.finish = [];
  getDataRun();
};
const clearClassifytree = () => {
  params.classify_id = null;
  drawerForm.classifytree = null;
  getDataRun();
};
const paramsSuper = computed(
  () =>
    params.type ||
    params.release_channel ||
    params.staff_name ||
    params.classify_id ||
    params.created_at_end ||
    params.created_at_begin,
);

const squareData = ref(null);
const pageShow = ref(true);
const tipsData = ref({
  con: '',
  btn: '',
  tipHeader: null,
});
const renewalDialogVisible = ref(false);
const upgrade = ref(false);
const teamId = ref(localStorage.getItem('businessTeamId') || '');
const openSquareVisible = ref(false);
const squareShow = ref(false);
const checkExpiration = (expiredAt) => {
  const now = new Date();
  const expirationDate = new Date(expiredAt);
  return now > expirationDate;
};
const getTeamAnnualFeeRun = async () => {
  const res = await getTeamAnnualFee(teamId.value);
  if (res.data) {
    squareData.value = res.data;
    tipsData.value.tipHeader = null;
    if (squareData.value.opened) {
      pageShow.value = false;
      squareShow.value = false;
      if (checkExpiration(squareData.value.annualFeeExpiredAt)) {
        if (squareData.value?.annualFeeDetail?.trial) {
          pageShow.value = true;
          tipsData.value.tipHeader = t('niche.vip_open_tiph');
          tipsData.value.con = t('niche.vip_open_tip5');
          tipsData.value.btn = t('niche.vip_open_btn4');
        } else {
          pageShow.value = true;
          tipsData.value.con = t('niche.vip_open_tip1');
          tipsData.value.btn = t('niche.vip_open_btn1');
        }
      } else {
        pageShow.value = false;
        if (squareData.value?.annualFeeDetail?.package) {
          const item = squareData.value?.annualFeeDetail?.package?.items.find((item) => item.itemType === 'NICHE');
          if (!item) {
            if (squareData.value?.annualFeeDetail?.trial) {
              tipsData.value.con = t('niche.vip_open_tip4');
              tipsData.value.btn = t('niche.vip_open_btn4');
              tipsData.value.tipHeader = t('niche.vip_open_tiph');
              upgrade.value = false;
            } else {
              tipsData.value.con = t('niche.vip_open_tip2');
              tipsData.value.btn = t('niche.vip_open_btn2');
              upgrade.value = true;
            }
            pageShow.value = true;
          } else {
            pageShow.value = false;
          }
        } else {
          tipsData.value.con = t('niche.vip_open_tip2');
          tipsData.value.btn = t('niche.vip_open_btn2');
          upgrade.value = true;
          pageShow.value = true;
        }
      }
    } else {
      tipsData.value.con = t('niche.vip_open_tip3');
      tipsData.value.btn = t('niche.vip_open_btn3');
      pageShow.value = true;
      squareShow.value = true;
    }
  }
};

const renewalSuccess = (e) => {
  renewalDialogVisible.value = false;
  openSquareVisible.value = false;
  getTeamAnnualFeeRun();
};

const initF = () => {
  filterVisible.value = false;
  clearFilters();
};

const classifytree = ref([]);
const getMarketClassifytree = () => {
  const area = props.activationGroupItem.teamRegion;
  marketClassifytree(area).then((res) => {
    console.log(res);
    if (res.data) {
      classifytree.value = res.data.data;
    }
  });
};

const classify_name = ref('');
const classifytreeChange = (e, ctx) => {
  console.log(e);
  let str = '';
  const item = ctx.node['__tdesign_tree-node__'];
  if (item.parent) {
    str = `${item.parent.label}/${item.label}`;
  } else {
    str = item.label;
  }
  classify_name.value = str;
};

const copyBusinessReq = (cid, myDialog) => {
  businessReleaseCopy({ id: cid }).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      // delSucc();
      MessagePlugin.success('复制成功');
      myDialog.hide();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};

const router = useRouter();
const toExtend = () => {
  router.push(`/workBenchIndex/nicheExtendManage`);
};

const getFeature = (rowData) => {
  if (rowData.effective_state === 3) {
    return [
      // {
      //   title: t("niche.tg"),
      //   id: "extend",
      //   icon: "icontotopcancel",
      //   show: true,
      //   keys: [0, 1, 2, 3, 4],
      // },
      {
        title: t('niche.copy'),
        id: 'copy',
        icon: 'iconcopy',
        show: true,
        keys: [0, 1, 2, 3, 4],
      },
    ];
  }
  // 发布状态（0：未生效，1：进行中，2：已下架，3：已失效，4：已删除）
  const funcs = [
    // {
    //   title: t("niche.tg"),
    //   id: "extend",
    //   icon: "icontotopcancel",
    //   show: true,
    //   keys: [0, 1, 2, 3, 4],
    // },
    {
      title: t('niche.sj'),
      id: 'listing',
      icon: 'iconListing',
      show: false,
      keys: [2],
    },
    {
      title: t('niche.delist'),
      id: 'delist',
      icon: 'iconcommodity-reduce',
      show: false,
      keys: [1],
    },
    {
      title: t('niche.edit'),
      id: 'edit',
      icon: 'iconedit',
      show: false,
      keys: [],
      // keys: [0, 2, 3],
    },
    {
      title: t('niche.top'),
      id: 'pinned',
      icon: 'icontotop',
      show: false,
      keys: [0, 1, 2],
    },
    {
      title: t('niche.topcal'),
      id: 'topCancel',
      icon: 'icontotopcancel',
      show: false,
      keys: [0, 1, 2],
    },
    {
      title: t('niche.copy'),
      id: 'copy',
      icon: 'iconcopy',
      show: true,
      keys: [0, 1, 2, 3, 4],
    },
    {
      title: t('niche.del'),
      id: 'delete',
      icon: 'icondelete',
      show: false,
      keys: [0, 1, 2, 3],
    },
  ];
  const topKey = rowData.is_top ? 'pinned' : 'topCancel';

  for (const item of rowData.channel_data) {
    for (const func of funcs) {
      if (func.keys.includes(item.release_state)) {
        func.show = true;
      }
    }
  }
  if(rowData.is_external_link){
    funcs[3].show = false;
  }
  if (rowData.operation_rules?.can_edit) {
    funcs[2].show = true;
  }
  return funcs.filter((item) => item.show).filter((item) => item.id !== topKey);
};

const extendRun = (row) => {
  console.log('推广');
  const isOpen = true;
  // const isOpen = row.channel_data[0].id !== 0; 5.20去掉该判断
  if (!isOpen) {
    const myDialog = DialogPlugin({
      header: t('niche.tip'),
      theme: 'info',
      body: t('niche.tgskss'),
      className: 'dialog-classp32',
      cancelBtn: null,
      onConfirm: () => {
        myDialog.hide();
      },
    });
  } else {
    router.push(`/workBenchIndex/detailExtendPage?to=2&id=${row.id}&title=${row.title}`);
  }
};
const delistEdit = ref(false);
const editBusRun = (rowData) => {
  console.log(t('niche.edit'));
  console.log(rowData);
  if (rowData.channel_data[0].release_state === 1 || rowData.channel_data[1]?.release_state === 1) {
    const myDialog = DialogPlugin({
      header: t('niche.tip'),
      theme: 'info',
      body: t('niche.edxiaj'),
      className: 'dialog-classp32',
      confirmBtn: t('niche.delist'),
      onConfirm: () => {
        myDialog.hide();
        delistEdit.value = true;
        delistRun(rowData, 0);
      },
      onCancel: () => {
        myDialog.hide();
      },
    });
  } else {
    router.push(`/workBenchIndex/nicheEdit?form=list&id=${rowData.id}`);
  }
};
const listingRunReq = (id, myDialog) => {
  releaseRelease(id).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc(t('niche.sjcg'));
      myDialog.hide();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};
const isMac = ref(process.platform === 'darwin');
const mactip = () => {
  const myDialog = DialogPlugin({
    header: t('niche.tip'),
    theme: 'info',
    body: t('niche.vip_open_tip2'),
    className: 'dialog-classp32',
    cancelBtn: null,
    confirmBtn: '知道了',
    onConfirm: () => {
      myDialog.hide();
    },
  });
};
const plazaRenew = () => {
  if (upgrade.value && isMac.value) {
    mactip();
    return;
  }
  const myDialog = DialogPlugin({
    header: tipsData.value.tipHeader || t('niche.tip'),
    theme: 'info',
    body: tipsData.value.con,
    className: 'dialog-classp32',
    confirmBtn: tipsData.value.btn,
    onConfirm: () => {
      if (squareShow.value) {
        openSquareVisible.value = true;
      } else {
        renewalDialogVisible.value = true;
      }
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const figureRenew = () => {
  const myDialog = DialogPlugin({
    header: t('niche.tip'),
    theme: 'info',
    body: t('niche.szpttip'),
    className: 'dialog-classp32',
    onConfirm: () => {
      console.log('figureRenew');
    },
    confirmBtn: t('niche.toopen'),
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const listingMain = (rowData) => {
  const myDialog = DialogPlugin({
    header: t('niche.tip'),
    theme: 'info',
    body: t('niche.sjhhc'),
    className: 'dialog-classp32',
    onConfirm: () => {
      listingRunReq(rowData.id, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const listingRun = async (rowData) => {
  console.log(t('niche.sj'));
  if (pageShow.value) {
    plazaRenew();
  } else {
    listingMain(rowData);
  }
  // else if (!figureAuth.value) {
  //   figureAuthTip();
  // }
};
const copyBusRun = (data) => {
  console.log(t('niche.copy'));
  const myDialog = DialogPlugin({
    header: t('niche.tip'),
    theme: 'info',
    body: `${t('niche.askcopy')} “${data.title}” 到草稿箱中？`,
    className: 'dialog-classp32',
    onConfirm: () => {
      copyBusinessReq(data.id, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const businessDelRun = (row) => {
  console.log(t('niche.del'));
  const myDialog = DialogPlugin({
    header: t('niche.tip'),
    theme: 'info',
    body: t('niche.re_del_tip'),
    className: 'dialog-classp32',
    onConfirm: () => {
      businessDelReq(row.id, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const delSucc = (msg) => {
  MessagePlugin.success(msg);
  getData();
};
const businessDelReq = (id, myDialog) => {
  businessReleaseDel(id).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc(t('niche.delsucc'));
      myDialog.hide();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};

const editHotReq = (hot, row, myDialog) => {
  hotSet({ is_top: hot }, row.id).then((res: any) => {
    console.log(res);
    if (res.data?.code === 0) {
      MessagePlugin.success(hot === 0 ? '取消置顶' : '置顶成功');
      myDialog.hide();
      getDataRun();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};

const editHotRun = (rowData) => {
  const val = rowData.is_top ? 0 : 1;
  const tipText = [t('niche.topcl'), t('niche.topop')];
  const myDialog = DialogPlugin({
    header: t('niche.tip'),
    theme: 'info',
    body: tipText[val],
    className: 'dialog-classp32',
    onConfirm: () => {
      editHotReq(val, rowData, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const delistRef = ref(null);
const delistRun = (rowData, origin) => {
  console.log(t('niche.delist'));
  delistRef.value.deOpen(rowData, origin);
};
const featureRun = (eventId, rowData) => {
  console.log(eventId, rowData);
  switch (eventId) {
    case 'extend':
      extendRun(rowData);
      break;
    case 'listing':
      listingRun(rowData);
      break;
    case 'delist':
      delistRun(rowData, 0);
      break;
    case 'edit':
      editBusRun(rowData);
      break;
    case 'pinned':
      editHotRun(rowData);
      break;
    case 'topCancel':
      editHotRun(rowData);
      break;
    case 'copy':
      copyBusRun(rowData);
      break;
    case 'delete':
      businessDelRun(rowData);
      break;
    default:
      console.log('推广');
      break;
  }
};

const delistOne = (data, type) => {
  delistRef.value.deOpen(data, type);
};
const publishOneReq = (data, type, myDialog) => {
  manageOn({ id: data.id, type }).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc(t('niche.sjcg'));
      myDialog.hide();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};
const publishOne = (data, type) => {
  if (pageShow.value) {
    plazaRenew();
    return;
  }
  const tiptext = [
    '',
    '上架后，在自身广场号上进行发布展示，且其他推广渠道均重新发送申请。确定继续上架？',
    '发布后，在自身数字平台上进行发布展示。确定继续上架？',
  ];
  const myDialog = DialogPlugin({
    header: t('niche.tip'),
    theme: 'info',
    body: tiptext[type],
    className: 'dialog-classp32',
    confirmBtn: t('niche.sj'),
    onConfirm: () => {
      publishOneReq(data, type, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const guideRef = ref(null);

const setOperationState = (key) => {
  params.operation_state = key;
  getDataRunDr();
};
const onRowClick = (row) => {
  console.log(row);
  
  // router.push(`/workBenchIndex/nicheDetail?to=1&id=${row.id}&title=${row.title}`);
};
const rowClick = (e) => {
  const row = e.row;
  router.push(`/workBenchIndex/nicheDetail?to=1&id=${row.id}&title=${row.title}`);
};
const figureAuth = ref(false);
const getAppAuth = async () => {
  const [err, res] = await to(getAppsState({ teamId: teamId.value }, teamId.value));
  if (err) {
    return;
  }
  const { data } = res;
  console.log(data.data, "figureAuth");
  if (data.data?.government || data.data?.member || data.data?.cbd|| data.data?.association|| data.data?.uni) {
    figureAuth.value = true;
  } else {
    figureAuth.value = false;
  }
};

const figureAuthTip = () => {
  const myDialog = DialogPlugin({
    header: t('niche.tip'),
    theme: 'info',
    body: t('niche.szpttip'),
    className: 'dialog-classp32',
    onConfirm: () => {
      jumpFigureRun();
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const jumpFigureRun = () => {
  goToDigitalPlatform_member(teamId.value);
  // ipcRenderer
  //   .invoke("click-menu-item", {
  //     url: "/digitalPlatformIndex/digital_platform_home",
  //     query: null,
  //     selected_path_uuid: "digital_platform",
  //     click_path_uuid: "digital_platform",
  //   })
  //   .then((res) => {
  //     console.log("goPath res: ", res);
  //     res && ipcRenderer.send("update-nume-index", 6);
  //     // if (res) {
  //     //   configData.$state.numeIndex = 5;
  //     //   configData.$state.path_uuid = "digital_platform";
  //     // }
  //   })
  //   .catch((err) => {
  //     console.log("goPath error: ", err);
  //   });
};

const channelResRun = (rowData, type) => {
  const typeText = type === 1 ? t('niche.gch') : t('niche.szpt');
  const myDialog = DialogPlugin({
    header: t('niche.tip'),
    theme: 'info',
    body: `是否将“${rowData.title}”发布到自身的“${typeText}”渠道中？`,
    className: 'dialog-classp32',
    onConfirm: () => {
      myDialog.hide();
      releaseSelfRun(rowData.id, type);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const releaseSelfRun = (rid, type) => {
  releaseSelf(rid, type).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc(t('niche.fbcg'));
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};

const toCreate = () => {
  if (pageShow.value) {
    plazaRenew();
  } else {
    router.push(`/workBenchIndex/nicheCreate`);
  }
};

const channelRelease = (row, item) => {
  // 广场
  if (item.promotion_type === 1) {
    if (pageShow.value) {
      plazaRenew();
    } else {
      channelResRun(row, 1);
    }
  } else if (figureAuth.value) {
    channelResRun(row, 2);
  } else {
    figureAuthTip();
  }
};
</script>

<style lang="less" scoped>
@import './styles/common.less';
.bus {
  height: 100%;
  display: flex;
}
.fill {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  padding-bottom: 202px;
}
.right-box {
  width: calc(100vw - 240px);
}
.right-content::-webkit-scrollbar {
  width: 0px;
}
.right-content {
  padding: 0 16px 16px 16px !important;
  position: relative;
  border-radius: 4px;
  background: #fff;
  height: calc(100vh - 40px);
  // overflow-y: auto;
  width: 100%;

  .pagination {
    margin-top: 15px;
  }
  .data-box-header {
    margin-bottom: 24px;
    .f-btns {
      display: flex;
      padding: 12px 0px;
      gap: 8px;
      align-items: center;
      align-self: stretch;
      border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
      .f-btn {
        width: 80px;
      }
    }
    .top {
      display: flex;
      margin-bottom: 24px;
      justify-content: space-between;
      padding-top: 16px;
      .approval-name {
        display: flex;
        align-items: center;
        .label {
          color: var(--text-kyy-color-text-1, #1a2139);
          display: flex;
          align-items: center;
        }
        .mock-in {
          width: 304px;
          min-height: 32px;
          background: #ffffff;
          border: 1px solid #e5e5e5;
          border-radius: 4px;
          display: flex;
          cursor: pointer;
          margin-left: 12px;
          .tag-con {
            width: 1050px;
            display: flex;
            flex-wrap: wrap;
            .tag {
              height: 24px;
              background: rgba(33, 118, 255, 0.1);
              border-radius: 15px;
              font-size: 14px;
              font-family: Undefined, Undefined-Regular;
              font-weight: 400;
              text-align: left;
              color: #2176ff;
              padding: 1px 8px;
              margin: 4px 0px 4px 8px;
              line-height: 24px;
            }
            .tag-text {
              font-size: 14px;

              font-weight: 400;
              text-align: left;
              color: #13161b;
              line-height: 28px;
              margin-left: 5px;
            }
          }
          .icon {
            padding-top: 5px;
          }
        }
        .borderColor {
          border: 1px solid red;
        }
      }
      .approval-time {
        display: flex;
        align-items: center;
        .label {
          width: 76px;
          font-size: 14px;

          font-weight: 400;
          color: var(--kyy_color_tag_text_black, #1a2139);
        }
        .f-icon {
          display: flex;
          width: 32px;
          height: 32px;
          cursor: pointer;
          min-height: 32px;
          max-height: 32px;
          padding: 6px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          margin-left: 8px;
          border-radius: var(--radius-kyy-radius-button-s, 4px);
          border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
          background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
        }
        .f-icon:hover {
          color: #4d5eff;
          border-color: #4d5eff;
        }
      }
      .approval-status {
        display: flex;
        align-items: center;
        .label {
          width: 84px;
          font-size: 14px;

          font-weight: 400;
          text-align: right;
          color: #13161b;
          margin-right: 8px;
        }
      }
      .approval-key {
        display: flex;
        align-items: center;
        margin-left: 32px;
        .label {
          width: 42px;
          font-size: 14px;

          font-weight: 400;
          text-align: right;
          color: var(--kyy_color_tag_text_black, #1a2139);
          margin-right: 8px;
        }
      }
      .approval-end {
        margin-left: 50px;
        display: flex;
        align-items: center;
        .label {
          width: 56px;
          font-size: 14px;

          font-weight: 400;
          text-align: right;
          color: var(--kyy_color_tag_text_black, #1a2139);
          margin-right: 7px;
          margin-left: 3px;
        }
      }
    }
  }

  .opblack {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    width: 100%;
  }
}
.data-box-con::-webkit-scrollbar {
  width: 0px;
}
.data-box-con {
  color: var(--kyy-color-table-text, #1a2139);
  height: calc(100% - 130px);
  overflow-y: auto;
  .status-box1 {
    width: 58px;
    height: 24px;
    border-radius: var(--kyy-radius-tag-full, 999px);
    background: var(--kyy-color-tag-bg-warning, #ffe5d1);
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: var(--kyy-color-tag-text-warning, #fc7c14);
  }
  .approval-name-ov {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .status-box2 {
    width: 58px;
    height: 24px;
    border-radius: var(--kyy-radius-tag-full, 999px);
    background: var(--kyy-color-tag-bg-success, #e0f2e5);
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: var(--kyy-color-tag-text-success, #499d60);
  }
  .status-box3 {
    width: 58px;
    height: 24px;
    border-radius: var(--kyy-radius-tag-full, 999px);
    background: #fbdde3;
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: var(--lingke-wrong, #d92f4d);
  }
  .status-box4 {
    width: 58px;
    height: 24px;
    background: #fff6e8;
    border-radius: 4px;
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: #e66800;
  }
}
.sw-box {
  padding: 12px;
  .item {
    display: flex;
  }
  .text {
    font-size: 14px;
    font-family: Undefined, Undefined-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
  }
  .sw {
    margin-left: 18px;
  }
}
.record {
  color: var(--text-kyy-color-text-2, #516082);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
:deep(.data-box-con .t-table__empty) {
  display: none;
}
.ex-btn {
  display: flex;
  height: 32px;
  min-width: 80px;
  min-height: 32px;
  max-height: 32px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--radius-kyy-radius-button-s, 4px);
  border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
  background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
  color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
  text-align: center;

  /* kyy_fontSize_2/bold */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
}

.g-box {
  display: flex;
  .main-img {
    width: 72px;
    height: 72px;
    border-radius: 4px;
    position: relative;
    img {
      cursor: pointer;
      width: 72px;
      height: 72px;
      border-radius: 4px;
      object-fit: cover;
    }
    .taglink {
      display: inline-flex;
      padding: 0 6px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      border-radius: 99px;
      background: linear-gradient(316deg, #6c95ff -1.59%, #deeff7 23.81%, #d0f8fa 49.21%, #fff 74.6%, #a3affd 100%);
          position: absolute;
      top: -8px;
    right: -8px;
    }
  }
  .good-info {
    margin-left: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 236px;
    .good-title {
      // display: flex;
      .tag {
        display: flex;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_brand, #eaecff);
        color: var(--kyy_color_tag_text_brand, #4d5eff);
        text-align: center;
        float: left;
        font-size: 12px;
      }
      .tag1 {
        display: flex;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_cyan, #e6f9f8);
        color: var(--kyy_color_tag_text_cyan, #11bdb2);
        text-align: center;

        /* kyy_fontSize_1/regular */
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
        text-align: center;
        font-size: 12px;
        float: left;
      }
      .tag2 {
        display: flex;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_warning, #ffe5d1);
        color: var(--kyy_color_tag_text_warning, #fc7c14);
        text-align: center;
        font-size: 12px;
        float: left;
      }
      .tc {
        color: var(--text-kyy-color-text-1, #1a2139);
        // width: 100%;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
        flex: 1;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        flex: 1 0 0;
        overflow: hidden;
        color: var(--text-kyy_color_text_1, #1a2139);
        text-overflow: ellipsis;
      }
    }
    .good-time {
      color: var(--text-kyy-color-text-3, #828da5);
      display: flex;
      gap: 2px;
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      margin-top: 6px;
    }
  }
}
.mbtn {
  width: 28px;
  height: 28px;
  padding: 4px;
  text-align: center;
  font-size: 24px;
  line-height: 4px;
  cursor: pointer;
  // border: 1px solid #fff;
  .icon {
    font-size: 20px;
    color: #828da5;
  }
}
.mbtn:hover {
  border-radius: 4px;
  background: var(--bg-kyy-color-bg-brand-foucs, #dbdfff);
  color: #4d5eff;
  // border: 1px solid #4d5eff;
  .icon {
    font-size: 20px;
    color: #4d5eff;
  }
}
.actions-boxs {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  width: 136px;
  min-height: 20px;
  .item {
    display: flex;
    height: 32px;
    min-width: 136px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    align-items: center;
    gap: 12px;
    align-self: stretch;
    cursor: pointer;
    .icon {
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
      }
    }
    .text {
      color: var(--kyy_color_dropdown_text_default, #1a2139);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      // margin-left: 4px;
    }
  }
  .item:hover {
    border-radius: var(--kyy_radius_dropdown_s, 4px);
    background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
  }
  .item-view {
    border-top: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  }
}

.channel_type {
  display: flex;
  justify-content: center;
  flex-direction: column;
  .channel_item {
    display: flex;
    align-items: center;
  }
  .tagr {
    width: 8px;
    height: 8px;
    border-radius: 8px;
    margin-right: 8px;
  }
  .type-tag {
    background-color: #4d5eff;
  }
  .type-tag1 {
    background-color: #d5dbe4;
  }
}

// :deep(.have-pagination .t-table__content) {
//   max-height: calc(100vh - 180px) !important;
// }
// :deep(.no-pagination .t-table__content) {
//   max-height: calc(100vh - 140px) !important;
// }

// :deep(.have-pagination1 .t-table__content) {
//   max-height: calc(100vh - 230px) !important;
// }
// :deep(.no-pagination1 .t-table__content) {
//   max-height: calc(100vh - 168px) !important;
// }

.channel-box {
  gap: 4px;
  display: flex;
  flex-direction: column;
  .item {
    display: flex;
    padding: 4px;
    align-items: center;
    gap: 4px;
    border-radius: 2px;
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
    .name {
      display: flex;
      width: 64px;
      height: 24px;
      justify-content: center;
      padding: 0px 4px;
      align-items: center;
      color: var(--text-kyy_color_text_1, #1a2139);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      border-radius: 2px;
      background: var(--bg-kyy_color_bg_light, #fff);
      margin-left: 2px;
    }
    .status {
      display: flex;
      height: 24px;
      justify-content: center;
      align-items: center;
      padding: 0px 8px;
      border-radius: 2px;
      background: var(--bg-kyy_color_bg_light, #fff);
    }
    .status00 {
      color: var(--kyy_color_tag_text_gray, #516082);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
    }
    .status0 {
      color: var(--brand-kyy_color_brand_default, #4d5eff);
      text-align: right;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .status1 {
      color: var(--warning-kyy_color_warning_default, #fc7c14);
      text-align: right;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .status2,
    .status3,
    .status4 {
      color: var(--error-kyy_color_error_default, #d54941);
      text-align: right;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .opt {
      display: flex;
      width: 58px;
      height: 24px;
      justify-content: center;
      align-items: center;
      padding: 0px 4px;
      border-radius: 2px;
      background: var(--bg-kyy_color_bg_light, #fff);
      color: var(--kyy_color_dropdown_text_default, #1a2139);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      cursor: pointer;
    }
    .opt:hover {
      background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
    }
  }
}
</style>
