// 活动助手
import { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue';
import moment from "moment";
import { MessagePlugin } from 'tdesign-vue-next';
import { ChevronDownIcon, ChevronUpIcon } from 'tdesign-icons-vue-next';
import { AppCard, AppCardHeader, AppCardBody, AppCardFooter, AppCardBtn, AppCardText } from '../MessageAppCard';
import ActivityTsx from './Activity/Activity';
import Activity from './Activity/Activity.vue';
import ActivityPromote from './Activity/promote/index.vue';
import MsgCard from './Activity/MsgCard.vue';
import { getActivityStatus, activityResponse, activityRegisters } from '../../service/extend/statusUtils';
import { i18nt } from "@/i18n";
import { formatStatus } from "@/views/activity/utils";
import './AppActivityStyle.less';
import { useMessageStore } from '../../service/store';
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

export default defineComponent({
    props: {
        data: { type: Object, default: null },
        activityContentType: { type: String, default: 'APP_ACTIVITY' }
    },
    setup(props, ctx) {
        const newVersion = props.data.contentExtra?.data?.new_version;
        const scene = props.data.contentExtra?.scene;

        if ([20052].includes(scene)) {
          return () => <ActivityPromote msg={ props.data } />;
        }
        if (newVersion && 'activity_card' === props.activityContentType) {
          return () => <MsgCard data={ props.data } />;
        }
        const isAssistant = ['APP_ACTIVITY'].includes(props.activityContentType);
        if (newVersion && isAssistant) {
          return () => <Activity data={ props.data } />;
        }
        if (isAssistant) {
          return () => <ActivityTsx data={ props.data } activityContentType={ props.activityContentType } />;
        }
        const isLoading = ref(false);
        const isLoadError = ref(false);
        const getData = (msg) => {
            isLoadError.value = false;
            isLoading.value = true;
            getActivityStatus(msg, props.activityContentType).then((res) => {
                isLoading.value = false;
            }).catch((err) => {
              isLoading.value = false;
              // if (!err?.response?.data?.metadata?.deletedAt) {
              //   isLoadError.value = true;
              // }
            });
        };

        getData(props.data);

        const activityStatusObj = computed(() => {
            const row = props.data.contentExtra?.data?.apiData?.activity || { ...props.data.contentExtra?.data, duration: { startTime: props.data.contentExtra?.data?.duration?.start_time, endTime: props.data.contentExtra?.data?.duration?.end_time } };
            if (!props.data.contentExtra?.data?.apiData?.activity && row.receiver?.start_time) {
                row.receiver = { startTime: row?.receiver?.start_time, endTime: row?.receiver?.end_time };
            }
            return {
                text: formatStatus(row, 'activityTypeText', 'duration') as string,
                durationValue: formatStatus(row, 'activityTypeClass', 'duration') as string,
                registerValue: formatStatus(row, 'activityTypeClass', 'register') as string
            };
        });

        let timer = null;
        const num = ref(0);
        const clearTimer = () => {
            clearInterval(timer);
            timer = null;
        };
        const setTimer = () => {
            if (timer) {
                clearTimer();
            }
            if (['noStart', 'ongoing'].includes(activityStatusObj.value.durationValue) && props.data.contentExtra?.data?.apiData?.role !== 'None') {
                timer = setInterval(() => {
                    // 2024/08/27
                    // console.log(props.data.contentExtra);


                    // const { startTime, endTime } = props.data.contentExtra?.data?.apiData?.activity?.duration;
                    const { startTime, endTime } = props.data.contentExtra?.data?.duration;

                    const dateTime = activityStatusObj.value.durationValue === 'noStart' ? startTime : endTime;
                    num.value = dateTime - moment().unix();
                    if (num.value <= 0) {
                        getActivityStatus(props.data, props.activityContentType);
                        (activityStatusObj.value.durationValue === 'ongoing') && clearTimer();
                    }
                }, 1000);
            }
        };
        onMounted(() => {
            if (['APP_ACTIVITY', 'ActivityInvitation'].includes(props.activityContentType)) {
                setTimer();
            }
        });
        onUnmounted(() => {
            if (timer) {
                clearTimer();
            }
        });


        return () => {
            const msg = props.data;
            const data = msg.contentExtra?.data;
            const scene = msg.contentExtra?.scene;
            const isAssistant = ['APP_ACTIVITY', 'ActivityInvitation'].includes(props.activityContentType);
            if (isAssistant) {
              return <ActivityTsx data={ props.data } activityContentType={ props.activityContentType } />;
            }
            const activityRes = (val, type) => {
                const info = textList?.find((item) => item.code === val);
                activityResponse(msg, info?.code, info?.value);
            };
            const registers = (isRegisters) => {
                activityRegisters(msg, isRegisters);
            };
            const changeActivityRes = (value) => {
                const info = textList?.find((item) => item.value === value);
                activityResponse(msg, info?.code, info?.value);
                // activityResponse(msg, Number(e.target.value));
            };

            console.log('kakaxi', data, 'isAssistant', isAssistant);
            // 是否显示 立即报名：公开活动&&报名未结束&&没有参与||参与  与app同步不显示
            let showRegisters = data?.apiData?.activity?.open && (data?.register?.end_time ? moment().unix() <= data?.register?.end_time : true) && (!data?.apiData?.role || data?.apiData?.role === 'None' || data?.apiData?.role === 'Registered');
            // 是否显示 无法加入：未删除&&非公开活动&&被移除参与
            let showNoJoin = !data?.apiData?.isDeleted && !data?.apiData?.activity?.open && (!data?.apiData?.role || data?.apiData?.role === 'None');
            // 是否显示 参与拒绝暂定：非创建者&&参与人&&活动未取消&&未开始
            let showStatusBtn = ((data?.creator?.card_id || data?.creator?.openid) !== (data?.receiver?.card_id || data?.receiver?.openid)) && data?.apiData?.role === 'Appointed' && !(data?.apiData?.activity?.cancelled > 0) && (moment().unix() <= data?.duration?.start_time);
            // { showRegisters ? <AppCardBtn text={data?.apiData?.role === 'Registered' ? '取消报名' : '立即报名'} theme={data?.apiData?.role === 'Registered' ? 'default_background' : 'primary_background'} style="min-width: 80px;" onClick={() => registers(data?.apiData?.role === 'Registered')} /> : null }
            let isUnknown = data?.apiData?.reply && data?.apiData?.reply === 'Unknown';
            // 是否显示 活动状态按钮（已删除、进行中、已结束、已取消)

            const isDeleted = data?.apiData?.isDeleted;
            const isGoing = activityStatusObj.value.durationValue === 'ongoing' && data?.apiData?.role !== 'None';
            const isEnd = activityStatusObj.value.durationValue === 'ended';
            const isCancel = activityStatusObj.value.durationValue === 'canceled';
            let showActivityStatus = isDeleted || isGoing || isEnd || isCancel;

            const PopupVisible = { visible: false };

            const textList = [
                { label: i18nt('im.msg.refuse'), value: 'Denied', code: 3, selected: data?.apiData?.reply === 'Denied' },
                { label: i18nt('im.msg.pending'), value: 'Received', code: 2, selected: data?.apiData?.reply === 'Received' },
                { label: i18nt('im.msg.join'), value: 'Confirmed', code: 1, selected: data?.apiData?.reply === 'Confirmed' },
            ];

            if (!isAssistant) {
                showStatusBtn = false;
                showRegisters = false;
                showNoJoin = false;
                isUnknown = false;
                showActivityStatus = false;
            }

            // 把活动分享到群
            if (props.activityContentType === 'ActivityInvitation') {
                // showStatusBtn = true;
                // showRegisters = true;
                // showNoJoin = false;
                // isUnknown = true;
                // showActivityStatus = false;

                showStatusBtn = false;
                showRegisters = false;
                showNoJoin = false;
                isUnknown = false;
                showActivityStatus = true;
            }


            const onClickDetail = async () => {
                if (showNoJoin) {
                    return MessagePlugin.info({
                        content: i18nt('im.msg.joinTip'),
                        placement: "center"
                        });
                }
                if (isDeleted) {
                    return MessagePlugin.warning({
                        content: i18nt('activity.activity.hasDelete'),
                        placement: "center"
                    });
                }
                const row = data;
                console.log(row, props.data);
                const contentExtra = props.data.contentExtra;
                console.log(row.receiver.card_id);
                console.log(`layoutActivity/activityDetailLayout/${row.id}?title=${row.title}&teamId=${row.receiver.team_id}&myCardId=${contentExtra.receiverId || row.receiver.card_id || row.receiver.openid}`);
                ipcRenderer.invoke('create-dialog', { url: `layoutActivity/activityParticipantDetailLayout/${row.id}?subject=${encodeURIComponent(row.title)}
                  &teamId=${encodeURIComponent(row.receiver.team_id)}&cardId=${encodeURIComponent(row.receiver.card_id)}&type=aloneWindow`, opts: { x: 50, y: 50, width: 1296, minWidth: 1296, height: 720, }, });
            };

             // 分享没有操作按钮，直接查看,
            const onClickDetailShare = async () => {
                if (isDeleted) {
                return MessagePlugin.warning({
                    content: i18nt('activity.activity.hasDelete'),
                    placement: "center"
                });
                }
                const row = data;
                const msgStore = useMessageStore();
                // console.error('msgStore', msgStore.chatingSession.localSessionId);
                // console.error('msgStore', msgStore.allMembers.get(msgStore.chatingSession.localSessionId));

                const item = msgStore.chatingSessionMembers?.get(msgStore.chatingSession.myCardId);
                console.log(item);
                console.log(data);
                ipcRenderer.invoke('create-dialog', { url: `layoutActivity/activityParticipantDetailLayout/${row.id}?subject=${encodeURIComponent(row.subject)}
                  &teamId=${encodeURIComponent(item.teamId)}&cardId=${encodeURIComponent(item.cardId)}&type=aloneWindow`, opts: { x: 50, y: 50, width: 1296, minWidth: 1296, height: 720, }, });
            };
            // 刷新回调
            const refreshCb = () => {
                console.log('refreshCb');
                getData(msg);
            };

            const valueDisplay = (h, { value }) => (['Denied', 'Confirmed']?.includes(value) ? `已${textList?.find((v) => v.value === value)?.label}` : textList?.find((v) => v.value === value)?.label);

            const suffixIcon = () => (PopupVisible.visible ? <ChevronUpIcon /> : <ChevronDownIcon />);

            return (
                <AppCard style="position:relative;width:360px;" cardHeight={'475px'} isLoading={isLoading.value} isLoadError={isLoadError.value} refreshCb={refreshCb} >
                    <AppCardHeader style="font-size: 16px;font-weight: 400;line-height: 24px;color: var(--brand-kyy_color_brand_default, #4D5EFF);">
                        { (isDeleted ? data.category_title : data?.apiData?.activity?.categoryTitle) || i18nt('im.tools.activity') }
                    </AppCardHeader>
                    <AppCardBody onClick={() => (isDeleted ? onClickDetail() : onClickDetailShare())}>
                        <img src={isDeleted ? data.assetUrl : data?.apiData?.activity?.assetUrl} style="width:100%;object-fit:cover;object-position:center;height:185px;" />
                        <AppCardText type='main' style='margin: 8px 0;width:100%;display:flex;gap:8px;' >
                        <div className={'line-2'} style="font-size: 14px;font-weight: 600;color: var(--text-kyy-color-text-1, #1A2139);">{ (isDeleted ? data.subject : data?.apiData?.activity?.subject) || '--' }</div>
                        </AppCardText>
                        <AppCardText type='info' style='margin: 8px 0;width:100%;font-size: 14px;line-height: 22px;' class="flex">
                          <span style="margin-right: 16px;color: var(--text-kyy-color-text-3, #828DA5);">{ i18nt('im.msg.activityTime') }</span>
                          <span style="color: var(--text-kyy-color-text-1, #1A2139);">
                              {isDeleted
                              ? (data?.duration?.startTime ? `${moment.unix(data.duration.startTime).format('MM-DD HH:mm')} ~ ${moment.unix(data.duration.endTime).format('MM-DD HH:mm')}` : '--')
                              : ((data?.apiData?.activity?.duration?.startTime) ? `${moment.unix(data?.apiData?.activity?.duration.startTime).format('MM-DD HH:mm')} ~ ${moment.unix(data?.apiData?.activity?.duration.endTime).format('MM-DD HH:mm')}` : '--')
                              }
                          </span>
                        </AppCardText>
                        <AppCardText type='info' style='margin: 8px 0;width:100%;font-size: 14px;line-height: 22px;display:flex;margin-bottom: 0'>
                        <span style="margin-right: 16px;color: var(--text-kyy-color-text-3, #828DA5);word-break: keep-all;">
                            { i18nt('im.msg.activityAddr') }
                        </span>
                        <span className={'line-2'} style="color: var(--text-kyy-color-text-1, #1A2139);">
                            {isDeleted
                             ? (data?.location?.title || data?.location?.address || '--')
                             : (data?.apiData?.activity?.location?.title || data?.apiData?.activity?.location?.address || '--')
                            }
                        </span>
                        </AppCardText>
                    </AppCardBody>
                    <AppCardFooter style="justify-content: start;padding:16px 0 0 0;margin: 0 16px 16px 16px;border-top: 1px solid #ECEFF5;gap:8px;">
                        {
                        showStatusBtn && !isUnknown && !showActivityStatus
                         ? (<AppCardBody style='padding:0;'>
                            <t-select v-replace-svg suffixIcon={suffixIcon} valueDisplay={valueDisplay} value={data.apiData.reply} onPopupVisibleChange={(visible) => PopupVisible.visible = visible} onChange={(e) => changeActivityRes(e)} className='AppActivityEleSelect'>
                                {textList.map((item) => (
                                    <t-option value={item.value} label={item.label} key={item.value}></t-option>
                                ))}
                            </t-select>
                         </AppCardBody>)
                          : null
                        }
                        { showStatusBtn && isUnknown && !showActivityStatus && !showNoJoin ? <AppCardBtn text={i18nt('im.msg.refuse')} theme={data?.apiData?.reply === 'Denied' ? 'primary_background' : 'default_background'} style="min-width: 80px;color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);font-size: 14px;border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);" onClick={() => activityRes(3)} /> : null }
                        { showStatusBtn && isUnknown && !showActivityStatus && !showNoJoin ? <AppCardBtn text={i18nt('im.msg.pending')} theme={data?.apiData?.reply === 'Received' ? 'primary_background' : 'default_background'} style="min-width: 80px;color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);font-size: 14px;border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);" onClick={() => activityRes(2)} /> : null }
                        { showStatusBtn && isUnknown && !showActivityStatus && !showNoJoin ? <AppCardBtn text={i18nt('im.msg.join')} theme={data?.apiData?.reply === 'Confirmed' ? 'primary_background' : 'default_background'} style="min-width: 80px;color: var(--color-button_primary-kyy_color_button_primary_text, #FFF);font-size: 14px;background-color: var(--color-button_primary-kyy_color_button_primary_bg_default, #4D5EFF);" onClick={() => activityRes(1)} /> : null }
                        { showNoJoin ? <AppCardBtn text={i18nt('im.msg.joinTip1')} theme="disable_background" style="cursor: not-allowed;" /> : null }
                        {
                          !showStatusBtn && !showNoJoin && isDeleted ?
                            <AppCardBtn text={isDeleted ? i18nt('square.post.deleted1') : activityStatusObj.value.text} theme="disable_background" style="cursor: not-allowed;color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);font-size: 14px;font-weight: 400;line-height: 22px;padding:0;border:none;min-width:auto;" />
                          : null
                        }
                        { !showStatusBtn && !showNoJoin && !isDeleted && !showActivityStatus ? <AppCardBtn class="w-full" text={i18nt('im.public.detail')} style="color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);font-size: 14px;font-weight: 600;line-height: 22px;border-radius: var(--radius-kyy_radius_button_s, 4px);border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);padding:5px 16px;" onClick={() => (isAssistant ? onClickDetail() : onClickDetailShare())} /> : null }
                        { showActivityStatus && !showNoJoin ? <AppCardBtn text={isDeleted ? i18nt('square.post.deleted1') : activityStatusObj.value.text} theme="disable_background" style="cursor: not-allowed;color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);font-size: 14px;font-weight: 400;line-height: 22px;padding:0;border:none;min-width:auto;" /> : null }
                    </AppCardFooter>
                </AppCard>
            );
        };
    }
});

