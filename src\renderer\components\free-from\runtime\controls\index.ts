import { defineAsyncComponent } from "vue";

const FInput = defineAsyncComponent(() => import("./FInput.vue"));
const FTextArea = defineAsyncComponent(() => import("./FTextArea.vue"));
const FNote = defineAsyncComponent(() => import("./FNote.vue"));
const FNumberInput = defineAsyncComponent(() => import("./FNumberInput.vue"));
const FMoneyInput = defineAsyncComponent(() => import("./FMoneyInput.vue"));
const FCalculator = defineAsyncComponent(() => import("./FCalculator.vue"));
const FSelect = defineAsyncComponent(() => import("./FSelect.vue"));
const FMultiSelect = defineAsyncComponent(() => import("./FMultiSelect.vue"));
const FDatePicker = defineAsyncComponent(() => import("./FDatePicker.vue"));
const FDateRangePicker = defineAsyncComponent(
  () => import("./FDateRangePicker.vue")
);
const FFileUpload = defineAsyncComponent(() => import("./FFileUpload.vue"));
const FFileUploadDown = defineAsyncComponent(
  () => import("./FFileUploadDown.vue")
);
const FImageUpload = defineAsyncComponent(() => import("./FImageUpload.vue"));
const FContact = defineAsyncComponent(() => import("./FContact.vue"));
const FDepartment = defineAsyncComponent(() => import("./FDepartment.vue"));
const FAddress = defineAsyncComponent(() => import("./FAddress.vue"));
const FRelativeProcedure = defineAsyncComponent(
  () => import("./FRelativeProcedure.vue")
);
const FFieldList = defineAsyncComponent(() => import("./FFieldList.vue"));
const FCustomer = defineAsyncComponent(() => import("./FCustomer.vue"));
const FEmail = defineAsyncComponent(() => import("./FEmail.vue"));
const FPhone = defineAsyncComponent(() => import("./FPhone.vue"));
const FSuperContact = defineAsyncComponent(() => import("./FSuperContact.vue"));
const FRadio = defineAsyncComponent(() => import("./FRadio.vue"));

const FDivider = defineAsyncComponent(() => import("./FDivider.vue"));
const FBaseInfoMember = defineAsyncComponent(
  () => import("./FBaseInfoMember.vue")
);

const FBaseInfoPolitics = defineAsyncComponent(
  () => import("./FBaseInfoPolitics.vue")
);

const FBaseInfoAdminMember = defineAsyncComponent(
  () => import("./FBaseInfoAdminMember.vue")
);

export const Runtimes = {
  Input: FInput,
  TextArea: FTextArea,
  Note: FNote,
  NumberInput: FNumberInput,
  MoneyInput: FMoneyInput,
  Calculator: FCalculator,
  Select: FSelect,
  MultiSelect: FMultiSelect,
  DatePicker: FDatePicker,
  DateRangePicker: FDateRangePicker,
  FileUpload: FFileUpload,
  FileUploadDown: FFileUploadDown,
  ImageUpload: FImageUpload,
  Contact: FContact,
  Department: FDepartment,
  Address: FAddress,
  RelativeProcedure: FRelativeProcedure,
  FieldList: FFieldList,
  Customer: FCustomer,
  Email: FEmail,
  Phone: FPhone,
  Radio: FRadio,
  BaseInfoMember: FBaseInfoMember,
  BaseInfoPolitics: FBaseInfoPolitics,
  Divider: FDivider,
  SuperContact: FSuperContact,
  BaseInfoAdminMember: FBaseInfoAdminMember,
};
