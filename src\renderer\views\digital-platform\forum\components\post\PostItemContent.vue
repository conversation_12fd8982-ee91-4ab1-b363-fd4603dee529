<template>
  <div>
    <div class="text" @click="clickContent">
      <TextEllipsis
        :key="ellipsisKey"
        :highlight="true"
        :keyword="keyword"
        :content="postText"
        :rows="5"
        :expand-text="$t('square.fullContent')"
        :collapse-text="$t('square.action.fold')"
        :atInfo="postItem.post.at"
      />
    </div>

    <div v-if="imageUrls.length || videoUrl" class="attachment" @click="clickContent">
      <VideoDisplay
        v-if="post.postType === PostType.Video && videoUrl"
        :video="videoUrl"
        :large="!inList"
        :post="postItem.post"
        forum
      />
      <div v-else>
        <span><ImageGrid :list="imageUrls" :post="postItem.post" forum /></span>
      </div>
    </div>

    <TopicSelectItem v-if="postItem.topics?.length" :data="postItem.topics[0]" readonly class="mt-12" />

    <!--工具栏-->
    <div ref="toolbar" class="toolbar">
      <div
        :class="['item', { active: postLiked, selected: toolbarSelected === 'like' && !inList }]"
        @click.stop="action('like')"
      >
        <!-- <div v-show="isAnimating" ref="likeIconRef" class="w-20 h-20" />
        <template v-if="!isAnimating">
          <iconpark-icon v-if="postLiked" name="iconlikefill2" class="icon icon-liked" />
          <iconpark-icon v-else name="iconlike" class="icon" />
        </template> -->
        <LikeAnimated :liked="postLiked" />
        {{ statsFormat.likes || $t('square.action.like') }}
      </div>

      <div
        :class="['item', { selected: toolbarSelected === 'comment' && !inList, 'in-list': inList }]"
        @click.stop="action('comment')"
      >
        <iconpark-icon name="reply" class="icon" />{{ statsFormat.comments || $t('square.post.comment') }}
      </div>
      <div class="item item-right" v-if="isOpenJoinFriend">
        开放加好友
        <t-switch v-model="checkedFriend" @change="onChange" />
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { computed, nextTick, onMounted, ref, watch, inject } from 'vue';
import to from 'await-to-js';
import { likeToggle } from '@renderer/api/forum/comment';
import { updatePostFriendFlag } from '@/api/forum/post';
import { ResourceType } from '@renderer/api/forum/models/comment';
import { Flag, Post, PostType } from '@renderer/api/forum/models/post';
import { formatNumber } from '@renderer/utils/assist';
import LikeAnimated from '@/components/common/like-animated/index.vue';
import ImageGrid from '@/views/square/components/ImageGrid.vue';
import TextEllipsis from '@/components/TextEllipse.vue';
import { entitiesToUtf16 } from '@/views/square/utils';
import VideoDisplay from '@/views/square/components/VideoDisplay.vue';
import { getImCardIds } from '@/utils/auth';
import TopicSelectItem from '../TopicSelectItem.vue';
import { useForumStore } from '../../store';
import { MessagePlugin } from 'tdesign-vue-next';
import { PageTypeMap } from '../../constant';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  toolbarActive: {
    type: String,
    default: '',
  },
  keyword: {
    type: String,
    default: '',
  },
  inList: Boolean,
  scrollToInput: Boolean,
  // square: Object,
  owner: Object,
  teamId: String,
  fromOuter: Boolean,
});

const emit = defineEmits(['click-content', 'toolbar-change', 'removed', 'refresh-item', 'count-change']);

const showJoinFriend = inject(PageTypeMap.Home);
const showTopic = inject(PageTypeMap.Topic);
// 加好友开关
const checkedFriend = ref(props.data?.post?.friendFlag || false);
const onChange = async (val) => {
  const [err] = await to(
    updatePostFriendFlag({
      postId: props.data.post.id,
      ownerId: props.data?.owner?.id,
      friendFlag: val,
    }),
  );
  if (err) {
    MessagePlugin.error(err.message);
    checkedFriend.value = !val;
    return;
  }
  if (val) {
    MessagePlugin.success('开放加好友成功！');
  } else {
    MessagePlugin.success('关闭加好友成功！');
  }
};

const forumStore = useForumStore();
const postItem = computed(() => props.data);
const post = computed(() => postItem.value?.post || ({} as Post));
const isSelfPost = computed(() => forumStore.currCard?.openid === props.owner?.openid);
const postText = computed(() => {
  const text = post.value.text;
  if (!text) return '';
  return entitiesToUtf16(text);
});
const toolbarSelected = ref(props.toolbarActive);
const ellipsisKey = ref(0);

// 开放加好友
const isOpenJoinFriend = computed(() => {
  return (
    (showJoinFriend || showTopic) &&
    forumStore.platformType === 'uni' &&
    postItem.value.owner.digitalPlatformFlag !== Flag.Anonymous
  );
});

const postLiked = ref(props.data.liked);
watch(
  () => postItem.value.liked,
  (val) => {
    postLiked.value = val;
  },
  { immediate: true },
);

// 点赞数和评论数统计
const statsFormat = computed(() => {
  const { likes, comments } = postItem.value.post || {};
  return {
    likes: formatNumber(likes),
    comments: formatNumber(comments),
  };
});

// 图片或视频链接
const imageUrls = computed(() => (post.value?.content?.objects || []).map((v) => v.url));
const videoUrl = computed(() => (post.value?.content?.objects || [])[0]?.url);

// 操作
const action = async (type) => {
  // if (!await checkStatus(type)) return;

  toolbarSelected.value = type;
  // toolbarActive.value = getToolbarActive();

  if (type === 'like') {
    const [err, res] = await to(
      likeToggle({
        resourceId: post.value.id,
        resourceType: ResourceType.Post,
        ownerId: props.owner?.id,
      }),
    );

    if (!err) {
      // const isLiked = res.data.data.like;
      const { like, likes } = res.data.data;
      // 避免赞为负数（详情点赞，列表中取消）
      // if (postLiked.value === isLiked) return;

      // const likes = postItem.value.post.likes;
      // postItem.value.post.likes = Math.max(likes + (isLiked ? 1 : -1), 0);
      postItem.value.post.likes = likes;
      postLiked.value = like;

      // 给自已动态点赞/取消点赞时，列表中需相应显示或取消显示自已的头像
      // const idx = selfLikes.value.findIndex((v) => v.ownerId === forumStore.ownerId);
      // if (postLiked.value) {
      //   idx === -1 && selfLikes.value.unshift(forumStore.currCard);
      // } else {
      //   idx > -1 && selfLikes.value.splice(idx, 1);
      // }
    }

    emit('count-change', { liked: postLiked.value });

    // 只能看自己的点赞列表
    if (isSelfPost.value) {
      emit('toolbar-change', type);
    }
  } else {
    emit('toolbar-change', type);
  }
};

const clickContent = async () => {
  // if (!await checkStatus()) return;

  emit('click-content', postItem.value);
};

const toolbar = ref(null);
const disableList = ref([]);

const delay = (ms) =>
  new Promise((resolve) => {
    setTimeout(resolve, ms);
  });

onMounted(() => {
  disableList.value = getImCardIds();

  nextTick(async () => {
    // 延迟计算，避免折行计算错误
    setTimeout(() => {
      ellipsisKey.value++;
    }, 100);

    if (['comment', 'forward'].includes(toolbarSelected.value) && props.scrollToInput) {
      const scrollToElement = () => {
        toolbar.value.scrollIntoView({ behavior: 'smooth', block: 'start' });
      };

      await delay(600);
      scrollToElement();

      await delay(600);
      scrollToElement();
    }
  });
});
</script>

<style lang="less" scoped>
.text {
  margin-top: 12px;
  // margin-bottom: 16px;
  font-size: 14px;
  font-weight: 400;
  color: var(--text-kyy_color_text_1, #1a2139);
  line-height: 22px; /* 157.143% */
  cursor: pointer;
}

.attachment {
  margin-top: 8px;
  cursor: pointer;
}

.toolbar {
  //padding-left: 4px;
  margin-left: -11px;
  margin-top: 12px;
  display: flex;
  color: var(--text-kyy_color_text_2, #516082);
  .item {
    display: flex;
    align-items: center;
    width: 160px;
    cursor: pointer;
    gap: 4px;
    padding: 5px 12px;
    color: var(--text-kyy_color_text_2, #516082);
    border-radius: 8px;
    user-select: none;
    .icon {
      border-radius: 4px;
      font-size: 20px;
      color: var(--text-kyy-color-text-3, #828da5);
    }
    &:hover {
      // color: #1f60da;
      background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
    }
    &.item-empty {
      background: transparent;
    }
    &.selected {
      color: var(--brand-kyy-color-brand-default, #4d5eff);
      .icon {
        color: var(--brand-kyy-color-brand-default, #4d5eff);
      }
    }
    &.disabled {
      color: var(--text-kyy_color_text_5, #acb3c0);
      .icon {
        color: var(--text-kyy_color_text_5, #acb3c0);
      }
    }
    // &.in-list {
    //   .icon {
    //     color: var(--brand-kyy-color-brand-default, #4D5EFF);
    //   }
    // }
    &.share {
      margin-right: 0;
      flex: 1;
    }
    .icon-liked {
      color: #d54941 !important;
    }
  }
  .item-right {
    width: auto !important;
    padding-right: 0 !important;
    gap: 8px !important;
    &:hover {
      background: none !important;
    }
    margin-left: auto;
  }
}

.video-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 314px;
  border-radius: 8px;
  margin-right: 8px;
  margin-bottom: 8px;
  border: 1px solid #e3e6eb;
  position: relative;
  cursor: pointer;
  .img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
  }
  .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    z-index: 1;
  }
}
</style>
