<template>
  <div class="page-container">
    <div class="page-content" :class="[mode]">
      <!-- 图文模式 -->
      <template v-if="PostType.Picture === mode">
        <!-- 动态内容 -->
        <div class="search-box">
          <t-textarea
            ref="inputRef"
            v-model="formData.post.text"
            class="editor-text"
            :placeholder="$t('square.post.postPlaceholder')"
            :maxlength="500"
          />
        </div>

        <!--上传图片-->
        <Draggable
          v-if="formData.post.postType === PostType.Picture"
          item-key="index"
          :list="images"
          :animation="400"
          group="imagesGroup"
          class="uploader"
        >
          <template #item="{ element, index }">
            <div :key="element.flag" class="img-wrap sortable">
              <t-image
                v-if="!element.loading"
                :src="element.previewUrl || `${element.url}?x-oss-process=image/resize,m_fill,w_400,quality,q_60`"
                :srcset="{
                  'image/webp': element.previewUrl || `${element.url}?x-oss-process=image/resize,m_fill,w_400/format,webp/quality,q_60`,
                }"
                fit="cover"
                class="img"
              >
                <template #loading>
                  <div class="img-wrap sortable flex-col loading">
                    <iconpark-icon name="iconimg" class="icon" />
                    <p class="color-text-2">加载中</p>
                    <iconpark-icon name="iconcleantransparent1" class="btn-close" @click="removeImage(index)" />
                  </div>
                </template>
              </t-image>
              <div v-else class="img-wrap sortable flex-col loading">
                <t-loading />
                <p class="color-text-2 progress-loading">{{ element.progress || '0' }}%</p>
                <iconpark-icon name="iconcleantransparent1" class="btn-close" @click="removeImage(index)" />
              </div>

              <iconpark-icon name="iconcleantransparent1" class="btn-close" @click="removeImage(index)" />
            </div>
          </template>
          <template #footer>
            <ImageUpload
              v-show="getIsAddPicture"
              v-model="images"
              class="flex"
              :add="true"
              @success="uploadSuccess($event, PostType.Picture)"
              @upload-file="onUploadImageFile"
            >
              <div class="img-wrap btn-upload flex-col">
                <iconpark-icon name="iconimg" class="icon" />
                <p class="color-text-2">点击上传</p>
              </div>
            </ImageUpload>
          </template>
        </Draggable>

        <!-- 卡片 -->
        <div class="card-container">
          <PostAlbumCard
            v-if="isAlbum"
            :node-id="nodeId"
            :album-node="formData.post.albumNode"
            class="mb-12!"
            @confirm="(val) => (formData.post.albumNode = val)"
          />

          <TeamHonorRollCard v-if="isTeamHonorRoll" v-bind="extraData" class="mb-12!" />

          <TeamIntroCard
            v-if="isTeamIntro"
            v-bind="extraData"
            class="mb-12!"
            @confirm="setCardData"
          />

          <TeamHistoryCard v-if="isTeamHistory" v-bind="extraData" class="mb-12!" />

          <PartyBuildingCard v-if="isPartyBuilding" v-bind="extraData" class="mb-12!" />

          <FengcaiCard v-if="isFengcai" v-bind="extraData" class="mb-12!" />
        </div>

        <LocationDisplay :location="formData.post.location" @remove="delete formData.post.location" @click="mapVisible = true" />
      </template>

      <!-- 视频模式 -->
      <template v-if="PostType.Video === mode">
        <div class="video-container" :class="{ init: videoState.isInitState }">
          <!-- 视频已上传 -->
          <div v-if="videoState.isVideoUploaded" class="video-display-wrap">
            <VideoDisplay
              :video="video.url"
              :large="false"
              class="w-336!"
              @remove="rmVideo"
            />

            <t-button theme="default" class="w-116" @click="triggerUpload">更换视频</t-button>
          </div>

          <!-- 未上传视频、上传中或取消上传 -->
          <div
            v-show="videoState.isUploadingOrCancel"
            class="video-uploader"
            :class="videoState.uploadCls"
          >
            <VideoUpload
              ref="videoUploadRef"
              v-model="videos"
              type="video"
              class="flex"
              :disabled="video.status === 'loading'"
              draggable
              @success="uploadSuccess($event, PostType.Video)"
              @changed="onChangedVideo"
              @upload-file="onUploadVideoFile"
              @dragenter="isUploadHover = $event"
            >
              <img src="@/assets/square/video-upload.png" alt="" class="img">

              <template v-if="video.status === 'loading'">
                <t-progress :percentage="videoProgress" />
              </template>
              <p v-else>{{ isUploadCancel ? '点击 上传视频' : '点击上传 或 直接将视频拖入此区域' }}</p>
            </VideoUpload>

            <iconpark-icon
              v-if="video.status === 'loading'"
              name="icon20delet"
              class="btn-close"
              @click="rmVideo"
            />
          </div>

          <LocationDisplay :location="videoLocation" @remove="videoLocation.value = null" @click="mapVisible = true" />

          <t-form
            v-if="videoState.showForm"
            ref="formRef"
            :rules="formRules"
            :data="formData"
            label-align="top"
            show-error-message
            class="post-publish-form"
          >
            <t-form-item label="动态内容" name="post.text">
              <div class="search-box">
                <t-textarea
                  ref="inputRef"
                  v-model="videoText"
                  class="editor-text"
                  :autosize="{ minRows: 3, maxRows: 8 }"
                  :placeholder="$t('square.post.postPlaceholder')"
                  :maxlength="500"
                />
              </div>
            </t-form-item>

            <t-form-item label="视频标签" name="videoLabel">
              <LabelSelect v-model="videoLabelId" @change="handleLabelChange" />
            </t-form-item>

            <t-form-item
              v-if="!store.isPersonal"
              label="添加商品"
              name="post.product"
              class="mb-0!"
            >
              <div class="flex-col gap-8 w-full">
                <div class="flex gap-8">
                  <t-select v-model="formData.post.product.type" class="w-100! shrink-0" @change="changeProductType">
                    <t-option label="商机" :value="ProductType.Niche" />
                    <t-option label="其它链接" :value="ProductType.URL" />
                  </t-select>

                  <t-select
                    v-if="formData.post.product.type === ProductType.Niche"
                    v-model="formData.post.product.niche.uuid"
                    class="flex-1"
                    :options="nicheOptions"
                    :popup-visible="false"
                    placeholder="请选择商机"
                    clearable
                    @click="toSelectNiche"
                  />
                  <t-form-item
                    v-else
                    name="post.product.url"
                    label-width="0"
                    class="flex-1"
                  >
                    <t-input
                      v-model="formData.post.product.url"
                      class="flex-1"
                      placeholder="请输入或粘贴链接"
                      :maxlength="500"
                    />
                  </t-form-item>
                </div>

                <t-input
                  v-model="formData.post.product.title"
                  placeholder="请选择商品后输入标题"
                  show-limit-number
                  :disabled="productTitleDisabled"
                  :maxlength="16"
                />
              </div>
            </t-form-item>
          </t-form>
        </div>
      </template>
    </div>

    <!-- 工具栏 -->
    <div v-if="showToolbar" class="toolbar">
      <div class="attachment">
        <!-- 临时方案 -->
        <EmojiSelector
          v-if="PostType.Video === mode"
          v-model="emojiVisible"
          :disabled="postText.length >= 500"
          @select="onSelectEmojiRef"
        />
        <EmojiSelector
          v-else
          v-model="emojiVisible"
          :disabled="postText.length >= 500"
          @select="onSelectEmoji"
        />

        <template v-if="!props.onlyText">
          <template v-if="actionsVisible.img">
            <div v-if="PostType.Video === mode || isAlbum" class="disabled-btn">
              <span :class="['item', { disabled: PostType.Video === mode || isAlbum }]">
                <iconpark-icon name="icon24img" class="icon" />
              </span>
            </div>
            <t-tooltip v-else content="图片">
              <div>
                <ImageUpload
                  v-model="images"
                  class="flex"
                  :add="true"
                  :disabled="PostType.Video === mode || isAlbum"
                  @success="uploadSuccess($event, PostType.Picture)"
                  @upload-file="onUploadImageFile"
                >
                  <span :class="['item', { disabled: PostType.Video === mode || isAlbum }]">
                    <iconpark-icon name="icon24img" class="icon" />
                  </span>
                </ImageUpload>
              </div>
            </t-tooltip>
          </template>

          <t-tooltip content="文章">
            <span v-if="actionsVisible.article" class="item" @click="action(PostType.Article)">
              <iconpark-icon name="iconlink" class="icon" />
            </span>
          </t-tooltip>

          <t-tooltip content="定位">
            <span class="item" @click="mapVisible = true">
              <iconpark-icon name="icon24local" class="icon" />
            </span>
          </t-tooltip>
        </template>

        <t-tooltip v-if="showMap" content="定位">
          <span class="item" @click="mapVisible = true">
            <iconpark-icon name="icon24local" class="icon" />
          </span>
        </t-tooltip>
        <!-- <MapSelector v-if="mapVisible" v-model="mapVisible" @confirm="addressConfirm" /> -->
        <BaiduMapSelector v-if="mapVisible" v-model:visible="mapVisible" @confirm="addressConfirm" />
      </div>

      <div class="right">
        <span class="tool-item" @click="toolItemClick('allowComment')">
          <span class="mr-4">{{ $t('square.post.comment') }}</span>
          <iconpark-icon v-if="formData.post.allowComment" name="iconlockopen" class="icon" />
          <iconpark-icon v-else name="iconlockclose" class="icon" />
          <!--<t-switch v-model="formData.post.allowComment" size="small" />-->
        </span>

        <span
          :class="['tool-item', { disabled: Visibility.PUBLIC !== formData.post.visibility }]"
          @click="toolItemClick('allowShare')"
        >
          <span class="mr-4">{{ $t('square.post.shareForward') }}</span>
          <iconpark-icon v-if="formData.post.allowShare" name="iconlockopen" class="icon" />
          <iconpark-icon v-else name="iconlockclose" class="icon" />
          <!--<t-switch v-model="formData.post.allowShare" size="small" :disabled="Visibility.PUBLIC !== formData.post.visibility" />-->
        </span>

        <!--可见性-->
        <VisibilitySelector
          v-model="formData.post.visibility"
          v-model:fans-group-id="formData.post.fansGroupId"
          placement="bottom-right"
          :include="visibilityIncludes"
          :group-names="groupNames"
          :team-id="teamId"
          @confirm="visibilityConfirm"
        >
          <template #default="{ currentType }">
            <div class="mr-8 btn-selector">
              {{ currentType.simpleContent }}
              <t-icon name="chevron-down" size="16" class="icon" />
            </div>
          </template>
        </VisibilitySelector>

        <t-button
          :disabled="!canPublish"
          :loading="submitting"
          class="publish-btn"
          size="small"
          @click="publish"
        >
          <template #icon><iconpark-icon name="iconrelease" class="text-16!" />&nbsp;</template>
          {{ $t('square.post.publish') }}
        </t-button>
      </div>
    </div>

    <!-- 选择商机 -->
    <NicheSelector v-model="nicheVisible" :default-value="formData.post.product.niche.uuid" @confirm="nicheConfirm" />
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch, onUnmounted } from 'vue';
import to from 'await-to-js';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import merge from 'lodash/merge';
import { AxiosError, AxiosResponse } from 'axios';
import { useRouter } from 'vue-router';
import Draggable from 'vuedraggable';
import { useI18n } from 'vue-i18n';
import { useNetwork } from '@vueuse/core';
import { getCertStatus } from '@renderer/api/square/square';
import { OrganizationCertStatus, SquareType } from '@renderer/api/square/enums';
import { useTabsStore } from '@renderer/components/page-header/store';
import BaiduMapSelector from '@renderer/components/common/map/BaiduMapSelector.vue';
import { isValidUrl } from '@renderer/utils/validator';
import { PostType, postTypeFieldMap, Visibility } from '@/views/square/constant';
import EmojiSelector from '@/views/square/components/EmojiSelector.vue';
import VisibilitySelector from '@/views/square/components/VisibilitySelector.vue';
import { useSquareStore } from '@/views/square/store/square';
import { useImageUpload, useVideoUpload } from '@/views/square/hooks/upload';
import { PostContentTemplateA, PostRequestData, PostStatus, ProductType } from '@/api/square/models/post';
import { addDraft, postPublish, publishDraft, updateDraft } from '@/api/square/post';
import useEmoji, { useEmojiInput } from '@/views/square/hooks/emoji';
import ImageUpload from '@/views/square/components/post/ImageUpload.vue';
import VideoUpload from '@/views/square/components/post/VideoUpload.vue';
import VideoDisplay from '@/views/square/components/VideoDisplay.vue';
import { DraftReason } from '@/views/square/enums';
import PostAlbumCard from '@/views/square/components/post/album/PostAlbumCard.vue';
import TeamHonorRollCard from '@/views/square/components/post/card/TeamHonorRoll.vue';
import TeamIntroCard from '@/views/square/components/post/card/TeamIntro.vue';
import TeamHistoryCard from '@/views/square/components/post/card/TeamHistory.vue';
import PartyBuildingCard from '@/views/square/components/post/card/PartyBuilding.vue';
import FengcaiCard from '@/views/square/components/post/card/Fengcai.vue';
import NicheSelector from '@/views/square/components/NicheSelector.vue';
import LabelSelect from '@/views/square/components/label/LabelSelect.vue';
import LocationDisplay from '@/views/square/components/post/publish/LocationDisplay.vue';
import { isPostCard } from '../../utils/business';
import { usePostProduct } from './hooks/usePostPublish';
import { createThumbnail } from '@/utils/file';

const props = withDefaults(
  defineProps<{
    visible?: boolean;
    defaultValue?: Record<string, any>;
    // 不显示其它类型的发布按钮
    onlyText?: boolean;
    source?: string;
    // 指定上传类型（例：相册节点时传 PostType.AlbumNode）
    type?: PostType;
    // 发布模式：图文/视频
    mode?: PostType.Picture | PostType.Video;

    // 是否可发布文章
    showArticle?: boolean;
    // 是否可上传图片
    showImg?: boolean;
    // 是否可上传视频
    showVideo?: boolean;
    // 是否显示定位
    showMap?: boolean;
    // 额外数据（用于卡片）
    extraData?: PostContentTemplateA;

    // 相册节点id, 相册节点时必传
    nodeId?: string;
    // 相册节点数据，用于从编辑相册节点动态
    albumNode?: Record<string, any>;

    // 指定以什么组织身份发布动态（传 -1 表示以个人身份发布动态）
    teamId?: string;
  }>(),
  {
    mode: PostType.Picture,
    showArticle: false,
    showImg: true,
    showVideo: true,
  },
);

const emit = defineEmits(['submit', 'error', 'close-dialog', 'certified-gov']);

const { t } = useI18n();
const router = useRouter();
const store = useSquareStore();
const tabStore = useTabsStore();

const { images, removeImage } = useImageUpload();
const { videos, removeVideo } = useVideoUpload();
const video = computed(() => (videos.value?.length ? videos.value[0] : ''));
const isRemoveVideo = ref(false);
const draftSaved = ref(false);

const visibilityIncludes = computed(() => {
  // 个人
  if (store.isPersonal || !props.teamId || props.teamId === '-1') return [];

  // 组织
  return [Visibility.PUBLIC, Visibility.PRIVATE];
});

const isAlbum = computed(() => props.type === PostType.AlbumNode);
const isTeamHonorRoll = computed(() => props.type === PostType.TeamHonorRoll);
const isTeamIntro = computed(() => props.type === PostType.TeamIntro);
const isTeamHistory = computed(() => props.type === PostType.TeamHistory);
const isPartyBuilding = computed(() => props.type === PostType.PartyBuilding);
const isFengcai = computed(() => props.type === PostType.Fengcai);

// 控制操作是否显示
const actionsVisible = computed(() => {
  const isCard = [
    PostType.TeamHonorRoll,
    PostType.TeamIntro,
    PostType.TeamHistory,
    PostType.PartyBuilding,
    PostType.Fengcai,
  ].includes(props.type);
  return {
    article: props.showArticle && !isCard && props.mode === PostType.Picture,
    img: props.showImg && !isCard && props.mode === PostType.Picture,
    video: props.showVideo && !isCard && props.mode === PostType.Video,
  };
});

// 隔离视频/图文的文本、位置数据
const videoText = ref('');
const videoLocation = ref(null);
const postText = computed(() => (PostType.Video === props.mode ? videoText.value : formData.post.text));
const postLocation = computed(() => (PostType.Video === props.mode ? videoLocation.value : formData.post.location));

const processedVideo = computed(() => (video.value && typeof video.value === 'object' ? video.value : { url: '', status: '' }));

// 是否显示工具栏
const showToolbar = computed(() => {
  const { url, status } = processedVideo.value;
  return !(PostType.Video === props.mode && !url && status !== 'loading') || isUploadCancel.value;
});

// 视频状态
const videoState = computed(() => {
  const { url, status } = processedVideo.value;
  const loading = status === 'loading';

  return {
    // 初始化状态
    isInitState: !url && !loading && !isUploadCancel.value,
    // 已上传视频
    isVideoUploaded: url && !loading && !isUploadCancel.value,
    // 未上传视频、上传中或取消上传
    isUploadingOrCancel: !url || loading || isUploadCancel.value,
    uploadCls: {
      loading,
      cancel: isUploadCancel.value,
      hover: isUploadHover.value,
    },
    // 是否显示视频表单
    showForm: url || loading || isUploadCancel.value,
  };
});

const uploadSuccess = (ctx, type) => {
  if (!isRemoveVideo.value && type === PostType.Video) {
    videos.value = ctx.fileList;
    isUploadCancel.value = false;
  }
  action(type);
};

const getIsAddPicture = computed(() => images.value.length > 0 && images.value.length < 9);

const onUploadImageFile = async (e) => {
  const index = images.value.findIndex((item) => item.flag === e.flag);
  if (index > -1 && images.value[index]) {
    if (e.file) {
      await createPreviewUrl(e);
    }

    images.value[index] = e;
  }
};

// 为上传的文件创建本地预览 URL
const createPreviewUrl = async (e) => {
  try {
    // 生成缩略图
    const blob = await createThumbnail(e.file);
    // 创建预览URL
    e.previewUrl = URL.createObjectURL(blob);
  } catch (error) {
    console.error('生成缩略图失败:', error);
    // 如果生成缩略图失败,使用原始文件
    e.previewUrl = URL.createObjectURL(e.file);
  }
};

const isUploadHover = ref(false);
const videoProgress = ref(0);
const onUploadVideoFile = (e, client) => {
  videoProgress.value = e.progress;

  if (isRemoveVideo.value) {
    videos.value = [];
    client.abortMultipartUpload();
  } else {
    videos.value = [e];
  }
};

const onChangedVideo = () => {
  isRemoveVideo.value = false;
  formData.post.postType = PostType.Video;
};

// 视频上传被取消
const isUploadCancel = ref(false);

// 移除视频或取消上传
const rmVideo = () => {
  if (video.value && video.value.status === 'loading') {
    isUploadCancel.value = true;
  }

  isRemoveVideo.value = true;
  removeVideo();
  formData.post.video = '';
};

// 删除图片或视频，切换回文本模式
watch(
  () => images.value,
  (val) => {
    if (val.length === 0) formData.post.postType = PostType.Text;
    else if (val.length > 0) formData.post.postType = PostType.Picture;
  },
  { deep: true },
);

watch(
  () => videos.value,
  (val) => {
    if (val && val.filter((item) => item.url || item.loading).length === 0) formData.post.postType = PostType.Text;
    else if (val.length > 0) formData.post.postType = PostType.Video;
  },
  { deep: true },
);

// 表单
const initPostForm = {
  text: '',
  postType: props.type || PostType.Text,
  // postType: PostType.Picture,
  picture: {
    urls: [],
  },
  allowShare: true,
  fansGroupId: [],
  visibility: Visibility.PUBLIC,
  video: '',
  allowComment: true,
  forward: {
    postId: undefined,
  },
  product: {
    title: '',
    type: ProductType.Niche,
    url: '',
    niche: {
      uuid: undefined,
    },
  },
};
const initArticleForm = {
  title: '',
  img: '',
  content: '',
};
const formData = reactive<PostRequestData>({ post: { ...initPostForm }, article: { ...initArticleForm }, videoLabel: undefined });
const inputRef = ref(null);

const formRef = ref(null);
const formRules = {
  'post.product.url': [
    {
      validator: (val) => {
        if (!val) return { result: true };
        if (!isValidUrl(val)) return { result: false, message: '该链接无效，请重新输入', type: 'error' };
        return { result: true };
      },
    },
  ],
};

// 商品选择
const {
  nicheVisible,
  nicheOptions,
  nicheConfirm,
  toSelectNiche,
  changeProductType,
  productTitleDisabled,
} = usePostProduct(formData, formRef);

// 视频标签
const videoLabelId = ref<string | undefined>('');
const handleLabelChange = (_, data) => {
  formData.videoLabel = data;
};

const formatTime = (time: any) => {
  if (!time) return '';
  if (typeof time === 'number' && String(time).length === 10) return new Date(time * 1000).toISOString();
  return new Date(time).toISOString();
};

// 设置卡片动态类型数据
const setCardData = (data) => {
  const typeField = postTypeFieldMap[formData.post.postType];
  if (typeField) {
    const fields = data?.fields;
    formData.post[typeField] = {
      ...data,
      fields: {
        ...fields,
        time: formatTime(fields?.time),
      },
    };
  }
};
setCardData(props.extraData);

// 好友圈缓存待发布内容
if (props.source === 'friendCircle') {
  if (store.cachePublishContent) merge(formData, store.cachePublishContent);

  watch(
    () => formData,
    (val) => {
      store.cachePublishContent = val;
    },
    { deep: true },
  );
}

watch(
  () => props.visible,
  async (isVisible: boolean) => {
    await nextTick();
    isVisible && inputRef.value?.focus();
  },
  { immediate: true },
);

watch(
  () => props.defaultValue,
  (val) => {
    merge(formData, val || {});
    if (formData.post.postType === PostType.Picture) {
      images.value = formData.post.picture.urls.map((url) => ({ url, status: 'success' }));
    }

    if (PostType.Video === props.mode) {
      videos.value = [{ url: formData.post.video }];

      videoText.value = formData.post.text;
      formData.post.text = '';
      videoLocation.value = formData.post.location;
      formData.post.location = null;
    }

    if (formData.videoLabel) {
      videoLabelId.value = formData.videoLabel.id;
    }

    if (formData.post.product?.niche) {
      const { uuid, title } = formData.post.product.niche;
      nicheOptions.value = [{ value: uuid, label: title }];
    }
  },
  { deep: true, immediate: true },
);

// 选表情
const { onSelectEmoji } = useEmoji(inputRef, formData.post);
const { onSelectEmojiRef } = useEmojiInput(inputRef, videoText);
const emojiVisible = ref(false);

const groupNames = ref([]);
const visibilityConfirm = (type, fansGroupId, list) => {
  // formData.post.allowComment = Visibility.PUBLIC === type.value;
  formData.post.allowShare = Visibility.PUBLIC === type.value;
  formData.post.visibility = type.value;

  if (fansGroupId) {
    formData.post.fansGroupId = fansGroupId;
  }

  if (list?.length) {
    groupNames.value = list.map((v) => v.name);
    // groupNames.value = list.reduce((acc, pre) => acc.concat(pre.fans), []).map((v) => v.name);
  }
};

const videoUploadRef = ref(null);
const triggerUpload = () => {
  videoUploadRef.value.triggerUpload();
};

// 检查上传状态
const getUploadStatus = (...itemsArr) => {
  const items = itemsArr.flat();
  if (!items.length) return 'empty';
  if (items.some((item) => item.status === 'loading')) return 'uploading';
  if (items.every((item) => item.url && (item.status === 'success' || item.status === undefined))) return 'allUploaded';
  return 'partial';
};

const canPublish = computed(() => {
  // 检查相册模式
  if (isAlbum.value) return Boolean(formData.post?.albumNode?.img);

  // 统一判断图片和视频上传状态
  const uploadStatus = getUploadStatus(images.value, videos.value);
  if (uploadStatus === 'uploading') return false;
  if (uploadStatus === 'allUploaded') return true;

  // 检查是否有填写的文章内容
  if (postText.value?.trim()) return true;

  // 检查是否为卡片形式发布
  if (isPostCard(formData.post.postType)) return true;

  return false;
});

watch(
  () => [images, videos],
  () => {
    formData.post.picture.urls = images.value.map((v) => v.url);
    formData.post.video = videos.value[0]?.url || '';
  },
  { deep: true },
);

watch(
  () => props.albumNode,
  (val) => {
    if (!val) return;
    formData.post.albumNode = formData.post.albumNode || {
      describe: val.recent_idea?.idea,
      img: val.cover,
      nodeId: val.node_id,
      postType: val.node_type,
    };
  },
  { immediate: true, deep: true },
);

// 地图选择
const mapVisible = ref(false);
const addressConfirm = (data) => {
  const { lat, lng } = data.location;
  const locData = {
    name: data.name,
    latLng: {
      latitude: lat,
      longitude: lng,
    },
    address: data.address,
  };

  if (PostType.Video === props.mode) {
    videoLocation.value = locData;
    return;
  }

  formData.post.location = locData;
};

const { isOnline } = useNetwork();

const confirmTip = (header: string) => {
  const confirmDia = DialogPlugin.confirm({
    header,
    theme: 'warning',
    cancelBtn: null,
    closeBtn: false,
    confirmBtn: t('square.action.confirm'),
    onConfirm: async () => {
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

// 政府类型判断其认证状态，才可发布动态
const beforeConfirm = async () => {
  if (store.isPersonal) return true;
  const orgType = store.squareInfo.organizationProfile.type;
  if (orgType !== SquareType.Government) return true;

  const [err, res] = await to(getCertStatus(props.teamId));
  if (err) return true;

  const { certStatus, squareType } = res.data;
  const { isAdmin } = store.squareSelected;
  if (squareType !== SquareType.Government) return true;

  // 未认证/已过期
  if (certStatus === OrganizationCertStatus.Uncertified) {
    // 有认证权限
    if (isAdmin) {
      const confirmDia = DialogPlugin.confirm({
        header: '组织广场仅认证后方可对外发布，请认证后操作',
        confirmBtn: '立即认证',
        theme: 'warning',
        closeOnOverlayClick: false,
        onConfirm: async () => {
          confirmDia.destroy();

          const data = { ...formData };
          let draftApi = addDraft;

          // 编辑草稿
          const postId = data.post.id;
          if (postId) {
            draftApi = updateDraft;
            (data as any).post_id = postId;
          }

          // 保存、编辑草稿
          const res = await draftApi(data);

          // 添加时返回的 postId 用于下次编辑
          if (res.data.postId) {
            data.post.id = res.data.postId;
          }

          draftSaved.value = true;
          await MessagePlugin.success('已保存草稿');
          emit('close-dialog');

          // 政府类型认证
          emit('certified-gov', data);
        },
        onClose: () => confirmDia.hide(),
      });
      return false;
    }

    // 无认证权限
    confirmTip('当前组织未认证成功，请联系管理员进行认证');
    return false;
  }

  // 组织认证审核中
  if (certStatus === OrganizationCertStatus.Pending) {
    confirmTip('当前组织认证审核中，请审核通过后操作');
    return false;
  }

  return true;
};

// 格式化提交数据
const submitData = computed(() => {
  const data = merge({}, formData);

  if (PostType.Video === props.mode) {
    // 个人发布无商品信息
    if (store.isPersonal) {
      delete data.post.product;
    } else {
      const { type, url, niche } = data.post.product;
      if (type === ProductType.URL) {
        // 未填写URL，删除商品信息
        if (!url && data.post.product) delete data.post.product;
        // 删除商机信息
        if (data.post.product) delete data.post.product.niche;
      } else if (type === ProductType.Niche && !niche?.uuid) {
        // 未选择商机，清空商品信息
        delete data.post.product;
      }
    }

    // 设置发布类型为视频
    data.post.postType = props.mode;
  } else {
    // 如果发布模式不是视频，删除商品信息
    delete data.post.product;
  }

  return data;
});

const submitting = ref(false);
const publish = async () => {
  if (!isOnline.value) {
    MessagePlugin.error('网络连接失败，请检查网络后重试');
    return;
  }

  if (submitting.value) return;

  if (formRef.value) {
    const valid = await formRef.value.validate();
    if (typeof valid !== 'boolean') return;
  }

  if (!(await beforeConfirm())) return;

  let api = postPublish;
  // 草稿编辑后发布
  if (formData.post.status === PostStatus.Draft) {
    api = publishDraft;
  }

  submitting.value = true;
  let [err, res] = await to<AxiosResponse, AxiosError>(api({
    ...submitData.value,
    post: {
      ...submitData.value.post,
      text: postText.value,
      location: postLocation.value,
    },
  }, { teamId: props.teamId }));

  submitting.value = false;
  if (err) {
    console.log(err);

    // 保存草稿到本地
    if (err.code === 'ERR_NETWORK') {
      formData.post.draftReason = DraftReason.NetworkUnavailable;
      store.addPostStashList(formData);
      emit('error');
    }

    return;
  }

  store.cachePublishContent = null;

  if (!res.data.qrToken) {
    await MessagePlugin.success(t('square.post.success'));
  }

  emit('submit', { data: res.data });
  if (PostType.Article === formData.post.postType) emit('close-dialog');

  resetForm();
};

// const routeRefresh = inject(ROUTE_REFRESH_INJECT_KEY);
const action = (type) => {
  // 选择了图片或视频不能切换
  if (formData.post.picture.urls.length || formData.post.video) {
    return;
  }

  if (PostType.Article === type) {
    formData.post.postType = type;
    emit('close-dialog');
    const fullPath = '/square/publish-article?from=add';
    tabStore.addTab({
      label: t('square.post.postArticle'),
      fullPath,
    });
    router.push(fullPath);
    // routeRefresh();
  }
};

const toolItemClick = async (type: 'allowShare' | 'allowComment') => {
  if (type === 'allowShare' && Visibility.PUBLIC !== formData.post.visibility) return;
  const action = formData.post[type] ? t('square.action.close') : t('square.action.enable');
  if (type === 'allowShare') {
    await MessagePlugin.success(t('square.post.openSetting', [action, t('square.post.forwardShare')]));
  } else if (type === 'allowComment') {
    await MessagePlugin.success(t('square.post.openSetting', [action, t('square.post.comment')]));
  }
  formData.post[type] = !formData.post[type];
};

const resetForm = () => {
  Object.assign(formData, { post: { ...initPostForm }, article: { ...initArticleForm } });
  images.value = [];
  videos.value = [];
  isUploadCancel.value = false;
  draftSaved.value = false; // 重置草稿保存状态
};

const postData = computed(() => ({
  ...formData,
  post: {
    ...formData.post,
    text: postText.value,
    location: postLocation.value,
  },
}));

defineExpose({
  canPublish,
  postData,
  resetForm,
  video,
  rmVideo,
  draftSaved,
});

// 在组件卸载时清理本地预览URL
onUnmounted(() => {
  images.value.forEach((img) => {
    if (img.previewUrl) {
      URL.revokeObjectURL(img.previewUrl);
    }
  });
});
</script>

<style lang="less">
.post-publish-form {
  .t-form__label {
    line-height: 22px;
    height: 22px;
    min-height: 22px;
    margin-bottom: 8px;
  }
}
.square-d-publish-trends {
  .footer {
    display: flex;
    .item {
      display: flex;
      align-items: center;
      margin-right: 24px;
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #717376;
      cursor: pointer;
    }
  }
  .icon {
    font-size: 20px;
  }
}

.p-publish-more-action {
  .icon {
    color: var(--icon-kyy-color-icon-default, #828da5);
  }
  .t-dropdown__item:hover {
    color: var(--kyy_color_dropdown_text_active, #4d5eff);
    background: var(--kyy_color_dropdown_bg_active, #e1eaff);
    .icon {
      color: var(--kyy_color_dropdown_text_active, #4d5eff);
    }
  }
}
</style>

<style lang="less" scoped>
@import './publish/publish.less';
</style>
