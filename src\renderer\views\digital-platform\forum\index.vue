<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import UserSelect from './components/UserSelect.vue';
import { goForumAdmin } from '@/views/digital-platform/forum/utils/navigate';
import Avatar from '@/components/kyy-avatar/index.vue';
import PostDetail from './components/post/PostDetail.vue';
import { useForumStore } from './store';
import { checkPerms } from './utils/business';
import { useDigitalPlatformStore } from '../store/digital-platform-store';
import { refreshBus } from '../utils/eventBus';
import AsideRight from './components/AsideRight.vue';

const store = useDigitalPlatformStore();
const route = useRoute();
const router = useRouter();
const forumStore = useForumStore();

const detailVisible = ref(false);
const detailId = ref(null);
watch(
  () => route.query.post_id,
  (val) => {
    if (val) {
      detailVisible.value = true;
      detailId.value = val;
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

const teamId = route.query.team_id as string;
const navList = ref([
  { label: '论坛', value: 'forumHome', icon: 'iconforum2', count: 0, redPoint: true },
  { label: '我的主页', value: 'forumPost', icon: 'iconposts', count: 0 },
  { label: '通知', value: 'forumNotice', icon: 'iconnotice', count: 0 },
  { label: '草稿箱', value: 'forumDraft', icon: 'icon20drafts', count: 0 },
]);
const activeNav = computed(() => forumStore.activeNav);

// 刷新页面
const showView = ref(true);
const refreshPage = () => {
  showView.value = false;
  nextTick(() => {
    showView.value = true;
  });
};

const navClick = (item) => {
  // 点击选中菜单刷新红点数据
  if (item.value === route.name) {
    refreshPage();
    setTimeout(() => {
      forumStore.getUnreadStats();
    }, 200);
    return;
  }

  forumStore.setKeyValue({
    key: 'activeNav',
    value: item.value,
  });
  router.push({ name: item.value, query: route.query });
};

// 动态更新选中 tab
watch(
  () => route.name,
  (val) => {
    const exist = navList.value.findIndex((item) => item.value === val) > -1;
    if (val && exist) {
      forumStore.setKeyValue({
        key: 'activeNav',
        value: val as string,
      });
    }

    topicListKey.value++;
  },
);

watch(
  () => route.fullPath,
  () => {
    const checkRoute = '/digitalPlatformIndex/digital_platform_forum/';
    const isForum = `${route.fullPath}`.startsWith(checkRoute);
    if (isForum) {
      /**
       * fix: https://www.tapd.cn/69781318/bugtrace/bugs/view/1169781318001043770
       * 动态更新tab的数据
       */
      const item = store.tabs.find((i) => {
        const info = JSON.parse(JSON.stringify(i));
        return `${info.path}` === 'digital_platform_forum';
      });

      if (item) {
        const query = Object.fromEntries(new URLSearchParams(route.fullPath.split('?')[1]));
        const newTab = {
          ...item,
          query: { ...(item.query || {}), ...query },
          fullPath: route.fullPath,
        };
        store.addTab(newTab, false);
      }
    }
  },
);

watch(
  () => forumStore.unreadData,
  (val) => {
    updateTabCount(val);
  },
);
const updateTabCount = (newsStats) => {
  const { post = 0, likes = 0, systems = 0, comments = 0, drafts = 0, atMe = 0 } = newsStats;
  navList.value[0].count = post;
  navList.value[2].count = comments + systems + likes + atMe;
  navList.value[3].count = drafts;
};

// 获取是否有后台权限
const canManage = ref(false);
const getCanManage = () => {
  setTimeout(async () => {
    const staff = JSON.parse((localStorage.getItem('staff') as string) || '[]');
    const cardId = staff.find((v) => v.teamId === teamId)?.uuid || forumStore.cardList.find((v) => v.flag === 'staff')?.card;

    const perms = await checkPerms({
      card_id: cardId,
      app_uuid: forumStore.platformType,
    });
    canManage.value = perms?.canManage;
  }, 400);
};

onMountedOrActivated(async () => {
  await forumStore.init();
  getCanManage();
});

// 自定义论坛刷新
const topicListKey = ref(0);
refreshBus.on(async (event: string) => {
  console.log(event, route.name);
  // const inForum = route.path.startsWith('/digitalPlatformIndex/digital_platform_forum/');
  if (event === 'refresh' && route.name === 'forumHome') {
    // 刷新身份卡列表
    await forumStore.preload(route.query.team_id as string);

    topicListKey.value++;
    refreshPage();
  }
});

/*
// 离开论坛时移除缓存的论坛所有者id
onBeforeRouteLeave((to) => {
  const inForum = to.path.startsWith('/digitalPlatformIndex/digital_platform_forum');
  if (!inForum) {
    localStorage.removeItem(HEADER_FORUM_KEY);
  }
});
*/
</script>

<template>
  <div class="page-container">
    <div class="nav-wrap">
      <div class="logo-wrap">
        <img src="@renderer/assets/digital/svg/forum_icon.svg" class="w-25 h-20">
        <img src="@renderer/assets/digital/svg/forum_text.svg" class="w-42 h-26">
      </div>

      <div class="user-wrap">
        <UserSelect :default-value="forumStore.currCard">
          <div class="user-select flex-1 min-w-0">
            <template v-if="forumStore.currCard">
              <Avatar
                :key="forumStore.currCard?.avatar"
                avatar-size="28px"
                :image-url="forumStore.currCard?.avatar ?? ''"
                :user-name="forumStore.currCard?.name"
                round-radius
                class="avatar"
              />

              <div class="name">{{ forumStore.currCard.name }}</div>
              <t-tag theme="primary" variant="light" class="border-0!">{{ forumStore.currCard._flagText }}</t-tag>
              <iconpark-icon v-if="forumStore.cardList.length > 1" name="iconarrowdown" class="icon" />
            </template>
          </div>
        </UserSelect>
      </div>

      <t-divider class="my-0!" />

      <div class="nav">
        <div class="nav-top">
          <div
            v-for="(item, index) in navList"
            :key="index"
            :class="['nav-item', { selected: activeNav === item.value }]"
            @click="navClick(item)"
          >
            <iconpark-icon :name="item.icon" :class="['icon', item.value]" />
            <div class="flex-1">{{ item.label }}</div>
            <img v-if="item.redPoint && item.count" src="@/assets/redpoint.svg" class="redpoint">
            <span v-else-if="item.count" :class="['count', item.value]">{{ item.count }}</span>
          </div>
        </div>

        <div v-if="canManage" class="nav-footer" @click="goForumAdmin(teamId)">
          <iconpark-icon name="iconcomputer" class="icon" /> {{ $t('forum.admin') }}
        </div>
      </div>
    </div>

    <main class="main">
      <router-view
        v-if="showView"
        :key="$route.fullPath"
        v-slot="{ Component }"
        class="forum-content narrow-scrollbar-8"
      >
        <component :is="Component" ref="currComponentRef" />
      </router-view>
    </main>

    <AsideRight :key="topicListKey" />

    <PostDetail v-if="detailVisible" :id="detailId" v-model="detailVisible" />
  </div>
</template>

<style lang="less" scoped>
.page-container {
  display: flex;
  padding: 4px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  overflow: auto;
}

.logo-wrap {
  display: flex;
  height: 34px;
  padding: 4px 16px;
  align-items: center;
  gap: 2px;
}

.nav-wrap {
  width: 240px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  background: var(--bg-kyy_color_bg_light, #fff);
  padding-top: 12px;

  .top-bg {
    height: 130px;
    .img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .user-wrap {
    padding: 18px 10px 16px 16px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    .avatar {
      border-radius: var(--kyy_avatar_radius_full, 999px);
      :deep(.t-avatar) {
        font-size: 14px !important;
      }
    }
  }

  .user-select {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    max-width: 100%;

    &:hover {
      background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
    }

    .name {
      color: var(--text-kyy_color_text_1, #1a2139);
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      .ellipsis();
    }

    .icon {
      font-size: 24px;
      color: #828da5;
    }
  }

  .nav {
    display: flex;
    flex-direction: column;
    gap: 4px;
    height: 100%;
    .nav-top {
      display: flex;
      padding: 12px 16px;
      flex-direction: column;
      gap: 8px;
      flex: 1;
      .nav-item {
        display: flex;
        height: 48px;
        padding: 0 var(--kyy_radius_dropdown_m, 8px) 0 12px;
        align-items: center;
        gap: var(--kyy_radius_dropdown_m, 8px);
        border-radius: 4px;
        color: var(--text-kyy_color_text_1, #1a2139);
        font-size: 16px;
        font-weight: 600;
        line-height: 24px; /* 150% */
        cursor: pointer;
        &:hover {
          background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
        }
        &.selected {
          background: var(--bg-kyy_color_bg_list_foucs, #e1eaff);
        }
        .count {
          display: flex;
          height: 20px;
          padding: 0 var(--kyy_radius_dropdown_m, 8px);
          justify-content: center;
          align-items: center;
          gap: 10px;
          &.forumNotice {
            color: #e5398c;
          }
          &.forumDraft {
            color: #516082;
          }
        }
      }
      .icon {
        font-size: 20px;
        &.forumDraft {
          color: #62bf7c;
        }
        &[name='iconnotice'] {
          color: #2596ff;
        }
      }
    }

    .nav-footer {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 1px 18px;
      border-top: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
      height: 40px;
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      cursor: pointer;
      .icon {
        font-size: 20px;
      }
    }
  }
}

.main {
  flex: 1;
  min-width: 0;
  padding-left: var(--kyy_radius_dropdown_m, 8px);

  .forum-content {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
  }
}
</style>
