import { BrowserWindow, ipc<PERSON>ain, WebContentsPrintOptions } from "electron";
import { printURL } from "@main/config/StaticPath";
import { getSDK, getRandomUUID } from '@lynker-desktop/electron-sdk/main';
import { IsUseSysTitle } from "../config/const";
import { otherWindowConfig } from "../config/windowsConfig";
import { ipcMainHandle } from "@main/utils";

export function usePrintHandle() {
  ipcMainHandle(
    "getPrinters",
    async (event) => await event.sender.getPrintersAsync(),
  );
  ipcMainHandle(
    "printHandlePrint",
    async (event, options: WebContentsPrintOptions) =>
      new Promise((resolve) => {
        event.sender.print(
          options,
          (success: boolean, failureReason: string) => {
            resolve({ success, failureReason });
          },
        );
      }),
  );

  ipcMainHandle("openPrintDemoWindow", () => {
    openPrintDemoWindow();
  });
}

let win: BrowserWindow;
export async function openPrintDemoWindow() {
  if (win) {
    win.show();
    return;
  }
  win = await getSDK().windowManager.create({
    name: `openPrintDemoWindow-${getRandomUUID()}`,
    url: ``,
    browserWindow: {
      titleBarStyle: IsUseSysTitle ? "default" : "hidden",
      ...Object.assign(otherWindowConfig(), {}),
    }
  });
  win.loadURL(printURL);
  // win.on("ready-to-show", () => {
    win.show();
  // });
  win.on("closed", () => {
    win = null;
  });
}
