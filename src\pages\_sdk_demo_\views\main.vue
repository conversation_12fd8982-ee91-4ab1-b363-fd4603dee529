<template>
  <div id="app" class="sdk-demo" style="padding: 24px">
    <h2 style="margin-bottom: 8px">Lynker SDK API Demo</h2>

    <!-- 右侧导航面板 -->
    <NavigationPanel :nav-items="navItems" @scroll-to-section="scrollToSection" />
    <div class="sdk-demo-content">
      <!-- 基础功能 -->
      <div id="basic" class="demo-group">
        <h3>基础功能</h3>
        <div class="demo-buttons">
          <button @click="checkDesktop">
            isDesktop
            <br />
            <small>当前是否在桌面端中</small>
          </button>
          <button @click="openDesktop">
            openDesktop
            <br />
            <small>打开桌面端</small>
          </button>
          <button @click="getConfig">
            getConfig
            <br />
            <small>获取桌面端配置信息</small>
          </button>
          <button @click="getUserInfo">
            getUserInfo
            <br />
            <small>获取用户信息</small>
          </button>
          <button @click="getUniqueId">
            getUniqueId
            <br />
            <small>获取唯一ID</small>
          </button>
          <button @click="getRandomUUID">
            getRandomUUID
            <br />
            <small>获取唯一随机uuid</small>
          </button>
          <button @click="openExternalApp">
            openExternalApp
            <br />
            <small>打开外部应用</small>
          </button>
        </div>
      </div>

      <!-- 存储与启动参数 -->
      <div id="storage" class="demo-group">
        <h3>存储与启动参数</h3>
        <div class="demo-buttons">
          <button @click="setLocalStorage">
            setLocalStorage
            <br />
            <small>设置桌面端LocalStorage</small>
          </button>
          <button @click="getLocalStorage">
            getLocalStorage
            <br />
            <small>获取桌面端LocalStorage</small>
          </button>
          <button @click="getLaunchOptions">
            getLaunchOptions
            <br />
            <small>获取桌面端启动/唤起参数</small>
          </button>
          <button @click="delLaunchOptions">
            delLaunchOptions
            <br />
            <small>清除启动/唤起参数</small>
          </button>
        </div>
      </div>

      <!-- 窗口管理 -->
      <div id="window" class="demo-group">
        <h3>窗口管理</h3>
        <div class="demo-buttons">
          <button @click="openBrowser">
            openBrowser
            <br />
            <small>打开浏览器</small>
          </button>
          <button @click="openWindow">
            openWindow
            <br />
            <small>打开窗口</small>
          </button>
          <button @click="closeWindow">
            closeWindow
            <br />
            <small>关闭窗口</small>
          </button>
        </div>
      </div>

      <!-- 业务组件 -->
      <div id="business" class="demo-group">
        <h3>业务组件</h3>
        <div class="demo-buttons">
          <button @click="openMyHelpWindow">
            openMyHelpWindow
            <br />
            <small>打开帮助中心</small>
          </button>
          <button @click="openMyOrderWindow">
            openMyOrderWindow
            <br />
            <small>打开我的订单</small>
          </button>
          <button @click="openMyInvoiceWindow">
            openMyInvoiceWindow
            <br />
            <small>打开发票中心</small>
          </button>
          <button @click="openSelectMember">
            openSelectMember
            <br />
            <small>打开选人组件</small>
          </button>
          <button @click="openJoinDigitalPlatformDrawer">
            openJoinDigitalPlatformDrawer
            <br />
            <small>打开加入数字平台组件</small>
          </button>
          <button @click="openAnnualFeeDrawer">
            openAnnualFeeDrawer
            <br />
            <small>打开年度费用组件</small>
          </button>
          <button @click="openOrgAuthDrawer">
            openOrgAuthDrawer
            <br />
            <small>打开组织认证组件</small>
          </button>
          <button @click="openAddContactsDialog">
            openAddContactsDialog
            <br />
            <small>打开添加联系人组件</small>
          </button>
        </div>
      </div>

      <!-- 文件操作 -->
      <div id="file" class="demo-group">
        <h3>文件操作</h3>
        <div class="demo-buttons">
          <button @click="previewImage">
            previewImage
            <br />
            <small>预览图片</small>
          </button>
          <button @click="previewVideo">
            previewVideo
            <br />
            <small>预览视频</small>
          </button>
          <button @click="previewFile">
            previewFile
            <br />
            <small>预览文件</small>
          </button>
          <button @click="downloadFile">
            downloadFile
            <br />
            <small>下载文件</small>
          </button>
        </div>
      </div>

      <!-- 截图与调试 -->
      <div id="debug" class="demo-group">
        <h3>截图与调试</h3>
        <div class="demo-buttons">
          <button @click="startCapture">
            startCapture
            <br />
            <small>开始截图</small>
          </button>
          <button @click="stopCapture">
            stopCapture
            <br />
            <small>停止截图</small>
          </button>
          <button @click="openDebugTools">
            openDebugTools
            <br />
            <small>打开调试工具</small>
          </button>
        </div>
      </div>

      <!-- 加载与环境 -->
      <div id="loading" class="demo-group">
        <h3>加载与环境</h3>
        <div class="demo-buttons">
          <button @click="showLoading">
            showLoading
            <br />
            <small>显示加载中</small>
          </button>
          <button @click="hideLoading">
            hideLoading
            <br />
            <small>隐藏加载中</small>
          </button>
          <button @click="checkIsManualEnv">
            checkIsManualEnv
            <br />
            <small>检查是否为手动环境</small>
          </button>
        </div>
      </div>

      <!-- 消息相关 -->
      <div id="message" class="demo-group">
        <h3>消息相关</h3>
        <div class="demo-buttons">
          <button @click="message_openChat">
            message_openChat
            <br />
            <small>打开聊天窗口</small>
          </button>
          <button @click="message_openMessage">
            message_openMessage
            <br />
            <small>打开指定消息</small>
          </button>
          <button @click="message_openDrawerForWebview">
            message_openDrawerForWebview
            <br />
            <small>打开消息主窗口抽屉用于webview</small>
          </button>
          <button @click="message_closeDrawerForWebview">
            message_closeDrawerForWebview
            <br />
            <small>关闭消息主窗口抽屉用于webview</small>
          </button>
        </div>
      </div>

      <!-- 工作台相关 -->
      <div id="workbench" class="demo-group">
        <h3>数智工场相关</h3>
        <div class="demo-buttons">
          <button @click="workBench_getActiveTeamId">
            workBench_getActiveTeamId
            <br />
            <small>数智工场获取当前组织ID</small>
          </button>
          <button @click="workBench_getTabList">
            workBench_getTabList
            <br />
            <small>数智工场获取标签列表</small>
          </button>
          <button @click="workBench_openTabForWebview">
            workBench_openTabForWebview
            <br />
            <small>数智工场打开标签用于webview</small>
          </button>
          <button @click="workBench_closeTab">
            workBench_closeTab
            <br />
            <small>数智工场关闭标签</small>
          </button>
          <button @click="workBench_updateTab">
            workBench_updateTab
            <br />
            <small>数智工场更新标签</small>
          </button>
          <button @click="workBench_openDrawerForWebview">
            workBench_openDrawerForWebview
            <br />
            <small>数智工场打开抽屉用于webview</small>
          </button>
          <button @click="workBench_closeDrawerForWebview">
            workBench_closeDrawerForWebview
            <br />
            <small>数智工场关闭抽屉用于webview</small>
          </button>
          <button @click="workBench_openTeamCertification">
            workBench_openTeamCertification
            <br />
            <small>数智工场打开组织认证</small>
          </button>
        </div>
      </div>

      <!-- 数字平台相关 -->
      <div id="digital" class="demo-group">
        <h3>数字平台相关</h3>
        <div class="demo-buttons">
          <button @click="digitalPlatform_getActiveTeamId">
            digitalPlatform_getActiveTeamId
            <br />
            <small>数字平台获取当前团队ID</small>
          </button>
          <button @click="digitalPlatform_openTabForWebview">
            digitalPlatform_openTabForWebview
            <br />
            <small>数字平台打开标签用于webview</small>
          </button>
          <button @click="digitalPlatform_closeTab">
            digitalPlatform_closeTab
            <br />
            <small>数字平台关闭标签</small>
          </button>
          <button @click="digitalPlatform_updateTab">
            digitalPlatform_updateTab
            <br />
            <small>数字平台更新标签</small>
          </button>
        </div>
      </div>

      <!-- 广场相关 -->
      <div id="square" class="demo-group">
        <h3>广场相关</h3>
        <div class="demo-buttons">
          <button @click="square_openTabForWebview">
            square_openTabForWebview
            <br />
            <small>广场打开标签用于webview</small>
          </button>
          <button @click="square_updateTab">
            square_updateTab
            <br />
            <small>广场更新标签</small>
          </button>
          <button @click="square_closeTab">
            square_closeTab
            <br />
            <small>广场关闭标签</small>
          </button>
        </div>
      </div>

      <!-- 大市场相关 -->
      <div id="bigMarket" class="demo-group">
        <h3>大市场相关</h3>
        <div class="demo-buttons">
          <button @click="bigMarket_openTabForWebview">
            bigMarket_openTabForWebview
            <br />
            <small>大市场打开标签用于webview</small>
          </button>
          <button @click="bigMarket_updateTab">
            bigMarket_updateTab
            <br />
            <small>大市场更新标签</small>
          </button>
          <button @click="bigMarket_closeTab">
            bigMarket_closeTab
            <br />
            <small>大市场关闭标签</small>
          </button>
        </div>
      </div>

      <!-- 窗口标签相关 -->
      <div id="tabs" class="demo-group">
        <h3>窗口标签相关</h3>
        <div class="demo-buttons">
          <button @click="windowsTabs_openTab">
            windowsTabs_openTab
            <br />
            <small>打开新窗口标签</small>
          </button>
          <button @click="windowsTabs_closeTab">
            windowsTabs_closeTab
            <br />
            <small>关闭窗口标签</small>
          </button>
        </div>
      </div>
    </div>
    <div class="line"></div>
    <div
      class="sdk-demo-result"
      style="
        white-space: pre-wrap;
        background: #f7f7f7;
        padding: 16px;
        border-radius: 8px;
        min-height: 120px;
        margin-top: 24px;
      "
    >
      <b>结果 / Result：</b>
      <div style="max-height: 200px; overflow: auto">{{ result }}</div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import sdk from '@lynker-desktop/web';
import NavigationPanel from '../components/NavigationPanel.vue';

const result = ref('');

// 导航项配置
const navItems = [
  { id: 'basic', title: '基础功能' },
  { id: 'storage', title: '存储与启动参数' },
  { id: 'window', title: '窗口管理' },
  { id: 'business', title: '业务组件' },
  { id: 'file', title: '文件操作' },
  { id: 'debug', title: '截图与调试' },
  { id: 'loading', title: '加载与环境' },
  { id: 'message', title: '消息相关' },
  { id: 'workbench', title: '数智工场相关' },
  { id: 'digital', title: '数字平台相关' },
  { id: 'square', title: '广场相关' },
  { id: 'bigMarket', title: '大市场相关' },
  { id: 'tabs', title: '窗口标签相关' },
];

function showResult(res: any) {
  if (typeof res === 'object') {
    result.value = JSON.stringify(res, null, 2);
  } else {
    result.value = String(res);
  }
}

// 滚动到指定区域
function scrollToSection(sectionId: string) {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  }
}

function checkDesktop() {
  showResult(sdk.isDesktop);
}
function openDesktop() {
  sdk.openDesktop();
  showResult('已调用 openDesktop');
}
async function getConfig() {
  showResult(await sdk.getConfig());
}
async function setLocalStorage() {
  showResult(await sdk.setLocalStorage({ key: 'sdk_test', value: '测试token' }));
}
async function getLocalStorage() {
  showResult(await sdk.getLocalStorage('sdk_test'));
}
async function getLaunchOptions() {
  showResult(await sdk.getLaunchOptions());
}
async function delLaunchOptions() {
  showResult(await sdk.delLaunchOptions());
}
async function getRandomUUID() {
  showResult(sdk.getRandomUUID());
}
async function openExternalApp() {
  showResult(
    await sdk.openExternalApp({
      url: 'https://mp.dagouzhi.com/',
    }),
  );
}
async function openBrowser() {
  showResult(await sdk.openBrowser('https://www.baidu.com'));
}
async function openMyHelpWindow() {
  showResult(await sdk.openMyHelpWindow());
}
async function openMyOrderWindow() {
  showResult(await sdk.openMyOrderWindow());
}
async function openMyInvoiceWindow() {
  showResult(await sdk.openMyInvoiceWindow());
}
async function openSelectMember() {
  showResult(
    await sdk.openSelectMember({
      query: {
        showDropdownMenu: false,
      },
    }),
  );
}
async function openJoinDigitalPlatformDrawer() {
  showResult(await sdk.openJoinDigitalPlatformDrawer());
}
// 年度费用组件
async function openAnnualFeeDrawer() {
  showResult(
    await sdk.openAnnualFeeDrawer({
      query: {
        teamId: '788406055590383616',
      },
    }),
  );
}
async function openOrgAuthDrawer() {
  showResult(
    await sdk.openOrgAuthDrawer({
      query: {
        // CN MO
        region: 'CN',
        teamId: '790542474645454848',
        // INDIVIDUAL_BUSINESS
        // ENTERPRISE
        // GOVERNMENT
        // OTHER
        orgType: 'ENTERPRISE',
        // detail 详情，edit 编辑
        showType: 'edit',
      },
    }),
  );
}

async function openAddContactsDialog() {
  showResult(
    await sdk.openAddContactsDialog({
      query: {
        searchValue: '测试',
      },
    }),
  );
}

async function getUserInfo() {
  showResult(await sdk.getUserInfo());
}
async function getUniqueId() {
  showResult(await sdk.getUniqueId());
}
async function openDebugTools() {
  showResult(await sdk.openDebugTools());
}
async function previewImage() {
  showResult(
    await sdk.previewImage({
      images: ['https://www.baidu.com/img/bd_logo1.png'],
      index: 0,
      url: 'https://www.baidu.com/img/bd_logo1.png',
    }),
  );
}
async function previewVideo() {
  showResult(
    await sdk.previewVideo({
      title: '测试',
      type: 'video',
      size: 12,
      url: 'https://www.w3school.com.cn/i/movie.mp4',
    }),
  );
}
async function previewFile() {
  showResult(
    await sdk.previewFile({
      type: 'pdf',
      title: '测试',
      size: 12,
      // officeId: '02hvfm1te1hc0',
      url: 'https://image.ringkol.com/message/2025-06-05/644bc124-d260-456c-b1a9-3cc7a95bcf87.pdf',
    }),
  );
}
async function downloadFile() {
  showResult(
    await sdk.downloadFile({
      title: '测试.pdf',
      url: 'https://image.ringkol.com/message/2025-06-05/644bc124-d260-456c-b1a9-3cc7a95bcf87.pdf',
    }),
  );
}
async function startCapture() {
  showResult(await sdk.startCapture());
}
async function stopCapture() {
  showResult(await sdk.stopCapture());
}
async function showLoading() {
  showResult(
    await sdk.showLoading({
      message: '测试, 请稍后...',
    }),
  );
}
async function hideLoading() {
  showResult(await sdk.hideLoading());
}
async function checkIsManualEnv() {
  showResult(await sdk.checkIsManualEnv());
}
async function message_openChat() {
  showResult(
    await sdk.message_openChat({
      main: '唯一id-测试',
      peer: '唯一id-测试',
      group: '唯一id-测试',
      assistant: '唯一id-测试',
      extInfo: {
        type: 'service_content',
        content: '唯一id-测试',
      },
      rela: '唯一id-测试',
    }),
  );
}
async function message_openMessage() {
  showResult(
    await sdk.message_openMessage({
      messageId: '唯一id-测试',
      type: 'GROUP',
      cardId: '唯一id-测试',
      fromId: '唯一id-测试',
    }),
  );
}
async function message_openDrawerForWebview() {
  showResult(
    await sdk.message_openDrawerForWebview({
      id: '唯一id-测试',
      title: '必应',
      url: 'https://www.bing.com/',
    }),
  );
}
async function message_closeDrawerForWebview() {
  showResult(
    await sdk.message_closeDrawerForWebview({
      id: '唯一id-测试',
    }),
  );
}
async function workBench_getActiveTeamId() {
  showResult(await sdk.workBench_getActiveTeamId());
}
async function workBench_getTabList() {
  showResult(await sdk.workBench_getTabList());
}
async function workBench_openTabForWebview() {
  showResult(
    await sdk.workBench_openTabForWebview({
      path_uuid: 'demo-唯一uuid',
      title: '必应',
      url: 'https://www.bing.com/',
      icon: 'https://www.bing.com/favicon.ico',
      beforeCloseOptions: {
        title: '提示',
        content: '确定要关闭吗？',
      },
    }),
  );
}
async function workBench_closeTab() {
  showResult(
    await sdk.workBench_closeTab({
      path_uuid: 'demo-唯一uuid',
    }),
  );
}
async function workBench_updateTab() {
  showResult(
    await sdk.workBench_updateTab({
      path_uuid: 'demo-唯一uuid',
      title: '改了名字',
      beforeCloseOptions: {
        title: '提示',
        content: '确定要关闭吗？',
      },
    }),
  );
}
async function workBench_openDrawerForWebview() {
  showResult(
    await sdk.workBench_openDrawerForWebview({
      id: '唯一id-测试',
      title: '必应',
      url: 'https://www.bing.com/',
    }),
  );
}
async function workBench_closeDrawerForWebview() {
  showResult(
    await sdk.workBench_closeDrawerForWebview({
      id: '唯一id-测试',
    }),
  );
}
async function workBench_openTeamCertification() {
  showResult(
    await sdk.workBench_openTeamCertification({
      teamId: '788406055590383616',
    }),
  );
}
async function digitalPlatform_getActiveTeamId() {
  showResult(await sdk.digitalPlatform_getActiveTeamId());
}
async function digitalPlatform_openTabForWebview() {
  showResult(
    await sdk.digitalPlatform_openTabForWebview({
      // 可传可不传，不传则使用当前团队ID
      teamId: '553630032471044096',
      path_uuid: 'demo-唯一uuid',
      title: 'bilibili',
      url: 'https://www.bilibili.com/',
      icon: 'https://www.bilibili.com/favicon.ico',
    }),
  );
}
async function digitalPlatform_closeTab() {
  showResult(
    await sdk.digitalPlatform_closeTab({
      path_uuid: 'demo-唯一uuid',
    }),
  );
}
async function digitalPlatform_updateTab() {
  showResult(
    await sdk.digitalPlatform_updateTab({
      path_uuid: 'demo-唯一uuid',
      title: 'b站',
      beforeCloseOptions: { title: '11', content: '222' },
    }),
  );
}
async function square_openTabForWebview() {
  showResult(
    await sdk.square_openTabForWebview({
      path_uuid: 'demo-唯一uuid',
      title: 'b站',
      url: 'https://www.bilibili.com/',
      icon: 'https://www.bilibili.com/favicon.ico',
    }),
  );
}
async function square_updateTab() {
  showResult(
    await sdk.square_updateTab({
      path_uuid: 'demo-唯一uuid',
      title: 'b站修改',
      beforeCloseOptions: { title: '11', content: '222' },
    }),
  );
}
async function square_closeTab() {
  showResult(
    await sdk.square_closeTab({
      path_uuid: 'demo-唯一uuid',
    }),
  );
}

async function bigMarket_openTabForWebview() {
  showResult(
    await sdk.bigMarket_openTabForWebview({
      path_uuid: 'demo-唯一uuid',
      title: 'b站',
      url: 'https://www.bilibili.com/',
    }),
  );
}
async function bigMarket_updateTab() {
  showResult(
    await sdk.bigMarket_updateTab({
      path_uuid: 'demo-唯一uuid',
      title: 'b站修改',
      beforeCloseOptions: { title: '11', content: '222' },
    }),
  );
}
async function bigMarket_closeTab() {
  showResult(
    await sdk.bigMarket_closeTab({
      path_uuid: 'demo-唯一uuid',
    }),
  );
}

async function windowsTabs_openTab() {
  // showResult(
  //   await sdk.windowsTabs_openTab({
  //     options: {
  //       // 如当前页面在tab中，打开新tab 可不传 id (自动生成)
  //       id: '123',
  //       title: '测试tab1',
  //       url: window.location.origin + '/_sdk_demo_/index.html#/tabs_demo',
  //       icon: 'https://www.bing.com/favicon.ico',
  //     },
  //     // 如当前页面在tab中，打开新tab 可不传 tabsId (未生成时，自动生成)
  //     tabsId: '123',
  //     tabsTitle: '测试',
  //   }),
  // );
  showResult(
    await sdk.windowsTabs_openTab({
      options: {
        // 如当前页面在tab中，打开新tab 可不传 id (自动生成)
        id: '123',
        title: '测试tab1',
        url: window.location.origin + '/_sdk_demo_/index.html#/tabs_demo',
        icon: 'https://www.bing.com/favicon.ico',
        hideCloseButton: true,
      },
      // 如当前页面在tab中，打开新tab 可不传 tabsId (未生成时，自动生成)
      // 固定值，用于打开新tab
      // tabsId?: string | 'square' | 'digitalPlatform' | 'workBench';
      // square: 广场
      // digitalPlatform: 数字平台
      // workBench: 数智工场
      // tabsId: 'square',
      tabsId: '123',
      tabsTitle: '测试',
    }),
  );
}
async function windowsTabs_closeTab() {
  showResult(
    await sdk.windowsTabs_closeTab({
      // 如当前页面在tab中，关闭当前tab 可不传 id
      id: '123',
      // 如当前页面在tab中，关闭当前tab 可不传 tabsId
      // 固定值，用于打开新tab
      // tabsId?: string | 'square' | 'digitalPlatform' | 'workBench';
      // square: 广场
      // digitalPlatform: 数字平台
      // workBench: 数智工场
      tabsId: '123',
    }),
  );
}
async function openWindow() {
  showResult(await sdk.openWindow({ id: '123', title: '新窗口', url: 'https://www.baidu.com' }, '1111'));
}
async function closeWindow() {
  showResult(await sdk.closeWindow({ id: '123', tabsId: '1111' }));
}
</script>

<style lang="less">
.sdk-demo {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  overflow: auto !important;
  position: relative;
  padding-right: 280px; // 为右侧导航留出空间

  .demo-group {
    margin-bottom: 32px;
    padding: 8px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #e5e5e5;
    scroll-margin-top: 20px; // 滚动时的偏移量

    h3 {
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .demo-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }
  }

  // 响应式处理
  @media (max-width: 1200px) {
    padding-right: 24px;
  }
}

button {
  padding: 6px 16px;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 160px;
  text-align: center;

  &:hover {
    background: #f0f0f0;
    border-color: #1976d2;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  small {
    display: block;
    color: #888;
    font-size: 12px;
    margin-top: 2px;
    line-height: 1.3;
  }
}
.sdk-demo-content {
  flex: 1;
  overflow: auto;
}
.line {
  height: 2px;
  background: #e5e5e5;
  margin-top: 16px;
  margin-top: 16px;
  flex-shrink: 0;
}
.sdk-demo-result {
  flex-shrink: 0;
}
</style>
