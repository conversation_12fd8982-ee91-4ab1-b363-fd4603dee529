<template>
  <div class="container">
    <title-bar :is-title="t('helpc.appName')" />
    <!-- 头部搜索 -->
    <div class="header">
      <h3>{{ t('helpc.tip') }}</h3>
      <t-input
        v-model="keyword"
        :maxlength="50"
        :placeholder="t('helpc.tip2')"
        class="input"
        clearable
        @change="onKeyWord"
      >
        <template #prefixIcon>
          <iconpark-icon name="iconsearch" size="20" style="color: #828da5"></iconpark-icon>
        </template>
      </t-input>
    </div>

    <div v-show="keyword && searchValue && searchValue.length > 0" class="search">
      <div class="directory">
        <div class="item">
          {{ t('helpc.index') }}
          <iconpark-icon name="iconarrowright" class="icon"></iconpark-icon>
        </div>
        <div class="text">{{ t('helpc.sres') }}</div>
      </div>
      <div class="result">
        <div v-show="keyword" class="result-name">“{{ keyword }}”{{ t('helpc.res2') }}</div>
        <div class="result-box">
          <div v-for="(sItem, sIndex) in searchValue" :key="sIndex" class="item cursor" @click="onGoWord(sItem)">
            <span class="top" v-html="highlightText(sItem.document_name, keyword)"></span>
            <!-- <p v-html="highlightText('ddd', 'dd')"></p> -->
            <span class="con" v-html="highlightText(sItem.content, keyword)"></span>
          </div>
        </div>
      </div>
    </div>

    <div v-show="keyword && searchValue && searchValue.length < 1" class="toEmpty">
      <REmpty name="no-result" />
    </div>
    <!-- <div
      v-show="!keyword && searchValue && searchValue.length < 1"
      class="toEmpty"
    >
      <Empty />
    </div> -->
    <div v-show="!keyword" class="body">
      <div class="body-left">
        <div class="menu">
          <span
            v-for="mod in modules"
            :key="mod.id"
            :class="{
              'menu-item': true,
              cursor: true,
              active: mod.id === currentPanel.id,
            }"
            @click="onSetTab(mod)"
          >
            {{ mod.module_name }}
          </span>
        </div>
        <!-- <div class="footer">

          <iconpark-icon name="icongroupmember" class="img"></iconpark-icon>
          <span class="text">在线客服</span>
        </div> -->
      </div>
      <div class="body-right">
        <component
          :is="panels[currentPanel?.panel ? currentPanel.panel : '']"
          :id="currentPanel?.id"
          :flag="currentPanel?.flag"
          :word-id="wordId"
          :directory-id="directoryId"
          :auto-update="autoUpdate"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TitleBar from '@components/common/BusinessBar.vue';
import { REmpty } from '@rk/unitPark';
import { Ref, ref, onMounted, onActivated } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { getHelpModuleListAxios, getSearchDocumentAxios } from '../api';
import { panels, panelsType } from './panel';
// import { getResponseResult } from '../utils';
import { Flag } from './common/enum';
import { v4 as uuidv4 } from 'uuid';

const route = useRoute();
const { t } = useI18n();
const currentPanel: Ref<any> = ref(null);

const keyword = ref('');
const modules = ref([]);
const wordId = ref(0); // 用于直接定位相应文档
const directoryId = ref(0); // 用于直接定位相应目录
const autoUpdate = ref(''); // 自动更新
onMounted(async () => {
  await getModuleList();
  console.log(route.query);
  if (route.query.id) {
    onGoWord({
      id: Number(route.query.id),
      module_id: Number(route.query.module_id),
      directory_id: Number(route.query.directory_id),
    });
  }
});

// ipcRenderer.on('argsHelp', (event, array) => {
//   console.log(array);

//   if (tagLoad) {
//     return;
//   }
//   tagLoad = true;
//   setTimeout(() => {
//     tagLoad = false;
//   }, 2000);
//   if (!array) return;
//   onGoWord({
//     id: Number(array.id),
//     module_id: Number(array.module_id),
//     directory_id: Number(array.directory_id),
//   });
// });
//
onActivated(async () => {
  await getModuleList();
  console.log(route.query);
  if (route.query.id) {
    onGoWord({
      id: Number(route.query.id),
      module_id: Number(route.query.module_id),
      directory_id: Number(route.query.directory_id),
    });
  }
});
const searchValue = ref([]);
const onKeyWord = async (e) => {
  console.log(e);
  if (!e) return;
  let res: any = null;
  try {
    res = await getSearchDocumentAxios({ keyWord: e });
    // res = getResponseResult(res);
    if (!res) return;
    console.log(res);
    res.data.list?.map((v) => {
      try {
        v.content = JSON.parse(v.content)?.htmlContent;
      } catch (error) {
        console.log(error?.message);
      }
      return v;
    });
    searchValue.value = res.data.list;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg) MessagePlugin.error(errMsg);
  }
};

const onGoWord = (sItem) => {
  keyword.value = '';
  console.log(sItem);
  const result = modules.value.find((v) => v.id === sItem.module_id);
  console.log(result);

  if (result) {
    currentPanel.value = result;
    wordId.value = sItem.id;
    directoryId.value = sItem.directory_id;
    setTimeout(() => {
      // autoUpdate.value = uuidv4();
      autoUpdate.value = `${wordId.value + directoryId.value + uuidv4()}`;
    }, 500);
  }
};

// 获取模块列表
const getModuleList = () => {
  let res: any = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      res = await getHelpModuleListAxios();
      // res = getResponseResult(res);
      if (!res) {
        reject();
        return;
      }
      console.log(res);
      modules.value = switchData(res?.data?.module_lists);
      resolve(false);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg) MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const onSetTab = (item) => {
  currentPanel.value = item;
  autoUpdate.value = '';
  wordId.value = 0;
  directoryId.value = 0;
};

// flag
// string
// 必需
// product产品手册 hot_issues 热门问题 other 其它
const switchData = (arr) => {
  // new 新功能 product产品手册 hot_issues 热门问题 feedback 产品反馈
  arr.forEach((item) => {
    if (item.flag === Flag.new && item.is_init === 0) {
      item.panel = panelsType.PNewFuncPanel;
      currentPanel.value = item;
    } else if (item.flag === Flag.feedback && item.is_init === 0) {
      item.panel = panelsType.PFeedBackPanel;
    } else {
      item.panel = panelsType.PDirectoryPanel;
    }
  });
  return arr;
};

const highlightText = (text, searchTerm) => {
  // 如果搜索词为空，则直接返回原始文本
  if (!searchTerm) {
    return text;
  }

  // 使用正则表达式删除HTML标签
  text = text.replace(/<[^>]+>/g, '');

  // 使用正则表达式进行全局不区分大小写的文本匹配
  const regex = new RegExp(searchTerm, 'gi');

  // 使用 <span> 标签包裹匹配到的文本，并添加样式
  const highlightedText = text.replace(regex, (match) => `<span class="highlight">${match}</span>`);

  return highlightedText;
};
</script>
<style lang="less" scoped>
@import url('./common/public.less');
.win-title {
  background: var(--bg-kyy-color-bg-deep, #f5f8fe);
  backdrop-filter: blur(5px);
  height: 44px;
  border-bottom: 0;
}
.container {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  max-width: 100%;
  height: 100vh;
  overflow: hidden;
  .header {
    background: url('@assets/help/help_bg_pic.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    height: 132px;

    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    h3 {
      color: var(--success-kyy-color-success-active, #499d60);
      text-align: center;

      /* kyy_fontSize_3/bold */
      // font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
    }
    .input {
      width: 480px;
    }
  }
  @height: calc(100% - 180px);
  .search {
    height: @height;
    flex: 1;
    display: flex;
    padding: 0 120px;
    flex-direction: column;
    overflow: auto;
    padding-bottom: 20px;

    :deep(.highlight) {
      color: #4d5eff;
    }
    .result {
      border-radius: 4px;
      border: 1px solid var(--border-kyy-color-border-default, #d5dbe4);
      &-name {
        border-bottom: 1px solid var(--border-kyy-color-border-default, #d5dbe4);
        padding: 16px;
        color: var(--text-kyy-color-text-2, #516082);

        /* kyy_fontSize_3/regular */
        font-family: PingFang SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
      }
      &-box {
        .item {
          padding: 12px 16px;
          border-bottom: 1px solid var(--border-kyy-color-border-default, #d5dbe4);

          display: flex;
          flex-direction: column;

          &:last-child {
            border-bottom: 0;
          }
          .top {
            color: var(--text-kyy-color-text-1, #1a2139);
            /* kyy_fontSize_3/regular */
            font-family: PingFang SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
          }
          .con {
            color: var(--text-kyy-color-text-3, #828da5);

            /* kyy_fontSize_2/regular */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
          }
        }
      }
    }
  }
  .toEmpty {
    height: @height;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .body {
    height: @height;
    flex: 1;
    display: flex;

    &-left {
      border-right: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
      width: 200px;
      flex: initial;

      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .menu {
        flex: 1;
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        overflow: auto;
        width: inherit;
        &-item {
          padding: 12px;
          border-radius: 4px;
          color: var(--text-kyy-color-text-1, #1a2139);
          /* kyy_fontSize_3/bold */
          font-family: PingFang SC;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          transition: background-color 0.25s linear;

          &:hover {
            background: var(--bg-kyy-color-bg-list-hover, #f3f6fa);
            transition: background-color 0.25s linear;
          }
        }
        .active {
          border-radius: 4px;
          background: var(--bg-kyy-color-bg-list-foucs, #e1eaff);
          transition: all 0.25s linear;
        }
      }
      .footer {
        flex: initial;
        height: 48px;
        width: 100%;
        border-top: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
        display: flex;
        gap: 8px;
        align-items: center;
        padding-left: 24px;

        .img {
          font-size: 20px;
          color: #516082;
        }
        .text {
          color: var(--lingke-contain-fonts, #516082);
          /* Body regular */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          // line-height: 20px; /* 142.857% */
        }
      }
    }

    &-right {
      flex: 1;
    }
  }
}

.directory {
  padding: 16px;
  padding-left: 0;
  display: flex;
  align-items: center;
  gap: 4px;
  .item {
    color: var(--kyy_color_breadcrumb_text_default, #828da5);
    text-overflow: ellipsis;
    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    max-width: 160px;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    display: flex;
    align-items: center;
    gap: 4px;
    .icon {
      font-size: 16px;
    }
  }
  .text {
    color: var(--kyy_color_breadcrumb_text_active, #1a2139);

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
}
</style>
