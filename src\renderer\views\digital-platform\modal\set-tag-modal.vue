<template>
  <t-dialog
    v-model:visible="visible"
    :header="true"
    :close-btn="true"
    width="360"
    attach="body"
    class="dialogSet dialogNoDefault dialogSet-noBodyPadding dialogSetHeader drawerSetForm"
    :close-on-overlay-click="false"
    :close-on-esc-keydown="false"
  >
    <template #header>
      <div class="header">
        <div class="title">设置标签</div>
      </div>
    </template>
    <template #closeBtn>
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #body>
      <div class="body">
        <!-- :rules="formRules" -->
        <t-form
          ref="form"
          reset-type="initial"
          label-align="top"
          :data="formData"
        >
          <t-form-item v-if="settingInfo?.platform?.enable" :label="settingInfo?.platform?.name ? `平台标签（${settingInfo?.platform?.name}）`: '平台标签'">
            <!-- @change="changeSelect" -->
            <t-select
              v-model="formData.platform_tag"
              v-replace-svg
              placeholder="请选择"
              clearable
              class="optTagSelect"
            >
              <template #valueDisplay="{value}">
                <div v-if="activeTag" class="active" :style="{'color': activeTag?.color, 'background-color': activeTag?.bgColor}">{{activeTag?.name }}</div>
                <div v-else class="defaultValue">
                  请选择
                </div>
              </template>
              <template #suffixIcon>
                <img src="@/assets/svg/icon_arrow_down.svg" >
              </template>
              <!-- <template #content>卡卡下地方</template> -->
              <t-option
                v-for="item in settingInfo?.platform?.value"
                :key="item?.value_id"
                :value="item?.value_id"
                :label="item?.name"
                class="opt"
              >
                <template #content>
                  <div class="con">
                    <span class="con-icon" :style="{'background-color': switchColor(item?.colour)?.color}"></span>
                    <span class="con-text">{{ item?.name }}</span>
                  </div>
                </template>
              </t-option>
            </t-select>
          </t-form-item>
          <t-form-item v-if="settingInfo?.personal?.enable">
            <template #label>
              <div class="label">
                个人标签
                <template v-if="settingInfo?.personal?.name">
                  （{{settingInfo?.personal?.name}}）
                </template>
                <t-tooltip placement="top">
                  <template #content>
                    <div style="max-width: 200px">
                       {{settingInfo?.personal?.explain}}
                    </div>
                  </template>
                  <iconpark-icon v-if="settingInfo?.personal?.explain" name="iconhelp" class="iconhelp"></iconpark-icon>
                </t-tooltip>
              </div>
            </template>
            <div class="tagList">
              <template v-if="!formData?.person_tag_obj">
                <SetTagPopup :placement="'right'" :tagType="TagType.Search" @onSelect="onSelectTag">
                  <div class="add cursor">
                    <iconpark-icon class="iconadd" name="iconadd"></iconpark-icon>
                    <span class="addText">添加</span>
                  </div>
                </SetTagPopup>
              </template>
              <span class="tag" v-if="formData?.person_tag_obj" :style="{'background-color': formData?.person_tag_obj?.bgColor, color: formData?.person_tag_obj?.color}">
                {{ formData?.person_tag_obj?.name }}
                <iconpark-icon name="iconerror" @click="removePerTag" class="iconerror cursor" :style="{'color': formData?.person_tag_obj?.color}"></iconpark-icon>
              </span>
            </div>
          </t-form-item>

        </t-form>
      </div>
    </template>
    <template #footer>
      <div class="footer">
        <t-button
          class="btn confirm"
          variant="base"
          theme="default"
          style="width: 80px"
          @click="onClose"
        > 取消</t-button>

        <t-button
          class="btn confirm"
          theme="primary"
          variant="base"
          style="width: 80px"
          @click="onSave"
        >保存</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
// import { useDigitalPlatformStore } from '@renderer/views/digital-platform/store/digital-platform-store';
// import { workAreaRefreshKey } from '@renderer/views/digital-platform/utils/eventBusKey';
// import { goToAdmin } from '@renderer/views/member/utils/auth';
import { useEventBus } from '@vueuse/core';
import { computed, ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { TagColors, TagType } from '@renderer/views/digital-platform/utils/constant'
import SetTagPopup from '@renderer/views/digital-platform/modal/set-tag-popup.vue'
// const bus = useEventBus(workAreaRefreshKey);
// const store = useDigitalPlatformStore();
import { originType } from "@renderer/views/digital-platform/utils/constant";
import { MessagePlugin } from "tdesign-vue-next";

import { 
  onGetLabelSettingAxios as onGetLabelSettingPoliticsAxios,
  saveLabelRelationAxios as saveLabelRelationPoliticsAxios
} from "@renderer/api/politics/api/businessApi"


import { 
  onGetLabelSettingAxios as onGetLabelSettingMemberAxios,
  saveLabelRelationAxios as saveLabelRelationMemberAxios
} from "@renderer/api/member/api/businessApi"

import { 
  onGetLabelSettingAxios as onGetLabelSettingCBDAxios,
  saveLabelRelationAxios as saveLabelRelationCBDAxios
} from "@renderer/api/cbd/api/businessApi"


import { 
  onGetLabelSettingAxios as onGetLabelSettingAssociationAxios,
  saveLabelRelationAxios as saveLabelRelationAssociationAxios
} from "@renderer/api/association/api/businessApi"


import { to } from 'await-to-js'; 

const props = defineProps({
  teamId: {
    type: Boolean,
    default: false,
  },
  origin: {
    type: String,
    default: originType.Member,
  },
  // orData: {
  //   type: Object,
  //   default: ()=> null
  // }
});

// let origin = originType.Member;
const orData = ref(null);
const settingInfo = ref(null); // 设置数据

 

const handleObj = {
  getLabelFunc: null,
  saveTitleFunc: null,
}
const handleFunc = () => {
  switch (props.origin) {
    case originType.Member:
      // handleObj.saveTitleFunc = 
      handleObj.getLabelFunc = onGetLabelSettingMemberAxios;
      handleObj.saveTitleFunc = saveLabelRelationMemberAxios;
      break;
    case originType.Politics:
      handleObj.getLabelFunc = onGetLabelSettingPoliticsAxios;
      handleObj.saveTitleFunc = saveLabelRelationPoliticsAxios;
      break;
    case originType.CBD:
      handleObj.getLabelFunc = onGetLabelSettingCBDAxios;
      handleObj.saveTitleFunc = saveLabelRelationCBDAxios;
      break;
    case originType.Association:
      handleObj.getLabelFunc = onGetLabelSettingAssociationAxios;
      handleObj.saveTitleFunc = saveLabelRelationAssociationAxios;
      break;
    default:
     
      break;
  }
}
handleFunc();

const router = useRouter();
const visible = ref(false);
// const emits = defineEmits(['toVerifyWeb']);
const emits = defineEmits(['onRefresh']);
const activeTag = computed(()=> {
  const initColor = settingInfo.value?.platform?.value?.find((item)=> item.value_id ==  formData.platform_tag);
  const obj = TagColors.find((item) => item.intColor == initColor?.colour);

  return obj ? {...obj, name: initColor?.name} : null
})
const initData = {
  platform_tag: '',
  person_tag: '',
  person_tag_obj: null
};
let formData = reactive({...initData})


const onSaveTitleHandle = async () => {
  // if(titleArrValue.value?.length < 1) return;
  const params = {
    member_id: orData.value?.id,
    directory_id: orData.value?.directory_id,
    personal_value_ids: formData.person_tag ? [Number(formData.person_tag)] : [],
    platform_value_ids: formData.platform_tag ? [Number(formData.platform_tag)] : []
  }
  console.log(orData.value)
  console.log(params);
  const [err, res] = await to(handleObj.saveTitleFunc(params, props.teamId))
  if(err) {
    return MessagePlugin.error(err?.message);
  }
  if(res) {
    MessagePlugin.success('设置成功，详情中可编辑');
    emits('onRefresh');
    onClose();
  }
}


const onGetLabelSettingInfo = async ()=> {
  const [err, res] = await to(handleObj.getLabelFunc({}, props.teamId))
  if(err) return ;
  if(res) {
    const {data} = res;
    console.log(data)
    settingInfo.value = data?.data;

  }
}

const onSelectTag = (tag) => {
  console.log(tag)
  formData.person_tag = tag?.value_id;

  const colorObj = switchColor(tag?.colour);
  formData.person_tag_obj = {
    ...colorObj,
    ...tag
  };
}

const switchColor = (intColor)=> {
  return TagColors.find((item) => item.intColor == intColor)
}


const removePerTag = () => {
  formData.person_tag = '';
  formData.person_tag_obj = null;
}

const onSave = () => {
  console.log('执行了吗')
  onSaveTitleHandle();
};

const onClose = () => {
  visible.value = false;
};

const onOpen = (box) => {
  console.log('settagmodal', box)
  formData = Object.assign(formData, {...initData})
  formData.platform_tag = box?.label_relation?.platform?.value?.length > 0 ? box?.label_relation?.platform?.value[0]?.value_id : '';
  if(box?.label_relation?.personal?.value?.length > 0 ) {
    formData.person_tag =   box?.label_relation?.personal?.value[0]?.value_id;
    const colorObj = switchColor( box?.label_relation?.personal?.value[0]?.colour);
    formData.person_tag_obj = {
      ...colorObj,
      ...box?.label_relation?.personal?.value[0]
    };
  }

  orData.value = box;
  onGetLabelSettingInfo();
  visible.value = true;
};




defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>

.defaultValue {
  color: var(--input-kyy_color_input_text_default, #ACB3C0);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.header {
.title {
    color: var(--kyy_color_modal_title, #1A2139);
    font-size: 16px;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
}

.active {
  padding: 2px 8px;
  margin-left: 4px;
  border-radius: 4px;
  height: 24px;
}


.body {
  padding: 0 24px;
  .label {
    display: flex;
    .iconhelp {
      font-size: 20px;
    }
  }
  .tagList {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
    .add {
      color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      height: 24px;
      padding-left: 8px;
      padding-right: 12px;
      gap: 4px;

      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);

      .iconadd {
        color: #828DA5;
        font-size: 20px;
      }

    }
    .tag {
      border-radius: var(--kyy_radius_tag_s, 4px);
      padding: 0 4px;
      height: 20px;
      line-height: 20px; /* 166.667% */
      display: flex;
      align-items: center;
      gap: 4px;
      .iconerror {
        font-size: 20px;
      }
    }
  }

}
.footer {
  display: flex;
  justify-content: flex-end;
  padding: 24px;
  padding-top: 16px;
}
</style>
