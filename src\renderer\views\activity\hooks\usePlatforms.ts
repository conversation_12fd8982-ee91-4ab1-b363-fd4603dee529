import { ref } from 'vue';
import { platformGetListByOpenid } from '@/api/activity/platform';

export const usePlatforms = (options = { immediate: false }) => {
  // 当前【个人入会】加入并且是正式成员的数字平台
  const personalPlatforms = ref([]);

  // 获取当前选择的个人已加入的数字平台
  const getPersonalPlatforms = async () => {
    const res = await platformGetListByOpenid();
    personalPlatforms.value = res.data.data.list;
  };

  if (options.immediate) {
    getPersonalPlatforms();
  }

  return {
    personalPlatforms,
    getPersonalPlatforms,
  };
};
