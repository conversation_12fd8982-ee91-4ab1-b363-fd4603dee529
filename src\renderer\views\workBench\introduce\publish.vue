<template>
  {{ route?.query?.isRefresh }}
  <div v-if="!isDelete" class="addhonor">
    <!-- {{route.path}} -->
    <div v-if="openFlag" class="chatBox">
      <img src="@renderer/assets/bench/icon_info.svg" alt="" />
      <div>{{ `${t("banch.publishverify")}` }}</div>
    </div>
    <t-form ref="activityForm" class="honorForm" scroll-to-first-error="auto" :label-width="600" :data="formData"
      :rules="rules" @submit="onSubmit">
      <div v-for="(item, idx) in formData.module_list" :key="item.id" class="form-item">
        <div v-if="idx > 0" class="delete-btn" @click="handleRemoveIntroduce(idx)">
          <t-popup overlay-class-name="delete-tip" :content="$t('banch.deleteModuleIconTip')">
            <iconpark-icon name="icondelete" size="20" color="#D54941"></iconpark-icon>
          </t-popup>
        </div>
        <t-form-item class="ly-form-item" :label="`${t('banch.introduceTitle')}${idx + 1}`"
          :name="`module_list.${idx}.title`" label-align="top" style="flex: 1">
          <t-input v-model="item.title" class="input" show-limit-number :maxlength="8"
            :placeholder="t('banch.honorTitleph')"></t-input>
        </t-form-item>
        <t-form-item class="ly-form-item ly-form-item-hide-label" :label="t('banch.introduceContent')"
          :name="`module_list.${idx}.content`" label-align="top" style="flex: 1">
          <Editor :editor-type="'B'" :id="`lk-editor-about-${item.id}`" class="activity-lk-editors"
            :ref="(el) => (editorRef[item.id] = el)" :key="item.id" type="B" root-dir="introduce" :options="{
              toolbar: ['annex', 'link'],
              height: 320,
              componentId: `lk-editor-about-${item.id}`,
              showBubble: true,
            }" @update="handleContentChange($event, item.id)" @img-insert="imgInsert" />
        </t-form-item>
      </div>
      <div class="add-btn">
        <t-popconfirm :popup-props="{ overlayClassName: 'add-btn-popconfirm' }" :icon="null" :visible="isShowPopconfirm"
          content="点击后可添加多个介绍内容，如组织简介、组织业务、组织架构等，分开展示更清晰！" :confirm-btn="{
            content: '我知道了',
            theme: 'default',
            class: 'add-btn-popconfirm-btn',
          }" :cancelBtn="null" :onConfirm="onAddBtnPopConfirm">
          <t-button variant="outline" theme="primary" @click="handleAddIntroduce">
            <template #icon>
              <iconpark-icon color="#4D5EFF" size="20" name="iconadd-b762a0p0"></iconpark-icon>
            </template>
            <p>{{ t("banch.addIntroduceModule") }}</p>
          </t-button>
        </t-popconfirm>
      </div>
      <!-- 发布至 -->
      <t-form-item name="channelType" label-align="top" class="sendTo" :label="t('banch.sendTo')">
        <t-checkbox-group v-model="formData.channelType">
          <t-checkbox v-if="showSwitchList.includes('square')" key="0" value="to_square"
            :label="t('banch.square')"></t-checkbox>
          <t-checkbox v-if="showSwitchList.includes('workshop')" key="1" value="to_workshop"
            :label="t('banch.banch')"></t-checkbox>
          <t-checkbox v-if="
              showSwitchList.includes('member') ||
              showSwitchList.includes('government') ||
              showSwitchList.includes('uni') ||
              showSwitchList.includes('association') ||
              showSwitchList.includes('cbd')
            " key="2" value="to_platform" :label="t('banch.member')"></t-checkbox>
        </t-checkbox-group>
      </t-form-item>
      <!-- 底部按钮 -->
      <t-form-item :label-width="0" label-align="left" class="bottomButtons">
        <div>
          <t-button style="margin-right: 8px" theme="default" @click="handleShowSortDialog">{{
            t("banch.setModuleOrder")
            }}</t-button>
          <t-button class="activityDraft" style="margin-right: 8px" theme="default" @click="saveDraft">{{
            t("activity.activity.saveDraft")
            }}</t-button>
          <t-button class="activityDraft" style="margin-right: 8px" theme="default" @click="handlePreview">{{
            t("banch.publishPreviewTIitle")
            }}</t-button>
          <t-button :class="activityDetail?.details?.status == 'Released' ? 'activityDraft' : 'activitySubmit'"
            :theme="activityDetail?.details?.status == 'Released' ? 'default' : 'primary'" :disabled="submitLoading"
            :loading="submitLoading" type="submit" @click="handleCheckPublish">{{ t("banch.publish") }}</t-button>
        </div>
      </t-form-item>
    </t-form>
    <!-- 点击存草稿的弹窗 -->
    <t-dialog v-model:visible="saveDraftFlag" class="appAuthTipDialog" theme="info" :cancel-btn="t('address.cancel')"
      :close-btn="false" :confirm-btn="t('address.sure')" :header="t('banch.saveDraft')" :body="t('banch.saveDraftTip')"
      @confirm="saveDraftConfirm">
    </t-dialog>

    <t-dialog v-model:visible="showSortDialog" width="500px" class="sort-table-dialog"
      :header="$t('banch.setModuleOrder')" @confirm="onConfirmSort">
      <t-table row-key="id" row-class-name="sort-table-row" :show-header="false" :columns="sortTableColumns"
        :data="sortList" drag-sort="row" @drag-sort="onDragSort">
        <template #title="{ row }">{{ row.title }}</template>
        <template #drag>
          <iconpark-icon name="icondrag" color="#828DA5" size="20"></iconpark-icon>
        </template>
      </t-table>
    </t-dialog>
    <AboutDialog v-model="showAboutDialog" type="introduce" :data="formData" :is-request-data="true"
      :hide-show-share="true"></AboutDialog>
    <!-- 非审核人的弹窗 -->
    <select-body-dialog v-if="selectVisible" v-model:visible="selectVisible" options-type="introduction"
      :id-team="activationGroupItem.teamId" @confirm="selectConfirm" />
  </div>
  <noData v-if="isDelete" class="no-data" text="内容已被删除" />
</template>

<script lang="ts" setup name="IntroducePublish">
  import { ref, onMounted, watch, nextTick, onUnmounted, onDeactivated, onActivated } from "vue";
  import { useI18n } from "vue-i18n";
  import _ from "lodash";
  import upload from "@renderer/views/zhixing/components/upload.vue";
  import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import { useRoute, useRouter } from "vue-router";
  import to from "await-to-js";
  import SelectBodyDialog from "@renderer/views/workBench/components/select-body-dialog.vue";
  import { editHonorAPI, auditHonorAPI, auditAPI } from "@renderer/api/workBench/index.ts";
  import AboutDialog from "@renderer/views/workBench/components/AboutDialog.vue";
  import uploadImage, { blobToPngFile } from "@/views/square/utils/upload";
  import {
    publishIntroduce,
    saveIntroduceAsDraft,
    editIntroduceAsDraft,
    getIntroduceDetailById,
    checkPublishIntroduce,
  } from "@/api/workBench/introduce";
  import { onMountedOrActivated } from "../../../hooks/onMountedOrActivated";
  import { getAppsState } from "@/api/workBench/index";
  import { useTabStore } from "../teamSting/honorStore/tabStore";
  import noData from "../components/noData.vue";
  import { getOpenid } from "@renderer/utils/auth";
  import Editor from "@/components/editor/index.vue";
  import { useDigitalPlatformStore } from '@renderer/views/digital-platform/store/digital-platform-store';

  const getRandomId = () => Date.now().toString().slice(7) + Math.ceil(Math.random() * 1000);
  const { t } = useI18n();
  const storeDigital = useDigitalPlatformStore();

  const route = useRoute();
  const router = useRouter();
  const tabStore = useTabStore();
  // 表单数据
  const formData = ref({
    module_list: [{ id: getRandomId(), title: "", content: "" }],
    channelType: [],
  });
  const editorRef = ref({});
  const activityForm = ref();
  const isDelete = ref(false);
  const isShowPopconfirm = ref(false);

  const isSort = ref(false); // 是否正在进行排序
  const submitLoading = ref(false); // 加载状态
  const publishDialogVisible = ref(false); // 发布弹窗状态
  const showSortDialog = ref(false); // 排序弹窗状态
  const sortList = ref(); // 排序列表
  const activationGroupItem = ref({}); // 激活的当前组织
  const selectVisible = ref(false); // 选审核人弹窗状态
  const showSwitchList = ref([]); // 显示发布至的开关列表
  const showAboutDialog = ref(false); // 显示预览的状态管理
  // 表单校验规则
  const rules = ref({
    title: [
      { required: true, message: t("banch.addHonorTip1") },
      {
        validator: () => {
          const titles = formData.value.module_list.map((item) => item.title?.trim() || '');
          const titleFields = Array.from({ length: titles.length }).map((_, index) => `module_list.${index}.title`);
          activityForm.value?.clearValidate(titleFields);
          // 仅检查非空标题的唯一性
          const nonEmptyTitles = titles.filter(title => title);
          const hasDuplicateNonEmptyTitle = new Set(nonEmptyTitles).size !== nonEmptyTitles.length;
          return !hasDuplicateNonEmptyTitle;
        },
        message: t("banch.introduceTitleNotSame"),
      },
    ],
    content: [{ required: true, message: t("banch.addHonorTip1") }],
    channelType: [{ required: true, validator: (val) => val.length, message: t("banch.addHonorTip1") }],
  });

  const sortTableColumns = [
    { colKey: "title" },
    {
      colKey: "drag", // 列拖拽排序必要参数
      width: 46,
    },
  ];

  /**
   * 监听表单内容是否变化
   */
  const contentWatcher = ref();
  const listenContentChange = () => {
    contentWatcher.value = watch(
      () => formData.value,
      () => {
        if (!route.query.introduceID) {
          const hasContent = formData.value.module_list.some((item) => item.content.trim());
          if (hasContent) {
            tabStore.setSaveDraftWhenCloseTab(true);
          } else {
            tabStore.setSaveDraftWhenCloseTab(false);
          }
        }
      },
      { immediate: true, deep: true },
    );
  };

  /**
   * 监听是否该保存草稿
   */
  watch(
    () => tabStore.isSaveDraft.introduce,
    (val) => {
      if (val) {
        saveDraftConfirm();
      }
    },
  );

  /**
   * 重置表单
   */
  watch(
    () => tabStore.resetIntroduceFormTag,
    () => {
      nextTick(() => {
        resetFormData();
      });
    },
    { immediate: true },
  );

  // 新用户发布组织介绍会有提示
  const introPopupKey = '__work_bench_introduce_publish_is_show_popconfirm__';
  const openId = getOpenid();
  const getOpenIds = () => {
    const openIds = localStorage.getItem(introPopupKey);
    try {
      // 值为 onclick 时是老数据，需兼容
      return openIds === 'onclick' ? [] : JSON.parse(openIds) || [];
    } catch (e) {
      console.error(e);
      return [];
    }
  };

  onMountedOrActivated(async () => {
    isDelete.value = false;
    tabStore.isSaveDraft.introduce = false;
    tabStore.setSaveDraftWhenCloseTab(false);
    await initAppState();
    await open();

    // if(route?.query?.isRefresh) {
    //   // route.query.isRefresh = false;
     
    // }
    if(storeDigital.isRefreshIntroduceEdit) {
      storeDigital.setIsRefreshIntroduceEdit(false);
      await initData();
    }
    listenContentChange();

    const openIds = getOpenIds();
    isShowPopconfirm.value = !openIds.includes(openId);
    window.addEventListener('focus', () => {
       console.log('回家后附件倒海翻江回到房间')
    });

  });

  const onAddBtnPopConfirm = () => {
    isShowPopconfirm.value = false;

    const openIds = getOpenIds();
    openIds.push(openId);
    const uniqueOpenIds = [...new Set(openIds)];
    localStorage.setItem(introPopupKey, JSON.stringify(uniqueOpenIds));
  };

  const openPersonal = ref(false); // 当前人是否有审核权限
  const openFlag = ref(false);
  const open = async () => {
    const res = await auditHonorAPI(localStorage.getItem("honorteamid"));
    openFlag.value = res.data.data.introduce_status === 1;
    openPersonal.value = res.data.data.introduce_auth === 1;
  };

  /**
   * 初始化发布平台的开关信息
   */
  const initAppState = async () => {
    const [err, res] = await to(getAppsState(localStorage.getItem("honorteamid")));
    if (err) {
      const errData = err?.response?.data;
      MessagePlugin.warning(errData?.message);
      return;
    }
    showSwitchList.value = [];
    const list = res.data.data;
    if (list.square) {
      showSwitchList.value.push("square");
    }
    if (list.workshop) {
      showSwitchList.value.push("workshop");
    }
    if (list.member) {
      showSwitchList.value.push("member");
    }
    if (list.government) {
      showSwitchList.value.push("government");
    }
    if (list.association) {
      showSwitchList.value.push('association');
    }
    if (list.cbd) {
      showSwitchList.value.push("cbd");
    }
     if (list.uni) {
      showSwitchList.value.push("uni");
    }
  };

  /*
  watch(
    () => route.query.introduceID,
    async (val) => {
      if (val) {
        // MessagePlugin.info(route.query.introduceID)
        // if()
        MessagePlugin.success(val)
        if(route.path === '/workBenchIndex/introduce-publish') {
          await initData();
        }
      }
    },
    // { immediate: true },
  );
  */
  

  /**
   * 初始化数据
   */
  const initData = async () => {
    // MessagePlugin.info('publish')
    console.log("route.query.introduceID", route.query.introduceID, route.query);
    if (!route.query.introduceID) {
      return;
    }
    const [err, res] = await to(getIntroduceDetailById(route.query.introduceID, localStorage.getItem("honorteamid")));
    if (err) {
      isDelete.value = true;
      MessagePlugin.warning(t("banch.contentIsDeleted"));
      return;
    }
    isDelete.value = false;
    const data = res.data.data;

    data.to_square === 1 && formData.value.channelType.push("to_square");
    data.to_workshop === 1 && formData.value.channelType.push("to_workshop");
    data.to_platform === 1 && formData.value.channelType.push("to_platform");
    formData.value.module_list = data.module_list.map(({ id, title, content }) => ({ id, title, content }));
    // 重新发布
    if (formData.value.status === 4) {
      formData.value.isRepublish = true;
    }
    await nextTick();
    data.module_list.forEach(({ id, content }, idx) => {
      if (content) {
        editorRef.value[id].renderContent({ ops: JSON.parse(content) });
      } else {
        // 如果没有就清空
        editorRef.value[id].renderContent({ ops: [] });
      }
    });
  };

  /**
   * 拖动排序
   */
  const onDragSort = (params) => {
    isSort.value = true;
    sortList.value = params.newData;
  };

  /**
   * 显示排序的弹窗
   */
  const handleShowSortDialog = () => {
    if (formData.value.module_list.length < 2) {
      MessagePlugin.warning(t("banch.norSortBySingleModule"));
      return;
    }
    sortList.value = formData.value.module_list.map(({ id, title }) => ({
      id,
      title: title || t("banch.notSetTitleModule"),
    }));
    showSortDialog.value = true;
  };

  /**
   * 确认排序
   */
  const onConfirmSort = async () => {
    const newData = [];
    const moduleList = JSON.parse(JSON.stringify(formData.value.module_list));
    for (const item of sortList.value) {
      const _item = moduleList.find((v) => v.id === item.id);
      console.log("_item", _item);
      newData.push(_item);
    }
    formData.value.module_list = newData;
    showSortDialog.value = false;
    MessagePlugin.success(t("banch.sortSettingSuccess"));
    setTimeout(() => {
      activityForm.value?.clearValidate();
    });
  };

  /**
   * 预览效果
   */
  const handlePreview = async () => {
    // const inValid = formData.value.module_list.some((item) => !item.title.trim() || !item.content.trim());
    const res = await activityForm.value.validate();
    if (typeof res !== "boolean") {
      const dia = DialogPlugin.confirm({
        header: t("account.tip"),
        theme: "info",
        confirmBtn: t("banch.iknow"),
        cancelBtn: null,
        body: t("banch.inpuAllMustContent"),
        onConfirm: async () => {
          moveFirstInvalidElement(res);
          dia.destroy();
        },
        onCancel: () => {
          dia.hide();
        },
      });
      return;
    }
    showAboutDialog.value = true;
  };

  /**
   * 添加一个介绍栏目
   */
  const handleAddIntroduce = () => {
    formData.value.module_list.push({ id: getRandomId(), title: "", content: "" });
    onAddBtnPopConfirm();
  };

  /**
   * 移除一个介绍栏目
   */
  const handleRemoveIntroduce = (idx) => {
    const dia = DialogPlugin.confirm({
      header: t("banch.delete"),
      theme: "danger",
      body: t("banch.deleteModuleTip"),
      onConfirm: () => {
        formData.value.module_list.splice(idx, 1);
        dia.destroy();
      },
      onCancel: () => {
        dia.hide();
      },
    });
  };

  /**
   * 点击存草稿按钮
   */
  const saveDraftFlag = ref(false);
  const saveDraft = async () => {
    const titleIsEmpty = formData.value.module_list.some((item) => item.title.trim() === "");
    if (titleIsEmpty) {
      MessagePlugin.warning(t("banch.titleNotEmpty"));
      return;
    }
    saveDraftFlag.value = true;
  };

  /**
   * 保存草稿
   */
  const saveDraftConfirm = async () => {
    const data = getFormData();
    let draftApi;
    if (route.query.introduceID) {
      data.id = route.query.introduceID;
      draftApi = editIntroduceAsDraft;
    } else {
      draftApi = saveIntroduceAsDraft;
    }

    const [err, _] = await to(draftApi(data, localStorage.getItem("honorteamid")));
    if (err) {
      const errData = err?.response.data;
      MessagePlugin.warning(errData?.message);
      return;
    }
    saveDraftFlag.value = false;
    resetFormData();
    router.replace({
      path: "/workBenchIndex/teamSting",
      query: {
        tab: 3,
        activeMenu: "introduce",
        closeTab: "/workBenchIndex/introduce-publish",
      },
    });
  };

  /**
   * 获取表单数据
   */
  const getFormData = () => {
    const data = {};
    data.to_square = formData.value.channelType.includes("to_square") ? 1 : 0;
    data.to_workshop = formData.value.channelType.includes("to_workshop") ? 1 : 0;
    data.to_platform = formData.value.channelType.includes("to_platform") ? 1 : 0;
    data.reviewer = formData.value.reviewer || "";
    if (route.query.type === "draft") {
      data.draft_id = route.query.introduceID;
    } else {
      data.draft_id = "";
    }
    data.module_list = formData.value.module_list.map(({ title, content }) => ({ title, content }));
    return data;
  };

  // 选择审核人的事件
  const selectConfirm = async (list) => {
    formData.value.reviewer = parseInt(list[0].cardId.substring(1), 10);
    const err = await handleSubmit(false);
    if (!err) {
      MessagePlugin.success(t("banch.auditSubmitTip"));
    }
  };

  /**
   * 移动到有错误的表单项位置
   */
  const moveFirstInvalidElement = (res) => {
    const keys = Object.keys(res);
    const dom = document.querySelector(".addhonor");
    let top = 0;
    if (!dom) {
      return;
    }
    const item = keys.find((key) => key.includes("title") || key.includes("content"));
    if (item) {
      const [_, index, __] = item.split(".");
      const items = document.querySelectorAll(".form-item");
      const target = items[index];
      top = target.offsetTop - 32;
    } else if (keys.includes("channelType")) {
      top = Number.MAX_SAFE_INTEGER;
    }
    dom.scrollTo({
      top,
      behavior: "smooth",
    });
  };

  /**
   * 发布弹框确认
   */
  const handleDialog = () => {
    const dia = DialogPlugin.confirm({
      header: t("account.tip"),
      theme: "info",
      body:
        openPersonal.value && openFlag.value
          ? t("banch.publishNeedAuth", [t("banch.intro")])
          : t("banch.publishNotNeedAuth", [t("banch.intro")]),
      onConfirm: () => {
        handleSubmit(true);
        dia.destroy();
      },
      onCancel: () => {
        dia.hide();
      },
    });
  };

  /**
   * 点击发布
   */
  const onSubmit = async () => {
    const res = await activityForm.value.validate();
    if (typeof res !== "boolean") {
      moveFirstInvalidElement(res);
      MessagePlugin.warning(t("banch.addHonorTip"));
      return;
    }
    submitLoading.value = true;
    const [err, checkRes] = await to(checkPublishIntroduce(localStorage.getItem("honorteamid")));
    if (err) {
      submitLoading.value = false;
      return;
    }
    if (!checkRes.data.data.is_allow) {
      MessagePlugin.warning(t("banch.onlyPublishIntroduce"));
      submitLoading.value = false;
      return;
    }

    auditAPI({
      params: JSON.stringify(formData.value.module_list.map((item) => `${item.title},${item.content}`)),
    })
      .then(async (res) => {
        submitLoading.value = false;
        activationGroupItem.value = JSON.parse(localStorage.getItem("honorteam"));
        if (res.data.message === "success") {
          // 审核通过
          await open();
          // 当前人有审核权限或者未开启安全认证弹出弹窗
          if (openPersonal.value || !openFlag.value) {
            handleDialog();
          } else {
            // 非审核人弹出选人弹窗
            selectVisible.value = true;
          }
        } else {
          MessagePlugin.warning("你输入的信息包含敏感内容，请修改后重试");
        }
      })
      .catch((err) => {
        submitLoading.value = false;
        if (err?.response?.status !== 418) {
          MessagePlugin.warning(err?.response?.data?.message);
        }
      });
  };

  /**
   * 进行发布
   */
  const handleSubmit = async (tip) => {
    const data = getFormData();
    const [err, _] = await to(publishIntroduce(data, localStorage.getItem("honorteamid")));
    if (err) {
      const errData = err?.response?.data;
      if (errData?.code === 90007) {
        MessagePlugin.warning(errData?.message);
      }
      return errData;
    }
    publishDialogVisible.value = false;
    resetFormData();
    tip && MessagePlugin.success("发布成功");
    router.replace({
      path: "/workBenchIndex/teamSting",
      query: {
        activeMenu: "introduce",
        closeTab: "/workBenchIndex/introduce-publish",
      },
    });
  };
  // 正文内容（富文本）
  const noteData = ref({
    content: {
      images: [],
    },
    user_id: "",
    openid: "",
  });

  const targetId = ref("");

  /**
   * 处理富文本中正文内容
   */
  const handleContentChange = (contents, id) => {
    const content = {
      imageUrl: "",
      description: "",
      images: [],
      attachments: [],
      delta: "",
    };
    /**
     * 处理content里的数据
     * description： 所有文本信息加起来
     * imageUrl：第一张图片
     * images：所有的图片集合
     * attachments：所有的文件集合
     * delta：富文本内容
     */
    const delta = [];
    // 附件大小
    let size = 0;
    contents.forEach((v) => {
      let deltaItem = _.cloneDeepWith(v);
      if (typeof v.insert === "string") {
        content.description += v.insert.trim();
      }
      if (v.insert?.image) {
        !content.imageUrl && (content.imageUrl = v.insert.image);
        const imgItem = noteData.value.content.images.find((img) => img.url === v.insert.image);
        content.images.push(imgItem);
        imgItem?.size && (size += imgItem.size);
      }
      if (v.insert?.custom) {
        size += v.insert.custom.size;
        content.attachments.push(v.insert.custom);
        const atta = { attachment: JSON.stringify(v.insert.custom) };
        const custom = { custom: JSON.stringify(atta) };
        deltaItem.insert = custom;
      }
      delta.push(deltaItem);
    });
    content.delta = JSON.stringify(delta);
    const index = formData.value.module_list.findIndex((item) => item.id === id);
    if (content.delta === '[{"insert":"\\n"}]') {
      formData.value.module_list[index].content = "";
    } else {
      formData.value.module_list[index].content = content.delta;
    }
  };

  // 上传图片
  const imgInsert = (val) => {
    val.forEach((img) => {
      // 更新content images
      // const imgObj = {
      //   name: img.name,
      //   size: img.size,
      //   type: img.type.split('/')[1],
      //   url: img.url,
      // };
      // 更新content images
      const imgObj = {
        name: img.name,
        size: img.size,
        type: img.name.split(".").slice(-1)?.[0] || "",
        url: img.url,
      };
      console.log("🚀 ~ imgInsert ~ val:", val, imgObj.type);
      noteData.value.content.images.push(imgObj);
    });
  };

  const resetFormData = () => {
    for (const key in editorRef.value) {
      editorRef.value[key]?.renderContent({ ops: [] });
    }
    activityForm.value?.reset();
    formData.value = {
      module_list: [{ id: "default_id_1", title: "", content: "" }],
      channelType: [],
    };
  };

  onUnmounted(() => {
    contentWatcher.value?.();
    isShowPopconfirm.value = false;


    window.removeEventListener('focus', ()=> {});

  });
  onDeactivated(() => {
    contentWatcher.value?.();
    isShowPopconfirm.value = false;
    window.removeEventListener('focus', ()=> {});
  });
</script>

<style lang="less" scoped>
  .ly-form-item {
    margin-bottom: 12px;

    :deep(.t-form__label) label {
      //styleName: kyy_fontSize_2/bold;
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: 600;
      line-height: 22px;
      color: #1a2139 !important;
    }
  }

  .ly-form-item-hide-label {
    :deep(.t-form__label) {
      display: none;
    }
  }

  :deep(#lk-editor) {
    .ql-editor {
      padding: 12px;

      &::before {
        left: 12px;
      }
    }
  }

  :deep(.detail-content--box) {
    padding: 0;
  }

  .t-form__item.bottomButtons button {
    font-weight: bold;
  }

  .activity-lk-editors {
    width: 100%;
    /* min-height: 270px !important; */
    border: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4) !important;
    border-radius: 4px;

    :deep(.lk-toolbar) {
      border-bottom: none !important;
      border-radius: 4px 4px 0 0 !important;
    }

    :deep(.ql-container) {
      border-radius: 4px;
      border-top: none !important;
      // padding:0 12px;
      // text-indent: 12px;
    }
  }

  :deep(.t-form__label--top) {
    min-height: 22px !important;

    label {
      // color: var(--text-kyy_color_text_1, #1A2139);
      // font-size: 14px;
      // font-style: normal;
      // font-weight: 600;
    }
  }

  .addhonor {
    display: flex;
    width: 100%;
    height: 100%;
    padding: 16px 304px;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    background: var(--bg-kyy_color_bg_light, #fff);
    overflow: overlay;
  }

  // 发布验证
  .chatBox {
    width: 608px;
    text-align: center;
    display: flex;
    padding: 8px 24px;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    background: var(--kyy_color_alert_bg_bule, #eaecff);
    color: var(--kyy_color_alert_text, #1a2139);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;

    // margin-top: 16px;
    /* 157.143% */
    .overHiddenName {
      max-width: calc(100% - 140px);
      padding-left: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .clickViewButton {
      height: 22px;
      padding: 0;
      color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff) !important;
      background-color: transparent !important;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;

      /* 157.143% */
      :deep(.t-button__suffix) {
        margin-left: 0;
      }
    }

    >img {
      width: 20px;
      height: 20px;
    }
  }

  // 表单
  .honorForm {
    width: 608px;

    :deep(.textarea) {
      .t-textarea__inner {
        height: 72px;
        resize: none;
      }
    }

    :deep(.ql-container.ql-snow) {
      // height: calc(100% - 48px);
    }

    :deep(.t-form__label) {
      margin-bottom: 8px;
      line-height: 22px;
    }

    //發送至
    .sendTo {
      margin-bottom: 80px;
    }

    //底部按鈕
    .bottomButtons {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 64px;
      padding: 16px 0;
      align-items: flex-start;
      gap: 8px;
      align-self: stretch;
      background: #fff;
      box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.08);
      z-index: 999;

      :deep(.t-button) {
        min-width: 88px;
      }

      :deep(.t-form__controls) {
        width: 608px;
        margin: auto;
      }
    }

    .add-btn {
      margin: 28px 0 24px 0;
      display: flex;
      align-items: center;
      overflow: hidden;

      p {
        color: var(--brand-kyy_color_brand_default, #4d5eff);
        text-align: center;
        font-size: 12px;
        font-weight: 400;
        margin-left: 8px;
      }
    }

    .form-item {
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #eceff5;
      position: relative;

      &:first-child {
        margin-top: 0;
        padding-top: 0;
        border-top: none;
      }

      .delete-btn {
        position: absolute;
        right: 0;
        top: 26px;
        color: #d54941;
        cursor: pointer;
      }
    }
  }
</style>
<style lang="less">
  .activity-lk-editor {
    width: 100%;
    min-height: 270px !important;

    .ql-toolbar {
      border: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4) !important;
      border-bottom: none !important;
      border-radius: 8px 8px 0 0;
    }

    .ql-container {
      border: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4) !important;
      border-top: none !important;
      padding: 0 12px;
      border-radius: 0 0 8px 8px;

      .ql-editor:before {
        left: 12px;
      }
    }
  }

  .addhonor .t-dialog {
    width: 480px;
    padding: 32px;
  }

  tr.sort-table-row {
    display: flex;
    width: 452px;
    color: var(--text-kyy_color_text_1, #1a2139);
    font-size: 14px;
    font-weight: 400;
    border-radius: 8px;
    margin-bottom: 8px;
    padding: 12px;
    border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);

    td {
      border: none;
      padding: 0 !important;

      &:first-child {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &.t-table__handle-draggable {
        flex-shrink: 0;
      }
    }
  }

  .delete-tip .t-popup__content {
    display: flex;
    max-width: 256px;
    padding: 4px 8px;
    justify-content: center;
    align-items: center;
    align-content: center;
    gap: 8px;
    flex-wrap: wrap;
    color: var(--kyy_color_toopltip_text, #fff);
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    /* 150% */
    border-radius: var(--kyy_radius_toopltip, 6px);
    background: var(--kyy_color_toopltip_bg, #1d1d1f);
    box-shadow: none;
  }

  .no-data {
    margin-top: 120px;
  }

  .sort-table-dialog {
    .t-dialog {
      padding: 24px 6px 24px 24px;
    }

    .t-dialog__body {
      max-height: 462px;
      padding: 24px 0;
      overflow-y: auto;

      &::-webkit-scrollbar-track {
        background: #fff;
      }
    }

    .t-dialog__footer {
      padding-top: 16px;
    }

    .t-dialog__body,
    .t-table,
    .t-table__content,
    .t-table--layout-fixed {
      overflow-x: hidden;
    }
  }

  .add-btn-popconfirm {
    width: 241px;

    .t-popup__content {
      background-color: #4d5eff;
      padding: 0;
    }

    .t-popup__arrow {
      margin-top: -5px;

      &::before {
        background-color: #4d5eff;
        box-shadow: none;
      }
    }

    .t-icon {
      display: none;
    }

    .t-popconfirm__inner {
      //styleName: kyy_fontSize_2/regular;
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      color: #fff !important;
    }

    .t-popconfirm__buttons {
      margin-top: 24px !important;
    }

    .add-btn-popconfirm-btn {
      &:hover {
        background: #fff !important;
        color: #516082 !important;
      }
    }
  }
</style>
