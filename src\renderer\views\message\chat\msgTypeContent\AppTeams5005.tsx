// 小秘书
// 场景值 5005， 加入组织申请
/**
   {
    "template": "normal",
    "type": "APP_TEAMS",
    "scene": 5005,
    "header": {
        "staff": {
            "name": "foster",
            "avatar": "",
            "from": "企业邀请码"
        }
    },
    "content": {
        "title": "foster申请加入你的企业，立即处理",
        "body": [
            {
                "key": "team",
                "value": "1004测试组织"
            },
            {
                "key": "logo",
                "value": ""
            },
            {
                "key": "department_name",
                "value": "1004测试组织"
            }
        ]
    },
    "extend": {
        "apply_id": 291,
        "openId": "1ifi80000nu6awvi1e"
    }
} */
import { defineComponent, ref } from 'vue';
import { AppCard, AppCardBody, AppCardFooter, AppCardTag } from '../MessageAppCard';
import { getAppTeamStatus, reviewAppTeamsApply } from '../../service/extend/statusUtils';
import { useMessageStore } from '../../service/store';
import ChatAvatar from '../../components/ChatAvatar.vue';
import { i18nt } from "@/i18n";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const openIdentityCardById = (mycard: string, targetCard: string) => {
    if (mycard && targetCard) {
        ipcRenderer.invoke("identity-card", { cardId: targetCard, myId: mycard });
    }
}

export default defineComponent({
    props: {
        data: { type: Object, default: null },
    },
    setup(props, ctx) {
        const isLoading = ref(true);
        const isLoadError = ref(false);
        const getData = (msg) => {
            isLoadError.value = false;
            isLoading.value = true;
            getAppTeamStatus(msg).then(res => {
                isLoading.value = false;
            }).catch(error => {
                isLoading.value = false;
                isLoadError.value = true
            });
        }

        getData(props.data)

        return () => {
            const msg = props.data;
            const data = msg.contentExtra?.data;
            const team = data?.content?.body?.[0];
            const logo = data?.content.body?.[1];
            const department = data?.content?.body?.[2];

            const onClickAvatar = () => {
              const myCard = useMessageStore().chatingSession.myCardId;
              openIdentityCardById(myCard, data?.extend?.openId);
            };
            // 刷新回调
            const refreshCb = () => {
                getData(msg)
            }

            return (
                <AppCard isLoading={isLoading.value} isLoadError={isLoadError.value} refreshCb={refreshCb} style="position:relative;">
                    <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', padding: '12px 12px 0' }}>
                        <ChatAvatar src={data?.header?.staff?.avatar} alt={data?.header?.staff?.name} onClick={onClickAvatar} />
                        <div style="flex:1;margin-left:8px;">
                            <div style='font-weight:bold;'>{data.header.staff.name}</div>
                            <div style="color: #828DA5;">
                                {data?.header?.from_val !== 'link'
                                    ? i18nt('im.msg.enter', [data?.header?.staff?.from])
                                    : <span>{i18nt('im.msg.enter1')}<span style="color:#4D5EFF;margin:0 4px;">{data?.header?.staff?.from}</span>{i18nt('im.msg.enter2')}</span>
                                }
                            </div>
                        </div>
                    </div>
                <AppCardBody style="line-height:22px; font-size:14px;">
                    <div style='display:flex;flex-direction:row;justify-content:space-between;gap:16px;'>
                    <div style="color:#828DA5;">{i18nt('im.msg.applyJoin')}</div>
                    <div style='flex:1; display:flex;flex-direction:row;justify-content:space-between;line-height:22px;'>
                        { logo?.value
                          ? <img style="border-radius:4px;overflow:hidden;margin:auto;" width='16' height='16' src={logo?.value} />
                          : <i class="i-svg-im_avatar_team text-20 color-[#488BF0]" />
                        }
                        <div style="flex:1;margin-left:4px;">
                        {team?.value}
                        </div>
                    </div>
                    </div>
                    <div style='display:flex;flex-direction:row;justify-content:space-between;margin-top:4px;gap:16px;'>
                    <div style="color:#828DA5;">{i18nt('im.msg.joinDepartment')}</div>
                    <div style='flex:1;' >
                        {department?.value}
                    </div>
                    </div>
                </AppCardBody>
                { !data.apiData?.status ? <div class="app-card-divider"></div> : null }
                { !data.apiData?.status ? (
                    <AppCardFooter>
                        <div class="app-card-btn app-card-btn-waring" onClick={() => { reviewAppTeamsApply(msg, false); }}>
                            {i18nt('im.msg.refuse')}
                        </div>
                        <div class="app-card-btn" onClick={() => { reviewAppTeamsApply(msg, true); }}>
                            {i18nt('im.msg.agree')}
                        </div>
                    </AppCardFooter>
                ) : null}
                {/* 0：未处理， 1: 已同意， 2: 已拒绝， 3: 已撤销 */}
                { data.apiData?.status === 1 ? <AppCardTag theme='success' style="position: absolute; top:12px; right:12px;">{i18nt('im.msg.agreed')}</AppCardTag> : null}
                { data.apiData?.status === 2 ? <AppCardTag theme='danger' style="position: absolute; top:12px; right:12px;">{i18nt('im.msg.refused')}</AppCardTag> : null}
                </AppCard>
            );
        }
    }
})
