<template>
  <t-dialog
    attach="body"
    class="product-des-preview-dialog"
    :class="{ phone: previewType === 'phone' }"
    :header="false"
    :visible="visible"
    :close-btn="false"
    :footer="false"
  >
    <div class="p-24">
      <div class="flex justify-between items-center">
        <div class="text-16 text-[#1A2139] leading-24 font-600">{{ title }}</div>
        <iconpark-icon class="text-24 text-[#1A2139] cursor-pointer" name="iconerror" @click="close"></iconpark-icon>
      </div>
      <div class="mt-12 text-[#516082]">
        {{ subTitle }}
      </div>
    </div>

    <div v-if="previewType === 'pc'" class="pl-24 pr-2 pb-16 flex gap-24 preview-content">
      <div class="p-16 rounded-8 border border-solid border-[#ECEFF5]">
        <div class="w-168 ellipsis-2">
          <iconpark-icon name="square-fill" class="text-24 float-left"></iconpark-icon>
          <span class="ml-4 text-[#1A2139] font-600 text-16 leading-24">{{ shopStore?.storeInfo?.store?.title }}</span>
        </div>
      </div>

      <t-tabs class="product-des-preview-tabs flex-1" :value="1">
        <t-tab-panel :value="1" label="商品详情">
          <div class="mt-16 scroll-content">
            <div v-if="description.length < 1" class="h-full flex flex-col justify-center items-center">
              <Empty tip="暂无添加内容" name="no-data" />
            </div>

            <template v-else>
              <div v-for="des in description" :key="des.id" class="des-item">
                <lk-editor-playground
                  v-if="des.type === 'text'"
                  class="h-full"
                  :content="JSON.parse(des.content || '[]')"
                />

                <img v-else-if="des.type === 'image'" :src="des.content" alt="" />
              </div>
            </template>
          </div>
        </t-tab-panel>
        <t-tab-panel :value="2" label="商品属性"></t-tab-panel>
        <t-tab-panel :value="3" label="商品评价"></t-tab-panel>
      </t-tabs>
    </div>

    <div v-else-if="previewType === 'phone'" class="px-24 pb-24 preview-content">
      <div class="py-12 pl-12 pr-2 rounded-24 border border-solid border-[#D5DBE4] bg-[#fff] h-full">
        <div class="flex gap-24 text-16 leading-24">
          <div class="flex flex-col items-center gap-3 text-[#4D5EFF] font-600">
            <span>商品详情</span>
            <div class="w-16 h-3 rounded-1.5 bg-[#4D5EFF]"></div>
          </div>
          <div class="text-[#1A2139]">商品属性</div>
          <div class="text-[#1A2139]">商品评价</div>
        </div>

        <div class="mt-12 scroll-content phone">
          <div v-if="description.length < 1" class="h-full flex flex-col justify-center items-center">
            <REmpty tip="暂无添加内容" name="no-data" />
          </div>

          <template v-else>
            <div v-for="des in description" :key="des.id" class="des-item">
              <lk-editor-playground
                v-if="des.type === 'text'"
                class="h-full"
                :content="JSON.parse(des.content || '[]')"
              />

              <img v-else-if="des.type === 'image'" :src="des.content" alt="" />
            </div>
          </template>
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { REmpty } from '@rk/unitPark';
import { LkEditorPlayground } from '@rk/editor';
import { useShopStore } from '@pages/shop/store';

defineProps({
  description: {
    type: Array,
    default: () => [],
  },
});

const shopStore = useShopStore();

const visible = ref(false);

const previewType = ref('pc');

const title = computed(() => {
  return previewType.value === 'pc' ? '电脑端预览' : '手机端预览';
});

const subTitle = computed(() => {
  return previewType.value === 'pc'
    ? '以下为电脑端预览示意，最终效果发布后对外可见'
    : '以下为手机端预览示意，最终效果发布后对外可见';
});

const open = (type = 'pc') => {
  visible.value = true;

  previewType.value = type;
};

const close = () => {
  visible.value = false;
};

defineExpose({
  open,
});
</script>

<style lang="less">
.product-des-preview-dialog {
  .t-dialog__position.t-dialog--top {
    padding: 20px 0 !important;
  }

  &.phone {
    .t-dialog {
      background: #f5f8fe;
      width: 416px;
    }
  }

  .t-dialog {
    padding: 0;
    width: 1168px;
    height: 100%;
    border-radius: 8px;

    .t-dialog__body {
      padding: 0;
      border-radius: 8px;
      height: 100%;
    }

    .preview-content {
      height: calc(100% - 106px);
    }

    .t-tabs__content {
      height: calc(100% - 56px);
    }

    .t-tab-panel {
      height: calc(100% - 16px);
    }

    .scroll-content {
      height: 100%;
      overflow: auto;
      padding-right: 22px;

      &.phone {
        height: calc(100% - 42px);
        padding-right: 10px;
      }

      .wrap {
        margin: 0;
      }

      .des-item {
        img {
          width: 100%;
          display: block;
        }
      }
    }
  }
}

.product-des-preview-tabs {
  .t-tabs__nav-wrap {
    gap: 32px;
  }

  .t-tabs__nav-item {
    color: #1a2139;
    font-size: 16px;
    height: 56px !important;

    .t-tabs__nav-item-wrapper {
      padding: 0;
      margin: 0;
    }
  }

  .t-tabs__bar {
    width: 16px !important;
    transform: translateX(20px);
    background: #4d5eff;
  }

  .t-tabs__nav-container.t-is-top::after {
    background: #eceff5;
  }

  .t-is-active {
    font-weight: bold;
  }
}
</style>
